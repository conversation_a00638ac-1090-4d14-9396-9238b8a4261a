// Code generated by protoc-gen-go. DO NOT EDIT.
// source: guild-honor-halls.proto

package guildhonorhalls

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type HonorType int32

const (
	HonorType_HONOR_GUILD        HonorType = 0
	HonorType_HIGH_QUALITY_GUILD HonorType = 1
	HonorType_ALL_HONOR          HonorType = 1024
)

var HonorType_name = map[int32]string{
	0:    "HONOR_GUILD",
	1:    "HIGH_QUALITY_GUILD",
	1024: "ALL_HONOR",
}

var HonorType_value = map[string]int32{
	"HONOR_GUILD":        0,
	"HIGH_QUALITY_GUILD": 1,
	"ALL_HONOR":          1024,
}

func (x HonorType) String() string {
	return proto.EnumName(HonorType_name, int32(x))
}

func (HonorType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{0}
}

type GuildType int32

const (
	GuildType_FUN_GUILD  GuildType = 0
	GuildType_LIVE_GUILD GuildType = 1
	GuildType_ALL_GUILD  GuildType = 1024
)

var GuildType_name = map[int32]string{
	0:    "FUN_GUILD",
	1:    "LIVE_GUILD",
	1024: "ALL_GUILD",
}

var GuildType_value = map[string]int32{
	"FUN_GUILD":  0,
	"LIVE_GUILD": 1,
	"ALL_GUILD":  1024,
}

func (x GuildType) String() string {
	return proto.EnumName(GuildType_name, int32(x))
}

func (GuildType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{1}
}

//展示中，结束，删除
type ShowState int32

const (
	ShowState_SHOWING   ShowState = 0
	ShowState_TIMEOUT   ShowState = 1
	ShowState_DELETE    ShowState = 2
	ShowState_WAIT_SHOW ShowState = 3
	ShowState_ALL_STATE ShowState = 1024
)

var ShowState_name = map[int32]string{
	0:    "SHOWING",
	1:    "TIMEOUT",
	2:    "DELETE",
	3:    "WAIT_SHOW",
	1024: "ALL_STATE",
}

var ShowState_value = map[string]int32{
	"SHOWING":   0,
	"TIMEOUT":   1,
	"DELETE":    2,
	"WAIT_SHOW": 3,
	"ALL_STATE": 1024,
}

func (x ShowState) String() string {
	return proto.EnumName(ShowState_name, int32(x))
}

func (ShowState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{2}
}

type TagItem struct {
	Text                 string   `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	Para                 string   `protobuf:"bytes,2,opt,name=para,proto3" json:"para,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagItem) Reset()         { *m = TagItem{} }
func (m *TagItem) String() string { return proto.CompactTextString(m) }
func (*TagItem) ProtoMessage()    {}
func (*TagItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{0}
}

func (m *TagItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagItem.Unmarshal(m, b)
}
func (m *TagItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagItem.Marshal(b, m, deterministic)
}
func (m *TagItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagItem.Merge(m, src)
}
func (m *TagItem) XXX_Size() int {
	return xxx_messageInfo_TagItem.Size(m)
}
func (m *TagItem) XXX_DiscardUnknown() {
	xxx_messageInfo_TagItem.DiscardUnknown(m)
}

var xxx_messageInfo_TagItem proto.InternalMessageInfo

func (m *TagItem) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *TagItem) GetPara() string {
	if m != nil {
		return m.Para
	}
	return ""
}

type HonorTitle struct {
	Text                 string   `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	IconUrl              string   `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HonorTitle) Reset()         { *m = HonorTitle{} }
func (m *HonorTitle) String() string { return proto.CompactTextString(m) }
func (*HonorTitle) ProtoMessage()    {}
func (*HonorTitle) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{1}
}

func (m *HonorTitle) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HonorTitle.Unmarshal(m, b)
}
func (m *HonorTitle) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HonorTitle.Marshal(b, m, deterministic)
}
func (m *HonorTitle) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HonorTitle.Merge(m, src)
}
func (m *HonorTitle) XXX_Size() int {
	return xxx_messageInfo_HonorTitle.Size(m)
}
func (m *HonorTitle) XXX_DiscardUnknown() {
	xxx_messageInfo_HonorTitle.DiscardUnknown(m)
}

var xxx_messageInfo_HonorTitle proto.InternalMessageInfo

func (m *HonorTitle) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *HonorTitle) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

type HonorGuild struct {
	Id                   int64         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GuildId              uint32        `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ShortId              uint32        `protobuf:"varint,3,opt,name=short_id,json=shortId,proto3" json:"short_id,omitempty"`
	BeginTime            int64         `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64         `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	IndexWeight          int64         `protobuf:"varint,6,opt,name=index_weight,json=indexWeight,proto3" json:"index_weight,omitempty"`
	HonourTitle          []*HonorTitle `protobuf:"bytes,7,rep,name=honour_title,json=honourTitle,proto3" json:"honour_title,omitempty"`
	MemberCnt            int64         `protobuf:"varint,8,opt,name=member_cnt,json=memberCnt,proto3" json:"member_cnt,omitempty"`
	GuildName            string        `protobuf:"bytes,9,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	GuildDesc            string        `protobuf:"bytes,10,opt,name=guild_desc,json=guildDesc,proto3" json:"guild_desc,omitempty"`
	IsCooperation        bool          `protobuf:"varint,11,opt,name=is_cooperation,json=isCooperation,proto3" json:"is_cooperation,omitempty"`
	TagList              []*TagItem    `protobuf:"bytes,12,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	GuildState           ShowState     `protobuf:"varint,13,opt,name=guild_state,json=guildState,proto3,enum=guildhonorhalls.ShowState" json:"guild_state,omitempty"`
	HonorType            HonorType     `protobuf:"varint,14,opt,name=honor_type,json=honorType,proto3,enum=guildhonorhalls.HonorType" json:"honor_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *HonorGuild) Reset()         { *m = HonorGuild{} }
func (m *HonorGuild) String() string { return proto.CompactTextString(m) }
func (*HonorGuild) ProtoMessage()    {}
func (*HonorGuild) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{2}
}

func (m *HonorGuild) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HonorGuild.Unmarshal(m, b)
}
func (m *HonorGuild) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HonorGuild.Marshal(b, m, deterministic)
}
func (m *HonorGuild) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HonorGuild.Merge(m, src)
}
func (m *HonorGuild) XXX_Size() int {
	return xxx_messageInfo_HonorGuild.Size(m)
}
func (m *HonorGuild) XXX_DiscardUnknown() {
	xxx_messageInfo_HonorGuild.DiscardUnknown(m)
}

var xxx_messageInfo_HonorGuild proto.InternalMessageInfo

func (m *HonorGuild) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *HonorGuild) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *HonorGuild) GetShortId() uint32 {
	if m != nil {
		return m.ShortId
	}
	return 0
}

func (m *HonorGuild) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *HonorGuild) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *HonorGuild) GetIndexWeight() int64 {
	if m != nil {
		return m.IndexWeight
	}
	return 0
}

func (m *HonorGuild) GetHonourTitle() []*HonorTitle {
	if m != nil {
		return m.HonourTitle
	}
	return nil
}

func (m *HonorGuild) GetMemberCnt() int64 {
	if m != nil {
		return m.MemberCnt
	}
	return 0
}

func (m *HonorGuild) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *HonorGuild) GetGuildDesc() string {
	if m != nil {
		return m.GuildDesc
	}
	return ""
}

func (m *HonorGuild) GetIsCooperation() bool {
	if m != nil {
		return m.IsCooperation
	}
	return false
}

func (m *HonorGuild) GetTagList() []*TagItem {
	if m != nil {
		return m.TagList
	}
	return nil
}

func (m *HonorGuild) GetGuildState() ShowState {
	if m != nil {
		return m.GuildState
	}
	return ShowState_SHOWING
}

func (m *HonorGuild) GetHonorType() HonorType {
	if m != nil {
		return m.HonorType
	}
	return HonorType_HONOR_GUILD
}

type SetTagItemListReq struct {
	TagItem              []*TagItem `protobuf:"bytes,1,rep,name=tag_item,json=tagItem,proto3" json:"tag_item,omitempty"`
	IsAdd                bool       `protobuf:"varint,2,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SetTagItemListReq) Reset()         { *m = SetTagItemListReq{} }
func (m *SetTagItemListReq) String() string { return proto.CompactTextString(m) }
func (*SetTagItemListReq) ProtoMessage()    {}
func (*SetTagItemListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{3}
}

func (m *SetTagItemListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTagItemListReq.Unmarshal(m, b)
}
func (m *SetTagItemListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTagItemListReq.Marshal(b, m, deterministic)
}
func (m *SetTagItemListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTagItemListReq.Merge(m, src)
}
func (m *SetTagItemListReq) XXX_Size() int {
	return xxx_messageInfo_SetTagItemListReq.Size(m)
}
func (m *SetTagItemListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTagItemListReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetTagItemListReq proto.InternalMessageInfo

func (m *SetTagItemListReq) GetTagItem() []*TagItem {
	if m != nil {
		return m.TagItem
	}
	return nil
}

func (m *SetTagItemListReq) GetIsAdd() bool {
	if m != nil {
		return m.IsAdd
	}
	return false
}

type SetTagItemListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetTagItemListResp) Reset()         { *m = SetTagItemListResp{} }
func (m *SetTagItemListResp) String() string { return proto.CompactTextString(m) }
func (*SetTagItemListResp) ProtoMessage()    {}
func (*SetTagItemListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{4}
}

func (m *SetTagItemListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTagItemListResp.Unmarshal(m, b)
}
func (m *SetTagItemListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTagItemListResp.Marshal(b, m, deterministic)
}
func (m *SetTagItemListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTagItemListResp.Merge(m, src)
}
func (m *SetTagItemListResp) XXX_Size() int {
	return xxx_messageInfo_SetTagItemListResp.Size(m)
}
func (m *SetTagItemListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTagItemListResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetTagItemListResp proto.InternalMessageInfo

type GetTagItemListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTagItemListReq) Reset()         { *m = GetTagItemListReq{} }
func (m *GetTagItemListReq) String() string { return proto.CompactTextString(m) }
func (*GetTagItemListReq) ProtoMessage()    {}
func (*GetTagItemListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{5}
}

func (m *GetTagItemListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTagItemListReq.Unmarshal(m, b)
}
func (m *GetTagItemListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTagItemListReq.Marshal(b, m, deterministic)
}
func (m *GetTagItemListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagItemListReq.Merge(m, src)
}
func (m *GetTagItemListReq) XXX_Size() int {
	return xxx_messageInfo_GetTagItemListReq.Size(m)
}
func (m *GetTagItemListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagItemListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagItemListReq proto.InternalMessageInfo

type GetTagItemListResp struct {
	TagList              []*TagItem `protobuf:"bytes,1,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetTagItemListResp) Reset()         { *m = GetTagItemListResp{} }
func (m *GetTagItemListResp) String() string { return proto.CompactTextString(m) }
func (*GetTagItemListResp) ProtoMessage()    {}
func (*GetTagItemListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{6}
}

func (m *GetTagItemListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTagItemListResp.Unmarshal(m, b)
}
func (m *GetTagItemListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTagItemListResp.Marshal(b, m, deterministic)
}
func (m *GetTagItemListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagItemListResp.Merge(m, src)
}
func (m *GetTagItemListResp) XXX_Size() int {
	return xxx_messageInfo_GetTagItemListResp.Size(m)
}
func (m *GetTagItemListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagItemListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagItemListResp proto.InternalMessageInfo

func (m *GetTagItemListResp) GetTagList() []*TagItem {
	if m != nil {
		return m.TagList
	}
	return nil
}

type DelTagItemListReq struct {
	TagList              []*TagItem `protobuf:"bytes,1,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *DelTagItemListReq) Reset()         { *m = DelTagItemListReq{} }
func (m *DelTagItemListReq) String() string { return proto.CompactTextString(m) }
func (*DelTagItemListReq) ProtoMessage()    {}
func (*DelTagItemListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{7}
}

func (m *DelTagItemListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTagItemListReq.Unmarshal(m, b)
}
func (m *DelTagItemListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTagItemListReq.Marshal(b, m, deterministic)
}
func (m *DelTagItemListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTagItemListReq.Merge(m, src)
}
func (m *DelTagItemListReq) XXX_Size() int {
	return xxx_messageInfo_DelTagItemListReq.Size(m)
}
func (m *DelTagItemListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTagItemListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelTagItemListReq proto.InternalMessageInfo

func (m *DelTagItemListReq) GetTagList() []*TagItem {
	if m != nil {
		return m.TagList
	}
	return nil
}

type DelTagItemListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTagItemListResp) Reset()         { *m = DelTagItemListResp{} }
func (m *DelTagItemListResp) String() string { return proto.CompactTextString(m) }
func (*DelTagItemListResp) ProtoMessage()    {}
func (*DelTagItemListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{8}
}

func (m *DelTagItemListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTagItemListResp.Unmarshal(m, b)
}
func (m *DelTagItemListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTagItemListResp.Marshal(b, m, deterministic)
}
func (m *DelTagItemListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTagItemListResp.Merge(m, src)
}
func (m *DelTagItemListResp) XXX_Size() int {
	return xxx_messageInfo_DelTagItemListResp.Size(m)
}
func (m *DelTagItemListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTagItemListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelTagItemListResp proto.InternalMessageInfo

type SetHonorTitleReq struct {
	HonorTitleList       []*HonorTitle `protobuf:"bytes,1,rep,name=honor_title_list,json=honorTitleList,proto3" json:"honor_title_list,omitempty"`
	IsAdd                bool          `protobuf:"varint,2,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetHonorTitleReq) Reset()         { *m = SetHonorTitleReq{} }
func (m *SetHonorTitleReq) String() string { return proto.CompactTextString(m) }
func (*SetHonorTitleReq) ProtoMessage()    {}
func (*SetHonorTitleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{9}
}

func (m *SetHonorTitleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetHonorTitleReq.Unmarshal(m, b)
}
func (m *SetHonorTitleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetHonorTitleReq.Marshal(b, m, deterministic)
}
func (m *SetHonorTitleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetHonorTitleReq.Merge(m, src)
}
func (m *SetHonorTitleReq) XXX_Size() int {
	return xxx_messageInfo_SetHonorTitleReq.Size(m)
}
func (m *SetHonorTitleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetHonorTitleReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetHonorTitleReq proto.InternalMessageInfo

func (m *SetHonorTitleReq) GetHonorTitleList() []*HonorTitle {
	if m != nil {
		return m.HonorTitleList
	}
	return nil
}

func (m *SetHonorTitleReq) GetIsAdd() bool {
	if m != nil {
		return m.IsAdd
	}
	return false
}

type SetHonorTitleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetHonorTitleResp) Reset()         { *m = SetHonorTitleResp{} }
func (m *SetHonorTitleResp) String() string { return proto.CompactTextString(m) }
func (*SetHonorTitleResp) ProtoMessage()    {}
func (*SetHonorTitleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{10}
}

func (m *SetHonorTitleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetHonorTitleResp.Unmarshal(m, b)
}
func (m *SetHonorTitleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetHonorTitleResp.Marshal(b, m, deterministic)
}
func (m *SetHonorTitleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetHonorTitleResp.Merge(m, src)
}
func (m *SetHonorTitleResp) XXX_Size() int {
	return xxx_messageInfo_SetHonorTitleResp.Size(m)
}
func (m *SetHonorTitleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetHonorTitleResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetHonorTitleResp proto.InternalMessageInfo

type DelHonorTitleListReq struct {
	HonorTitleList       []*HonorTitle `protobuf:"bytes,1,rep,name=honor_title_list,json=honorTitleList,proto3" json:"honor_title_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DelHonorTitleListReq) Reset()         { *m = DelHonorTitleListReq{} }
func (m *DelHonorTitleListReq) String() string { return proto.CompactTextString(m) }
func (*DelHonorTitleListReq) ProtoMessage()    {}
func (*DelHonorTitleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{11}
}

func (m *DelHonorTitleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelHonorTitleListReq.Unmarshal(m, b)
}
func (m *DelHonorTitleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelHonorTitleListReq.Marshal(b, m, deterministic)
}
func (m *DelHonorTitleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelHonorTitleListReq.Merge(m, src)
}
func (m *DelHonorTitleListReq) XXX_Size() int {
	return xxx_messageInfo_DelHonorTitleListReq.Size(m)
}
func (m *DelHonorTitleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelHonorTitleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelHonorTitleListReq proto.InternalMessageInfo

func (m *DelHonorTitleListReq) GetHonorTitleList() []*HonorTitle {
	if m != nil {
		return m.HonorTitleList
	}
	return nil
}

type DelHonorTitleListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelHonorTitleListResp) Reset()         { *m = DelHonorTitleListResp{} }
func (m *DelHonorTitleListResp) String() string { return proto.CompactTextString(m) }
func (*DelHonorTitleListResp) ProtoMessage()    {}
func (*DelHonorTitleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{12}
}

func (m *DelHonorTitleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelHonorTitleListResp.Unmarshal(m, b)
}
func (m *DelHonorTitleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelHonorTitleListResp.Marshal(b, m, deterministic)
}
func (m *DelHonorTitleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelHonorTitleListResp.Merge(m, src)
}
func (m *DelHonorTitleListResp) XXX_Size() int {
	return xxx_messageInfo_DelHonorTitleListResp.Size(m)
}
func (m *DelHonorTitleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelHonorTitleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelHonorTitleListResp proto.InternalMessageInfo

type GetHonorTitleReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHonorTitleReq) Reset()         { *m = GetHonorTitleReq{} }
func (m *GetHonorTitleReq) String() string { return proto.CompactTextString(m) }
func (*GetHonorTitleReq) ProtoMessage()    {}
func (*GetHonorTitleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{13}
}

func (m *GetHonorTitleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHonorTitleReq.Unmarshal(m, b)
}
func (m *GetHonorTitleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHonorTitleReq.Marshal(b, m, deterministic)
}
func (m *GetHonorTitleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHonorTitleReq.Merge(m, src)
}
func (m *GetHonorTitleReq) XXX_Size() int {
	return xxx_messageInfo_GetHonorTitleReq.Size(m)
}
func (m *GetHonorTitleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHonorTitleReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHonorTitleReq proto.InternalMessageInfo

type GetHonorTitleResp struct {
	TitleList            []*HonorTitle `protobuf:"bytes,1,rep,name=title_list,json=titleList,proto3" json:"title_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetHonorTitleResp) Reset()         { *m = GetHonorTitleResp{} }
func (m *GetHonorTitleResp) String() string { return proto.CompactTextString(m) }
func (*GetHonorTitleResp) ProtoMessage()    {}
func (*GetHonorTitleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{14}
}

func (m *GetHonorTitleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHonorTitleResp.Unmarshal(m, b)
}
func (m *GetHonorTitleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHonorTitleResp.Marshal(b, m, deterministic)
}
func (m *GetHonorTitleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHonorTitleResp.Merge(m, src)
}
func (m *GetHonorTitleResp) XXX_Size() int {
	return xxx_messageInfo_GetHonorTitleResp.Size(m)
}
func (m *GetHonorTitleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHonorTitleResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHonorTitleResp proto.InternalMessageInfo

func (m *GetHonorTitleResp) GetTitleList() []*HonorTitle {
	if m != nil {
		return m.TitleList
	}
	return nil
}

//添加公会列表
type SetHonorGuildListReq struct {
	GuildIdList          []*HonorGuild `protobuf:"bytes,1,rep,name=guild_id_list,json=guildIdList,proto3" json:"guild_id_list,omitempty"`
	HonorType            HonorType     `protobuf:"varint,2,opt,name=honor_type,json=honorType,proto3,enum=guildhonorhalls.HonorType" json:"honor_type,omitempty"`
	GuildType            GuildType     `protobuf:"varint,3,opt,name=guild_type,json=guildType,proto3,enum=guildhonorhalls.GuildType" json:"guild_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetHonorGuildListReq) Reset()         { *m = SetHonorGuildListReq{} }
func (m *SetHonorGuildListReq) String() string { return proto.CompactTextString(m) }
func (*SetHonorGuildListReq) ProtoMessage()    {}
func (*SetHonorGuildListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{15}
}

func (m *SetHonorGuildListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetHonorGuildListReq.Unmarshal(m, b)
}
func (m *SetHonorGuildListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetHonorGuildListReq.Marshal(b, m, deterministic)
}
func (m *SetHonorGuildListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetHonorGuildListReq.Merge(m, src)
}
func (m *SetHonorGuildListReq) XXX_Size() int {
	return xxx_messageInfo_SetHonorGuildListReq.Size(m)
}
func (m *SetHonorGuildListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetHonorGuildListReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetHonorGuildListReq proto.InternalMessageInfo

func (m *SetHonorGuildListReq) GetGuildIdList() []*HonorGuild {
	if m != nil {
		return m.GuildIdList
	}
	return nil
}

func (m *SetHonorGuildListReq) GetHonorType() HonorType {
	if m != nil {
		return m.HonorType
	}
	return HonorType_HONOR_GUILD
}

func (m *SetHonorGuildListReq) GetGuildType() GuildType {
	if m != nil {
		return m.GuildType
	}
	return GuildType_FUN_GUILD
}

type SetHonorGuildListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetHonorGuildListResp) Reset()         { *m = SetHonorGuildListResp{} }
func (m *SetHonorGuildListResp) String() string { return proto.CompactTextString(m) }
func (*SetHonorGuildListResp) ProtoMessage()    {}
func (*SetHonorGuildListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{16}
}

func (m *SetHonorGuildListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetHonorGuildListResp.Unmarshal(m, b)
}
func (m *SetHonorGuildListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetHonorGuildListResp.Marshal(b, m, deterministic)
}
func (m *SetHonorGuildListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetHonorGuildListResp.Merge(m, src)
}
func (m *SetHonorGuildListResp) XXX_Size() int {
	return xxx_messageInfo_SetHonorGuildListResp.Size(m)
}
func (m *SetHonorGuildListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetHonorGuildListResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetHonorGuildListResp proto.InternalMessageInfo

//取荣誉公会列表
type GetHonorGuildListReq struct {
	HonorType            HonorType `protobuf:"varint,1,opt,name=honor_type,json=honorType,proto3,enum=guildhonorhalls.HonorType" json:"honor_type,omitempty"`
	GuildType            GuildType `protobuf:"varint,2,opt,name=guild_type,json=guildType,proto3,enum=guildhonorhalls.GuildType" json:"guild_type,omitempty"`
	GuildState           ShowState `protobuf:"varint,3,opt,name=guild_state,json=guildState,proto3,enum=guildhonorhalls.ShowState" json:"guild_state,omitempty"`
	Off                  int64     `protobuf:"varint,4,opt,name=off,proto3" json:"off,omitempty"`
	Count                int64     `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetHonorGuildListReq) Reset()         { *m = GetHonorGuildListReq{} }
func (m *GetHonorGuildListReq) String() string { return proto.CompactTextString(m) }
func (*GetHonorGuildListReq) ProtoMessage()    {}
func (*GetHonorGuildListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{17}
}

func (m *GetHonorGuildListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHonorGuildListReq.Unmarshal(m, b)
}
func (m *GetHonorGuildListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHonorGuildListReq.Marshal(b, m, deterministic)
}
func (m *GetHonorGuildListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHonorGuildListReq.Merge(m, src)
}
func (m *GetHonorGuildListReq) XXX_Size() int {
	return xxx_messageInfo_GetHonorGuildListReq.Size(m)
}
func (m *GetHonorGuildListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHonorGuildListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHonorGuildListReq proto.InternalMessageInfo

func (m *GetHonorGuildListReq) GetHonorType() HonorType {
	if m != nil {
		return m.HonorType
	}
	return HonorType_HONOR_GUILD
}

func (m *GetHonorGuildListReq) GetGuildType() GuildType {
	if m != nil {
		return m.GuildType
	}
	return GuildType_FUN_GUILD
}

func (m *GetHonorGuildListReq) GetGuildState() ShowState {
	if m != nil {
		return m.GuildState
	}
	return ShowState_SHOWING
}

func (m *GetHonorGuildListReq) GetOff() int64 {
	if m != nil {
		return m.Off
	}
	return 0
}

func (m *GetHonorGuildListReq) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetHonorGuildListResp struct {
	GuildInfoList        []*HonorGuild `protobuf:"bytes,1,rep,name=guild_info_list,json=guildInfoList,proto3" json:"guild_info_list,omitempty"`
	TotalCount           int64         `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetHonorGuildListResp) Reset()         { *m = GetHonorGuildListResp{} }
func (m *GetHonorGuildListResp) String() string { return proto.CompactTextString(m) }
func (*GetHonorGuildListResp) ProtoMessage()    {}
func (*GetHonorGuildListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{18}
}

func (m *GetHonorGuildListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHonorGuildListResp.Unmarshal(m, b)
}
func (m *GetHonorGuildListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHonorGuildListResp.Marshal(b, m, deterministic)
}
func (m *GetHonorGuildListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHonorGuildListResp.Merge(m, src)
}
func (m *GetHonorGuildListResp) XXX_Size() int {
	return xxx_messageInfo_GetHonorGuildListResp.Size(m)
}
func (m *GetHonorGuildListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHonorGuildListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHonorGuildListResp proto.InternalMessageInfo

func (m *GetHonorGuildListResp) GetGuildInfoList() []*HonorGuild {
	if m != nil {
		return m.GuildInfoList
	}
	return nil
}

func (m *GetHonorGuildListResp) GetTotalCount() int64 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

//单取
type GetHonorGuildReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHonorGuildReq) Reset()         { *m = GetHonorGuildReq{} }
func (m *GetHonorGuildReq) String() string { return proto.CompactTextString(m) }
func (*GetHonorGuildReq) ProtoMessage()    {}
func (*GetHonorGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{19}
}

func (m *GetHonorGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHonorGuildReq.Unmarshal(m, b)
}
func (m *GetHonorGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHonorGuildReq.Marshal(b, m, deterministic)
}
func (m *GetHonorGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHonorGuildReq.Merge(m, src)
}
func (m *GetHonorGuildReq) XXX_Size() int {
	return xxx_messageInfo_GetHonorGuildReq.Size(m)
}
func (m *GetHonorGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHonorGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHonorGuildReq proto.InternalMessageInfo

func (m *GetHonorGuildReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetHonorGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetHonorGuildResp struct {
	GuildInfo            *HonorGuild `protobuf:"bytes,1,opt,name=guild_info,json=guildInfo,proto3" json:"guild_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetHonorGuildResp) Reset()         { *m = GetHonorGuildResp{} }
func (m *GetHonorGuildResp) String() string { return proto.CompactTextString(m) }
func (*GetHonorGuildResp) ProtoMessage()    {}
func (*GetHonorGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{20}
}

func (m *GetHonorGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHonorGuildResp.Unmarshal(m, b)
}
func (m *GetHonorGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHonorGuildResp.Marshal(b, m, deterministic)
}
func (m *GetHonorGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHonorGuildResp.Merge(m, src)
}
func (m *GetHonorGuildResp) XXX_Size() int {
	return xxx_messageInfo_GetHonorGuildResp.Size(m)
}
func (m *GetHonorGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHonorGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHonorGuildResp proto.InternalMessageInfo

func (m *GetHonorGuildResp) GetGuildInfo() *HonorGuild {
	if m != nil {
		return m.GuildInfo
	}
	return nil
}

type GuildChannel struct {
	ChannelId            uint32    `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string    `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelDesc          string    `protobuf:"bytes,3,opt,name=channel_desc,json=channelDesc,proto3" json:"channel_desc,omitempty"`
	IndexWeight          int64     `protobuf:"varint,4,opt,name=index_weight,json=indexWeight,proto3" json:"index_weight,omitempty"`
	ChannelType          uint32    `protobuf:"varint,5,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ShowState            ShowState `protobuf:"varint,6,opt,name=show_state,json=showState,proto3,enum=guildhonorhalls.ShowState" json:"show_state,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GuildChannel) Reset()         { *m = GuildChannel{} }
func (m *GuildChannel) String() string { return proto.CompactTextString(m) }
func (*GuildChannel) ProtoMessage()    {}
func (*GuildChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{21}
}

func (m *GuildChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildChannel.Unmarshal(m, b)
}
func (m *GuildChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildChannel.Marshal(b, m, deterministic)
}
func (m *GuildChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildChannel.Merge(m, src)
}
func (m *GuildChannel) XXX_Size() int {
	return xxx_messageInfo_GuildChannel.Size(m)
}
func (m *GuildChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildChannel.DiscardUnknown(m)
}

var xxx_messageInfo_GuildChannel proto.InternalMessageInfo

func (m *GuildChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GuildChannel) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *GuildChannel) GetChannelDesc() string {
	if m != nil {
		return m.ChannelDesc
	}
	return ""
}

func (m *GuildChannel) GetIndexWeight() int64 {
	if m != nil {
		return m.IndexWeight
	}
	return 0
}

func (m *GuildChannel) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *GuildChannel) GetShowState() ShowState {
	if m != nil {
		return m.ShowState
	}
	return ShowState_SHOWING
}

//设置公会热门房间
type SetGuildHotChannelListReq struct {
	GuildId              uint32          `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelInfoList      []*GuildChannel `protobuf:"bytes,3,rep,name=channel_info_list,json=channelInfoList,proto3" json:"channel_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SetGuildHotChannelListReq) Reset()         { *m = SetGuildHotChannelListReq{} }
func (m *SetGuildHotChannelListReq) String() string { return proto.CompactTextString(m) }
func (*SetGuildHotChannelListReq) ProtoMessage()    {}
func (*SetGuildHotChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{22}
}

func (m *SetGuildHotChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGuildHotChannelListReq.Unmarshal(m, b)
}
func (m *SetGuildHotChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGuildHotChannelListReq.Marshal(b, m, deterministic)
}
func (m *SetGuildHotChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGuildHotChannelListReq.Merge(m, src)
}
func (m *SetGuildHotChannelListReq) XXX_Size() int {
	return xxx_messageInfo_SetGuildHotChannelListReq.Size(m)
}
func (m *SetGuildHotChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGuildHotChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGuildHotChannelListReq proto.InternalMessageInfo

func (m *SetGuildHotChannelListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SetGuildHotChannelListReq) GetChannelInfoList() []*GuildChannel {
	if m != nil {
		return m.ChannelInfoList
	}
	return nil
}

type SetGuildHotChannelListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGuildHotChannelListResp) Reset()         { *m = SetGuildHotChannelListResp{} }
func (m *SetGuildHotChannelListResp) String() string { return proto.CompactTextString(m) }
func (*SetGuildHotChannelListResp) ProtoMessage()    {}
func (*SetGuildHotChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{23}
}

func (m *SetGuildHotChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGuildHotChannelListResp.Unmarshal(m, b)
}
func (m *SetGuildHotChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGuildHotChannelListResp.Marshal(b, m, deterministic)
}
func (m *SetGuildHotChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGuildHotChannelListResp.Merge(m, src)
}
func (m *SetGuildHotChannelListResp) XXX_Size() int {
	return xxx_messageInfo_SetGuildHotChannelListResp.Size(m)
}
func (m *SetGuildHotChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGuildHotChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGuildHotChannelListResp proto.InternalMessageInfo

type GetGuildHotChannelListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildHotChannelListReq) Reset()         { *m = GetGuildHotChannelListReq{} }
func (m *GetGuildHotChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildHotChannelListReq) ProtoMessage()    {}
func (*GetGuildHotChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{24}
}

func (m *GetGuildHotChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildHotChannelListReq.Unmarshal(m, b)
}
func (m *GetGuildHotChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildHotChannelListReq.Marshal(b, m, deterministic)
}
func (m *GetGuildHotChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildHotChannelListReq.Merge(m, src)
}
func (m *GetGuildHotChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildHotChannelListReq.Size(m)
}
func (m *GetGuildHotChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildHotChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildHotChannelListReq proto.InternalMessageInfo

func (m *GetGuildHotChannelListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildHotChannelListReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type GetGuildHotChannelListResp struct {
	ChannelList          []*GuildChannel `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	IsSet                bool            `protobuf:"varint,2,opt,name=is_set,json=isSet,proto3" json:"is_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetGuildHotChannelListResp) Reset()         { *m = GetGuildHotChannelListResp{} }
func (m *GetGuildHotChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildHotChannelListResp) ProtoMessage()    {}
func (*GetGuildHotChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{25}
}

func (m *GetGuildHotChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildHotChannelListResp.Unmarshal(m, b)
}
func (m *GetGuildHotChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildHotChannelListResp.Marshal(b, m, deterministic)
}
func (m *GetGuildHotChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildHotChannelListResp.Merge(m, src)
}
func (m *GetGuildHotChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildHotChannelListResp.Size(m)
}
func (m *GetGuildHotChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildHotChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildHotChannelListResp proto.InternalMessageInfo

func (m *GetGuildHotChannelListResp) GetChannelList() []*GuildChannel {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetGuildHotChannelListResp) GetIsSet() bool {
	if m != nil {
		return m.IsSet
	}
	return false
}

//设置公会魅力成员
type GuildCharmMember struct {
	Uid                  uint32    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string    `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string    `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Alias                string    `protobuf:"bytes,4,opt,name=alias,proto3" json:"alias,omitempty"`
	ShowState            ShowState `protobuf:"varint,5,opt,name=show_state,json=showState,proto3,enum=guildhonorhalls.ShowState" json:"show_state,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GuildCharmMember) Reset()         { *m = GuildCharmMember{} }
func (m *GuildCharmMember) String() string { return proto.CompactTextString(m) }
func (*GuildCharmMember) ProtoMessage()    {}
func (*GuildCharmMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{26}
}

func (m *GuildCharmMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildCharmMember.Unmarshal(m, b)
}
func (m *GuildCharmMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildCharmMember.Marshal(b, m, deterministic)
}
func (m *GuildCharmMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildCharmMember.Merge(m, src)
}
func (m *GuildCharmMember) XXX_Size() int {
	return xxx_messageInfo_GuildCharmMember.Size(m)
}
func (m *GuildCharmMember) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildCharmMember.DiscardUnknown(m)
}

var xxx_messageInfo_GuildCharmMember proto.InternalMessageInfo

func (m *GuildCharmMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GuildCharmMember) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GuildCharmMember) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GuildCharmMember) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *GuildCharmMember) GetShowState() ShowState {
	if m != nil {
		return m.ShowState
	}
	return ShowState_SHOWING
}

type SetGuildCharmMemberListReq struct {
	GuildId              uint32              `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	MemberList           []*GuildCharmMember `protobuf:"bytes,2,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SetGuildCharmMemberListReq) Reset()         { *m = SetGuildCharmMemberListReq{} }
func (m *SetGuildCharmMemberListReq) String() string { return proto.CompactTextString(m) }
func (*SetGuildCharmMemberListReq) ProtoMessage()    {}
func (*SetGuildCharmMemberListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{27}
}

func (m *SetGuildCharmMemberListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGuildCharmMemberListReq.Unmarshal(m, b)
}
func (m *SetGuildCharmMemberListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGuildCharmMemberListReq.Marshal(b, m, deterministic)
}
func (m *SetGuildCharmMemberListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGuildCharmMemberListReq.Merge(m, src)
}
func (m *SetGuildCharmMemberListReq) XXX_Size() int {
	return xxx_messageInfo_SetGuildCharmMemberListReq.Size(m)
}
func (m *SetGuildCharmMemberListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGuildCharmMemberListReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGuildCharmMemberListReq proto.InternalMessageInfo

func (m *SetGuildCharmMemberListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SetGuildCharmMemberListReq) GetMemberList() []*GuildCharmMember {
	if m != nil {
		return m.MemberList
	}
	return nil
}

type SetGuildCharmMemberListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGuildCharmMemberListResp) Reset()         { *m = SetGuildCharmMemberListResp{} }
func (m *SetGuildCharmMemberListResp) String() string { return proto.CompactTextString(m) }
func (*SetGuildCharmMemberListResp) ProtoMessage()    {}
func (*SetGuildCharmMemberListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{28}
}

func (m *SetGuildCharmMemberListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGuildCharmMemberListResp.Unmarshal(m, b)
}
func (m *SetGuildCharmMemberListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGuildCharmMemberListResp.Marshal(b, m, deterministic)
}
func (m *SetGuildCharmMemberListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGuildCharmMemberListResp.Merge(m, src)
}
func (m *SetGuildCharmMemberListResp) XXX_Size() int {
	return xxx_messageInfo_SetGuildCharmMemberListResp.Size(m)
}
func (m *SetGuildCharmMemberListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGuildCharmMemberListResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGuildCharmMemberListResp proto.InternalMessageInfo

type GetGuildCharmMemberListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildCharmMemberListReq) Reset()         { *m = GetGuildCharmMemberListReq{} }
func (m *GetGuildCharmMemberListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildCharmMemberListReq) ProtoMessage()    {}
func (*GetGuildCharmMemberListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{29}
}

func (m *GetGuildCharmMemberListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCharmMemberListReq.Unmarshal(m, b)
}
func (m *GetGuildCharmMemberListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCharmMemberListReq.Marshal(b, m, deterministic)
}
func (m *GetGuildCharmMemberListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCharmMemberListReq.Merge(m, src)
}
func (m *GetGuildCharmMemberListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildCharmMemberListReq.Size(m)
}
func (m *GetGuildCharmMemberListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCharmMemberListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCharmMemberListReq proto.InternalMessageInfo

func (m *GetGuildCharmMemberListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildCharmMemberListResp struct {
	MemberList           []*GuildCharmMember `protobuf:"bytes,1,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	IsSet                bool                `protobuf:"varint,2,opt,name=is_set,json=isSet,proto3" json:"is_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetGuildCharmMemberListResp) Reset()         { *m = GetGuildCharmMemberListResp{} }
func (m *GetGuildCharmMemberListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildCharmMemberListResp) ProtoMessage()    {}
func (*GetGuildCharmMemberListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{30}
}

func (m *GetGuildCharmMemberListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCharmMemberListResp.Unmarshal(m, b)
}
func (m *GetGuildCharmMemberListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCharmMemberListResp.Marshal(b, m, deterministic)
}
func (m *GetGuildCharmMemberListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCharmMemberListResp.Merge(m, src)
}
func (m *GetGuildCharmMemberListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildCharmMemberListResp.Size(m)
}
func (m *GetGuildCharmMemberListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCharmMemberListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCharmMemberListResp proto.InternalMessageInfo

func (m *GetGuildCharmMemberListResp) GetMemberList() []*GuildCharmMember {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *GetGuildCharmMemberListResp) GetIsSet() bool {
	if m != nil {
		return m.IsSet
	}
	return false
}

//公会成员模糊搜索
type SearchGuildMemberReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchGuildMemberReq) Reset()         { *m = SearchGuildMemberReq{} }
func (m *SearchGuildMemberReq) String() string { return proto.CompactTextString(m) }
func (*SearchGuildMemberReq) ProtoMessage()    {}
func (*SearchGuildMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{31}
}

func (m *SearchGuildMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchGuildMemberReq.Unmarshal(m, b)
}
func (m *SearchGuildMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchGuildMemberReq.Marshal(b, m, deterministic)
}
func (m *SearchGuildMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchGuildMemberReq.Merge(m, src)
}
func (m *SearchGuildMemberReq) XXX_Size() int {
	return xxx_messageInfo_SearchGuildMemberReq.Size(m)
}
func (m *SearchGuildMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchGuildMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchGuildMemberReq proto.InternalMessageInfo

func (m *SearchGuildMemberReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SearchGuildMemberReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

type SearchGuildMemberResp struct {
	MemberList           []*GuildCharmMember `protobuf:"bytes,1,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SearchGuildMemberResp) Reset()         { *m = SearchGuildMemberResp{} }
func (m *SearchGuildMemberResp) String() string { return proto.CompactTextString(m) }
func (*SearchGuildMemberResp) ProtoMessage()    {}
func (*SearchGuildMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3f81f9901fd3083e, []int{32}
}

func (m *SearchGuildMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchGuildMemberResp.Unmarshal(m, b)
}
func (m *SearchGuildMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchGuildMemberResp.Marshal(b, m, deterministic)
}
func (m *SearchGuildMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchGuildMemberResp.Merge(m, src)
}
func (m *SearchGuildMemberResp) XXX_Size() int {
	return xxx_messageInfo_SearchGuildMemberResp.Size(m)
}
func (m *SearchGuildMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchGuildMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchGuildMemberResp proto.InternalMessageInfo

func (m *SearchGuildMemberResp) GetMemberList() []*GuildCharmMember {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func init() {
	proto.RegisterEnum("guildhonorhalls.HonorType", HonorType_name, HonorType_value)
	proto.RegisterEnum("guildhonorhalls.GuildType", GuildType_name, GuildType_value)
	proto.RegisterEnum("guildhonorhalls.ShowState", ShowState_name, ShowState_value)
	proto.RegisterType((*TagItem)(nil), "guildhonorhalls.TagItem")
	proto.RegisterType((*HonorTitle)(nil), "guildhonorhalls.HonorTitle")
	proto.RegisterType((*HonorGuild)(nil), "guildhonorhalls.HonorGuild")
	proto.RegisterType((*SetTagItemListReq)(nil), "guildhonorhalls.SetTagItemListReq")
	proto.RegisterType((*SetTagItemListResp)(nil), "guildhonorhalls.SetTagItemListResp")
	proto.RegisterType((*GetTagItemListReq)(nil), "guildhonorhalls.GetTagItemListReq")
	proto.RegisterType((*GetTagItemListResp)(nil), "guildhonorhalls.GetTagItemListResp")
	proto.RegisterType((*DelTagItemListReq)(nil), "guildhonorhalls.DelTagItemListReq")
	proto.RegisterType((*DelTagItemListResp)(nil), "guildhonorhalls.DelTagItemListResp")
	proto.RegisterType((*SetHonorTitleReq)(nil), "guildhonorhalls.SetHonorTitleReq")
	proto.RegisterType((*SetHonorTitleResp)(nil), "guildhonorhalls.SetHonorTitleResp")
	proto.RegisterType((*DelHonorTitleListReq)(nil), "guildhonorhalls.DelHonorTitleListReq")
	proto.RegisterType((*DelHonorTitleListResp)(nil), "guildhonorhalls.DelHonorTitleListResp")
	proto.RegisterType((*GetHonorTitleReq)(nil), "guildhonorhalls.GetHonorTitleReq")
	proto.RegisterType((*GetHonorTitleResp)(nil), "guildhonorhalls.GetHonorTitleResp")
	proto.RegisterType((*SetHonorGuildListReq)(nil), "guildhonorhalls.SetHonorGuildListReq")
	proto.RegisterType((*SetHonorGuildListResp)(nil), "guildhonorhalls.SetHonorGuildListResp")
	proto.RegisterType((*GetHonorGuildListReq)(nil), "guildhonorhalls.GetHonorGuildListReq")
	proto.RegisterType((*GetHonorGuildListResp)(nil), "guildhonorhalls.GetHonorGuildListResp")
	proto.RegisterType((*GetHonorGuildReq)(nil), "guildhonorhalls.GetHonorGuildReq")
	proto.RegisterType((*GetHonorGuildResp)(nil), "guildhonorhalls.GetHonorGuildResp")
	proto.RegisterType((*GuildChannel)(nil), "guildhonorhalls.GuildChannel")
	proto.RegisterType((*SetGuildHotChannelListReq)(nil), "guildhonorhalls.SetGuildHotChannelListReq")
	proto.RegisterType((*SetGuildHotChannelListResp)(nil), "guildhonorhalls.SetGuildHotChannelListResp")
	proto.RegisterType((*GetGuildHotChannelListReq)(nil), "guildhonorhalls.GetGuildHotChannelListReq")
	proto.RegisterType((*GetGuildHotChannelListResp)(nil), "guildhonorhalls.GetGuildHotChannelListResp")
	proto.RegisterType((*GuildCharmMember)(nil), "guildhonorhalls.GuildCharmMember")
	proto.RegisterType((*SetGuildCharmMemberListReq)(nil), "guildhonorhalls.SetGuildCharmMemberListReq")
	proto.RegisterType((*SetGuildCharmMemberListResp)(nil), "guildhonorhalls.SetGuildCharmMemberListResp")
	proto.RegisterType((*GetGuildCharmMemberListReq)(nil), "guildhonorhalls.GetGuildCharmMemberListReq")
	proto.RegisterType((*GetGuildCharmMemberListResp)(nil), "guildhonorhalls.GetGuildCharmMemberListResp")
	proto.RegisterType((*SearchGuildMemberReq)(nil), "guildhonorhalls.SearchGuildMemberReq")
	proto.RegisterType((*SearchGuildMemberResp)(nil), "guildhonorhalls.SearchGuildMemberResp")
}

func init() { proto.RegisterFile("guild-honor-halls.proto", fileDescriptor_3f81f9901fd3083e) }

var fileDescriptor_3f81f9901fd3083e = []byte{
	// 1441 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0xeb, 0x6e, 0xdb, 0xc6,
	0x12, 0x36, 0x25, 0xdb, 0x12, 0x47, 0x96, 0x2c, 0x6f, 0x9c, 0x84, 0x51, 0x8e, 0x71, 0x9c, 0x3d,
	0xc8, 0x81, 0xe1, 0x73, 0xe2, 0x20, 0x0e, 0x8a, 0xa2, 0x09, 0x7a, 0x71, 0x6c, 0x75, 0x45, 0x40,
	0xb1, 0x5b, 0x4a, 0x6e, 0x9a, 0x06, 0x05, 0xc1, 0x88, 0x6b, 0x89, 0x28, 0x45, 0x32, 0xe2, 0x3a,
	0x17, 0x14, 0x05, 0xd2, 0x37, 0xe9, 0xcf, 0x02, 0x7d, 0x96, 0x3e, 0x4c, 0x81, 0x3e, 0x40, 0xb1,
	0x43, 0x52, 0x17, 0x8a, 0xb2, 0xa4, 0x20, 0xff, 0x76, 0x67, 0xe7, 0xf2, 0xcd, 0xec, 0xb7, 0x3b,
	0x5c, 0xc2, 0xcd, 0xee, 0xa5, 0xe3, 0xda, 0xf7, 0x7a, 0xbe, 0xe7, 0x0f, 0xee, 0xf5, 0x2c, 0xd7,
	0x0d, 0x0f, 0x82, 0x81, 0x2f, 0x7c, 0xb2, 0x89, 0x0b, 0x28, 0x47, 0x31, 0x7d, 0x00, 0x85, 0xb6,
	0xd5, 0xd5, 0x05, 0xef, 0x13, 0x02, 0xab, 0x82, 0xbf, 0x15, 0x9a, 0xb2, 0xab, 0xec, 0xa9, 0x06,
	0x8e, 0xa5, 0x2c, 0xb0, 0x06, 0x96, 0x96, 0x8b, 0x64, 0x72, 0x4c, 0x1f, 0x03, 0x34, 0xa4, 0x83,
	0xb6, 0x23, 0x5c, 0x9e, 0x69, 0x75, 0x0b, 0x8a, 0x4e, 0xc7, 0xf7, 0xcc, 0xcb, 0x81, 0x1b, 0x5b,
	0x16, 0xe4, 0xfc, 0x7c, 0xe0, 0xd2, 0xdf, 0x56, 0x63, 0x6b, 0x26, 0x81, 0x90, 0x0a, 0xe4, 0x1c,
	0x1b, 0x6d, 0xf3, 0x46, 0xce, 0xb1, 0xa5, 0x25, 0x22, 0x34, 0x1d, 0x1b, 0x2d, 0xcb, 0x46, 0x01,
	0xe7, 0x3a, 0x2e, 0x85, 0x3d, 0x7f, 0x20, 0xe4, 0x52, 0x3e, 0x5a, 0xc2, 0xb9, 0x6e, 0x93, 0x1d,
	0x80, 0x97, 0xbc, 0xeb, 0x78, 0xa6, 0x70, 0xfa, 0x5c, 0x5b, 0x45, 0x6f, 0x2a, 0x4a, 0xda, 0x4e,
	0x9f, 0x4b, 0x4b, 0xee, 0xd9, 0xd1, 0xe2, 0x1a, 0x2e, 0x16, 0xb8, 0x67, 0xe3, 0xd2, 0x1d, 0xd8,
	0x70, 0x3c, 0x9b, 0xbf, 0x35, 0xdf, 0x70, 0xa7, 0xdb, 0x13, 0xda, 0x3a, 0x2e, 0x97, 0x50, 0xf6,
	0x0c, 0x45, 0xe4, 0x0b, 0xd8, 0x90, 0xf5, 0xba, 0x1c, 0x98, 0x42, 0x26, 0xac, 0x15, 0x76, 0xf3,
	0x7b, 0xa5, 0xc3, 0xdb, 0x07, 0xa9, 0x4a, 0x1e, 0x8c, 0x6a, 0x62, 0x94, 0x22, 0x83, 0xa8, 0x40,
	0x3b, 0x00, 0x7d, 0xde, 0x7f, 0xc9, 0x07, 0x66, 0xc7, 0x13, 0x5a, 0x31, 0x02, 0x17, 0x49, 0x8e,
	0x3d, 0x21, 0x97, 0xa3, 0x8c, 0x3d, 0xab, 0xcf, 0x35, 0x15, 0xab, 0xa5, 0xa2, 0xe4, 0xd4, 0xea,
	0xf3, 0xd1, 0xb2, 0xcd, 0xc3, 0x8e, 0x06, 0x63, 0xcb, 0x27, 0x3c, 0xec, 0x90, 0xbb, 0x50, 0x71,
	0x42, 0xb3, 0xe3, 0xfb, 0x01, 0x1f, 0x58, 0xc2, 0xf1, 0x3d, 0xad, 0xb4, 0xab, 0xec, 0x15, 0x8d,
	0xb2, 0x13, 0x1e, 0x8f, 0x84, 0xe4, 0x21, 0x14, 0x85, 0xd5, 0x35, 0x5d, 0x27, 0x14, 0xda, 0x06,
	0xe2, 0xd7, 0xa6, 0xf0, 0xc7, 0x34, 0x30, 0x0a, 0xc2, 0xea, 0x36, 0x9d, 0x50, 0x90, 0xc7, 0x50,
	0x8a, 0x42, 0x87, 0xc2, 0x12, 0x5c, 0x2b, 0xef, 0x2a, 0x7b, 0x95, 0xc3, 0xda, 0x94, 0x5d, 0xab,
	0xe7, 0xbf, 0x69, 0x49, 0x0d, 0x23, 0x42, 0x8a, 0x63, 0xf2, 0x19, 0x00, 0xea, 0x98, 0xe2, 0x5d,
	0xc0, 0xb5, 0xca, 0x0c, 0xdb, 0xa8, 0x66, 0xef, 0x02, 0x6e, 0xa8, 0xbd, 0x64, 0x48, 0x4d, 0xd8,
	0x6a, 0x71, 0x11, 0xc3, 0x91, 0x48, 0x0c, 0xfe, 0x2a, 0xc9, 0xc0, 0x11, 0xbc, 0xaf, 0x29, 0x0b,
	0x64, 0x80, 0x8c, 0xbe, 0x0e, 0xeb, 0x4e, 0x68, 0x5a, 0x76, 0xc4, 0xa5, 0xa2, 0xb1, 0xe6, 0x84,
	0x47, 0xb6, 0x4d, 0xb7, 0x81, 0xa4, 0x03, 0x84, 0x01, 0xbd, 0x06, 0x5b, 0x2c, 0x1d, 0x96, 0xea,
	0x40, 0xd8, 0x94, 0xea, 0x44, 0x39, 0x95, 0x05, 0xcb, 0x49, 0x1b, 0xb0, 0x75, 0xc2, 0xdd, 0xec,
	0xb4, 0x96, 0xf3, 0xb4, 0x0d, 0x24, 0xed, 0x29, 0x0c, 0x68, 0x00, 0xd5, 0x16, 0x17, 0x63, 0x2c,
	0xe4, 0xaf, 0x48, 0x1d, 0xaa, 0xf1, 0x2e, 0x48, 0xc9, 0x78, 0x98, 0x2b, 0xf9, 0x5b, 0xe9, 0x0d,
	0xc7, 0xc8, 0x84, 0x19, 0x75, 0xbc, 0x86, 0x1b, 0x35, 0x1e, 0x31, 0x0c, 0xe8, 0x8f, 0xb0, 0x7d,
	0xc2, 0xdd, 0xc6, 0x84, 0x83, 0x8f, 0x07, 0x85, 0xde, 0x84, 0xeb, 0x19, 0xee, 0xc3, 0x80, 0x12,
	0xa8, 0xb2, 0x54, 0xfa, 0xf4, 0x0c, 0xb7, 0x74, 0x12, 0x20, 0x79, 0x04, 0xb0, 0x1c, 0x04, 0x55,
	0x0c, 0xa3, 0xff, 0xa9, 0xc0, 0x76, 0x92, 0x32, 0x5e, 0x60, 0x49, 0x76, 0x5f, 0x42, 0x39, 0xb9,
	0xb7, 0x16, 0xf0, 0x8b, 0xa6, 0x46, 0x29, 0xbe, 0xd9, 0xb0, 0xc4, 0x93, 0xe7, 0x25, 0xb7, 0xc4,
	0x79, 0x91, 0xa6, 0x51, 0x6c, 0x34, 0xcd, 0xcf, 0x30, 0xc5, 0x98, 0x91, 0x69, 0x37, 0x19, 0xca,
	0x6a, 0x66, 0xa4, 0x13, 0x06, 0xf4, 0x2f, 0x05, 0xb6, 0x59, 0x56, 0xa2, 0x93, 0x38, 0x95, 0x0f,
	0xc7, 0x99, 0x5b, 0x02, 0x67, 0xfa, 0x2a, 0xca, 0x2f, 0x75, 0x15, 0x55, 0x21, 0xef, 0x5f, 0x5c,
	0xc4, 0x6d, 0x41, 0x0e, 0xc9, 0x36, 0xac, 0x75, 0xfc, 0x4b, 0x4f, 0xc4, 0xdd, 0x20, 0x9a, 0xd0,
	0x5f, 0xe0, 0x3a, 0xcb, 0x2a, 0x06, 0x39, 0x86, 0xcd, 0x78, 0x73, 0xbd, 0x0b, 0x7f, 0xe1, 0xed,
	0x8d, 0x08, 0xa1, 0x7b, 0x17, 0x3e, 0x6e, 0xf0, 0xbf, 0xa1, 0x24, 0x7c, 0x61, 0xb9, 0x66, 0x14,
	0x39, 0x87, 0x91, 0x01, 0x45, 0xc7, 0x18, 0xfe, 0xf3, 0x11, 0x81, 0x23, 0x07, 0xfc, 0xd5, 0x58,
	0x7b, 0x2c, 0xcf, 0x69, 0x8f, 0xe3, 0x5c, 0x8f, 0xcd, 0x23, 0xae, 0x8f, 0x90, 0xa3, 0x9f, 0x39,
	0xa0, 0xd5, 0x21, 0x68, 0xfa, 0xb7, 0x02, 0x1b, 0x28, 0x3c, 0xee, 0x59, 0x9e, 0xc7, 0x5d, 0xd9,
	0x8a, 0x3a, 0xd1, 0xd0, 0x1c, 0x82, 0x52, 0x63, 0x89, 0x6e, 0xcb, 0x56, 0x9a, 0x2c, 0x63, 0x2b,
	0x8b, 0x1a, 0x7f, 0x29, 0x96, 0x61, 0x33, 0x1b, 0x53, 0xc1, 0x76, 0x96, 0x9f, 0x50, 0xc1, 0x86,
	0x96, 0x6e, 0xc8, 0xab, 0xd3, 0x0d, 0x79, 0xcc, 0x0b, 0x32, 0x69, 0x0d, 0x91, 0x24, 0x5e, 0x12,
	0xaa, 0x85, 0x3d, 0xff, 0x4d, 0x4c, 0x97, 0xf5, 0xb9, 0x74, 0x51, 0xc3, 0x64, 0x48, 0x7f, 0x55,
	0xe0, 0x56, 0x8b, 0x0b, 0xcc, 0xbc, 0xe1, 0x8b, 0x38, 0xf9, 0x84, 0xfe, 0xe3, 0x1b, 0xa0, 0x4c,
	0x7e, 0x9f, 0xe8, 0xb0, 0x35, 0x2c, 0xcf, 0x90, 0x27, 0x79, 0xe4, 0xc9, 0x4e, 0x36, 0xcb, 0x63,
	0xdf, 0xc6, 0x66, 0x52, 0xc4, 0x98, 0x2b, 0xf4, 0x5f, 0x50, 0x9b, 0x05, 0x21, 0x0c, 0xe8, 0x73,
	0xb8, 0xc5, 0x3e, 0x04, 0x60, 0xba, 0x6e, 0xb9, 0xa9, 0xba, 0xd1, 0x4b, 0xa8, 0xb1, 0x99, 0x81,
	0xc9, 0x57, 0x23, 0x07, 0x63, 0x87, 0x60, 0x4e, 0x72, 0x89, 0xff, 0xb1, 0x46, 0x12, 0x72, 0x31,
	0x6a, 0x24, 0x2d, 0x2e, 0xe8, 0x1f, 0x0a, 0x54, 0x13, 0xa3, 0x41, 0xff, 0x29, 0x7e, 0x1b, 0xc9,
	0x63, 0x7b, 0x39, 0x4c, 0x42, 0x0e, 0x49, 0x0d, 0x8a, 0x9e, 0xd3, 0xf9, 0x69, 0x8c, 0x5d, 0xc3,
	0x39, 0xd1, 0xa0, 0x60, 0x75, 0xa2, 0xa3, 0x15, 0xb1, 0x2a, 0x99, 0xca, 0xc3, 0x6e, 0xb9, 0x8e,
	0x15, 0x22, 0x95, 0x54, 0x23, 0x9a, 0xa4, 0x18, 0xb2, 0xb6, 0x0c, 0x43, 0x7e, 0x1e, 0xed, 0xce,
	0x18, 0xde, 0x05, 0x36, 0xe0, 0x09, 0x94, 0xe2, 0x2f, 0x41, 0x2c, 0x5f, 0x0e, 0xcb, 0x77, 0x67,
	0x66, 0xf9, 0x12, 0xcf, 0x46, 0xfc, 0xfd, 0x88, 0xd4, 0xd8, 0x81, 0xdb, 0x33, 0x83, 0x87, 0x01,
	0xfd, 0x74, 0xb4, 0x81, 0x4b, 0x61, 0xa3, 0x6f, 0xe1, 0x36, 0x9b, 0xed, 0x37, 0x0d, 0x5d, 0xf9,
	0x00, 0xe8, 0xb3, 0x36, 0xff, 0xa9, 0x6c, 0xa9, 0xd6, 0xa0, 0xd3, 0x43, 0xe3, 0xd8, 0xee, 0xea,
	0x42, 0x5e, 0x41, 0x04, 0xfa, 0x42, 0xb6, 0xb4, 0x29, 0x77, 0x1f, 0x27, 0x85, 0xfd, 0x13, 0x50,
	0x87, 0xad, 0x8d, 0x6c, 0x42, 0xa9, 0x71, 0x76, 0x7a, 0x66, 0x98, 0xec, 0x5c, 0x6f, 0x9e, 0x54,
	0x57, 0xc8, 0x0d, 0x20, 0x0d, 0x9d, 0x35, 0xcc, 0x6f, 0xcf, 0x8f, 0x9a, 0x7a, 0xfb, 0x79, 0x2c,
	0x57, 0x48, 0x05, 0xd4, 0xa3, 0x66, 0xd3, 0x44, 0xe5, 0xea, 0xfb, 0xe2, 0xfe, 0x23, 0x50, 0x87,
	0x5d, 0x8e, 0x94, 0x41, 0xfd, 0xfa, 0xfc, 0x74, 0xe8, 0xa3, 0x02, 0xd0, 0xd4, 0xbf, 0xab, 0xa7,
	0x6d, 0xa3, 0xe9, 0xfb, 0xe2, 0xfe, 0x37, 0xa0, 0x0e, 0x49, 0x49, 0x4a, 0x50, 0x68, 0x35, 0xce,
	0x9e, 0xe9, 0xa7, 0xac, 0xba, 0x22, 0x27, 0x6d, 0xfd, 0x69, 0xfd, 0xec, 0xbc, 0x5d, 0x55, 0x08,
	0xc0, 0xfa, 0x49, 0xbd, 0x59, 0x6f, 0xd7, 0xab, 0x39, 0x19, 0xe1, 0xd9, 0x91, 0xde, 0x36, 0xa5,
	0x6a, 0x35, 0x9f, 0x78, 0x6c, 0xb5, 0x8f, 0xda, 0xf5, 0xea, 0xfb, 0xe2, 0xe1, 0xef, 0x25, 0xd8,
	0x8c, 0x4f, 0xbc, 0xe7, 0x0f, 0x1a, 0xb2, 0x08, 0xc4, 0x1e, 0x7d, 0xd9, 0x0d, 0x5b, 0x21, 0xb9,
	0x3b, 0x7d, 0x3c, 0x32, 0xbe, 0x10, 0x6a, 0xff, 0x5d, 0x44, 0x2d, 0x0c, 0xe8, 0x8a, 0x8c, 0xc2,
	0x16, 0x88, 0xc2, 0x16, 0x8b, 0xc2, 0x66, 0x44, 0xf9, 0x1e, 0xca, 0x13, 0x4b, 0xe4, 0xce, 0xd5,
	0xa6, 0xd2, 0x3b, 0x9d, 0xa7, 0x82, 0x9e, 0x43, 0xb8, 0x91, 0x7d, 0x4d, 0x93, 0xfd, 0xac, 0x1a,
	0x64, 0xdf, 0xd8, 0xb5, 0xff, 0x2d, 0xac, 0x9b, 0x04, 0x65, 0x8b, 0x06, 0x65, 0x4b, 0x04, 0x65,
	0x57, 0x05, 0x7d, 0x0d, 0x37, 0x67, 0xdc, 0x3a, 0x64, 0x36, 0xfc, 0xe9, 0x0b, 0xa8, 0xf6, 0xff,
	0xc5, 0x95, 0x93, 0xb8, 0x6c, 0xe1, 0xb8, 0x6c, 0x99, 0xb8, 0xec, 0xca, 0xb8, 0xc8, 0xff, 0xd4,
	0x25, 0x92, 0xc9, 0xff, 0xe9, 0x7b, 0x2b, 0x93, 0xff, 0x19, 0xf7, 0x11, 0x5d, 0x21, 0x2f, 0xa0,
	0x32, 0xf9, 0x0e, 0x25, 0x34, 0xab, 0x3e, 0x93, 0x4f, 0xc6, 0xda, 0x7f, 0xe6, 0xea, 0x24, 0xb4,
	0x9f, 0x78, 0x9c, 0x65, 0xd0, 0x3e, 0xfd, 0x5c, 0xac, 0xd1, 0x79, 0x2a, 0x09, 0x6c, 0x36, 0x0f,
	0x36, 0x5b, 0x00, 0x36, 0x9b, 0x01, 0x9b, 0xcd, 0x81, 0xcd, 0xe6, 0xc3, 0x66, 0xd9, 0xb0, 0x27,
	0x5f, 0xcd, 0x19, 0xb0, 0xa7, 0x1e, 0xe8, 0x19, 0xb0, 0x33, 0x9e, 0xde, 0x48, 0x98, 0xa9, 0x67,
	0x69, 0x06, 0x61, 0xb2, 0x5e, 0xc6, 0x19, 0x84, 0xc9, 0x7e, 0xe1, 0xae, 0x3c, 0x79, 0xf8, 0xc3,
	0x83, 0xae, 0xef, 0x5a, 0x5e, 0xf7, 0xe0, 0x93, 0x43, 0x21, 0x0e, 0x3a, 0x7e, 0xff, 0x3e, 0xfe,
	0xd6, 0xeb, 0xf8, 0xee, 0xfd, 0x90, 0x0f, 0x5e, 0x3b, 0x1d, 0x1e, 0xde, 0x4f, 0x39, 0x7b, 0xb9,
	0x8e, 0x2a, 0x0f, 0xff, 0x09, 0x00, 0x00, 0xff, 0xff, 0x04, 0xd0, 0x83, 0x2d, 0x14, 0x14, 0x00,
	0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GuildHonorHallsClient is the client API for GuildHonorHalls service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GuildHonorHallsClient interface {
	SetHonorGuildList(ctx context.Context, in *SetHonorGuildListReq, opts ...grpc.CallOption) (*SetHonorGuildListResp, error)
	GetHonorGuildList(ctx context.Context, in *GetHonorGuildListReq, opts ...grpc.CallOption) (*GetHonorGuildListResp, error)
	GetHonorGuild(ctx context.Context, in *GetHonorGuildReq, opts ...grpc.CallOption) (*GetHonorGuildResp, error)
	SetGuildHotChannelList(ctx context.Context, in *SetGuildHotChannelListReq, opts ...grpc.CallOption) (*SetGuildHotChannelListResp, error)
	GetGuildHotChannelList(ctx context.Context, in *GetGuildHotChannelListReq, opts ...grpc.CallOption) (*GetGuildHotChannelListResp, error)
	SetGuildCharmMemberList(ctx context.Context, in *SetGuildCharmMemberListReq, opts ...grpc.CallOption) (*SetGuildCharmMemberListResp, error)
	GetGuildCharmMemberList(ctx context.Context, in *GetGuildCharmMemberListReq, opts ...grpc.CallOption) (*GetGuildCharmMemberListResp, error)
	SearchGuildMember(ctx context.Context, in *SearchGuildMemberReq, opts ...grpc.CallOption) (*SearchGuildMemberResp, error)
	SetTagItemList(ctx context.Context, in *SetTagItemListReq, opts ...grpc.CallOption) (*SetTagItemListResp, error)
	SetHonorTitle(ctx context.Context, in *SetHonorTitleReq, opts ...grpc.CallOption) (*SetHonorTitleResp, error)
	GetTagItemList(ctx context.Context, in *GetTagItemListReq, opts ...grpc.CallOption) (*GetTagItemListResp, error)
	GetHonorTitle(ctx context.Context, in *GetHonorTitleReq, opts ...grpc.CallOption) (*GetHonorTitleResp, error)
	DelTagItemList(ctx context.Context, in *DelTagItemListReq, opts ...grpc.CallOption) (*DelTagItemListResp, error)
	DelHonorTitleList(ctx context.Context, in *DelHonorTitleListReq, opts ...grpc.CallOption) (*DelHonorTitleListResp, error)
}

type guildHonorHallsClient struct {
	cc *grpc.ClientConn
}

func NewGuildHonorHallsClient(cc *grpc.ClientConn) GuildHonorHallsClient {
	return &guildHonorHallsClient{cc}
}

func (c *guildHonorHallsClient) SetHonorGuildList(ctx context.Context, in *SetHonorGuildListReq, opts ...grpc.CallOption) (*SetHonorGuildListResp, error) {
	out := new(SetHonorGuildListResp)
	err := c.cc.Invoke(ctx, "/guildhonorhalls.GuildHonorHalls/SetHonorGuildList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildHonorHallsClient) GetHonorGuildList(ctx context.Context, in *GetHonorGuildListReq, opts ...grpc.CallOption) (*GetHonorGuildListResp, error) {
	out := new(GetHonorGuildListResp)
	err := c.cc.Invoke(ctx, "/guildhonorhalls.GuildHonorHalls/GetHonorGuildList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildHonorHallsClient) GetHonorGuild(ctx context.Context, in *GetHonorGuildReq, opts ...grpc.CallOption) (*GetHonorGuildResp, error) {
	out := new(GetHonorGuildResp)
	err := c.cc.Invoke(ctx, "/guildhonorhalls.GuildHonorHalls/GetHonorGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildHonorHallsClient) SetGuildHotChannelList(ctx context.Context, in *SetGuildHotChannelListReq, opts ...grpc.CallOption) (*SetGuildHotChannelListResp, error) {
	out := new(SetGuildHotChannelListResp)
	err := c.cc.Invoke(ctx, "/guildhonorhalls.GuildHonorHalls/SetGuildHotChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildHonorHallsClient) GetGuildHotChannelList(ctx context.Context, in *GetGuildHotChannelListReq, opts ...grpc.CallOption) (*GetGuildHotChannelListResp, error) {
	out := new(GetGuildHotChannelListResp)
	err := c.cc.Invoke(ctx, "/guildhonorhalls.GuildHonorHalls/GetGuildHotChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildHonorHallsClient) SetGuildCharmMemberList(ctx context.Context, in *SetGuildCharmMemberListReq, opts ...grpc.CallOption) (*SetGuildCharmMemberListResp, error) {
	out := new(SetGuildCharmMemberListResp)
	err := c.cc.Invoke(ctx, "/guildhonorhalls.GuildHonorHalls/SetGuildCharmMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildHonorHallsClient) GetGuildCharmMemberList(ctx context.Context, in *GetGuildCharmMemberListReq, opts ...grpc.CallOption) (*GetGuildCharmMemberListResp, error) {
	out := new(GetGuildCharmMemberListResp)
	err := c.cc.Invoke(ctx, "/guildhonorhalls.GuildHonorHalls/GetGuildCharmMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildHonorHallsClient) SearchGuildMember(ctx context.Context, in *SearchGuildMemberReq, opts ...grpc.CallOption) (*SearchGuildMemberResp, error) {
	out := new(SearchGuildMemberResp)
	err := c.cc.Invoke(ctx, "/guildhonorhalls.GuildHonorHalls/SearchGuildMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildHonorHallsClient) SetTagItemList(ctx context.Context, in *SetTagItemListReq, opts ...grpc.CallOption) (*SetTagItemListResp, error) {
	out := new(SetTagItemListResp)
	err := c.cc.Invoke(ctx, "/guildhonorhalls.GuildHonorHalls/SetTagItemList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildHonorHallsClient) SetHonorTitle(ctx context.Context, in *SetHonorTitleReq, opts ...grpc.CallOption) (*SetHonorTitleResp, error) {
	out := new(SetHonorTitleResp)
	err := c.cc.Invoke(ctx, "/guildhonorhalls.GuildHonorHalls/SetHonorTitle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildHonorHallsClient) GetTagItemList(ctx context.Context, in *GetTagItemListReq, opts ...grpc.CallOption) (*GetTagItemListResp, error) {
	out := new(GetTagItemListResp)
	err := c.cc.Invoke(ctx, "/guildhonorhalls.GuildHonorHalls/GetTagItemList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildHonorHallsClient) GetHonorTitle(ctx context.Context, in *GetHonorTitleReq, opts ...grpc.CallOption) (*GetHonorTitleResp, error) {
	out := new(GetHonorTitleResp)
	err := c.cc.Invoke(ctx, "/guildhonorhalls.GuildHonorHalls/GetHonorTitle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildHonorHallsClient) DelTagItemList(ctx context.Context, in *DelTagItemListReq, opts ...grpc.CallOption) (*DelTagItemListResp, error) {
	out := new(DelTagItemListResp)
	err := c.cc.Invoke(ctx, "/guildhonorhalls.GuildHonorHalls/DelTagItemList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildHonorHallsClient) DelHonorTitleList(ctx context.Context, in *DelHonorTitleListReq, opts ...grpc.CallOption) (*DelHonorTitleListResp, error) {
	out := new(DelHonorTitleListResp)
	err := c.cc.Invoke(ctx, "/guildhonorhalls.GuildHonorHalls/DelHonorTitleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GuildHonorHallsServer is the server API for GuildHonorHalls service.
type GuildHonorHallsServer interface {
	SetHonorGuildList(context.Context, *SetHonorGuildListReq) (*SetHonorGuildListResp, error)
	GetHonorGuildList(context.Context, *GetHonorGuildListReq) (*GetHonorGuildListResp, error)
	GetHonorGuild(context.Context, *GetHonorGuildReq) (*GetHonorGuildResp, error)
	SetGuildHotChannelList(context.Context, *SetGuildHotChannelListReq) (*SetGuildHotChannelListResp, error)
	GetGuildHotChannelList(context.Context, *GetGuildHotChannelListReq) (*GetGuildHotChannelListResp, error)
	SetGuildCharmMemberList(context.Context, *SetGuildCharmMemberListReq) (*SetGuildCharmMemberListResp, error)
	GetGuildCharmMemberList(context.Context, *GetGuildCharmMemberListReq) (*GetGuildCharmMemberListResp, error)
	SearchGuildMember(context.Context, *SearchGuildMemberReq) (*SearchGuildMemberResp, error)
	SetTagItemList(context.Context, *SetTagItemListReq) (*SetTagItemListResp, error)
	SetHonorTitle(context.Context, *SetHonorTitleReq) (*SetHonorTitleResp, error)
	GetTagItemList(context.Context, *GetTagItemListReq) (*GetTagItemListResp, error)
	GetHonorTitle(context.Context, *GetHonorTitleReq) (*GetHonorTitleResp, error)
	DelTagItemList(context.Context, *DelTagItemListReq) (*DelTagItemListResp, error)
	DelHonorTitleList(context.Context, *DelHonorTitleListReq) (*DelHonorTitleListResp, error)
}

func RegisterGuildHonorHallsServer(s *grpc.Server, srv GuildHonorHallsServer) {
	s.RegisterService(&_GuildHonorHalls_serviceDesc, srv)
}

func _GuildHonorHalls_SetHonorGuildList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetHonorGuildListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildHonorHallsServer).SetHonorGuildList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildhonorhalls.GuildHonorHalls/SetHonorGuildList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildHonorHallsServer).SetHonorGuildList(ctx, req.(*SetHonorGuildListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildHonorHalls_GetHonorGuildList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHonorGuildListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildHonorHallsServer).GetHonorGuildList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildhonorhalls.GuildHonorHalls/GetHonorGuildList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildHonorHallsServer).GetHonorGuildList(ctx, req.(*GetHonorGuildListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildHonorHalls_GetHonorGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHonorGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildHonorHallsServer).GetHonorGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildhonorhalls.GuildHonorHalls/GetHonorGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildHonorHallsServer).GetHonorGuild(ctx, req.(*GetHonorGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildHonorHalls_SetGuildHotChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGuildHotChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildHonorHallsServer).SetGuildHotChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildhonorhalls.GuildHonorHalls/SetGuildHotChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildHonorHallsServer).SetGuildHotChannelList(ctx, req.(*SetGuildHotChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildHonorHalls_GetGuildHotChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildHotChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildHonorHallsServer).GetGuildHotChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildhonorhalls.GuildHonorHalls/GetGuildHotChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildHonorHallsServer).GetGuildHotChannelList(ctx, req.(*GetGuildHotChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildHonorHalls_SetGuildCharmMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGuildCharmMemberListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildHonorHallsServer).SetGuildCharmMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildhonorhalls.GuildHonorHalls/SetGuildCharmMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildHonorHallsServer).SetGuildCharmMemberList(ctx, req.(*SetGuildCharmMemberListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildHonorHalls_GetGuildCharmMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildCharmMemberListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildHonorHallsServer).GetGuildCharmMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildhonorhalls.GuildHonorHalls/GetGuildCharmMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildHonorHallsServer).GetGuildCharmMemberList(ctx, req.(*GetGuildCharmMemberListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildHonorHalls_SearchGuildMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchGuildMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildHonorHallsServer).SearchGuildMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildhonorhalls.GuildHonorHalls/SearchGuildMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildHonorHallsServer).SearchGuildMember(ctx, req.(*SearchGuildMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildHonorHalls_SetTagItemList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTagItemListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildHonorHallsServer).SetTagItemList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildhonorhalls.GuildHonorHalls/SetTagItemList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildHonorHallsServer).SetTagItemList(ctx, req.(*SetTagItemListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildHonorHalls_SetHonorTitle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetHonorTitleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildHonorHallsServer).SetHonorTitle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildhonorhalls.GuildHonorHalls/SetHonorTitle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildHonorHallsServer).SetHonorTitle(ctx, req.(*SetHonorTitleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildHonorHalls_GetTagItemList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTagItemListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildHonorHallsServer).GetTagItemList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildhonorhalls.GuildHonorHalls/GetTagItemList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildHonorHallsServer).GetTagItemList(ctx, req.(*GetTagItemListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildHonorHalls_GetHonorTitle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHonorTitleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildHonorHallsServer).GetHonorTitle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildhonorhalls.GuildHonorHalls/GetHonorTitle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildHonorHallsServer).GetHonorTitle(ctx, req.(*GetHonorTitleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildHonorHalls_DelTagItemList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTagItemListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildHonorHallsServer).DelTagItemList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildhonorhalls.GuildHonorHalls/DelTagItemList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildHonorHallsServer).DelTagItemList(ctx, req.(*DelTagItemListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildHonorHalls_DelHonorTitleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelHonorTitleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildHonorHallsServer).DelHonorTitleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildhonorhalls.GuildHonorHalls/DelHonorTitleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildHonorHallsServer).DelHonorTitleList(ctx, req.(*DelHonorTitleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GuildHonorHalls_serviceDesc = grpc.ServiceDesc{
	ServiceName: "guildhonorhalls.GuildHonorHalls",
	HandlerType: (*GuildHonorHallsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetHonorGuildList",
			Handler:    _GuildHonorHalls_SetHonorGuildList_Handler,
		},
		{
			MethodName: "GetHonorGuildList",
			Handler:    _GuildHonorHalls_GetHonorGuildList_Handler,
		},
		{
			MethodName: "GetHonorGuild",
			Handler:    _GuildHonorHalls_GetHonorGuild_Handler,
		},
		{
			MethodName: "SetGuildHotChannelList",
			Handler:    _GuildHonorHalls_SetGuildHotChannelList_Handler,
		},
		{
			MethodName: "GetGuildHotChannelList",
			Handler:    _GuildHonorHalls_GetGuildHotChannelList_Handler,
		},
		{
			MethodName: "SetGuildCharmMemberList",
			Handler:    _GuildHonorHalls_SetGuildCharmMemberList_Handler,
		},
		{
			MethodName: "GetGuildCharmMemberList",
			Handler:    _GuildHonorHalls_GetGuildCharmMemberList_Handler,
		},
		{
			MethodName: "SearchGuildMember",
			Handler:    _GuildHonorHalls_SearchGuildMember_Handler,
		},
		{
			MethodName: "SetTagItemList",
			Handler:    _GuildHonorHalls_SetTagItemList_Handler,
		},
		{
			MethodName: "SetHonorTitle",
			Handler:    _GuildHonorHalls_SetHonorTitle_Handler,
		},
		{
			MethodName: "GetTagItemList",
			Handler:    _GuildHonorHalls_GetTagItemList_Handler,
		},
		{
			MethodName: "GetHonorTitle",
			Handler:    _GuildHonorHalls_GetHonorTitle_Handler,
		},
		{
			MethodName: "DelTagItemList",
			Handler:    _GuildHonorHalls_DelTagItemList_Handler,
		},
		{
			MethodName: "DelHonorTitleList",
			Handler:    _GuildHonorHalls_DelHonorTitleList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "guild-honor-halls.proto",
}
