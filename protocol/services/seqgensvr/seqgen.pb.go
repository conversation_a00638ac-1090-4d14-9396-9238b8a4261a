// Code generated by protoc-gen-gogo.
// source: src/seqgensvr/seqgen.proto
// DO NOT EDIT!

/*
	Package SeqGen is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/seqgensvr/seqgen.proto

	It has these top-level messages:
		GenSeqReq
		GenSeqRsp
		GetLastestSeqReq
		GetLastestSeqResp
		SeqPair
		SeqPairList
		GetSeqMapReq
		GetSeqMapResp
		SetSeqMapReq
		SetSeqMapResp
		DelSeqMapReq
		DelSeqMapResp
		BatchSetUserGroupNeedUpdateReq
		BatchSetUserGroupNeedUpdateResp
		SetSeqReq
		SetSeqResp
		BatchGetLastestSeqReq
		BatchGetLastestSeqResp
*/
package SeqGen

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// ////////////////
type GenSeqReq struct {
	Id     uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Type   uint32 `protobuf:"varint,2,req,name=type" json:"type"`
	Suffix string `protobuf:"bytes,3,opt,name=suffix" json:"suffix"`
	Incr   uint32 `protobuf:"varint,4,opt,name=incr" json:"incr"`
}

func (m *GenSeqReq) Reset()                    { *m = GenSeqReq{} }
func (m *GenSeqReq) String() string            { return proto.CompactTextString(m) }
func (*GenSeqReq) ProtoMessage()               {}
func (*GenSeqReq) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{0} }

func (m *GenSeqReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GenSeqReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GenSeqReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *GenSeqReq) GetIncr() uint32 {
	if m != nil {
		return m.Incr
	}
	return 0
}

type GenSeqRsp struct {
	Ret int32  `protobuf:"varint,1,req,name=ret" json:"ret"`
	Seq uint32 `protobuf:"varint,2,req,name=seq" json:"seq"`
}

func (m *GenSeqRsp) Reset()                    { *m = GenSeqRsp{} }
func (m *GenSeqRsp) String() string            { return proto.CompactTextString(m) }
func (*GenSeqRsp) ProtoMessage()               {}
func (*GenSeqRsp) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{1} }

func (m *GenSeqRsp) GetRet() int32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

func (m *GenSeqRsp) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

// ////////////////
type GetLastestSeqReq struct {
	Id     uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Type   uint32 `protobuf:"varint,2,req,name=type" json:"type"`
	Suffix string `protobuf:"bytes,3,opt,name=suffix" json:"suffix"`
}

func (m *GetLastestSeqReq) Reset()                    { *m = GetLastestSeqReq{} }
func (m *GetLastestSeqReq) String() string            { return proto.CompactTextString(m) }
func (*GetLastestSeqReq) ProtoMessage()               {}
func (*GetLastestSeqReq) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{2} }

func (m *GetLastestSeqReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetLastestSeqReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetLastestSeqReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

type GetLastestSeqResp struct {
	Ret int32  `protobuf:"varint,1,req,name=ret" json:"ret"`
	Seq uint32 `protobuf:"varint,2,req,name=seq" json:"seq"`
}

func (m *GetLastestSeqResp) Reset()                    { *m = GetLastestSeqResp{} }
func (m *GetLastestSeqResp) String() string            { return proto.CompactTextString(m) }
func (*GetLastestSeqResp) ProtoMessage()               {}
func (*GetLastestSeqResp) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{3} }

func (m *GetLastestSeqResp) GetRet() int32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

func (m *GetLastestSeqResp) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

type SeqPair struct {
	Id  uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Seq uint32 `protobuf:"varint,2,req,name=seq" json:"seq"`
}

func (m *SeqPair) Reset()                    { *m = SeqPair{} }
func (m *SeqPair) String() string            { return proto.CompactTextString(m) }
func (*SeqPair) ProtoMessage()               {}
func (*SeqPair) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{4} }

func (m *SeqPair) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SeqPair) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

// for marshall
type SeqPairList struct {
	SeqPairList []*SeqPair `protobuf:"bytes,1,rep,name=seq_pair_list,json=seqPairList" json:"seq_pair_list,omitempty"`
}

func (m *SeqPairList) Reset()                    { *m = SeqPairList{} }
func (m *SeqPairList) String() string            { return proto.CompactTextString(m) }
func (*SeqPairList) ProtoMessage()               {}
func (*SeqPairList) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{5} }

func (m *SeqPairList) GetSeqPairList() []*SeqPair {
	if m != nil {
		return m.SeqPairList
	}
	return nil
}

type GetSeqMapReq struct {
	Type uint32 `protobuf:"varint,1,req,name=type" json:"type"`
}

func (m *GetSeqMapReq) Reset()                    { *m = GetSeqMapReq{} }
func (m *GetSeqMapReq) String() string            { return proto.CompactTextString(m) }
func (*GetSeqMapReq) ProtoMessage()               {}
func (*GetSeqMapReq) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{6} }

func (m *GetSeqMapReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetSeqMapResp struct {
	SeqPairList []*SeqPair `protobuf:"bytes,1,rep,name=seq_pair_list,json=seqPairList" json:"seq_pair_list,omitempty"`
}

func (m *GetSeqMapResp) Reset()                    { *m = GetSeqMapResp{} }
func (m *GetSeqMapResp) String() string            { return proto.CompactTextString(m) }
func (*GetSeqMapResp) ProtoMessage()               {}
func (*GetSeqMapResp) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{7} }

func (m *GetSeqMapResp) GetSeqPairList() []*SeqPair {
	if m != nil {
		return m.SeqPairList
	}
	return nil
}

type SetSeqMapReq struct {
	Type        uint32     `protobuf:"varint,1,req,name=type" json:"type"`
	SeqPairList []*SeqPair `protobuf:"bytes,2,rep,name=seq_pair_list,json=seqPairList" json:"seq_pair_list,omitempty"`
}

func (m *SetSeqMapReq) Reset()                    { *m = SetSeqMapReq{} }
func (m *SetSeqMapReq) String() string            { return proto.CompactTextString(m) }
func (*SetSeqMapReq) ProtoMessage()               {}
func (*SetSeqMapReq) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{8} }

func (m *SetSeqMapReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SetSeqMapReq) GetSeqPairList() []*SeqPair {
	if m != nil {
		return m.SeqPairList
	}
	return nil
}

type SetSeqMapResp struct {
}

func (m *SetSeqMapResp) Reset()                    { *m = SetSeqMapResp{} }
func (m *SetSeqMapResp) String() string            { return proto.CompactTextString(m) }
func (*SetSeqMapResp) ProtoMessage()               {}
func (*SetSeqMapResp) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{9} }

type DelSeqMapReq struct {
	Type   uint32   `protobuf:"varint,1,req,name=type" json:"type"`
	IdList []uint32 `protobuf:"varint,2,rep,name=id_list,json=idList" json:"id_list,omitempty"`
}

func (m *DelSeqMapReq) Reset()                    { *m = DelSeqMapReq{} }
func (m *DelSeqMapReq) String() string            { return proto.CompactTextString(m) }
func (*DelSeqMapReq) ProtoMessage()               {}
func (*DelSeqMapReq) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{10} }

func (m *DelSeqMapReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *DelSeqMapReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type DelSeqMapResp struct {
}

func (m *DelSeqMapResp) Reset()                    { *m = DelSeqMapResp{} }
func (m *DelSeqMapResp) String() string            { return proto.CompactTextString(m) }
func (*DelSeqMapResp) ProtoMessage()               {}
func (*DelSeqMapResp) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{11} }

type BatchSetUserGroupNeedUpdateReq struct {
	GroupId   uint32   `protobuf:"varint,1,req,name=group_id,json=groupId" json:"group_id"`
	Type      uint32   `protobuf:"varint,2,req,name=type" json:"type"`
	MemberUid []uint32 `protobuf:"varint,3,rep,name=member_uid,json=memberUid" json:"member_uid,omitempty"`
}

func (m *BatchSetUserGroupNeedUpdateReq) Reset()         { *m = BatchSetUserGroupNeedUpdateReq{} }
func (m *BatchSetUserGroupNeedUpdateReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetUserGroupNeedUpdateReq) ProtoMessage()    {}
func (*BatchSetUserGroupNeedUpdateReq) Descriptor() ([]byte, []int) {
	return fileDescriptorSeqgen, []int{12}
}

func (m *BatchSetUserGroupNeedUpdateReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *BatchSetUserGroupNeedUpdateReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *BatchSetUserGroupNeedUpdateReq) GetMemberUid() []uint32 {
	if m != nil {
		return m.MemberUid
	}
	return nil
}

type BatchSetUserGroupNeedUpdateResp struct {
}

func (m *BatchSetUserGroupNeedUpdateResp) Reset()         { *m = BatchSetUserGroupNeedUpdateResp{} }
func (m *BatchSetUserGroupNeedUpdateResp) String() string { return proto.CompactTextString(m) }
func (*BatchSetUserGroupNeedUpdateResp) ProtoMessage()    {}
func (*BatchSetUserGroupNeedUpdateResp) Descriptor() ([]byte, []int) {
	return fileDescriptorSeqgen, []int{13}
}

type SetSeqReq struct {
	Id     uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Type   uint32 `protobuf:"varint,2,req,name=type" json:"type"`
	SeqId  uint32 `protobuf:"varint,3,req,name=seq_id,json=seqId" json:"seq_id"`
	Suffix string `protobuf:"bytes,4,opt,name=suffix" json:"suffix"`
}

func (m *SetSeqReq) Reset()                    { *m = SetSeqReq{} }
func (m *SetSeqReq) String() string            { return proto.CompactTextString(m) }
func (*SetSeqReq) ProtoMessage()               {}
func (*SetSeqReq) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{14} }

func (m *SetSeqReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SetSeqReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SetSeqReq) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *SetSeqReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

type SetSeqResp struct {
}

func (m *SetSeqResp) Reset()                    { *m = SetSeqResp{} }
func (m *SetSeqResp) String() string            { return proto.CompactTextString(m) }
func (*SetSeqResp) ProtoMessage()               {}
func (*SetSeqResp) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{15} }

type BatchGetLastestSeqReq struct {
	Id     []uint32 `protobuf:"varint,1,rep,name=id" json:"id,omitempty"`
	Type   uint32   `protobuf:"varint,2,req,name=type" json:"type"`
	Suffix string   `protobuf:"bytes,3,opt,name=suffix" json:"suffix"`
}

func (m *BatchGetLastestSeqReq) Reset()                    { *m = BatchGetLastestSeqReq{} }
func (m *BatchGetLastestSeqReq) String() string            { return proto.CompactTextString(m) }
func (*BatchGetLastestSeqReq) ProtoMessage()               {}
func (*BatchGetLastestSeqReq) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{16} }

func (m *BatchGetLastestSeqReq) GetId() []uint32 {
	if m != nil {
		return m.Id
	}
	return nil
}

func (m *BatchGetLastestSeqReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *BatchGetLastestSeqReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

type BatchGetLastestSeqResp struct {
	SeqIdList []*SeqPair `protobuf:"bytes,1,rep,name=seq_id_list,json=seqIdList" json:"seq_id_list,omitempty"`
}

func (m *BatchGetLastestSeqResp) Reset()                    { *m = BatchGetLastestSeqResp{} }
func (m *BatchGetLastestSeqResp) String() string            { return proto.CompactTextString(m) }
func (*BatchGetLastestSeqResp) ProtoMessage()               {}
func (*BatchGetLastestSeqResp) Descriptor() ([]byte, []int) { return fileDescriptorSeqgen, []int{17} }

func (m *BatchGetLastestSeqResp) GetSeqIdList() []*SeqPair {
	if m != nil {
		return m.SeqIdList
	}
	return nil
}

func init() {
	proto.RegisterType((*GenSeqReq)(nil), "SeqGen.GenSeqReq")
	proto.RegisterType((*GenSeqRsp)(nil), "SeqGen.GenSeqRsp")
	proto.RegisterType((*GetLastestSeqReq)(nil), "SeqGen.GetLastestSeqReq")
	proto.RegisterType((*GetLastestSeqResp)(nil), "SeqGen.GetLastestSeqResp")
	proto.RegisterType((*SeqPair)(nil), "SeqGen.SeqPair")
	proto.RegisterType((*SeqPairList)(nil), "SeqGen.SeqPairList")
	proto.RegisterType((*GetSeqMapReq)(nil), "SeqGen.GetSeqMapReq")
	proto.RegisterType((*GetSeqMapResp)(nil), "SeqGen.GetSeqMapResp")
	proto.RegisterType((*SetSeqMapReq)(nil), "SeqGen.SetSeqMapReq")
	proto.RegisterType((*SetSeqMapResp)(nil), "SeqGen.SetSeqMapResp")
	proto.RegisterType((*DelSeqMapReq)(nil), "SeqGen.DelSeqMapReq")
	proto.RegisterType((*DelSeqMapResp)(nil), "SeqGen.DelSeqMapResp")
	proto.RegisterType((*BatchSetUserGroupNeedUpdateReq)(nil), "SeqGen.BatchSetUserGroupNeedUpdateReq")
	proto.RegisterType((*BatchSetUserGroupNeedUpdateResp)(nil), "SeqGen.BatchSetUserGroupNeedUpdateResp")
	proto.RegisterType((*SetSeqReq)(nil), "SeqGen.SetSeqReq")
	proto.RegisterType((*SetSeqResp)(nil), "SeqGen.SetSeqResp")
	proto.RegisterType((*BatchGetLastestSeqReq)(nil), "SeqGen.BatchGetLastestSeqReq")
	proto.RegisterType((*BatchGetLastestSeqResp)(nil), "SeqGen.BatchGetLastestSeqResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for SeqGen service

type SeqGenClient interface {
	GenSeqId(ctx context.Context, in *GenSeqReq, opts ...grpc.CallOption) (*GenSeqRsp, error)
	GetLastestSeq(ctx context.Context, in *GetLastestSeqReq, opts ...grpc.CallOption) (*GetLastestSeqResp, error)
	GetSeqMap(ctx context.Context, in *GetSeqMapReq, opts ...grpc.CallOption) (*GetSeqMapResp, error)
	SetSeqMap(ctx context.Context, in *SetSeqMapReq, opts ...grpc.CallOption) (*SetSeqMapResp, error)
	DelSeqMap(ctx context.Context, in *DelSeqMapReq, opts ...grpc.CallOption) (*DelSeqMapResp, error)
	BatchSetUserGroupNeedUpdate(ctx context.Context, in *BatchSetUserGroupNeedUpdateReq, opts ...grpc.CallOption) (*BatchSetUserGroupNeedUpdateResp, error)
	SetSeq(ctx context.Context, in *SetSeqReq, opts ...grpc.CallOption) (*SetSeqResp, error)
	BatchGetLastestSeq(ctx context.Context, in *BatchGetLastestSeqReq, opts ...grpc.CallOption) (*BatchGetLastestSeqResp, error)
}

type seqGenClient struct {
	cc *grpc.ClientConn
}

func NewSeqGenClient(cc *grpc.ClientConn) SeqGenClient {
	return &seqGenClient{cc}
}

func (c *seqGenClient) GenSeqId(ctx context.Context, in *GenSeqReq, opts ...grpc.CallOption) (*GenSeqRsp, error) {
	out := new(GenSeqRsp)
	err := grpc.Invoke(ctx, "/SeqGen.SeqGen/GenSeqId", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seqGenClient) GetLastestSeq(ctx context.Context, in *GetLastestSeqReq, opts ...grpc.CallOption) (*GetLastestSeqResp, error) {
	out := new(GetLastestSeqResp)
	err := grpc.Invoke(ctx, "/SeqGen.SeqGen/GetLastestSeq", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seqGenClient) GetSeqMap(ctx context.Context, in *GetSeqMapReq, opts ...grpc.CallOption) (*GetSeqMapResp, error) {
	out := new(GetSeqMapResp)
	err := grpc.Invoke(ctx, "/SeqGen.SeqGen/GetSeqMap", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seqGenClient) SetSeqMap(ctx context.Context, in *SetSeqMapReq, opts ...grpc.CallOption) (*SetSeqMapResp, error) {
	out := new(SetSeqMapResp)
	err := grpc.Invoke(ctx, "/SeqGen.SeqGen/SetSeqMap", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seqGenClient) DelSeqMap(ctx context.Context, in *DelSeqMapReq, opts ...grpc.CallOption) (*DelSeqMapResp, error) {
	out := new(DelSeqMapResp)
	err := grpc.Invoke(ctx, "/SeqGen.SeqGen/DelSeqMap", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seqGenClient) BatchSetUserGroupNeedUpdate(ctx context.Context, in *BatchSetUserGroupNeedUpdateReq, opts ...grpc.CallOption) (*BatchSetUserGroupNeedUpdateResp, error) {
	out := new(BatchSetUserGroupNeedUpdateResp)
	err := grpc.Invoke(ctx, "/SeqGen.SeqGen/BatchSetUserGroupNeedUpdate", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seqGenClient) SetSeq(ctx context.Context, in *SetSeqReq, opts ...grpc.CallOption) (*SetSeqResp, error) {
	out := new(SetSeqResp)
	err := grpc.Invoke(ctx, "/SeqGen.SeqGen/SetSeq", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seqGenClient) BatchGetLastestSeq(ctx context.Context, in *BatchGetLastestSeqReq, opts ...grpc.CallOption) (*BatchGetLastestSeqResp, error) {
	out := new(BatchGetLastestSeqResp)
	err := grpc.Invoke(ctx, "/SeqGen.SeqGen/BatchGetLastestSeq", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for SeqGen service

type SeqGenServer interface {
	GenSeqId(context.Context, *GenSeqReq) (*GenSeqRsp, error)
	GetLastestSeq(context.Context, *GetLastestSeqReq) (*GetLastestSeqResp, error)
	GetSeqMap(context.Context, *GetSeqMapReq) (*GetSeqMapResp, error)
	SetSeqMap(context.Context, *SetSeqMapReq) (*SetSeqMapResp, error)
	DelSeqMap(context.Context, *DelSeqMapReq) (*DelSeqMapResp, error)
	BatchSetUserGroupNeedUpdate(context.Context, *BatchSetUserGroupNeedUpdateReq) (*BatchSetUserGroupNeedUpdateResp, error)
	SetSeq(context.Context, *SetSeqReq) (*SetSeqResp, error)
	BatchGetLastestSeq(context.Context, *BatchGetLastestSeqReq) (*BatchGetLastestSeqResp, error)
}

func RegisterSeqGenServer(s *grpc.Server, srv SeqGenServer) {
	s.RegisterService(&_SeqGen_serviceDesc, srv)
}

func _SeqGen_GenSeqId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).GenSeqId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/SeqGen.SeqGen/GenSeqId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).GenSeqId(ctx, req.(*GenSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeqGen_GetLastestSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastestSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).GetLastestSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/SeqGen.SeqGen/GetLastestSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).GetLastestSeq(ctx, req.(*GetLastestSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeqGen_GetSeqMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeqMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).GetSeqMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/SeqGen.SeqGen/GetSeqMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).GetSeqMap(ctx, req.(*GetSeqMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeqGen_SetSeqMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSeqMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).SetSeqMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/SeqGen.SeqGen/SetSeqMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).SetSeqMap(ctx, req.(*SetSeqMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeqGen_DelSeqMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelSeqMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).DelSeqMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/SeqGen.SeqGen/DelSeqMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).DelSeqMap(ctx, req.(*DelSeqMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeqGen_BatchSetUserGroupNeedUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetUserGroupNeedUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).BatchSetUserGroupNeedUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/SeqGen.SeqGen/BatchSetUserGroupNeedUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).BatchSetUserGroupNeedUpdate(ctx, req.(*BatchSetUserGroupNeedUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeqGen_SetSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).SetSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/SeqGen.SeqGen/SetSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).SetSeq(ctx, req.(*SetSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeqGen_BatchGetLastestSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetLastestSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).BatchGetLastestSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/SeqGen.SeqGen/BatchGetLastestSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).BatchGetLastestSeq(ctx, req.(*BatchGetLastestSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SeqGen_serviceDesc = grpc.ServiceDesc{
	ServiceName: "SeqGen.SeqGen",
	HandlerType: (*SeqGenServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenSeqId",
			Handler:    _SeqGen_GenSeqId_Handler,
		},
		{
			MethodName: "GetLastestSeq",
			Handler:    _SeqGen_GetLastestSeq_Handler,
		},
		{
			MethodName: "GetSeqMap",
			Handler:    _SeqGen_GetSeqMap_Handler,
		},
		{
			MethodName: "SetSeqMap",
			Handler:    _SeqGen_SetSeqMap_Handler,
		},
		{
			MethodName: "DelSeqMap",
			Handler:    _SeqGen_DelSeqMap_Handler,
		},
		{
			MethodName: "BatchSetUserGroupNeedUpdate",
			Handler:    _SeqGen_BatchSetUserGroupNeedUpdate_Handler,
		},
		{
			MethodName: "SetSeq",
			Handler:    _SeqGen_SetSeq_Handler,
		},
		{
			MethodName: "BatchGetLastestSeq",
			Handler:    _SeqGen_BatchGetLastestSeq_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/seqgensvr/seqgen.proto",
}

func (m *GenSeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GenSeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	dAtA[i] = 0x20
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Incr))
	return i, nil
}

func (m *GenSeqRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GenSeqRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Ret))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Seq))
	return i, nil
}

func (m *GetLastestSeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLastestSeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	return i, nil
}

func (m *GetLastestSeqResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLastestSeqResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Ret))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Seq))
	return i, nil
}

func (m *SeqPair) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SeqPair) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Seq))
	return i, nil
}

func (m *SeqPairList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SeqPairList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SeqPairList) > 0 {
		for _, msg := range m.SeqPairList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintSeqgen(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetSeqMapReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSeqMapReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *GetSeqMapResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSeqMapResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SeqPairList) > 0 {
		for _, msg := range m.SeqPairList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintSeqgen(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetSeqMapReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetSeqMapReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Type))
	if len(m.SeqPairList) > 0 {
		for _, msg := range m.SeqPairList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintSeqgen(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetSeqMapResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetSeqMapResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DelSeqMapReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelSeqMapReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Type))
	if len(m.IdList) > 0 {
		for _, num := range m.IdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintSeqgen(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *DelSeqMapResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelSeqMapResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *BatchSetUserGroupNeedUpdateReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSetUserGroupNeedUpdateReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Type))
	if len(m.MemberUid) > 0 {
		for _, num := range m.MemberUid {
			dAtA[i] = 0x18
			i++
			i = encodeVarintSeqgen(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchSetUserGroupNeedUpdateResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSetUserGroupNeedUpdateResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SetSeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetSeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x18
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.SeqId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	return i, nil
}

func (m *SetSeqResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetSeqResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *BatchGetLastestSeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetLastestSeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Id) > 0 {
		for _, num := range m.Id {
			dAtA[i] = 0x8
			i++
			i = encodeVarintSeqgen(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintSeqgen(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	return i, nil
}

func (m *BatchGetLastestSeqResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetLastestSeqResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SeqIdList) > 0 {
		for _, msg := range m.SeqIdList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintSeqgen(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Seqgen(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Seqgen(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintSeqgen(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GenSeqReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSeqgen(uint64(m.Id))
	n += 1 + sovSeqgen(uint64(m.Type))
	l = len(m.Suffix)
	n += 1 + l + sovSeqgen(uint64(l))
	n += 1 + sovSeqgen(uint64(m.Incr))
	return n
}

func (m *GenSeqRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSeqgen(uint64(m.Ret))
	n += 1 + sovSeqgen(uint64(m.Seq))
	return n
}

func (m *GetLastestSeqReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSeqgen(uint64(m.Id))
	n += 1 + sovSeqgen(uint64(m.Type))
	l = len(m.Suffix)
	n += 1 + l + sovSeqgen(uint64(l))
	return n
}

func (m *GetLastestSeqResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSeqgen(uint64(m.Ret))
	n += 1 + sovSeqgen(uint64(m.Seq))
	return n
}

func (m *SeqPair) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSeqgen(uint64(m.Id))
	n += 1 + sovSeqgen(uint64(m.Seq))
	return n
}

func (m *SeqPairList) Size() (n int) {
	var l int
	_ = l
	if len(m.SeqPairList) > 0 {
		for _, e := range m.SeqPairList {
			l = e.Size()
			n += 1 + l + sovSeqgen(uint64(l))
		}
	}
	return n
}

func (m *GetSeqMapReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSeqgen(uint64(m.Type))
	return n
}

func (m *GetSeqMapResp) Size() (n int) {
	var l int
	_ = l
	if len(m.SeqPairList) > 0 {
		for _, e := range m.SeqPairList {
			l = e.Size()
			n += 1 + l + sovSeqgen(uint64(l))
		}
	}
	return n
}

func (m *SetSeqMapReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSeqgen(uint64(m.Type))
	if len(m.SeqPairList) > 0 {
		for _, e := range m.SeqPairList {
			l = e.Size()
			n += 1 + l + sovSeqgen(uint64(l))
		}
	}
	return n
}

func (m *SetSeqMapResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DelSeqMapReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSeqgen(uint64(m.Type))
	if len(m.IdList) > 0 {
		for _, e := range m.IdList {
			n += 1 + sovSeqgen(uint64(e))
		}
	}
	return n
}

func (m *DelSeqMapResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *BatchSetUserGroupNeedUpdateReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSeqgen(uint64(m.GroupId))
	n += 1 + sovSeqgen(uint64(m.Type))
	if len(m.MemberUid) > 0 {
		for _, e := range m.MemberUid {
			n += 1 + sovSeqgen(uint64(e))
		}
	}
	return n
}

func (m *BatchSetUserGroupNeedUpdateResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SetSeqReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSeqgen(uint64(m.Id))
	n += 1 + sovSeqgen(uint64(m.Type))
	n += 1 + sovSeqgen(uint64(m.SeqId))
	l = len(m.Suffix)
	n += 1 + l + sovSeqgen(uint64(l))
	return n
}

func (m *SetSeqResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *BatchGetLastestSeqReq) Size() (n int) {
	var l int
	_ = l
	if len(m.Id) > 0 {
		for _, e := range m.Id {
			n += 1 + sovSeqgen(uint64(e))
		}
	}
	n += 1 + sovSeqgen(uint64(m.Type))
	l = len(m.Suffix)
	n += 1 + l + sovSeqgen(uint64(l))
	return n
}

func (m *BatchGetLastestSeqResp) Size() (n int) {
	var l int
	_ = l
	if len(m.SeqIdList) > 0 {
		for _, e := range m.SeqIdList {
			l = e.Size()
			n += 1 + l + sovSeqgen(uint64(l))
		}
	}
	return n
}

func sovSeqgen(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozSeqgen(x uint64) (n int) {
	return sovSeqgen(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GenSeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GenSeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GenSeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSeqgen
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Incr", wireType)
			}
			m.Incr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Incr |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GenSeqRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GenSeqRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GenSeqRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			m.Ret = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ret |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seq", wireType)
			}
			m.Seq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seq |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("ret")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("seq")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLastestSeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLastestSeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLastestSeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSeqgen
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLastestSeqResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLastestSeqResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLastestSeqResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			m.Ret = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ret |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seq", wireType)
			}
			m.Seq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seq |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("ret")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("seq")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SeqPair) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SeqPair: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SeqPair: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seq", wireType)
			}
			m.Seq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seq |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("seq")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SeqPairList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SeqPairList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SeqPairList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqPairList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSeqgen
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SeqPairList = append(m.SeqPairList, &SeqPair{})
			if err := m.SeqPairList[len(m.SeqPairList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSeqMapReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSeqMapReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSeqMapReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSeqMapResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSeqMapResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSeqMapResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqPairList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSeqgen
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SeqPairList = append(m.SeqPairList, &SeqPair{})
			if err := m.SeqPairList[len(m.SeqPairList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetSeqMapReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetSeqMapReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetSeqMapReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqPairList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSeqgen
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SeqPairList = append(m.SeqPairList, &SeqPair{})
			if err := m.SeqPairList[len(m.SeqPairList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetSeqMapResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetSeqMapResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetSeqMapResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelSeqMapReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelSeqMapReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelSeqMapReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSeqgen
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.IdList = append(m.IdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSeqgen
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthSeqgen
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowSeqgen
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.IdList = append(m.IdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelSeqMapResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelSeqMapResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelSeqMapResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSetUserGroupNeedUpdateReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchSetUserGroupNeedUpdateReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchSetUserGroupNeedUpdateReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSeqgen
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.MemberUid = append(m.MemberUid, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSeqgen
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthSeqgen
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowSeqgen
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.MemberUid = append(m.MemberUid, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberUid", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSetUserGroupNeedUpdateResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchSetUserGroupNeedUpdateResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchSetUserGroupNeedUpdateResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetSeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetSeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetSeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqId", wireType)
			}
			m.SeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSeqgen
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("seq_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetSeqResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetSeqResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetSeqResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetLastestSeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetLastestSeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetLastestSeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSeqgen
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Id = append(m.Id, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSeqgen
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthSeqgen
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowSeqgen
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Id = append(m.Id, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSeqgen
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetLastestSeqResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetLastestSeqResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetLastestSeqResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqIdList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSeqgen
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SeqIdList = append(m.SeqIdList, &SeqPair{})
			if err := m.SeqIdList[len(m.SeqIdList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSeqgen(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSeqgen
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipSeqgen(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowSeqgen
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSeqgen
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthSeqgen
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowSeqgen
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipSeqgen(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthSeqgen = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowSeqgen   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/seqgensvr/seqgen.proto", fileDescriptorSeqgen) }

var fileDescriptorSeqgen = []byte{
	// 760 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x94, 0xcf, 0x6e, 0xd3, 0x4a,
	0x14, 0xc6, 0xe3, 0x38, 0x75, 0x9a, 0x93, 0xe4, 0xb6, 0x1d, 0xb5, 0xbd, 0xb9, 0xee, 0x6d, 0xea,
	0x3b, 0xba, 0x40, 0x58, 0xb4, 0x91, 0xda, 0x05, 0x52, 0xa9, 0x2a, 0x08, 0x95, 0xa2, 0x48, 0x05,
	0xa1, 0x98, 0x2e, 0x51, 0x48, 0xe3, 0x69, 0x19, 0x35, 0x7f, 0xc6, 0x1e, 0xbb, 0xa2, 0x2c, 0x10,
	0x4b, 0xd4, 0x15, 0xe2, 0x19, 0xf2, 0x00, 0x3c, 0x46, 0x57, 0x88, 0x27, 0x40, 0xa8, 0x6c, 0xf2,
	0x18, 0x68, 0xfc, 0x27, 0xb6, 0x93, 0x34, 0x0a, 0x45, 0xec, 0xac, 0xe3, 0x99, 0xef, 0xf7, 0xcd,
	0x99, 0xf3, 0x0d, 0xa8, 0xdc, 0x6a, 0x95, 0x39, 0x31, 0x4f, 0x49, 0x97, 0x9f, 0x5b, 0xfe, 0xd7,
	0x16, 0xb3, 0x7a, 0x76, 0x0f, 0x29, 0x3a, 0x31, 0xab, 0xa4, 0xab, 0xfe, 0xdf, 0xea, 0x75, 0x3a,
	0xbd, 0x6e, 0xd9, 0x6e, 0x9f, 0x33, 0xda, 0x3a, 0x6b, 0x93, 0x32, 0x3f, 0x3b, 0x76, 0x68, 0xdb,
	0xa6, 0x5d, 0xfb, 0x82, 0x11, 0x6f, 0x35, 0xe6, 0x90, 0xa9, 0x92, 0xae, 0x4e, 0xcc, 0x3a, 0x31,
	0xd1, 0x32, 0x24, 0xa9, 0x51, 0x90, 0xb4, 0x64, 0x29, 0x5f, 0x49, 0x5d, 0x7d, 0xdb, 0x48, 0xd4,
	0x93, 0xd4, 0x40, 0x05, 0x48, 0x89, 0x0d, 0x85, 0x64, 0xa4, 0xee, 0x56, 0xd0, 0xbf, 0xa0, 0x70,
	0xe7, 0xe4, 0x84, 0xbe, 0x29, 0xc8, 0x9a, 0x54, 0xca, 0xf8, 0xff, 0xfc, 0x9a, 0xd8, 0x47, 0xbb,
	0x2d, 0xab, 0x90, 0xd2, 0xa4, 0x70, 0x9f, 0xa8, 0xe0, 0x87, 0x43, 0x28, 0x67, 0x68, 0x15, 0x64,
	0x8b, 0xd8, 0x2e, 0x75, 0xce, 0x5f, 0x25, 0x0a, 0xa2, 0xce, 0x89, 0x19, 0xa3, 0x8a, 0x02, 0x7e,
	0x05, 0x8b, 0x55, 0x62, 0x1f, 0x36, 0xb9, 0x4d, 0xb8, 0xfd, 0x27, 0x8c, 0xe3, 0x27, 0xb0, 0x34,
	0x42, 0xb8, 0x85, 0xcd, 0x07, 0x90, 0xd6, 0x89, 0xf9, 0xbc, 0x49, 0xad, 0x1b, 0xdc, 0xdd, 0xb4,
	0xb1, 0x02, 0x59, 0x7f, 0xe3, 0x21, 0xe5, 0x36, 0xda, 0x81, 0x3c, 0x27, 0x66, 0x83, 0x35, 0xa9,
	0xd5, 0x68, 0x53, 0x2e, 0x1c, 0xc8, 0xa5, 0xec, 0xf6, 0xc2, 0x96, 0x77, 0xcd, 0x5b, 0xfe, 0xda,
	0x7a, 0x96, 0x87, 0x9b, 0x70, 0x09, 0x72, 0x55, 0x22, 0xac, 0x3f, 0x6d, 0x32, 0xd1, 0x9f, 0xa0,
	0x13, 0xd2, 0x68, 0x27, 0xf0, 0x01, 0xe4, 0x23, 0x2b, 0x39, 0xbb, 0x1d, 0xef, 0x25, 0xe4, 0xf4,
	0x99, 0x78, 0xe3, 0xf2, 0xc9, 0x19, 0xe4, 0x17, 0x20, 0xaf, 0x47, 0x4d, 0xe2, 0xc7, 0x90, 0x3b,
	0x20, 0xed, 0x59, 0x78, 0x7f, 0x43, 0x9a, 0x1a, 0x21, 0x29, 0x5f, 0x57, 0xa8, 0x11, 0x68, 0x46,
	0x24, 0x38, 0xc3, 0x6f, 0xa1, 0x58, 0x69, 0xda, 0xad, 0xd7, 0x3a, 0xb1, 0x8f, 0x38, 0xb1, 0xaa,
	0x56, 0xcf, 0x61, 0xcf, 0x08, 0x31, 0x8e, 0x98, 0xd1, 0xb4, 0x89, 0xa0, 0x6c, 0xc0, 0xfc, 0xa9,
	0xa8, 0x36, 0x46, 0x6e, 0x33, 0xed, 0x56, 0x6b, 0xd3, 0x06, 0x6e, 0x1d, 0xa0, 0x43, 0x3a, 0xc7,
	0xc4, 0x6a, 0x38, 0xd4, 0x28, 0xc8, 0xae, 0x93, 0x8c, 0x57, 0x39, 0xa2, 0x06, 0xfe, 0x0f, 0x36,
	0xa6, 0xb2, 0x39, 0xc3, 0xe7, 0x90, 0xf1, 0x7a, 0x70, 0x9b, 0x79, 0x5f, 0x03, 0x45, 0x74, 0xdd,
	0x45, 0x87, 0xff, 0xe6, 0x38, 0x31, 0x6b, 0x46, 0x24, 0x0c, 0xa9, 0x09, 0x61, 0xc8, 0x01, 0x04,
	0x5c, 0xce, 0x70, 0x03, 0x56, 0x5c, 0xa3, 0x63, 0x09, 0xfc, 0xcb, 0x77, 0x24, 0x0e, 0xf6, 0x3b,
	0xd9, 0xab, 0xc1, 0xea, 0x24, 0x00, 0x67, 0xa8, 0x0c, 0x59, 0xef, 0x0c, 0x53, 0xc7, 0x32, 0xe3,
	0x9e, 0x49, 0xdc, 0xf0, 0xf6, 0x17, 0x05, 0xfc, 0xb7, 0x10, 0x11, 0x98, 0xf7, 0x1e, 0x9c, 0x9a,
	0x81, 0x96, 0x82, 0x2d, 0xc3, 0x77, 0x4f, 0x1d, 0x2d, 0x71, 0x86, 0x77, 0xde, 0xf7, 0x07, 0xb2,
	0x74, 0xd9, 0x1f, 0xc8, 0x8a, 0xb3, 0xcb, 0x77, 0xed, 0xdd, 0x4f, 0xfd, 0x81, 0x5c, 0xdc, 0x74,
	0xb4, 0x3d, 0x87, 0x1a, 0xfb, 0xda, 0x26, 0xd7, 0xf6, 0x3c, 0xc7, 0xfb, 0xda, 0xa6, 0xad, 0xed,
	0x89, 0x93, 0xed, 0xa3, 0x0b, 0x37, 0x4c, 0xa1, 0x6f, 0x54, 0x08, 0x85, 0xe3, 0xfd, 0x52, 0xff,
	0xb9, 0xe1, 0x4f, 0x80, 0x4e, 0xfe, 0x22, 0xba, 0x21, 0x9e, 0x54, 0x3f, 0x22, 0x68, 0x39, 0x22,
	0x3e, 0x0c, 0x89, 0xba, 0x32, 0xa1, 0xca, 0x19, 0xbe, 0x23, 0x70, 0xb2, 0xc0, 0xa5, 0x1c, 0x1f,
	0x86, 0x42, 0xd8, 0x10, 0xf0, 0x22, 0x98, 0xbf, 0x18, 0x40, 0x9f, 0x08, 0x88, 0x87, 0x55, 0x15,
	0x80, 0x94, 0x00, 0x24, 0x3d, 0xf9, 0x4c, 0x4c, 0x75, 0x98, 0xc2, 0x50, 0x35, 0x9a, 0xed, 0x50,
	0x35, 0x1e, 0x57, 0x57, 0x75, 0x6e, 0xb2, 0xea, 0xa5, 0x04, 0x6b, 0x53, 0xf2, 0x84, 0xee, 0x06,
	0x92, 0xd3, 0x03, 0xaf, 0xde, 0x9b, 0x69, 0x5d, 0x60, 0x46, 0x99, 0x6c, 0xe6, 0x91, 0x98, 0x42,
	0x77, 0x1a, 0x96, 0xe2, 0xfd, 0x11, 0x04, 0x34, 0x5a, 0xe2, 0x0c, 0x2f, 0x08, 0xb1, 0xb4, 0x10,
	0x4b, 0x08, 0xa9, 0x04, 0x7a, 0x07, 0x68, 0x3c, 0x13, 0x68, 0x3d, 0x66, 0x6e, 0x6c, 0xc0, 0x8a,
	0xd3, 0x7e, 0x73, 0x86, 0xef, 0x0b, 0xca, 0x7c, 0xec, 0xda, 0x57, 0xc5, 0xb5, 0xfb, 0xf9, 0x8a,
	0x5c, 0xbd, 0xaa, 0x7c, 0xe8, 0x0f, 0xe4, 0xcf, 0x9d, 0xca, 0xe2, 0xd5, 0x75, 0x51, 0xfa, 0x7a,
	0x5d, 0x94, 0xbe, 0x5f, 0x17, 0xa5, 0x8f, 0x3f, 0x8a, 0x89, 0x9f, 0x01, 0x00, 0x00, 0xff, 0xff,
	0xc6, 0x14, 0x52, 0x4b, 0x88, 0x08, 0x00, 0x00,
}
