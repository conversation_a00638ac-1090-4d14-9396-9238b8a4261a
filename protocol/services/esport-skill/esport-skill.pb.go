// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/esport-skill/esport-skill.proto

package esport_skill // import "golang.52tt.com/protocol/services/esport-skill"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 游戏类型
// buf:lint:ignore ENUM_PASCAL_CASE
type GAME_TYPE int32

const (
	GAME_TYPE_GAME_TYPE_INVALID GAME_TYPE = 0
	GAME_TYPE_GAME_TYPE_MOBILE  GAME_TYPE = 1
	GAME_TYPE_GAME_TYPE_PC      GAME_TYPE = 2
)

var GAME_TYPE_name = map[int32]string{
	0: "GAME_TYPE_INVALID",
	1: "GAME_TYPE_MOBILE",
	2: "GAME_TYPE_PC",
}
var GAME_TYPE_value = map[string]int32{
	"GAME_TYPE_INVALID": 0,
	"GAME_TYPE_MOBILE":  1,
	"GAME_TYPE_PC":      2,
}

func (x GAME_TYPE) String() string {
	return proto.EnumName(GAME_TYPE_name, int32(x))
}
func (GAME_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{0}
}

// 定价类型
// buf:lint:ignore ENUM_PASCAL_CASE
type GAME_PRICING_TYPE int32

const (
	GAME_PRICING_TYPE_GAME_PRICING_TYPE_INVALID GAME_PRICING_TYPE = 0
	GAME_PRICING_TYPE_GAME_PRICING_TYPE_UNIFY   GAME_PRICING_TYPE = 1
	GAME_PRICING_TYPE_GAME_PRICING_TYPE_RANK    GAME_PRICING_TYPE = 2
)

var GAME_PRICING_TYPE_name = map[int32]string{
	0: "GAME_PRICING_TYPE_INVALID",
	1: "GAME_PRICING_TYPE_UNIFY",
	2: "GAME_PRICING_TYPE_RANK",
}
var GAME_PRICING_TYPE_value = map[string]int32{
	"GAME_PRICING_TYPE_INVALID": 0,
	"GAME_PRICING_TYPE_UNIFY":   1,
	"GAME_PRICING_TYPE_RANK":    2,
}

func (x GAME_PRICING_TYPE) String() string {
	return proto.EnumName(GAME_PRICING_TYPE_name, int32(x))
}
func (GAME_PRICING_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{1}
}

// 价格单位
// buf:lint:ignore ENUM_PASCAL_CASE
type GAME_PRICING_UNIT_TYPE int32

const (
	GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_INVALID    GAME_PRICING_UNIT_TYPE = 0
	GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_PER_30_MIN GAME_PRICING_UNIT_TYPE = 1
	GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_PER_GAME   GAME_PRICING_UNIT_TYPE = 2
)

var GAME_PRICING_UNIT_TYPE_name = map[int32]string{
	0: "GAME_PRICING_UNIT_TYPE_INVALID",
	1: "GAME_PRICING_UNIT_TYPE_PER_30_MIN",
	2: "GAME_PRICING_UNIT_TYPE_PER_GAME",
}
var GAME_PRICING_UNIT_TYPE_value = map[string]int32{
	"GAME_PRICING_UNIT_TYPE_INVALID":    0,
	"GAME_PRICING_UNIT_TYPE_PER_30_MIN": 1,
	"GAME_PRICING_UNIT_TYPE_PER_GAME":   2,
}

func (x GAME_PRICING_UNIT_TYPE) String() string {
	return proto.EnumName(GAME_PRICING_UNIT_TYPE_name, int32(x))
}
func (GAME_PRICING_UNIT_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{2}
}

// 技能审核来源
type AuditSource int32

const (
	AuditSource_AUDIT_SOURCE_UNSPECIFIED AuditSource = 0
	AuditSource_AUDIT_SOURCE_NEW_ROLE    AuditSource = 1
	AuditSource_AUDIT_SOURCE_NEW_SKILL   AuditSource = 2
	AuditSource_AUDIT_SOURCE_MOD_SKILL   AuditSource = 3
)

var AuditSource_name = map[int32]string{
	0: "AUDIT_SOURCE_UNSPECIFIED",
	1: "AUDIT_SOURCE_NEW_ROLE",
	2: "AUDIT_SOURCE_NEW_SKILL",
	3: "AUDIT_SOURCE_MOD_SKILL",
}
var AuditSource_value = map[string]int32{
	"AUDIT_SOURCE_UNSPECIFIED": 0,
	"AUDIT_SOURCE_NEW_ROLE":    1,
	"AUDIT_SOURCE_NEW_SKILL":   2,
	"AUDIT_SOURCE_MOD_SKILL":   3,
}

func (x AuditSource) String() string {
	return proto.EnumName(AuditSource_name, int32(x))
}
func (AuditSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{3}
}

// 搜索类型
type AuditSearchType int32

const (
	AuditSearchType_AUDIT_SEARCH_TYPE_UNSPECIFIED AuditSearchType = 0
	AuditSearchType_AUDIT_SEARCH_TYPE_BY_USER     AuditSearchType = 1
	AuditSearchType_AUDIT_SEARCH_TYPE_BY_GUILD    AuditSearchType = 2
	AuditSearchType_AUDIT_SEARCH_TYPE_ALL         AuditSearchType = 3
)

var AuditSearchType_name = map[int32]string{
	0: "AUDIT_SEARCH_TYPE_UNSPECIFIED",
	1: "AUDIT_SEARCH_TYPE_BY_USER",
	2: "AUDIT_SEARCH_TYPE_BY_GUILD",
	3: "AUDIT_SEARCH_TYPE_ALL",
}
var AuditSearchType_value = map[string]int32{
	"AUDIT_SEARCH_TYPE_UNSPECIFIED": 0,
	"AUDIT_SEARCH_TYPE_BY_USER":     1,
	"AUDIT_SEARCH_TYPE_BY_GUILD":    2,
	"AUDIT_SEARCH_TYPE_ALL":         3,
}

func (x AuditSearchType) String() string {
	return proto.EnumName(AuditSearchType_name, int32(x))
}
func (AuditSearchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{4}
}

// 开关状态
type EsportSwitchStatus int32

const (
	EsportSwitchStatus_SWITCH_STATUS_UNSPECIFIED EsportSwitchStatus = 0
	EsportSwitchStatus_SWITCH_STATUS_ON          EsportSwitchStatus = 1
	EsportSwitchStatus_SWITCH_STATUS_OFF         EsportSwitchStatus = 2
)

var EsportSwitchStatus_name = map[int32]string{
	0: "SWITCH_STATUS_UNSPECIFIED",
	1: "SWITCH_STATUS_ON",
	2: "SWITCH_STATUS_OFF",
}
var EsportSwitchStatus_value = map[string]int32{
	"SWITCH_STATUS_UNSPECIFIED": 0,
	"SWITCH_STATUS_ON":          1,
	"SWITCH_STATUS_OFF":         2,
}

func (x EsportSwitchStatus) String() string {
	return proto.EnumName(EsportSwitchStatus_name, int32(x))
}
func (EsportSwitchStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{5}
}

// LabelType 标识类型
type LabelType int32

const (
	LabelType_LABEL_TYPE_UNSPECIFIED LabelType = 0
	LabelType_LABEL_TYPE_COACH       LabelType = 1
	LabelType_LABEL_TYPE_SKILL       LabelType = 2
	LabelType_LABEL_TYPE_SPECIAL     LabelType = 3
	LabelType_LABEL_TYPE_VOICE       LabelType = 4
)

var LabelType_name = map[int32]string{
	0: "LABEL_TYPE_UNSPECIFIED",
	1: "LABEL_TYPE_COACH",
	2: "LABEL_TYPE_SKILL",
	3: "LABEL_TYPE_SPECIAL",
	4: "LABEL_TYPE_VOICE",
}
var LabelType_value = map[string]int32{
	"LABEL_TYPE_UNSPECIFIED": 0,
	"LABEL_TYPE_COACH":       1,
	"LABEL_TYPE_SKILL":       2,
	"LABEL_TYPE_SPECIAL":     3,
	"LABEL_TYPE_VOICE":       4,
}

func (x LabelType) String() string {
	return proto.EnumName(LabelType_name, int32(x))
}
func (LabelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{6}
}

type IssuanceStatus int32

const (
	IssuanceStatus_ISSUANCE_STATUS_UNSPECIFIED IssuanceStatus = 0
	IssuanceStatus_ISSUANCE_STATUS_EFFECTIVE   IssuanceStatus = 1
	IssuanceStatus_ISSUANCE_STATUS_EXPIRED     IssuanceStatus = 2
	IssuanceStatus_ISSUANCE_STATUS_PENDING     IssuanceStatus = 3
)

var IssuanceStatus_name = map[int32]string{
	0: "ISSUANCE_STATUS_UNSPECIFIED",
	1: "ISSUANCE_STATUS_EFFECTIVE",
	2: "ISSUANCE_STATUS_EXPIRED",
	3: "ISSUANCE_STATUS_PENDING",
}
var IssuanceStatus_value = map[string]int32{
	"ISSUANCE_STATUS_UNSPECIFIED": 0,
	"ISSUANCE_STATUS_EFFECTIVE":   1,
	"ISSUANCE_STATUS_EXPIRED":     2,
	"ISSUANCE_STATUS_PENDING":     3,
}

func (x IssuanceStatus) String() string {
	return proto.EnumName(IssuanceStatus_name, int32(x))
}
func (IssuanceStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{7}
}

type FreezeType int32

const (
	FreezeType_FREEZE_TYPE_UNFREEZE FreezeType = 0
	FreezeType_FREEZE_TYPE_FOREVER  FreezeType = 1
	FreezeType_FREEZE_TYPE_TO_TIME  FreezeType = 2
)

var FreezeType_name = map[int32]string{
	0: "FREEZE_TYPE_UNFREEZE",
	1: "FREEZE_TYPE_FOREVER",
	2: "FREEZE_TYPE_TO_TIME",
}
var FreezeType_value = map[string]int32{
	"FREEZE_TYPE_UNFREEZE": 0,
	"FREEZE_TYPE_FOREVER":  1,
	"FREEZE_TYPE_TO_TIME":  2,
}

func (x FreezeType) String() string {
	return proto.EnumName(FreezeType_name, int32(x))
}
func (FreezeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{8}
}

// 特色标签类型
type SpecialLabelType int32

const (
	SpecialLabelType_SPECIAL_LABEL_TYPE_UNSPECIFIED SpecialLabelType = 0
	SpecialLabelType_SPECIAL_LABEL_TYPE_VOICE       SpecialLabelType = 1
	SpecialLabelType_SPECIAL_LABEL_TYPE_SPECIAL     SpecialLabelType = 2
)

var SpecialLabelType_name = map[int32]string{
	0: "SPECIAL_LABEL_TYPE_UNSPECIFIED",
	1: "SPECIAL_LABEL_TYPE_VOICE",
	2: "SPECIAL_LABEL_TYPE_SPECIAL",
}
var SpecialLabelType_value = map[string]int32{
	"SPECIAL_LABEL_TYPE_UNSPECIFIED": 0,
	"SPECIAL_LABEL_TYPE_VOICE":       1,
	"SPECIAL_LABEL_TYPE_SPECIAL":     2,
}

func (x SpecialLabelType) String() string {
	return proto.EnumName(SpecialLabelType_name, int32(x))
}
func (SpecialLabelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{9}
}

// 游戏资料类型
// buf:lint:ignore ENUM_PASCAL_CASE
type GameInformation_GAME_INFORMATION_TYPE int32

const (
	GameInformation_GAME_INFORMATION_TYPE_INVALID GameInformation_GAME_INFORMATION_TYPE = 0
	GameInformation_GAME_INFORMATION_TYPE_RANK    GameInformation_GAME_INFORMATION_TYPE = 1
	GameInformation_GAME_INFORMATION_TYPE_DIY     GameInformation_GAME_INFORMATION_TYPE = 2
)

var GameInformation_GAME_INFORMATION_TYPE_name = map[int32]string{
	0: "GAME_INFORMATION_TYPE_INVALID",
	1: "GAME_INFORMATION_TYPE_RANK",
	2: "GAME_INFORMATION_TYPE_DIY",
}
var GameInformation_GAME_INFORMATION_TYPE_value = map[string]int32{
	"GAME_INFORMATION_TYPE_INVALID": 0,
	"GAME_INFORMATION_TYPE_RANK":    1,
	"GAME_INFORMATION_TYPE_DIY":     2,
}

func (x GameInformation_GAME_INFORMATION_TYPE) String() string {
	return proto.EnumName(GameInformation_GAME_INFORMATION_TYPE_name, int32(x))
}
func (GameInformation_GAME_INFORMATION_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{0, 0}
}

// buf:lint:ignore ENUM_PASCAL_CASE
type GameInformation_GAME_INFORMATION_SELECT_TYPE int32

const (
	GameInformation_GAME_INFORMATION_SELECT_TYPE_INVALID GameInformation_GAME_INFORMATION_SELECT_TYPE = 0
	GameInformation_GAME_INFORMATION_SELECT_TYPE_SINGLE  GameInformation_GAME_INFORMATION_SELECT_TYPE = 1
	GameInformation_GAME_INFORMATION_SELECT_TYPE_MULTI   GameInformation_GAME_INFORMATION_SELECT_TYPE = 2
)

var GameInformation_GAME_INFORMATION_SELECT_TYPE_name = map[int32]string{
	0: "GAME_INFORMATION_SELECT_TYPE_INVALID",
	1: "GAME_INFORMATION_SELECT_TYPE_SINGLE",
	2: "GAME_INFORMATION_SELECT_TYPE_MULTI",
}
var GameInformation_GAME_INFORMATION_SELECT_TYPE_value = map[string]int32{
	"GAME_INFORMATION_SELECT_TYPE_INVALID": 0,
	"GAME_INFORMATION_SELECT_TYPE_SINGLE":  1,
	"GAME_INFORMATION_SELECT_TYPE_MULTI":   2,
}

func (x GameInformation_GAME_INFORMATION_SELECT_TYPE) String() string {
	return proto.EnumName(GameInformation_GAME_INFORMATION_SELECT_TYPE_name, int32(x))
}
func (GameInformation_GAME_INFORMATION_SELECT_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{0, 1}
}

type AddUserAuditSkillRequest_IgnoreAuditResource int32

const (
	AddUserAuditSkillRequest_IGNORE_AUDIT_RESOURCE_UNSPECIFIED AddUserAuditSkillRequest_IgnoreAuditResource = 0
	AddUserAuditSkillRequest_IGNORE_AUDIT_RESOURCE_EVIDENCE    AddUserAuditSkillRequest_IgnoreAuditResource = 1
	AddUserAuditSkillRequest_IGNORE_AUDIT_RESOURCE_AUDIO       AddUserAuditSkillRequest_IgnoreAuditResource = 2
)

var AddUserAuditSkillRequest_IgnoreAuditResource_name = map[int32]string{
	0: "IGNORE_AUDIT_RESOURCE_UNSPECIFIED",
	1: "IGNORE_AUDIT_RESOURCE_EVIDENCE",
	2: "IGNORE_AUDIT_RESOURCE_AUDIO",
}
var AddUserAuditSkillRequest_IgnoreAuditResource_value = map[string]int32{
	"IGNORE_AUDIT_RESOURCE_UNSPECIFIED": 0,
	"IGNORE_AUDIT_RESOURCE_EVIDENCE":    1,
	"IGNORE_AUDIT_RESOURCE_AUDIO":       2,
}

func (x AddUserAuditSkillRequest_IgnoreAuditResource) String() string {
	return proto.EnumName(AddUserAuditSkillRequest_IgnoreAuditResource_name, int32(x))
}
func (AddUserAuditSkillRequest_IgnoreAuditResource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{16, 0}
}

type CheckLabelOrderRequest_CheckType int32

const (
	CheckLabelOrderRequest_CHECK_TYPE_UNSPECIFIED CheckLabelOrderRequest_CheckType = 0
	CheckLabelOrderRequest_CHECK_TYPE_ADD         CheckLabelOrderRequest_CheckType = 1
	CheckLabelOrderRequest_CHECK_TYPE_EDIT        CheckLabelOrderRequest_CheckType = 2
)

var CheckLabelOrderRequest_CheckType_name = map[int32]string{
	0: "CHECK_TYPE_UNSPECIFIED",
	1: "CHECK_TYPE_ADD",
	2: "CHECK_TYPE_EDIT",
}
var CheckLabelOrderRequest_CheckType_value = map[string]int32{
	"CHECK_TYPE_UNSPECIFIED": 0,
	"CHECK_TYPE_ADD":         1,
	"CHECK_TYPE_EDIT":        2,
}

func (x CheckLabelOrderRequest_CheckType) String() string {
	return proto.EnumName(CheckLabelOrderRequest_CheckType_name, int32(x))
}
func (CheckLabelOrderRequest_CheckType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{141, 0}
}

// 游戏section（段位信息、擅长位置、擅长英雄...）
type GameInformation struct {
	InformationType      GameInformation_GAME_INFORMATION_TYPE        `protobuf:"varint,1,opt,name=information_type,json=informationType,proto3,enum=esport_skill.GameInformation_GAME_INFORMATION_TYPE" json:"information_type,omitempty"`
	SectionName          string                                       `protobuf:"bytes,2,opt,name=section_name,json=sectionName,proto3" json:"section_name,omitempty"`
	ItemList             []string                                     `protobuf:"bytes,3,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	SelectType           GameInformation_GAME_INFORMATION_SELECT_TYPE `protobuf:"varint,4,opt,name=select_type,json=selectType,proto3,enum=esport_skill.GameInformation_GAME_INFORMATION_SELECT_TYPE" json:"select_type,omitempty"`
	SectionId            uint32                                       `protobuf:"varint,5,opt,name=section_id,json=sectionId,proto3" json:"section_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *GameInformation) Reset()         { *m = GameInformation{} }
func (m *GameInformation) String() string { return proto.CompactTextString(m) }
func (*GameInformation) ProtoMessage()    {}
func (*GameInformation) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{0}
}
func (m *GameInformation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInformation.Unmarshal(m, b)
}
func (m *GameInformation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInformation.Marshal(b, m, deterministic)
}
func (dst *GameInformation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInformation.Merge(dst, src)
}
func (m *GameInformation) XXX_Size() int {
	return xxx_messageInfo_GameInformation.Size(m)
}
func (m *GameInformation) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInformation.DiscardUnknown(m)
}

var xxx_messageInfo_GameInformation proto.InternalMessageInfo

func (m *GameInformation) GetInformationType() GameInformation_GAME_INFORMATION_TYPE {
	if m != nil {
		return m.InformationType
	}
	return GameInformation_GAME_INFORMATION_TYPE_INVALID
}

func (m *GameInformation) GetSectionName() string {
	if m != nil {
		return m.SectionName
	}
	return ""
}

func (m *GameInformation) GetItemList() []string {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *GameInformation) GetSelectType() GameInformation_GAME_INFORMATION_SELECT_TYPE {
	if m != nil {
		return m.SelectType
	}
	return GameInformation_GAME_INFORMATION_SELECT_TYPE_INVALID
}

func (m *GameInformation) GetSectionId() uint32 {
	if m != nil {
		return m.SectionId
	}
	return 0
}

// 基础定价
type BasePrice struct {
	GamePricingUnitType  GAME_PRICING_UNIT_TYPE `protobuf:"varint,1,opt,name=game_pricing_unit_type,json=gamePricingUnitType,proto3,enum=esport_skill.GAME_PRICING_UNIT_TYPE" json:"game_pricing_unit_type,omitempty"`
	RankPriceMap         map[uint32]uint32      `protobuf:"bytes,2,rep,name=rank_price_map,json=rankPriceMap,proto3" json:"rank_price_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BasePrice) Reset()         { *m = BasePrice{} }
func (m *BasePrice) String() string { return proto.CompactTextString(m) }
func (*BasePrice) ProtoMessage()    {}
func (*BasePrice) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{1}
}
func (m *BasePrice) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BasePrice.Unmarshal(m, b)
}
func (m *BasePrice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BasePrice.Marshal(b, m, deterministic)
}
func (dst *BasePrice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BasePrice.Merge(dst, src)
}
func (m *BasePrice) XXX_Size() int {
	return xxx_messageInfo_BasePrice.Size(m)
}
func (m *BasePrice) XXX_DiscardUnknown() {
	xxx_messageInfo_BasePrice.DiscardUnknown(m)
}

var xxx_messageInfo_BasePrice proto.InternalMessageInfo

func (m *BasePrice) GetGamePricingUnitType() GAME_PRICING_UNIT_TYPE {
	if m != nil {
		return m.GamePricingUnitType
	}
	return GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_INVALID
}

func (m *BasePrice) GetRankPriceMap() map[uint32]uint32 {
	if m != nil {
		return m.RankPriceMap
	}
	return nil
}

// 定价
type GamePricing struct {
	GamePricingType      GAME_PRICING_TYPE      `protobuf:"varint,1,opt,name=game_pricing_type,json=gamePricingType,proto3,enum=esport_skill.GAME_PRICING_TYPE" json:"game_pricing_type,omitempty"`
	GamePricingUnitType  GAME_PRICING_UNIT_TYPE `protobuf:"varint,2,opt,name=game_pricing_unit_type,json=gamePricingUnitType,proto3,enum=esport_skill.GAME_PRICING_UNIT_TYPE" json:"game_pricing_unit_type,omitempty"`
	Price                []uint32               `protobuf:"varint,3,rep,packed,name=price,proto3" json:"price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GamePricing) Reset()         { *m = GamePricing{} }
func (m *GamePricing) String() string { return proto.CompactTextString(m) }
func (*GamePricing) ProtoMessage()    {}
func (*GamePricing) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{2}
}
func (m *GamePricing) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GamePricing.Unmarshal(m, b)
}
func (m *GamePricing) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GamePricing.Marshal(b, m, deterministic)
}
func (dst *GamePricing) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GamePricing.Merge(dst, src)
}
func (m *GamePricing) XXX_Size() int {
	return xxx_messageInfo_GamePricing.Size(m)
}
func (m *GamePricing) XXX_DiscardUnknown() {
	xxx_messageInfo_GamePricing.DiscardUnknown(m)
}

var xxx_messageInfo_GamePricing proto.InternalMessageInfo

func (m *GamePricing) GetGamePricingType() GAME_PRICING_TYPE {
	if m != nil {
		return m.GamePricingType
	}
	return GAME_PRICING_TYPE_GAME_PRICING_TYPE_INVALID
}

func (m *GamePricing) GetGamePricingUnitType() GAME_PRICING_UNIT_TYPE {
	if m != nil {
		return m.GamePricingUnitType
	}
	return GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_INVALID
}

func (m *GamePricing) GetPrice() []uint32 {
	if m != nil {
		return m.Price
	}
	return nil
}

type EsportGameConfig struct {
	GameId               uint32              `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Name                 string              `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	GameType             GAME_TYPE           `protobuf:"varint,3,opt,name=game_type,json=gameType,proto3,enum=esport_skill.GAME_TYPE" json:"game_type,omitempty"`
	GameIcon             string              `protobuf:"bytes,4,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon,omitempty"`
	GameBackground       string              `protobuf:"bytes,5,opt,name=game_background,json=gameBackground,proto3" json:"game_background,omitempty"`
	GameColor            string              `protobuf:"bytes,6,opt,name=game_color,json=gameColor,proto3" json:"game_color,omitempty"`
	GameRank             float32             `protobuf:"fixed32,7,opt,name=game_rank,json=gameRank,proto3" json:"game_rank,omitempty"`
	SkillEvidence        string              `protobuf:"bytes,8,opt,name=skill_evidence,json=skillEvidence,proto3" json:"skill_evidence,omitempty"`
	SkillDesc            string              `protobuf:"bytes,9,opt,name=skill_desc,json=skillDesc,proto3" json:"skill_desc,omitempty"`
	GameInformationList  []*GameInformation  `protobuf:"bytes,10,rep,name=game_information_list,json=gameInformationList,proto3" json:"game_information_list,omitempty"`
	GamePricing          *GamePricing        `protobuf:"bytes,11,opt,name=game_pricing,json=gamePricing,proto3" json:"game_pricing,omitempty"`
	UpdateTime           int64               `protobuf:"varint,12,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	BasePrice            *BasePrice          `protobuf:"bytes,13,opt,name=base_price,json=basePrice,proto3" json:"base_price,omitempty"`
	GameCardInfoItemList []*GameCardInfoItem `protobuf:"bytes,14,rep,name=game_card_info_item_list,json=gameCardInfoItemList,proto3" json:"game_card_info_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *EsportGameConfig) Reset()         { *m = EsportGameConfig{} }
func (m *EsportGameConfig) String() string { return proto.CompactTextString(m) }
func (*EsportGameConfig) ProtoMessage()    {}
func (*EsportGameConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{3}
}
func (m *EsportGameConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportGameConfig.Unmarshal(m, b)
}
func (m *EsportGameConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportGameConfig.Marshal(b, m, deterministic)
}
func (dst *EsportGameConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportGameConfig.Merge(dst, src)
}
func (m *EsportGameConfig) XXX_Size() int {
	return xxx_messageInfo_EsportGameConfig.Size(m)
}
func (m *EsportGameConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportGameConfig.DiscardUnknown(m)
}

var xxx_messageInfo_EsportGameConfig proto.InternalMessageInfo

func (m *EsportGameConfig) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *EsportGameConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *EsportGameConfig) GetGameType() GAME_TYPE {
	if m != nil {
		return m.GameType
	}
	return GAME_TYPE_GAME_TYPE_INVALID
}

func (m *EsportGameConfig) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

func (m *EsportGameConfig) GetGameBackground() string {
	if m != nil {
		return m.GameBackground
	}
	return ""
}

func (m *EsportGameConfig) GetGameColor() string {
	if m != nil {
		return m.GameColor
	}
	return ""
}

func (m *EsportGameConfig) GetGameRank() float32 {
	if m != nil {
		return m.GameRank
	}
	return 0
}

func (m *EsportGameConfig) GetSkillEvidence() string {
	if m != nil {
		return m.SkillEvidence
	}
	return ""
}

func (m *EsportGameConfig) GetSkillDesc() string {
	if m != nil {
		return m.SkillDesc
	}
	return ""
}

func (m *EsportGameConfig) GetGameInformationList() []*GameInformation {
	if m != nil {
		return m.GameInformationList
	}
	return nil
}

func (m *EsportGameConfig) GetGamePricing() *GamePricing {
	if m != nil {
		return m.GamePricing
	}
	return nil
}

func (m *EsportGameConfig) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *EsportGameConfig) GetBasePrice() *BasePrice {
	if m != nil {
		return m.BasePrice
	}
	return nil
}

func (m *EsportGameConfig) GetGameCardInfoItemList() []*GameCardInfoItem {
	if m != nil {
		return m.GameCardInfoItemList
	}
	return nil
}

// 新增游戏配置
type AddEsportGameConfigRequest struct {
	EsportGameConfig     *EsportGameConfig `protobuf:"bytes,1,opt,name=esport_game_config,json=esportGameConfig,proto3" json:"esport_game_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddEsportGameConfigRequest) Reset()         { *m = AddEsportGameConfigRequest{} }
func (m *AddEsportGameConfigRequest) String() string { return proto.CompactTextString(m) }
func (*AddEsportGameConfigRequest) ProtoMessage()    {}
func (*AddEsportGameConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{4}
}
func (m *AddEsportGameConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddEsportGameConfigRequest.Unmarshal(m, b)
}
func (m *AddEsportGameConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddEsportGameConfigRequest.Marshal(b, m, deterministic)
}
func (dst *AddEsportGameConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddEsportGameConfigRequest.Merge(dst, src)
}
func (m *AddEsportGameConfigRequest) XXX_Size() int {
	return xxx_messageInfo_AddEsportGameConfigRequest.Size(m)
}
func (m *AddEsportGameConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddEsportGameConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddEsportGameConfigRequest proto.InternalMessageInfo

func (m *AddEsportGameConfigRequest) GetEsportGameConfig() *EsportGameConfig {
	if m != nil {
		return m.EsportGameConfig
	}
	return nil
}

type AddEsportGameConfigResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddEsportGameConfigResponse) Reset()         { *m = AddEsportGameConfigResponse{} }
func (m *AddEsportGameConfigResponse) String() string { return proto.CompactTextString(m) }
func (*AddEsportGameConfigResponse) ProtoMessage()    {}
func (*AddEsportGameConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{5}
}
func (m *AddEsportGameConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddEsportGameConfigResponse.Unmarshal(m, b)
}
func (m *AddEsportGameConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddEsportGameConfigResponse.Marshal(b, m, deterministic)
}
func (dst *AddEsportGameConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddEsportGameConfigResponse.Merge(dst, src)
}
func (m *AddEsportGameConfigResponse) XXX_Size() int {
	return xxx_messageInfo_AddEsportGameConfigResponse.Size(m)
}
func (m *AddEsportGameConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddEsportGameConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddEsportGameConfigResponse proto.InternalMessageInfo

// 更新游戏配置
type UpdateEsportGameConfigRequest struct {
	Config               *EsportGameConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpdateEsportGameConfigRequest) Reset()         { *m = UpdateEsportGameConfigRequest{} }
func (m *UpdateEsportGameConfigRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateEsportGameConfigRequest) ProtoMessage()    {}
func (*UpdateEsportGameConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{6}
}
func (m *UpdateEsportGameConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateEsportGameConfigRequest.Unmarshal(m, b)
}
func (m *UpdateEsportGameConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateEsportGameConfigRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateEsportGameConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateEsportGameConfigRequest.Merge(dst, src)
}
func (m *UpdateEsportGameConfigRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateEsportGameConfigRequest.Size(m)
}
func (m *UpdateEsportGameConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateEsportGameConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateEsportGameConfigRequest proto.InternalMessageInfo

func (m *UpdateEsportGameConfigRequest) GetConfig() *EsportGameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpdateEsportGameConfigResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateEsportGameConfigResponse) Reset()         { *m = UpdateEsportGameConfigResponse{} }
func (m *UpdateEsportGameConfigResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateEsportGameConfigResponse) ProtoMessage()    {}
func (*UpdateEsportGameConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{7}
}
func (m *UpdateEsportGameConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateEsportGameConfigResponse.Unmarshal(m, b)
}
func (m *UpdateEsportGameConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateEsportGameConfigResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateEsportGameConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateEsportGameConfigResponse.Merge(dst, src)
}
func (m *UpdateEsportGameConfigResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateEsportGameConfigResponse.Size(m)
}
func (m *UpdateEsportGameConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateEsportGameConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateEsportGameConfigResponse proto.InternalMessageInfo

// 删除游戏
type DeleteEsportGameConfigRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteEsportGameConfigRequest) Reset()         { *m = DeleteEsportGameConfigRequest{} }
func (m *DeleteEsportGameConfigRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteEsportGameConfigRequest) ProtoMessage()    {}
func (*DeleteEsportGameConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{8}
}
func (m *DeleteEsportGameConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteEsportGameConfigRequest.Unmarshal(m, b)
}
func (m *DeleteEsportGameConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteEsportGameConfigRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteEsportGameConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteEsportGameConfigRequest.Merge(dst, src)
}
func (m *DeleteEsportGameConfigRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteEsportGameConfigRequest.Size(m)
}
func (m *DeleteEsportGameConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteEsportGameConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteEsportGameConfigRequest proto.InternalMessageInfo

func (m *DeleteEsportGameConfigRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type DeleteEsportGameConfigResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteEsportGameConfigResponse) Reset()         { *m = DeleteEsportGameConfigResponse{} }
func (m *DeleteEsportGameConfigResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteEsportGameConfigResponse) ProtoMessage()    {}
func (*DeleteEsportGameConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{9}
}
func (m *DeleteEsportGameConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteEsportGameConfigResponse.Unmarshal(m, b)
}
func (m *DeleteEsportGameConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteEsportGameConfigResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteEsportGameConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteEsportGameConfigResponse.Merge(dst, src)
}
func (m *DeleteEsportGameConfigResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteEsportGameConfigResponse.Size(m)
}
func (m *DeleteEsportGameConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteEsportGameConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteEsportGameConfigResponse proto.InternalMessageInfo

// 分页获取游戏列表
type GetEsportGameConfigListByPageRequest struct {
	GameType             GAME_TYPE `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=esport_skill.GAME_TYPE" json:"game_type,omitempty"`
	GameName             string    `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	PageNum              uint32    `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             uint32    `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetEsportGameConfigListByPageRequest) Reset()         { *m = GetEsportGameConfigListByPageRequest{} }
func (m *GetEsportGameConfigListByPageRequest) String() string { return proto.CompactTextString(m) }
func (*GetEsportGameConfigListByPageRequest) ProtoMessage()    {}
func (*GetEsportGameConfigListByPageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{10}
}
func (m *GetEsportGameConfigListByPageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGameConfigListByPageRequest.Unmarshal(m, b)
}
func (m *GetEsportGameConfigListByPageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGameConfigListByPageRequest.Marshal(b, m, deterministic)
}
func (dst *GetEsportGameConfigListByPageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGameConfigListByPageRequest.Merge(dst, src)
}
func (m *GetEsportGameConfigListByPageRequest) XXX_Size() int {
	return xxx_messageInfo_GetEsportGameConfigListByPageRequest.Size(m)
}
func (m *GetEsportGameConfigListByPageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGameConfigListByPageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGameConfigListByPageRequest proto.InternalMessageInfo

func (m *GetEsportGameConfigListByPageRequest) GetGameType() GAME_TYPE {
	if m != nil {
		return m.GameType
	}
	return GAME_TYPE_GAME_TYPE_INVALID
}

func (m *GetEsportGameConfigListByPageRequest) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GetEsportGameConfigListByPageRequest) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetEsportGameConfigListByPageRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetEsportGameConfigListByPageResponse struct {
	ItemList             []*EsportGameConfig `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	TotalCnt             uint32              `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetEsportGameConfigListByPageResponse) Reset()         { *m = GetEsportGameConfigListByPageResponse{} }
func (m *GetEsportGameConfigListByPageResponse) String() string { return proto.CompactTextString(m) }
func (*GetEsportGameConfigListByPageResponse) ProtoMessage()    {}
func (*GetEsportGameConfigListByPageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{11}
}
func (m *GetEsportGameConfigListByPageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGameConfigListByPageResponse.Unmarshal(m, b)
}
func (m *GetEsportGameConfigListByPageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGameConfigListByPageResponse.Marshal(b, m, deterministic)
}
func (dst *GetEsportGameConfigListByPageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGameConfigListByPageResponse.Merge(dst, src)
}
func (m *GetEsportGameConfigListByPageResponse) XXX_Size() int {
	return xxx_messageInfo_GetEsportGameConfigListByPageResponse.Size(m)
}
func (m *GetEsportGameConfigListByPageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGameConfigListByPageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGameConfigListByPageResponse proto.InternalMessageInfo

func (m *GetEsportGameConfigListByPageResponse) GetItemList() []*EsportGameConfig {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *GetEsportGameConfigListByPageResponse) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 获取顶部游戏列表
type GetTopGameListRequest struct {
	Uid                  uint32    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameType             GAME_TYPE `protobuf:"varint,2,opt,name=game_type,json=gameType,proto3,enum=esport_skill.GAME_TYPE" json:"game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetTopGameListRequest) Reset()         { *m = GetTopGameListRequest{} }
func (m *GetTopGameListRequest) String() string { return proto.CompactTextString(m) }
func (*GetTopGameListRequest) ProtoMessage()    {}
func (*GetTopGameListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{12}
}
func (m *GetTopGameListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopGameListRequest.Unmarshal(m, b)
}
func (m *GetTopGameListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopGameListRequest.Marshal(b, m, deterministic)
}
func (dst *GetTopGameListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopGameListRequest.Merge(dst, src)
}
func (m *GetTopGameListRequest) XXX_Size() int {
	return xxx_messageInfo_GetTopGameListRequest.Size(m)
}
func (m *GetTopGameListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopGameListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopGameListRequest proto.InternalMessageInfo

func (m *GetTopGameListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTopGameListRequest) GetGameType() GAME_TYPE {
	if m != nil {
		return m.GameType
	}
	return GAME_TYPE_GAME_TYPE_INVALID
}

type GetTopGameListResponse struct {
	ItemList             []*GameItem `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetTopGameListResponse) Reset()         { *m = GetTopGameListResponse{} }
func (m *GetTopGameListResponse) String() string { return proto.CompactTextString(m) }
func (*GetTopGameListResponse) ProtoMessage()    {}
func (*GetTopGameListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{13}
}
func (m *GetTopGameListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopGameListResponse.Unmarshal(m, b)
}
func (m *GetTopGameListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopGameListResponse.Marshal(b, m, deterministic)
}
func (dst *GetTopGameListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopGameListResponse.Merge(dst, src)
}
func (m *GetTopGameListResponse) XXX_Size() int {
	return xxx_messageInfo_GetTopGameListResponse.Size(m)
}
func (m *GetTopGameListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopGameListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopGameListResponse proto.InternalMessageInfo

func (m *GetTopGameListResponse) GetItemList() []*GameItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type SectionInfo struct {
	SectionName          string   `protobuf:"bytes,1,opt,name=section_name,json=sectionName,proto3" json:"section_name,omitempty"`
	ItemList             []string `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	SectionId            uint32   `protobuf:"varint,3,opt,name=section_id,json=sectionId,proto3" json:"section_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SectionInfo) Reset()         { *m = SectionInfo{} }
func (m *SectionInfo) String() string { return proto.CompactTextString(m) }
func (*SectionInfo) ProtoMessage()    {}
func (*SectionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{14}
}
func (m *SectionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SectionInfo.Unmarshal(m, b)
}
func (m *SectionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SectionInfo.Marshal(b, m, deterministic)
}
func (dst *SectionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SectionInfo.Merge(dst, src)
}
func (m *SectionInfo) XXX_Size() int {
	return xxx_messageInfo_SectionInfo.Size(m)
}
func (m *SectionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SectionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SectionInfo proto.InternalMessageInfo

func (m *SectionInfo) GetSectionName() string {
	if m != nil {
		return m.SectionName
	}
	return ""
}

func (m *SectionInfo) GetItemList() []string {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *SectionInfo) GetSectionId() uint32 {
	if m != nil {
		return m.SectionId
	}
	return 0
}

// 用户游戏资料信息
type UserSkillInfo struct {
	GameId               uint32         `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string         `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	SkillEvidence        string         `protobuf:"bytes,3,opt,name=skill_evidence,json=skillEvidence,proto3" json:"skill_evidence,omitempty"`
	Audio                string         `protobuf:"bytes,4,opt,name=audio,proto3" json:"audio,omitempty"`
	AudioDuration        uint32         `protobuf:"varint,5,opt,name=audio_duration,json=audioDuration,proto3" json:"audio_duration,omitempty"`
	SectionList          []*SectionInfo `protobuf:"bytes,6,rep,name=section_list,json=sectionList,proto3" json:"section_list,omitempty"`
	TextDesc             string         `protobuf:"bytes,7,opt,name=text_desc,json=textDesc,proto3" json:"text_desc,omitempty"`
	GameRank             uint32         `protobuf:"varint,9,opt,name=game_rank,json=gameRank,proto3" json:"game_rank,omitempty"`
	IsGuaranteeWin       bool           `protobuf:"varint,10,opt,name=is_guarantee_win,json=isGuaranteeWin,proto3" json:"is_guarantee_win,omitempty"`
	FreezeType           FreezeType     `protobuf:"varint,11,opt,name=freeze_type,json=freezeType,proto3,enum=esport_skill.FreezeType" json:"freeze_type,omitempty"`
	FreezeStopTs         int64          `protobuf:"varint,12,opt,name=freeze_stop_ts,json=freezeStopTs,proto3" json:"freeze_stop_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserSkillInfo) Reset()         { *m = UserSkillInfo{} }
func (m *UserSkillInfo) String() string { return proto.CompactTextString(m) }
func (*UserSkillInfo) ProtoMessage()    {}
func (*UserSkillInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{15}
}
func (m *UserSkillInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSkillInfo.Unmarshal(m, b)
}
func (m *UserSkillInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSkillInfo.Marshal(b, m, deterministic)
}
func (dst *UserSkillInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSkillInfo.Merge(dst, src)
}
func (m *UserSkillInfo) XXX_Size() int {
	return xxx_messageInfo_UserSkillInfo.Size(m)
}
func (m *UserSkillInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSkillInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserSkillInfo proto.InternalMessageInfo

func (m *UserSkillInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserSkillInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *UserSkillInfo) GetSkillEvidence() string {
	if m != nil {
		return m.SkillEvidence
	}
	return ""
}

func (m *UserSkillInfo) GetAudio() string {
	if m != nil {
		return m.Audio
	}
	return ""
}

func (m *UserSkillInfo) GetAudioDuration() uint32 {
	if m != nil {
		return m.AudioDuration
	}
	return 0
}

func (m *UserSkillInfo) GetSectionList() []*SectionInfo {
	if m != nil {
		return m.SectionList
	}
	return nil
}

func (m *UserSkillInfo) GetTextDesc() string {
	if m != nil {
		return m.TextDesc
	}
	return ""
}

func (m *UserSkillInfo) GetGameRank() uint32 {
	if m != nil {
		return m.GameRank
	}
	return 0
}

func (m *UserSkillInfo) GetIsGuaranteeWin() bool {
	if m != nil {
		return m.IsGuaranteeWin
	}
	return false
}

func (m *UserSkillInfo) GetFreezeType() FreezeType {
	if m != nil {
		return m.FreezeType
	}
	return FreezeType_FREEZE_TYPE_UNFREEZE
}

func (m *UserSkillInfo) GetFreezeStopTs() int64 {
	if m != nil {
		return m.FreezeStopTs
	}
	return 0
}

// 增加新技能审核信息请求
type AddUserAuditSkillRequest struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AuditToken           string           `protobuf:"bytes,2,opt,name=audit_token,json=auditToken,proto3" json:"audit_token,omitempty"`
	AuditSource          uint32           `protobuf:"varint,3,opt,name=audit_source,json=auditSource,proto3" json:"audit_source,omitempty"`
	Skill                []*UserSkillInfo `protobuf:"bytes,4,rep,name=skill,proto3" json:"skill,omitempty"`
	GuildId              uint32           `protobuf:"varint,5,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	AuditType            uint32           `protobuf:"varint,6,opt,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	IgnoreAuditResource  uint64           `protobuf:"varint,7,opt,name=ignore_audit_resource,json=ignoreAuditResource,proto3" json:"ignore_audit_resource,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AddUserAuditSkillRequest) Reset()         { *m = AddUserAuditSkillRequest{} }
func (m *AddUserAuditSkillRequest) String() string { return proto.CompactTextString(m) }
func (*AddUserAuditSkillRequest) ProtoMessage()    {}
func (*AddUserAuditSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{16}
}
func (m *AddUserAuditSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserAuditSkillRequest.Unmarshal(m, b)
}
func (m *AddUserAuditSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserAuditSkillRequest.Marshal(b, m, deterministic)
}
func (dst *AddUserAuditSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserAuditSkillRequest.Merge(dst, src)
}
func (m *AddUserAuditSkillRequest) XXX_Size() int {
	return xxx_messageInfo_AddUserAuditSkillRequest.Size(m)
}
func (m *AddUserAuditSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserAuditSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserAuditSkillRequest proto.InternalMessageInfo

func (m *AddUserAuditSkillRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserAuditSkillRequest) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *AddUserAuditSkillRequest) GetAuditSource() uint32 {
	if m != nil {
		return m.AuditSource
	}
	return 0
}

func (m *AddUserAuditSkillRequest) GetSkill() []*UserSkillInfo {
	if m != nil {
		return m.Skill
	}
	return nil
}

func (m *AddUserAuditSkillRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AddUserAuditSkillRequest) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *AddUserAuditSkillRequest) GetIgnoreAuditResource() uint64 {
	if m != nil {
		return m.IgnoreAuditResource
	}
	return 0
}

// 增加新技能审核信息响应
type AddUserAuditSkillResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserAuditSkillResponse) Reset()         { *m = AddUserAuditSkillResponse{} }
func (m *AddUserAuditSkillResponse) String() string { return proto.CompactTextString(m) }
func (*AddUserAuditSkillResponse) ProtoMessage()    {}
func (*AddUserAuditSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{17}
}
func (m *AddUserAuditSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserAuditSkillResponse.Unmarshal(m, b)
}
func (m *AddUserAuditSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserAuditSkillResponse.Marshal(b, m, deterministic)
}
func (dst *AddUserAuditSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserAuditSkillResponse.Merge(dst, src)
}
func (m *AddUserAuditSkillResponse) XXX_Size() int {
	return xxx_messageInfo_AddUserAuditSkillResponse.Size(m)
}
func (m *AddUserAuditSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserAuditSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserAuditSkillResponse proto.InternalMessageInfo

// 获取技能审核信息请求
type GetUserAuditSkillRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AuditToken           string   `protobuf:"bytes,2,opt,name=audit_token,json=auditToken,proto3" json:"audit_token,omitempty"`
	AuditSource          uint32   `protobuf:"varint,3,opt,name=audit_source,json=auditSource,proto3" json:"audit_source,omitempty"`
	WithUrlPrefix        bool     `protobuf:"varint,4,opt,name=with_url_prefix,json=withUrlPrefix,proto3" json:"with_url_prefix,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAuditSkillRequest) Reset()         { *m = GetUserAuditSkillRequest{} }
func (m *GetUserAuditSkillRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserAuditSkillRequest) ProtoMessage()    {}
func (*GetUserAuditSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{18}
}
func (m *GetUserAuditSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAuditSkillRequest.Unmarshal(m, b)
}
func (m *GetUserAuditSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAuditSkillRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserAuditSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAuditSkillRequest.Merge(dst, src)
}
func (m *GetUserAuditSkillRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserAuditSkillRequest.Size(m)
}
func (m *GetUserAuditSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAuditSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAuditSkillRequest proto.InternalMessageInfo

func (m *GetUserAuditSkillRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserAuditSkillRequest) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *GetUserAuditSkillRequest) GetAuditSource() uint32 {
	if m != nil {
		return m.AuditSource
	}
	return 0
}

func (m *GetUserAuditSkillRequest) GetWithUrlPrefix() bool {
	if m != nil {
		return m.WithUrlPrefix
	}
	return false
}

// 获取技能审核信息响应
type GetUserAuditSkillResponse struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Skill                []*UserSkillInfo `protobuf:"bytes,2,rep,name=skill,proto3" json:"skill,omitempty"`
	AuditType            uint32           `protobuf:"varint,3,opt,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserAuditSkillResponse) Reset()         { *m = GetUserAuditSkillResponse{} }
func (m *GetUserAuditSkillResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserAuditSkillResponse) ProtoMessage()    {}
func (*GetUserAuditSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{19}
}
func (m *GetUserAuditSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAuditSkillResponse.Unmarshal(m, b)
}
func (m *GetUserAuditSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAuditSkillResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserAuditSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAuditSkillResponse.Merge(dst, src)
}
func (m *GetUserAuditSkillResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserAuditSkillResponse.Size(m)
}
func (m *GetUserAuditSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAuditSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAuditSkillResponse proto.InternalMessageInfo

func (m *GetUserAuditSkillResponse) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserAuditSkillResponse) GetSkill() []*UserSkillInfo {
	if m != nil {
		return m.Skill
	}
	return nil
}

func (m *GetUserAuditSkillResponse) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

// 获取新增技能审核信息请求
type BatchGetAuditSkillRequest struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uids                 []uint32 `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	AuditType            []uint32 `protobuf:"varint,3,rep,packed,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	OffSet               uint32   `protobuf:"varint,4,opt,name=off_set,json=offSet,proto3" json:"off_set,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	NeedTotal            uint32   `protobuf:"varint,6,opt,name=need_total,json=needTotal,proto3" json:"need_total,omitempty"`
	SearchType           uint32   `protobuf:"varint,7,opt,name=search_type,json=searchType,proto3" json:"search_type,omitempty"`
	AuditSource          []uint32 `protobuf:"varint,8,rep,packed,name=audit_source,json=auditSource,proto3" json:"audit_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAuditSkillRequest) Reset()         { *m = BatchGetAuditSkillRequest{} }
func (m *BatchGetAuditSkillRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetAuditSkillRequest) ProtoMessage()    {}
func (*BatchGetAuditSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{20}
}
func (m *BatchGetAuditSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAuditSkillRequest.Unmarshal(m, b)
}
func (m *BatchGetAuditSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAuditSkillRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetAuditSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAuditSkillRequest.Merge(dst, src)
}
func (m *BatchGetAuditSkillRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetAuditSkillRequest.Size(m)
}
func (m *BatchGetAuditSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAuditSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAuditSkillRequest proto.InternalMessageInfo

func (m *BatchGetAuditSkillRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *BatchGetAuditSkillRequest) GetAuditType() []uint32 {
	if m != nil {
		return m.AuditType
	}
	return nil
}

func (m *BatchGetAuditSkillRequest) GetOffSet() uint32 {
	if m != nil {
		return m.OffSet
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetNeedTotal() uint32 {
	if m != nil {
		return m.NeedTotal
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetSearchType() uint32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetAuditSource() []uint32 {
	if m != nil {
		return m.AuditSource
	}
	return nil
}

type AuditSkillRecord struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string           `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string           `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Ttid                 string           `protobuf:"bytes,4,opt,name=ttid,proto3" json:"ttid,omitempty"`
	AuditToken           string           `protobuf:"bytes,5,opt,name=audit_token,json=auditToken,proto3" json:"audit_token,omitempty"`
	AuditType            uint32           `protobuf:"varint,6,opt,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	ApplyTime            uint32           `protobuf:"varint,7,opt,name=apply_time,json=applyTime,proto3" json:"apply_time,omitempty"`
	Skill                []*UserSkillInfo `protobuf:"bytes,8,rep,name=skill,proto3" json:"skill,omitempty"`
	AuditSource          uint32           `protobuf:"varint,9,opt,name=audit_source,json=auditSource,proto3" json:"audit_source,omitempty"`
	Reason               string           `protobuf:"bytes,10,opt,name=reason,proto3" json:"reason,omitempty"`
	GuildId              uint32           `protobuf:"varint,11,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	SignDuration         string           `protobuf:"bytes,12,opt,name=sign_duration,json=signDuration,proto3" json:"sign_duration,omitempty"`
	Operator             string           `protobuf:"bytes,13,opt,name=operator,proto3" json:"operator,omitempty"`
	UpdateTime           uint32           `protobuf:"varint,14,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AuditSkillRecord) Reset()         { *m = AuditSkillRecord{} }
func (m *AuditSkillRecord) String() string { return proto.CompactTextString(m) }
func (*AuditSkillRecord) ProtoMessage()    {}
func (*AuditSkillRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{21}
}
func (m *AuditSkillRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuditSkillRecord.Unmarshal(m, b)
}
func (m *AuditSkillRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuditSkillRecord.Marshal(b, m, deterministic)
}
func (dst *AuditSkillRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuditSkillRecord.Merge(dst, src)
}
func (m *AuditSkillRecord) XXX_Size() int {
	return xxx_messageInfo_AuditSkillRecord.Size(m)
}
func (m *AuditSkillRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_AuditSkillRecord.DiscardUnknown(m)
}

var xxx_messageInfo_AuditSkillRecord proto.InternalMessageInfo

func (m *AuditSkillRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AuditSkillRecord) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AuditSkillRecord) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *AuditSkillRecord) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AuditSkillRecord) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *AuditSkillRecord) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *AuditSkillRecord) GetApplyTime() uint32 {
	if m != nil {
		return m.ApplyTime
	}
	return 0
}

func (m *AuditSkillRecord) GetSkill() []*UserSkillInfo {
	if m != nil {
		return m.Skill
	}
	return nil
}

func (m *AuditSkillRecord) GetAuditSource() uint32 {
	if m != nil {
		return m.AuditSource
	}
	return 0
}

func (m *AuditSkillRecord) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *AuditSkillRecord) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AuditSkillRecord) GetSignDuration() string {
	if m != nil {
		return m.SignDuration
	}
	return ""
}

func (m *AuditSkillRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *AuditSkillRecord) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 获取新增技能审核信息请求
type BatchGetAuditSkillResponse struct {
	GuildId              uint32              `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uids                 []uint32            `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	AuditType            []uint32            `protobuf:"varint,3,rep,packed,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	OffSet               uint32              `protobuf:"varint,4,opt,name=off_set,json=offSet,proto3" json:"off_set,omitempty"`
	Limit                uint32              `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	List                 []*AuditSkillRecord `protobuf:"bytes,6,rep,name=list,proto3" json:"list,omitempty"`
	TotalCount           uint32              `protobuf:"varint,7,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	SearchType           uint32              `protobuf:"varint,8,opt,name=search_type,json=searchType,proto3" json:"search_type,omitempty"`
	AuditSource          []uint32            `protobuf:"varint,9,rep,packed,name=audit_source,json=auditSource,proto3" json:"audit_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetAuditSkillResponse) Reset()         { *m = BatchGetAuditSkillResponse{} }
func (m *BatchGetAuditSkillResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetAuditSkillResponse) ProtoMessage()    {}
func (*BatchGetAuditSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{22}
}
func (m *BatchGetAuditSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAuditSkillResponse.Unmarshal(m, b)
}
func (m *BatchGetAuditSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAuditSkillResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetAuditSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAuditSkillResponse.Merge(dst, src)
}
func (m *BatchGetAuditSkillResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetAuditSkillResponse.Size(m)
}
func (m *BatchGetAuditSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAuditSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAuditSkillResponse proto.InternalMessageInfo

func (m *BatchGetAuditSkillResponse) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *BatchGetAuditSkillResponse) GetAuditType() []uint32 {
	if m != nil {
		return m.AuditType
	}
	return nil
}

func (m *BatchGetAuditSkillResponse) GetOffSet() uint32 {
	if m != nil {
		return m.OffSet
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetList() []*AuditSkillRecord {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *BatchGetAuditSkillResponse) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetSearchType() uint32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetAuditSource() []uint32 {
	if m != nil {
		return m.AuditSource
	}
	return nil
}

// 设置用户技能审核结果
type SetUserSkillAuditTypeRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AuditToken           string   `protobuf:"bytes,2,opt,name=audit_token,json=auditToken,proto3" json:"audit_token,omitempty"`
	AuditType            uint32   `protobuf:"varint,3,opt,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	Reason               string   `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	Operator             string   `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	AuditTokenList       []string `protobuf:"bytes,6,rep,name=audit_token_list,json=auditTokenList,proto3" json:"audit_token_list,omitempty"`
	IsCancel             bool     `protobuf:"varint,7,opt,name=is_cancel,json=isCancel,proto3" json:"is_cancel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSkillAuditTypeRequest) Reset()         { *m = SetUserSkillAuditTypeRequest{} }
func (m *SetUserSkillAuditTypeRequest) String() string { return proto.CompactTextString(m) }
func (*SetUserSkillAuditTypeRequest) ProtoMessage()    {}
func (*SetUserSkillAuditTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{23}
}
func (m *SetUserSkillAuditTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSkillAuditTypeRequest.Unmarshal(m, b)
}
func (m *SetUserSkillAuditTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSkillAuditTypeRequest.Marshal(b, m, deterministic)
}
func (dst *SetUserSkillAuditTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSkillAuditTypeRequest.Merge(dst, src)
}
func (m *SetUserSkillAuditTypeRequest) XXX_Size() int {
	return xxx_messageInfo_SetUserSkillAuditTypeRequest.Size(m)
}
func (m *SetUserSkillAuditTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSkillAuditTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSkillAuditTypeRequest proto.InternalMessageInfo

func (m *SetUserSkillAuditTypeRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserSkillAuditTypeRequest) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *SetUserSkillAuditTypeRequest) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *SetUserSkillAuditTypeRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *SetUserSkillAuditTypeRequest) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *SetUserSkillAuditTypeRequest) GetAuditTokenList() []string {
	if m != nil {
		return m.AuditTokenList
	}
	return nil
}

func (m *SetUserSkillAuditTypeRequest) GetIsCancel() bool {
	if m != nil {
		return m.IsCancel
	}
	return false
}

type SetUserSkillAuditTypeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSkillAuditTypeResponse) Reset()         { *m = SetUserSkillAuditTypeResponse{} }
func (m *SetUserSkillAuditTypeResponse) String() string { return proto.CompactTextString(m) }
func (*SetUserSkillAuditTypeResponse) ProtoMessage()    {}
func (*SetUserSkillAuditTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{24}
}
func (m *SetUserSkillAuditTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSkillAuditTypeResponse.Unmarshal(m, b)
}
func (m *SetUserSkillAuditTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSkillAuditTypeResponse.Marshal(b, m, deterministic)
}
func (dst *SetUserSkillAuditTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSkillAuditTypeResponse.Merge(dst, src)
}
func (m *SetUserSkillAuditTypeResponse) XXX_Size() int {
	return xxx_messageInfo_SetUserSkillAuditTypeResponse.Size(m)
}
func (m *SetUserSkillAuditTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSkillAuditTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSkillAuditTypeResponse proto.InternalMessageInfo

type SetUserSkillRiskAuditTypeRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AuditToken           string   `protobuf:"bytes,2,opt,name=audit_token,json=auditToken,proto3" json:"audit_token,omitempty"`
	AuditType            uint32   `protobuf:"varint,3,opt,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	SceneCode            string   `protobuf:"bytes,4,opt,name=scene_code,json=sceneCode,proto3" json:"scene_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSkillRiskAuditTypeRequest) Reset()         { *m = SetUserSkillRiskAuditTypeRequest{} }
func (m *SetUserSkillRiskAuditTypeRequest) String() string { return proto.CompactTextString(m) }
func (*SetUserSkillRiskAuditTypeRequest) ProtoMessage()    {}
func (*SetUserSkillRiskAuditTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{25}
}
func (m *SetUserSkillRiskAuditTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSkillRiskAuditTypeRequest.Unmarshal(m, b)
}
func (m *SetUserSkillRiskAuditTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSkillRiskAuditTypeRequest.Marshal(b, m, deterministic)
}
func (dst *SetUserSkillRiskAuditTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSkillRiskAuditTypeRequest.Merge(dst, src)
}
func (m *SetUserSkillRiskAuditTypeRequest) XXX_Size() int {
	return xxx_messageInfo_SetUserSkillRiskAuditTypeRequest.Size(m)
}
func (m *SetUserSkillRiskAuditTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSkillRiskAuditTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSkillRiskAuditTypeRequest proto.InternalMessageInfo

func (m *SetUserSkillRiskAuditTypeRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserSkillRiskAuditTypeRequest) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *SetUserSkillRiskAuditTypeRequest) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *SetUserSkillRiskAuditTypeRequest) GetSceneCode() string {
	if m != nil {
		return m.SceneCode
	}
	return ""
}

type SetUserSkillRiskAuditTypeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSkillRiskAuditTypeResponse) Reset()         { *m = SetUserSkillRiskAuditTypeResponse{} }
func (m *SetUserSkillRiskAuditTypeResponse) String() string { return proto.CompactTextString(m) }
func (*SetUserSkillRiskAuditTypeResponse) ProtoMessage()    {}
func (*SetUserSkillRiskAuditTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{26}
}
func (m *SetUserSkillRiskAuditTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSkillRiskAuditTypeResponse.Unmarshal(m, b)
}
func (m *SetUserSkillRiskAuditTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSkillRiskAuditTypeResponse.Marshal(b, m, deterministic)
}
func (dst *SetUserSkillRiskAuditTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSkillRiskAuditTypeResponse.Merge(dst, src)
}
func (m *SetUserSkillRiskAuditTypeResponse) XXX_Size() int {
	return xxx_messageInfo_SetUserSkillRiskAuditTypeResponse.Size(m)
}
func (m *SetUserSkillRiskAuditTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSkillRiskAuditTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSkillRiskAuditTypeResponse proto.InternalMessageInfo

// 获取用户当前技能信息
type GetUserCurrentSkillRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	WithUrlPrefix        bool     `protobuf:"varint,2,opt,name=with_url_prefix,json=withUrlPrefix,proto3" json:"with_url_prefix,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCurrentSkillRequest) Reset()         { *m = GetUserCurrentSkillRequest{} }
func (m *GetUserCurrentSkillRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserCurrentSkillRequest) ProtoMessage()    {}
func (*GetUserCurrentSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{27}
}
func (m *GetUserCurrentSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCurrentSkillRequest.Unmarshal(m, b)
}
func (m *GetUserCurrentSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCurrentSkillRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserCurrentSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCurrentSkillRequest.Merge(dst, src)
}
func (m *GetUserCurrentSkillRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserCurrentSkillRequest.Size(m)
}
func (m *GetUserCurrentSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCurrentSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCurrentSkillRequest proto.InternalMessageInfo

func (m *GetUserCurrentSkillRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCurrentSkillRequest) GetWithUrlPrefix() bool {
	if m != nil {
		return m.WithUrlPrefix
	}
	return false
}

type GetUserCurrentSkillResponse struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Skill                []*UserSkillInfo `protobuf:"bytes,2,rep,name=skill,proto3" json:"skill,omitempty"`
	AuditType            uint32           `protobuf:"varint,3,opt,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserCurrentSkillResponse) Reset()         { *m = GetUserCurrentSkillResponse{} }
func (m *GetUserCurrentSkillResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserCurrentSkillResponse) ProtoMessage()    {}
func (*GetUserCurrentSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{28}
}
func (m *GetUserCurrentSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCurrentSkillResponse.Unmarshal(m, b)
}
func (m *GetUserCurrentSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCurrentSkillResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserCurrentSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCurrentSkillResponse.Merge(dst, src)
}
func (m *GetUserCurrentSkillResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserCurrentSkillResponse.Size(m)
}
func (m *GetUserCurrentSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCurrentSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCurrentSkillResponse proto.InternalMessageInfo

func (m *GetUserCurrentSkillResponse) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCurrentSkillResponse) GetSkill() []*UserSkillInfo {
	if m != nil {
		return m.Skill
	}
	return nil
}

func (m *GetUserCurrentSkillResponse) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

// 测试增加技能接口
type TestAddUserSkillRequest struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Skill                *UserSkillInfo `protobuf:"bytes,2,opt,name=skill,proto3" json:"skill,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *TestAddUserSkillRequest) Reset()         { *m = TestAddUserSkillRequest{} }
func (m *TestAddUserSkillRequest) String() string { return proto.CompactTextString(m) }
func (*TestAddUserSkillRequest) ProtoMessage()    {}
func (*TestAddUserSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{29}
}
func (m *TestAddUserSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestAddUserSkillRequest.Unmarshal(m, b)
}
func (m *TestAddUserSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestAddUserSkillRequest.Marshal(b, m, deterministic)
}
func (dst *TestAddUserSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestAddUserSkillRequest.Merge(dst, src)
}
func (m *TestAddUserSkillRequest) XXX_Size() int {
	return xxx_messageInfo_TestAddUserSkillRequest.Size(m)
}
func (m *TestAddUserSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TestAddUserSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TestAddUserSkillRequest proto.InternalMessageInfo

func (m *TestAddUserSkillRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestAddUserSkillRequest) GetSkill() *UserSkillInfo {
	if m != nil {
		return m.Skill
	}
	return nil
}

type TestAddUserSkillResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestAddUserSkillResponse) Reset()         { *m = TestAddUserSkillResponse{} }
func (m *TestAddUserSkillResponse) String() string { return proto.CompactTextString(m) }
func (*TestAddUserSkillResponse) ProtoMessage()    {}
func (*TestAddUserSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{30}
}
func (m *TestAddUserSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestAddUserSkillResponse.Unmarshal(m, b)
}
func (m *TestAddUserSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestAddUserSkillResponse.Marshal(b, m, deterministic)
}
func (dst *TestAddUserSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestAddUserSkillResponse.Merge(dst, src)
}
func (m *TestAddUserSkillResponse) XXX_Size() int {
	return xxx_messageInfo_TestAddUserSkillResponse.Size(m)
}
func (m *TestAddUserSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TestAddUserSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TestAddUserSkillResponse proto.InternalMessageInfo

// 批量获取用户当前技能信息
type BatchGetUserCurrentSkillRequest struct {
	Uid                  []uint32 `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserCurrentSkillRequest) Reset()         { *m = BatchGetUserCurrentSkillRequest{} }
func (m *BatchGetUserCurrentSkillRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserCurrentSkillRequest) ProtoMessage()    {}
func (*BatchGetUserCurrentSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{31}
}
func (m *BatchGetUserCurrentSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserCurrentSkillRequest.Unmarshal(m, b)
}
func (m *BatchGetUserCurrentSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserCurrentSkillRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserCurrentSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserCurrentSkillRequest.Merge(dst, src)
}
func (m *BatchGetUserCurrentSkillRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserCurrentSkillRequest.Size(m)
}
func (m *BatchGetUserCurrentSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserCurrentSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserCurrentSkillRequest proto.InternalMessageInfo

func (m *BatchGetUserCurrentSkillRequest) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

type UserCurrentSkill struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Skill                []*UserSkillInfo `protobuf:"bytes,2,rep,name=skill,proto3" json:"skill,omitempty"`
	AuditType            uint32           `protobuf:"varint,3,opt,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserCurrentSkill) Reset()         { *m = UserCurrentSkill{} }
func (m *UserCurrentSkill) String() string { return proto.CompactTextString(m) }
func (*UserCurrentSkill) ProtoMessage()    {}
func (*UserCurrentSkill) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{32}
}
func (m *UserCurrentSkill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserCurrentSkill.Unmarshal(m, b)
}
func (m *UserCurrentSkill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserCurrentSkill.Marshal(b, m, deterministic)
}
func (dst *UserCurrentSkill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserCurrentSkill.Merge(dst, src)
}
func (m *UserCurrentSkill) XXX_Size() int {
	return xxx_messageInfo_UserCurrentSkill.Size(m)
}
func (m *UserCurrentSkill) XXX_DiscardUnknown() {
	xxx_messageInfo_UserCurrentSkill.DiscardUnknown(m)
}

var xxx_messageInfo_UserCurrentSkill proto.InternalMessageInfo

func (m *UserCurrentSkill) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserCurrentSkill) GetSkill() []*UserSkillInfo {
	if m != nil {
		return m.Skill
	}
	return nil
}

func (m *UserCurrentSkill) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

type BatchGetUserCurrentSkillResponse struct {
	List                 []*UserCurrentSkill `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetUserCurrentSkillResponse) Reset()         { *m = BatchGetUserCurrentSkillResponse{} }
func (m *BatchGetUserCurrentSkillResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserCurrentSkillResponse) ProtoMessage()    {}
func (*BatchGetUserCurrentSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{33}
}
func (m *BatchGetUserCurrentSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserCurrentSkillResponse.Unmarshal(m, b)
}
func (m *BatchGetUserCurrentSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserCurrentSkillResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserCurrentSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserCurrentSkillResponse.Merge(dst, src)
}
func (m *BatchGetUserCurrentSkillResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserCurrentSkillResponse.Size(m)
}
func (m *BatchGetUserCurrentSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserCurrentSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserCurrentSkillResponse proto.InternalMessageInfo

func (m *BatchGetUserCurrentSkillResponse) GetList() []*UserCurrentSkill {
	if m != nil {
		return m.List
	}
	return nil
}

// 修改用户技能
type ModifyUserSkillRequest struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Skill                *UserSkillInfo `protobuf:"bytes,2,opt,name=skill,proto3" json:"skill,omitempty"`
	GuildId              uint32         `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ModifyUserSkillRequest) Reset()         { *m = ModifyUserSkillRequest{} }
func (m *ModifyUserSkillRequest) String() string { return proto.CompactTextString(m) }
func (*ModifyUserSkillRequest) ProtoMessage()    {}
func (*ModifyUserSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{34}
}
func (m *ModifyUserSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyUserSkillRequest.Unmarshal(m, b)
}
func (m *ModifyUserSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyUserSkillRequest.Marshal(b, m, deterministic)
}
func (dst *ModifyUserSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyUserSkillRequest.Merge(dst, src)
}
func (m *ModifyUserSkillRequest) XXX_Size() int {
	return xxx_messageInfo_ModifyUserSkillRequest.Size(m)
}
func (m *ModifyUserSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyUserSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyUserSkillRequest proto.InternalMessageInfo

func (m *ModifyUserSkillRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ModifyUserSkillRequest) GetSkill() *UserSkillInfo {
	if m != nil {
		return m.Skill
	}
	return nil
}

func (m *ModifyUserSkillRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type ModifyUserSkillResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyUserSkillResponse) Reset()         { *m = ModifyUserSkillResponse{} }
func (m *ModifyUserSkillResponse) String() string { return proto.CompactTextString(m) }
func (*ModifyUserSkillResponse) ProtoMessage()    {}
func (*ModifyUserSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{35}
}
func (m *ModifyUserSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyUserSkillResponse.Unmarshal(m, b)
}
func (m *ModifyUserSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyUserSkillResponse.Marshal(b, m, deterministic)
}
func (dst *ModifyUserSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyUserSkillResponse.Merge(dst, src)
}
func (m *ModifyUserSkillResponse) XXX_Size() int {
	return xxx_messageInfo_ModifyUserSkillResponse.Size(m)
}
func (m *ModifyUserSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyUserSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyUserSkillResponse proto.InternalMessageInfo

type DelUserSkillRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserSkillRequest) Reset()         { *m = DelUserSkillRequest{} }
func (m *DelUserSkillRequest) String() string { return proto.CompactTextString(m) }
func (*DelUserSkillRequest) ProtoMessage()    {}
func (*DelUserSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{36}
}
func (m *DelUserSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserSkillRequest.Unmarshal(m, b)
}
func (m *DelUserSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserSkillRequest.Marshal(b, m, deterministic)
}
func (dst *DelUserSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserSkillRequest.Merge(dst, src)
}
func (m *DelUserSkillRequest) XXX_Size() int {
	return xxx_messageInfo_DelUserSkillRequest.Size(m)
}
func (m *DelUserSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserSkillRequest proto.InternalMessageInfo

func (m *DelUserSkillRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelUserSkillRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type DelUserSkillResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserSkillResponse) Reset()         { *m = DelUserSkillResponse{} }
func (m *DelUserSkillResponse) String() string { return proto.CompactTextString(m) }
func (*DelUserSkillResponse) ProtoMessage()    {}
func (*DelUserSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{37}
}
func (m *DelUserSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserSkillResponse.Unmarshal(m, b)
}
func (m *DelUserSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserSkillResponse.Marshal(b, m, deterministic)
}
func (dst *DelUserSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserSkillResponse.Merge(dst, src)
}
func (m *DelUserSkillResponse) XXX_Size() int {
	return xxx_messageInfo_DelUserSkillResponse.Size(m)
}
func (m *DelUserSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserSkillResponse proto.InternalMessageInfo

type GetUserSkillByGameIdRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	IsHost               bool     `protobuf:"varint,3,opt,name=is_host,json=isHost,proto3" json:"is_host,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSkillByGameIdRequest) Reset()         { *m = GetUserSkillByGameIdRequest{} }
func (m *GetUserSkillByGameIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserSkillByGameIdRequest) ProtoMessage()    {}
func (*GetUserSkillByGameIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{38}
}
func (m *GetUserSkillByGameIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSkillByGameIdRequest.Unmarshal(m, b)
}
func (m *GetUserSkillByGameIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSkillByGameIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserSkillByGameIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSkillByGameIdRequest.Merge(dst, src)
}
func (m *GetUserSkillByGameIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserSkillByGameIdRequest.Size(m)
}
func (m *GetUserSkillByGameIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSkillByGameIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSkillByGameIdRequest proto.InternalMessageInfo

func (m *GetUserSkillByGameIdRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserSkillByGameIdRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetUserSkillByGameIdRequest) GetIsHost() bool {
	if m != nil {
		return m.IsHost
	}
	return false
}

type GetUserSkillByGameIdResponse struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32           `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	AuditSkill           []*UserSkillInfo `protobuf:"bytes,3,rep,name=audit_skill,json=auditSkill,proto3" json:"audit_skill,omitempty"`
	CurrentSkill         *UserSkillInfo   `protobuf:"bytes,4,opt,name=current_skill,json=currentSkill,proto3" json:"current_skill,omitempty"`
	AuditType            uint32           `protobuf:"varint,5,opt,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserSkillByGameIdResponse) Reset()         { *m = GetUserSkillByGameIdResponse{} }
func (m *GetUserSkillByGameIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserSkillByGameIdResponse) ProtoMessage()    {}
func (*GetUserSkillByGameIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{39}
}
func (m *GetUserSkillByGameIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSkillByGameIdResponse.Unmarshal(m, b)
}
func (m *GetUserSkillByGameIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSkillByGameIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserSkillByGameIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSkillByGameIdResponse.Merge(dst, src)
}
func (m *GetUserSkillByGameIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserSkillByGameIdResponse.Size(m)
}
func (m *GetUserSkillByGameIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSkillByGameIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSkillByGameIdResponse proto.InternalMessageInfo

func (m *GetUserSkillByGameIdResponse) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserSkillByGameIdResponse) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetUserSkillByGameIdResponse) GetAuditSkill() []*UserSkillInfo {
	if m != nil {
		return m.AuditSkill
	}
	return nil
}

func (m *GetUserSkillByGameIdResponse) GetCurrentSkill() *UserSkillInfo {
	if m != nil {
		return m.CurrentSkill
	}
	return nil
}

func (m *GetUserSkillByGameIdResponse) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

type GetUserSkillStatusRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSkillStatusRequest) Reset()         { *m = GetUserSkillStatusRequest{} }
func (m *GetUserSkillStatusRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserSkillStatusRequest) ProtoMessage()    {}
func (*GetUserSkillStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{40}
}
func (m *GetUserSkillStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSkillStatusRequest.Unmarshal(m, b)
}
func (m *GetUserSkillStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSkillStatusRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserSkillStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSkillStatusRequest.Merge(dst, src)
}
func (m *GetUserSkillStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserSkillStatusRequest.Size(m)
}
func (m *GetUserSkillStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSkillStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSkillStatusRequest proto.InternalMessageInfo

func (m *GetUserSkillStatusRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserSkillStatusResponse struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	StatusMap            map[uint32]uint32 `protobuf:"bytes,2,rep,name=status_map,json=statusMap,proto3" json:"status_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetUserSkillStatusResponse) Reset()         { *m = GetUserSkillStatusResponse{} }
func (m *GetUserSkillStatusResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserSkillStatusResponse) ProtoMessage()    {}
func (*GetUserSkillStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{41}
}
func (m *GetUserSkillStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSkillStatusResponse.Unmarshal(m, b)
}
func (m *GetUserSkillStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSkillStatusResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserSkillStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSkillStatusResponse.Merge(dst, src)
}
func (m *GetUserSkillStatusResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserSkillStatusResponse.Size(m)
}
func (m *GetUserSkillStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSkillStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSkillStatusResponse proto.InternalMessageInfo

func (m *GetUserSkillStatusResponse) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserSkillStatusResponse) GetStatusMap() map[uint32]uint32 {
	if m != nil {
		return m.StatusMap
	}
	return nil
}

type GetUserSkillFreezeStatusRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSkillFreezeStatusRequest) Reset()         { *m = GetUserSkillFreezeStatusRequest{} }
func (m *GetUserSkillFreezeStatusRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserSkillFreezeStatusRequest) ProtoMessage()    {}
func (*GetUserSkillFreezeStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{42}
}
func (m *GetUserSkillFreezeStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSkillFreezeStatusRequest.Unmarshal(m, b)
}
func (m *GetUserSkillFreezeStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSkillFreezeStatusRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserSkillFreezeStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSkillFreezeStatusRequest.Merge(dst, src)
}
func (m *GetUserSkillFreezeStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserSkillFreezeStatusRequest.Size(m)
}
func (m *GetUserSkillFreezeStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSkillFreezeStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSkillFreezeStatusRequest proto.InternalMessageInfo

func (m *GetUserSkillFreezeStatusRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserSkillFreezeStatusResponse struct {
	GameStatusMap        map[uint32]*SkillFreezeStatus `protobuf:"bytes,1,rep,name=game_status_map,json=gameStatusMap,proto3" json:"game_status_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetUserSkillFreezeStatusResponse) Reset()         { *m = GetUserSkillFreezeStatusResponse{} }
func (m *GetUserSkillFreezeStatusResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserSkillFreezeStatusResponse) ProtoMessage()    {}
func (*GetUserSkillFreezeStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{43}
}
func (m *GetUserSkillFreezeStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSkillFreezeStatusResponse.Unmarshal(m, b)
}
func (m *GetUserSkillFreezeStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSkillFreezeStatusResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserSkillFreezeStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSkillFreezeStatusResponse.Merge(dst, src)
}
func (m *GetUserSkillFreezeStatusResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserSkillFreezeStatusResponse.Size(m)
}
func (m *GetUserSkillFreezeStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSkillFreezeStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSkillFreezeStatusResponse proto.InternalMessageInfo

func (m *GetUserSkillFreezeStatusResponse) GetGameStatusMap() map[uint32]*SkillFreezeStatus {
	if m != nil {
		return m.GameStatusMap
	}
	return nil
}

type BatGetUserSkillFreezeStatusRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetUserSkillFreezeStatusRequest) Reset()         { *m = BatGetUserSkillFreezeStatusRequest{} }
func (m *BatGetUserSkillFreezeStatusRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetUserSkillFreezeStatusRequest) ProtoMessage()    {}
func (*BatGetUserSkillFreezeStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{44}
}
func (m *BatGetUserSkillFreezeStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUserSkillFreezeStatusRequest.Unmarshal(m, b)
}
func (m *BatGetUserSkillFreezeStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUserSkillFreezeStatusRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetUserSkillFreezeStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUserSkillFreezeStatusRequest.Merge(dst, src)
}
func (m *BatGetUserSkillFreezeStatusRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetUserSkillFreezeStatusRequest.Size(m)
}
func (m *BatGetUserSkillFreezeStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUserSkillFreezeStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUserSkillFreezeStatusRequest proto.InternalMessageInfo

func (m *BatGetUserSkillFreezeStatusRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type SkillFreezeStatusMap struct {
	GameStatusMap        map[uint32]*SkillFreezeStatus `protobuf:"bytes,1,rep,name=game_status_map,json=gameStatusMap,proto3" json:"game_status_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *SkillFreezeStatusMap) Reset()         { *m = SkillFreezeStatusMap{} }
func (m *SkillFreezeStatusMap) String() string { return proto.CompactTextString(m) }
func (*SkillFreezeStatusMap) ProtoMessage()    {}
func (*SkillFreezeStatusMap) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{45}
}
func (m *SkillFreezeStatusMap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkillFreezeStatusMap.Unmarshal(m, b)
}
func (m *SkillFreezeStatusMap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkillFreezeStatusMap.Marshal(b, m, deterministic)
}
func (dst *SkillFreezeStatusMap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkillFreezeStatusMap.Merge(dst, src)
}
func (m *SkillFreezeStatusMap) XXX_Size() int {
	return xxx_messageInfo_SkillFreezeStatusMap.Size(m)
}
func (m *SkillFreezeStatusMap) XXX_DiscardUnknown() {
	xxx_messageInfo_SkillFreezeStatusMap.DiscardUnknown(m)
}

var xxx_messageInfo_SkillFreezeStatusMap proto.InternalMessageInfo

func (m *SkillFreezeStatusMap) GetGameStatusMap() map[uint32]*SkillFreezeStatus {
	if m != nil {
		return m.GameStatusMap
	}
	return nil
}

type BatGetUserSkillFreezeStatusResponse struct {
	UserMap              map[uint32]*SkillFreezeStatusMap `protobuf:"bytes,1,rep,name=user_map,json=userMap,proto3" json:"user_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *BatGetUserSkillFreezeStatusResponse) Reset()         { *m = BatGetUserSkillFreezeStatusResponse{} }
func (m *BatGetUserSkillFreezeStatusResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetUserSkillFreezeStatusResponse) ProtoMessage()    {}
func (*BatGetUserSkillFreezeStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{46}
}
func (m *BatGetUserSkillFreezeStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUserSkillFreezeStatusResponse.Unmarshal(m, b)
}
func (m *BatGetUserSkillFreezeStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUserSkillFreezeStatusResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetUserSkillFreezeStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUserSkillFreezeStatusResponse.Merge(dst, src)
}
func (m *BatGetUserSkillFreezeStatusResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetUserSkillFreezeStatusResponse.Size(m)
}
func (m *BatGetUserSkillFreezeStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUserSkillFreezeStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUserSkillFreezeStatusResponse proto.InternalMessageInfo

func (m *BatGetUserSkillFreezeStatusResponse) GetUserMap() map[uint32]*SkillFreezeStatusMap {
	if m != nil {
		return m.UserMap
	}
	return nil
}

type SkillFreezeStatus struct {
	GameId               uint32     `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	FreezeType           FreezeType `protobuf:"varint,2,opt,name=freeze_type,json=freezeType,proto3,enum=esport_skill.FreezeType" json:"freeze_type,omitempty"`
	FreezeStopTs         int64      `protobuf:"varint,3,opt,name=freeze_stop_ts,json=freezeStopTs,proto3" json:"freeze_stop_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SkillFreezeStatus) Reset()         { *m = SkillFreezeStatus{} }
func (m *SkillFreezeStatus) String() string { return proto.CompactTextString(m) }
func (*SkillFreezeStatus) ProtoMessage()    {}
func (*SkillFreezeStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{47}
}
func (m *SkillFreezeStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkillFreezeStatus.Unmarshal(m, b)
}
func (m *SkillFreezeStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkillFreezeStatus.Marshal(b, m, deterministic)
}
func (dst *SkillFreezeStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkillFreezeStatus.Merge(dst, src)
}
func (m *SkillFreezeStatus) XXX_Size() int {
	return xxx_messageInfo_SkillFreezeStatus.Size(m)
}
func (m *SkillFreezeStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_SkillFreezeStatus.DiscardUnknown(m)
}

var xxx_messageInfo_SkillFreezeStatus proto.InternalMessageInfo

func (m *SkillFreezeStatus) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SkillFreezeStatus) GetFreezeType() FreezeType {
	if m != nil {
		return m.FreezeType
	}
	return FreezeType_FREEZE_TYPE_UNFREEZE
}

func (m *SkillFreezeStatus) GetFreezeStopTs() int64 {
	if m != nil {
		return m.FreezeStopTs
	}
	return 0
}

// 分页请求游戏列表
type GetGameListRequest struct {
	GameType             GAME_TYPE `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=esport_skill.GAME_TYPE" json:"game_type,omitempty"`
	PageToken            string    `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	PageSize             uint32    `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetGameListRequest) Reset()         { *m = GetGameListRequest{} }
func (m *GetGameListRequest) String() string { return proto.CompactTextString(m) }
func (*GetGameListRequest) ProtoMessage()    {}
func (*GetGameListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{48}
}
func (m *GetGameListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameListRequest.Unmarshal(m, b)
}
func (m *GetGameListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameListRequest.Marshal(b, m, deterministic)
}
func (dst *GetGameListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameListRequest.Merge(dst, src)
}
func (m *GetGameListRequest) XXX_Size() int {
	return xxx_messageInfo_GetGameListRequest.Size(m)
}
func (m *GetGameListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameListRequest proto.InternalMessageInfo

func (m *GetGameListRequest) GetGameType() GAME_TYPE {
	if m != nil {
		return m.GameType
	}
	return GAME_TYPE_GAME_TYPE_INVALID
}

func (m *GetGameListRequest) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

func (m *GetGameListRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GameItem struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	GameIcon             string   `protobuf:"bytes,3,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameItem) Reset()         { *m = GameItem{} }
func (m *GameItem) String() string { return proto.CompactTextString(m) }
func (*GameItem) ProtoMessage()    {}
func (*GameItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{49}
}
func (m *GameItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameItem.Unmarshal(m, b)
}
func (m *GameItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameItem.Marshal(b, m, deterministic)
}
func (dst *GameItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameItem.Merge(dst, src)
}
func (m *GameItem) XXX_Size() int {
	return xxx_messageInfo_GameItem.Size(m)
}
func (m *GameItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameItem proto.InternalMessageInfo

func (m *GameItem) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameItem) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GameItem) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

type GetGameListResponse struct {
	ItemList             []*GameItem `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	NextPageToken        string      `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGameListResponse) Reset()         { *m = GetGameListResponse{} }
func (m *GetGameListResponse) String() string { return proto.CompactTextString(m) }
func (*GetGameListResponse) ProtoMessage()    {}
func (*GetGameListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{50}
}
func (m *GetGameListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameListResponse.Unmarshal(m, b)
}
func (m *GetGameListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameListResponse.Marshal(b, m, deterministic)
}
func (dst *GetGameListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameListResponse.Merge(dst, src)
}
func (m *GetGameListResponse) XXX_Size() int {
	return xxx_messageInfo_GetGameListResponse.Size(m)
}
func (m *GetGameListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameListResponse proto.InternalMessageInfo

func (m *GetGameListResponse) GetItemList() []*GameItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *GetGameListResponse) GetNextPageToken() string {
	if m != nil {
		return m.NextPageToken
	}
	return ""
}

// 获取电竞指导开关
type GetSwitchRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSwitchRequest) Reset()         { *m = GetSwitchRequest{} }
func (m *GetSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*GetSwitchRequest) ProtoMessage()    {}
func (*GetSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{51}
}
func (m *GetSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSwitchRequest.Unmarshal(m, b)
}
func (m *GetSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *GetSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSwitchRequest.Merge(dst, src)
}
func (m *GetSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_GetSwitchRequest.Size(m)
}
func (m *GetSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSwitchRequest proto.InternalMessageInfo

func (m *GetSwitchRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSwitchResponse struct {
	SwitchStatus         *SwitchStatus `protobuf:"bytes,1,opt,name=switch_status,json=switchStatus,proto3" json:"switch_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSwitchResponse) Reset()         { *m = GetSwitchResponse{} }
func (m *GetSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*GetSwitchResponse) ProtoMessage()    {}
func (*GetSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{52}
}
func (m *GetSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSwitchResponse.Unmarshal(m, b)
}
func (m *GetSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *GetSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSwitchResponse.Merge(dst, src)
}
func (m *GetSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_GetSwitchResponse.Size(m)
}
func (m *GetSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSwitchResponse proto.InternalMessageInfo

func (m *GetSwitchResponse) GetSwitchStatus() *SwitchStatus {
	if m != nil {
		return m.SwitchStatus
	}
	return nil
}

type SwitchStatus struct {
	MainSwitchStatus     EsportSwitchStatus `protobuf:"varint,1,opt,name=main_switch_status,json=mainSwitchStatus,proto3,enum=esport_skill.EsportSwitchStatus" json:"main_switch_status,omitempty"`
	HomepageSwitchStatus EsportSwitchStatus `protobuf:"varint,2,opt,name=homepage_switch_status,json=homepageSwitchStatus,proto3,enum=esport_skill.EsportSwitchStatus" json:"homepage_switch_status,omitempty"`
	AppealSwitchStatus   EsportSwitchStatus `protobuf:"varint,3,opt,name=appeal_switch_status,json=appealSwitchStatus,proto3,enum=esport_skill.EsportSwitchStatus" json:"appeal_switch_status,omitempty"`
	SidebarSwitchStatus  EsportSwitchStatus `protobuf:"varint,4,opt,name=sidebar_switch_status,json=sidebarSwitchStatus,proto3,enum=esport_skill.EsportSwitchStatus" json:"sidebar_switch_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SwitchStatus) Reset()         { *m = SwitchStatus{} }
func (m *SwitchStatus) String() string { return proto.CompactTextString(m) }
func (*SwitchStatus) ProtoMessage()    {}
func (*SwitchStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{53}
}
func (m *SwitchStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchStatus.Unmarshal(m, b)
}
func (m *SwitchStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchStatus.Marshal(b, m, deterministic)
}
func (dst *SwitchStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchStatus.Merge(dst, src)
}
func (m *SwitchStatus) XXX_Size() int {
	return xxx_messageInfo_SwitchStatus.Size(m)
}
func (m *SwitchStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchStatus.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchStatus proto.InternalMessageInfo

func (m *SwitchStatus) GetMainSwitchStatus() EsportSwitchStatus {
	if m != nil {
		return m.MainSwitchStatus
	}
	return EsportSwitchStatus_SWITCH_STATUS_UNSPECIFIED
}

func (m *SwitchStatus) GetHomepageSwitchStatus() EsportSwitchStatus {
	if m != nil {
		return m.HomepageSwitchStatus
	}
	return EsportSwitchStatus_SWITCH_STATUS_UNSPECIFIED
}

func (m *SwitchStatus) GetAppealSwitchStatus() EsportSwitchStatus {
	if m != nil {
		return m.AppealSwitchStatus
	}
	return EsportSwitchStatus_SWITCH_STATUS_UNSPECIFIED
}

func (m *SwitchStatus) GetSidebarSwitchStatus() EsportSwitchStatus {
	if m != nil {
		return m.SidebarSwitchStatus
	}
	return EsportSwitchStatus_SWITCH_STATUS_UNSPECIFIED
}

// 获取电竞指导开关
type SetSwitchRequest struct {
	SwitchStatus         *SwitchStatus `protobuf:"bytes,1,opt,name=switch_status,json=switchStatus,proto3" json:"switch_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetSwitchRequest) Reset()         { *m = SetSwitchRequest{} }
func (m *SetSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*SetSwitchRequest) ProtoMessage()    {}
func (*SetSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{54}
}
func (m *SetSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSwitchRequest.Unmarshal(m, b)
}
func (m *SetSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSwitchRequest.Merge(dst, src)
}
func (m *SetSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetSwitchRequest.Size(m)
}
func (m *SetSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetSwitchRequest proto.InternalMessageInfo

func (m *SetSwitchRequest) GetSwitchStatus() *SwitchStatus {
	if m != nil {
		return m.SwitchStatus
	}
	return nil
}

type SetSwitchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSwitchResponse) Reset()         { *m = SetSwitchResponse{} }
func (m *SetSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*SetSwitchResponse) ProtoMessage()    {}
func (*SetSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{55}
}
func (m *SetSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSwitchResponse.Unmarshal(m, b)
}
func (m *SetSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSwitchResponse.Merge(dst, src)
}
func (m *SetSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetSwitchResponse.Size(m)
}
func (m *SetSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetSwitchResponse proto.InternalMessageInfo

type GetGameDetailByIdRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameDetailByIdRequest) Reset()         { *m = GetGameDetailByIdRequest{} }
func (m *GetGameDetailByIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetGameDetailByIdRequest) ProtoMessage()    {}
func (*GetGameDetailByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{56}
}
func (m *GetGameDetailByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameDetailByIdRequest.Unmarshal(m, b)
}
func (m *GetGameDetailByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameDetailByIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetGameDetailByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameDetailByIdRequest.Merge(dst, src)
}
func (m *GetGameDetailByIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetGameDetailByIdRequest.Size(m)
}
func (m *GetGameDetailByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameDetailByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameDetailByIdRequest proto.InternalMessageInfo

func (m *GetGameDetailByIdRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetGameDetailByIdResponse struct {
	Config               *EsportGameConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	MinimumPrice         uint32            `protobuf:"varint,2,opt,name=minimum_price,json=minimumPrice,proto3" json:"minimum_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetGameDetailByIdResponse) Reset()         { *m = GetGameDetailByIdResponse{} }
func (m *GetGameDetailByIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetGameDetailByIdResponse) ProtoMessage()    {}
func (*GetGameDetailByIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{57}
}
func (m *GetGameDetailByIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameDetailByIdResponse.Unmarshal(m, b)
}
func (m *GetGameDetailByIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameDetailByIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetGameDetailByIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameDetailByIdResponse.Merge(dst, src)
}
func (m *GetGameDetailByIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetGameDetailByIdResponse.Size(m)
}
func (m *GetGameDetailByIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameDetailByIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameDetailByIdResponse proto.InternalMessageInfo

func (m *GetGameDetailByIdResponse) GetConfig() *EsportGameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

func (m *GetGameDetailByIdResponse) GetMinimumPrice() uint32 {
	if m != nil {
		return m.MinimumPrice
	}
	return 0
}

type GetGameDetailByIdsRequest struct {
	GameIds              []uint32 `protobuf:"varint,1,rep,packed,name=game_ids,json=gameIds,proto3" json:"game_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameDetailByIdsRequest) Reset()         { *m = GetGameDetailByIdsRequest{} }
func (m *GetGameDetailByIdsRequest) String() string { return proto.CompactTextString(m) }
func (*GetGameDetailByIdsRequest) ProtoMessage()    {}
func (*GetGameDetailByIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{58}
}
func (m *GetGameDetailByIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameDetailByIdsRequest.Unmarshal(m, b)
}
func (m *GetGameDetailByIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameDetailByIdsRequest.Marshal(b, m, deterministic)
}
func (dst *GetGameDetailByIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameDetailByIdsRequest.Merge(dst, src)
}
func (m *GetGameDetailByIdsRequest) XXX_Size() int {
	return xxx_messageInfo_GetGameDetailByIdsRequest.Size(m)
}
func (m *GetGameDetailByIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameDetailByIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameDetailByIdsRequest proto.InternalMessageInfo

func (m *GetGameDetailByIdsRequest) GetGameIds() []uint32 {
	if m != nil {
		return m.GameIds
	}
	return nil
}

type GetGameDetailByIdsResponse struct {
	ConfigList           []*EsportGameConfig `protobuf:"bytes,1,rep,name=config_list,json=configList,proto3" json:"config_list,omitempty"`
	MinimumPrice         uint32              `protobuf:"varint,2,opt,name=minimum_price,json=minimumPrice,proto3" json:"minimum_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetGameDetailByIdsResponse) Reset()         { *m = GetGameDetailByIdsResponse{} }
func (m *GetGameDetailByIdsResponse) String() string { return proto.CompactTextString(m) }
func (*GetGameDetailByIdsResponse) ProtoMessage()    {}
func (*GetGameDetailByIdsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{59}
}
func (m *GetGameDetailByIdsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameDetailByIdsResponse.Unmarshal(m, b)
}
func (m *GetGameDetailByIdsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameDetailByIdsResponse.Marshal(b, m, deterministic)
}
func (dst *GetGameDetailByIdsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameDetailByIdsResponse.Merge(dst, src)
}
func (m *GetGameDetailByIdsResponse) XXX_Size() int {
	return xxx_messageInfo_GetGameDetailByIdsResponse.Size(m)
}
func (m *GetGameDetailByIdsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameDetailByIdsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameDetailByIdsResponse proto.InternalMessageInfo

func (m *GetGameDetailByIdsResponse) GetConfigList() []*EsportGameConfig {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

func (m *GetGameDetailByIdsResponse) GetMinimumPrice() uint32 {
	if m != nil {
		return m.MinimumPrice
	}
	return 0
}

type GetAllGameSimpleInfoRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllGameSimpleInfoRequest) Reset()         { *m = GetAllGameSimpleInfoRequest{} }
func (m *GetAllGameSimpleInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetAllGameSimpleInfoRequest) ProtoMessage()    {}
func (*GetAllGameSimpleInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{60}
}
func (m *GetAllGameSimpleInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllGameSimpleInfoRequest.Unmarshal(m, b)
}
func (m *GetAllGameSimpleInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllGameSimpleInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetAllGameSimpleInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllGameSimpleInfoRequest.Merge(dst, src)
}
func (m *GetAllGameSimpleInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetAllGameSimpleInfoRequest.Size(m)
}
func (m *GetAllGameSimpleInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllGameSimpleInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllGameSimpleInfoRequest proto.InternalMessageInfo

// 获取最低价格
type GetMinimumPriceRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMinimumPriceRequest) Reset()         { *m = GetMinimumPriceRequest{} }
func (m *GetMinimumPriceRequest) String() string { return proto.CompactTextString(m) }
func (*GetMinimumPriceRequest) ProtoMessage()    {}
func (*GetMinimumPriceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{61}
}
func (m *GetMinimumPriceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMinimumPriceRequest.Unmarshal(m, b)
}
func (m *GetMinimumPriceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMinimumPriceRequest.Marshal(b, m, deterministic)
}
func (dst *GetMinimumPriceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMinimumPriceRequest.Merge(dst, src)
}
func (m *GetMinimumPriceRequest) XXX_Size() int {
	return xxx_messageInfo_GetMinimumPriceRequest.Size(m)
}
func (m *GetMinimumPriceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMinimumPriceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMinimumPriceRequest proto.InternalMessageInfo

type GetMinimumPriceResponse struct {
	MinimumPrice         uint32   `protobuf:"varint,1,opt,name=minimum_price,json=minimumPrice,proto3" json:"minimum_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMinimumPriceResponse) Reset()         { *m = GetMinimumPriceResponse{} }
func (m *GetMinimumPriceResponse) String() string { return proto.CompactTextString(m) }
func (*GetMinimumPriceResponse) ProtoMessage()    {}
func (*GetMinimumPriceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{62}
}
func (m *GetMinimumPriceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMinimumPriceResponse.Unmarshal(m, b)
}
func (m *GetMinimumPriceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMinimumPriceResponse.Marshal(b, m, deterministic)
}
func (dst *GetMinimumPriceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMinimumPriceResponse.Merge(dst, src)
}
func (m *GetMinimumPriceResponse) XXX_Size() int {
	return xxx_messageInfo_GetMinimumPriceResponse.Size(m)
}
func (m *GetMinimumPriceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMinimumPriceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMinimumPriceResponse proto.InternalMessageInfo

func (m *GetMinimumPriceResponse) GetMinimumPrice() uint32 {
	if m != nil {
		return m.MinimumPrice
	}
	return 0
}

type SimpleGameInfo struct {
	GameId               uint32    `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string    `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	GameIcon             string    `protobuf:"bytes,3,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon,omitempty"`
	Rank                 float32   `protobuf:"fixed32,4,opt,name=rank,proto3" json:"rank,omitempty"`
	GameType             GAME_TYPE `protobuf:"varint,5,opt,name=game_type,json=gameType,proto3,enum=esport_skill.GAME_TYPE" json:"game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SimpleGameInfo) Reset()         { *m = SimpleGameInfo{} }
func (m *SimpleGameInfo) String() string { return proto.CompactTextString(m) }
func (*SimpleGameInfo) ProtoMessage()    {}
func (*SimpleGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{63}
}
func (m *SimpleGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleGameInfo.Unmarshal(m, b)
}
func (m *SimpleGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleGameInfo.Marshal(b, m, deterministic)
}
func (dst *SimpleGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleGameInfo.Merge(dst, src)
}
func (m *SimpleGameInfo) XXX_Size() int {
	return xxx_messageInfo_SimpleGameInfo.Size(m)
}
func (m *SimpleGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleGameInfo proto.InternalMessageInfo

func (m *SimpleGameInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SimpleGameInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *SimpleGameInfo) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

func (m *SimpleGameInfo) GetRank() float32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *SimpleGameInfo) GetGameType() GAME_TYPE {
	if m != nil {
		return m.GameType
	}
	return GAME_TYPE_GAME_TYPE_INVALID
}

type GetAllGameSimpleInfoResponse struct {
	GameList             []*SimpleGameInfo `protobuf:"bytes,1,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAllGameSimpleInfoResponse) Reset()         { *m = GetAllGameSimpleInfoResponse{} }
func (m *GetAllGameSimpleInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetAllGameSimpleInfoResponse) ProtoMessage()    {}
func (*GetAllGameSimpleInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{64}
}
func (m *GetAllGameSimpleInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllGameSimpleInfoResponse.Unmarshal(m, b)
}
func (m *GetAllGameSimpleInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllGameSimpleInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetAllGameSimpleInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllGameSimpleInfoResponse.Merge(dst, src)
}
func (m *GetAllGameSimpleInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetAllGameSimpleInfoResponse.Size(m)
}
func (m *GetAllGameSimpleInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllGameSimpleInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllGameSimpleInfoResponse proto.InternalMessageInfo

func (m *GetAllGameSimpleInfoResponse) GetGameList() []*SimpleGameInfo {
	if m != nil {
		return m.GameList
	}
	return nil
}

type BatchGetGameInfoByNamesRequest struct {
	NameList             []string `protobuf:"bytes,1,rep,name=name_list,json=nameList,proto3" json:"name_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGameInfoByNamesRequest) Reset()         { *m = BatchGetGameInfoByNamesRequest{} }
func (m *BatchGetGameInfoByNamesRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetGameInfoByNamesRequest) ProtoMessage()    {}
func (*BatchGetGameInfoByNamesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{65}
}
func (m *BatchGetGameInfoByNamesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGameInfoByNamesRequest.Unmarshal(m, b)
}
func (m *BatchGetGameInfoByNamesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGameInfoByNamesRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetGameInfoByNamesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGameInfoByNamesRequest.Merge(dst, src)
}
func (m *BatchGetGameInfoByNamesRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetGameInfoByNamesRequest.Size(m)
}
func (m *BatchGetGameInfoByNamesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGameInfoByNamesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGameInfoByNamesRequest proto.InternalMessageInfo

func (m *BatchGetGameInfoByNamesRequest) GetNameList() []string {
	if m != nil {
		return m.NameList
	}
	return nil
}

type BatchGetGameInfoByNamesResponse struct {
	GameList             []*SimpleGameInfo `protobuf:"bytes,1,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetGameInfoByNamesResponse) Reset()         { *m = BatchGetGameInfoByNamesResponse{} }
func (m *BatchGetGameInfoByNamesResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetGameInfoByNamesResponse) ProtoMessage()    {}
func (*BatchGetGameInfoByNamesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{66}
}
func (m *BatchGetGameInfoByNamesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGameInfoByNamesResponse.Unmarshal(m, b)
}
func (m *BatchGetGameInfoByNamesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGameInfoByNamesResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetGameInfoByNamesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGameInfoByNamesResponse.Merge(dst, src)
}
func (m *BatchGetGameInfoByNamesResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetGameInfoByNamesResponse.Size(m)
}
func (m *BatchGetGameInfoByNamesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGameInfoByNamesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGameInfoByNamesResponse proto.InternalMessageInfo

func (m *BatchGetGameInfoByNamesResponse) GetGameList() []*SimpleGameInfo {
	if m != nil {
		return m.GameList
	}
	return nil
}

// 获取用户知名选手信息
type BatchGetUserRenownedInfoRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserRenownedInfoRequest) Reset()         { *m = BatchGetUserRenownedInfoRequest{} }
func (m *BatchGetUserRenownedInfoRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserRenownedInfoRequest) ProtoMessage()    {}
func (*BatchGetUserRenownedInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{67}
}
func (m *BatchGetUserRenownedInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserRenownedInfoRequest.Unmarshal(m, b)
}
func (m *BatchGetUserRenownedInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserRenownedInfoRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserRenownedInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserRenownedInfoRequest.Merge(dst, src)
}
func (m *BatchGetUserRenownedInfoRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserRenownedInfoRequest.Size(m)
}
func (m *BatchGetUserRenownedInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserRenownedInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserRenownedInfoRequest proto.InternalMessageInfo

func (m *BatchGetUserRenownedInfoRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *BatchGetUserRenownedInfoRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 用户知名选手信息
type BatchGetUserRenownedInfoResponse struct {
	UserRenownedInfoMap  map[uint32]*BatchGetUserRenownedInfoResponse_UserRenownedInfo `protobuf:"bytes,1,rep,name=user_renowned_info_map,json=userRenownedInfoMap,proto3" json:"user_renowned_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                      `json:"-"`
	XXX_unrecognized     []byte                                                        `json:"-"`
	XXX_sizecache        int32                                                         `json:"-"`
}

func (m *BatchGetUserRenownedInfoResponse) Reset()         { *m = BatchGetUserRenownedInfoResponse{} }
func (m *BatchGetUserRenownedInfoResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserRenownedInfoResponse) ProtoMessage()    {}
func (*BatchGetUserRenownedInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{68}
}
func (m *BatchGetUserRenownedInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserRenownedInfoResponse.Unmarshal(m, b)
}
func (m *BatchGetUserRenownedInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserRenownedInfoResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserRenownedInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserRenownedInfoResponse.Merge(dst, src)
}
func (m *BatchGetUserRenownedInfoResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserRenownedInfoResponse.Size(m)
}
func (m *BatchGetUserRenownedInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserRenownedInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserRenownedInfoResponse proto.InternalMessageInfo

func (m *BatchGetUserRenownedInfoResponse) GetUserRenownedInfoMap() map[uint32]*BatchGetUserRenownedInfoResponse_UserRenownedInfo {
	if m != nil {
		return m.UserRenownedInfoMap
	}
	return nil
}

type BatchGetUserRenownedInfoResponse_UserRenownedInfo struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserRenownedInfoResponse_UserRenownedInfo) Reset() {
	*m = BatchGetUserRenownedInfoResponse_UserRenownedInfo{}
}
func (m *BatchGetUserRenownedInfoResponse_UserRenownedInfo) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetUserRenownedInfoResponse_UserRenownedInfo) ProtoMessage() {}
func (*BatchGetUserRenownedInfoResponse_UserRenownedInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{68, 0}
}
func (m *BatchGetUserRenownedInfoResponse_UserRenownedInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserRenownedInfoResponse_UserRenownedInfo.Unmarshal(m, b)
}
func (m *BatchGetUserRenownedInfoResponse_UserRenownedInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserRenownedInfoResponse_UserRenownedInfo.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserRenownedInfoResponse_UserRenownedInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserRenownedInfoResponse_UserRenownedInfo.Merge(dst, src)
}
func (m *BatchGetUserRenownedInfoResponse_UserRenownedInfo) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserRenownedInfoResponse_UserRenownedInfo.Size(m)
}
func (m *BatchGetUserRenownedInfoResponse_UserRenownedInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserRenownedInfoResponse_UserRenownedInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserRenownedInfoResponse_UserRenownedInfo proto.InternalMessageInfo

// LabelInfo 标识信息
type LabelInfo struct {
	Id                   uint32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	LabelType            LabelType `protobuf:"varint,2,opt,name=label_type,json=labelType,proto3,enum=esport_skill.LabelType" json:"label_type,omitempty"`
	LabelImage           string    `protobuf:"bytes,3,opt,name=label_image,json=labelImage,proto3" json:"label_image,omitempty"`
	HasPricing           bool      `protobuf:"varint,4,opt,name=has_pricing,json=hasPricing,proto3" json:"has_pricing,omitempty"`
	PricingAmount        uint32    `protobuf:"varint,5,opt,name=pricing_amount,json=pricingAmount,proto3" json:"pricing_amount,omitempty"`
	ApplicableLevel      uint32    `protobuf:"varint,6,opt,name=applicable_level,json=applicableLevel,proto3" json:"applicable_level,omitempty"`
	DisplayOrder         uint32    `protobuf:"varint,7,opt,name=display_order,json=displayOrder,proto3" json:"display_order,omitempty"`
	LabelDescription     string    `protobuf:"bytes,8,opt,name=label_description,json=labelDescription,proto3" json:"label_description,omitempty"`
	LabelRequirements    string    `protobuf:"bytes,9,opt,name=label_requirements,json=labelRequirements,proto3" json:"label_requirements,omitempty"`
	LabelName            string    `protobuf:"bytes,10,opt,name=label_name,json=labelName,proto3" json:"label_name,omitempty"`
	GameId               uint32    `protobuf:"varint,11,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	ApplyEntry           string    `protobuf:"bytes,12,opt,name=apply_entry,json=applyEntry,proto3" json:"apply_entry,omitempty"`
	GameName             string    `protobuf:"bytes,13,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *LabelInfo) Reset()         { *m = LabelInfo{} }
func (m *LabelInfo) String() string { return proto.CompactTextString(m) }
func (*LabelInfo) ProtoMessage()    {}
func (*LabelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{69}
}
func (m *LabelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LabelInfo.Unmarshal(m, b)
}
func (m *LabelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LabelInfo.Marshal(b, m, deterministic)
}
func (dst *LabelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelInfo.Merge(dst, src)
}
func (m *LabelInfo) XXX_Size() int {
	return xxx_messageInfo_LabelInfo.Size(m)
}
func (m *LabelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LabelInfo proto.InternalMessageInfo

func (m *LabelInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LabelInfo) GetLabelType() LabelType {
	if m != nil {
		return m.LabelType
	}
	return LabelType_LABEL_TYPE_UNSPECIFIED
}

func (m *LabelInfo) GetLabelImage() string {
	if m != nil {
		return m.LabelImage
	}
	return ""
}

func (m *LabelInfo) GetHasPricing() bool {
	if m != nil {
		return m.HasPricing
	}
	return false
}

func (m *LabelInfo) GetPricingAmount() uint32 {
	if m != nil {
		return m.PricingAmount
	}
	return 0
}

func (m *LabelInfo) GetApplicableLevel() uint32 {
	if m != nil {
		return m.ApplicableLevel
	}
	return 0
}

func (m *LabelInfo) GetDisplayOrder() uint32 {
	if m != nil {
		return m.DisplayOrder
	}
	return 0
}

func (m *LabelInfo) GetLabelDescription() string {
	if m != nil {
		return m.LabelDescription
	}
	return ""
}

func (m *LabelInfo) GetLabelRequirements() string {
	if m != nil {
		return m.LabelRequirements
	}
	return ""
}

func (m *LabelInfo) GetLabelName() string {
	if m != nil {
		return m.LabelName
	}
	return ""
}

func (m *LabelInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *LabelInfo) GetApplyEntry() string {
	if m != nil {
		return m.ApplyEntry
	}
	return ""
}

func (m *LabelInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

// 创建大神或技能标识
type CreateLabelRequest struct {
	LabelInfo            *LabelInfo `protobuf:"bytes,1,opt,name=label_info,json=labelInfo,proto3" json:"label_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CreateLabelRequest) Reset()         { *m = CreateLabelRequest{} }
func (m *CreateLabelRequest) String() string { return proto.CompactTextString(m) }
func (*CreateLabelRequest) ProtoMessage()    {}
func (*CreateLabelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{70}
}
func (m *CreateLabelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateLabelRequest.Unmarshal(m, b)
}
func (m *CreateLabelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateLabelRequest.Marshal(b, m, deterministic)
}
func (dst *CreateLabelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateLabelRequest.Merge(dst, src)
}
func (m *CreateLabelRequest) XXX_Size() int {
	return xxx_messageInfo_CreateLabelRequest.Size(m)
}
func (m *CreateLabelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateLabelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateLabelRequest proto.InternalMessageInfo

func (m *CreateLabelRequest) GetLabelInfo() *LabelInfo {
	if m != nil {
		return m.LabelInfo
	}
	return nil
}

// 创建标识响应
type CreateLabelResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateLabelResponse) Reset()         { *m = CreateLabelResponse{} }
func (m *CreateLabelResponse) String() string { return proto.CompactTextString(m) }
func (*CreateLabelResponse) ProtoMessage()    {}
func (*CreateLabelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{71}
}
func (m *CreateLabelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateLabelResponse.Unmarshal(m, b)
}
func (m *CreateLabelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateLabelResponse.Marshal(b, m, deterministic)
}
func (dst *CreateLabelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateLabelResponse.Merge(dst, src)
}
func (m *CreateLabelResponse) XXX_Size() int {
	return xxx_messageInfo_CreateLabelResponse.Size(m)
}
func (m *CreateLabelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateLabelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateLabelResponse proto.InternalMessageInfo

// 编辑标识配置
type EditLabelRequest struct {
	LabelInfo            *LabelInfo `protobuf:"bytes,1,opt,name=label_info,json=labelInfo,proto3" json:"label_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *EditLabelRequest) Reset()         { *m = EditLabelRequest{} }
func (m *EditLabelRequest) String() string { return proto.CompactTextString(m) }
func (*EditLabelRequest) ProtoMessage()    {}
func (*EditLabelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{72}
}
func (m *EditLabelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditLabelRequest.Unmarshal(m, b)
}
func (m *EditLabelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditLabelRequest.Marshal(b, m, deterministic)
}
func (dst *EditLabelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditLabelRequest.Merge(dst, src)
}
func (m *EditLabelRequest) XXX_Size() int {
	return xxx_messageInfo_EditLabelRequest.Size(m)
}
func (m *EditLabelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EditLabelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EditLabelRequest proto.InternalMessageInfo

func (m *EditLabelRequest) GetLabelInfo() *LabelInfo {
	if m != nil {
		return m.LabelInfo
	}
	return nil
}

// 编辑标识响应
type EditLabelResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditLabelResponse) Reset()         { *m = EditLabelResponse{} }
func (m *EditLabelResponse) String() string { return proto.CompactTextString(m) }
func (*EditLabelResponse) ProtoMessage()    {}
func (*EditLabelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{73}
}
func (m *EditLabelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditLabelResponse.Unmarshal(m, b)
}
func (m *EditLabelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditLabelResponse.Marshal(b, m, deterministic)
}
func (dst *EditLabelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditLabelResponse.Merge(dst, src)
}
func (m *EditLabelResponse) XXX_Size() int {
	return xxx_messageInfo_EditLabelResponse.Size(m)
}
func (m *EditLabelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_EditLabelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_EditLabelResponse proto.InternalMessageInfo

// 删除标识配置
type DeleteLabelRequest struct {
	LabelId              uint32   `protobuf:"varint,1,opt,name=label_id,json=labelId,proto3" json:"label_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteLabelRequest) Reset()         { *m = DeleteLabelRequest{} }
func (m *DeleteLabelRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteLabelRequest) ProtoMessage()    {}
func (*DeleteLabelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{74}
}
func (m *DeleteLabelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteLabelRequest.Unmarshal(m, b)
}
func (m *DeleteLabelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteLabelRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteLabelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteLabelRequest.Merge(dst, src)
}
func (m *DeleteLabelRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteLabelRequest.Size(m)
}
func (m *DeleteLabelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteLabelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteLabelRequest proto.InternalMessageInfo

func (m *DeleteLabelRequest) GetLabelId() uint32 {
	if m != nil {
		return m.LabelId
	}
	return 0
}

// 删除标识响应
type DeleteLabelResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteLabelResponse) Reset()         { *m = DeleteLabelResponse{} }
func (m *DeleteLabelResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteLabelResponse) ProtoMessage()    {}
func (*DeleteLabelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{75}
}
func (m *DeleteLabelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteLabelResponse.Unmarshal(m, b)
}
func (m *DeleteLabelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteLabelResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteLabelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteLabelResponse.Merge(dst, src)
}
func (m *DeleteLabelResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteLabelResponse.Size(m)
}
func (m *DeleteLabelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteLabelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteLabelResponse proto.InternalMessageInfo

// 查询标识列表
type ListLabelsRequest struct {
	PageNumber           uint32    `protobuf:"varint,1,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty"`
	PageSize             uint32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	LabelId              uint32    `protobuf:"varint,3,opt,name=label_id,json=labelId,proto3" json:"label_id,omitempty"`
	LabelType            LabelType `protobuf:"varint,4,opt,name=label_type,json=labelType,proto3,enum=esport_skill.LabelType" json:"label_type,omitempty"`
	GameId               uint32    `protobuf:"varint,5,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ListLabelsRequest) Reset()         { *m = ListLabelsRequest{} }
func (m *ListLabelsRequest) String() string { return proto.CompactTextString(m) }
func (*ListLabelsRequest) ProtoMessage()    {}
func (*ListLabelsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{76}
}
func (m *ListLabelsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListLabelsRequest.Unmarshal(m, b)
}
func (m *ListLabelsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListLabelsRequest.Marshal(b, m, deterministic)
}
func (dst *ListLabelsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListLabelsRequest.Merge(dst, src)
}
func (m *ListLabelsRequest) XXX_Size() int {
	return xxx_messageInfo_ListLabelsRequest.Size(m)
}
func (m *ListLabelsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListLabelsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListLabelsRequest proto.InternalMessageInfo

func (m *ListLabelsRequest) GetPageNumber() uint32 {
	if m != nil {
		return m.PageNumber
	}
	return 0
}

func (m *ListLabelsRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *ListLabelsRequest) GetLabelId() uint32 {
	if m != nil {
		return m.LabelId
	}
	return 0
}

func (m *ListLabelsRequest) GetLabelType() LabelType {
	if m != nil {
		return m.LabelType
	}
	return LabelType_LABEL_TYPE_UNSPECIFIED
}

func (m *ListLabelsRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

// 查询标识列表响应
type ListLabelsResponse struct {
	LabelInfoList        []*LabelInfo `protobuf:"bytes,1,rep,name=label_info_list,json=labelInfoList,proto3" json:"label_info_list,omitempty"`
	TotalCnt             uint32       `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListLabelsResponse) Reset()         { *m = ListLabelsResponse{} }
func (m *ListLabelsResponse) String() string { return proto.CompactTextString(m) }
func (*ListLabelsResponse) ProtoMessage()    {}
func (*ListLabelsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{77}
}
func (m *ListLabelsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListLabelsResponse.Unmarshal(m, b)
}
func (m *ListLabelsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListLabelsResponse.Marshal(b, m, deterministic)
}
func (dst *ListLabelsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListLabelsResponse.Merge(dst, src)
}
func (m *ListLabelsResponse) XXX_Size() int {
	return xxx_messageInfo_ListLabelsResponse.Size(m)
}
func (m *ListLabelsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListLabelsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListLabelsResponse proto.InternalMessageInfo

func (m *ListLabelsResponse) GetLabelInfoList() []*LabelInfo {
	if m != nil {
		return m.LabelInfoList
	}
	return nil
}

func (m *ListLabelsResponse) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// IssueLabelRequest 发放标识请求
type IssueLabelRequest struct {
	LabelId              uint32   `protobuf:"varint,1,opt,name=label_id,json=labelId,proto3" json:"label_id,omitempty"`
	Uid                  []uint32 `protobuf:"varint,2,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	EffectiveStartTime   int64    `protobuf:"varint,3,opt,name=effective_start_time,json=effectiveStartTime,proto3" json:"effective_start_time,omitempty"`
	EffectiveEndTime     int64    `protobuf:"varint,4,opt,name=effective_end_time,json=effectiveEndTime,proto3" json:"effective_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IssueLabelRequest) Reset()         { *m = IssueLabelRequest{} }
func (m *IssueLabelRequest) String() string { return proto.CompactTextString(m) }
func (*IssueLabelRequest) ProtoMessage()    {}
func (*IssueLabelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{78}
}
func (m *IssueLabelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssueLabelRequest.Unmarshal(m, b)
}
func (m *IssueLabelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssueLabelRequest.Marshal(b, m, deterministic)
}
func (dst *IssueLabelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssueLabelRequest.Merge(dst, src)
}
func (m *IssueLabelRequest) XXX_Size() int {
	return xxx_messageInfo_IssueLabelRequest.Size(m)
}
func (m *IssueLabelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IssueLabelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IssueLabelRequest proto.InternalMessageInfo

func (m *IssueLabelRequest) GetLabelId() uint32 {
	if m != nil {
		return m.LabelId
	}
	return 0
}

func (m *IssueLabelRequest) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *IssueLabelRequest) GetEffectiveStartTime() int64 {
	if m != nil {
		return m.EffectiveStartTime
	}
	return 0
}

func (m *IssueLabelRequest) GetEffectiveEndTime() int64 {
	if m != nil {
		return m.EffectiveEndTime
	}
	return 0
}

// IssueLabelResponse 发放标识响应
type IssueLabelResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IssueLabelResponse) Reset()         { *m = IssueLabelResponse{} }
func (m *IssueLabelResponse) String() string { return proto.CompactTextString(m) }
func (*IssueLabelResponse) ProtoMessage()    {}
func (*IssueLabelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{79}
}
func (m *IssueLabelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssueLabelResponse.Unmarshal(m, b)
}
func (m *IssueLabelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssueLabelResponse.Marshal(b, m, deterministic)
}
func (dst *IssueLabelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssueLabelResponse.Merge(dst, src)
}
func (m *IssueLabelResponse) XXX_Size() int {
	return xxx_messageInfo_IssueLabelResponse.Size(m)
}
func (m *IssueLabelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IssueLabelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IssueLabelResponse proto.InternalMessageInfo

// RevokeLabelRequest 用于回收已发放标识
type RevokeLabelRequest struct {
	LabelId              uint32   `protobuf:"varint,1,opt,name=label_id,json=labelId,proto3" json:"label_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeLabelRequest) Reset()         { *m = RevokeLabelRequest{} }
func (m *RevokeLabelRequest) String() string { return proto.CompactTextString(m) }
func (*RevokeLabelRequest) ProtoMessage()    {}
func (*RevokeLabelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{80}
}
func (m *RevokeLabelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeLabelRequest.Unmarshal(m, b)
}
func (m *RevokeLabelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeLabelRequest.Marshal(b, m, deterministic)
}
func (dst *RevokeLabelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeLabelRequest.Merge(dst, src)
}
func (m *RevokeLabelRequest) XXX_Size() int {
	return xxx_messageInfo_RevokeLabelRequest.Size(m)
}
func (m *RevokeLabelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeLabelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeLabelRequest proto.InternalMessageInfo

func (m *RevokeLabelRequest) GetLabelId() uint32 {
	if m != nil {
		return m.LabelId
	}
	return 0
}

// RevokeLabelResponse 用于回收已发放标识
type RevokeLabelResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeLabelResponse) Reset()         { *m = RevokeLabelResponse{} }
func (m *RevokeLabelResponse) String() string { return proto.CompactTextString(m) }
func (*RevokeLabelResponse) ProtoMessage()    {}
func (*RevokeLabelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{81}
}
func (m *RevokeLabelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeLabelResponse.Unmarshal(m, b)
}
func (m *RevokeLabelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeLabelResponse.Marshal(b, m, deterministic)
}
func (dst *RevokeLabelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeLabelResponse.Merge(dst, src)
}
func (m *RevokeLabelResponse) XXX_Size() int {
	return xxx_messageInfo_RevokeLabelResponse.Size(m)
}
func (m *RevokeLabelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeLabelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeLabelResponse proto.InternalMessageInfo

// QueryIssuanceRecordsRequest 用于查询发放记录列表
type QueryIssuanceRecordsRequest struct {
	PageNumber           int32          `protobuf:"varint,1,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty"`
	PageSize             int32          `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	CoachIds             []uint32       `protobuf:"varint,3,rep,packed,name=coach_ids,json=coachIds,proto3" json:"coach_ids,omitempty"`
	LabelId              uint32         `protobuf:"varint,4,opt,name=label_id,json=labelId,proto3" json:"label_id,omitempty"`
	EffectiveStartTime   int64          `protobuf:"varint,5,opt,name=effective_start_time,json=effectiveStartTime,proto3" json:"effective_start_time,omitempty"`
	EffectiveEndTime     int64          `protobuf:"varint,6,opt,name=effective_end_time,json=effectiveEndTime,proto3" json:"effective_end_time,omitempty"`
	LabelType            LabelType      `protobuf:"varint,7,opt,name=label_type,json=labelType,proto3,enum=esport_skill.LabelType" json:"label_type,omitempty"`
	Status               IssuanceStatus `protobuf:"varint,8,opt,name=status,proto3,enum=esport_skill.IssuanceStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *QueryIssuanceRecordsRequest) Reset()         { *m = QueryIssuanceRecordsRequest{} }
func (m *QueryIssuanceRecordsRequest) String() string { return proto.CompactTextString(m) }
func (*QueryIssuanceRecordsRequest) ProtoMessage()    {}
func (*QueryIssuanceRecordsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{82}
}
func (m *QueryIssuanceRecordsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryIssuanceRecordsRequest.Unmarshal(m, b)
}
func (m *QueryIssuanceRecordsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryIssuanceRecordsRequest.Marshal(b, m, deterministic)
}
func (dst *QueryIssuanceRecordsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryIssuanceRecordsRequest.Merge(dst, src)
}
func (m *QueryIssuanceRecordsRequest) XXX_Size() int {
	return xxx_messageInfo_QueryIssuanceRecordsRequest.Size(m)
}
func (m *QueryIssuanceRecordsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryIssuanceRecordsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryIssuanceRecordsRequest proto.InternalMessageInfo

func (m *QueryIssuanceRecordsRequest) GetPageNumber() int32 {
	if m != nil {
		return m.PageNumber
	}
	return 0
}

func (m *QueryIssuanceRecordsRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *QueryIssuanceRecordsRequest) GetCoachIds() []uint32 {
	if m != nil {
		return m.CoachIds
	}
	return nil
}

func (m *QueryIssuanceRecordsRequest) GetLabelId() uint32 {
	if m != nil {
		return m.LabelId
	}
	return 0
}

func (m *QueryIssuanceRecordsRequest) GetEffectiveStartTime() int64 {
	if m != nil {
		return m.EffectiveStartTime
	}
	return 0
}

func (m *QueryIssuanceRecordsRequest) GetEffectiveEndTime() int64 {
	if m != nil {
		return m.EffectiveEndTime
	}
	return 0
}

func (m *QueryIssuanceRecordsRequest) GetLabelType() LabelType {
	if m != nil {
		return m.LabelType
	}
	return LabelType_LABEL_TYPE_UNSPECIFIED
}

func (m *QueryIssuanceRecordsRequest) GetStatus() IssuanceStatus {
	if m != nil {
		return m.Status
	}
	return IssuanceStatus_ISSUANCE_STATUS_UNSPECIFIED
}

// 发放记录的数据模型。
type IssuanceRecord struct {
	Id                   uint32     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32     `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	StartTime            int64      `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64      `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	LabelInfo            *LabelInfo `protobuf:"bytes,5,opt,name=label_info,json=labelInfo,proto3" json:"label_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *IssuanceRecord) Reset()         { *m = IssuanceRecord{} }
func (m *IssuanceRecord) String() string { return proto.CompactTextString(m) }
func (*IssuanceRecord) ProtoMessage()    {}
func (*IssuanceRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{83}
}
func (m *IssuanceRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IssuanceRecord.Unmarshal(m, b)
}
func (m *IssuanceRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IssuanceRecord.Marshal(b, m, deterministic)
}
func (dst *IssuanceRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IssuanceRecord.Merge(dst, src)
}
func (m *IssuanceRecord) XXX_Size() int {
	return xxx_messageInfo_IssuanceRecord.Size(m)
}
func (m *IssuanceRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_IssuanceRecord.DiscardUnknown(m)
}

var xxx_messageInfo_IssuanceRecord proto.InternalMessageInfo

func (m *IssuanceRecord) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *IssuanceRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IssuanceRecord) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *IssuanceRecord) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *IssuanceRecord) GetLabelInfo() *LabelInfo {
	if m != nil {
		return m.LabelInfo
	}
	return nil
}

// QueryIssuanceRecordsResponse 用于查询发放记录列表
type QueryIssuanceRecordsResponse struct {
	IssuanceRecordList   []*IssuanceRecord `protobuf:"bytes,1,rep,name=issuance_record_list,json=issuanceRecordList,proto3" json:"issuance_record_list,omitempty"`
	TotalCnt             uint32            `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *QueryIssuanceRecordsResponse) Reset()         { *m = QueryIssuanceRecordsResponse{} }
func (m *QueryIssuanceRecordsResponse) String() string { return proto.CompactTextString(m) }
func (*QueryIssuanceRecordsResponse) ProtoMessage()    {}
func (*QueryIssuanceRecordsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{84}
}
func (m *QueryIssuanceRecordsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryIssuanceRecordsResponse.Unmarshal(m, b)
}
func (m *QueryIssuanceRecordsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryIssuanceRecordsResponse.Marshal(b, m, deterministic)
}
func (dst *QueryIssuanceRecordsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryIssuanceRecordsResponse.Merge(dst, src)
}
func (m *QueryIssuanceRecordsResponse) XXX_Size() int {
	return xxx_messageInfo_QueryIssuanceRecordsResponse.Size(m)
}
func (m *QueryIssuanceRecordsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryIssuanceRecordsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryIssuanceRecordsResponse proto.InternalMessageInfo

func (m *QueryIssuanceRecordsResponse) GetIssuanceRecordList() []*IssuanceRecord {
	if m != nil {
		return m.IssuanceRecordList
	}
	return nil
}

func (m *QueryIssuanceRecordsResponse) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// AddRenownedPlayerRequest 发放知名选手标识
type AddRenownedPlayerRequest struct {
	InfoList             []*AddRenownedPlayerRequest_RenownedPlayerInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                       `json:"-"`
	XXX_unrecognized     []byte                                         `json:"-"`
	XXX_sizecache        int32                                          `json:"-"`
}

func (m *AddRenownedPlayerRequest) Reset()         { *m = AddRenownedPlayerRequest{} }
func (m *AddRenownedPlayerRequest) String() string { return proto.CompactTextString(m) }
func (*AddRenownedPlayerRequest) ProtoMessage()    {}
func (*AddRenownedPlayerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{85}
}
func (m *AddRenownedPlayerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRenownedPlayerRequest.Unmarshal(m, b)
}
func (m *AddRenownedPlayerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRenownedPlayerRequest.Marshal(b, m, deterministic)
}
func (dst *AddRenownedPlayerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRenownedPlayerRequest.Merge(dst, src)
}
func (m *AddRenownedPlayerRequest) XXX_Size() int {
	return xxx_messageInfo_AddRenownedPlayerRequest.Size(m)
}
func (m *AddRenownedPlayerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRenownedPlayerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddRenownedPlayerRequest proto.InternalMessageInfo

func (m *AddRenownedPlayerRequest) GetInfoList() []*AddRenownedPlayerRequest_RenownedPlayerInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type AddRenownedPlayerRequest_RenownedPlayerInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SkillName            string   `protobuf:"bytes,2,opt,name=skill_name,json=skillName,proto3" json:"skill_name,omitempty"`
	Price                int64    `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRenownedPlayerRequest_RenownedPlayerInfo) Reset() {
	*m = AddRenownedPlayerRequest_RenownedPlayerInfo{}
}
func (m *AddRenownedPlayerRequest_RenownedPlayerInfo) String() string {
	return proto.CompactTextString(m)
}
func (*AddRenownedPlayerRequest_RenownedPlayerInfo) ProtoMessage() {}
func (*AddRenownedPlayerRequest_RenownedPlayerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{85, 0}
}
func (m *AddRenownedPlayerRequest_RenownedPlayerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRenownedPlayerRequest_RenownedPlayerInfo.Unmarshal(m, b)
}
func (m *AddRenownedPlayerRequest_RenownedPlayerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRenownedPlayerRequest_RenownedPlayerInfo.Marshal(b, m, deterministic)
}
func (dst *AddRenownedPlayerRequest_RenownedPlayerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRenownedPlayerRequest_RenownedPlayerInfo.Merge(dst, src)
}
func (m *AddRenownedPlayerRequest_RenownedPlayerInfo) XXX_Size() int {
	return xxx_messageInfo_AddRenownedPlayerRequest_RenownedPlayerInfo.Size(m)
}
func (m *AddRenownedPlayerRequest_RenownedPlayerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRenownedPlayerRequest_RenownedPlayerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AddRenownedPlayerRequest_RenownedPlayerInfo proto.InternalMessageInfo

func (m *AddRenownedPlayerRequest_RenownedPlayerInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddRenownedPlayerRequest_RenownedPlayerInfo) GetSkillName() string {
	if m != nil {
		return m.SkillName
	}
	return ""
}

func (m *AddRenownedPlayerRequest_RenownedPlayerInfo) GetPrice() int64 {
	if m != nil {
		return m.Price
	}
	return 0
}

// AddRenownedPlayerResponse 发放知名选手标识
type AddRenownedPlayerResponse struct {
	ErrMsg               string   `protobuf:"bytes,1,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRenownedPlayerResponse) Reset()         { *m = AddRenownedPlayerResponse{} }
func (m *AddRenownedPlayerResponse) String() string { return proto.CompactTextString(m) }
func (*AddRenownedPlayerResponse) ProtoMessage()    {}
func (*AddRenownedPlayerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{86}
}
func (m *AddRenownedPlayerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRenownedPlayerResponse.Unmarshal(m, b)
}
func (m *AddRenownedPlayerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRenownedPlayerResponse.Marshal(b, m, deterministic)
}
func (dst *AddRenownedPlayerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRenownedPlayerResponse.Merge(dst, src)
}
func (m *AddRenownedPlayerResponse) XXX_Size() int {
	return xxx_messageInfo_AddRenownedPlayerResponse.Size(m)
}
func (m *AddRenownedPlayerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRenownedPlayerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddRenownedPlayerResponse proto.InternalMessageInfo

func (m *AddRenownedPlayerResponse) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

// ListRenownedPlayersRequest 查询知名选手列表
type ListRenownedPlayersRequest struct {
	PageNumber           int32    `protobuf:"varint,1,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty"`
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	GameName             string   `protobuf:"bytes,4,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListRenownedPlayersRequest) Reset()         { *m = ListRenownedPlayersRequest{} }
func (m *ListRenownedPlayersRequest) String() string { return proto.CompactTextString(m) }
func (*ListRenownedPlayersRequest) ProtoMessage()    {}
func (*ListRenownedPlayersRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{87}
}
func (m *ListRenownedPlayersRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListRenownedPlayersRequest.Unmarshal(m, b)
}
func (m *ListRenownedPlayersRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListRenownedPlayersRequest.Marshal(b, m, deterministic)
}
func (dst *ListRenownedPlayersRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListRenownedPlayersRequest.Merge(dst, src)
}
func (m *ListRenownedPlayersRequest) XXX_Size() int {
	return xxx_messageInfo_ListRenownedPlayersRequest.Size(m)
}
func (m *ListRenownedPlayersRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListRenownedPlayersRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListRenownedPlayersRequest proto.InternalMessageInfo

func (m *ListRenownedPlayersRequest) GetPageNumber() int32 {
	if m != nil {
		return m.PageNumber
	}
	return 0
}

func (m *ListRenownedPlayersRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *ListRenownedPlayersRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ListRenownedPlayersRequest) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

// RenownedPlayerInfo 知名选手信息
type RenownedPlayerInfo struct {
	Id                   uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32                 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32                 `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	OrderPrice           int64                  `protobuf:"varint,4,opt,name=order_price,json=orderPrice,proto3" json:"order_price,omitempty"`
	GamePricingUnitType  GAME_PRICING_UNIT_TYPE `protobuf:"varint,5,opt,name=game_pricing_unit_type,json=gamePricingUnitType,proto3,enum=esport_skill.GAME_PRICING_UNIT_TYPE" json:"game_pricing_unit_type,omitempty"`
	GameName             string                 `protobuf:"bytes,6,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	UpdateTime           int64                  `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime           int64                  `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *RenownedPlayerInfo) Reset()         { *m = RenownedPlayerInfo{} }
func (m *RenownedPlayerInfo) String() string { return proto.CompactTextString(m) }
func (*RenownedPlayerInfo) ProtoMessage()    {}
func (*RenownedPlayerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{88}
}
func (m *RenownedPlayerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RenownedPlayerInfo.Unmarshal(m, b)
}
func (m *RenownedPlayerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RenownedPlayerInfo.Marshal(b, m, deterministic)
}
func (dst *RenownedPlayerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RenownedPlayerInfo.Merge(dst, src)
}
func (m *RenownedPlayerInfo) XXX_Size() int {
	return xxx_messageInfo_RenownedPlayerInfo.Size(m)
}
func (m *RenownedPlayerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RenownedPlayerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RenownedPlayerInfo proto.InternalMessageInfo

func (m *RenownedPlayerInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RenownedPlayerInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RenownedPlayerInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *RenownedPlayerInfo) GetOrderPrice() int64 {
	if m != nil {
		return m.OrderPrice
	}
	return 0
}

func (m *RenownedPlayerInfo) GetGamePricingUnitType() GAME_PRICING_UNIT_TYPE {
	if m != nil {
		return m.GamePricingUnitType
	}
	return GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_INVALID
}

func (m *RenownedPlayerInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *RenownedPlayerInfo) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *RenownedPlayerInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

// ListRenownedPlayersResponse 查询知名选手列表响应
type ListRenownedPlayersResponse struct {
	PlayerInfos          []*RenownedPlayerInfo `protobuf:"bytes,1,rep,name=player_infos,json=playerInfos,proto3" json:"player_infos,omitempty"`
	TotalCnt             uint32                `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ListRenownedPlayersResponse) Reset()         { *m = ListRenownedPlayersResponse{} }
func (m *ListRenownedPlayersResponse) String() string { return proto.CompactTextString(m) }
func (*ListRenownedPlayersResponse) ProtoMessage()    {}
func (*ListRenownedPlayersResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{89}
}
func (m *ListRenownedPlayersResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListRenownedPlayersResponse.Unmarshal(m, b)
}
func (m *ListRenownedPlayersResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListRenownedPlayersResponse.Marshal(b, m, deterministic)
}
func (dst *ListRenownedPlayersResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListRenownedPlayersResponse.Merge(dst, src)
}
func (m *ListRenownedPlayersResponse) XXX_Size() int {
	return xxx_messageInfo_ListRenownedPlayersResponse.Size(m)
}
func (m *ListRenownedPlayersResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListRenownedPlayersResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListRenownedPlayersResponse proto.InternalMessageInfo

func (m *ListRenownedPlayersResponse) GetPlayerInfos() []*RenownedPlayerInfo {
	if m != nil {
		return m.PlayerInfos
	}
	return nil
}

func (m *ListRenownedPlayersResponse) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// BatchRemoveRenownedPlayersRequest 批量移除知名选手
type BatchRemoveRenownedPlayersRequest struct {
	IdList               []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchRemoveRenownedPlayersRequest) Reset()         { *m = BatchRemoveRenownedPlayersRequest{} }
func (m *BatchRemoveRenownedPlayersRequest) String() string { return proto.CompactTextString(m) }
func (*BatchRemoveRenownedPlayersRequest) ProtoMessage()    {}
func (*BatchRemoveRenownedPlayersRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{90}
}
func (m *BatchRemoveRenownedPlayersRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchRemoveRenownedPlayersRequest.Unmarshal(m, b)
}
func (m *BatchRemoveRenownedPlayersRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchRemoveRenownedPlayersRequest.Marshal(b, m, deterministic)
}
func (dst *BatchRemoveRenownedPlayersRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchRemoveRenownedPlayersRequest.Merge(dst, src)
}
func (m *BatchRemoveRenownedPlayersRequest) XXX_Size() int {
	return xxx_messageInfo_BatchRemoveRenownedPlayersRequest.Size(m)
}
func (m *BatchRemoveRenownedPlayersRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchRemoveRenownedPlayersRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchRemoveRenownedPlayersRequest proto.InternalMessageInfo

func (m *BatchRemoveRenownedPlayersRequest) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

// BatchRemoveRenownedPlayersResponse 批量移除知名选手响应
type BatchRemoveRenownedPlayersResponse struct {
	ErrMsg               string   `protobuf:"bytes,1,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchRemoveRenownedPlayersResponse) Reset()         { *m = BatchRemoveRenownedPlayersResponse{} }
func (m *BatchRemoveRenownedPlayersResponse) String() string { return proto.CompactTextString(m) }
func (*BatchRemoveRenownedPlayersResponse) ProtoMessage()    {}
func (*BatchRemoveRenownedPlayersResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{91}
}
func (m *BatchRemoveRenownedPlayersResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchRemoveRenownedPlayersResponse.Unmarshal(m, b)
}
func (m *BatchRemoveRenownedPlayersResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchRemoveRenownedPlayersResponse.Marshal(b, m, deterministic)
}
func (dst *BatchRemoveRenownedPlayersResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchRemoveRenownedPlayersResponse.Merge(dst, src)
}
func (m *BatchRemoveRenownedPlayersResponse) XXX_Size() int {
	return xxx_messageInfo_BatchRemoveRenownedPlayersResponse.Size(m)
}
func (m *BatchRemoveRenownedPlayersResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchRemoveRenownedPlayersResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchRemoveRenownedPlayersResponse proto.InternalMessageInfo

func (m *BatchRemoveRenownedPlayersResponse) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

// UpdateRenownedPlayerRequest 更新知名选手信息
type UpdateRenownedPlayerRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	OrderPrice           int64    `protobuf:"varint,2,opt,name=order_price,json=orderPrice,proto3" json:"order_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateRenownedPlayerRequest) Reset()         { *m = UpdateRenownedPlayerRequest{} }
func (m *UpdateRenownedPlayerRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateRenownedPlayerRequest) ProtoMessage()    {}
func (*UpdateRenownedPlayerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{92}
}
func (m *UpdateRenownedPlayerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRenownedPlayerRequest.Unmarshal(m, b)
}
func (m *UpdateRenownedPlayerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRenownedPlayerRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateRenownedPlayerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRenownedPlayerRequest.Merge(dst, src)
}
func (m *UpdateRenownedPlayerRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateRenownedPlayerRequest.Size(m)
}
func (m *UpdateRenownedPlayerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRenownedPlayerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRenownedPlayerRequest proto.InternalMessageInfo

func (m *UpdateRenownedPlayerRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateRenownedPlayerRequest) GetOrderPrice() int64 {
	if m != nil {
		return m.OrderPrice
	}
	return 0
}

// UpdateRenownedPlayerResponse 更新知名选手信息响应
type UpdateRenownedPlayerResponse struct {
	ErrMsg               string   `protobuf:"bytes,1,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateRenownedPlayerResponse) Reset()         { *m = UpdateRenownedPlayerResponse{} }
func (m *UpdateRenownedPlayerResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateRenownedPlayerResponse) ProtoMessage()    {}
func (*UpdateRenownedPlayerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{93}
}
func (m *UpdateRenownedPlayerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRenownedPlayerResponse.Unmarshal(m, b)
}
func (m *UpdateRenownedPlayerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRenownedPlayerResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateRenownedPlayerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRenownedPlayerResponse.Merge(dst, src)
}
func (m *UpdateRenownedPlayerResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateRenownedPlayerResponse.Size(m)
}
func (m *UpdateRenownedPlayerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRenownedPlayerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRenownedPlayerResponse proto.InternalMessageInfo

func (m *UpdateRenownedPlayerResponse) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type GetBasePriceSettingRequest struct {
	CoachId              uint32   `protobuf:"varint,1,opt,name=coach_id,json=coachId,proto3" json:"coach_id,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBasePriceSettingRequest) Reset()         { *m = GetBasePriceSettingRequest{} }
func (m *GetBasePriceSettingRequest) String() string { return proto.CompactTextString(m) }
func (*GetBasePriceSettingRequest) ProtoMessage()    {}
func (*GetBasePriceSettingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{94}
}
func (m *GetBasePriceSettingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBasePriceSettingRequest.Unmarshal(m, b)
}
func (m *GetBasePriceSettingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBasePriceSettingRequest.Marshal(b, m, deterministic)
}
func (dst *GetBasePriceSettingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBasePriceSettingRequest.Merge(dst, src)
}
func (m *GetBasePriceSettingRequest) XXX_Size() int {
	return xxx_messageInfo_GetBasePriceSettingRequest.Size(m)
}
func (m *GetBasePriceSettingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBasePriceSettingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBasePriceSettingRequest proto.InternalMessageInfo

func (m *GetBasePriceSettingRequest) GetCoachId() uint32 {
	if m != nil {
		return m.CoachId
	}
	return 0
}

func (m *GetBasePriceSettingRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetBasePriceSettingResponse struct {
	Level                uint32     `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	BasePrice            *BasePrice `protobuf:"bytes,2,opt,name=base_price,json=basePrice,proto3" json:"base_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetBasePriceSettingResponse) Reset()         { *m = GetBasePriceSettingResponse{} }
func (m *GetBasePriceSettingResponse) String() string { return proto.CompactTextString(m) }
func (*GetBasePriceSettingResponse) ProtoMessage()    {}
func (*GetBasePriceSettingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{95}
}
func (m *GetBasePriceSettingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBasePriceSettingResponse.Unmarshal(m, b)
}
func (m *GetBasePriceSettingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBasePriceSettingResponse.Marshal(b, m, deterministic)
}
func (dst *GetBasePriceSettingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBasePriceSettingResponse.Merge(dst, src)
}
func (m *GetBasePriceSettingResponse) XXX_Size() int {
	return xxx_messageInfo_GetBasePriceSettingResponse.Size(m)
}
func (m *GetBasePriceSettingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBasePriceSettingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBasePriceSettingResponse proto.InternalMessageInfo

func (m *GetBasePriceSettingResponse) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetBasePriceSettingResponse) GetBasePrice() *BasePrice {
	if m != nil {
		return m.BasePrice
	}
	return nil
}

type SetBasePriceSettingRequest struct {
	CoachId              uint32   `protobuf:"varint,1,opt,name=coach_id,json=coachId,proto3" json:"coach_id,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Level                uint32   `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBasePriceSettingRequest) Reset()         { *m = SetBasePriceSettingRequest{} }
func (m *SetBasePriceSettingRequest) String() string { return proto.CompactTextString(m) }
func (*SetBasePriceSettingRequest) ProtoMessage()    {}
func (*SetBasePriceSettingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{96}
}
func (m *SetBasePriceSettingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBasePriceSettingRequest.Unmarshal(m, b)
}
func (m *SetBasePriceSettingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBasePriceSettingRequest.Marshal(b, m, deterministic)
}
func (dst *SetBasePriceSettingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBasePriceSettingRequest.Merge(dst, src)
}
func (m *SetBasePriceSettingRequest) XXX_Size() int {
	return xxx_messageInfo_SetBasePriceSettingRequest.Size(m)
}
func (m *SetBasePriceSettingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBasePriceSettingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetBasePriceSettingRequest proto.InternalMessageInfo

func (m *SetBasePriceSettingRequest) GetCoachId() uint32 {
	if m != nil {
		return m.CoachId
	}
	return 0
}

func (m *SetBasePriceSettingRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetBasePriceSettingRequest) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type SetBasePriceSettingResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBasePriceSettingResponse) Reset()         { *m = SetBasePriceSettingResponse{} }
func (m *SetBasePriceSettingResponse) String() string { return proto.CompactTextString(m) }
func (*SetBasePriceSettingResponse) ProtoMessage()    {}
func (*SetBasePriceSettingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{97}
}
func (m *SetBasePriceSettingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBasePriceSettingResponse.Unmarshal(m, b)
}
func (m *SetBasePriceSettingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBasePriceSettingResponse.Marshal(b, m, deterministic)
}
func (dst *SetBasePriceSettingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBasePriceSettingResponse.Merge(dst, src)
}
func (m *SetBasePriceSettingResponse) XXX_Size() int {
	return xxx_messageInfo_SetBasePriceSettingResponse.Size(m)
}
func (m *SetBasePriceSettingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBasePriceSettingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetBasePriceSettingResponse proto.InternalMessageInfo

type Price struct {
	Price                uint32                 `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	GamePricingUnitType  GAME_PRICING_UNIT_TYPE `protobuf:"varint,2,opt,name=game_pricing_unit_type,json=gamePricingUnitType,proto3,enum=esport_skill.GAME_PRICING_UNIT_TYPE" json:"game_pricing_unit_type,omitempty"`
	GameId               uint32                 `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CoachId              uint32                 `protobuf:"varint,4,opt,name=coach_id,json=coachId,proto3" json:"coach_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *Price) Reset()         { *m = Price{} }
func (m *Price) String() string { return proto.CompactTextString(m) }
func (*Price) ProtoMessage()    {}
func (*Price) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{98}
}
func (m *Price) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Price.Unmarshal(m, b)
}
func (m *Price) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Price.Marshal(b, m, deterministic)
}
func (dst *Price) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Price.Merge(dst, src)
}
func (m *Price) XXX_Size() int {
	return xxx_messageInfo_Price.Size(m)
}
func (m *Price) XXX_DiscardUnknown() {
	xxx_messageInfo_Price.DiscardUnknown(m)
}

var xxx_messageInfo_Price proto.InternalMessageInfo

func (m *Price) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *Price) GetGamePricingUnitType() GAME_PRICING_UNIT_TYPE {
	if m != nil {
		return m.GamePricingUnitType
	}
	return GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_INVALID
}

func (m *Price) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *Price) GetCoachId() uint32 {
	if m != nil {
		return m.CoachId
	}
	return 0
}

type CalculatePriceRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CoachIds             []uint32 `protobuf:"varint,2,rep,packed,name=coach_ids,json=coachIds,proto3" json:"coach_ids,omitempty"`
	WithCache            bool     `protobuf:"varint,3,opt,name=with_cache,json=withCache,proto3" json:"with_cache,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CalculatePriceRequest) Reset()         { *m = CalculatePriceRequest{} }
func (m *CalculatePriceRequest) String() string { return proto.CompactTextString(m) }
func (*CalculatePriceRequest) ProtoMessage()    {}
func (*CalculatePriceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{99}
}
func (m *CalculatePriceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalculatePriceRequest.Unmarshal(m, b)
}
func (m *CalculatePriceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalculatePriceRequest.Marshal(b, m, deterministic)
}
func (dst *CalculatePriceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalculatePriceRequest.Merge(dst, src)
}
func (m *CalculatePriceRequest) XXX_Size() int {
	return xxx_messageInfo_CalculatePriceRequest.Size(m)
}
func (m *CalculatePriceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CalculatePriceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CalculatePriceRequest proto.InternalMessageInfo

func (m *CalculatePriceRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CalculatePriceRequest) GetCoachIds() []uint32 {
	if m != nil {
		return m.CoachIds
	}
	return nil
}

func (m *CalculatePriceRequest) GetWithCache() bool {
	if m != nil {
		return m.WithCache
	}
	return false
}

type CalculatePriceResponse struct {
	PriceMap             map[uint32]*Price `protobuf:"bytes,1,rep,name=price_map,json=priceMap,proto3" json:"price_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CalculatePriceResponse) Reset()         { *m = CalculatePriceResponse{} }
func (m *CalculatePriceResponse) String() string { return proto.CompactTextString(m) }
func (*CalculatePriceResponse) ProtoMessage()    {}
func (*CalculatePriceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{100}
}
func (m *CalculatePriceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalculatePriceResponse.Unmarshal(m, b)
}
func (m *CalculatePriceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalculatePriceResponse.Marshal(b, m, deterministic)
}
func (dst *CalculatePriceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalculatePriceResponse.Merge(dst, src)
}
func (m *CalculatePriceResponse) XXX_Size() int {
	return xxx_messageInfo_CalculatePriceResponse.Size(m)
}
func (m *CalculatePriceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CalculatePriceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CalculatePriceResponse proto.InternalMessageInfo

func (m *CalculatePriceResponse) GetPriceMap() map[uint32]*Price {
	if m != nil {
		return m.PriceMap
	}
	return nil
}

type CalculatePriceByGamesRequest struct {
	GameIds              []uint32 `protobuf:"varint,1,rep,packed,name=game_ids,json=gameIds,proto3" json:"game_ids,omitempty"`
	CoachId              uint32   `protobuf:"varint,2,opt,name=coach_id,json=coachId,proto3" json:"coach_id,omitempty"`
	WithCache            bool     `protobuf:"varint,3,opt,name=with_cache,json=withCache,proto3" json:"with_cache,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CalculatePriceByGamesRequest) Reset()         { *m = CalculatePriceByGamesRequest{} }
func (m *CalculatePriceByGamesRequest) String() string { return proto.CompactTextString(m) }
func (*CalculatePriceByGamesRequest) ProtoMessage()    {}
func (*CalculatePriceByGamesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{101}
}
func (m *CalculatePriceByGamesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalculatePriceByGamesRequest.Unmarshal(m, b)
}
func (m *CalculatePriceByGamesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalculatePriceByGamesRequest.Marshal(b, m, deterministic)
}
func (dst *CalculatePriceByGamesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalculatePriceByGamesRequest.Merge(dst, src)
}
func (m *CalculatePriceByGamesRequest) XXX_Size() int {
	return xxx_messageInfo_CalculatePriceByGamesRequest.Size(m)
}
func (m *CalculatePriceByGamesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CalculatePriceByGamesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CalculatePriceByGamesRequest proto.InternalMessageInfo

func (m *CalculatePriceByGamesRequest) GetGameIds() []uint32 {
	if m != nil {
		return m.GameIds
	}
	return nil
}

func (m *CalculatePriceByGamesRequest) GetCoachId() uint32 {
	if m != nil {
		return m.CoachId
	}
	return 0
}

func (m *CalculatePriceByGamesRequest) GetWithCache() bool {
	if m != nil {
		return m.WithCache
	}
	return false
}

type CalculatePriceByGamesResponse struct {
	Prices               []*Price `protobuf:"bytes,1,rep,name=prices,proto3" json:"prices,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CalculatePriceByGamesResponse) Reset()         { *m = CalculatePriceByGamesResponse{} }
func (m *CalculatePriceByGamesResponse) String() string { return proto.CompactTextString(m) }
func (*CalculatePriceByGamesResponse) ProtoMessage()    {}
func (*CalculatePriceByGamesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{102}
}
func (m *CalculatePriceByGamesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CalculatePriceByGamesResponse.Unmarshal(m, b)
}
func (m *CalculatePriceByGamesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CalculatePriceByGamesResponse.Marshal(b, m, deterministic)
}
func (dst *CalculatePriceByGamesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CalculatePriceByGamesResponse.Merge(dst, src)
}
func (m *CalculatePriceByGamesResponse) XXX_Size() int {
	return xxx_messageInfo_CalculatePriceByGamesResponse.Size(m)
}
func (m *CalculatePriceByGamesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CalculatePriceByGamesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CalculatePriceByGamesResponse proto.InternalMessageInfo

func (m *CalculatePriceByGamesResponse) GetPrices() []*Price {
	if m != nil {
		return m.Prices
	}
	return nil
}

type GetCoachApplicableLabelsRequest struct {
	CoachId              uint32   `protobuf:"varint,1,opt,name=coach_id,json=coachId,proto3" json:"coach_id,omitempty"`
	GameId               []uint32 `protobuf:"varint,2,rep,packed,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachApplicableLabelsRequest) Reset()         { *m = GetCoachApplicableLabelsRequest{} }
func (m *GetCoachApplicableLabelsRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachApplicableLabelsRequest) ProtoMessage()    {}
func (*GetCoachApplicableLabelsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{103}
}
func (m *GetCoachApplicableLabelsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachApplicableLabelsRequest.Unmarshal(m, b)
}
func (m *GetCoachApplicableLabelsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachApplicableLabelsRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachApplicableLabelsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachApplicableLabelsRequest.Merge(dst, src)
}
func (m *GetCoachApplicableLabelsRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachApplicableLabelsRequest.Size(m)
}
func (m *GetCoachApplicableLabelsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachApplicableLabelsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachApplicableLabelsRequest proto.InternalMessageInfo

func (m *GetCoachApplicableLabelsRequest) GetCoachId() uint32 {
	if m != nil {
		return m.CoachId
	}
	return 0
}

func (m *GetCoachApplicableLabelsRequest) GetGameId() []uint32 {
	if m != nil {
		return m.GameId
	}
	return nil
}

type GetCoachApplicableLabelsResponse struct {
	LabelList            []*GetCoachApplicableLabelsResponse_ApplicableLabel `protobuf:"bytes,1,rep,name=label_list,json=labelList,proto3" json:"label_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                            `json:"-"`
	XXX_unrecognized     []byte                                              `json:"-"`
	XXX_sizecache        int32                                               `json:"-"`
}

func (m *GetCoachApplicableLabelsResponse) Reset()         { *m = GetCoachApplicableLabelsResponse{} }
func (m *GetCoachApplicableLabelsResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachApplicableLabelsResponse) ProtoMessage()    {}
func (*GetCoachApplicableLabelsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{104}
}
func (m *GetCoachApplicableLabelsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachApplicableLabelsResponse.Unmarshal(m, b)
}
func (m *GetCoachApplicableLabelsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachApplicableLabelsResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachApplicableLabelsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachApplicableLabelsResponse.Merge(dst, src)
}
func (m *GetCoachApplicableLabelsResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachApplicableLabelsResponse.Size(m)
}
func (m *GetCoachApplicableLabelsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachApplicableLabelsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachApplicableLabelsResponse proto.InternalMessageInfo

func (m *GetCoachApplicableLabelsResponse) GetLabelList() []*GetCoachApplicableLabelsResponse_ApplicableLabel {
	if m != nil {
		return m.LabelList
	}
	return nil
}

type GetCoachApplicableLabelsResponse_ApplicableLabel struct {
	Info                 *LabelInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	IsApplied            bool       `protobuf:"varint,2,opt,name=is_applied,json=isApplied,proto3" json:"is_applied,omitempty"`
	IsAddToPrice         bool       `protobuf:"varint,3,opt,name=is_add_to_price,json=isAddToPrice,proto3" json:"is_add_to_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetCoachApplicableLabelsResponse_ApplicableLabel) Reset() {
	*m = GetCoachApplicableLabelsResponse_ApplicableLabel{}
}
func (m *GetCoachApplicableLabelsResponse_ApplicableLabel) String() string {
	return proto.CompactTextString(m)
}
func (*GetCoachApplicableLabelsResponse_ApplicableLabel) ProtoMessage() {}
func (*GetCoachApplicableLabelsResponse_ApplicableLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{104, 0}
}
func (m *GetCoachApplicableLabelsResponse_ApplicableLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachApplicableLabelsResponse_ApplicableLabel.Unmarshal(m, b)
}
func (m *GetCoachApplicableLabelsResponse_ApplicableLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachApplicableLabelsResponse_ApplicableLabel.Marshal(b, m, deterministic)
}
func (dst *GetCoachApplicableLabelsResponse_ApplicableLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachApplicableLabelsResponse_ApplicableLabel.Merge(dst, src)
}
func (m *GetCoachApplicableLabelsResponse_ApplicableLabel) XXX_Size() int {
	return xxx_messageInfo_GetCoachApplicableLabelsResponse_ApplicableLabel.Size(m)
}
func (m *GetCoachApplicableLabelsResponse_ApplicableLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachApplicableLabelsResponse_ApplicableLabel.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachApplicableLabelsResponse_ApplicableLabel proto.InternalMessageInfo

func (m *GetCoachApplicableLabelsResponse_ApplicableLabel) GetInfo() *LabelInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetCoachApplicableLabelsResponse_ApplicableLabel) GetIsApplied() bool {
	if m != nil {
		return m.IsApplied
	}
	return false
}

func (m *GetCoachApplicableLabelsResponse_ApplicableLabel) GetIsAddToPrice() bool {
	if m != nil {
		return m.IsAddToPrice
	}
	return false
}

type SetLabelPriceSwitchRequest struct {
	CoachId              uint32   `protobuf:"varint,1,opt,name=coach_id,json=coachId,proto3" json:"coach_id,omitempty"`
	LabelId              uint32   `protobuf:"varint,2,opt,name=label_id,json=labelId,proto3" json:"label_id,omitempty"`
	TargetState          bool     `protobuf:"varint,3,opt,name=target_state,json=targetState,proto3" json:"target_state,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetLabelPriceSwitchRequest) Reset()         { *m = SetLabelPriceSwitchRequest{} }
func (m *SetLabelPriceSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*SetLabelPriceSwitchRequest) ProtoMessage()    {}
func (*SetLabelPriceSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{105}
}
func (m *SetLabelPriceSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLabelPriceSwitchRequest.Unmarshal(m, b)
}
func (m *SetLabelPriceSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLabelPriceSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetLabelPriceSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLabelPriceSwitchRequest.Merge(dst, src)
}
func (m *SetLabelPriceSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetLabelPriceSwitchRequest.Size(m)
}
func (m *SetLabelPriceSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLabelPriceSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetLabelPriceSwitchRequest proto.InternalMessageInfo

func (m *SetLabelPriceSwitchRequest) GetCoachId() uint32 {
	if m != nil {
		return m.CoachId
	}
	return 0
}

func (m *SetLabelPriceSwitchRequest) GetLabelId() uint32 {
	if m != nil {
		return m.LabelId
	}
	return 0
}

func (m *SetLabelPriceSwitchRequest) GetTargetState() bool {
	if m != nil {
		return m.TargetState
	}
	return false
}

type SetLabelPriceSwitchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetLabelPriceSwitchResponse) Reset()         { *m = SetLabelPriceSwitchResponse{} }
func (m *SetLabelPriceSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*SetLabelPriceSwitchResponse) ProtoMessage()    {}
func (*SetLabelPriceSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{106}
}
func (m *SetLabelPriceSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLabelPriceSwitchResponse.Unmarshal(m, b)
}
func (m *SetLabelPriceSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLabelPriceSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetLabelPriceSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLabelPriceSwitchResponse.Merge(dst, src)
}
func (m *SetLabelPriceSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetLabelPriceSwitchResponse.Size(m)
}
func (m *SetLabelPriceSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLabelPriceSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetLabelPriceSwitchResponse proto.InternalMessageInfo

type CheckCoachIdentityAndSkillRequest struct {
	CheckList            []*CheckCoachIdentityAndSkillRequest_CheckItem `protobuf:"bytes,1,rep,name=check_list,json=checkList,proto3" json:"check_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                       `json:"-"`
	XXX_unrecognized     []byte                                         `json:"-"`
	XXX_sizecache        int32                                          `json:"-"`
}

func (m *CheckCoachIdentityAndSkillRequest) Reset()         { *m = CheckCoachIdentityAndSkillRequest{} }
func (m *CheckCoachIdentityAndSkillRequest) String() string { return proto.CompactTextString(m) }
func (*CheckCoachIdentityAndSkillRequest) ProtoMessage()    {}
func (*CheckCoachIdentityAndSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{107}
}
func (m *CheckCoachIdentityAndSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCoachIdentityAndSkillRequest.Unmarshal(m, b)
}
func (m *CheckCoachIdentityAndSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCoachIdentityAndSkillRequest.Marshal(b, m, deterministic)
}
func (dst *CheckCoachIdentityAndSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCoachIdentityAndSkillRequest.Merge(dst, src)
}
func (m *CheckCoachIdentityAndSkillRequest) XXX_Size() int {
	return xxx_messageInfo_CheckCoachIdentityAndSkillRequest.Size(m)
}
func (m *CheckCoachIdentityAndSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCoachIdentityAndSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCoachIdentityAndSkillRequest proto.InternalMessageInfo

func (m *CheckCoachIdentityAndSkillRequest) GetCheckList() []*CheckCoachIdentityAndSkillRequest_CheckItem {
	if m != nil {
		return m.CheckList
	}
	return nil
}

type CheckCoachIdentityAndSkillRequest_CheckItem struct {
	CoachId              uint32   `protobuf:"varint,1,opt,name=coach_id,json=coachId,proto3" json:"coach_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCoachIdentityAndSkillRequest_CheckItem) Reset() {
	*m = CheckCoachIdentityAndSkillRequest_CheckItem{}
}
func (m *CheckCoachIdentityAndSkillRequest_CheckItem) String() string {
	return proto.CompactTextString(m)
}
func (*CheckCoachIdentityAndSkillRequest_CheckItem) ProtoMessage() {}
func (*CheckCoachIdentityAndSkillRequest_CheckItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{107, 0}
}
func (m *CheckCoachIdentityAndSkillRequest_CheckItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCoachIdentityAndSkillRequest_CheckItem.Unmarshal(m, b)
}
func (m *CheckCoachIdentityAndSkillRequest_CheckItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCoachIdentityAndSkillRequest_CheckItem.Marshal(b, m, deterministic)
}
func (dst *CheckCoachIdentityAndSkillRequest_CheckItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCoachIdentityAndSkillRequest_CheckItem.Merge(dst, src)
}
func (m *CheckCoachIdentityAndSkillRequest_CheckItem) XXX_Size() int {
	return xxx_messageInfo_CheckCoachIdentityAndSkillRequest_CheckItem.Size(m)
}
func (m *CheckCoachIdentityAndSkillRequest_CheckItem) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCoachIdentityAndSkillRequest_CheckItem.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCoachIdentityAndSkillRequest_CheckItem proto.InternalMessageInfo

func (m *CheckCoachIdentityAndSkillRequest_CheckItem) GetCoachId() uint32 {
	if m != nil {
		return m.CoachId
	}
	return 0
}

func (m *CheckCoachIdentityAndSkillRequest_CheckItem) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

// 检查不同过则报错
type CheckCoachIdentityAndSkillResponse struct {
	ErrMsg               string   `protobuf:"bytes,1,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCoachIdentityAndSkillResponse) Reset()         { *m = CheckCoachIdentityAndSkillResponse{} }
func (m *CheckCoachIdentityAndSkillResponse) String() string { return proto.CompactTextString(m) }
func (*CheckCoachIdentityAndSkillResponse) ProtoMessage()    {}
func (*CheckCoachIdentityAndSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{108}
}
func (m *CheckCoachIdentityAndSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCoachIdentityAndSkillResponse.Unmarshal(m, b)
}
func (m *CheckCoachIdentityAndSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCoachIdentityAndSkillResponse.Marshal(b, m, deterministic)
}
func (dst *CheckCoachIdentityAndSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCoachIdentityAndSkillResponse.Merge(dst, src)
}
func (m *CheckCoachIdentityAndSkillResponse) XXX_Size() int {
	return xxx_messageInfo_CheckCoachIdentityAndSkillResponse.Size(m)
}
func (m *CheckCoachIdentityAndSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCoachIdentityAndSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCoachIdentityAndSkillResponse proto.InternalMessageInfo

func (m *CheckCoachIdentityAndSkillResponse) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type BatchGetCoachLabelsForGameRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CoachIds             []uint32 `protobuf:"varint,2,rep,packed,name=coach_ids,json=coachIds,proto3" json:"coach_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetCoachLabelsForGameRequest) Reset()         { *m = BatchGetCoachLabelsForGameRequest{} }
func (m *BatchGetCoachLabelsForGameRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetCoachLabelsForGameRequest) ProtoMessage()    {}
func (*BatchGetCoachLabelsForGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{109}
}
func (m *BatchGetCoachLabelsForGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCoachLabelsForGameRequest.Unmarshal(m, b)
}
func (m *BatchGetCoachLabelsForGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCoachLabelsForGameRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetCoachLabelsForGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCoachLabelsForGameRequest.Merge(dst, src)
}
func (m *BatchGetCoachLabelsForGameRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetCoachLabelsForGameRequest.Size(m)
}
func (m *BatchGetCoachLabelsForGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCoachLabelsForGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCoachLabelsForGameRequest proto.InternalMessageInfo

func (m *BatchGetCoachLabelsForGameRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *BatchGetCoachLabelsForGameRequest) GetCoachIds() []uint32 {
	if m != nil {
		return m.CoachIds
	}
	return nil
}

type BatchGetCoachLabelsForGameResponse struct {
	LabelList            []*BatchGetCoachLabelsForGameResponse_CoachLabelInfo `protobuf:"bytes,1,rep,name=label_list,json=labelList,proto3" json:"label_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                             `json:"-"`
	XXX_unrecognized     []byte                                               `json:"-"`
	XXX_sizecache        int32                                                `json:"-"`
}

func (m *BatchGetCoachLabelsForGameResponse) Reset()         { *m = BatchGetCoachLabelsForGameResponse{} }
func (m *BatchGetCoachLabelsForGameResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetCoachLabelsForGameResponse) ProtoMessage()    {}
func (*BatchGetCoachLabelsForGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{110}
}
func (m *BatchGetCoachLabelsForGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCoachLabelsForGameResponse.Unmarshal(m, b)
}
func (m *BatchGetCoachLabelsForGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCoachLabelsForGameResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetCoachLabelsForGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCoachLabelsForGameResponse.Merge(dst, src)
}
func (m *BatchGetCoachLabelsForGameResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetCoachLabelsForGameResponse.Size(m)
}
func (m *BatchGetCoachLabelsForGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCoachLabelsForGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCoachLabelsForGameResponse proto.InternalMessageInfo

func (m *BatchGetCoachLabelsForGameResponse) GetLabelList() []*BatchGetCoachLabelsForGameResponse_CoachLabelInfo {
	if m != nil {
		return m.LabelList
	}
	return nil
}

type BatchGetCoachLabelsForGameResponse_LabelList struct {
	LabelList            []*LabelInfo `protobuf:"bytes,1,rep,name=label_list,json=labelList,proto3" json:"label_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchGetCoachLabelsForGameResponse_LabelList) Reset() {
	*m = BatchGetCoachLabelsForGameResponse_LabelList{}
}
func (m *BatchGetCoachLabelsForGameResponse_LabelList) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetCoachLabelsForGameResponse_LabelList) ProtoMessage() {}
func (*BatchGetCoachLabelsForGameResponse_LabelList) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{110, 0}
}
func (m *BatchGetCoachLabelsForGameResponse_LabelList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCoachLabelsForGameResponse_LabelList.Unmarshal(m, b)
}
func (m *BatchGetCoachLabelsForGameResponse_LabelList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCoachLabelsForGameResponse_LabelList.Marshal(b, m, deterministic)
}
func (dst *BatchGetCoachLabelsForGameResponse_LabelList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCoachLabelsForGameResponse_LabelList.Merge(dst, src)
}
func (m *BatchGetCoachLabelsForGameResponse_LabelList) XXX_Size() int {
	return xxx_messageInfo_BatchGetCoachLabelsForGameResponse_LabelList.Size(m)
}
func (m *BatchGetCoachLabelsForGameResponse_LabelList) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCoachLabelsForGameResponse_LabelList.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCoachLabelsForGameResponse_LabelList proto.InternalMessageInfo

func (m *BatchGetCoachLabelsForGameResponse_LabelList) GetLabelList() []*LabelInfo {
	if m != nil {
		return m.LabelList
	}
	return nil
}

type BatchGetCoachLabelsForGameResponse_CoachLabelInfo struct {
	LabelMap             map[uint32]*BatchGetCoachLabelsForGameResponse_LabelList `protobuf:"bytes,1,rep,name=label_map,json=labelMap,proto3" json:"label_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	IsRenowned           bool                                                     `protobuf:"varint,2,opt,name=is_renowned,json=isRenowned,proto3" json:"is_renowned,omitempty"`
	CoachId              uint32                                                   `protobuf:"varint,3,opt,name=coach_id,json=coachId,proto3" json:"coach_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                                 `json:"-"`
	XXX_unrecognized     []byte                                                   `json:"-"`
	XXX_sizecache        int32                                                    `json:"-"`
}

func (m *BatchGetCoachLabelsForGameResponse_CoachLabelInfo) Reset() {
	*m = BatchGetCoachLabelsForGameResponse_CoachLabelInfo{}
}
func (m *BatchGetCoachLabelsForGameResponse_CoachLabelInfo) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetCoachLabelsForGameResponse_CoachLabelInfo) ProtoMessage() {}
func (*BatchGetCoachLabelsForGameResponse_CoachLabelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{110, 1}
}
func (m *BatchGetCoachLabelsForGameResponse_CoachLabelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCoachLabelsForGameResponse_CoachLabelInfo.Unmarshal(m, b)
}
func (m *BatchGetCoachLabelsForGameResponse_CoachLabelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCoachLabelsForGameResponse_CoachLabelInfo.Marshal(b, m, deterministic)
}
func (dst *BatchGetCoachLabelsForGameResponse_CoachLabelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCoachLabelsForGameResponse_CoachLabelInfo.Merge(dst, src)
}
func (m *BatchGetCoachLabelsForGameResponse_CoachLabelInfo) XXX_Size() int {
	return xxx_messageInfo_BatchGetCoachLabelsForGameResponse_CoachLabelInfo.Size(m)
}
func (m *BatchGetCoachLabelsForGameResponse_CoachLabelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCoachLabelsForGameResponse_CoachLabelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCoachLabelsForGameResponse_CoachLabelInfo proto.InternalMessageInfo

func (m *BatchGetCoachLabelsForGameResponse_CoachLabelInfo) GetLabelMap() map[uint32]*BatchGetCoachLabelsForGameResponse_LabelList {
	if m != nil {
		return m.LabelMap
	}
	return nil
}

func (m *BatchGetCoachLabelsForGameResponse_CoachLabelInfo) GetIsRenowned() bool {
	if m != nil {
		return m.IsRenowned
	}
	return false
}

func (m *BatchGetCoachLabelsForGameResponse_CoachLabelInfo) GetCoachId() uint32 {
	if m != nil {
		return m.CoachId
	}
	return 0
}

type BatchCheckCoachHasGameRequest struct {
	CoachIds             []uint32 `protobuf:"varint,1,rep,packed,name=coach_ids,json=coachIds,proto3" json:"coach_ids,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchCheckCoachHasGameRequest) Reset()         { *m = BatchCheckCoachHasGameRequest{} }
func (m *BatchCheckCoachHasGameRequest) String() string { return proto.CompactTextString(m) }
func (*BatchCheckCoachHasGameRequest) ProtoMessage()    {}
func (*BatchCheckCoachHasGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{111}
}
func (m *BatchCheckCoachHasGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckCoachHasGameRequest.Unmarshal(m, b)
}
func (m *BatchCheckCoachHasGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckCoachHasGameRequest.Marshal(b, m, deterministic)
}
func (dst *BatchCheckCoachHasGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckCoachHasGameRequest.Merge(dst, src)
}
func (m *BatchCheckCoachHasGameRequest) XXX_Size() int {
	return xxx_messageInfo_BatchCheckCoachHasGameRequest.Size(m)
}
func (m *BatchCheckCoachHasGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckCoachHasGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckCoachHasGameRequest proto.InternalMessageInfo

func (m *BatchCheckCoachHasGameRequest) GetCoachIds() []uint32 {
	if m != nil {
		return m.CoachIds
	}
	return nil
}

func (m *BatchCheckCoachHasGameRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type BatchCheckCoachHasGameResponse struct {
	CoachHasGameMap      map[uint32]bool `protobuf:"bytes,1,rep,name=coach_has_game_map,json=coachHasGameMap,proto3" json:"coach_has_game_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchCheckCoachHasGameResponse) Reset()         { *m = BatchCheckCoachHasGameResponse{} }
func (m *BatchCheckCoachHasGameResponse) String() string { return proto.CompactTextString(m) }
func (*BatchCheckCoachHasGameResponse) ProtoMessage()    {}
func (*BatchCheckCoachHasGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{112}
}
func (m *BatchCheckCoachHasGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckCoachHasGameResponse.Unmarshal(m, b)
}
func (m *BatchCheckCoachHasGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckCoachHasGameResponse.Marshal(b, m, deterministic)
}
func (dst *BatchCheckCoachHasGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckCoachHasGameResponse.Merge(dst, src)
}
func (m *BatchCheckCoachHasGameResponse) XXX_Size() int {
	return xxx_messageInfo_BatchCheckCoachHasGameResponse.Size(m)
}
func (m *BatchCheckCoachHasGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckCoachHasGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckCoachHasGameResponse proto.InternalMessageInfo

func (m *BatchCheckCoachHasGameResponse) GetCoachHasGameMap() map[uint32]bool {
	if m != nil {
		return m.CoachHasGameMap
	}
	return nil
}

// 游戏名片选项
type GameCardInfoItem struct {
	ItemName             string   `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	ItemList             []string `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	Tips                 string   `protobuf:"bytes,3,opt,name=tips,proto3" json:"tips,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameCardInfoItem) Reset()         { *m = GameCardInfoItem{} }
func (m *GameCardInfoItem) String() string { return proto.CompactTextString(m) }
func (*GameCardInfoItem) ProtoMessage()    {}
func (*GameCardInfoItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{113}
}
func (m *GameCardInfoItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCardInfoItem.Unmarshal(m, b)
}
func (m *GameCardInfoItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCardInfoItem.Marshal(b, m, deterministic)
}
func (dst *GameCardInfoItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCardInfoItem.Merge(dst, src)
}
func (m *GameCardInfoItem) XXX_Size() int {
	return xxx_messageInfo_GameCardInfoItem.Size(m)
}
func (m *GameCardInfoItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCardInfoItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameCardInfoItem proto.InternalMessageInfo

func (m *GameCardInfoItem) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *GameCardInfoItem) GetItemList() []string {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *GameCardInfoItem) GetTips() string {
	if m != nil {
		return m.Tips
	}
	return ""
}

type GameCardConfig struct {
	GameId               uint32              `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string              `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	GameCardInfoItemList []*GameCardInfoItem `protobuf:"bytes,3,rep,name=game_card_info_item_list,json=gameCardInfoItemList,proto3" json:"game_card_info_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GameCardConfig) Reset()         { *m = GameCardConfig{} }
func (m *GameCardConfig) String() string { return proto.CompactTextString(m) }
func (*GameCardConfig) ProtoMessage()    {}
func (*GameCardConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{114}
}
func (m *GameCardConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCardConfig.Unmarshal(m, b)
}
func (m *GameCardConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCardConfig.Marshal(b, m, deterministic)
}
func (dst *GameCardConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCardConfig.Merge(dst, src)
}
func (m *GameCardConfig) XXX_Size() int {
	return xxx_messageInfo_GameCardConfig.Size(m)
}
func (m *GameCardConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCardConfig.DiscardUnknown(m)
}

var xxx_messageInfo_GameCardConfig proto.InternalMessageInfo

func (m *GameCardConfig) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameCardConfig) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GameCardConfig) GetGameCardInfoItemList() []*GameCardInfoItem {
	if m != nil {
		return m.GameCardInfoItemList
	}
	return nil
}

type GetAllGameCardConfigRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllGameCardConfigRequest) Reset()         { *m = GetAllGameCardConfigRequest{} }
func (m *GetAllGameCardConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetAllGameCardConfigRequest) ProtoMessage()    {}
func (*GetAllGameCardConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{115}
}
func (m *GetAllGameCardConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllGameCardConfigRequest.Unmarshal(m, b)
}
func (m *GetAllGameCardConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllGameCardConfigRequest.Marshal(b, m, deterministic)
}
func (dst *GetAllGameCardConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllGameCardConfigRequest.Merge(dst, src)
}
func (m *GetAllGameCardConfigRequest) XXX_Size() int {
	return xxx_messageInfo_GetAllGameCardConfigRequest.Size(m)
}
func (m *GetAllGameCardConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllGameCardConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllGameCardConfigRequest proto.InternalMessageInfo

type GetAllGameCardConfigResponse struct {
	GameCardConfigList   []*GameCardConfig `protobuf:"bytes,1,rep,name=game_card_config_list,json=gameCardConfigList,proto3" json:"game_card_config_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAllGameCardConfigResponse) Reset()         { *m = GetAllGameCardConfigResponse{} }
func (m *GetAllGameCardConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetAllGameCardConfigResponse) ProtoMessage()    {}
func (*GetAllGameCardConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{116}
}
func (m *GetAllGameCardConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllGameCardConfigResponse.Unmarshal(m, b)
}
func (m *GetAllGameCardConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllGameCardConfigResponse.Marshal(b, m, deterministic)
}
func (dst *GetAllGameCardConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllGameCardConfigResponse.Merge(dst, src)
}
func (m *GetAllGameCardConfigResponse) XXX_Size() int {
	return xxx_messageInfo_GetAllGameCardConfigResponse.Size(m)
}
func (m *GetAllGameCardConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllGameCardConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllGameCardConfigResponse proto.InternalMessageInfo

func (m *GetAllGameCardConfigResponse) GetGameCardConfigList() []*GameCardConfig {
	if m != nil {
		return m.GameCardConfigList
	}
	return nil
}

// 设置游戏包赢开关
type SetGameGuaranteeStatusRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	IsGuaranteeWin       bool     `protobuf:"varint,3,opt,name=is_guarantee_win,json=isGuaranteeWin,proto3" json:"is_guarantee_win,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameGuaranteeStatusRequest) Reset()         { *m = SetGameGuaranteeStatusRequest{} }
func (m *SetGameGuaranteeStatusRequest) String() string { return proto.CompactTextString(m) }
func (*SetGameGuaranteeStatusRequest) ProtoMessage()    {}
func (*SetGameGuaranteeStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{117}
}
func (m *SetGameGuaranteeStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameGuaranteeStatusRequest.Unmarshal(m, b)
}
func (m *SetGameGuaranteeStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameGuaranteeStatusRequest.Marshal(b, m, deterministic)
}
func (dst *SetGameGuaranteeStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameGuaranteeStatusRequest.Merge(dst, src)
}
func (m *SetGameGuaranteeStatusRequest) XXX_Size() int {
	return xxx_messageInfo_SetGameGuaranteeStatusRequest.Size(m)
}
func (m *SetGameGuaranteeStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameGuaranteeStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameGuaranteeStatusRequest proto.InternalMessageInfo

func (m *SetGameGuaranteeStatusRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetGameGuaranteeStatusRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetGameGuaranteeStatusRequest) GetIsGuaranteeWin() bool {
	if m != nil {
		return m.IsGuaranteeWin
	}
	return false
}

type SetGameGuaranteeStatusResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameGuaranteeStatusResponse) Reset()         { *m = SetGameGuaranteeStatusResponse{} }
func (m *SetGameGuaranteeStatusResponse) String() string { return proto.CompactTextString(m) }
func (*SetGameGuaranteeStatusResponse) ProtoMessage()    {}
func (*SetGameGuaranteeStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{118}
}
func (m *SetGameGuaranteeStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameGuaranteeStatusResponse.Unmarshal(m, b)
}
func (m *SetGameGuaranteeStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameGuaranteeStatusResponse.Marshal(b, m, deterministic)
}
func (dst *SetGameGuaranteeStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameGuaranteeStatusResponse.Merge(dst, src)
}
func (m *SetGameGuaranteeStatusResponse) XXX_Size() int {
	return xxx_messageInfo_SetGameGuaranteeStatusResponse.Size(m)
}
func (m *SetGameGuaranteeStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameGuaranteeStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameGuaranteeStatusResponse proto.InternalMessageInfo

type HandleGameUpdateRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleGameUpdateRequest) Reset()         { *m = HandleGameUpdateRequest{} }
func (m *HandleGameUpdateRequest) String() string { return proto.CompactTextString(m) }
func (*HandleGameUpdateRequest) ProtoMessage()    {}
func (*HandleGameUpdateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{119}
}
func (m *HandleGameUpdateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleGameUpdateRequest.Unmarshal(m, b)
}
func (m *HandleGameUpdateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleGameUpdateRequest.Marshal(b, m, deterministic)
}
func (dst *HandleGameUpdateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleGameUpdateRequest.Merge(dst, src)
}
func (m *HandleGameUpdateRequest) XXX_Size() int {
	return xxx_messageInfo_HandleGameUpdateRequest.Size(m)
}
func (m *HandleGameUpdateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleGameUpdateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HandleGameUpdateRequest proto.InternalMessageInfo

func (m *HandleGameUpdateRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type HandleGameUpdateResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleGameUpdateResponse) Reset()         { *m = HandleGameUpdateResponse{} }
func (m *HandleGameUpdateResponse) String() string { return proto.CompactTextString(m) }
func (*HandleGameUpdateResponse) ProtoMessage()    {}
func (*HandleGameUpdateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{120}
}
func (m *HandleGameUpdateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleGameUpdateResponse.Unmarshal(m, b)
}
func (m *HandleGameUpdateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleGameUpdateResponse.Marshal(b, m, deterministic)
}
func (dst *HandleGameUpdateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleGameUpdateResponse.Merge(dst, src)
}
func (m *HandleGameUpdateResponse) XXX_Size() int {
	return xxx_messageInfo_HandleGameUpdateResponse.Size(m)
}
func (m *HandleGameUpdateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleGameUpdateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HandleGameUpdateResponse proto.InternalMessageInfo

type FreezeCoachSkillRequest struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameIdList           []uint32   `protobuf:"varint,2,rep,packed,name=game_id_list,json=gameIdList,proto3" json:"game_id_list,omitempty"`
	FreezeType           FreezeType `protobuf:"varint,3,opt,name=freeze_type,json=freezeType,proto3,enum=esport_skill.FreezeType" json:"freeze_type,omitempty"`
	FreezeStopTs         int64      `protobuf:"varint,4,opt,name=freeze_stop_ts,json=freezeStopTs,proto3" json:"freeze_stop_ts,omitempty"`
	FreezeReason         string     `protobuf:"bytes,5,opt,name=freeze_reason,json=freezeReason,proto3" json:"freeze_reason,omitempty"`
	OpUser               string     `protobuf:"bytes,6,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *FreezeCoachSkillRequest) Reset()         { *m = FreezeCoachSkillRequest{} }
func (m *FreezeCoachSkillRequest) String() string { return proto.CompactTextString(m) }
func (*FreezeCoachSkillRequest) ProtoMessage()    {}
func (*FreezeCoachSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{121}
}
func (m *FreezeCoachSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeCoachSkillRequest.Unmarshal(m, b)
}
func (m *FreezeCoachSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeCoachSkillRequest.Marshal(b, m, deterministic)
}
func (dst *FreezeCoachSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeCoachSkillRequest.Merge(dst, src)
}
func (m *FreezeCoachSkillRequest) XXX_Size() int {
	return xxx_messageInfo_FreezeCoachSkillRequest.Size(m)
}
func (m *FreezeCoachSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeCoachSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeCoachSkillRequest proto.InternalMessageInfo

func (m *FreezeCoachSkillRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FreezeCoachSkillRequest) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

func (m *FreezeCoachSkillRequest) GetFreezeType() FreezeType {
	if m != nil {
		return m.FreezeType
	}
	return FreezeType_FREEZE_TYPE_UNFREEZE
}

func (m *FreezeCoachSkillRequest) GetFreezeStopTs() int64 {
	if m != nil {
		return m.FreezeStopTs
	}
	return 0
}

func (m *FreezeCoachSkillRequest) GetFreezeReason() string {
	if m != nil {
		return m.FreezeReason
	}
	return ""
}

func (m *FreezeCoachSkillRequest) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

type FreezeCoachSkillResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeCoachSkillResponse) Reset()         { *m = FreezeCoachSkillResponse{} }
func (m *FreezeCoachSkillResponse) String() string { return proto.CompactTextString(m) }
func (*FreezeCoachSkillResponse) ProtoMessage()    {}
func (*FreezeCoachSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{122}
}
func (m *FreezeCoachSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeCoachSkillResponse.Unmarshal(m, b)
}
func (m *FreezeCoachSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeCoachSkillResponse.Marshal(b, m, deterministic)
}
func (dst *FreezeCoachSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeCoachSkillResponse.Merge(dst, src)
}
func (m *FreezeCoachSkillResponse) XXX_Size() int {
	return xxx_messageInfo_FreezeCoachSkillResponse.Size(m)
}
func (m *FreezeCoachSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeCoachSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeCoachSkillResponse proto.InternalMessageInfo

type UnfreezeCoachSkillRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameIdList           []uint32 `protobuf:"varint,2,rep,packed,name=game_id_list,json=gameIdList,proto3" json:"game_id_list,omitempty"`
	OpUser               string   `protobuf:"bytes,3,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnfreezeCoachSkillRequest) Reset()         { *m = UnfreezeCoachSkillRequest{} }
func (m *UnfreezeCoachSkillRequest) String() string { return proto.CompactTextString(m) }
func (*UnfreezeCoachSkillRequest) ProtoMessage()    {}
func (*UnfreezeCoachSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{123}
}
func (m *UnfreezeCoachSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfreezeCoachSkillRequest.Unmarshal(m, b)
}
func (m *UnfreezeCoachSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfreezeCoachSkillRequest.Marshal(b, m, deterministic)
}
func (dst *UnfreezeCoachSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfreezeCoachSkillRequest.Merge(dst, src)
}
func (m *UnfreezeCoachSkillRequest) XXX_Size() int {
	return xxx_messageInfo_UnfreezeCoachSkillRequest.Size(m)
}
func (m *UnfreezeCoachSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfreezeCoachSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UnfreezeCoachSkillRequest proto.InternalMessageInfo

func (m *UnfreezeCoachSkillRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UnfreezeCoachSkillRequest) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

func (m *UnfreezeCoachSkillRequest) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

type UnfreezeCoachSkillResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnfreezeCoachSkillResponse) Reset()         { *m = UnfreezeCoachSkillResponse{} }
func (m *UnfreezeCoachSkillResponse) String() string { return proto.CompactTextString(m) }
func (*UnfreezeCoachSkillResponse) ProtoMessage()    {}
func (*UnfreezeCoachSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{124}
}
func (m *UnfreezeCoachSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfreezeCoachSkillResponse.Unmarshal(m, b)
}
func (m *UnfreezeCoachSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfreezeCoachSkillResponse.Marshal(b, m, deterministic)
}
func (dst *UnfreezeCoachSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfreezeCoachSkillResponse.Merge(dst, src)
}
func (m *UnfreezeCoachSkillResponse) XXX_Size() int {
	return xxx_messageInfo_UnfreezeCoachSkillResponse.Size(m)
}
func (m *UnfreezeCoachSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfreezeCoachSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UnfreezeCoachSkillResponse proto.InternalMessageInfo

type GetSkillFreezeOperationListRequest struct {
	Page                 int32    `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	GuildId              uint32   `protobuf:"varint,4,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GameId               uint32   `protobuf:"varint,5,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSkillFreezeOperationListRequest) Reset()         { *m = GetSkillFreezeOperationListRequest{} }
func (m *GetSkillFreezeOperationListRequest) String() string { return proto.CompactTextString(m) }
func (*GetSkillFreezeOperationListRequest) ProtoMessage()    {}
func (*GetSkillFreezeOperationListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{125}
}
func (m *GetSkillFreezeOperationListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSkillFreezeOperationListRequest.Unmarshal(m, b)
}
func (m *GetSkillFreezeOperationListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSkillFreezeOperationListRequest.Marshal(b, m, deterministic)
}
func (dst *GetSkillFreezeOperationListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSkillFreezeOperationListRequest.Merge(dst, src)
}
func (m *GetSkillFreezeOperationListRequest) XXX_Size() int {
	return xxx_messageInfo_GetSkillFreezeOperationListRequest.Size(m)
}
func (m *GetSkillFreezeOperationListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSkillFreezeOperationListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSkillFreezeOperationListRequest proto.InternalMessageInfo

func (m *GetSkillFreezeOperationListRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetSkillFreezeOperationListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetSkillFreezeOperationListRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetSkillFreezeOperationListRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetSkillFreezeOperationListRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetSkillFreezeOperationListResponse struct {
	OperationList        []*SkillFreezeOperation `protobuf:"bytes,1,rep,name=operation_list,json=operationList,proto3" json:"operation_list,omitempty"`
	Total                int32                   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetSkillFreezeOperationListResponse) Reset()         { *m = GetSkillFreezeOperationListResponse{} }
func (m *GetSkillFreezeOperationListResponse) String() string { return proto.CompactTextString(m) }
func (*GetSkillFreezeOperationListResponse) ProtoMessage()    {}
func (*GetSkillFreezeOperationListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{126}
}
func (m *GetSkillFreezeOperationListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSkillFreezeOperationListResponse.Unmarshal(m, b)
}
func (m *GetSkillFreezeOperationListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSkillFreezeOperationListResponse.Marshal(b, m, deterministic)
}
func (dst *GetSkillFreezeOperationListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSkillFreezeOperationListResponse.Merge(dst, src)
}
func (m *GetSkillFreezeOperationListResponse) XXX_Size() int {
	return xxx_messageInfo_GetSkillFreezeOperationListResponse.Size(m)
}
func (m *GetSkillFreezeOperationListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSkillFreezeOperationListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSkillFreezeOperationListResponse proto.InternalMessageInfo

func (m *GetSkillFreezeOperationListResponse) GetOperationList() []*SkillFreezeOperation {
	if m != nil {
		return m.OperationList
	}
	return nil
}

func (m *GetSkillFreezeOperationListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type SkillFreezeOperation struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32     `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GameIdList           []uint32   `protobuf:"varint,3,rep,packed,name=game_id_list,json=gameIdList,proto3" json:"game_id_list,omitempty"`
	FreezeType           FreezeType `protobuf:"varint,4,opt,name=freeze_type,json=freezeType,proto3,enum=esport_skill.FreezeType" json:"freeze_type,omitempty"`
	FreezeStopTs         int64      `protobuf:"varint,5,opt,name=freeze_stop_ts,json=freezeStopTs,proto3" json:"freeze_stop_ts,omitempty"`
	FreezeReason         string     `protobuf:"bytes,6,opt,name=freeze_reason,json=freezeReason,proto3" json:"freeze_reason,omitempty"`
	OpUser               string     `protobuf:"bytes,7,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	OpTs                 int64      `protobuf:"varint,8,opt,name=op_ts,json=opTs,proto3" json:"op_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SkillFreezeOperation) Reset()         { *m = SkillFreezeOperation{} }
func (m *SkillFreezeOperation) String() string { return proto.CompactTextString(m) }
func (*SkillFreezeOperation) ProtoMessage()    {}
func (*SkillFreezeOperation) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{127}
}
func (m *SkillFreezeOperation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkillFreezeOperation.Unmarshal(m, b)
}
func (m *SkillFreezeOperation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkillFreezeOperation.Marshal(b, m, deterministic)
}
func (dst *SkillFreezeOperation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkillFreezeOperation.Merge(dst, src)
}
func (m *SkillFreezeOperation) XXX_Size() int {
	return xxx_messageInfo_SkillFreezeOperation.Size(m)
}
func (m *SkillFreezeOperation) XXX_DiscardUnknown() {
	xxx_messageInfo_SkillFreezeOperation.DiscardUnknown(m)
}

var xxx_messageInfo_SkillFreezeOperation proto.InternalMessageInfo

func (m *SkillFreezeOperation) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SkillFreezeOperation) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SkillFreezeOperation) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

func (m *SkillFreezeOperation) GetFreezeType() FreezeType {
	if m != nil {
		return m.FreezeType
	}
	return FreezeType_FREEZE_TYPE_UNFREEZE
}

func (m *SkillFreezeOperation) GetFreezeStopTs() int64 {
	if m != nil {
		return m.FreezeStopTs
	}
	return 0
}

func (m *SkillFreezeOperation) GetFreezeReason() string {
	if m != nil {
		return m.FreezeReason
	}
	return ""
}

func (m *SkillFreezeOperation) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

func (m *SkillFreezeOperation) GetOpTs() int64 {
	if m != nil {
		return m.OpTs
	}
	return 0
}

type DebugCheckGuaranteeWinPermissionRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DebugCheckGuaranteeWinPermissionRequest) Reset() {
	*m = DebugCheckGuaranteeWinPermissionRequest{}
}
func (m *DebugCheckGuaranteeWinPermissionRequest) String() string { return proto.CompactTextString(m) }
func (*DebugCheckGuaranteeWinPermissionRequest) ProtoMessage()    {}
func (*DebugCheckGuaranteeWinPermissionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{128}
}
func (m *DebugCheckGuaranteeWinPermissionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DebugCheckGuaranteeWinPermissionRequest.Unmarshal(m, b)
}
func (m *DebugCheckGuaranteeWinPermissionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DebugCheckGuaranteeWinPermissionRequest.Marshal(b, m, deterministic)
}
func (dst *DebugCheckGuaranteeWinPermissionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DebugCheckGuaranteeWinPermissionRequest.Merge(dst, src)
}
func (m *DebugCheckGuaranteeWinPermissionRequest) XXX_Size() int {
	return xxx_messageInfo_DebugCheckGuaranteeWinPermissionRequest.Size(m)
}
func (m *DebugCheckGuaranteeWinPermissionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DebugCheckGuaranteeWinPermissionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DebugCheckGuaranteeWinPermissionRequest proto.InternalMessageInfo

type DebugCheckGuaranteeWinPermissionResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DebugCheckGuaranteeWinPermissionResponse) Reset() {
	*m = DebugCheckGuaranteeWinPermissionResponse{}
}
func (m *DebugCheckGuaranteeWinPermissionResponse) String() string { return proto.CompactTextString(m) }
func (*DebugCheckGuaranteeWinPermissionResponse) ProtoMessage()    {}
func (*DebugCheckGuaranteeWinPermissionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{129}
}
func (m *DebugCheckGuaranteeWinPermissionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DebugCheckGuaranteeWinPermissionResponse.Unmarshal(m, b)
}
func (m *DebugCheckGuaranteeWinPermissionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DebugCheckGuaranteeWinPermissionResponse.Marshal(b, m, deterministic)
}
func (dst *DebugCheckGuaranteeWinPermissionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DebugCheckGuaranteeWinPermissionResponse.Merge(dst, src)
}
func (m *DebugCheckGuaranteeWinPermissionResponse) XXX_Size() int {
	return xxx_messageInfo_DebugCheckGuaranteeWinPermissionResponse.Size(m)
}
func (m *DebugCheckGuaranteeWinPermissionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DebugCheckGuaranteeWinPermissionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DebugCheckGuaranteeWinPermissionResponse proto.InternalMessageInfo

// 特色标签列表接口
type GetSpecialLabelListRequest struct {
	LabelType            SpecialLabelType `protobuf:"varint,1,opt,name=label_type,json=labelType,proto3,enum=esport_skill.SpecialLabelType" json:"label_type,omitempty"`
	GameId               uint32           `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetSpecialLabelListRequest) Reset()         { *m = GetSpecialLabelListRequest{} }
func (m *GetSpecialLabelListRequest) String() string { return proto.CompactTextString(m) }
func (*GetSpecialLabelListRequest) ProtoMessage()    {}
func (*GetSpecialLabelListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{130}
}
func (m *GetSpecialLabelListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSpecialLabelListRequest.Unmarshal(m, b)
}
func (m *GetSpecialLabelListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSpecialLabelListRequest.Marshal(b, m, deterministic)
}
func (dst *GetSpecialLabelListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSpecialLabelListRequest.Merge(dst, src)
}
func (m *GetSpecialLabelListRequest) XXX_Size() int {
	return xxx_messageInfo_GetSpecialLabelListRequest.Size(m)
}
func (m *GetSpecialLabelListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSpecialLabelListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSpecialLabelListRequest proto.InternalMessageInfo

func (m *GetSpecialLabelListRequest) GetLabelType() SpecialLabelType {
	if m != nil {
		return m.LabelType
	}
	return SpecialLabelType_SPECIAL_LABEL_TYPE_UNSPECIFIED
}

func (m *GetSpecialLabelListRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type SpecialLabel struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	LabelName            string   `protobuf:"bytes,2,opt,name=label_name,json=labelName,proto3" json:"label_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SpecialLabel) Reset()         { *m = SpecialLabel{} }
func (m *SpecialLabel) String() string { return proto.CompactTextString(m) }
func (*SpecialLabel) ProtoMessage()    {}
func (*SpecialLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{131}
}
func (m *SpecialLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SpecialLabel.Unmarshal(m, b)
}
func (m *SpecialLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SpecialLabel.Marshal(b, m, deterministic)
}
func (dst *SpecialLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SpecialLabel.Merge(dst, src)
}
func (m *SpecialLabel) XXX_Size() int {
	return xxx_messageInfo_SpecialLabel.Size(m)
}
func (m *SpecialLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_SpecialLabel.DiscardUnknown(m)
}

var xxx_messageInfo_SpecialLabel proto.InternalMessageInfo

func (m *SpecialLabel) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SpecialLabel) GetLabelName() string {
	if m != nil {
		return m.LabelName
	}
	return ""
}

// 特色标签列表响应
type GetSpecialLabelListResponse struct {
	LabelList            []*SpecialLabel `protobuf:"bytes,1,rep,name=label_list,json=labelList,proto3" json:"label_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetSpecialLabelListResponse) Reset()         { *m = GetSpecialLabelListResponse{} }
func (m *GetSpecialLabelListResponse) String() string { return proto.CompactTextString(m) }
func (*GetSpecialLabelListResponse) ProtoMessage()    {}
func (*GetSpecialLabelListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{132}
}
func (m *GetSpecialLabelListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSpecialLabelListResponse.Unmarshal(m, b)
}
func (m *GetSpecialLabelListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSpecialLabelListResponse.Marshal(b, m, deterministic)
}
func (dst *GetSpecialLabelListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSpecialLabelListResponse.Merge(dst, src)
}
func (m *GetSpecialLabelListResponse) XXX_Size() int {
	return xxx_messageInfo_GetSpecialLabelListResponse.Size(m)
}
func (m *GetSpecialLabelListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSpecialLabelListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSpecialLabelListResponse proto.InternalMessageInfo

func (m *GetSpecialLabelListResponse) GetLabelList() []*SpecialLabel {
	if m != nil {
		return m.LabelList
	}
	return nil
}

// 用户特色标签信息 web运营后台请求
type BatchGetUserSpecialLabelRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserSpecialLabelRequest) Reset()         { *m = BatchGetUserSpecialLabelRequest{} }
func (m *BatchGetUserSpecialLabelRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserSpecialLabelRequest) ProtoMessage()    {}
func (*BatchGetUserSpecialLabelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{133}
}
func (m *BatchGetUserSpecialLabelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserSpecialLabelRequest.Unmarshal(m, b)
}
func (m *BatchGetUserSpecialLabelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserSpecialLabelRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserSpecialLabelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserSpecialLabelRequest.Merge(dst, src)
}
func (m *BatchGetUserSpecialLabelRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserSpecialLabelRequest.Size(m)
}
func (m *BatchGetUserSpecialLabelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserSpecialLabelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserSpecialLabelRequest proto.InternalMessageInfo

func (m *BatchGetUserSpecialLabelRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 用户特色标签信息 web运营后台请求
type BatchGetUserSpecialLabelResponse struct {
	UserSpecialLabelMap  map[uint32]*BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList `protobuf:"bytes,1,rep,name=user_special_label_map,json=userSpecialLabelMap,proto3" json:"user_special_label_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                              `json:"-"`
	XXX_unrecognized     []byte                                                                `json:"-"`
	XXX_sizecache        int32                                                                 `json:"-"`
}

func (m *BatchGetUserSpecialLabelResponse) Reset()         { *m = BatchGetUserSpecialLabelResponse{} }
func (m *BatchGetUserSpecialLabelResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserSpecialLabelResponse) ProtoMessage()    {}
func (*BatchGetUserSpecialLabelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{134}
}
func (m *BatchGetUserSpecialLabelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserSpecialLabelResponse.Unmarshal(m, b)
}
func (m *BatchGetUserSpecialLabelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserSpecialLabelResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserSpecialLabelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserSpecialLabelResponse.Merge(dst, src)
}
func (m *BatchGetUserSpecialLabelResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserSpecialLabelResponse.Size(m)
}
func (m *BatchGetUserSpecialLabelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserSpecialLabelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserSpecialLabelResponse proto.InternalMessageInfo

func (m *BatchGetUserSpecialLabelResponse) GetUserSpecialLabelMap() map[uint32]*BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList {
	if m != nil {
		return m.UserSpecialLabelMap
	}
	return nil
}

type BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList struct {
	GameList             []*UserGameSpecialLabelItem `protobuf:"bytes,1,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList) Reset() {
	*m = BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList{}
}
func (m *BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList) ProtoMessage() {}
func (*BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{134, 0}
}
func (m *BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList.Unmarshal(m, b)
}
func (m *BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList.Merge(dst, src)
}
func (m *BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList.Size(m)
}
func (m *BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList proto.InternalMessageInfo

func (m *BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList) GetGameList() []*UserGameSpecialLabelItem {
	if m != nil {
		return m.GameList
	}
	return nil
}

// 用户特色标签信息 app请求
type BatchGetUserGameSpecialLabelRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserGameSpecialLabelRequest) Reset()         { *m = BatchGetUserGameSpecialLabelRequest{} }
func (m *BatchGetUserGameSpecialLabelRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserGameSpecialLabelRequest) ProtoMessage()    {}
func (*BatchGetUserGameSpecialLabelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{135}
}
func (m *BatchGetUserGameSpecialLabelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserGameSpecialLabelRequest.Unmarshal(m, b)
}
func (m *BatchGetUserGameSpecialLabelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserGameSpecialLabelRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserGameSpecialLabelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserGameSpecialLabelRequest.Merge(dst, src)
}
func (m *BatchGetUserGameSpecialLabelRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserGameSpecialLabelRequest.Size(m)
}
func (m *BatchGetUserGameSpecialLabelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserGameSpecialLabelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserGameSpecialLabelRequest proto.InternalMessageInfo

func (m *BatchGetUserGameSpecialLabelRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetUserGameSpecialLabelRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

// 用户特色标签信息 app请求
type BatchGetUserGameSpecialLabelResponse struct {
	UserSpecialLabelMap  map[uint32]*BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList `protobuf:"bytes,1,rep,name=user_special_label_map,json=userSpecialLabelMap,proto3" json:"user_special_label_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                                  `json:"-"`
	XXX_unrecognized     []byte                                                                    `json:"-"`
	XXX_sizecache        int32                                                                     `json:"-"`
}

func (m *BatchGetUserGameSpecialLabelResponse) Reset()         { *m = BatchGetUserGameSpecialLabelResponse{} }
func (m *BatchGetUserGameSpecialLabelResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserGameSpecialLabelResponse) ProtoMessage()    {}
func (*BatchGetUserGameSpecialLabelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{136}
}
func (m *BatchGetUserGameSpecialLabelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserGameSpecialLabelResponse.Unmarshal(m, b)
}
func (m *BatchGetUserGameSpecialLabelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserGameSpecialLabelResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserGameSpecialLabelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserGameSpecialLabelResponse.Merge(dst, src)
}
func (m *BatchGetUserGameSpecialLabelResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserGameSpecialLabelResponse.Size(m)
}
func (m *BatchGetUserGameSpecialLabelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserGameSpecialLabelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserGameSpecialLabelResponse proto.InternalMessageInfo

func (m *BatchGetUserGameSpecialLabelResponse) GetUserSpecialLabelMap() map[uint32]*BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList {
	if m != nil {
		return m.UserSpecialLabelMap
	}
	return nil
}

type BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo struct {
	LabelName            string   `protobuf:"bytes,1,opt,name=label_name,json=labelName,proto3" json:"label_name,omitempty"`
	LabelImage           string   `protobuf:"bytes,2,opt,name=label_image,json=labelImage,proto3" json:"label_image,omitempty"`
	DisplayOrder         uint32   `protobuf:"varint,3,opt,name=display_order,json=displayOrder,proto3" json:"display_order,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo) Reset() {
	*m = BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo{}
}
func (m *BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo) ProtoMessage() {}
func (*BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{136, 0}
}
func (m *BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo.Unmarshal(m, b)
}
func (m *BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo.Merge(dst, src)
}
func (m *BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo.Size(m)
}
func (m *BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo proto.InternalMessageInfo

func (m *BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo) GetLabelName() string {
	if m != nil {
		return m.LabelName
	}
	return ""
}

func (m *BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo) GetLabelImage() string {
	if m != nil {
		return m.LabelImage
	}
	return ""
}

func (m *BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo) GetDisplayOrder() uint32 {
	if m != nil {
		return m.DisplayOrder
	}
	return 0
}

type BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList struct {
	LabelList            []*BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo `protobuf:"bytes,1,rep,name=label_list,json=labelList,proto3" json:"label_list,omitempty"`
	VoiceLabel           string                                                         `protobuf:"bytes,2,opt,name=voice_label,json=voiceLabel,proto3" json:"voice_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                                       `json:"-"`
	XXX_unrecognized     []byte                                                         `json:"-"`
	XXX_sizecache        int32                                                          `json:"-"`
}

func (m *BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList) Reset() {
	*m = BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList{}
}
func (m *BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList) ProtoMessage() {}
func (*BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{136, 1}
}
func (m *BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList.Unmarshal(m, b)
}
func (m *BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList.Merge(dst, src)
}
func (m *BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList.Size(m)
}
func (m *BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList proto.InternalMessageInfo

func (m *BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList) GetLabelList() []*BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo {
	if m != nil {
		return m.LabelList
	}
	return nil
}

func (m *BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList) GetVoiceLabel() string {
	if m != nil {
		return m.VoiceLabel
	}
	return ""
}

type UserSpecialLabelItem struct {
	LabelId              uint32           `protobuf:"varint,1,opt,name=label_id,json=labelId,proto3" json:"label_id,omitempty"`
	LabelType            SpecialLabelType `protobuf:"varint,2,opt,name=label_type,json=labelType,proto3,enum=esport_skill.SpecialLabelType" json:"label_type,omitempty"`
	LabelName            string           `protobuf:"bytes,3,opt,name=label_name,json=labelName,proto3" json:"label_name,omitempty"`
	LabelImage           string           `protobuf:"bytes,4,opt,name=label_image,json=labelImage,proto3" json:"label_image,omitempty"`
	PricingAmount        string           `protobuf:"bytes,5,opt,name=pricing_amount,json=pricingAmount,proto3" json:"pricing_amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserSpecialLabelItem) Reset()         { *m = UserSpecialLabelItem{} }
func (m *UserSpecialLabelItem) String() string { return proto.CompactTextString(m) }
func (*UserSpecialLabelItem) ProtoMessage()    {}
func (*UserSpecialLabelItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{137}
}
func (m *UserSpecialLabelItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSpecialLabelItem.Unmarshal(m, b)
}
func (m *UserSpecialLabelItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSpecialLabelItem.Marshal(b, m, deterministic)
}
func (dst *UserSpecialLabelItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSpecialLabelItem.Merge(dst, src)
}
func (m *UserSpecialLabelItem) XXX_Size() int {
	return xxx_messageInfo_UserSpecialLabelItem.Size(m)
}
func (m *UserSpecialLabelItem) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSpecialLabelItem.DiscardUnknown(m)
}

var xxx_messageInfo_UserSpecialLabelItem proto.InternalMessageInfo

func (m *UserSpecialLabelItem) GetLabelId() uint32 {
	if m != nil {
		return m.LabelId
	}
	return 0
}

func (m *UserSpecialLabelItem) GetLabelType() SpecialLabelType {
	if m != nil {
		return m.LabelType
	}
	return SpecialLabelType_SPECIAL_LABEL_TYPE_UNSPECIFIED
}

func (m *UserSpecialLabelItem) GetLabelName() string {
	if m != nil {
		return m.LabelName
	}
	return ""
}

func (m *UserSpecialLabelItem) GetLabelImage() string {
	if m != nil {
		return m.LabelImage
	}
	return ""
}

func (m *UserSpecialLabelItem) GetPricingAmount() string {
	if m != nil {
		return m.PricingAmount
	}
	return ""
}

// 同一个游戏下的多个 标签
type UserGameSpecialLabelItem struct {
	GameId               uint32                  `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	LabelList            []*UserSpecialLabelItem `protobuf:"bytes,2,rep,name=label_list,json=labelList,proto3" json:"label_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UserGameSpecialLabelItem) Reset()         { *m = UserGameSpecialLabelItem{} }
func (m *UserGameSpecialLabelItem) String() string { return proto.CompactTextString(m) }
func (*UserGameSpecialLabelItem) ProtoMessage()    {}
func (*UserGameSpecialLabelItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{138}
}
func (m *UserGameSpecialLabelItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGameSpecialLabelItem.Unmarshal(m, b)
}
func (m *UserGameSpecialLabelItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGameSpecialLabelItem.Marshal(b, m, deterministic)
}
func (dst *UserGameSpecialLabelItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGameSpecialLabelItem.Merge(dst, src)
}
func (m *UserGameSpecialLabelItem) XXX_Size() int {
	return xxx_messageInfo_UserGameSpecialLabelItem.Size(m)
}
func (m *UserGameSpecialLabelItem) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGameSpecialLabelItem.DiscardUnknown(m)
}

var xxx_messageInfo_UserGameSpecialLabelItem proto.InternalMessageInfo

func (m *UserGameSpecialLabelItem) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserGameSpecialLabelItem) GetLabelList() []*UserSpecialLabelItem {
	if m != nil {
		return m.LabelList
	}
	return nil
}

// 更新用户特色标签信息
type UpdateUserSpecialLabelRequest struct {
	Uid                  uint32                      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameList             []*UserGameSpecialLabelItem `protobuf:"bytes,2,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *UpdateUserSpecialLabelRequest) Reset()         { *m = UpdateUserSpecialLabelRequest{} }
func (m *UpdateUserSpecialLabelRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateUserSpecialLabelRequest) ProtoMessage()    {}
func (*UpdateUserSpecialLabelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{139}
}
func (m *UpdateUserSpecialLabelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserSpecialLabelRequest.Unmarshal(m, b)
}
func (m *UpdateUserSpecialLabelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserSpecialLabelRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateUserSpecialLabelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserSpecialLabelRequest.Merge(dst, src)
}
func (m *UpdateUserSpecialLabelRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateUserSpecialLabelRequest.Size(m)
}
func (m *UpdateUserSpecialLabelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserSpecialLabelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserSpecialLabelRequest proto.InternalMessageInfo

func (m *UpdateUserSpecialLabelRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserSpecialLabelRequest) GetGameList() []*UserGameSpecialLabelItem {
	if m != nil {
		return m.GameList
	}
	return nil
}

// 更新用户特色标签信息
type UpdateUserSpecialLabelResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserSpecialLabelResponse) Reset()         { *m = UpdateUserSpecialLabelResponse{} }
func (m *UpdateUserSpecialLabelResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateUserSpecialLabelResponse) ProtoMessage()    {}
func (*UpdateUserSpecialLabelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{140}
}
func (m *UpdateUserSpecialLabelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserSpecialLabelResponse.Unmarshal(m, b)
}
func (m *UpdateUserSpecialLabelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserSpecialLabelResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateUserSpecialLabelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserSpecialLabelResponse.Merge(dst, src)
}
func (m *UpdateUserSpecialLabelResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateUserSpecialLabelResponse.Size(m)
}
func (m *UpdateUserSpecialLabelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserSpecialLabelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserSpecialLabelResponse proto.InternalMessageInfo

// 校验优先级是否冲突
type CheckLabelOrderRequest struct {
	LabelType            LabelType                        `protobuf:"varint,1,opt,name=label_type,json=labelType,proto3,enum=esport_skill.LabelType" json:"label_type,omitempty"`
	DisplayOrder         uint32                           `protobuf:"varint,2,opt,name=display_order,json=displayOrder,proto3" json:"display_order,omitempty"`
	CheckType            CheckLabelOrderRequest_CheckType `protobuf:"varint,3,opt,name=check_type,json=checkType,proto3,enum=esport_skill.CheckLabelOrderRequest_CheckType" json:"check_type,omitempty"`
	LabelId              uint32                           `protobuf:"varint,4,opt,name=label_id,json=labelId,proto3" json:"label_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *CheckLabelOrderRequest) Reset()         { *m = CheckLabelOrderRequest{} }
func (m *CheckLabelOrderRequest) String() string { return proto.CompactTextString(m) }
func (*CheckLabelOrderRequest) ProtoMessage()    {}
func (*CheckLabelOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{141}
}
func (m *CheckLabelOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckLabelOrderRequest.Unmarshal(m, b)
}
func (m *CheckLabelOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckLabelOrderRequest.Marshal(b, m, deterministic)
}
func (dst *CheckLabelOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckLabelOrderRequest.Merge(dst, src)
}
func (m *CheckLabelOrderRequest) XXX_Size() int {
	return xxx_messageInfo_CheckLabelOrderRequest.Size(m)
}
func (m *CheckLabelOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckLabelOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckLabelOrderRequest proto.InternalMessageInfo

func (m *CheckLabelOrderRequest) GetLabelType() LabelType {
	if m != nil {
		return m.LabelType
	}
	return LabelType_LABEL_TYPE_UNSPECIFIED
}

func (m *CheckLabelOrderRequest) GetDisplayOrder() uint32 {
	if m != nil {
		return m.DisplayOrder
	}
	return 0
}

func (m *CheckLabelOrderRequest) GetCheckType() CheckLabelOrderRequest_CheckType {
	if m != nil {
		return m.CheckType
	}
	return CheckLabelOrderRequest_CHECK_TYPE_UNSPECIFIED
}

func (m *CheckLabelOrderRequest) GetLabelId() uint32 {
	if m != nil {
		return m.LabelId
	}
	return 0
}

// 校验优先级是否冲突,失败时接口报错
type CheckLabelOrderResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckLabelOrderResponse) Reset()         { *m = CheckLabelOrderResponse{} }
func (m *CheckLabelOrderResponse) String() string { return proto.CompactTextString(m) }
func (*CheckLabelOrderResponse) ProtoMessage()    {}
func (*CheckLabelOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{142}
}
func (m *CheckLabelOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckLabelOrderResponse.Unmarshal(m, b)
}
func (m *CheckLabelOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckLabelOrderResponse.Marshal(b, m, deterministic)
}
func (dst *CheckLabelOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckLabelOrderResponse.Merge(dst, src)
}
func (m *CheckLabelOrderResponse) XXX_Size() int {
	return xxx_messageInfo_CheckLabelOrderResponse.Size(m)
}
func (m *CheckLabelOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckLabelOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckLabelOrderResponse proto.InternalMessageInfo

type GetLabelIssuanceRecordRequest struct {
	PageNum              int32    `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLabelIssuanceRecordRequest) Reset()         { *m = GetLabelIssuanceRecordRequest{} }
func (m *GetLabelIssuanceRecordRequest) String() string { return proto.CompactTextString(m) }
func (*GetLabelIssuanceRecordRequest) ProtoMessage()    {}
func (*GetLabelIssuanceRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{143}
}
func (m *GetLabelIssuanceRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLabelIssuanceRecordRequest.Unmarshal(m, b)
}
func (m *GetLabelIssuanceRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLabelIssuanceRecordRequest.Marshal(b, m, deterministic)
}
func (dst *GetLabelIssuanceRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLabelIssuanceRecordRequest.Merge(dst, src)
}
func (m *GetLabelIssuanceRecordRequest) XXX_Size() int {
	return xxx_messageInfo_GetLabelIssuanceRecordRequest.Size(m)
}
func (m *GetLabelIssuanceRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLabelIssuanceRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLabelIssuanceRecordRequest proto.InternalMessageInfo

func (m *GetLabelIssuanceRecordRequest) GetPageNum() int32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetLabelIssuanceRecordRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type SimpleIssuanceRecord struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	LabelId              uint32   `protobuf:"varint,2,opt,name=label_id,json=labelId,proto3" json:"label_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimpleIssuanceRecord) Reset()         { *m = SimpleIssuanceRecord{} }
func (m *SimpleIssuanceRecord) String() string { return proto.CompactTextString(m) }
func (*SimpleIssuanceRecord) ProtoMessage()    {}
func (*SimpleIssuanceRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{144}
}
func (m *SimpleIssuanceRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleIssuanceRecord.Unmarshal(m, b)
}
func (m *SimpleIssuanceRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleIssuanceRecord.Marshal(b, m, deterministic)
}
func (dst *SimpleIssuanceRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleIssuanceRecord.Merge(dst, src)
}
func (m *SimpleIssuanceRecord) XXX_Size() int {
	return xxx_messageInfo_SimpleIssuanceRecord.Size(m)
}
func (m *SimpleIssuanceRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleIssuanceRecord.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleIssuanceRecord proto.InternalMessageInfo

func (m *SimpleIssuanceRecord) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *SimpleIssuanceRecord) GetLabelId() uint32 {
	if m != nil {
		return m.LabelId
	}
	return 0
}

func (m *SimpleIssuanceRecord) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetLabelIssuanceRecordResponse struct {
	RecordList           []*SimpleIssuanceRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetLabelIssuanceRecordResponse) Reset()         { *m = GetLabelIssuanceRecordResponse{} }
func (m *GetLabelIssuanceRecordResponse) String() string { return proto.CompactTextString(m) }
func (*GetLabelIssuanceRecordResponse) ProtoMessage()    {}
func (*GetLabelIssuanceRecordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{145}
}
func (m *GetLabelIssuanceRecordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLabelIssuanceRecordResponse.Unmarshal(m, b)
}
func (m *GetLabelIssuanceRecordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLabelIssuanceRecordResponse.Marshal(b, m, deterministic)
}
func (dst *GetLabelIssuanceRecordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLabelIssuanceRecordResponse.Merge(dst, src)
}
func (m *GetLabelIssuanceRecordResponse) XXX_Size() int {
	return xxx_messageInfo_GetLabelIssuanceRecordResponse.Size(m)
}
func (m *GetLabelIssuanceRecordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLabelIssuanceRecordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLabelIssuanceRecordResponse proto.InternalMessageInfo

func (m *GetLabelIssuanceRecordResponse) GetRecordList() []*SimpleIssuanceRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

type GetSpecialLabelIssuanceRecordRequest struct {
	PageNum              int32    `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSpecialLabelIssuanceRecordRequest) Reset()         { *m = GetSpecialLabelIssuanceRecordRequest{} }
func (m *GetSpecialLabelIssuanceRecordRequest) String() string { return proto.CompactTextString(m) }
func (*GetSpecialLabelIssuanceRecordRequest) ProtoMessage()    {}
func (*GetSpecialLabelIssuanceRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{146}
}
func (m *GetSpecialLabelIssuanceRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSpecialLabelIssuanceRecordRequest.Unmarshal(m, b)
}
func (m *GetSpecialLabelIssuanceRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSpecialLabelIssuanceRecordRequest.Marshal(b, m, deterministic)
}
func (dst *GetSpecialLabelIssuanceRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSpecialLabelIssuanceRecordRequest.Merge(dst, src)
}
func (m *GetSpecialLabelIssuanceRecordRequest) XXX_Size() int {
	return xxx_messageInfo_GetSpecialLabelIssuanceRecordRequest.Size(m)
}
func (m *GetSpecialLabelIssuanceRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSpecialLabelIssuanceRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSpecialLabelIssuanceRecordRequest proto.InternalMessageInfo

func (m *GetSpecialLabelIssuanceRecordRequest) GetPageNum() int32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetSpecialLabelIssuanceRecordRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetSpecialLabelIssuanceRecordResponse struct {
	RecordList           []*SimpleIssuanceRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetSpecialLabelIssuanceRecordResponse) Reset()         { *m = GetSpecialLabelIssuanceRecordResponse{} }
func (m *GetSpecialLabelIssuanceRecordResponse) String() string { return proto.CompactTextString(m) }
func (*GetSpecialLabelIssuanceRecordResponse) ProtoMessage()    {}
func (*GetSpecialLabelIssuanceRecordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_skill_02b79c516d0fa99e, []int{147}
}
func (m *GetSpecialLabelIssuanceRecordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSpecialLabelIssuanceRecordResponse.Unmarshal(m, b)
}
func (m *GetSpecialLabelIssuanceRecordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSpecialLabelIssuanceRecordResponse.Marshal(b, m, deterministic)
}
func (dst *GetSpecialLabelIssuanceRecordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSpecialLabelIssuanceRecordResponse.Merge(dst, src)
}
func (m *GetSpecialLabelIssuanceRecordResponse) XXX_Size() int {
	return xxx_messageInfo_GetSpecialLabelIssuanceRecordResponse.Size(m)
}
func (m *GetSpecialLabelIssuanceRecordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSpecialLabelIssuanceRecordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSpecialLabelIssuanceRecordResponse proto.InternalMessageInfo

func (m *GetSpecialLabelIssuanceRecordResponse) GetRecordList() []*SimpleIssuanceRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func init() {
	proto.RegisterType((*GameInformation)(nil), "esport_skill.GameInformation")
	proto.RegisterType((*BasePrice)(nil), "esport_skill.BasePrice")
	proto.RegisterMapType((map[uint32]uint32)(nil), "esport_skill.BasePrice.RankPriceMapEntry")
	proto.RegisterType((*GamePricing)(nil), "esport_skill.GamePricing")
	proto.RegisterType((*EsportGameConfig)(nil), "esport_skill.EsportGameConfig")
	proto.RegisterType((*AddEsportGameConfigRequest)(nil), "esport_skill.AddEsportGameConfigRequest")
	proto.RegisterType((*AddEsportGameConfigResponse)(nil), "esport_skill.AddEsportGameConfigResponse")
	proto.RegisterType((*UpdateEsportGameConfigRequest)(nil), "esport_skill.UpdateEsportGameConfigRequest")
	proto.RegisterType((*UpdateEsportGameConfigResponse)(nil), "esport_skill.UpdateEsportGameConfigResponse")
	proto.RegisterType((*DeleteEsportGameConfigRequest)(nil), "esport_skill.DeleteEsportGameConfigRequest")
	proto.RegisterType((*DeleteEsportGameConfigResponse)(nil), "esport_skill.DeleteEsportGameConfigResponse")
	proto.RegisterType((*GetEsportGameConfigListByPageRequest)(nil), "esport_skill.GetEsportGameConfigListByPageRequest")
	proto.RegisterType((*GetEsportGameConfigListByPageResponse)(nil), "esport_skill.GetEsportGameConfigListByPageResponse")
	proto.RegisterType((*GetTopGameListRequest)(nil), "esport_skill.GetTopGameListRequest")
	proto.RegisterType((*GetTopGameListResponse)(nil), "esport_skill.GetTopGameListResponse")
	proto.RegisterType((*SectionInfo)(nil), "esport_skill.SectionInfo")
	proto.RegisterType((*UserSkillInfo)(nil), "esport_skill.UserSkillInfo")
	proto.RegisterType((*AddUserAuditSkillRequest)(nil), "esport_skill.AddUserAuditSkillRequest")
	proto.RegisterType((*AddUserAuditSkillResponse)(nil), "esport_skill.AddUserAuditSkillResponse")
	proto.RegisterType((*GetUserAuditSkillRequest)(nil), "esport_skill.GetUserAuditSkillRequest")
	proto.RegisterType((*GetUserAuditSkillResponse)(nil), "esport_skill.GetUserAuditSkillResponse")
	proto.RegisterType((*BatchGetAuditSkillRequest)(nil), "esport_skill.BatchGetAuditSkillRequest")
	proto.RegisterType((*AuditSkillRecord)(nil), "esport_skill.AuditSkillRecord")
	proto.RegisterType((*BatchGetAuditSkillResponse)(nil), "esport_skill.BatchGetAuditSkillResponse")
	proto.RegisterType((*SetUserSkillAuditTypeRequest)(nil), "esport_skill.SetUserSkillAuditTypeRequest")
	proto.RegisterType((*SetUserSkillAuditTypeResponse)(nil), "esport_skill.SetUserSkillAuditTypeResponse")
	proto.RegisterType((*SetUserSkillRiskAuditTypeRequest)(nil), "esport_skill.SetUserSkillRiskAuditTypeRequest")
	proto.RegisterType((*SetUserSkillRiskAuditTypeResponse)(nil), "esport_skill.SetUserSkillRiskAuditTypeResponse")
	proto.RegisterType((*GetUserCurrentSkillRequest)(nil), "esport_skill.GetUserCurrentSkillRequest")
	proto.RegisterType((*GetUserCurrentSkillResponse)(nil), "esport_skill.GetUserCurrentSkillResponse")
	proto.RegisterType((*TestAddUserSkillRequest)(nil), "esport_skill.TestAddUserSkillRequest")
	proto.RegisterType((*TestAddUserSkillResponse)(nil), "esport_skill.TestAddUserSkillResponse")
	proto.RegisterType((*BatchGetUserCurrentSkillRequest)(nil), "esport_skill.BatchGetUserCurrentSkillRequest")
	proto.RegisterType((*UserCurrentSkill)(nil), "esport_skill.UserCurrentSkill")
	proto.RegisterType((*BatchGetUserCurrentSkillResponse)(nil), "esport_skill.BatchGetUserCurrentSkillResponse")
	proto.RegisterType((*ModifyUserSkillRequest)(nil), "esport_skill.ModifyUserSkillRequest")
	proto.RegisterType((*ModifyUserSkillResponse)(nil), "esport_skill.ModifyUserSkillResponse")
	proto.RegisterType((*DelUserSkillRequest)(nil), "esport_skill.DelUserSkillRequest")
	proto.RegisterType((*DelUserSkillResponse)(nil), "esport_skill.DelUserSkillResponse")
	proto.RegisterType((*GetUserSkillByGameIdRequest)(nil), "esport_skill.GetUserSkillByGameIdRequest")
	proto.RegisterType((*GetUserSkillByGameIdResponse)(nil), "esport_skill.GetUserSkillByGameIdResponse")
	proto.RegisterType((*GetUserSkillStatusRequest)(nil), "esport_skill.GetUserSkillStatusRequest")
	proto.RegisterType((*GetUserSkillStatusResponse)(nil), "esport_skill.GetUserSkillStatusResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "esport_skill.GetUserSkillStatusResponse.StatusMapEntry")
	proto.RegisterType((*GetUserSkillFreezeStatusRequest)(nil), "esport_skill.GetUserSkillFreezeStatusRequest")
	proto.RegisterType((*GetUserSkillFreezeStatusResponse)(nil), "esport_skill.GetUserSkillFreezeStatusResponse")
	proto.RegisterMapType((map[uint32]*SkillFreezeStatus)(nil), "esport_skill.GetUserSkillFreezeStatusResponse.GameStatusMapEntry")
	proto.RegisterType((*BatGetUserSkillFreezeStatusRequest)(nil), "esport_skill.BatGetUserSkillFreezeStatusRequest")
	proto.RegisterType((*SkillFreezeStatusMap)(nil), "esport_skill.SkillFreezeStatusMap")
	proto.RegisterMapType((map[uint32]*SkillFreezeStatus)(nil), "esport_skill.SkillFreezeStatusMap.GameStatusMapEntry")
	proto.RegisterType((*BatGetUserSkillFreezeStatusResponse)(nil), "esport_skill.BatGetUserSkillFreezeStatusResponse")
	proto.RegisterMapType((map[uint32]*SkillFreezeStatusMap)(nil), "esport_skill.BatGetUserSkillFreezeStatusResponse.UserMapEntry")
	proto.RegisterType((*SkillFreezeStatus)(nil), "esport_skill.SkillFreezeStatus")
	proto.RegisterType((*GetGameListRequest)(nil), "esport_skill.GetGameListRequest")
	proto.RegisterType((*GameItem)(nil), "esport_skill.GameItem")
	proto.RegisterType((*GetGameListResponse)(nil), "esport_skill.GetGameListResponse")
	proto.RegisterType((*GetSwitchRequest)(nil), "esport_skill.GetSwitchRequest")
	proto.RegisterType((*GetSwitchResponse)(nil), "esport_skill.GetSwitchResponse")
	proto.RegisterType((*SwitchStatus)(nil), "esport_skill.SwitchStatus")
	proto.RegisterType((*SetSwitchRequest)(nil), "esport_skill.SetSwitchRequest")
	proto.RegisterType((*SetSwitchResponse)(nil), "esport_skill.SetSwitchResponse")
	proto.RegisterType((*GetGameDetailByIdRequest)(nil), "esport_skill.GetGameDetailByIdRequest")
	proto.RegisterType((*GetGameDetailByIdResponse)(nil), "esport_skill.GetGameDetailByIdResponse")
	proto.RegisterType((*GetGameDetailByIdsRequest)(nil), "esport_skill.GetGameDetailByIdsRequest")
	proto.RegisterType((*GetGameDetailByIdsResponse)(nil), "esport_skill.GetGameDetailByIdsResponse")
	proto.RegisterType((*GetAllGameSimpleInfoRequest)(nil), "esport_skill.GetAllGameSimpleInfoRequest")
	proto.RegisterType((*GetMinimumPriceRequest)(nil), "esport_skill.GetMinimumPriceRequest")
	proto.RegisterType((*GetMinimumPriceResponse)(nil), "esport_skill.GetMinimumPriceResponse")
	proto.RegisterType((*SimpleGameInfo)(nil), "esport_skill.SimpleGameInfo")
	proto.RegisterType((*GetAllGameSimpleInfoResponse)(nil), "esport_skill.GetAllGameSimpleInfoResponse")
	proto.RegisterType((*BatchGetGameInfoByNamesRequest)(nil), "esport_skill.BatchGetGameInfoByNamesRequest")
	proto.RegisterType((*BatchGetGameInfoByNamesResponse)(nil), "esport_skill.BatchGetGameInfoByNamesResponse")
	proto.RegisterType((*BatchGetUserRenownedInfoRequest)(nil), "esport_skill.BatchGetUserRenownedInfoRequest")
	proto.RegisterType((*BatchGetUserRenownedInfoResponse)(nil), "esport_skill.BatchGetUserRenownedInfoResponse")
	proto.RegisterMapType((map[uint32]*BatchGetUserRenownedInfoResponse_UserRenownedInfo)(nil), "esport_skill.BatchGetUserRenownedInfoResponse.UserRenownedInfoMapEntry")
	proto.RegisterType((*BatchGetUserRenownedInfoResponse_UserRenownedInfo)(nil), "esport_skill.BatchGetUserRenownedInfoResponse.UserRenownedInfo")
	proto.RegisterType((*LabelInfo)(nil), "esport_skill.LabelInfo")
	proto.RegisterType((*CreateLabelRequest)(nil), "esport_skill.CreateLabelRequest")
	proto.RegisterType((*CreateLabelResponse)(nil), "esport_skill.CreateLabelResponse")
	proto.RegisterType((*EditLabelRequest)(nil), "esport_skill.EditLabelRequest")
	proto.RegisterType((*EditLabelResponse)(nil), "esport_skill.EditLabelResponse")
	proto.RegisterType((*DeleteLabelRequest)(nil), "esport_skill.DeleteLabelRequest")
	proto.RegisterType((*DeleteLabelResponse)(nil), "esport_skill.DeleteLabelResponse")
	proto.RegisterType((*ListLabelsRequest)(nil), "esport_skill.ListLabelsRequest")
	proto.RegisterType((*ListLabelsResponse)(nil), "esport_skill.ListLabelsResponse")
	proto.RegisterType((*IssueLabelRequest)(nil), "esport_skill.IssueLabelRequest")
	proto.RegisterType((*IssueLabelResponse)(nil), "esport_skill.IssueLabelResponse")
	proto.RegisterType((*RevokeLabelRequest)(nil), "esport_skill.RevokeLabelRequest")
	proto.RegisterType((*RevokeLabelResponse)(nil), "esport_skill.RevokeLabelResponse")
	proto.RegisterType((*QueryIssuanceRecordsRequest)(nil), "esport_skill.QueryIssuanceRecordsRequest")
	proto.RegisterType((*IssuanceRecord)(nil), "esport_skill.IssuanceRecord")
	proto.RegisterType((*QueryIssuanceRecordsResponse)(nil), "esport_skill.QueryIssuanceRecordsResponse")
	proto.RegisterType((*AddRenownedPlayerRequest)(nil), "esport_skill.AddRenownedPlayerRequest")
	proto.RegisterType((*AddRenownedPlayerRequest_RenownedPlayerInfo)(nil), "esport_skill.AddRenownedPlayerRequest.RenownedPlayerInfo")
	proto.RegisterType((*AddRenownedPlayerResponse)(nil), "esport_skill.AddRenownedPlayerResponse")
	proto.RegisterType((*ListRenownedPlayersRequest)(nil), "esport_skill.ListRenownedPlayersRequest")
	proto.RegisterType((*RenownedPlayerInfo)(nil), "esport_skill.RenownedPlayerInfo")
	proto.RegisterType((*ListRenownedPlayersResponse)(nil), "esport_skill.ListRenownedPlayersResponse")
	proto.RegisterType((*BatchRemoveRenownedPlayersRequest)(nil), "esport_skill.BatchRemoveRenownedPlayersRequest")
	proto.RegisterType((*BatchRemoveRenownedPlayersResponse)(nil), "esport_skill.BatchRemoveRenownedPlayersResponse")
	proto.RegisterType((*UpdateRenownedPlayerRequest)(nil), "esport_skill.UpdateRenownedPlayerRequest")
	proto.RegisterType((*UpdateRenownedPlayerResponse)(nil), "esport_skill.UpdateRenownedPlayerResponse")
	proto.RegisterType((*GetBasePriceSettingRequest)(nil), "esport_skill.GetBasePriceSettingRequest")
	proto.RegisterType((*GetBasePriceSettingResponse)(nil), "esport_skill.GetBasePriceSettingResponse")
	proto.RegisterType((*SetBasePriceSettingRequest)(nil), "esport_skill.SetBasePriceSettingRequest")
	proto.RegisterType((*SetBasePriceSettingResponse)(nil), "esport_skill.SetBasePriceSettingResponse")
	proto.RegisterType((*Price)(nil), "esport_skill.Price")
	proto.RegisterType((*CalculatePriceRequest)(nil), "esport_skill.CalculatePriceRequest")
	proto.RegisterType((*CalculatePriceResponse)(nil), "esport_skill.CalculatePriceResponse")
	proto.RegisterMapType((map[uint32]*Price)(nil), "esport_skill.CalculatePriceResponse.PriceMapEntry")
	proto.RegisterType((*CalculatePriceByGamesRequest)(nil), "esport_skill.CalculatePriceByGamesRequest")
	proto.RegisterType((*CalculatePriceByGamesResponse)(nil), "esport_skill.CalculatePriceByGamesResponse")
	proto.RegisterType((*GetCoachApplicableLabelsRequest)(nil), "esport_skill.GetCoachApplicableLabelsRequest")
	proto.RegisterType((*GetCoachApplicableLabelsResponse)(nil), "esport_skill.GetCoachApplicableLabelsResponse")
	proto.RegisterType((*GetCoachApplicableLabelsResponse_ApplicableLabel)(nil), "esport_skill.GetCoachApplicableLabelsResponse.ApplicableLabel")
	proto.RegisterType((*SetLabelPriceSwitchRequest)(nil), "esport_skill.SetLabelPriceSwitchRequest")
	proto.RegisterType((*SetLabelPriceSwitchResponse)(nil), "esport_skill.SetLabelPriceSwitchResponse")
	proto.RegisterType((*CheckCoachIdentityAndSkillRequest)(nil), "esport_skill.CheckCoachIdentityAndSkillRequest")
	proto.RegisterType((*CheckCoachIdentityAndSkillRequest_CheckItem)(nil), "esport_skill.CheckCoachIdentityAndSkillRequest.CheckItem")
	proto.RegisterType((*CheckCoachIdentityAndSkillResponse)(nil), "esport_skill.CheckCoachIdentityAndSkillResponse")
	proto.RegisterType((*BatchGetCoachLabelsForGameRequest)(nil), "esport_skill.BatchGetCoachLabelsForGameRequest")
	proto.RegisterType((*BatchGetCoachLabelsForGameResponse)(nil), "esport_skill.BatchGetCoachLabelsForGameResponse")
	proto.RegisterType((*BatchGetCoachLabelsForGameResponse_LabelList)(nil), "esport_skill.BatchGetCoachLabelsForGameResponse.LabelList")
	proto.RegisterType((*BatchGetCoachLabelsForGameResponse_CoachLabelInfo)(nil), "esport_skill.BatchGetCoachLabelsForGameResponse.CoachLabelInfo")
	proto.RegisterMapType((map[uint32]*BatchGetCoachLabelsForGameResponse_LabelList)(nil), "esport_skill.BatchGetCoachLabelsForGameResponse.CoachLabelInfo.LabelMapEntry")
	proto.RegisterType((*BatchCheckCoachHasGameRequest)(nil), "esport_skill.BatchCheckCoachHasGameRequest")
	proto.RegisterType((*BatchCheckCoachHasGameResponse)(nil), "esport_skill.BatchCheckCoachHasGameResponse")
	proto.RegisterMapType((map[uint32]bool)(nil), "esport_skill.BatchCheckCoachHasGameResponse.CoachHasGameMapEntry")
	proto.RegisterType((*GameCardInfoItem)(nil), "esport_skill.GameCardInfoItem")
	proto.RegisterType((*GameCardConfig)(nil), "esport_skill.GameCardConfig")
	proto.RegisterType((*GetAllGameCardConfigRequest)(nil), "esport_skill.GetAllGameCardConfigRequest")
	proto.RegisterType((*GetAllGameCardConfigResponse)(nil), "esport_skill.GetAllGameCardConfigResponse")
	proto.RegisterType((*SetGameGuaranteeStatusRequest)(nil), "esport_skill.SetGameGuaranteeStatusRequest")
	proto.RegisterType((*SetGameGuaranteeStatusResponse)(nil), "esport_skill.SetGameGuaranteeStatusResponse")
	proto.RegisterType((*HandleGameUpdateRequest)(nil), "esport_skill.HandleGameUpdateRequest")
	proto.RegisterType((*HandleGameUpdateResponse)(nil), "esport_skill.HandleGameUpdateResponse")
	proto.RegisterType((*FreezeCoachSkillRequest)(nil), "esport_skill.FreezeCoachSkillRequest")
	proto.RegisterType((*FreezeCoachSkillResponse)(nil), "esport_skill.FreezeCoachSkillResponse")
	proto.RegisterType((*UnfreezeCoachSkillRequest)(nil), "esport_skill.UnfreezeCoachSkillRequest")
	proto.RegisterType((*UnfreezeCoachSkillResponse)(nil), "esport_skill.UnfreezeCoachSkillResponse")
	proto.RegisterType((*GetSkillFreezeOperationListRequest)(nil), "esport_skill.GetSkillFreezeOperationListRequest")
	proto.RegisterType((*GetSkillFreezeOperationListResponse)(nil), "esport_skill.GetSkillFreezeOperationListResponse")
	proto.RegisterType((*SkillFreezeOperation)(nil), "esport_skill.SkillFreezeOperation")
	proto.RegisterType((*DebugCheckGuaranteeWinPermissionRequest)(nil), "esport_skill.DebugCheckGuaranteeWinPermissionRequest")
	proto.RegisterType((*DebugCheckGuaranteeWinPermissionResponse)(nil), "esport_skill.DebugCheckGuaranteeWinPermissionResponse")
	proto.RegisterType((*GetSpecialLabelListRequest)(nil), "esport_skill.GetSpecialLabelListRequest")
	proto.RegisterType((*SpecialLabel)(nil), "esport_skill.SpecialLabel")
	proto.RegisterType((*GetSpecialLabelListResponse)(nil), "esport_skill.GetSpecialLabelListResponse")
	proto.RegisterType((*BatchGetUserSpecialLabelRequest)(nil), "esport_skill.BatchGetUserSpecialLabelRequest")
	proto.RegisterType((*BatchGetUserSpecialLabelResponse)(nil), "esport_skill.BatchGetUserSpecialLabelResponse")
	proto.RegisterMapType((map[uint32]*BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList)(nil), "esport_skill.BatchGetUserSpecialLabelResponse.UserSpecialLabelMapEntry")
	proto.RegisterType((*BatchGetUserSpecialLabelResponse_UserGameSpecialLabelList)(nil), "esport_skill.BatchGetUserSpecialLabelResponse.UserGameSpecialLabelList")
	proto.RegisterType((*BatchGetUserGameSpecialLabelRequest)(nil), "esport_skill.BatchGetUserGameSpecialLabelRequest")
	proto.RegisterType((*BatchGetUserGameSpecialLabelResponse)(nil), "esport_skill.BatchGetUserGameSpecialLabelResponse")
	proto.RegisterMapType((map[uint32]*BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList)(nil), "esport_skill.BatchGetUserGameSpecialLabelResponse.UserSpecialLabelMapEntry")
	proto.RegisterType((*BatchGetUserGameSpecialLabelResponse_SimpleSpecialLabelInfo)(nil), "esport_skill.BatchGetUserGameSpecialLabelResponse.SimpleSpecialLabelInfo")
	proto.RegisterType((*BatchGetUserGameSpecialLabelResponse_UserGameSpecialLabelList)(nil), "esport_skill.BatchGetUserGameSpecialLabelResponse.UserGameSpecialLabelList")
	proto.RegisterType((*UserSpecialLabelItem)(nil), "esport_skill.UserSpecialLabelItem")
	proto.RegisterType((*UserGameSpecialLabelItem)(nil), "esport_skill.UserGameSpecialLabelItem")
	proto.RegisterType((*UpdateUserSpecialLabelRequest)(nil), "esport_skill.UpdateUserSpecialLabelRequest")
	proto.RegisterType((*UpdateUserSpecialLabelResponse)(nil), "esport_skill.UpdateUserSpecialLabelResponse")
	proto.RegisterType((*CheckLabelOrderRequest)(nil), "esport_skill.CheckLabelOrderRequest")
	proto.RegisterType((*CheckLabelOrderResponse)(nil), "esport_skill.CheckLabelOrderResponse")
	proto.RegisterType((*GetLabelIssuanceRecordRequest)(nil), "esport_skill.GetLabelIssuanceRecordRequest")
	proto.RegisterType((*SimpleIssuanceRecord)(nil), "esport_skill.SimpleIssuanceRecord")
	proto.RegisterType((*GetLabelIssuanceRecordResponse)(nil), "esport_skill.GetLabelIssuanceRecordResponse")
	proto.RegisterType((*GetSpecialLabelIssuanceRecordRequest)(nil), "esport_skill.GetSpecialLabelIssuanceRecordRequest")
	proto.RegisterType((*GetSpecialLabelIssuanceRecordResponse)(nil), "esport_skill.GetSpecialLabelIssuanceRecordResponse")
	proto.RegisterEnum("esport_skill.GAME_TYPE", GAME_TYPE_name, GAME_TYPE_value)
	proto.RegisterEnum("esport_skill.GAME_PRICING_TYPE", GAME_PRICING_TYPE_name, GAME_PRICING_TYPE_value)
	proto.RegisterEnum("esport_skill.GAME_PRICING_UNIT_TYPE", GAME_PRICING_UNIT_TYPE_name, GAME_PRICING_UNIT_TYPE_value)
	proto.RegisterEnum("esport_skill.AuditSource", AuditSource_name, AuditSource_value)
	proto.RegisterEnum("esport_skill.AuditSearchType", AuditSearchType_name, AuditSearchType_value)
	proto.RegisterEnum("esport_skill.EsportSwitchStatus", EsportSwitchStatus_name, EsportSwitchStatus_value)
	proto.RegisterEnum("esport_skill.LabelType", LabelType_name, LabelType_value)
	proto.RegisterEnum("esport_skill.IssuanceStatus", IssuanceStatus_name, IssuanceStatus_value)
	proto.RegisterEnum("esport_skill.FreezeType", FreezeType_name, FreezeType_value)
	proto.RegisterEnum("esport_skill.SpecialLabelType", SpecialLabelType_name, SpecialLabelType_value)
	proto.RegisterEnum("esport_skill.GameInformation_GAME_INFORMATION_TYPE", GameInformation_GAME_INFORMATION_TYPE_name, GameInformation_GAME_INFORMATION_TYPE_value)
	proto.RegisterEnum("esport_skill.GameInformation_GAME_INFORMATION_SELECT_TYPE", GameInformation_GAME_INFORMATION_SELECT_TYPE_name, GameInformation_GAME_INFORMATION_SELECT_TYPE_value)
	proto.RegisterEnum("esport_skill.AddUserAuditSkillRequest_IgnoreAuditResource", AddUserAuditSkillRequest_IgnoreAuditResource_name, AddUserAuditSkillRequest_IgnoreAuditResource_value)
	proto.RegisterEnum("esport_skill.CheckLabelOrderRequest_CheckType", CheckLabelOrderRequest_CheckType_name, CheckLabelOrderRequest_CheckType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// EsportSkillClient is the client API for EsportSkill service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EsportSkillClient interface {
	// ----------------- 运营后台接口 -----------------
	AddEsportGameConfig(ctx context.Context, in *AddEsportGameConfigRequest, opts ...grpc.CallOption) (*AddEsportGameConfigResponse, error)
	UpdateEsportGameConfig(ctx context.Context, in *UpdateEsportGameConfigRequest, opts ...grpc.CallOption) (*UpdateEsportGameConfigResponse, error)
	DeleteEsportGameConfig(ctx context.Context, in *DeleteEsportGameConfigRequest, opts ...grpc.CallOption) (*DeleteEsportGameConfigResponse, error)
	GetEsportGameConfigListByPage(ctx context.Context, in *GetEsportGameConfigListByPageRequest, opts ...grpc.CallOption) (*GetEsportGameConfigListByPageResponse, error)
	GetUserAuditSkill(ctx context.Context, in *GetUserAuditSkillRequest, opts ...grpc.CallOption) (*GetUserAuditSkillResponse, error)
	// 冻结大神技能
	FreezeCoachSkill(ctx context.Context, in *FreezeCoachSkillRequest, opts ...grpc.CallOption) (*FreezeCoachSkillResponse, error)
	// 解冻大神技能
	UnfreezeCoachSkill(ctx context.Context, in *UnfreezeCoachSkillRequest, opts ...grpc.CallOption) (*UnfreezeCoachSkillResponse, error)
	// 获取技能冻结操作列表
	GetSkillFreezeOperationList(ctx context.Context, in *GetSkillFreezeOperationListRequest, opts ...grpc.CallOption) (*GetSkillFreezeOperationListResponse, error)
	// 设置技能审核状态
	SetUserSkillAuditType(ctx context.Context, in *SetUserSkillAuditTypeRequest, opts ...grpc.CallOption) (*SetUserSkillAuditTypeResponse, error)
	// 获取所有游戏名片配置
	GetAllGameCardConfig(ctx context.Context, in *GetAllGameCardConfigRequest, opts ...grpc.CallOption) (*GetAllGameCardConfigResponse, error)
	// 获取用户当前的技能列表
	GetUserCurrentSkill(ctx context.Context, in *GetUserCurrentSkillRequest, opts ...grpc.CallOption) (*GetUserCurrentSkillResponse, error)
	BatchGetUserCurrentSkill(ctx context.Context, in *BatchGetUserCurrentSkillRequest, opts ...grpc.CallOption) (*BatchGetUserCurrentSkillResponse, error)
	ModifyUserSkill(ctx context.Context, in *ModifyUserSkillRequest, opts ...grpc.CallOption) (*ModifyUserSkillResponse, error)
	DelUserSkill(ctx context.Context, in *DelUserSkillRequest, opts ...grpc.CallOption) (*DelUserSkillResponse, error)
	GetGameList(ctx context.Context, in *GetGameListRequest, opts ...grpc.CallOption) (*GetGameListResponse, error)
	AddUserAuditSkill(ctx context.Context, in *AddUserAuditSkillRequest, opts ...grpc.CallOption) (*AddUserAuditSkillResponse, error)
	TestAddUserSkill(ctx context.Context, in *TestAddUserSkillRequest, opts ...grpc.CallOption) (*TestAddUserSkillResponse, error)
	BatchGetAuditSkill(ctx context.Context, in *BatchGetAuditSkillRequest, opts ...grpc.CallOption) (*BatchGetAuditSkillResponse, error)
	HandleGameUpdate(ctx context.Context, in *HandleGameUpdateRequest, opts ...grpc.CallOption) (*HandleGameUpdateResponse, error)
	// 获取开关状态
	GetSwitch(ctx context.Context, in *GetSwitchRequest, opts ...grpc.CallOption) (*GetSwitchResponse, error)
	// 设置开关状态
	SetSwitch(ctx context.Context, in *SetSwitchRequest, opts ...grpc.CallOption) (*SetSwitchResponse, error)
	// 获取顶部游戏列表
	GetTopGameList(ctx context.Context, in *GetTopGameListRequest, opts ...grpc.CallOption) (*GetTopGameListResponse, error)
	// 获取游戏信息
	GetGameDetailById(ctx context.Context, in *GetGameDetailByIdRequest, opts ...grpc.CallOption) (*GetGameDetailByIdResponse, error)
	// 批量获取游戏信息
	GetGameDetailByIds(ctx context.Context, in *GetGameDetailByIdsRequest, opts ...grpc.CallOption) (*GetGameDetailByIdsResponse, error)
	// 通过游戏名称批量获取游戏信息
	BatchGetGameInfoByNames(ctx context.Context, in *BatchGetGameInfoByNamesRequest, opts ...grpc.CallOption) (*BatchGetGameInfoByNamesResponse, error)
	// 设置技能风控状态
	SetUserSkillRiskAuditType(ctx context.Context, in *SetUserSkillRiskAuditTypeRequest, opts ...grpc.CallOption) (*SetUserSkillRiskAuditTypeResponse, error)
	// 获取全部游戏简略信息
	GetAllGameSimpleInfo(ctx context.Context, in *GetAllGameSimpleInfoRequest, opts ...grpc.CallOption) (*GetAllGameSimpleInfoResponse, error)
	GetUserSkillByGameId(ctx context.Context, in *GetUserSkillByGameIdRequest, opts ...grpc.CallOption) (*GetUserSkillByGameIdResponse, error)
	// 获取最低价格
	GetMinimumPrice(ctx context.Context, in *GetMinimumPriceRequest, opts ...grpc.CallOption) (*GetMinimumPriceResponse, error)
	// 获取技能状态
	GetUserSkillStatus(ctx context.Context, in *GetUserSkillStatusRequest, opts ...grpc.CallOption) (*GetUserSkillStatusResponse, error)
	// 获取技能冻结状态
	GetUserSkillFreezeStatus(ctx context.Context, in *GetUserSkillFreezeStatusRequest, opts ...grpc.CallOption) (*GetUserSkillFreezeStatusResponse, error)
	// 批量获取技能冻结状态
	BatGetUserSkillFreezeStatus(ctx context.Context, in *BatGetUserSkillFreezeStatusRequest, opts ...grpc.CallOption) (*BatGetUserSkillFreezeStatusResponse, error)
	// 获取用户知名选手信息
	BatchGetUserRenownedInfo(ctx context.Context, in *BatchGetUserRenownedInfoRequest, opts ...grpc.CallOption) (*BatchGetUserRenownedInfoResponse, error)
	// CreateLabel 创建大神或技能标识
	CreateLabel(ctx context.Context, in *CreateLabelRequest, opts ...grpc.CallOption) (*CreateLabelResponse, error)
	// EditLabel 编辑标识配置
	EditLabel(ctx context.Context, in *EditLabelRequest, opts ...grpc.CallOption) (*EditLabelResponse, error)
	// DeleteLabel 删除标识配置
	DeleteLabel(ctx context.Context, in *DeleteLabelRequest, opts ...grpc.CallOption) (*DeleteLabelResponse, error)
	// ListLabels 查询标识列表
	ListLabels(ctx context.Context, in *ListLabelsRequest, opts ...grpc.CallOption) (*ListLabelsResponse, error)
	// IssueLabel 发放标识
	IssueLabel(ctx context.Context, in *IssueLabelRequest, opts ...grpc.CallOption) (*IssueLabelResponse, error)
	// RevokeLabel 用于回收已发放标识。
	RevokeLabel(ctx context.Context, in *RevokeLabelRequest, opts ...grpc.CallOption) (*RevokeLabelResponse, error)
	// QueryIssuanceRecords 用于查询发放记录列表。
	QueryIssuanceRecords(ctx context.Context, in *QueryIssuanceRecordsRequest, opts ...grpc.CallOption) (*QueryIssuanceRecordsResponse, error)
	// AddRenownedPlayer 发放知名选手标识
	AddRenownedPlayer(ctx context.Context, in *AddRenownedPlayerRequest, opts ...grpc.CallOption) (*AddRenownedPlayerResponse, error)
	// ListRenownedPlayers 查询知名选手列表
	ListRenownedPlayers(ctx context.Context, in *ListRenownedPlayersRequest, opts ...grpc.CallOption) (*ListRenownedPlayersResponse, error)
	// BatchRemoveRenownedPlayers 批量移除知名选手
	BatchRemoveRenownedPlayers(ctx context.Context, in *BatchRemoveRenownedPlayersRequest, opts ...grpc.CallOption) (*BatchRemoveRenownedPlayersResponse, error)
	// UpdateRenownedPlayerRequest 更新知名选手信息
	UpdateRenownedPlayer(ctx context.Context, in *UpdateRenownedPlayerRequest, opts ...grpc.CallOption) (*UpdateRenownedPlayerResponse, error)
	// 获取基础定价
	GetBasePriceSetting(ctx context.Context, in *GetBasePriceSettingRequest, opts ...grpc.CallOption) (*GetBasePriceSettingResponse, error)
	// 设置基础定价
	SetBasePriceSetting(ctx context.Context, in *SetBasePriceSettingRequest, opts ...grpc.CallOption) (*SetBasePriceSettingResponse, error)
	// 计算教练价格
	CalculatePrice(ctx context.Context, in *CalculatePriceRequest, opts ...grpc.CallOption) (*CalculatePriceResponse, error)
	// 计算教练的技能列表的价格
	CalculatePriceByGames(ctx context.Context, in *CalculatePriceByGamesRequest, opts ...grpc.CallOption) (*CalculatePriceByGamesResponse, error)
	// 获取教练当前可申请的标识列表
	GetCoachApplicableLabels(ctx context.Context, in *GetCoachApplicableLabelsRequest, opts ...grpc.CallOption) (*GetCoachApplicableLabelsResponse, error)
	// 检查教练身份和检查是否拥有游戏技能
	CheckCoachIdentityAndSkill(ctx context.Context, in *CheckCoachIdentityAndSkillRequest, opts ...grpc.CallOption) (*CheckCoachIdentityAndSkillResponse, error)
	// 批量获取指定游戏的多个教练的标识列表
	BatchGetCoachLabelsForGame(ctx context.Context, in *BatchGetCoachLabelsForGameRequest, opts ...grpc.CallOption) (*BatchGetCoachLabelsForGameResponse, error)
	// 批量检查教练是否拥有指定的技能
	BatchCheckCoachHasGame(ctx context.Context, in *BatchCheckCoachHasGameRequest, opts ...grpc.CallOption) (*BatchCheckCoachHasGameResponse, error)
	// SetGameGuaranteeStatus
	SetGameGuaranteeStatus(ctx context.Context, in *SetGameGuaranteeStatusRequest, opts ...grpc.CallOption) (*SetGameGuaranteeStatusResponse, error)
	// debug接口，检查大神是否拥有开启包赢权限
	DebugCheckGuaranteeWinPermission(ctx context.Context, in *DebugCheckGuaranteeWinPermissionRequest, opts ...grpc.CallOption) (*DebugCheckGuaranteeWinPermissionResponse, error)
	// ========================== 特色标签 =====================
	// 获取特色标签列表
	GetSpecialLabelList(ctx context.Context, in *GetSpecialLabelListRequest, opts ...grpc.CallOption) (*GetSpecialLabelListResponse, error)
	// 更新用户特色标签
	UpdateUserSpecialLabel(ctx context.Context, in *UpdateUserSpecialLabelRequest, opts ...grpc.CallOption) (*UpdateUserSpecialLabelResponse, error)
	// 批量获取用户特色标签
	BatchGetUserSpecialLabel(ctx context.Context, in *BatchGetUserSpecialLabelRequest, opts ...grpc.CallOption) (*BatchGetUserSpecialLabelResponse, error)
	// 检查标签展示顺序
	CheckLabelOrder(ctx context.Context, in *CheckLabelOrderRequest, opts ...grpc.CallOption) (*CheckLabelOrderResponse, error)
	// 获取用户在游戏x中的特色标签
	BatchGetUserGameSpecialLabel(ctx context.Context, in *BatchGetUserGameSpecialLabelRequest, opts ...grpc.CallOption) (*BatchGetUserGameSpecialLabelResponse, error)
	// 获取标签发放记录
	GetLabelIssueRecord(ctx context.Context, in *GetLabelIssuanceRecordRequest, opts ...grpc.CallOption) (*GetLabelIssuanceRecordResponse, error)
	// 获取特色标签发放记录
	GetSpecialIssueRecord(ctx context.Context, in *GetSpecialLabelIssuanceRecordRequest, opts ...grpc.CallOption) (*GetSpecialLabelIssuanceRecordResponse, error)
	// 用户设置标识是否参与加价开关
	SetLabelPriceSwitch(ctx context.Context, in *SetLabelPriceSwitchRequest, opts ...grpc.CallOption) (*SetLabelPriceSwitchResponse, error)
}

type esportSkillClient struct {
	cc *grpc.ClientConn
}

func NewEsportSkillClient(cc *grpc.ClientConn) EsportSkillClient {
	return &esportSkillClient{cc}
}

func (c *esportSkillClient) AddEsportGameConfig(ctx context.Context, in *AddEsportGameConfigRequest, opts ...grpc.CallOption) (*AddEsportGameConfigResponse, error) {
	out := new(AddEsportGameConfigResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/AddEsportGameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) UpdateEsportGameConfig(ctx context.Context, in *UpdateEsportGameConfigRequest, opts ...grpc.CallOption) (*UpdateEsportGameConfigResponse, error) {
	out := new(UpdateEsportGameConfigResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/UpdateEsportGameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) DeleteEsportGameConfig(ctx context.Context, in *DeleteEsportGameConfigRequest, opts ...grpc.CallOption) (*DeleteEsportGameConfigResponse, error) {
	out := new(DeleteEsportGameConfigResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/DeleteEsportGameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetEsportGameConfigListByPage(ctx context.Context, in *GetEsportGameConfigListByPageRequest, opts ...grpc.CallOption) (*GetEsportGameConfigListByPageResponse, error) {
	out := new(GetEsportGameConfigListByPageResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetEsportGameConfigListByPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetUserAuditSkill(ctx context.Context, in *GetUserAuditSkillRequest, opts ...grpc.CallOption) (*GetUserAuditSkillResponse, error) {
	out := new(GetUserAuditSkillResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetUserAuditSkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) FreezeCoachSkill(ctx context.Context, in *FreezeCoachSkillRequest, opts ...grpc.CallOption) (*FreezeCoachSkillResponse, error) {
	out := new(FreezeCoachSkillResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/FreezeCoachSkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) UnfreezeCoachSkill(ctx context.Context, in *UnfreezeCoachSkillRequest, opts ...grpc.CallOption) (*UnfreezeCoachSkillResponse, error) {
	out := new(UnfreezeCoachSkillResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/UnfreezeCoachSkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetSkillFreezeOperationList(ctx context.Context, in *GetSkillFreezeOperationListRequest, opts ...grpc.CallOption) (*GetSkillFreezeOperationListResponse, error) {
	out := new(GetSkillFreezeOperationListResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetSkillFreezeOperationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) SetUserSkillAuditType(ctx context.Context, in *SetUserSkillAuditTypeRequest, opts ...grpc.CallOption) (*SetUserSkillAuditTypeResponse, error) {
	out := new(SetUserSkillAuditTypeResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/SetUserSkillAuditType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetAllGameCardConfig(ctx context.Context, in *GetAllGameCardConfigRequest, opts ...grpc.CallOption) (*GetAllGameCardConfigResponse, error) {
	out := new(GetAllGameCardConfigResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetAllGameCardConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetUserCurrentSkill(ctx context.Context, in *GetUserCurrentSkillRequest, opts ...grpc.CallOption) (*GetUserCurrentSkillResponse, error) {
	out := new(GetUserCurrentSkillResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetUserCurrentSkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) BatchGetUserCurrentSkill(ctx context.Context, in *BatchGetUserCurrentSkillRequest, opts ...grpc.CallOption) (*BatchGetUserCurrentSkillResponse, error) {
	out := new(BatchGetUserCurrentSkillResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/BatchGetUserCurrentSkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) ModifyUserSkill(ctx context.Context, in *ModifyUserSkillRequest, opts ...grpc.CallOption) (*ModifyUserSkillResponse, error) {
	out := new(ModifyUserSkillResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/ModifyUserSkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) DelUserSkill(ctx context.Context, in *DelUserSkillRequest, opts ...grpc.CallOption) (*DelUserSkillResponse, error) {
	out := new(DelUserSkillResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/DelUserSkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetGameList(ctx context.Context, in *GetGameListRequest, opts ...grpc.CallOption) (*GetGameListResponse, error) {
	out := new(GetGameListResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) AddUserAuditSkill(ctx context.Context, in *AddUserAuditSkillRequest, opts ...grpc.CallOption) (*AddUserAuditSkillResponse, error) {
	out := new(AddUserAuditSkillResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/AddUserAuditSkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) TestAddUserSkill(ctx context.Context, in *TestAddUserSkillRequest, opts ...grpc.CallOption) (*TestAddUserSkillResponse, error) {
	out := new(TestAddUserSkillResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/TestAddUserSkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) BatchGetAuditSkill(ctx context.Context, in *BatchGetAuditSkillRequest, opts ...grpc.CallOption) (*BatchGetAuditSkillResponse, error) {
	out := new(BatchGetAuditSkillResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/BatchGetAuditSkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) HandleGameUpdate(ctx context.Context, in *HandleGameUpdateRequest, opts ...grpc.CallOption) (*HandleGameUpdateResponse, error) {
	out := new(HandleGameUpdateResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/HandleGameUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetSwitch(ctx context.Context, in *GetSwitchRequest, opts ...grpc.CallOption) (*GetSwitchResponse, error) {
	out := new(GetSwitchResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) SetSwitch(ctx context.Context, in *SetSwitchRequest, opts ...grpc.CallOption) (*SetSwitchResponse, error) {
	out := new(SetSwitchResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/SetSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetTopGameList(ctx context.Context, in *GetTopGameListRequest, opts ...grpc.CallOption) (*GetTopGameListResponse, error) {
	out := new(GetTopGameListResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetTopGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetGameDetailById(ctx context.Context, in *GetGameDetailByIdRequest, opts ...grpc.CallOption) (*GetGameDetailByIdResponse, error) {
	out := new(GetGameDetailByIdResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetGameDetailById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetGameDetailByIds(ctx context.Context, in *GetGameDetailByIdsRequest, opts ...grpc.CallOption) (*GetGameDetailByIdsResponse, error) {
	out := new(GetGameDetailByIdsResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetGameDetailByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) BatchGetGameInfoByNames(ctx context.Context, in *BatchGetGameInfoByNamesRequest, opts ...grpc.CallOption) (*BatchGetGameInfoByNamesResponse, error) {
	out := new(BatchGetGameInfoByNamesResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/BatchGetGameInfoByNames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) SetUserSkillRiskAuditType(ctx context.Context, in *SetUserSkillRiskAuditTypeRequest, opts ...grpc.CallOption) (*SetUserSkillRiskAuditTypeResponse, error) {
	out := new(SetUserSkillRiskAuditTypeResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/SetUserSkillRiskAuditType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetAllGameSimpleInfo(ctx context.Context, in *GetAllGameSimpleInfoRequest, opts ...grpc.CallOption) (*GetAllGameSimpleInfoResponse, error) {
	out := new(GetAllGameSimpleInfoResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetAllGameSimpleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetUserSkillByGameId(ctx context.Context, in *GetUserSkillByGameIdRequest, opts ...grpc.CallOption) (*GetUserSkillByGameIdResponse, error) {
	out := new(GetUserSkillByGameIdResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetUserSkillByGameId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetMinimumPrice(ctx context.Context, in *GetMinimumPriceRequest, opts ...grpc.CallOption) (*GetMinimumPriceResponse, error) {
	out := new(GetMinimumPriceResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetMinimumPrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetUserSkillStatus(ctx context.Context, in *GetUserSkillStatusRequest, opts ...grpc.CallOption) (*GetUserSkillStatusResponse, error) {
	out := new(GetUserSkillStatusResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetUserSkillStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetUserSkillFreezeStatus(ctx context.Context, in *GetUserSkillFreezeStatusRequest, opts ...grpc.CallOption) (*GetUserSkillFreezeStatusResponse, error) {
	out := new(GetUserSkillFreezeStatusResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetUserSkillFreezeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) BatGetUserSkillFreezeStatus(ctx context.Context, in *BatGetUserSkillFreezeStatusRequest, opts ...grpc.CallOption) (*BatGetUserSkillFreezeStatusResponse, error) {
	out := new(BatGetUserSkillFreezeStatusResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/BatGetUserSkillFreezeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) BatchGetUserRenownedInfo(ctx context.Context, in *BatchGetUserRenownedInfoRequest, opts ...grpc.CallOption) (*BatchGetUserRenownedInfoResponse, error) {
	out := new(BatchGetUserRenownedInfoResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/BatchGetUserRenownedInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) CreateLabel(ctx context.Context, in *CreateLabelRequest, opts ...grpc.CallOption) (*CreateLabelResponse, error) {
	out := new(CreateLabelResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/CreateLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) EditLabel(ctx context.Context, in *EditLabelRequest, opts ...grpc.CallOption) (*EditLabelResponse, error) {
	out := new(EditLabelResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/EditLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) DeleteLabel(ctx context.Context, in *DeleteLabelRequest, opts ...grpc.CallOption) (*DeleteLabelResponse, error) {
	out := new(DeleteLabelResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/DeleteLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) ListLabels(ctx context.Context, in *ListLabelsRequest, opts ...grpc.CallOption) (*ListLabelsResponse, error) {
	out := new(ListLabelsResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/ListLabels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) IssueLabel(ctx context.Context, in *IssueLabelRequest, opts ...grpc.CallOption) (*IssueLabelResponse, error) {
	out := new(IssueLabelResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/IssueLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) RevokeLabel(ctx context.Context, in *RevokeLabelRequest, opts ...grpc.CallOption) (*RevokeLabelResponse, error) {
	out := new(RevokeLabelResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/RevokeLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) QueryIssuanceRecords(ctx context.Context, in *QueryIssuanceRecordsRequest, opts ...grpc.CallOption) (*QueryIssuanceRecordsResponse, error) {
	out := new(QueryIssuanceRecordsResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/QueryIssuanceRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) AddRenownedPlayer(ctx context.Context, in *AddRenownedPlayerRequest, opts ...grpc.CallOption) (*AddRenownedPlayerResponse, error) {
	out := new(AddRenownedPlayerResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/AddRenownedPlayer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) ListRenownedPlayers(ctx context.Context, in *ListRenownedPlayersRequest, opts ...grpc.CallOption) (*ListRenownedPlayersResponse, error) {
	out := new(ListRenownedPlayersResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/ListRenownedPlayers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) BatchRemoveRenownedPlayers(ctx context.Context, in *BatchRemoveRenownedPlayersRequest, opts ...grpc.CallOption) (*BatchRemoveRenownedPlayersResponse, error) {
	out := new(BatchRemoveRenownedPlayersResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/BatchRemoveRenownedPlayers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) UpdateRenownedPlayer(ctx context.Context, in *UpdateRenownedPlayerRequest, opts ...grpc.CallOption) (*UpdateRenownedPlayerResponse, error) {
	out := new(UpdateRenownedPlayerResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/UpdateRenownedPlayer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetBasePriceSetting(ctx context.Context, in *GetBasePriceSettingRequest, opts ...grpc.CallOption) (*GetBasePriceSettingResponse, error) {
	out := new(GetBasePriceSettingResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetBasePriceSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) SetBasePriceSetting(ctx context.Context, in *SetBasePriceSettingRequest, opts ...grpc.CallOption) (*SetBasePriceSettingResponse, error) {
	out := new(SetBasePriceSettingResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/SetBasePriceSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) CalculatePrice(ctx context.Context, in *CalculatePriceRequest, opts ...grpc.CallOption) (*CalculatePriceResponse, error) {
	out := new(CalculatePriceResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/CalculatePrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) CalculatePriceByGames(ctx context.Context, in *CalculatePriceByGamesRequest, opts ...grpc.CallOption) (*CalculatePriceByGamesResponse, error) {
	out := new(CalculatePriceByGamesResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/CalculatePriceByGames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetCoachApplicableLabels(ctx context.Context, in *GetCoachApplicableLabelsRequest, opts ...grpc.CallOption) (*GetCoachApplicableLabelsResponse, error) {
	out := new(GetCoachApplicableLabelsResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetCoachApplicableLabels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) CheckCoachIdentityAndSkill(ctx context.Context, in *CheckCoachIdentityAndSkillRequest, opts ...grpc.CallOption) (*CheckCoachIdentityAndSkillResponse, error) {
	out := new(CheckCoachIdentityAndSkillResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/CheckCoachIdentityAndSkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) BatchGetCoachLabelsForGame(ctx context.Context, in *BatchGetCoachLabelsForGameRequest, opts ...grpc.CallOption) (*BatchGetCoachLabelsForGameResponse, error) {
	out := new(BatchGetCoachLabelsForGameResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/BatchGetCoachLabelsForGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) BatchCheckCoachHasGame(ctx context.Context, in *BatchCheckCoachHasGameRequest, opts ...grpc.CallOption) (*BatchCheckCoachHasGameResponse, error) {
	out := new(BatchCheckCoachHasGameResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/BatchCheckCoachHasGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) SetGameGuaranteeStatus(ctx context.Context, in *SetGameGuaranteeStatusRequest, opts ...grpc.CallOption) (*SetGameGuaranteeStatusResponse, error) {
	out := new(SetGameGuaranteeStatusResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/SetGameGuaranteeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) DebugCheckGuaranteeWinPermission(ctx context.Context, in *DebugCheckGuaranteeWinPermissionRequest, opts ...grpc.CallOption) (*DebugCheckGuaranteeWinPermissionResponse, error) {
	out := new(DebugCheckGuaranteeWinPermissionResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/DebugCheckGuaranteeWinPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetSpecialLabelList(ctx context.Context, in *GetSpecialLabelListRequest, opts ...grpc.CallOption) (*GetSpecialLabelListResponse, error) {
	out := new(GetSpecialLabelListResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetSpecialLabelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) UpdateUserSpecialLabel(ctx context.Context, in *UpdateUserSpecialLabelRequest, opts ...grpc.CallOption) (*UpdateUserSpecialLabelResponse, error) {
	out := new(UpdateUserSpecialLabelResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/UpdateUserSpecialLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) BatchGetUserSpecialLabel(ctx context.Context, in *BatchGetUserSpecialLabelRequest, opts ...grpc.CallOption) (*BatchGetUserSpecialLabelResponse, error) {
	out := new(BatchGetUserSpecialLabelResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/BatchGetUserSpecialLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) CheckLabelOrder(ctx context.Context, in *CheckLabelOrderRequest, opts ...grpc.CallOption) (*CheckLabelOrderResponse, error) {
	out := new(CheckLabelOrderResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/CheckLabelOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) BatchGetUserGameSpecialLabel(ctx context.Context, in *BatchGetUserGameSpecialLabelRequest, opts ...grpc.CallOption) (*BatchGetUserGameSpecialLabelResponse, error) {
	out := new(BatchGetUserGameSpecialLabelResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/BatchGetUserGameSpecialLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetLabelIssueRecord(ctx context.Context, in *GetLabelIssuanceRecordRequest, opts ...grpc.CallOption) (*GetLabelIssuanceRecordResponse, error) {
	out := new(GetLabelIssuanceRecordResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetLabelIssueRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) GetSpecialIssueRecord(ctx context.Context, in *GetSpecialLabelIssuanceRecordRequest, opts ...grpc.CallOption) (*GetSpecialLabelIssuanceRecordResponse, error) {
	out := new(GetSpecialLabelIssuanceRecordResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/GetSpecialIssueRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportSkillClient) SetLabelPriceSwitch(ctx context.Context, in *SetLabelPriceSwitchRequest, opts ...grpc.CallOption) (*SetLabelPriceSwitchResponse, error) {
	out := new(SetLabelPriceSwitchResponse)
	err := c.cc.Invoke(ctx, "/esport_skill.EsportSkill/SetLabelPriceSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EsportSkillServer is the server API for EsportSkill service.
type EsportSkillServer interface {
	// ----------------- 运营后台接口 -----------------
	AddEsportGameConfig(context.Context, *AddEsportGameConfigRequest) (*AddEsportGameConfigResponse, error)
	UpdateEsportGameConfig(context.Context, *UpdateEsportGameConfigRequest) (*UpdateEsportGameConfigResponse, error)
	DeleteEsportGameConfig(context.Context, *DeleteEsportGameConfigRequest) (*DeleteEsportGameConfigResponse, error)
	GetEsportGameConfigListByPage(context.Context, *GetEsportGameConfigListByPageRequest) (*GetEsportGameConfigListByPageResponse, error)
	GetUserAuditSkill(context.Context, *GetUserAuditSkillRequest) (*GetUserAuditSkillResponse, error)
	// 冻结大神技能
	FreezeCoachSkill(context.Context, *FreezeCoachSkillRequest) (*FreezeCoachSkillResponse, error)
	// 解冻大神技能
	UnfreezeCoachSkill(context.Context, *UnfreezeCoachSkillRequest) (*UnfreezeCoachSkillResponse, error)
	// 获取技能冻结操作列表
	GetSkillFreezeOperationList(context.Context, *GetSkillFreezeOperationListRequest) (*GetSkillFreezeOperationListResponse, error)
	// 设置技能审核状态
	SetUserSkillAuditType(context.Context, *SetUserSkillAuditTypeRequest) (*SetUserSkillAuditTypeResponse, error)
	// 获取所有游戏名片配置
	GetAllGameCardConfig(context.Context, *GetAllGameCardConfigRequest) (*GetAllGameCardConfigResponse, error)
	// 获取用户当前的技能列表
	GetUserCurrentSkill(context.Context, *GetUserCurrentSkillRequest) (*GetUserCurrentSkillResponse, error)
	BatchGetUserCurrentSkill(context.Context, *BatchGetUserCurrentSkillRequest) (*BatchGetUserCurrentSkillResponse, error)
	ModifyUserSkill(context.Context, *ModifyUserSkillRequest) (*ModifyUserSkillResponse, error)
	DelUserSkill(context.Context, *DelUserSkillRequest) (*DelUserSkillResponse, error)
	GetGameList(context.Context, *GetGameListRequest) (*GetGameListResponse, error)
	AddUserAuditSkill(context.Context, *AddUserAuditSkillRequest) (*AddUserAuditSkillResponse, error)
	TestAddUserSkill(context.Context, *TestAddUserSkillRequest) (*TestAddUserSkillResponse, error)
	BatchGetAuditSkill(context.Context, *BatchGetAuditSkillRequest) (*BatchGetAuditSkillResponse, error)
	HandleGameUpdate(context.Context, *HandleGameUpdateRequest) (*HandleGameUpdateResponse, error)
	// 获取开关状态
	GetSwitch(context.Context, *GetSwitchRequest) (*GetSwitchResponse, error)
	// 设置开关状态
	SetSwitch(context.Context, *SetSwitchRequest) (*SetSwitchResponse, error)
	// 获取顶部游戏列表
	GetTopGameList(context.Context, *GetTopGameListRequest) (*GetTopGameListResponse, error)
	// 获取游戏信息
	GetGameDetailById(context.Context, *GetGameDetailByIdRequest) (*GetGameDetailByIdResponse, error)
	// 批量获取游戏信息
	GetGameDetailByIds(context.Context, *GetGameDetailByIdsRequest) (*GetGameDetailByIdsResponse, error)
	// 通过游戏名称批量获取游戏信息
	BatchGetGameInfoByNames(context.Context, *BatchGetGameInfoByNamesRequest) (*BatchGetGameInfoByNamesResponse, error)
	// 设置技能风控状态
	SetUserSkillRiskAuditType(context.Context, *SetUserSkillRiskAuditTypeRequest) (*SetUserSkillRiskAuditTypeResponse, error)
	// 获取全部游戏简略信息
	GetAllGameSimpleInfo(context.Context, *GetAllGameSimpleInfoRequest) (*GetAllGameSimpleInfoResponse, error)
	GetUserSkillByGameId(context.Context, *GetUserSkillByGameIdRequest) (*GetUserSkillByGameIdResponse, error)
	// 获取最低价格
	GetMinimumPrice(context.Context, *GetMinimumPriceRequest) (*GetMinimumPriceResponse, error)
	// 获取技能状态
	GetUserSkillStatus(context.Context, *GetUserSkillStatusRequest) (*GetUserSkillStatusResponse, error)
	// 获取技能冻结状态
	GetUserSkillFreezeStatus(context.Context, *GetUserSkillFreezeStatusRequest) (*GetUserSkillFreezeStatusResponse, error)
	// 批量获取技能冻结状态
	BatGetUserSkillFreezeStatus(context.Context, *BatGetUserSkillFreezeStatusRequest) (*BatGetUserSkillFreezeStatusResponse, error)
	// 获取用户知名选手信息
	BatchGetUserRenownedInfo(context.Context, *BatchGetUserRenownedInfoRequest) (*BatchGetUserRenownedInfoResponse, error)
	// CreateLabel 创建大神或技能标识
	CreateLabel(context.Context, *CreateLabelRequest) (*CreateLabelResponse, error)
	// EditLabel 编辑标识配置
	EditLabel(context.Context, *EditLabelRequest) (*EditLabelResponse, error)
	// DeleteLabel 删除标识配置
	DeleteLabel(context.Context, *DeleteLabelRequest) (*DeleteLabelResponse, error)
	// ListLabels 查询标识列表
	ListLabels(context.Context, *ListLabelsRequest) (*ListLabelsResponse, error)
	// IssueLabel 发放标识
	IssueLabel(context.Context, *IssueLabelRequest) (*IssueLabelResponse, error)
	// RevokeLabel 用于回收已发放标识。
	RevokeLabel(context.Context, *RevokeLabelRequest) (*RevokeLabelResponse, error)
	// QueryIssuanceRecords 用于查询发放记录列表。
	QueryIssuanceRecords(context.Context, *QueryIssuanceRecordsRequest) (*QueryIssuanceRecordsResponse, error)
	// AddRenownedPlayer 发放知名选手标识
	AddRenownedPlayer(context.Context, *AddRenownedPlayerRequest) (*AddRenownedPlayerResponse, error)
	// ListRenownedPlayers 查询知名选手列表
	ListRenownedPlayers(context.Context, *ListRenownedPlayersRequest) (*ListRenownedPlayersResponse, error)
	// BatchRemoveRenownedPlayers 批量移除知名选手
	BatchRemoveRenownedPlayers(context.Context, *BatchRemoveRenownedPlayersRequest) (*BatchRemoveRenownedPlayersResponse, error)
	// UpdateRenownedPlayerRequest 更新知名选手信息
	UpdateRenownedPlayer(context.Context, *UpdateRenownedPlayerRequest) (*UpdateRenownedPlayerResponse, error)
	// 获取基础定价
	GetBasePriceSetting(context.Context, *GetBasePriceSettingRequest) (*GetBasePriceSettingResponse, error)
	// 设置基础定价
	SetBasePriceSetting(context.Context, *SetBasePriceSettingRequest) (*SetBasePriceSettingResponse, error)
	// 计算教练价格
	CalculatePrice(context.Context, *CalculatePriceRequest) (*CalculatePriceResponse, error)
	// 计算教练的技能列表的价格
	CalculatePriceByGames(context.Context, *CalculatePriceByGamesRequest) (*CalculatePriceByGamesResponse, error)
	// 获取教练当前可申请的标识列表
	GetCoachApplicableLabels(context.Context, *GetCoachApplicableLabelsRequest) (*GetCoachApplicableLabelsResponse, error)
	// 检查教练身份和检查是否拥有游戏技能
	CheckCoachIdentityAndSkill(context.Context, *CheckCoachIdentityAndSkillRequest) (*CheckCoachIdentityAndSkillResponse, error)
	// 批量获取指定游戏的多个教练的标识列表
	BatchGetCoachLabelsForGame(context.Context, *BatchGetCoachLabelsForGameRequest) (*BatchGetCoachLabelsForGameResponse, error)
	// 批量检查教练是否拥有指定的技能
	BatchCheckCoachHasGame(context.Context, *BatchCheckCoachHasGameRequest) (*BatchCheckCoachHasGameResponse, error)
	// SetGameGuaranteeStatus
	SetGameGuaranteeStatus(context.Context, *SetGameGuaranteeStatusRequest) (*SetGameGuaranteeStatusResponse, error)
	// debug接口，检查大神是否拥有开启包赢权限
	DebugCheckGuaranteeWinPermission(context.Context, *DebugCheckGuaranteeWinPermissionRequest) (*DebugCheckGuaranteeWinPermissionResponse, error)
	// ========================== 特色标签 =====================
	// 获取特色标签列表
	GetSpecialLabelList(context.Context, *GetSpecialLabelListRequest) (*GetSpecialLabelListResponse, error)
	// 更新用户特色标签
	UpdateUserSpecialLabel(context.Context, *UpdateUserSpecialLabelRequest) (*UpdateUserSpecialLabelResponse, error)
	// 批量获取用户特色标签
	BatchGetUserSpecialLabel(context.Context, *BatchGetUserSpecialLabelRequest) (*BatchGetUserSpecialLabelResponse, error)
	// 检查标签展示顺序
	CheckLabelOrder(context.Context, *CheckLabelOrderRequest) (*CheckLabelOrderResponse, error)
	// 获取用户在游戏x中的特色标签
	BatchGetUserGameSpecialLabel(context.Context, *BatchGetUserGameSpecialLabelRequest) (*BatchGetUserGameSpecialLabelResponse, error)
	// 获取标签发放记录
	GetLabelIssueRecord(context.Context, *GetLabelIssuanceRecordRequest) (*GetLabelIssuanceRecordResponse, error)
	// 获取特色标签发放记录
	GetSpecialIssueRecord(context.Context, *GetSpecialLabelIssuanceRecordRequest) (*GetSpecialLabelIssuanceRecordResponse, error)
	// 用户设置标识是否参与加价开关
	SetLabelPriceSwitch(context.Context, *SetLabelPriceSwitchRequest) (*SetLabelPriceSwitchResponse, error)
}

func RegisterEsportSkillServer(s *grpc.Server, srv EsportSkillServer) {
	s.RegisterService(&_EsportSkill_serviceDesc, srv)
}

func _EsportSkill_AddEsportGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddEsportGameConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).AddEsportGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/AddEsportGameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).AddEsportGameConfig(ctx, req.(*AddEsportGameConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_UpdateEsportGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEsportGameConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).UpdateEsportGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/UpdateEsportGameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).UpdateEsportGameConfig(ctx, req.(*UpdateEsportGameConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_DeleteEsportGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEsportGameConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).DeleteEsportGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/DeleteEsportGameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).DeleteEsportGameConfig(ctx, req.(*DeleteEsportGameConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetEsportGameConfigListByPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEsportGameConfigListByPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetEsportGameConfigListByPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetEsportGameConfigListByPage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetEsportGameConfigListByPage(ctx, req.(*GetEsportGameConfigListByPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetUserAuditSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAuditSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetUserAuditSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetUserAuditSkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetUserAuditSkill(ctx, req.(*GetUserAuditSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_FreezeCoachSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreezeCoachSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).FreezeCoachSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/FreezeCoachSkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).FreezeCoachSkill(ctx, req.(*FreezeCoachSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_UnfreezeCoachSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnfreezeCoachSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).UnfreezeCoachSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/UnfreezeCoachSkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).UnfreezeCoachSkill(ctx, req.(*UnfreezeCoachSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetSkillFreezeOperationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSkillFreezeOperationListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetSkillFreezeOperationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetSkillFreezeOperationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetSkillFreezeOperationList(ctx, req.(*GetSkillFreezeOperationListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_SetUserSkillAuditType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserSkillAuditTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).SetUserSkillAuditType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/SetUserSkillAuditType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).SetUserSkillAuditType(ctx, req.(*SetUserSkillAuditTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetAllGameCardConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllGameCardConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetAllGameCardConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetAllGameCardConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetAllGameCardConfig(ctx, req.(*GetAllGameCardConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetUserCurrentSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCurrentSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetUserCurrentSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetUserCurrentSkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetUserCurrentSkill(ctx, req.(*GetUserCurrentSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_BatchGetUserCurrentSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserCurrentSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).BatchGetUserCurrentSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/BatchGetUserCurrentSkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).BatchGetUserCurrentSkill(ctx, req.(*BatchGetUserCurrentSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_ModifyUserSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyUserSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).ModifyUserSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/ModifyUserSkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).ModifyUserSkill(ctx, req.(*ModifyUserSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_DelUserSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelUserSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).DelUserSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/DelUserSkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).DelUserSkill(ctx, req.(*DelUserSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetGameList(ctx, req.(*GetGameListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_AddUserAuditSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserAuditSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).AddUserAuditSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/AddUserAuditSkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).AddUserAuditSkill(ctx, req.(*AddUserAuditSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_TestAddUserSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestAddUserSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).TestAddUserSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/TestAddUserSkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).TestAddUserSkill(ctx, req.(*TestAddUserSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_BatchGetAuditSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAuditSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).BatchGetAuditSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/BatchGetAuditSkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).BatchGetAuditSkill(ctx, req.(*BatchGetAuditSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_HandleGameUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleGameUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).HandleGameUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/HandleGameUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).HandleGameUpdate(ctx, req.(*HandleGameUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetSwitch(ctx, req.(*GetSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_SetSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).SetSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/SetSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).SetSwitch(ctx, req.(*SetSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetTopGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopGameListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetTopGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetTopGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetTopGameList(ctx, req.(*GetTopGameListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetGameDetailById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameDetailByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetGameDetailById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetGameDetailById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetGameDetailById(ctx, req.(*GetGameDetailByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetGameDetailByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameDetailByIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetGameDetailByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetGameDetailByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetGameDetailByIds(ctx, req.(*GetGameDetailByIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_BatchGetGameInfoByNames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGameInfoByNamesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).BatchGetGameInfoByNames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/BatchGetGameInfoByNames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).BatchGetGameInfoByNames(ctx, req.(*BatchGetGameInfoByNamesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_SetUserSkillRiskAuditType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserSkillRiskAuditTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).SetUserSkillRiskAuditType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/SetUserSkillRiskAuditType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).SetUserSkillRiskAuditType(ctx, req.(*SetUserSkillRiskAuditTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetAllGameSimpleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllGameSimpleInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetAllGameSimpleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetAllGameSimpleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetAllGameSimpleInfo(ctx, req.(*GetAllGameSimpleInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetUserSkillByGameId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSkillByGameIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetUserSkillByGameId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetUserSkillByGameId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetUserSkillByGameId(ctx, req.(*GetUserSkillByGameIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetMinimumPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMinimumPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetMinimumPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetMinimumPrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetMinimumPrice(ctx, req.(*GetMinimumPriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetUserSkillStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSkillStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetUserSkillStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetUserSkillStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetUserSkillStatus(ctx, req.(*GetUserSkillStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetUserSkillFreezeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSkillFreezeStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetUserSkillFreezeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetUserSkillFreezeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetUserSkillFreezeStatus(ctx, req.(*GetUserSkillFreezeStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_BatGetUserSkillFreezeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetUserSkillFreezeStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).BatGetUserSkillFreezeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/BatGetUserSkillFreezeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).BatGetUserSkillFreezeStatus(ctx, req.(*BatGetUserSkillFreezeStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_BatchGetUserRenownedInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserRenownedInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).BatchGetUserRenownedInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/BatchGetUserRenownedInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).BatchGetUserRenownedInfo(ctx, req.(*BatchGetUserRenownedInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_CreateLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).CreateLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/CreateLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).CreateLabel(ctx, req.(*CreateLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_EditLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).EditLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/EditLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).EditLabel(ctx, req.(*EditLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_DeleteLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).DeleteLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/DeleteLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).DeleteLabel(ctx, req.(*DeleteLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_ListLabels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLabelsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).ListLabels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/ListLabels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).ListLabels(ctx, req.(*ListLabelsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_IssueLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IssueLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).IssueLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/IssueLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).IssueLabel(ctx, req.(*IssueLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_RevokeLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).RevokeLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/RevokeLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).RevokeLabel(ctx, req.(*RevokeLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_QueryIssuanceRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryIssuanceRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).QueryIssuanceRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/QueryIssuanceRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).QueryIssuanceRecords(ctx, req.(*QueryIssuanceRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_AddRenownedPlayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRenownedPlayerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).AddRenownedPlayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/AddRenownedPlayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).AddRenownedPlayer(ctx, req.(*AddRenownedPlayerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_ListRenownedPlayers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRenownedPlayersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).ListRenownedPlayers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/ListRenownedPlayers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).ListRenownedPlayers(ctx, req.(*ListRenownedPlayersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_BatchRemoveRenownedPlayers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRemoveRenownedPlayersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).BatchRemoveRenownedPlayers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/BatchRemoveRenownedPlayers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).BatchRemoveRenownedPlayers(ctx, req.(*BatchRemoveRenownedPlayersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_UpdateRenownedPlayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRenownedPlayerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).UpdateRenownedPlayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/UpdateRenownedPlayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).UpdateRenownedPlayer(ctx, req.(*UpdateRenownedPlayerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetBasePriceSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBasePriceSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetBasePriceSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetBasePriceSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetBasePriceSetting(ctx, req.(*GetBasePriceSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_SetBasePriceSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBasePriceSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).SetBasePriceSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/SetBasePriceSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).SetBasePriceSetting(ctx, req.(*SetBasePriceSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_CalculatePrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculatePriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).CalculatePrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/CalculatePrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).CalculatePrice(ctx, req.(*CalculatePriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_CalculatePriceByGames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculatePriceByGamesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).CalculatePriceByGames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/CalculatePriceByGames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).CalculatePriceByGames(ctx, req.(*CalculatePriceByGamesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetCoachApplicableLabels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachApplicableLabelsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetCoachApplicableLabels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetCoachApplicableLabels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetCoachApplicableLabels(ctx, req.(*GetCoachApplicableLabelsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_CheckCoachIdentityAndSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckCoachIdentityAndSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).CheckCoachIdentityAndSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/CheckCoachIdentityAndSkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).CheckCoachIdentityAndSkill(ctx, req.(*CheckCoachIdentityAndSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_BatchGetCoachLabelsForGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCoachLabelsForGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).BatchGetCoachLabelsForGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/BatchGetCoachLabelsForGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).BatchGetCoachLabelsForGame(ctx, req.(*BatchGetCoachLabelsForGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_BatchCheckCoachHasGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckCoachHasGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).BatchCheckCoachHasGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/BatchCheckCoachHasGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).BatchCheckCoachHasGame(ctx, req.(*BatchCheckCoachHasGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_SetGameGuaranteeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGameGuaranteeStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).SetGameGuaranteeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/SetGameGuaranteeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).SetGameGuaranteeStatus(ctx, req.(*SetGameGuaranteeStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_DebugCheckGuaranteeWinPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DebugCheckGuaranteeWinPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).DebugCheckGuaranteeWinPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/DebugCheckGuaranteeWinPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).DebugCheckGuaranteeWinPermission(ctx, req.(*DebugCheckGuaranteeWinPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetSpecialLabelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSpecialLabelListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetSpecialLabelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetSpecialLabelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetSpecialLabelList(ctx, req.(*GetSpecialLabelListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_UpdateUserSpecialLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserSpecialLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).UpdateUserSpecialLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/UpdateUserSpecialLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).UpdateUserSpecialLabel(ctx, req.(*UpdateUserSpecialLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_BatchGetUserSpecialLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserSpecialLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).BatchGetUserSpecialLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/BatchGetUserSpecialLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).BatchGetUserSpecialLabel(ctx, req.(*BatchGetUserSpecialLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_CheckLabelOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckLabelOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).CheckLabelOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/CheckLabelOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).CheckLabelOrder(ctx, req.(*CheckLabelOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_BatchGetUserGameSpecialLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserGameSpecialLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).BatchGetUserGameSpecialLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/BatchGetUserGameSpecialLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).BatchGetUserGameSpecialLabel(ctx, req.(*BatchGetUserGameSpecialLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetLabelIssueRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLabelIssuanceRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetLabelIssueRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetLabelIssueRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetLabelIssueRecord(ctx, req.(*GetLabelIssuanceRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_GetSpecialIssueRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSpecialLabelIssuanceRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).GetSpecialIssueRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/GetSpecialIssueRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).GetSpecialIssueRecord(ctx, req.(*GetSpecialLabelIssuanceRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportSkill_SetLabelPriceSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLabelPriceSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportSkillServer).SetLabelPriceSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_skill.EsportSkill/SetLabelPriceSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportSkillServer).SetLabelPriceSwitch(ctx, req.(*SetLabelPriceSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _EsportSkill_serviceDesc = grpc.ServiceDesc{
	ServiceName: "esport_skill.EsportSkill",
	HandlerType: (*EsportSkillServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddEsportGameConfig",
			Handler:    _EsportSkill_AddEsportGameConfig_Handler,
		},
		{
			MethodName: "UpdateEsportGameConfig",
			Handler:    _EsportSkill_UpdateEsportGameConfig_Handler,
		},
		{
			MethodName: "DeleteEsportGameConfig",
			Handler:    _EsportSkill_DeleteEsportGameConfig_Handler,
		},
		{
			MethodName: "GetEsportGameConfigListByPage",
			Handler:    _EsportSkill_GetEsportGameConfigListByPage_Handler,
		},
		{
			MethodName: "GetUserAuditSkill",
			Handler:    _EsportSkill_GetUserAuditSkill_Handler,
		},
		{
			MethodName: "FreezeCoachSkill",
			Handler:    _EsportSkill_FreezeCoachSkill_Handler,
		},
		{
			MethodName: "UnfreezeCoachSkill",
			Handler:    _EsportSkill_UnfreezeCoachSkill_Handler,
		},
		{
			MethodName: "GetSkillFreezeOperationList",
			Handler:    _EsportSkill_GetSkillFreezeOperationList_Handler,
		},
		{
			MethodName: "SetUserSkillAuditType",
			Handler:    _EsportSkill_SetUserSkillAuditType_Handler,
		},
		{
			MethodName: "GetAllGameCardConfig",
			Handler:    _EsportSkill_GetAllGameCardConfig_Handler,
		},
		{
			MethodName: "GetUserCurrentSkill",
			Handler:    _EsportSkill_GetUserCurrentSkill_Handler,
		},
		{
			MethodName: "BatchGetUserCurrentSkill",
			Handler:    _EsportSkill_BatchGetUserCurrentSkill_Handler,
		},
		{
			MethodName: "ModifyUserSkill",
			Handler:    _EsportSkill_ModifyUserSkill_Handler,
		},
		{
			MethodName: "DelUserSkill",
			Handler:    _EsportSkill_DelUserSkill_Handler,
		},
		{
			MethodName: "GetGameList",
			Handler:    _EsportSkill_GetGameList_Handler,
		},
		{
			MethodName: "AddUserAuditSkill",
			Handler:    _EsportSkill_AddUserAuditSkill_Handler,
		},
		{
			MethodName: "TestAddUserSkill",
			Handler:    _EsportSkill_TestAddUserSkill_Handler,
		},
		{
			MethodName: "BatchGetAuditSkill",
			Handler:    _EsportSkill_BatchGetAuditSkill_Handler,
		},
		{
			MethodName: "HandleGameUpdate",
			Handler:    _EsportSkill_HandleGameUpdate_Handler,
		},
		{
			MethodName: "GetSwitch",
			Handler:    _EsportSkill_GetSwitch_Handler,
		},
		{
			MethodName: "SetSwitch",
			Handler:    _EsportSkill_SetSwitch_Handler,
		},
		{
			MethodName: "GetTopGameList",
			Handler:    _EsportSkill_GetTopGameList_Handler,
		},
		{
			MethodName: "GetGameDetailById",
			Handler:    _EsportSkill_GetGameDetailById_Handler,
		},
		{
			MethodName: "GetGameDetailByIds",
			Handler:    _EsportSkill_GetGameDetailByIds_Handler,
		},
		{
			MethodName: "BatchGetGameInfoByNames",
			Handler:    _EsportSkill_BatchGetGameInfoByNames_Handler,
		},
		{
			MethodName: "SetUserSkillRiskAuditType",
			Handler:    _EsportSkill_SetUserSkillRiskAuditType_Handler,
		},
		{
			MethodName: "GetAllGameSimpleInfo",
			Handler:    _EsportSkill_GetAllGameSimpleInfo_Handler,
		},
		{
			MethodName: "GetUserSkillByGameId",
			Handler:    _EsportSkill_GetUserSkillByGameId_Handler,
		},
		{
			MethodName: "GetMinimumPrice",
			Handler:    _EsportSkill_GetMinimumPrice_Handler,
		},
		{
			MethodName: "GetUserSkillStatus",
			Handler:    _EsportSkill_GetUserSkillStatus_Handler,
		},
		{
			MethodName: "GetUserSkillFreezeStatus",
			Handler:    _EsportSkill_GetUserSkillFreezeStatus_Handler,
		},
		{
			MethodName: "BatGetUserSkillFreezeStatus",
			Handler:    _EsportSkill_BatGetUserSkillFreezeStatus_Handler,
		},
		{
			MethodName: "BatchGetUserRenownedInfo",
			Handler:    _EsportSkill_BatchGetUserRenownedInfo_Handler,
		},
		{
			MethodName: "CreateLabel",
			Handler:    _EsportSkill_CreateLabel_Handler,
		},
		{
			MethodName: "EditLabel",
			Handler:    _EsportSkill_EditLabel_Handler,
		},
		{
			MethodName: "DeleteLabel",
			Handler:    _EsportSkill_DeleteLabel_Handler,
		},
		{
			MethodName: "ListLabels",
			Handler:    _EsportSkill_ListLabels_Handler,
		},
		{
			MethodName: "IssueLabel",
			Handler:    _EsportSkill_IssueLabel_Handler,
		},
		{
			MethodName: "RevokeLabel",
			Handler:    _EsportSkill_RevokeLabel_Handler,
		},
		{
			MethodName: "QueryIssuanceRecords",
			Handler:    _EsportSkill_QueryIssuanceRecords_Handler,
		},
		{
			MethodName: "AddRenownedPlayer",
			Handler:    _EsportSkill_AddRenownedPlayer_Handler,
		},
		{
			MethodName: "ListRenownedPlayers",
			Handler:    _EsportSkill_ListRenownedPlayers_Handler,
		},
		{
			MethodName: "BatchRemoveRenownedPlayers",
			Handler:    _EsportSkill_BatchRemoveRenownedPlayers_Handler,
		},
		{
			MethodName: "UpdateRenownedPlayer",
			Handler:    _EsportSkill_UpdateRenownedPlayer_Handler,
		},
		{
			MethodName: "GetBasePriceSetting",
			Handler:    _EsportSkill_GetBasePriceSetting_Handler,
		},
		{
			MethodName: "SetBasePriceSetting",
			Handler:    _EsportSkill_SetBasePriceSetting_Handler,
		},
		{
			MethodName: "CalculatePrice",
			Handler:    _EsportSkill_CalculatePrice_Handler,
		},
		{
			MethodName: "CalculatePriceByGames",
			Handler:    _EsportSkill_CalculatePriceByGames_Handler,
		},
		{
			MethodName: "GetCoachApplicableLabels",
			Handler:    _EsportSkill_GetCoachApplicableLabels_Handler,
		},
		{
			MethodName: "CheckCoachIdentityAndSkill",
			Handler:    _EsportSkill_CheckCoachIdentityAndSkill_Handler,
		},
		{
			MethodName: "BatchGetCoachLabelsForGame",
			Handler:    _EsportSkill_BatchGetCoachLabelsForGame_Handler,
		},
		{
			MethodName: "BatchCheckCoachHasGame",
			Handler:    _EsportSkill_BatchCheckCoachHasGame_Handler,
		},
		{
			MethodName: "SetGameGuaranteeStatus",
			Handler:    _EsportSkill_SetGameGuaranteeStatus_Handler,
		},
		{
			MethodName: "DebugCheckGuaranteeWinPermission",
			Handler:    _EsportSkill_DebugCheckGuaranteeWinPermission_Handler,
		},
		{
			MethodName: "GetSpecialLabelList",
			Handler:    _EsportSkill_GetSpecialLabelList_Handler,
		},
		{
			MethodName: "UpdateUserSpecialLabel",
			Handler:    _EsportSkill_UpdateUserSpecialLabel_Handler,
		},
		{
			MethodName: "BatchGetUserSpecialLabel",
			Handler:    _EsportSkill_BatchGetUserSpecialLabel_Handler,
		},
		{
			MethodName: "CheckLabelOrder",
			Handler:    _EsportSkill_CheckLabelOrder_Handler,
		},
		{
			MethodName: "BatchGetUserGameSpecialLabel",
			Handler:    _EsportSkill_BatchGetUserGameSpecialLabel_Handler,
		},
		{
			MethodName: "GetLabelIssueRecord",
			Handler:    _EsportSkill_GetLabelIssueRecord_Handler,
		},
		{
			MethodName: "GetSpecialIssueRecord",
			Handler:    _EsportSkill_GetSpecialIssueRecord_Handler,
		},
		{
			MethodName: "SetLabelPriceSwitch",
			Handler:    _EsportSkill_SetLabelPriceSwitch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/esport-skill/esport-skill.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/esport-skill/esport-skill.proto", fileDescriptor_esport_skill_02b79c516d0fa99e)
}

var fileDescriptor_esport_skill_02b79c516d0fa99e = []byte{
	// 6651 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x3d, 0x4b, 0x6c, 0x1c, 0x47,
	0x76, 0xea, 0x99, 0xe1, 0x67, 0x1e, 0x3f, 0x1a, 0x15, 0x29, 0x72, 0x38, 0x14, 0x45, 0xaa, 0x25,
	0xd9, 0x94, 0x6c, 0x51, 0x5e, 0xc9, 0xf6, 0x7e, 0x62, 0xaf, 0x97, 0x1a, 0x8e, 0xa8, 0x59, 0xf3,
	0xb7, 0x3d, 0xa4, 0x6c, 0xad, 0x63, 0xcd, 0xb6, 0x66, 0x8a, 0x64, 0x5b, 0xf3, 0xdb, 0xee, 0x1e,
	0xd9, 0x5c, 0x24, 0xf1, 0x7a, 0x91, 0x6c, 0x36, 0x09, 0x92, 0x20, 0x58, 0x04, 0xc8, 0x5e, 0x82,
	0x1c, 0x36, 0x97, 0x60, 0x73, 0x70, 0x6e, 0x41, 0x90, 0x1c, 0x82, 0xbd, 0xe5, 0x90, 0x4b, 0x80,
	0xe4, 0x14, 0x6c, 0x2e, 0xc1, 0x02, 0xc9, 0x21, 0xc7, 0x00, 0x41, 0x90, 0xa0, 0x3e, 0xdd, 0x53,
	0x55, 0x5d, 0xdd, 0x3d, 0x14, 0xbd, 0x4e, 0x6e, 0xd3, 0xf5, 0x79, 0xf5, 0xea, 0xd5, 0xab, 0x57,
	0xaf, 0xde, 0xa7, 0x06, 0xd6, 0x7c, 0xff, 0xf6, 0xb7, 0xfb, 0x4e, 0xe3, 0xa9, 0xe7, 0xb4, 0x9e,
	0x61, 0xf7, 0x36, 0xf6, 0x7a, 0x5d, 0xd7, 0xbf, 0xe5, 0x3d, 0x75, 0x5a, 0x2d, 0xe9, 0x63, 0xad,
	0xe7, 0x76, 0xfd, 0x2e, 0x9a, 0x64, 0x65, 0x75, 0x5a, 0x66, 0xfe, 0x34, 0x07, 0xe7, 0x37, 0xed,
	0x36, 0xae, 0x76, 0x0e, 0xbb, 0x6e, 0xdb, 0xf6, 0x9d, 0x6e, 0x07, 0x3d, 0x86, 0x82, 0x33, 0xf8,
	0xac, 0xfb, 0x27, 0x3d, 0x5c, 0x34, 0x56, 0x8c, 0xd5, 0xe9, 0x3b, 0x77, 0xd7, 0xc4, 0xce, 0x6b,
	0x4a, 0xc7, 0xb5, 0xcd, 0xf5, 0xed, 0x4a, 0xbd, 0xba, 0x73, 0x7f, 0xd7, 0xda, 0x5e, 0xdf, 0xaf,
	0xee, 0xee, 0xd4, 0xf7, 0x1f, 0xed, 0x55, 0xac, 0xf3, 0x02, 0xb0, 0xfd, 0x93, 0x1e, 0x46, 0x57,
	0x60, 0xd2, 0xc3, 0x0d, 0x0a, 0xbb, 0x63, 0xb7, 0x71, 0x31, 0xb3, 0x62, 0xac, 0xe6, 0xad, 0x09,
	0x5e, 0xb6, 0x63, 0xb7, 0x31, 0x5a, 0x84, 0xbc, 0xe3, 0xe3, 0x76, 0xbd, 0xe5, 0x78, 0x7e, 0x31,
	0xbb, 0x92, 0x5d, 0xcd, 0x5b, 0xe3, 0xa4, 0x60, 0xcb, 0xf1, 0x7c, 0xf4, 0x1e, 0x4c, 0x78, 0xb8,
	0x85, 0x1b, 0x3e, 0x43, 0x2d, 0x47, 0x51, 0xfb, 0xca, 0x29, 0x51, 0xab, 0x55, 0xb6, 0x2a, 0xe5,
	0x7d, 0x86, 0x21, 0x30, 0x70, 0x14, 0xb9, 0x25, 0x80, 0x00, 0x39, 0xa7, 0x59, 0x1c, 0x59, 0x31,
	0x56, 0xa7, 0xac, 0x3c, 0x2f, 0xa9, 0x36, 0xcd, 0x13, 0xb8, 0xa8, 0x9d, 0x25, 0xba, 0x02, 0x4b,
	0xda, 0x8a, 0x7a, 0x75, 0xe7, 0xe1, 0xfa, 0x56, 0x75, 0xa3, 0x70, 0x0e, 0x5d, 0x86, 0x92, 0xbe,
	0x89, 0xb5, 0xbe, 0xf3, 0x76, 0xc1, 0x40, 0x4b, 0xb0, 0xa0, 0xaf, 0xdf, 0xa8, 0x3e, 0x2a, 0x64,
	0xcc, 0x1f, 0x19, 0x70, 0x29, 0x69, 0x1a, 0x68, 0x15, 0xae, 0x25, 0xd5, 0x0b, 0x98, 0xbc, 0x08,
	0x57, 0x13, 0x5b, 0xd6, 0xaa, 0x3b, 0x9b, 0x5b, 0x95, 0x82, 0x81, 0x5e, 0x00, 0x33, 0xb1, 0xe1,
	0xf6, 0xc1, 0xd6, 0x7e, 0xb5, 0x90, 0x31, 0xff, 0xd3, 0x80, 0xfc, 0x3d, 0xdb, 0xc3, 0x7b, 0xae,
	0xd3, 0xc0, 0xe8, 0x11, 0xcc, 0x1d, 0xd9, 0x6d, 0x5c, 0xef, 0xb9, 0x4e, 0xc3, 0xe9, 0x1c, 0xd5,
	0xfb, 0x1d, 0xc7, 0x17, 0xd9, 0xe8, 0x9a, 0xb2, 0x56, 0x64, 0x84, 0x3d, 0xab, 0x5a, 0xae, 0xee,
	0x6c, 0xd6, 0x0f, 0x76, 0xaa, 0x7c, 0x55, 0x66, 0x08, 0x8c, 0x3d, 0x06, 0xe2, 0xa0, 0xe3, 0xb0,
	0xe5, 0xd9, 0x85, 0x69, 0xd7, 0xee, 0x3c, 0xa5, 0xa0, 0x71, 0xbd, 0x6d, 0xf7, 0x8a, 0x99, 0x95,
	0xec, 0xea, 0xc4, 0x9d, 0x1b, 0x32, 0xc8, 0x10, 0x97, 0x35, 0xcb, 0xee, 0x3c, 0xa5, 0xbf, 0xb6,
	0xed, 0x5e, 0xa5, 0xe3, 0xbb, 0x27, 0xd6, 0xa4, 0x2b, 0x14, 0x95, 0xde, 0x82, 0x0b, 0x91, 0x26,
	0xa8, 0x00, 0xd9, 0xa7, 0xf8, 0x84, 0x62, 0x3b, 0x65, 0x91, 0x9f, 0x68, 0x16, 0x46, 0x9e, 0xd9,
	0xad, 0x3e, 0x63, 0xd6, 0x29, 0x8b, 0x7d, 0x7c, 0x25, 0xf3, 0x25, 0xc3, 0xfc, 0x3b, 0x03, 0x26,
	0x36, 0x07, 0x98, 0xa2, 0xb7, 0xe1, 0x82, 0x34, 0x79, 0x61, 0xde, 0xcb, 0x09, 0xf3, 0x66, 0x5b,
	0x45, 0x98, 0x32, 0x9d, 0x6e, 0x3c, 0x25, 0x33, 0x67, 0xa5, 0xe4, 0x2c, 0x8c, 0x50, 0x22, 0xd2,
	0xed, 0x35, 0x65, 0xb1, 0x0f, 0xf3, 0x7f, 0x72, 0x50, 0xa8, 0x50, 0x90, 0x64, 0x4e, 0xe5, 0x6e,
	0xe7, 0xd0, 0x39, 0x42, 0xf3, 0x30, 0x46, 0xb1, 0x70, 0x9a, 0x9c, 0x24, 0xa3, 0xe4, 0xb3, 0xda,
	0x44, 0x08, 0x72, 0xc2, 0x0e, 0xa6, 0xbf, 0xd1, 0xab, 0x90, 0xa7, 0x8d, 0x29, 0x96, 0x59, 0x8a,
	0xe5, 0xbc, 0x06, 0x4b, 0x8a, 0xd8, 0x38, 0x69, 0x49, 0xb1, 0x59, 0xe4, 0xbd, 0x9c, 0x46, 0xb7,
	0x43, 0x77, 0x74, 0x9e, 0x55, 0x56, 0x1b, 0xdd, 0x0e, 0x7a, 0x11, 0x28, 0x61, 0xea, 0x4f, 0xec,
	0xc6, 0xd3, 0x23, 0xb7, 0xdb, 0xef, 0xb0, 0x8d, 0x99, 0xb7, 0xa6, 0x49, 0xf1, 0xbd, 0xb0, 0x94,
	0x6c, 0x5e, 0xda, 0xb0, 0xd1, 0x6d, 0x75, 0xdd, 0xe2, 0x28, 0x6d, 0x43, 0xe1, 0x96, 0x49, 0x41,
	0x38, 0x08, 0x61, 0x80, 0xe2, 0xd8, 0x8a, 0xb1, 0x9a, 0x61, 0x83, 0x10, 0x06, 0x40, 0xd7, 0x61,
	0x9a, 0xa2, 0x57, 0xc7, 0xcf, 0x9c, 0x26, 0xee, 0x34, 0x70, 0x71, 0x9c, 0xf6, 0x9f, 0xa2, 0xa5,
	0x15, 0x5e, 0x48, 0xe5, 0x03, 0x6d, 0xd6, 0xc4, 0x5e, 0xa3, 0x98, 0x67, 0x43, 0xd0, 0x92, 0x0d,
	0xec, 0x35, 0xd0, 0x37, 0xe0, 0x22, 0x9b, 0x87, 0x20, 0x40, 0xa9, 0x10, 0x03, 0xca, 0xa6, 0x4b,
	0x89, 0x52, 0x8a, 0x2d, 0x94, 0x50, 0x40, 0xc5, 0xdd, 0x1b, 0x30, 0x29, 0xf2, 0x40, 0x71, 0x62,
	0xc5, 0x58, 0x9d, 0xb8, 0xb3, 0x10, 0x85, 0xc4, 0x57, 0xd8, 0x9a, 0x10, 0x96, 0x1b, 0x2d, 0xc3,
	0x44, 0xbf, 0xd7, 0xb4, 0x7d, 0x5c, 0xf7, 0x9d, 0x36, 0x2e, 0x4e, 0xae, 0x18, 0xab, 0x59, 0x0b,
	0x58, 0xd1, 0xbe, 0xd3, 0xc6, 0xe8, 0x75, 0x80, 0x27, 0xb6, 0xc7, 0xc0, 0xe3, 0xe2, 0x14, 0x05,
	0x3e, 0x1f, 0xb3, 0x9b, 0xac, 0xfc, 0x93, 0x70, 0x93, 0x3f, 0x84, 0x22, 0xa3, 0xb5, 0xed, 0x36,
	0xe9, 0x74, 0xeb, 0x03, 0x89, 0x3d, 0x4d, 0x27, 0x7b, 0x39, 0x8a, 0x62, 0xd9, 0x76, 0x9b, 0x64,
	0x7e, 0x55, 0x1f, 0xb7, 0xad, 0xd9, 0x23, 0xa5, 0x84, 0x4c, 0xd7, 0xfc, 0x00, 0x4a, 0xeb, 0xcd,
	0xa6, 0xca, 0x83, 0x16, 0xfe, 0x76, 0x1f, 0x7b, 0x3e, 0xda, 0x02, 0xc4, 0x81, 0xf2, 0x85, 0x26,
	0x95, 0x94, 0x2b, 0x23, 0xe3, 0x45, 0x40, 0x14, 0xb0, 0x52, 0x62, 0x2e, 0xc1, 0xa2, 0x76, 0x2c,
	0xaf, 0xd7, 0xed, 0x78, 0xd8, 0x7c, 0x07, 0x96, 0x0e, 0x28, 0xa1, 0xe2, 0xb0, 0x79, 0x1d, 0x46,
	0x4f, 0x85, 0x01, 0x6f, 0x6d, 0xae, 0xc0, 0xe5, 0x38, 0xc0, 0x7c, 0xe8, 0x2f, 0xc1, 0xd2, 0x06,
	0x6e, 0xe1, 0xf8, 0xa1, 0xe3, 0xf6, 0x24, 0x81, 0x1d, 0xd7, 0x93, 0xc3, 0xfe, 0xd4, 0x80, 0x6b,
	0x9b, 0xd8, 0x57, 0xeb, 0x09, 0xf5, 0xef, 0x9d, 0xec, 0xd9, 0x47, 0x38, 0x18, 0x43, 0xda, 0xca,
	0xc6, 0x69, 0xb7, 0xb2, 0x20, 0x19, 0x68, 0x25, 0x3d, 0xd8, 0x17, 0x60, 0xbc, 0x67, 0x1f, 0xe1,
	0x7a, 0xa7, 0xdf, 0xa6, 0xc2, 0x61, 0xca, 0x1a, 0x23, 0xdf, 0x3b, 0xfd, 0x36, 0xe9, 0x47, 0xab,
	0x3c, 0xe7, 0x3b, 0xec, 0x50, 0x9f, 0xb2, 0x68, 0xdb, 0x9a, 0xf3, 0x1d, 0x6c, 0x7e, 0x62, 0xc0,
	0xf5, 0x14, 0x9c, 0xd9, 0xec, 0xd0, 0x2f, 0x89, 0xaa, 0x83, 0xa1, 0x63, 0xc4, 0x08, 0x61, 0x06,
	0xaa, 0xc5, 0x22, 0xe4, 0xfd, 0xae, 0x6f, 0xb7, 0xea, 0x8d, 0x8e, 0xcf, 0x45, 0xfd, 0x38, 0x2d,
	0x28, 0x77, 0x7c, 0xb3, 0x0e, 0x17, 0x37, 0xb1, 0xbf, 0xdf, 0xed, 0x91, 0xae, 0xa4, 0x79, 0x40,
	0xa7, 0x02, 0x64, 0xfb, 0xe1, 0x3a, 0x90, 0x9f, 0x32, 0xe5, 0x32, 0x43, 0x52, 0xce, 0xdc, 0x86,
	0x39, 0x75, 0x00, 0x3e, 0xa9, 0xbb, 0xd1, 0x49, 0xcd, 0x69, 0x44, 0x09, 0xd9, 0x55, 0xe1, 0x64,
	0xcc, 0x16, 0x4c, 0xd4, 0xb8, 0xe2, 0xd2, 0x39, 0xec, 0x46, 0xd4, 0x2e, 0x23, 0x45, 0xed, 0xca,
	0x28, 0x6a, 0x97, 0xac, 0x19, 0x65, 0x55, 0xcd, 0xe8, 0x27, 0x59, 0x98, 0x3a, 0xf0, 0xb0, 0x5b,
	0x23, 0xe8, 0xd0, 0x01, 0x63, 0x8f, 0x8d, 0x44, 0x0e, 0x89, 0xca, 0xe1, 0xac, 0x4e, 0x0e, 0xcf,
	0xc2, 0x88, 0xdd, 0x6f, 0x3a, 0x5d, 0x7e, 0x58, 0xb0, 0x0f, 0xd2, 0x99, 0xfe, 0xa8, 0x37, 0xfb,
	0x2e, 0x95, 0xa0, 0x5c, 0x83, 0x9b, 0xa2, 0xa5, 0x1b, 0xbc, 0x90, 0x88, 0xd4, 0x60, 0x2a, 0x74,
	0xaa, 0xa3, 0x94, 0xa2, 0x8a, 0x48, 0x15, 0x68, 0x17, 0x52, 0x29, 0x64, 0x12, 0xfc, 0x91, 0xcf,
	0x4e, 0x80, 0x31, 0x86, 0x3e, 0x29, 0xa0, 0x07, 0x80, 0x74, 0xc6, 0xe4, 0x19, 0x07, 0x85, 0x67,
	0xcc, 0x2a, 0x14, 0x1c, 0xaf, 0x7e, 0xd4, 0xb7, 0x5d, 0xbb, 0xe3, 0x63, 0x5c, 0xff, 0xd0, 0xe9,
	0x14, 0x61, 0xc5, 0x58, 0x1d, 0xb7, 0xa6, 0x1d, 0x6f, 0x33, 0x28, 0x7e, 0xc7, 0xe9, 0xa0, 0x2f,
	0xc3, 0xc4, 0xa1, 0x8b, 0xf1, 0x77, 0x38, 0x0b, 0x4d, 0x50, 0x16, 0x2a, 0xca, 0x08, 0xde, 0xa7,
	0x0d, 0x08, 0xe7, 0x58, 0x70, 0x18, 0xfe, 0x46, 0xd7, 0x60, 0x9a, 0x77, 0xf5, 0xfc, 0x6e, 0xaf,
	0xee, 0x7b, 0x5c, 0xe8, 0x4f, 0xb2, 0xd2, 0x9a, 0xdf, 0xed, 0xed, 0x7b, 0xe6, 0x1f, 0x66, 0xa1,
	0xb8, 0xde, 0x6c, 0x92, 0x15, 0x5b, 0xef, 0x37, 0x1d, 0x9f, 0x2e, 0x5b, 0x3c, 0x43, 0x2f, 0xc3,
	0x04, 0x21, 0xa1, 0x5f, 0xf7, 0xbb, 0x4f, 0x71, 0x87, 0x2f, 0x1a, 0xd0, 0xa2, 0x7d, 0x52, 0x42,
	0xb8, 0x8b, 0x35, 0xf0, 0xba, 0x7d, 0x97, 0x2f, 0xda, 0x94, 0xc5, 0x3a, 0xd5, 0x68, 0x11, 0xfa,
	0x02, 0x8c, 0x50, 0xc4, 0x8b, 0x39, 0x4a, 0xee, 0x45, 0x79, 0x36, 0x12, 0xef, 0x58, 0xac, 0x25,
	0x11, 0x17, 0x47, 0x7d, 0xa7, 0xd5, 0x1c, 0xe8, 0xe2, 0x63, 0xf4, 0xbb, 0x4a, 0xcf, 0x7a, 0x8e,
	0x11, 0x21, 0xd0, 0x28, 0x63, 0x47, 0x86, 0x10, 0xa1, 0xc2, 0x1d, 0xb8, 0xe8, 0x1c, 0x75, 0xba,
	0x2e, 0xae, 0xb3, 0x56, 0x2e, 0xe6, 0x88, 0x91, 0x05, 0xcb, 0x59, 0x33, 0xac, 0x92, 0x4e, 0xdd,
	0xe2, 0x55, 0x44, 0xc8, 0xcc, 0x54, 0xa3, 0xe5, 0xe8, 0x3a, 0x5c, 0xa9, 0x6e, 0xee, 0xec, 0x5a,
	0x95, 0xfa, 0xfa, 0xc1, 0x46, 0x75, 0xbf, 0x6e, 0x55, 0x6a, 0xbb, 0x07, 0x56, 0xb9, 0x52, 0x3f,
	0xd8, 0xa9, 0xed, 0x55, 0xca, 0xd5, 0xfb, 0xd5, 0x0a, 0xd1, 0xaa, 0x4d, 0xb8, 0xac, 0x6f, 0x56,
	0x79, 0x58, 0xdd, 0xa8, 0xec, 0x94, 0x89, 0x42, 0xbd, 0x0c, 0x8b, 0xfa, 0x36, 0xe4, 0x73, 0xb7,
	0x90, 0x31, 0x17, 0x61, 0x41, 0xb3, 0x2c, 0x5c, 0x72, 0xff, 0x91, 0x01, 0xc5, 0x4d, 0xec, 0x7f,
	0x9e, 0x8b, 0xf6, 0x02, 0x9c, 0xff, 0xd0, 0xf1, 0x8f, 0xeb, 0x7d, 0xb7, 0x55, 0xef, 0xb9, 0xf8,
	0xd0, 0xf9, 0x88, 0xee, 0xb8, 0x71, 0x6b, 0x8a, 0x14, 0x1f, 0xb8, 0xad, 0x3d, 0x5a, 0x68, 0x7e,
	0x0c, 0x0b, 0x1a, 0xcc, 0xb8, 0xf8, 0x8a, 0xa2, 0x16, 0xf2, 0x42, 0x66, 0x68, 0x5e, 0x90, 0x17,
	0x3c, 0xab, 0x2c, 0xb8, 0xf9, 0x5f, 0x06, 0x2c, 0xdc, 0xb3, 0xfd, 0xc6, 0xf1, 0x26, 0xf6, 0xa3,
	0xc4, 0x11, 0x19, 0xc9, 0x90, 0x19, 0x09, 0x41, 0xae, 0xef, 0x34, 0x3d, 0x8a, 0xc9, 0x94, 0x45,
	0x7f, 0x47, 0xc6, 0xca, 0xca, 0xcc, 0x35, 0x0f, 0x63, 0xdd, 0xc3, 0xc3, 0xba, 0x87, 0x7d, 0x7e,
	0x50, 0x8d, 0x76, 0x0f, 0x0f, 0x6b, 0xd8, 0x27, 0x52, 0xa9, 0xe5, 0xb4, 0x1d, 0x9f, 0x33, 0x2b,
	0xfb, 0x20, 0xd0, 0x3a, 0x18, 0x37, 0xeb, 0xf4, 0x24, 0x09, 0x58, 0x95, 0x94, 0xec, 0x93, 0x02,
	0xb2, 0x4c, 0x1e, 0xb6, 0xdd, 0xc6, 0x31, 0x1b, 0x6d, 0x8c, 0xd6, 0x03, 0x2b, 0x0a, 0x2e, 0xcc,
	0xd2, 0x32, 0x8d, 0x53, 0x7c, 0xc4, 0x65, 0x32, 0xff, 0x32, 0x0b, 0x05, 0x71, 0xd6, 0x8d, 0xae,
	0xdb, 0xd4, 0x90, 0xbd, 0x08, 0x63, 0x76, 0xa3, 0xd1, 0xed, 0xf3, 0xd3, 0x2d, 0x6f, 0x05, 0x9f,
	0x44, 0x6e, 0x75, 0x9c, 0xc6, 0x53, 0x26, 0x93, 0x99, 0xc4, 0x1d, 0x27, 0x05, 0x54, 0x26, 0x23,
	0xc8, 0xf9, 0xbe, 0xd3, 0xe4, 0xb2, 0x96, 0xfe, 0x56, 0x99, 0x6b, 0x24, 0xc2, 0x5c, 0x29, 0x1b,
	0x94, 0x54, 0xf7, 0x7a, 0xad, 0x13, 0xa6, 0x97, 0x8e, 0xf1, 0x6a, 0x52, 0x42, 0xd5, 0xd2, 0x90,
	0x41, 0xc6, 0x87, 0x66, 0x10, 0x95, 0x4c, 0xf9, 0x28, 0x37, 0xcf, 0xc1, 0xa8, 0x8b, 0x6d, 0xaf,
	0xcb, 0xc4, 0x6e, 0xde, 0xe2, 0x5f, 0x12, 0x7b, 0x4c, 0xc8, 0xec, 0x71, 0x15, 0xa6, 0x3c, 0xe7,
	0xa8, 0x33, 0x38, 0x51, 0x26, 0x69, 0xcf, 0x49, 0x52, 0x18, 0x1e, 0x28, 0x25, 0x18, 0xef, 0xf6,
	0xb0, 0x6b, 0xfb, 0x5d, 0x97, 0xaa, 0xd0, 0x79, 0x2b, 0xfc, 0x56, 0x35, 0xf0, 0x69, 0xb6, 0xbc,
	0x03, 0x0d, 0xdc, 0xfc, 0x8b, 0x0c, 0x94, 0x74, 0x9c, 0xcb, 0x37, 0xcf, 0xff, 0x29, 0xeb, 0xde,
	0x81, 0x9c, 0x70, 0x42, 0x2a, 0x8a, 0x94, 0xca, 0x70, 0x16, 0x6d, 0x4b, 0x26, 0xcc, 0x95, 0x28,
	0xca, 0x68, 0x9c, 0x9f, 0x99, 0x1a, 0x45, 0x79, 0x4d, 0x61, 0xf8, 0xf1, 0x54, 0x86, 0xcf, 0x47,
	0x19, 0xfe, 0xdf, 0x0c, 0xb8, 0x54, 0x63, 0x02, 0x87, 0x62, 0xb0, 0x1e, 0xcc, 0xf0, 0x0c, 0xe2,
	0x30, 0x59, 0xc2, 0x08, 0xcc, 0x93, 0x93, 0x98, 0x47, 0x5c, 0xfc, 0x11, 0x65, 0xf1, 0x57, 0xa1,
	0x20, 0x8c, 0x39, 0xd0, 0x36, 0xf2, 0xd6, 0xf4, 0x60, 0xe0, 0x40, 0xab, 0x70, 0xbc, 0x7a, 0xc3,
	0xee, 0x34, 0x70, 0x8b, 0xd2, 0x6c, 0xdc, 0x1a, 0x77, 0xbc, 0x32, 0xfd, 0x36, 0x97, 0x61, 0x29,
	0x66, 0xb2, 0xfc, 0x64, 0xf8, 0xa1, 0x01, 0x2b, 0x62, 0x0b, 0xcb, 0xf1, 0x9e, 0x7e, 0x0e, 0x24,
	0x21, 0x3a, 0x61, 0x03, 0x77, 0xc8, 0x45, 0xac, 0x89, 0x39, 0x59, 0xf2, 0xb4, 0xa4, 0xdc, 0x6d,
	0x62, 0xf3, 0x2a, 0x5c, 0x49, 0x40, 0x8a, 0xa3, 0xfe, 0x10, 0x4a, 0xfc, 0xe4, 0x28, 0xf7, 0x5d,
	0x17, 0x77, 0xd2, 0x4e, 0x35, 0xcd, 0x89, 0x94, 0xd1, 0x9d, 0x48, 0x9f, 0x18, 0xb0, 0xa8, 0x05,
	0xfc, 0x39, 0x1e, 0x4a, 0x8f, 0x61, 0x7e, 0x1f, 0x7b, 0x3e, 0x3f, 0xd1, 0x53, 0x26, 0x26, 0x0c,
	0x6f, 0x0c, 0x37, 0xbc, 0x59, 0x82, 0x62, 0x14, 0x3e, 0xa7, 0xeb, 0x5d, 0x58, 0x0e, 0xa4, 0x4a,
	0x2a, 0x71, 0xb3, 0x1c, 0x07, 0xf3, 0x19, 0x14, 0xd4, 0xc6, 0x9f, 0x0b, 0xa1, 0x1e, 0xc2, 0x4a,
	0x3c, 0xb2, 0x7c, 0xc1, 0x02, 0x59, 0xa4, 0xbd, 0xd4, 0x45, 0x7a, 0xd1, 0xb6, 0xe6, 0x47, 0x30,
	0xb7, 0xdd, 0x6d, 0x3a, 0x87, 0x27, 0xbf, 0x10, 0xfa, 0x4b, 0xb2, 0x39, 0x2b, 0xc9, 0x66, 0x73,
	0x01, 0xe6, 0x23, 0x23, 0xf3, 0x95, 0xf9, 0x1a, 0xcc, 0x6c, 0xe0, 0xd6, 0x10, 0x18, 0x09, 0x37,
	0xa8, 0x8c, 0x74, 0xc9, 0x9f, 0x83, 0x59, 0x19, 0x02, 0x87, 0x6c, 0x87, 0x2c, 0x4f, 0xcb, 0xef,
	0x9d, 0xd0, 0x6b, 0x61, 0xf3, 0xf4, 0x23, 0x90, 0x0a, 0xc7, 0xab, 0x1f, 0x77, 0xa9, 0xfd, 0x9d,
	0xec, 0xae, 0x51, 0xc7, 0x7b, 0xd0, 0xf5, 0x7c, 0xf3, 0xe7, 0x06, 0x5c, 0xd2, 0x8f, 0x11, 0xbb,
	0xaf, 0x62, 0x07, 0x79, 0x23, 0x10, 0x3f, 0x8c, 0xee, 0xd9, 0x74, 0x6e, 0x62, 0x4c, 0xc4, 0xf8,
	0xf2, 0x6b, 0x30, 0xd5, 0x60, 0x2b, 0x5e, 0x0f, 0xee, 0x15, 0xa9, 0xeb, 0x36, 0xd9, 0x10, 0x39,
	0x5b, 0x66, 0xca, 0x11, 0x95, 0x29, 0x6f, 0x85, 0x3a, 0x2d, 0x6d, 0x5e, 0xf3, 0x6d, 0xbf, 0xef,
	0xc5, 0xd2, 0xd2, 0xfc, 0xa9, 0x11, 0x4a, 0x32, 0xa9, 0x7d, 0x2c, 0x5d, 0x1e, 0x02, 0x78, 0xb4,
	0x8d, 0x60, 0xc8, 0xfe, 0xa2, 0x72, 0xad, 0x8f, 0x85, 0xb7, 0xc6, 0x3e, 0x43, 0xb3, 0x76, 0xde,
	0x0b, 0xbe, 0x4b, 0x6f, 0xc0, 0xb4, 0x5c, 0x79, 0x2a, 0x83, 0xf6, 0x5d, 0x58, 0x16, 0x47, 0xbd,
	0xcf, 0x6f, 0x8d, 0xc9, 0x73, 0xff, 0x0f, 0x03, 0x56, 0xe2, 0x7b, 0x71, 0x0a, 0x38, 0xdc, 0x8e,
	0x2b, 0x4c, 0x9a, 0xed, 0xe5, 0xf5, 0xf8, 0x49, 0xeb, 0x00, 0x51, 0x63, 0x87, 0x32, 0xfd, 0xa9,
	0x23, 0xb1, 0xac, 0x64, 0x03, 0x8a, 0x36, 0xd2, 0x90, 0xe1, 0x35, 0x91, 0x0c, 0x13, 0xaa, 0x85,
	0x3e, 0x8a, 0x81, 0x40, 0xa7, 0xb7, 0xc0, 0xbc, 0x67, 0xfb, 0x69, 0xa4, 0x5a, 0x80, 0xf1, 0xbe,
	0xd3, 0x1c, 0x18, 0x6e, 0xa6, 0xac, 0xb1, 0xbe, 0xd3, 0xa4, 0xf6, 0x99, 0x7f, 0x32, 0x60, 0x36,
	0xd2, 0x6f, 0xdb, 0xee, 0xa1, 0xf7, 0xe3, 0xe8, 0xf4, 0x5a, 0x0a, 0x7a, 0xdb, 0x76, 0xef, 0xff,
	0x07, 0x6d, 0x7e, 0x66, 0xc0, 0xd5, 0x44, 0xe2, 0x70, 0x8e, 0x78, 0x04, 0xe3, 0x7d, 0x0f, 0xbb,
	0xc2, 0x14, 0xbf, 0xaa, 0x9a, 0x9e, 0x53, 0x81, 0xd0, 0x1d, 0x1e, 0xce, 0x75, 0xac, 0xcf, 0xbe,
	0x4a, 0x8f, 0x61, 0x52, 0xac, 0xd0, 0xcc, 0xef, 0x4b, 0xf2, 0xfc, 0xcc, 0x74, 0xe2, 0x8a, 0x53,
	0xfc, 0x5d, 0x03, 0x2e, 0x44, 0xda, 0xc4, 0xdb, 0xbc, 0x14, 0x83, 0x4e, 0xe6, 0x4c, 0x06, 0x9d,
	0xac, 0xc6, 0xa0, 0xf3, 0x7d, 0x03, 0xd0, 0x26, 0xf6, 0x55, 0xdb, 0xe4, 0xf3, 0xd9, 0x70, 0x97,
	0x00, 0xa8, 0x2d, 0x56, 0x54, 0x0b, 0xa9, 0x75, 0x96, 0x69, 0x85, 0x92, 0xa9, 0x36, 0xab, 0x98,
	0x6a, 0xdf, 0x87, 0xf1, 0xc0, 0x18, 0xf9, 0x9c, 0x26, 0x40, 0xc9, 0x19, 0x94, 0x95, 0x9d, 0x41,
	0xa6, 0x0b, 0x33, 0xd2, 0x34, 0xcf, 0x60, 0x21, 0x25, 0xaa, 0x64, 0x07, 0x7f, 0xe4, 0xd7, 0x23,
	0x73, 0x9d, 0x22, 0xc5, 0x7b, 0xc1, 0x7c, 0xcd, 0x6b, 0x50, 0xd8, 0xc4, 0x7e, 0xed, 0x43, 0xc7,
	0x6f, 0x1c, 0xc7, 0xcb, 0xc0, 0x7d, 0xb8, 0x20, 0xb4, 0xe2, 0x78, 0xbd, 0x05, 0x53, 0x1e, 0x2d,
	0xe1, 0xbb, 0x99, 0x7b, 0x0a, 0x4a, 0x0a, 0xb3, 0xd1, 0x26, 0x9c, 0xaf, 0x27, 0x3d, 0xe1, 0xcb,
	0xfc, 0x59, 0x06, 0x26, 0xc5, 0x6a, 0xb4, 0x03, 0xa8, 0x6d, 0x3b, 0x9d, 0x7a, 0x14, 0xec, 0xf4,
	0x9d, 0x15, 0x9d, 0xa5, 0x5b, 0x02, 0x5e, 0x20, 0x7d, 0x25, 0x78, 0x0f, 0x61, 0xee, 0xb8, 0xdb,
	0xc6, 0x6c, 0x41, 0x25, 0x98, 0x99, 0x21, 0x61, 0xce, 0x06, 0xfd, 0x25, 0xb8, 0x16, 0xcc, 0xda,
	0xbd, 0x1e, 0xb6, 0x5b, 0x0a, 0xd4, 0xec, 0x90, 0x50, 0x11, 0xeb, 0x2d, 0xc1, 0xdc, 0x87, 0x8b,
	0x9e, 0xd3, 0xc4, 0x4f, 0x6c, 0x57, 0x01, 0x9a, 0x1b, 0x12, 0xe8, 0x0c, 0xef, 0x2e, 0x16, 0x9a,
	0x35, 0x28, 0xd4, 0xd4, 0xe5, 0x3d, 0xf3, 0xba, 0xcd, 0xc0, 0x85, 0x9a, 0xca, 0x0d, 0xe6, 0x5d,
	0x6a, 0xbf, 0x23, 0x9c, 0xb8, 0x81, 0x7d, 0xdb, 0x69, 0xdd, 0x3b, 0x19, 0x28, 0x67, 0xb1, 0x1e,
	0x9d, 0x8f, 0xa8, 0x1a, 0xa2, 0x76, 0xe2, 0xfc, 0xf5, 0x9c, 0x2e, 0x28, 0x74, 0x15, 0xa6, 0xda,
	0x4e, 0xc7, 0x69, 0xf7, 0xdb, 0xdc, 0xf3, 0xc7, 0xf4, 0x80, 0x49, 0x5e, 0x48, 0x7d, 0x7c, 0xe6,
	0xeb, 0x9a, 0x91, 0xc5, 0x93, 0x8d, 0xe3, 0xeb, 0x05, 0x27, 0x1b, 0x43, 0xd8, 0x33, 0xbf, 0xc7,
	0x34, 0xa1, 0x48, 0xc7, 0x70, 0x4f, 0x4c, 0x30, 0x2c, 0x4e, 0xe3, 0xa4, 0x81, 0x46, 0xe8, 0xf1,
	0x19, 0x0e, 0xf9, 0x25, 0xaa, 0x0b, 0xaf, 0xb7, 0x5a, 0xf4, 0xb0, 0x73, 0xda, 0xbd, 0x16, 0x75,
	0xad, 0x72, 0xf4, 0xcd, 0x22, 0x75, 0xb6, 0x6c, 0x0b, 0x3d, 0x82, 0x9a, 0xaf, 0xc2, 0x7c, 0xa4,
	0x86, 0x63, 0x1e, 0x19, 0xd8, 0xd0, 0x0c, 0xfc, 0xe7, 0x06, 0x4c, 0xb3, 0xf1, 0x02, 0xff, 0xee,
	0x2f, 0x40, 0x0e, 0x22, 0x04, 0x39, 0xea, 0x63, 0xc8, 0x51, 0x3f, 0x36, 0xfd, 0x2d, 0x0b, 0xfb,
	0x91, 0x61, 0xdd, 0x4e, 0x8f, 0xa8, 0x42, 0xaf, 0x21, 0x14, 0x9f, 0xf4, 0x97, 0x39, 0x54, 0x61,
	0xb1, 0x2e, 0x29, 0xdb, 0x40, 0x9a, 0x2d, 0x03, 0x4d, 0x55, 0x9c, 0x37, 0xe1, 0x72, 0x70, 0xad,
	0x0b, 0x6a, 0xef, 0x9d, 0x90, 0xb9, 0x85, 0x5c, 0xb4, 0x08, 0xf9, 0x8e, 0x04, 0x3c, 0x6f, 0x8d,
	0x77, 0x82, 0xee, 0xbf, 0x3c, 0xb8, 0xc2, 0x46, 0xba, 0x9f, 0x1d, 0xb9, 0x03, 0xf9, 0x82, 0x6c,
	0xe1, 0x4e, 0xf7, 0xc3, 0x0e, 0x6e, 0x0a, 0x4c, 0x12, 0xbf, 0x6e, 0xa2, 0x5a, 0x97, 0x91, 0xd5,
	0xba, 0xbf, 0xc9, 0xc8, 0x77, 0x59, 0x19, 0x2e, 0x47, 0xfb, 0x57, 0x60, 0x8e, 0x2a, 0x3e, 0x2e,
	0xaf, 0x64, 0x1e, 0xf4, 0x81, 0x1a, 0xb4, 0x19, 0x51, 0x83, 0x12, 0xe1, 0xad, 0xa9, 0x15, 0xa1,
	0x3e, 0x34, 0xd3, 0x8f, 0xd6, 0x94, 0x10, 0xbb, 0xe5, 0x8b, 0xc5, 0xa5, 0xdf, 0x34, 0xa0, 0x18,
	0x07, 0x45, 0xa3, 0x3c, 0x1d, 0xc8, 0xca, 0xd3, 0x5b, 0x67, 0xc4, 0x57, 0xd4, 0xac, 0xfe, 0x39,
	0x0b, 0xf9, 0x2d, 0xfb, 0x09, 0x66, 0x5e, 0xc4, 0x69, 0xc8, 0x84, 0xd4, 0xcf, 0x38, 0x4d, 0xf4,
	0x3a, 0x40, 0x8b, 0x54, 0x26, 0xf8, 0x56, 0x69, 0x67, 0xaa, 0x46, 0xe5, 0x5b, 0xc1, 0x4f, 0xb4,
	0x0c, 0x13, 0xac, 0x9f, 0xd3, 0xb6, 0x8f, 0x02, 0x13, 0x37, 0x03, 0x55, 0x25, 0x25, 0xa4, 0xc1,
	0xb1, 0xed, 0x85, 0x61, 0x16, 0xcc, 0xcb, 0x01, 0xc7, 0xb6, 0x17, 0x84, 0x52, 0x5c, 0x87, 0xe9,
	0x20, 0x0e, 0xc7, 0x6e, 0x53, 0xd3, 0x26, 0x77, 0x2e, 0xf2, 0xd2, 0x75, 0x5a, 0x88, 0x6e, 0x40,
	0xc1, 0xee, 0xf5, 0x5a, 0x4e, 0xc3, 0x7e, 0xd2, 0xc2, 0xf5, 0x16, 0x7e, 0x86, 0x03, 0x9b, 0xff,
	0xf9, 0x41, 0xf9, 0x16, 0x29, 0x26, 0xe2, 0xa4, 0xe9, 0x78, 0xbd, 0x96, 0x7d, 0x52, 0xef, 0xba,
	0x4d, 0xec, 0x72, 0x5b, 0xe9, 0x24, 0x2f, 0xdc, 0x25, 0x65, 0xe8, 0x25, 0xb8, 0xc0, 0x10, 0x6f,
	0x62, 0xaf, 0xe1, 0x3a, 0x3d, 0x6a, 0x84, 0x66, 0xb1, 0x29, 0x05, 0x5a, 0xb1, 0x31, 0x28, 0x47,
	0xb7, 0x00, 0xb1, 0xc6, 0x2e, 0xfe, 0x76, 0xdf, 0x71, 0x71, 0x1b, 0x77, 0x7c, 0x8f, 0x87, 0xa9,
	0x30, 0x30, 0x96, 0x50, 0x41, 0xf4, 0x3c, 0xd6, 0x9c, 0xca, 0x1f, 0x66, 0x13, 0x67, 0x34, 0xa3,
	0x02, 0x48, 0x60, 0xff, 0x09, 0x89, 0xfd, 0x97, 0x61, 0x82, 0x19, 0xef, 0x31, 0x61, 0x0f, 0x6e,
	0x12, 0x67, 0xf6, 0x7c, 0xc6, 0x30, 0x92, 0x5c, 0x9b, 0x92, 0xe5, 0x9a, 0xb9, 0x05, 0xa8, 0xec,
	0x62, 0xdb, 0xc7, 0x5b, 0x01, 0x42, 0x2c, 0x98, 0x82, 0xe3, 0x42, 0xb6, 0x02, 0x3f, 0xcd, 0x74,
	0x0b, 0x4b, 0xd9, 0x85, 0x21, 0x49, 0x7e, 0x9a, 0x17, 0x61, 0x46, 0x82, 0xc6, 0x8f, 0xda, 0xaf,
	0x43, 0xa1, 0xd2, 0x74, 0xfc, 0xcf, 0x64, 0x88, 0x19, 0xb8, 0x20, 0xc0, 0xe2, 0x03, 0xdc, 0x06,
	0xc4, 0x02, 0x2d, 0xa4, 0x21, 0x16, 0x60, 0x9c, 0x0f, 0x11, 0x5a, 0xeb, 0x19, 0x9c, 0x26, 0x41,
	0x54, 0xea, 0xc0, 0xe1, 0xfc, 0xb5, 0x01, 0x17, 0x88, 0xe0, 0xa0, 0xa5, 0xa1, 0x5c, 0x5c, 0x86,
	0x89, 0x20, 0x50, 0xe2, 0x09, 0x76, 0x39, 0x28, 0xe0, 0xb1, 0x12, 0x4f, 0xb0, 0x2b, 0xeb, 0xe0,
	0x19, 0x59, 0x07, 0x97, 0xb0, 0xc8, 0x4a, 0x58, 0x28, 0xfb, 0x27, 0x37, 0xf4, 0xfe, 0x11, 0x78,
	0x61, 0x44, 0x52, 0x4f, 0x5c, 0x40, 0x22, 0xfa, 0xe1, 0x19, 0x7f, 0x7e, 0x40, 0x6a, 0x51, 0x3a,
	0xc7, 0xd2, 0x7b, 0x2a, 0xa4, 0x77, 0x7a, 0x28, 0xc6, 0x9f, 0x18, 0x70, 0xa1, 0xea, 0x79, 0xfd,
	0x61, 0x69, 0x1f, 0x68, 0xeb, 0x99, 0xd0, 0xd2, 0x89, 0x5e, 0x81, 0x59, 0x7c, 0x78, 0x88, 0x1b,
	0xbe, 0xf3, 0x8c, 0xde, 0xb4, 0x5d, 0x9f, 0xf9, 0x67, 0xd8, 0xdd, 0x0a, 0x85, 0x75, 0x35, 0x52,
	0x45, 0x5d, 0x52, 0x2f, 0xc3, 0xa0, 0xb4, 0x8e, 0x3b, 0x4d, 0xd6, 0x3e, 0x47, 0xdb, 0x17, 0xc2,
	0x9a, 0x4a, 0xa7, 0x49, 0xbd, 0x3a, 0xb3, 0x80, 0x44, 0x0c, 0x07, 0x4c, 0x63, 0xe1, 0x67, 0xdd,
	0xa7, 0xa7, 0x61, 0x1a, 0xa9, 0x03, 0x87, 0xf3, 0x2f, 0x19, 0x58, 0xfc, 0x46, 0x1f, 0xbb, 0x27,
	0x64, 0x0c, 0xbb, 0x43, 0x54, 0x94, 0x46, 0xd7, 0x6d, 0x26, 0xb1, 0xcf, 0x48, 0x32, 0xfb, 0x8c,
	0x08, 0xec, 0xb3, 0x08, 0xf9, 0x46, 0xd7, 0x6e, 0x1c, 0x53, 0xdd, 0x8e, 0xb9, 0x90, 0xc6, 0x69,
	0x41, 0xb5, 0xe9, 0x49, 0xc8, 0xe6, 0x64, 0x2a, 0xc7, 0xd1, 0x74, 0xe4, 0x94, 0x34, 0x1d, 0xd5,
	0xd3, 0x54, 0xe1, 0xdd, 0xb1, 0xa1, 0x79, 0xf7, 0x55, 0x18, 0xe5, 0x5a, 0xfc, 0x38, 0xed, 0xa3,
	0x68, 0x08, 0x01, 0x0d, 0xb9, 0x1e, 0xcf, 0xdb, 0x9a, 0x3f, 0x36, 0x60, 0x5a, 0x26, 0x6f, 0xe4,
	0x30, 0x0a, 0xd9, 0x2a, 0xb4, 0xe9, 0x2d, 0x51, 0x9b, 0x9e, 0xcc, 0x4c, 0x79, 0x2f, 0x9c, 0xef,
	0x02, 0x8c, 0x2b, 0x9c, 0x33, 0x86, 0xd5, 0xc9, 0x51, 0xe1, 0x34, 0x32, 0xb4, 0x70, 0xfa, 0x1d,
	0x03, 0x2e, 0xe9, 0x59, 0x81, 0x6f, 0xc5, 0x1d, 0x98, 0x75, 0x78, 0x55, 0xdd, 0xa5, 0x75, 0x09,
	0xda, 0x92, 0x0c, 0xc4, 0x42, 0x8e, 0xf4, 0x9d, 0xbe, 0x33, 0xff, 0xde, 0xa0, 0x71, 0x25, 0xc1,
	0xd9, 0xbe, 0xd7, 0xb2, 0x4f, 0xc8, 0x49, 0xcf, 0xb8, 0xf2, 0x21, 0xe4, 0x55, 0x71, 0xf0, 0x65,
	0xc5, 0xa5, 0x18, 0xd3, 0x75, 0x4d, 0x2e, 0x65, 0x9a, 0x9c, 0xc3, 0x65, 0x45, 0xe9, 0x3d, 0xb2,
	0xab, 0xd4, 0x7a, 0x8d, 0xc1, 0x35, 0x0c, 0xde, 0x14, 0xd4, 0x6d, 0x16, 0xbc, 0x49, 0x8f, 0x3b,
	0x21, 0x24, 0x96, 0xac, 0x0c, 0x0f, 0x89, 0x7d, 0x95, 0x46, 0x64, 0xa8, 0x58, 0x71, 0xda, 0xce,
	0xc3, 0x18, 0x76, 0xdd, 0x7a, 0xdb, 0x3b, 0xe2, 0xf1, 0x54, 0xa3, 0xd8, 0x75, 0xb7, 0xbd, 0x23,
	0xf3, 0xb7, 0x0c, 0x28, 0x31, 0x03, 0x85, 0xd8, 0xef, 0x33, 0xda, 0x9f, 0x7c, 0x66, 0xd9, 0xc1,
	0xcc, 0xa4, 0xf3, 0x36, 0xa7, 0x9c, 0xb7, 0x7f, 0x96, 0xd1, 0xd2, 0x27, 0x9d, 0x99, 0x05, 0x99,
	0x9f, 0x55, 0xcf, 0x7f, 0xaa, 0xb0, 0xf0, 0x5b, 0x10, 0xe3, 0x64, 0xa0, 0x45, 0x69, 0x21, 0xe0,
	0x23, 0x67, 0x0d, 0x5c, 0x96, 0xa6, 0x3a, 0xaa, 0x5c, 0x99, 0x14, 0x67, 0xfb, 0x58, 0x24, 0xdc,
	0x75, 0x19, 0x26, 0x1a, 0x54, 0x5b, 0x60, 0x0d, 0xc6, 0x59, 0x03, 0x56, 0x44, 0xe5, 0xf6, 0xc7,
	0xb0, 0xa8, 0x5d, 0x37, 0xbe, 0xe0, 0x65, 0x98, 0xec, 0xd1, 0x22, 0xba, 0x4d, 0x3d, 0xce, 0xc5,
	0x8a, 0xe1, 0x41, 0xc3, 0xac, 0x13, 0xbd, 0xf0, 0xb7, 0x97, 0xbc, 0x83, 0xde, 0x80, 0x2b, 0x54,
	0x7d, 0xb6, 0x70, 0xbb, 0xfb, 0x0c, 0xc7, 0xf0, 0xcf, 0x3c, 0x8c, 0xc9, 0x56, 0xe5, 0x51, 0x7e,
	0xfb, 0x78, 0x93, 0x5a, 0xa5, 0x63, 0x7b, 0xa7, 0xb1, 0xed, 0x0e, 0x2c, 0xb2, 0xc8, 0x54, 0xfd,
	0x06, 0x56, 0x59, 0x46, 0xe1, 0x83, 0x8c, 0xca, 0x07, 0xe6, 0x17, 0xe1, 0x92, 0x1e, 0x5e, 0x1a,
	0x22, 0x7b, 0xd4, 0x82, 0x10, 0x46, 0x1e, 0xd7, 0xb0, 0xef, 0x3b, 0x9d, 0x23, 0xe1, 0xc0, 0x0c,
	0x0e, 0xa8, 0xe0, 0xc0, 0xe4, 0xe7, 0x53, 0xbc, 0xcf, 0xec, 0x29, 0xb5, 0x07, 0x44, 0x21, 0x72,
	0x4c, 0x66, 0x61, 0x84, 0xe9, 0xea, 0x06, 0x8f, 0x7f, 0xa0, 0x1a, 0xba, 0x1c, 0x1d, 0x9d, 0x19,
	0x36, 0x3a, 0xda, 0x3c, 0x84, 0x52, 0xed, 0x33, 0x45, 0x7f, 0x80, 0x5f, 0x56, 0xc0, 0xcf, 0x5c,
	0x82, 0xc5, 0x5a, 0xfc, 0xa4, 0xcc, 0x9f, 0x18, 0x30, 0xc2, 0x36, 0x64, 0x28, 0xdb, 0xf8, 0xf4,
	0x7a, 0x29, 0xdb, 0xf4, 0xcc, 0xf9, 0x05, 0xb1, 0xb2, 0x43, 0x9c, 0x7c, 0x4e, 0x9a, 0xbc, 0xd9,
	0x82, 0x8b, 0x65, 0xbb, 0xd5, 0xe8, 0xb7, 0x6c, 0x1f, 0x8b, 0x26, 0x99, 0x44, 0xfb, 0xc9, 0x40,
	0x53, 0xc9, 0x28, 0x9a, 0xca, 0x12, 0x00, 0x8d, 0x14, 0x68, 0xd8, 0x8d, 0x63, 0xcc, 0xdd, 0x98,
	0x79, 0x52, 0x52, 0x26, 0x05, 0xe6, 0x5f, 0x19, 0x30, 0xa7, 0x0e, 0xc7, 0x99, 0x61, 0x17, 0xf2,
	0x83, 0x0c, 0x13, 0xb6, 0xc5, 0xef, 0xc8, 0xa4, 0xd0, 0x77, 0x5c, 0x93, 0x53, 0x4d, 0xc6, 0x7b,
	0x41, 0x9a, 0xc9, 0x1e, 0x4c, 0xa5, 0xa5, 0x98, 0xdc, 0x90, 0x6f, 0xd4, 0x33, 0xf2, 0x78, 0x6c,
	0x18, 0xe1, 0x96, 0xec, 0xc1, 0x25, 0x19, 0x07, 0xe6, 0x88, 0x1d, 0xc2, 0x3c, 0x27, 0xad, 0x40,
	0x46, 0x66, 0xbf, 0x14, 0x92, 0x6d, 0xc1, 0x52, 0xcc, 0xa0, 0x9c, 0x70, 0x2f, 0xc1, 0x28, 0x9d,
	0x73, 0x20, 0x18, 0xb5, 0xb3, 0xe0, 0x4d, 0xcc, 0x03, 0xea, 0x69, 0x2c, 0x93, 0xa1, 0xd7, 0x07,
	0x37, 0x63, 0xe9, 0x1a, 0x34, 0xec, 0x4e, 0xc9, 0x0a, 0x1b, 0xfd, 0x07, 0x19, 0xea, 0x8b, 0x8c,
	0x81, 0xcb, 0x11, 0x7d, 0x3f, 0xd0, 0xb6, 0x04, 0x5d, 0xe4, 0xab, 0x11, 0x37, 0x64, 0x22, 0x8c,
	0x35, 0xa5, 0x82, 0x2b, 0x65, 0x54, 0x23, 0xf9, 0x75, 0x03, 0xce, 0x2b, 0xd5, 0xe8, 0x25, 0xc8,
	0x0d, 0x73, 0xef, 0xa4, 0x8d, 0xc8, 0x42, 0x38, 0x5e, 0x9d, 0x1a, 0x0c, 0x70, 0x93, 0x07, 0xb8,
	0xe4, 0x1d, 0x6f, 0x9d, 0x15, 0xa0, 0xeb, 0x70, 0x9e, 0x54, 0x37, 0x9b, 0x75, 0xbf, 0x5b, 0x1f,
	0x28, 0x2d, 0xe3, 0xd6, 0xa4, 0xe3, 0xad, 0x37, 0x9b, 0xfb, 0x5d, 0x26, 0x86, 0xfa, 0x54, 0x0c,
	0x51, 0xd8, 0x4c, 0x3e, 0x48, 0x36, 0xee, 0x04, 0xe2, 0x8a, 0x4a, 0x7e, 0x46, 0x56, 0xf2, 0xaf,
	0xc0, 0xa4, 0x6f, 0xbb, 0x47, 0xd8, 0xa7, 0x96, 0xf1, 0x60, 0xdc, 0x09, 0x56, 0x46, 0x54, 0x68,
	0xcc, 0xa5, 0x52, 0x74, 0x58, 0x2e, 0x95, 0xfe, 0xd6, 0x80, 0x2b, 0xe5, 0x63, 0xdc, 0x78, 0x5a,
	0x66, 0xa3, 0xe1, 0x8e, 0xef, 0xf8, 0x27, 0xeb, 0x9d, 0xa6, 0x14, 0x0e, 0xf1, 0x2e, 0x40, 0x83,
	0x34, 0x4a, 0xd0, 0x16, 0x53, 0x81, 0xb0, 0x16, 0xd4, 0xeb, 0x93, 0xa7, 0xc0, 0xe8, 0xe2, 0x94,
	0x21, 0x1f, 0x96, 0x27, 0x11, 0x21, 0xc9, 0x38, 0x4b, 0x0e, 0xda, 0xa4, 0xe1, 0xd3, 0xce, 0xb7,
	0x47, 0xfc, 0x94, 0x0f, 0x98, 0x8c, 0xb1, 0xd6, 0xfd, 0xae, 0x4b, 0xf6, 0xd3, 0x99, 0xc4, 0x9e,
	0xf9, 0xdf, 0x59, 0xae, 0x03, 0xc4, 0xc0, 0xe6, 0xa8, 0x3d, 0xd6, 0xec, 0x80, 0x18, 0x33, 0x5e,
	0x3c, 0x94, 0xb5, 0x41, 0x95, 0x70, 0x2f, 0x09, 0xa8, 0xbc, 0x15, 0x7c, 0x0c, 0x2e, 0x37, 0xc3,
	0x58, 0x02, 0x04, 0x20, 0x9f, 0x66, 0x60, 0x5a, 0x1e, 0x02, 0x7d, 0x00, 0xac, 0x5e, 0x90, 0xcd,
	0xdb, 0x67, 0x44, 0x9b, 0x8d, 0x3d, 0x10, 0xdb, 0x2d, 0xfe, 0x49, 0xf4, 0x1b, 0xc7, 0x0b, 0x8d,
	0xb4, 0x7c, 0x1b, 0x82, 0xe3, 0x05, 0xda, 0x8c, 0xc4, 0x3d, 0x59, 0x89, 0x7b, 0x4a, 0x1f, 0xc2,
	0x94, 0x04, 0x56, 0x23, 0xf2, 0xf7, 0x64, 0x91, 0xff, 0x95, 0x53, 0x4f, 0x23, 0x24, 0xb0, 0x78,
	0x32, 0x1c, 0xc0, 0x12, 0xed, 0x3a, 0x60, 0xcf, 0x07, 0xb6, 0x27, 0xb2, 0x95, 0xc4, 0x3d, 0x86,
	0x72, 0x68, 0xc6, 0xea, 0x4f, 0xff, 0x60, 0x70, 0x63, 0xbe, 0x06, 0x2e, 0x67, 0xa9, 0x0e, 0x20,
	0x06, 0xf8, 0xd8, 0xf6, 0x58, 0x82, 0x56, 0x6c, 0x8c, 0x47, 0x32, 0xa4, 0x35, 0xb1, 0x30, 0x5c,
	0x97, 0xf3, 0x0d, 0xb9, 0xb4, 0x74, 0x0f, 0x66, 0x75, 0x0d, 0xd3, 0xc2, 0x5d, 0xc6, 0x45, 0x6a,
	0x7d, 0x0b, 0x0a, 0x6a, 0x66, 0x5a, 0x98, 0x07, 0x23, 0xe4, 0xc9, 0x50, 0xa7, 0x71, 0x7a, 0x92,
	0x0c, 0x82, 0x9c, 0xef, 0xf4, 0x3c, 0x6e, 0x5e, 0xa6, 0xbf, 0xcd, 0x3f, 0x36, 0x60, 0x3a, 0x18,
	0x22, 0x2d, 0xa3, 0x32, 0xd1, 0x1f, 0x94, 0x94, 0x72, 0x97, 0x3d, 0x43, 0xca, 0x9d, 0xe4, 0x29,
	0x1b, 0x60, 0x19, 0xf8, 0xc3, 0xba, 0xa2, 0x7f, 0x48, 0xac, 0x0e, 0x95, 0xa5, 0x8b, 0x03, 0xb4,
	0xa2, 0x8e, 0xbd, 0x4b, 0x7a, 0x9c, 0x38, 0x10, 0x74, 0x24, 0x7d, 0x53, 0x7c, 0x7c, 0x1a, 0xed,
	0x4a, 0x1a, 0x86, 0x39, 0x31, 0x29, 0xf1, 0x47, 0xf1, 0x6a, 0xb3, 0x2e, 0xe5, 0x26, 0xab, 0x4b,
	0xb9, 0x31, 0x57, 0xe0, 0x72, 0xdc, 0xa8, 0xfc, 0xdc, 0xba, 0x03, 0xf3, 0x0f, 0xec, 0x4e, 0x93,
	0x39, 0x93, 0x82, 0x6b, 0x4d, 0x8a, 0xf3, 0xb6, 0x04, 0xc5, 0x68, 0x1f, 0x0e, 0xef, 0xdf, 0x0d,
	0x98, 0x67, 0x31, 0x1f, 0x94, 0x8b, 0x53, 0x82, 0x01, 0x57, 0x78, 0x1e, 0xa8, 0xec, 0x76, 0x02,
	0x36, 0x0e, 0x65, 0x3e, 0x25, 0xc6, 0x24, 0x7b, 0xa6, 0x18, 0x93, 0x5c, 0x34, 0xc6, 0x04, 0x5d,
	0x85, 0x29, 0xde, 0x8a, 0x07, 0x42, 0xb3, 0x70, 0x67, 0xde, 0xc8, 0x62, 0xe1, 0xd0, 0xf3, 0x30,
	0xd6, 0xed, 0xd5, 0xfb, 0x1e, 0x0e, 0x32, 0x70, 0x47, 0xbb, 0xbd, 0x03, 0x0f, 0xbb, 0x84, 0x14,
	0xd1, 0xd9, 0x72, 0x52, 0x1c, 0xc3, 0xc2, 0x41, 0xe7, 0xf0, 0x33, 0xa4, 0x85, 0x80, 0x45, 0x56,
	0xc2, 0xe2, 0x12, 0x94, 0x74, 0x23, 0x71, 0x3c, 0x7e, 0x6c, 0x80, 0xb9, 0x89, 0x7d, 0x21, 0xb0,
	0x67, 0x97, 0x86, 0x72, 0xf3, 0xdc, 0xaf, 0x00, 0x23, 0x04, 0xb9, 0x9e, 0x7d, 0x84, 0xb9, 0xdd,
	0x86, 0xfe, 0x4e, 0xb6, 0xd8, 0x88, 0xfe, 0xc2, 0xac, 0xe4, 0x2f, 0x94, 0x62, 0x48, 0x73, 0x72,
	0x7c, 0x7f, 0xac, 0xcd, 0xfd, 0xfb, 0x06, 0x5c, 0x4d, 0x44, 0x93, 0x6f, 0xcd, 0x2a, 0x4c, 0x77,
	0x83, 0x0a, 0x71, 0x4f, 0xc6, 0xc7, 0x3a, 0x85, 0x70, 0xac, 0xa9, 0xae, 0x08, 0x92, 0x48, 0x50,
	0x96, 0xbf, 0xc2, 0xa6, 0xc6, 0x3e, 0xcc, 0x1f, 0x65, 0xa4, 0x18, 0xb6, 0xb0, 0xb7, 0x66, 0xcd,
	0xc4, 0x79, 0x66, 0xe4, 0x79, 0xaa, 0xcb, 0x99, 0x4d, 0x63, 0xed, 0xdc, 0x99, 0x58, 0x7b, 0x64,
	0x18, 0xd6, 0x1e, 0x4d, 0x66, 0xed, 0x31, 0x91, 0xa9, 0xd0, 0x0c, 0x8c, 0x30, 0xd0, 0xcc, 0x9e,
	0x94, 0xa3, 0x11, 0x59, 0x37, 0xe0, 0xc5, 0x0d, 0xfc, 0xa4, 0x7f, 0x44, 0x4f, 0x39, 0x51, 0xd4,
	0xec, 0x61, 0xb7, 0xed, 0x78, 0x1e, 0x21, 0x32, 0x17, 0xb1, 0x37, 0x61, 0x35, 0xbd, 0x29, 0x67,
	0x51, 0x9f, 0x5a, 0x46, 0x6a, 0x3d, 0xdc, 0x70, 0xec, 0xd6, 0x40, 0x01, 0xe0, 0x9c, 0xf9, 0xa6,
	0x64, 0x22, 0x67, 0x51, 0x41, 0xca, 0xa9, 0x20, 0x76, 0x4d, 0xf0, 0xf2, 0xc8, 0xa7, 0xff, 0x9b,
	0x30, 0x29, 0xf6, 0x8b, 0x58, 0x82, 0x64, 0x4f, 0x62, 0x46, 0xf1, 0x24, 0x9a, 0xef, 0xd2, 0x23,
	0x26, 0x8a, 0x74, 0xe8, 0xc5, 0x8f, 0xaa, 0x87, 0xa5, 0x78, 0xac, 0x05, 0x0d, 0xd1, 0x7c, 0x43,
	0xf6, 0xe2, 0x4b, 0xcd, 0xd2, 0x63, 0x30, 0x7f, 0x98, 0x95, 0x9d, 0xf5, 0x72, 0x77, 0xc5, 0x59,
	0xef, 0xb1, 0xca, 0xba, 0xaa, 0x7e, 0x26, 0x38, 0xeb, 0x75, 0xf0, 0xd6, 0xd4, 0x0a, 0xd9, 0x59,
	0xaf, 0xd4, 0x94, 0xea, 0xcc, 0x2f, 0x4f, 0x83, 0x33, 0x14, 0xfa, 0xa1, 0x72, 0x34, 0xfa, 0xe1,
	0x85, 0x68, 0xf8, 0xb3, 0xda, 0x95, 0x45, 0xc1, 0x05, 0x71, 0x10, 0xa5, 0xdf, 0xe7, 0x9e, 0x7f,
	0x1d, 0x4a, 0x1a, 0x55, 0xea, 0x7d, 0x59, 0x69, 0x7d, 0x9e, 0xc9, 0xeb, 0xe6, 0x22, 0xea, 0x64,
	0x8f, 0x68, 0xf4, 0x68, 0x08, 0x47, 0x6d, 0x9e, 0xbe, 0xae, 0xf1, 0x7c, 0xfc, 0xaf, 0x39, 0xb8,
	0x96, 0x0c, 0x9b, 0x2f, 0xfa, 0x77, 0x8d, 0x94, 0x55, 0x7f, 0x3b, 0x7e, 0xe2, 0x71, 0x40, 0x4f,
	0xb9, 0xf2, 0xbf, 0x0a, 0x73, 0x2c, 0x78, 0x45, 0x5a, 0x3c, 0x6e, 0x1d, 0x10, 0x76, 0x9b, 0xa1,
	0xfa, 0xed, 0x95, 0x58, 0x87, 0x4c, 0x24, 0xd6, 0x21, 0x12, 0x78, 0x90, 0x8d, 0x06, 0x1e, 0x94,
	0xfe, 0xd4, 0x48, 0xe0, 0xbc, 0x63, 0xcd, 0x8e, 0xad, 0x3e, 0x07, 0x45, 0xf4, 0x13, 0x14, 0x36,
	0x38, 0x99, 0xcc, 0xb3, 0xae, 0xd3, 0xc0, 0x6c, 0x01, 0x82, 0xc9, 0xd0, 0x22, 0xda, 0xbe, 0xf4,
	0xc3, 0xd3, 0xf1, 0xaf, 0x2d, 0xf3, 0xef, 0xf3, 0x2e, 0x63, 0x1a, 0x0f, 0xff, 0xa3, 0x01, 0xb3,
	0x2a, 0x56, 0x81, 0xc1, 0x21, 0xce, 0x4b, 0xfd, 0xa6, 0x26, 0xb6, 0xe5, 0x14, 0xc2, 0x5b, 0xe6,
	0x8a, 0x6c, 0x0a, 0x57, 0xe4, 0x22, 0x5c, 0xa1, 0x0f, 0x70, 0xc9, 0x2b, 0x01, 0x2e, 0xe6, 0x33,
	0x3d, 0x5b, 0x24, 0x07, 0xfc, 0xae, 0x4b, 0xfc, 0x92, 0xd1, 0x69, 0x21, 0x3a, 0x6a, 0x89, 0x92,
	0xfe, 0x59, 0xf0, 0x1c, 0x47, 0x9c, 0x9c, 0x8f, 0xea, 0x1c, 0x92, 0x7c, 0xcc, 0x3c, 0x9f, 0x7c,
	0x1c, 0xbc, 0xd6, 0x11, 0x27, 0xd3, 0xcc, 0x4f, 0x33, 0x30, 0x47, 0x8f, 0x6e, 0x5a, 0x4c, 0x77,
	0x4f, 0x24, 0xe4, 0x24, 0x3e, 0x00, 0x5b, 0xbb, 0x96, 0x91, 0x1d, 0x9a, 0xd1, 0x84, 0x06, 0x6d,
	0x07, 0x26, 0x32, 0x41, 0xdf, 0x5f, 0xd3, 0x98, 0xc8, 0x22, 0x68, 0xb1, 0x62, 0x36, 0x66, 0x23,
	0xf8, 0x99, 0xe0, 0xd9, 0x37, 0xf7, 0xb8, 0xc9, 0x8c, 0xb6, 0x2b, 0xc1, 0x5c, 0xf9, 0x41, 0xa5,
	0xfc, 0x36, 0x7b, 0x03, 0x4a, 0x4e, 0x82, 0x47, 0x30, 0x2d, 0xd4, 0xad, 0x6f, 0x6c, 0x14, 0x0c,
	0x34, 0x03, 0xe7, 0x85, 0xb2, 0xca, 0x46, 0x75, 0xbf, 0x90, 0x31, 0x17, 0x60, 0x3e, 0x82, 0xdb,
	0xe0, 0xdd, 0x95, 0x4d, 0x6e, 0x3e, 0x54, 0xdc, 0xd1, 0x03, 0xc1, 0x1f, 0xbe, 0x22, 0xc2, 0x54,
	0x70, 0xfd, 0x2b, 0x22, 0x8a, 0x16, 0x6e, 0x1e, 0xc1, 0x2c, 0x8f, 0x6f, 0x94, 0xdd, 0xfa, 0xa1,
	0x41, 0x64, 0xc0, 0x3e, 0xcc, 0x20, 0x72, 0xe0, 0x24, 0x9a, 0x42, 0xe3, 0x7c, 0x1c, 0x26, 0x86,
	0xcb, 0x71, 0x33, 0x08, 0xfd, 0x88, 0x13, 0x51, 0x5f, 0xbc, 0xa9, 0x8b, 0x5c, 0x54, 0x00, 0x80,
	0x1b, 0x7a, 0xe2, 0xcd, 0xc7, 0xf4, 0x21, 0x17, 0x89, 0x75, 0x3f, 0x53, 0x7a, 0xb5, 0xe8, 0xa3,
	0x2b, 0x49, 0xf0, 0x3f, 0xc3, 0xd9, 0xdc, 0xfc, 0x3a, 0xe4, 0xc3, 0xf0, 0x54, 0x74, 0x11, 0x2e,
	0x84, 0x1f, 0xc2, 0xcb, 0x65, 0xb3, 0x50, 0x18, 0x14, 0x6f, 0xef, 0xde, 0xab, 0xd2, 0x67, 0xca,
	0x0a, 0x30, 0x39, 0x28, 0xdd, 0x2b, 0x17, 0x32, 0x37, 0x9f, 0xf2, 0xee, 0xe2, 0xf3, 0x5a, 0xe1,
	0x03, 0x6b, 0x62, 0xa1, 0x00, 0x7b, 0x11, 0xe6, 0xa3, 0xd5, 0x07, 0x3b, 0xd5, 0xfb, 0x8f, 0x0a,
	0x06, 0xe1, 0xf9, 0x68, 0x25, 0x7d, 0xb8, 0x2d, 0x73, 0xf3, 0x07, 0x86, 0x52, 0x19, 0xba, 0xc6,
	0x90, 0x09, 0x97, 0xf5, 0x35, 0xc2, 0xb8, 0xd7, 0xe1, 0x4a, 0x4c, 0x9b, 0xbd, 0x8a, 0x55, 0xbf,
	0xfb, 0x4a, 0x7d, 0xbb, 0xba, 0x53, 0x30, 0xd0, 0x55, 0x58, 0x4e, 0x68, 0x46, 0xaa, 0x0a, 0x99,
	0x9b, 0xbf, 0x06, 0x13, 0xeb, 0x42, 0xbe, 0xfb, 0x25, 0x28, 0xb2, 0x77, 0x26, 0xb4, 0x0f, 0x56,
	0x2c, 0xc0, 0x45, 0xa9, 0x76, 0xa7, 0xf2, 0x4e, 0xdd, 0xda, 0xa5, 0x14, 0x2d, 0xc1, 0x5c, 0xa4,
	0xaa, 0xf6, 0x76, 0x75, 0x6b, 0xab, 0x90, 0x89, 0xd4, 0x6d, 0xef, 0x6e, 0xf0, 0xba, 0xec, 0xcd,
	0xdf, 0x33, 0xe0, 0x3c, 0x43, 0x40, 0xcc, 0xe6, 0x5e, 0xe2, 0xed, 0x2b, 0xeb, 0x56, 0xf9, 0x81,
	0x4e, 0x6a, 0x2c, 0xc1, 0x42, 0xb4, 0xc9, 0xbd, 0x47, 0xf5, 0x83, 0x5a, 0xc5, 0x2a, 0x18, 0xe8,
	0x32, 0x94, 0xb4, 0xd5, 0x9b, 0x07, 0xd5, 0xad, 0x8d, 0x42, 0x46, 0x98, 0x88, 0x50, 0xbf, 0x4e,
	0x11, 0xfa, 0x16, 0xa0, 0x68, 0x1a, 0x00, 0x19, 0xaf, 0xf6, 0x4e, 0x75, 0xbf, 0xfc, 0xa0, 0x5e,
	0xdb, 0x5f, 0xdf, 0x3f, 0xa8, 0x29, 0xe8, 0xcc, 0x42, 0x41, 0xae, 0xde, 0x25, 0x0b, 0x70, 0x11,
	0x2e, 0x28, 0xa5, 0xf7, 0xef, 0x17, 0x32, 0x37, 0x3f, 0x31, 0xb8, 0xa1, 0x3b, 0x90, 0x8d, 0x5b,
	0xeb, 0xf7, 0x2a, 0x5b, 0xba, 0x59, 0xce, 0x42, 0x41, 0xa8, 0x2b, 0xef, 0xae, 0x97, 0x1f, 0x14,
	0x0c, 0xa5, 0x34, 0x20, 0xf2, 0x1c, 0x20, 0xb1, 0x94, 0x40, 0x59, 0xdf, 0x2a, 0x64, 0x95, 0xd6,
	0x0f, 0x77, 0xab, 0xe5, 0x4a, 0x21, 0x77, 0xf3, 0xb7, 0x85, 0x50, 0x25, 0x3e, 0xc5, 0x65, 0x58,
	0xac, 0xd6, 0x6a, 0x07, 0xeb, 0x3b, 0xe5, 0x8a, 0x7e, 0x92, 0x4b, 0xb0, 0xa0, 0x36, 0xa8, 0xdc,
	0xbf, 0x5f, 0x29, 0xef, 0x57, 0x1f, 0x12, 0x0e, 0x58, 0x84, 0xf9, 0x48, 0xf5, 0xbb, 0x7b, 0x55,
	0xab, 0x42, 0x08, 0xae, 0xa9, 0xdc, 0xab, 0xec, 0x6c, 0x54, 0x77, 0x36, 0x0b, 0xd9, 0x9b, 0xef,
	0x02, 0x0c, 0xae, 0xe2, 0xa8, 0x08, 0xb3, 0xf7, 0xad, 0x4a, 0xe5, 0x9b, 0x95, 0x80, 0x22, 0xec,
	0xab, 0x70, 0x0e, 0xcd, 0xc3, 0x8c, 0x58, 0x73, 0x7f, 0xd7, 0xaa, 0x3c, 0xa4, 0xcb, 0xad, 0x54,
	0xec, 0xef, 0xd6, 0xf7, 0xab, 0x94, 0xbb, 0x7d, 0x28, 0xa8, 0xfa, 0x0f, 0xd9, 0x61, 0x9c, 0x3a,
	0xf5, 0x58, 0xc2, 0x5f, 0x82, 0xa2, 0xa6, 0x0d, 0x23, 0x1e, 0xe5, 0x2e, 0x4d, 0x6d, 0x40, 0xf2,
	0xcc, 0x9d, 0x9f, 0xbf, 0x0a, 0x13, 0x9c, 0x87, 0x68, 0xd6, 0xe8, 0x07, 0x30, 0xa3, 0x79, 0x35,
	0x0c, 0xad, 0x46, 0x22, 0x99, 0x62, 0xde, 0xee, 0x2a, 0xdd, 0x18, 0xa2, 0x25, 0x17, 0xac, 0x1e,
	0xcc, 0xe9, 0x5f, 0x0a, 0x43, 0x2f, 0x29, 0x7a, 0x4c, 0xd2, 0x43, 0x65, 0xa5, 0x97, 0x87, 0x6b,
	0x3c, 0x18, 0x54, 0xff, 0x84, 0x98, 0x3a, 0x68, 0xe2, 0x13, 0x65, 0xea, 0xa0, 0xc9, 0xaf, 0x92,
	0xa1, 0x1f, 0x18, 0xf4, 0xd4, 0x8f, 0x7f, 0xe1, 0x0b, 0xdd, 0x89, 0xb8, 0x67, 0x53, 0x9f, 0x30,
	0x2b, 0xdd, 0x3d, 0x55, 0x1f, 0x8e, 0x4a, 0x93, 0x26, 0x72, 0xc9, 0x6f, 0xd9, 0xa0, 0x17, 0xb4,
	0x39, 0xaa, 0x91, 0x97, 0x66, 0x4a, 0x2f, 0xa6, 0xb6, 0xe3, 0xa3, 0xd8, 0x50, 0x50, 0xcd, 0xa1,
	0xe8, 0xba, 0xce, 0xa2, 0x15, 0x31, 0x88, 0x96, 0x5e, 0x48, 0x6b, 0xc6, 0x87, 0x38, 0x02, 0x14,
	0xb5, 0x75, 0x22, 0x05, 0xc3, 0x58, 0xbb, 0x6b, 0x69, 0x35, 0xbd, 0x21, 0x1f, 0xe8, 0x7b, 0xec,
	0xad, 0x85, 0x38, 0x7b, 0x24, 0x7a, 0x25, 0x42, 0x94, 0x14, 0x0b, 0x6b, 0xe9, 0x0b, 0xa7, 0xe8,
	0xc1, 0x91, 0xe8, 0xc1, 0x45, 0xed, 0x23, 0x19, 0xe8, 0xa6, 0xfa, 0xb0, 0x57, 0xfc, 0xb3, 0x21,
	0xa5, 0x97, 0x86, 0x6a, 0xcb, 0x47, 0x6c, 0xc3, 0xac, 0xce, 0x33, 0x82, 0x6e, 0x44, 0x90, 0x8f,
	0x73, 0xae, 0x94, 0x6e, 0x0e, 0xd3, 0x94, 0x0f, 0xf7, 0x01, 0x4d, 0x7d, 0x8c, 0xbc, 0xcf, 0xb0,
	0xaa, 0xe5, 0x38, 0xcd, 0x7b, 0x0f, 0xa5, 0x1b, 0x43, 0xb4, 0xe4, 0x63, 0x9d, 0x40, 0x31, 0xee,
	0x41, 0x06, 0x74, 0x2b, 0xfe, 0xca, 0xac, 0x1b, 0x75, 0x6d, 0xd8, 0xe6, 0xa1, 0x63, 0xfa, 0xbc,
	0xf2, 0x72, 0x02, 0x52, 0xe2, 0x90, 0xf4, 0x4f, 0x3a, 0x94, 0xae, 0xa7, 0xb4, 0xe2, 0xf0, 0x0f,
	0x60, 0x52, 0x7c, 0x3c, 0x01, 0x5d, 0x89, 0xc8, 0xa9, 0x08, 0x64, 0x33, 0xa9, 0x09, 0x07, 0x6b,
	0xc1, 0x84, 0x90, 0x98, 0x8a, 0x56, 0x22, 0xb4, 0x56, 0x52, 0x73, 0x4b, 0x57, 0x12, 0x5a, 0x0c,
	0x24, 0x51, 0xe4, 0x35, 0x30, 0x55, 0x12, 0xc5, 0xbd, 0xe2, 0xa6, 0x4a, 0xa2, 0xd8, 0x67, 0xc5,
	0x88, 0x24, 0x52, 0x5f, 0x11, 0x51, 0x25, 0x51, 0xcc, 0x2b, 0x26, 0xaa, 0x24, 0x8a, 0x7b, 0x8c,
	0x84, 0x48, 0xa2, 0xe8, 0x13, 0x47, 0xaa, 0x24, 0x8a, 0x7d, 0xbe, 0x4b, 0x95, 0x44, 0x09, 0xaf,
	0x25, 0xd9, 0x50, 0x50, 0xfd, 0x6d, 0xea, 0x5c, 0x62, 0x7c, 0x78, 0xea, 0x5c, 0xe2, 0xdc, 0x76,
	0x68, 0x0b, 0xf2, 0x61, 0x9e, 0x2f, 0xba, 0x1c, 0x95, 0x53, 0x62, 0x8c, 0x4d, 0x69, 0x39, 0xb6,
	0x7e, 0x00, 0xad, 0x16, 0x07, 0xad, 0x96, 0x02, 0x2d, 0x92, 0x60, 0x8a, 0xde, 0x83, 0x69, 0xf9,
	0x09, 0x49, 0x74, 0x35, 0x82, 0x40, 0xf4, 0x05, 0xcb, 0xd2, 0xb5, 0xe4, 0x46, 0xd2, 0xb9, 0x28,
	0x67, 0x75, 0x6a, 0xce, 0x45, 0x6d, 0x7a, 0xab, 0xe6, 0x5c, 0x8c, 0xc9, 0x68, 0x3d, 0x0a, 0xf3,
	0xd8, 0x85, 0xdc, 0x51, 0x94, 0xd6, 0xdd, 0x8b, 0x61, 0x95, 0x84, 0x34, 0xd4, 0x67, 0x30, 0x1f,
	0x93, 0x5d, 0x88, 0x5e, 0xd6, 0xf3, 0x9b, 0x3e, 0x87, 0xb1, 0x74, 0x6b, 0xc8, 0xd6, 0xa1, 0x3b,
	0x61, 0x21, 0xf6, 0x55, 0x24, 0xb4, 0x16, 0x7f, 0xfe, 0xe8, 0xde, 0x74, 0x2a, 0xdd, 0x1e, 0xba,
	0xbd, 0xee, 0xcc, 0x1a, 0x64, 0x7b, 0xc6, 0x9f, 0x59, 0x91, 0xd4, 0xd9, 0xf8, 0x33, 0x4b, 0x93,
	0x3c, 0xca, 0x86, 0x8b, 0xbc, 0x16, 0x83, 0x6e, 0xc4, 0x3f, 0xf9, 0xa1, 0xbc, 0x5a, 0xa3, 0x19,
	0x2e, 0xfe, 0xf1, 0x99, 0xc7, 0x70, 0x5e, 0xc9, 0xdd, 0x45, 0x51, 0xde, 0xd6, 0x24, 0xfd, 0xaa,
	0x67, 0x47, 0x5c, 0x02, 0x30, 0x63, 0x4e, 0xe5, 0x49, 0x16, 0xf4, 0x62, 0xfa, 0xa3, 0x2d, 0x71,
	0xcc, 0x19, 0xf7, 0x5a, 0xcc, 0x49, 0xf8, 0xd2, 0x63, 0xf4, 0x91, 0x89, 0x5b, 0xc3, 0x3e, 0x97,
	0xa2, 0x3d, 0x7f, 0x53, 0x1f, 0xe5, 0x20, 0xca, 0x5c, 0xc2, 0xbb, 0x1b, 0xaa, 0x32, 0x97, 0xfe,
	0x08, 0x8a, 0xaa, 0xcc, 0x0d, 0xf3, 0x32, 0x88, 0xa2, 0x7f, 0x88, 0xb9, 0xa2, 0x49, 0xfa, 0x87,
	0x26, 0x89, 0x37, 0x49, 0xff, 0xd0, 0xe6, 0xe6, 0xee, 0xc3, 0x84, 0x90, 0x50, 0xa8, 0x1e, 0xe4,
	0xd1, 0xcc, 0x45, 0xf5, 0x20, 0xd7, 0x65, 0x23, 0x9e, 0x43, 0x3b, 0x90, 0x0f, 0x73, 0x08, 0x55,
	0x39, 0xaf, 0x26, 0x2a, 0xaa, 0x72, 0x3e, 0x9a, 0x7c, 0x78, 0x8e, 0x60, 0x29, 0x64, 0x13, 0xaa,
	0x58, 0x46, 0x33, 0x13, 0x4b, 0x57, 0x12, 0x5a, 0x84, 0x50, 0xbf, 0x01, 0x30, 0x48, 0xe6, 0x43,
	0x0a, 0x1a, 0x91, 0x2c, 0xc5, 0xd2, 0x4a, 0x7c, 0x03, 0x11, 0xe4, 0x20, 0x11, 0x4e, 0x05, 0x19,
	0x49, 0xe2, 0x53, 0x41, 0x6a, 0x72, 0xe8, 0xce, 0x11, 0x55, 0x4b, 0x48, 0x8a, 0x43, 0x91, 0xec,
	0x0b, 0x35, 0xc1, 0x4e, 0x9d, 0xbb, 0x26, 0xa3, 0x8e, 0x08, 0x2a, 0x5d, 0x16, 0x95, 0x2a, 0xa8,
	0x12, 0x92, 0xee, 0x54, 0x41, 0x95, 0x98, 0x94, 0x75, 0x48, 0x35, 0x3b, 0x39, 0x2b, 0x42, 0xa3,
	0xd9, 0x69, 0xd3, 0x30, 0x34, 0x9a, 0x9d, 0x3e, 0xbd, 0xc2, 0x3c, 0x87, 0x5a, 0x30, 0xa3, 0x49,
	0x67, 0x51, 0xef, 0x0c, 0xf1, 0x99, 0x4a, 0xea, 0x9d, 0x21, 0x21, 0x37, 0xc6, 0x3c, 0x87, 0x3e,
	0x31, 0xf8, 0x53, 0x96, 0xda, 0xf4, 0x13, 0x74, 0x5b, 0xb3, 0x13, 0x93, 0xd2, 0x5c, 0x4a, 0xaf,
	0x0c, 0xdf, 0x21, 0xc4, 0xa1, 0x0b, 0xb3, 0xba, 0x94, 0x13, 0x75, 0x21, 0x13, 0xd2, 0x5c, 0xd4,
	0x85, 0x4c, 0xca, 0x60, 0x61, 0x24, 0xd6, 0x24, 0x96, 0x68, 0xae, 0x65, 0x31, 0xe9, 0x20, 0x9a,
	0x6b, 0x59, 0x6c, 0x42, 0x07, 0x1d, 0xad, 0x96, 0x3e, 0x5a, 0x6d, 0xe8, 0xd1, 0x6a, 0x89, 0xa3,
	0xbd, 0x07, 0xd3, 0x72, 0xc0, 0xbf, 0xaa, 0x4f, 0x6a, 0xf3, 0x35, 0x54, 0x7d, 0x32, 0x26, 0xcb,
	0xa2, 0xa7, 0xa6, 0x7b, 0xf0, 0x6c, 0x02, 0xf5, 0xc2, 0x9e, 0x94, 0xe7, 0xa0, 0x5e, 0xd8, 0x93,
	0xd3, 0x13, 0xd8, 0xa9, 0xaa, 0x8d, 0xea, 0xd7, 0x9c, 0xaa, 0x49, 0x99, 0x09, 0x9a, 0x53, 0x35,
	0x39, 0xe1, 0xe0, 0x63, 0x28, 0xc5, 0xc7, 0x8b, 0xab, 0x3b, 0x23, 0x35, 0xb0, 0x5d, 0xdd, 0x19,
	0x43, 0x84, 0xa2, 0x7f, 0x3c, 0x78, 0x65, 0x36, 0x1a, 0x51, 0xac, 0xdd, 0x9a, 0x49, 0xb1, 0xe9,
	0xda, 0xad, 0x99, 0x1c, 0x70, 0xee, 0xc1, 0x9c, 0x3e, 0xea, 0x57, 0x35, 0x2b, 0x26, 0x46, 0x2f,
	0x97, 0x5e, 0x3e, 0x4d, 0x20, 0x31, 0x19, 0x54, 0x1f, 0xd5, 0x89, 0xa2, 0x96, 0x9e, 0xf8, 0x88,
	0x53, 0x75, 0xd0, 0xe4, 0x40, 0x51, 0xf4, 0x07, 0x06, 0xac, 0xa4, 0xc5, 0x73, 0xa1, 0xd7, 0xd4,
	0xf3, 0x78, 0xa8, 0x50, 0xb1, 0xd2, 0xeb, 0xa7, 0xed, 0x26, 0x19, 0x8f, 0x22, 0x71, 0x1c, 0x51,
	0x29, 0x15, 0x13, 0x59, 0xa6, 0x91, 0x52, 0xb1, 0xe1, 0x5c, 0xa1, 0xd5, 0x5a, 0xf5, 0x98, 0xeb,
	0xad, 0xd6, 0x31, 0xfe, 0x7c, 0xbd, 0xd5, 0x3a, 0x36, 0x4a, 0x4b, 0xd1, 0x18, 0xa5, 0x61, 0x6f,
	0x0d, 0x1b, 0xa4, 0x94, 0xaa, 0x31, 0x6a, 0x87, 0x7e, 0x0c, 0xe7, 0x15, 0x5f, 0xb6, 0x7a, 0xeb,
	0xd0, 0xbb, 0xe1, 0xd5, 0x5b, 0x47, 0x8c, 0x43, 0x1c, 0xfd, 0x86, 0x01, 0x97, 0x92, 0x02, 0x53,
	0xd0, 0x17, 0x4e, 0x13, 0xc4, 0xc2, 0x86, 0xbe, 0x73, 0xfa, 0xb8, 0x17, 0xd4, 0xa3, 0x3c, 0x14,
	0x3a, 0x82, 0x03, 0xf7, 0xf9, 0x4b, 0x11, 0xce, 0x88, 0xf7, 0x45, 0xab, 0x8b, 0x9a, 0xe2, 0x26,
	0xff, 0xae, 0x41, 0xff, 0x74, 0x83, 0x63, 0x23, 0x0e, 0x7a, 0x27, 0x91, 0x1d, 0xf5, 0x63, 0xdf,
	0x3d, 0x55, 0x9f, 0xc1, 0xc6, 0xd1, 0x24, 0x33, 0x69, 0x0e, 0xdc, 0x98, 0x34, 0x2b, 0xcd, 0x81,
	0x1b, 0x97, 0x19, 0x75, 0xef, 0x95, 0x6f, 0xae, 0x1d, 0x75, 0x5b, 0x76, 0xe7, 0x68, 0xed, 0xb5,
	0x3b, 0xbe, 0xbf, 0xd6, 0xe8, 0xb6, 0x6f, 0xd3, 0x7f, 0x6d, 0x6b, 0x74, 0x5b, 0xb7, 0x3d, 0xec,
	0x3e, 0x73, 0x1a, 0xd8, 0x93, 0xfe, 0xd4, 0xed, 0xc9, 0x28, 0xad, 0xbf, 0xfb, 0xbf, 0x01, 0x00,
	0x00, 0xff, 0xff, 0x84, 0x2e, 0xf0, 0x6c, 0x07, 0x6e, 0x00, 0x00,
}
