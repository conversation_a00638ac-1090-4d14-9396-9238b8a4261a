// Code generated by protoc-gen-go. DO NOT EDIT.
// source: services/tbean/tbean.proto

package tbean

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TradeNotifyRequest_Command int32

const (
	TradeNotifyRequest_COMMIT   TradeNotifyRequest_Command = 0
	TradeNotifyRequest_ROLLBACK TradeNotifyRequest_Command = 1
)

var TradeNotifyRequest_Command_name = map[int32]string{
	0: "COMMIT",
	1: "ROLLBACK",
}
var TradeNotifyRequest_Command_value = map[string]int32{
	"COMMIT":   0,
	"ROLLBACK": 1,
}

func (x TradeNotifyRequest_Command) String() string {
	return proto.EnumName(TradeNotifyRequest_Command_name, int32(x))
}
func (TradeNotifyRequest_Command) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tbean_24b0c9375db4cb91, []int{5, 0}
}

type ApiResponseHeader struct {
	Result               string   `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApiResponseHeader) Reset()         { *m = ApiResponseHeader{} }
func (m *ApiResponseHeader) String() string { return proto.CompactTextString(m) }
func (*ApiResponseHeader) ProtoMessage()    {}
func (*ApiResponseHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_tbean_24b0c9375db4cb91, []int{0}
}
func (m *ApiResponseHeader) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApiResponseHeader.Unmarshal(m, b)
}
func (m *ApiResponseHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApiResponseHeader.Marshal(b, m, deterministic)
}
func (dst *ApiResponseHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApiResponseHeader.Merge(dst, src)
}
func (m *ApiResponseHeader) XXX_Size() int {
	return xxx_messageInfo_ApiResponseHeader.Size(m)
}
func (m *ApiResponseHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_ApiResponseHeader.DiscardUnknown(m)
}

var xxx_messageInfo_ApiResponseHeader proto.InternalMessageInfo

func (m *ApiResponseHeader) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

func (m *ApiResponseHeader) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type GetBalanceRequest struct {
	AppId                string   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	Type                 string   `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBalanceRequest) Reset()         { *m = GetBalanceRequest{} }
func (m *GetBalanceRequest) String() string { return proto.CompactTextString(m) }
func (*GetBalanceRequest) ProtoMessage()    {}
func (*GetBalanceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_tbean_24b0c9375db4cb91, []int{1}
}
func (m *GetBalanceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBalanceRequest.Unmarshal(m, b)
}
func (m *GetBalanceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBalanceRequest.Marshal(b, m, deterministic)
}
func (dst *GetBalanceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBalanceRequest.Merge(dst, src)
}
func (m *GetBalanceRequest) XXX_Size() int {
	return xxx_messageInfo_GetBalanceRequest.Size(m)
}
func (m *GetBalanceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBalanceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBalanceRequest proto.InternalMessageInfo

func (m *GetBalanceRequest) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *GetBalanceRequest) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *GetBalanceRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetBalanceResponseBody struct {
	Balance              int32    `protobuf:"varint,1,opt,name=balance,proto3" json:"balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBalanceResponseBody) Reset()         { *m = GetBalanceResponseBody{} }
func (m *GetBalanceResponseBody) String() string { return proto.CompactTextString(m) }
func (*GetBalanceResponseBody) ProtoMessage()    {}
func (*GetBalanceResponseBody) Descriptor() ([]byte, []int) {
	return fileDescriptor_tbean_24b0c9375db4cb91, []int{2}
}
func (m *GetBalanceResponseBody) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBalanceResponseBody.Unmarshal(m, b)
}
func (m *GetBalanceResponseBody) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBalanceResponseBody.Marshal(b, m, deterministic)
}
func (dst *GetBalanceResponseBody) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBalanceResponseBody.Merge(dst, src)
}
func (m *GetBalanceResponseBody) XXX_Size() int {
	return xxx_messageInfo_GetBalanceResponseBody.Size(m)
}
func (m *GetBalanceResponseBody) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBalanceResponseBody.DiscardUnknown(m)
}

var xxx_messageInfo_GetBalanceResponseBody proto.InternalMessageInfo

func (m *GetBalanceResponseBody) GetBalance() int32 {
	if m != nil {
		return m.Balance
	}
	return 0
}

// TradeRequest is the request of both
// /rest/api/consume and /rest/api/prepare
type TradeRequest struct {
	AppId                string   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OutTradeNo           string   `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	BuyerId              uint32   `protobuf:"varint,3,opt,name=buyerId,proto3" json:"buyerId,omitempty"`
	BuyerName            string   `protobuf:"bytes,4,opt,name=buyerName,proto3" json:"buyerName,omitempty"`
	CommodityId          string   `protobuf:"bytes,5,opt,name=commodityId,proto3" json:"commodityId,omitempty"`
	CommodityName        string   `protobuf:"bytes,6,opt,name=commodityName,proto3" json:"commodityName,omitempty"`
	Num                  uint32   `protobuf:"varint,7,opt,name=num,proto3" json:"num,omitempty"`
	UnitPrice            uint32   `protobuf:"varint,8,opt,name=unitPrice,proto3" json:"unitPrice,omitempty"`
	Price                uint32   `protobuf:"varint,9,opt,name=price,proto3" json:"price,omitempty"`
	Note                 string   `protobuf:"bytes,10,opt,name=note,proto3" json:"note,omitempty"`
	Platform             string   `protobuf:"bytes,11,opt,name=platform,proto3" json:"platform,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TradeRequest) Reset()         { *m = TradeRequest{} }
func (m *TradeRequest) String() string { return proto.CompactTextString(m) }
func (*TradeRequest) ProtoMessage()    {}
func (*TradeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_tbean_24b0c9375db4cb91, []int{3}
}
func (m *TradeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TradeRequest.Unmarshal(m, b)
}
func (m *TradeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TradeRequest.Marshal(b, m, deterministic)
}
func (dst *TradeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TradeRequest.Merge(dst, src)
}
func (m *TradeRequest) XXX_Size() int {
	return xxx_messageInfo_TradeRequest.Size(m)
}
func (m *TradeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TradeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TradeRequest proto.InternalMessageInfo

func (m *TradeRequest) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *TradeRequest) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *TradeRequest) GetBuyerId() uint32 {
	if m != nil {
		return m.BuyerId
	}
	return 0
}

func (m *TradeRequest) GetBuyerName() string {
	if m != nil {
		return m.BuyerName
	}
	return ""
}

func (m *TradeRequest) GetCommodityId() string {
	if m != nil {
		return m.CommodityId
	}
	return ""
}

func (m *TradeRequest) GetCommodityName() string {
	if m != nil {
		return m.CommodityName
	}
	return ""
}

func (m *TradeRequest) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *TradeRequest) GetUnitPrice() uint32 {
	if m != nil {
		return m.UnitPrice
	}
	return 0
}

func (m *TradeRequest) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *TradeRequest) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *TradeRequest) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

type TradeResponseBody struct {
	AppId                string   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OutTradeNo           string   `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	BuyerId              uint32   `protobuf:"varint,3,opt,name=buyerId,proto3" json:"buyerId,omitempty"`
	BuyerName            string   `protobuf:"bytes,4,opt,name=buyerName,proto3" json:"buyerName,omitempty"`
	CommodityId          string   `protobuf:"bytes,5,opt,name=commodityId,proto3" json:"commodityId,omitempty"`
	CommodityName        string   `protobuf:"bytes,6,opt,name=commodityName,proto3" json:"commodityName,omitempty"`
	Num                  uint32   `protobuf:"varint,7,opt,name=num,proto3" json:"num,omitempty"`
	UnitPrice            uint32   `protobuf:"varint,8,opt,name=unitPrice,proto3" json:"unitPrice,omitempty"`
	Price                uint32   `protobuf:"varint,9,opt,name=price,proto3" json:"price,omitempty"`
	TradeNo              string   `protobuf:"bytes,10,opt,name=tradeNo,proto3" json:"tradeNo,omitempty"`
	Ctime                string   `protobuf:"bytes,11,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Balance              int32    `protobuf:"varint,12,opt,name=balance,proto3" json:"balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TradeResponseBody) Reset()         { *m = TradeResponseBody{} }
func (m *TradeResponseBody) String() string { return proto.CompactTextString(m) }
func (*TradeResponseBody) ProtoMessage()    {}
func (*TradeResponseBody) Descriptor() ([]byte, []int) {
	return fileDescriptor_tbean_24b0c9375db4cb91, []int{4}
}
func (m *TradeResponseBody) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TradeResponseBody.Unmarshal(m, b)
}
func (m *TradeResponseBody) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TradeResponseBody.Marshal(b, m, deterministic)
}
func (dst *TradeResponseBody) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TradeResponseBody.Merge(dst, src)
}
func (m *TradeResponseBody) XXX_Size() int {
	return xxx_messageInfo_TradeResponseBody.Size(m)
}
func (m *TradeResponseBody) XXX_DiscardUnknown() {
	xxx_messageInfo_TradeResponseBody.DiscardUnknown(m)
}

var xxx_messageInfo_TradeResponseBody proto.InternalMessageInfo

func (m *TradeResponseBody) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *TradeResponseBody) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *TradeResponseBody) GetBuyerId() uint32 {
	if m != nil {
		return m.BuyerId
	}
	return 0
}

func (m *TradeResponseBody) GetBuyerName() string {
	if m != nil {
		return m.BuyerName
	}
	return ""
}

func (m *TradeResponseBody) GetCommodityId() string {
	if m != nil {
		return m.CommodityId
	}
	return ""
}

func (m *TradeResponseBody) GetCommodityName() string {
	if m != nil {
		return m.CommodityName
	}
	return ""
}

func (m *TradeResponseBody) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *TradeResponseBody) GetUnitPrice() uint32 {
	if m != nil {
		return m.UnitPrice
	}
	return 0
}

func (m *TradeResponseBody) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *TradeResponseBody) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *TradeResponseBody) GetCtime() string {
	if m != nil {
		return m.Ctime
	}
	return ""
}

func (m *TradeResponseBody) GetBalance() int32 {
	if m != nil {
		return m.Balance
	}
	return 0
}

type TradeNotifyRequest struct {
	AppId                string                     `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OutTradeNo           string                     `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	TradeNo              string                     `protobuf:"bytes,3,opt,name=tradeNo,proto3" json:"tradeNo,omitempty"`
	Command              TradeNotifyRequest_Command `protobuf:"varint,4,opt,name=command,proto3,enum=tbean.TradeNotifyRequest_Command" json:"command,omitempty"`
	Platform             string                     `protobuf:"bytes,5,opt,name=platform,proto3" json:"platform,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *TradeNotifyRequest) Reset()         { *m = TradeNotifyRequest{} }
func (m *TradeNotifyRequest) String() string { return proto.CompactTextString(m) }
func (*TradeNotifyRequest) ProtoMessage()    {}
func (*TradeNotifyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_tbean_24b0c9375db4cb91, []int{5}
}
func (m *TradeNotifyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TradeNotifyRequest.Unmarshal(m, b)
}
func (m *TradeNotifyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TradeNotifyRequest.Marshal(b, m, deterministic)
}
func (dst *TradeNotifyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TradeNotifyRequest.Merge(dst, src)
}
func (m *TradeNotifyRequest) XXX_Size() int {
	return xxx_messageInfo_TradeNotifyRequest.Size(m)
}
func (m *TradeNotifyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TradeNotifyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TradeNotifyRequest proto.InternalMessageInfo

func (m *TradeNotifyRequest) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *TradeNotifyRequest) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *TradeNotifyRequest) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *TradeNotifyRequest) GetCommand() TradeNotifyRequest_Command {
	if m != nil {
		return m.Command
	}
	return TradeNotifyRequest_COMMIT
}

func (m *TradeNotifyRequest) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

type TradeNotifyResponseBody struct {
	AppId                string   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OutTradeNo           string   `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	TradeNo              string   `protobuf:"bytes,3,opt,name=tradeNo,proto3" json:"tradeNo,omitempty"`
	Status               string   `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	Mtime                string   `protobuf:"bytes,5,opt,name=mtime,proto3" json:"mtime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TradeNotifyResponseBody) Reset()         { *m = TradeNotifyResponseBody{} }
func (m *TradeNotifyResponseBody) String() string { return proto.CompactTextString(m) }
func (*TradeNotifyResponseBody) ProtoMessage()    {}
func (*TradeNotifyResponseBody) Descriptor() ([]byte, []int) {
	return fileDescriptor_tbean_24b0c9375db4cb91, []int{6}
}
func (m *TradeNotifyResponseBody) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TradeNotifyResponseBody.Unmarshal(m, b)
}
func (m *TradeNotifyResponseBody) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TradeNotifyResponseBody.Marshal(b, m, deterministic)
}
func (dst *TradeNotifyResponseBody) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TradeNotifyResponseBody.Merge(dst, src)
}
func (m *TradeNotifyResponseBody) XXX_Size() int {
	return xxx_messageInfo_TradeNotifyResponseBody.Size(m)
}
func (m *TradeNotifyResponseBody) XXX_DiscardUnknown() {
	xxx_messageInfo_TradeNotifyResponseBody.DiscardUnknown(m)
}

var xxx_messageInfo_TradeNotifyResponseBody proto.InternalMessageInfo

func (m *TradeNotifyResponseBody) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *TradeNotifyResponseBody) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *TradeNotifyResponseBody) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *TradeNotifyResponseBody) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *TradeNotifyResponseBody) GetMtime() string {
	if m != nil {
		return m.Mtime
	}
	return ""
}

// Transfer from internal account to custom account
// API: /rest/api/transfer-i2c
type TransferI2CRequest struct {
	AppId                string                   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OutTradeNo           string                   `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	From                 uint32                   `protobuf:"varint,3,opt,name=from,proto3" json:"from,omitempty"`
	To                   []*TransferI2CRequest_To `protobuf:"bytes,4,rep,name=to,proto3" json:"to,omitempty"`
	Notes                string                   `protobuf:"bytes,5,opt,name=notes,proto3" json:"notes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *TransferI2CRequest) Reset()         { *m = TransferI2CRequest{} }
func (m *TransferI2CRequest) String() string { return proto.CompactTextString(m) }
func (*TransferI2CRequest) ProtoMessage()    {}
func (*TransferI2CRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_tbean_24b0c9375db4cb91, []int{7}
}
func (m *TransferI2CRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransferI2CRequest.Unmarshal(m, b)
}
func (m *TransferI2CRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransferI2CRequest.Marshal(b, m, deterministic)
}
func (dst *TransferI2CRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransferI2CRequest.Merge(dst, src)
}
func (m *TransferI2CRequest) XXX_Size() int {
	return xxx_messageInfo_TransferI2CRequest.Size(m)
}
func (m *TransferI2CRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TransferI2CRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TransferI2CRequest proto.InternalMessageInfo

func (m *TransferI2CRequest) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *TransferI2CRequest) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *TransferI2CRequest) GetFrom() uint32 {
	if m != nil {
		return m.From
	}
	return 0
}

func (m *TransferI2CRequest) GetTo() []*TransferI2CRequest_To {
	if m != nil {
		return m.To
	}
	return nil
}

func (m *TransferI2CRequest) GetNotes() string {
	if m != nil {
		return m.Notes
	}
	return ""
}

type TransferI2CRequest_To struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Amount               uint64   `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TransferI2CRequest_To) Reset()         { *m = TransferI2CRequest_To{} }
func (m *TransferI2CRequest_To) String() string { return proto.CompactTextString(m) }
func (*TransferI2CRequest_To) ProtoMessage()    {}
func (*TransferI2CRequest_To) Descriptor() ([]byte, []int) {
	return fileDescriptor_tbean_24b0c9375db4cb91, []int{7, 0}
}
func (m *TransferI2CRequest_To) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransferI2CRequest_To.Unmarshal(m, b)
}
func (m *TransferI2CRequest_To) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransferI2CRequest_To.Marshal(b, m, deterministic)
}
func (dst *TransferI2CRequest_To) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransferI2CRequest_To.Merge(dst, src)
}
func (m *TransferI2CRequest_To) XXX_Size() int {
	return xxx_messageInfo_TransferI2CRequest_To.Size(m)
}
func (m *TransferI2CRequest_To) XXX_DiscardUnknown() {
	xxx_messageInfo_TransferI2CRequest_To.DiscardUnknown(m)
}

var xxx_messageInfo_TransferI2CRequest_To proto.InternalMessageInfo

func (m *TransferI2CRequest_To) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TransferI2CRequest_To) GetAmount() uint64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

type TransferI2CResponseBody struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TransferI2CResponseBody) Reset()         { *m = TransferI2CResponseBody{} }
func (m *TransferI2CResponseBody) String() string { return proto.CompactTextString(m) }
func (*TransferI2CResponseBody) ProtoMessage()    {}
func (*TransferI2CResponseBody) Descriptor() ([]byte, []int) {
	return fileDescriptor_tbean_24b0c9375db4cb91, []int{8}
}
func (m *TransferI2CResponseBody) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransferI2CResponseBody.Unmarshal(m, b)
}
func (m *TransferI2CResponseBody) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransferI2CResponseBody.Marshal(b, m, deterministic)
}
func (dst *TransferI2CResponseBody) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransferI2CResponseBody.Merge(dst, src)
}
func (m *TransferI2CResponseBody) XXX_Size() int {
	return xxx_messageInfo_TransferI2CResponseBody.Size(m)
}
func (m *TransferI2CResponseBody) XXX_DiscardUnknown() {
	xxx_messageInfo_TransferI2CResponseBody.DiscardUnknown(m)
}

var xxx_messageInfo_TransferI2CResponseBody proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ApiResponseHeader)(nil), "tbean.ApiResponseHeader")
	proto.RegisterType((*GetBalanceRequest)(nil), "tbean.GetBalanceRequest")
	proto.RegisterType((*GetBalanceResponseBody)(nil), "tbean.GetBalanceResponseBody")
	proto.RegisterType((*TradeRequest)(nil), "tbean.TradeRequest")
	proto.RegisterType((*TradeResponseBody)(nil), "tbean.TradeResponseBody")
	proto.RegisterType((*TradeNotifyRequest)(nil), "tbean.TradeNotifyRequest")
	proto.RegisterType((*TradeNotifyResponseBody)(nil), "tbean.TradeNotifyResponseBody")
	proto.RegisterType((*TransferI2CRequest)(nil), "tbean.TransferI2CRequest")
	proto.RegisterType((*TransferI2CRequest_To)(nil), "tbean.TransferI2CRequest.To")
	proto.RegisterType((*TransferI2CResponseBody)(nil), "tbean.TransferI2CResponseBody")
	proto.RegisterEnum("tbean.TradeNotifyRequest_Command", TradeNotifyRequest_Command_name, TradeNotifyRequest_Command_value)
}

func init() { proto.RegisterFile("services/tbean/tbean.proto", fileDescriptor_tbean_24b0c9375db4cb91) }

var fileDescriptor_tbean_24b0c9375db4cb91 = []byte{
	// 554 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x54, 0xcd, 0x8e, 0xd3, 0x30,
	0x10, 0x26, 0xe9, 0xff, 0xb4, 0x8b, 0x5a, 0x0b, 0x15, 0x53, 0xad, 0x50, 0x09, 0x1c, 0x7a, 0x40,
	0x45, 0x2a, 0x47, 0x4e, 0xdb, 0x0a, 0x41, 0xc5, 0xee, 0x16, 0x45, 0x7d, 0x01, 0xb7, 0x71, 0x51,
	0xa4, 0x3a, 0x0e, 0xb1, 0x83, 0xd4, 0x27, 0xe1, 0x5d, 0x78, 0x0c, 0x6e, 0x5c, 0x79, 0x12, 0xe4,
	0xb1, 0xdd, 0x36, 0x8b, 0x10, 0x87, 0xe5, 0xc6, 0xa5, 0x9a, 0x6f, 0x3c, 0x99, 0xf9, 0xe6, 0xeb,
	0x67, 0xc3, 0x48, 0xf1, 0xe2, 0x4b, 0xba, 0xe5, 0xea, 0x95, 0xde, 0x70, 0x96, 0xd9, 0xdf, 0x69,
	0x5e, 0x48, 0x2d, 0x49, 0x03, 0x41, 0xf4, 0x16, 0x06, 0x57, 0x79, 0x1a, 0x73, 0x95, 0xcb, 0x4c,
	0xf1, 0xf7, 0x9c, 0x25, 0xbc, 0x20, 0x43, 0x68, 0x16, 0x5c, 0x95, 0x7b, 0x4d, 0x83, 0x71, 0x30,
	0xe9, 0xc4, 0x0e, 0x11, 0x0a, 0x2d, 0xc1, 0x95, 0x62, 0x9f, 0x38, 0x0d, 0xf1, 0xc0, 0xc3, 0x68,
	0x05, 0x83, 0x77, 0x5c, 0xcf, 0xd9, 0x9e, 0x65, 0x5b, 0x1e, 0xf3, 0xcf, 0x25, 0x57, 0x9a, 0x3c,
	0x82, 0x06, 0xcb, 0xf3, 0x65, 0xe2, 0xba, 0x58, 0x40, 0x08, 0xd4, 0xf5, 0x21, 0xf7, 0x1d, 0x30,
	0x26, 0x7d, 0xa8, 0x95, 0x69, 0x42, 0x6b, 0xe3, 0x60, 0x72, 0x11, 0x9b, 0x30, 0x9a, 0xc1, 0xf0,
	0xbc, 0xa1, 0xa5, 0x37, 0x97, 0xc9, 0xc1, 0x90, 0xd8, 0xd8, 0x34, 0xf6, 0x6d, 0xc4, 0x1e, 0x46,
	0xdf, 0x42, 0xe8, 0xad, 0x0b, 0x96, 0xfc, 0x85, 0xc0, 0x53, 0x00, 0x59, 0x6a, 0x2c, 0xbc, 0x95,
	0x8e, 0xc6, 0x59, 0x06, 0x07, 0x94, 0x07, 0x5e, 0x2c, 0x3d, 0x21, 0x0f, 0xc9, 0x25, 0x74, 0x30,
	0xbc, 0x65, 0x82, 0xd3, 0x3a, 0x7e, 0x78, 0x4a, 0x90, 0x31, 0x74, 0xb7, 0x52, 0x08, 0x99, 0xa4,
	0xfa, 0xb0, 0x4c, 0x68, 0x03, 0xcf, 0xcf, 0x53, 0xe4, 0x05, 0x5c, 0x1c, 0x21, 0xf6, 0x68, 0x62,
	0x4d, 0x35, 0x69, 0xc4, 0xc8, 0x4a, 0x41, 0x5b, 0x56, 0x8c, 0xac, 0x14, 0x66, 0x6e, 0x99, 0xa5,
	0xfa, 0x63, 0x91, 0x6e, 0x39, 0x6d, 0x63, 0xfe, 0x94, 0x30, 0x5b, 0xe6, 0x78, 0xd2, 0xc1, 0x13,
	0x0b, 0x8c, 0xcc, 0x99, 0xd4, 0x9c, 0x82, 0x95, 0xd9, 0xc4, 0x64, 0x04, 0xed, 0x7c, 0xcf, 0xf4,
	0x4e, 0x16, 0x82, 0x76, 0x31, 0x7f, 0xc4, 0xd1, 0x8f, 0x10, 0x06, 0x4e, 0xbc, 0x33, 0xb1, 0xff,
	0x67, 0x05, 0x29, 0xb4, 0xb4, 0x5b, 0xd1, 0x8a, 0xe8, 0xa1, 0xa9, 0xdf, 0xea, 0x54, 0x70, 0x27,
	0xa2, 0x05, 0xe7, 0xc6, 0xec, 0x55, 0x8d, 0xf9, 0x33, 0x00, 0xe2, 0xb4, 0xd1, 0xe9, 0xee, 0x70,
	0x6f, 0x7b, 0x7a, 0x5a, 0xb5, 0x2a, 0xad, 0x37, 0xd0, 0x32, 0x3a, 0xb0, 0x2c, 0x41, 0x69, 0x1f,
	0xce, 0x9e, 0x4d, 0xed, 0x8d, 0xff, 0x7d, 0xf6, 0x74, 0x61, 0x0b, 0x63, 0xff, 0x45, 0xc5, 0x1b,
	0x8d, 0x3b, 0xde, 0x78, 0x0e, 0x2d, 0x57, 0x4f, 0x00, 0x9a, 0x8b, 0xd5, 0xcd, 0xcd, 0x72, 0xdd,
	0x7f, 0x40, 0x7a, 0xd0, 0x8e, 0x57, 0xd7, 0xd7, 0xf3, 0xab, 0xc5, 0x87, 0x7e, 0x10, 0x7d, 0x0d,
	0xe0, 0x71, 0x65, 0xd0, 0xbf, 0xb0, 0xd1, 0x1f, 0x36, 0x1d, 0x42, 0x53, 0x69, 0xa6, 0x4b, 0xe5,
	0x3c, 0xe4, 0x90, 0x99, 0x23, 0xf0, 0x8f, 0xb1, 0x1b, 0x58, 0x10, 0x7d, 0xb7, 0xf2, 0x67, 0x6a,
	0xc7, 0x8b, 0xe5, 0x6c, 0x71, 0x3f, 0xf9, 0x09, 0xd4, 0x77, 0x85, 0x14, 0xce, 0xd8, 0x18, 0x93,
	0x97, 0x10, 0x6a, 0x49, 0xeb, 0xe3, 0xda, 0xa4, 0x3b, 0xbb, 0x3c, 0x69, 0x7e, 0x67, 0xe0, 0x74,
	0x2d, 0xe3, 0x50, 0xa3, 0x7b, 0xcc, 0x6d, 0x54, 0x9e, 0x24, 0x82, 0xd1, 0x14, 0xc2, 0xb5, 0xf4,
	0x0f, 0x61, 0x70, 0x7c, 0x08, 0xcd, 0xaa, 0x4c, 0xc8, 0x32, 0xd3, 0xc8, 0xa5, 0x1e, 0x3b, 0x14,
	0x3d, 0x41, 0xb5, 0x4f, 0x23, 0x4e, 0x6a, 0x6f, 0x9a, 0xf8, 0xc2, 0xbf, 0xfe, 0x15, 0x00, 0x00,
	0xff, 0xff, 0xe5, 0x00, 0x0f, 0xcc, 0xff, 0x05, 0x00, 0x00,
}
