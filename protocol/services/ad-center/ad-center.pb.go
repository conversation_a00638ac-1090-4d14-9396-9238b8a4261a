// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ad-center/ad-center.proto

package ad_center // import "golang.52tt.com/protocol/services/ad-center"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Ad_Mic_Mode_Type int32

const (
	// 非法
	Ad_Mic_Mode_Type_INVALID Ad_Mic_Mode_Type = 0
	// 娱乐模式(派对，点唱厅，扩列，踢保，小故事)
	Ad_Mic_Mode_Type_PGC_Fun Ad_Mic_Mode_Type = 1
	// CP战
	Ad_Mic_Mode_Type_PGC_CP Ad_Mic_Mode_Type = 2
	// 狼人杀
	Ad_Mic_Mode_Type_PGC_WarewolvesGame Ad_Mic_Mode_Type = 3
	// pia戏
	Ad_Mic_Mode_Type_PGC_PIA Ad_Mic_Mode_Type = 4
	// 相亲交友
	Ad_Mic_Mode_Type_PGC_Dating Ad_Mic_Mode_Type = 5
)

var Ad_Mic_Mode_Type_name = map[int32]string{
	0: "INVALID",
	1: "PGC_Fun",
	2: "PGC_CP",
	3: "PGC_WarewolvesGame",
	4: "PGC_PIA",
	5: "PGC_Dating",
}
var Ad_Mic_Mode_Type_value = map[string]int32{
	"INVALID":            0,
	"PGC_Fun":            1,
	"PGC_CP":             2,
	"PGC_WarewolvesGame": 3,
	"PGC_PIA":            4,
	"PGC_Dating":         5,
}

func (x Ad_Mic_Mode_Type) String() string {
	return proto.EnumName(Ad_Mic_Mode_Type_name, int32(x))
}
func (Ad_Mic_Mode_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{0}
}

type VisibleUserType int32

const (
	VisibleUserType_VisibleUserType_UNDEFINED VisibleUserType = 0
	VisibleUserType_VisibleUserType_ALL       VisibleUserType = 1
	VisibleUserType_VisibleUserType_FRESHMAN  VisibleUserType = 2
)

var VisibleUserType_name = map[int32]string{
	0: "VisibleUserType_UNDEFINED",
	1: "VisibleUserType_ALL",
	2: "VisibleUserType_FRESHMAN",
}
var VisibleUserType_value = map[string]int32{
	"VisibleUserType_UNDEFINED": 0,
	"VisibleUserType_ALL":       1,
	"VisibleUserType_FRESHMAN":  2,
}

func (x VisibleUserType) String() string {
	return proto.EnumName(VisibleUserType_name, int32(x))
}
func (VisibleUserType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{1}
}

type Area_Type int32

const (
	Area_Type_Music    Area_Type = 0
	Area_Type_Activity Area_Type = 1
)

var Area_Type_name = map[int32]string{
	0: "Music",
	1: "Activity",
}
var Area_Type_value = map[string]int32{
	"Music":    0,
	"Activity": 1,
}

func (x Area_Type) String() string {
	return proto.EnumName(Area_Type_name, int32(x))
}
func (Area_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{2}
}

type CampaignCreativeType int32

const (
	// 图文
	CampaignCreativeType_TELETEXT CampaignCreativeType = 0
	// 轮播图
	CampaignCreativeType_SLIDESHOW CampaignCreativeType = 1
	// 信息流
	CampaignCreativeType_FLOW CampaignCreativeType = 3
	// 视频
	CampaignCreativeType_VIDEO CampaignCreativeType = 4
)

var CampaignCreativeType_name = map[int32]string{
	0: "TELETEXT",
	1: "SLIDESHOW",
	3: "FLOW",
	4: "VIDEO",
}
var CampaignCreativeType_value = map[string]int32{
	"TELETEXT":  0,
	"SLIDESHOW": 1,
	"FLOW":      3,
	"VIDEO":     4,
}

func (x CampaignCreativeType) String() string {
	return proto.EnumName(CampaignCreativeType_name, int32(x))
}
func (CampaignCreativeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{3}
}

type AdCenterStatus int32

const (
	// 关闭
	AdCenterStatus_CLOSE AdCenterStatus = 0
	// 开启
	AdCenterStatus_OPEN AdCenterStatus = 1
)

var AdCenterStatus_name = map[int32]string{
	0: "CLOSE",
	1: "OPEN",
}
var AdCenterStatus_value = map[string]int32{
	"CLOSE": 0,
	"OPEN":  1,
}

func (x AdCenterStatus) String() string {
	return proto.EnumName(AdCenterStatus_name, int32(x))
}
func (AdCenterStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{4}
}

type BatchGetAdReq_Ad_Config_Extra_Type int32

const (
	// 非法
	BatchGetAdReq_INVALID BatchGetAdReq_Ad_Config_Extra_Type = 0
	// 活动中心
	BatchGetAdReq_ActivityArea BatchGetAdReq_Ad_Config_Extra_Type = 1
)

var BatchGetAdReq_Ad_Config_Extra_Type_name = map[int32]string{
	0: "INVALID",
	1: "ActivityArea",
}
var BatchGetAdReq_Ad_Config_Extra_Type_value = map[string]int32{
	"INVALID":      0,
	"ActivityArea": 1,
}

func (x BatchGetAdReq_Ad_Config_Extra_Type) String() string {
	return proto.EnumName(BatchGetAdReq_Ad_Config_Extra_Type_name, int32(x))
}
func (BatchGetAdReq_Ad_Config_Extra_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{0, 0}
}

type Campaign_PolicyType int32

const (
	// 普通
	Campaign_NORMAL Campaign_PolicyType = 0
	// 赛马
	Campaign_OPTIMIZE Campaign_PolicyType = 1
	// ab测
	Campaign_ABTEST Campaign_PolicyType = 2
	// 随机类型
	Campaign_RANDOM Campaign_PolicyType = 3
)

var Campaign_PolicyType_name = map[int32]string{
	0: "NORMAL",
	1: "OPTIMIZE",
	2: "ABTEST",
	3: "RANDOM",
}
var Campaign_PolicyType_value = map[string]int32{
	"NORMAL":   0,
	"OPTIMIZE": 1,
	"ABTEST":   2,
	"RANDOM":   3,
}

func (x Campaign_PolicyType) String() string {
	return proto.EnumName(Campaign_PolicyType_name, int32(x))
}
func (Campaign_PolicyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{17, 0}
}

type SearchCampaignsReq_CampaignFilterStatusDesc int32

const (
	// 未知
	SearchCampaignsReq_UNKNOW SearchCampaignsReq_CampaignFilterStatusDesc = 0
	// 未生效
	SearchCampaignsReq_CLOSE SearchCampaignsReq_CampaignFilterStatusDesc = 1
	// 生效中
	SearchCampaignsReq_OPEN SearchCampaignsReq_CampaignFilterStatusDesc = 2
	// 已过期
	SearchCampaignsReq_EXPIRED SearchCampaignsReq_CampaignFilterStatusDesc = 3
)

var SearchCampaignsReq_CampaignFilterStatusDesc_name = map[int32]string{
	0: "UNKNOW",
	1: "CLOSE",
	2: "OPEN",
	3: "EXPIRED",
}
var SearchCampaignsReq_CampaignFilterStatusDesc_value = map[string]int32{
	"UNKNOW":  0,
	"CLOSE":   1,
	"OPEN":    2,
	"EXPIRED": 3,
}

func (x SearchCampaignsReq_CampaignFilterStatusDesc) String() string {
	return proto.EnumName(SearchCampaignsReq_CampaignFilterStatusDesc_name, int32(x))
}
func (SearchCampaignsReq_CampaignFilterStatusDesc) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{25, 0}
}

type SearchCampaignsReq_ChannelType int32

const (
	SearchCampaignsReq_INVALID_CHANNEL_TYPE SearchCampaignsReq_ChannelType = 0
	SearchCampaignsReq_PGC                  SearchCampaignsReq_ChannelType = 1
	SearchCampaignsReq_UGC                  SearchCampaignsReq_ChannelType = 2
	SearchCampaignsReq_LIVE                 SearchCampaignsReq_ChannelType = 3
	SearchCampaignsReq_GUILD_PGC            SearchCampaignsReq_ChannelType = 4
)

var SearchCampaignsReq_ChannelType_name = map[int32]string{
	0: "INVALID_CHANNEL_TYPE",
	1: "PGC",
	2: "UGC",
	3: "LIVE",
	4: "GUILD_PGC",
}
var SearchCampaignsReq_ChannelType_value = map[string]int32{
	"INVALID_CHANNEL_TYPE": 0,
	"PGC":       1,
	"UGC":       2,
	"LIVE":      3,
	"GUILD_PGC": 4,
}

func (x SearchCampaignsReq_ChannelType) String() string {
	return proto.EnumName(SearchCampaignsReq_ChannelType_name, int32(x))
}
func (SearchCampaignsReq_ChannelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{25, 1}
}

type CampaignFilter_CampaignFilterStatusDesc int32

const (
	// 未知
	CampaignFilter_UNKNOW CampaignFilter_CampaignFilterStatusDesc = 0
	// 未生效
	CampaignFilter_CLOSE CampaignFilter_CampaignFilterStatusDesc = 1
	// 生效中
	CampaignFilter_OPEN CampaignFilter_CampaignFilterStatusDesc = 2
	// 已过期
	CampaignFilter_EXPIRED CampaignFilter_CampaignFilterStatusDesc = 3
)

var CampaignFilter_CampaignFilterStatusDesc_name = map[int32]string{
	0: "UNKNOW",
	1: "CLOSE",
	2: "OPEN",
	3: "EXPIRED",
}
var CampaignFilter_CampaignFilterStatusDesc_value = map[string]int32{
	"UNKNOW":  0,
	"CLOSE":   1,
	"OPEN":    2,
	"EXPIRED": 3,
}

func (x CampaignFilter_CampaignFilterStatusDesc) String() string {
	return proto.EnumName(CampaignFilter_CampaignFilterStatusDesc_name, int32(x))
}
func (CampaignFilter_CampaignFilterStatusDesc) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{27, 0}
}

type Filter_Platform int32

const (
	// 全部
	Filter_ALL Filter_Platform = 0
	// android
	Filter_ANDROID Filter_Platform = 1
	// ios
	Filter_IOS Filter_Platform = 2
	// pc
	Filter_PC Filter_Platform = 3
)

var Filter_Platform_name = map[int32]string{
	0: "ALL",
	1: "ANDROID",
	2: "IOS",
	3: "PC",
}
var Filter_Platform_value = map[string]int32{
	"ALL":     0,
	"ANDROID": 1,
	"IOS":     2,
	"PC":      3,
}

func (x Filter_Platform) String() string {
	return proto.EnumName(Filter_Platform_name, int32(x))
}
func (Filter_Platform) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{28, 0}
}

type Filter_ChannelType int32

const (
	Filter_INVALID_CHANNEL_TYPE Filter_ChannelType = 0
	Filter_PGC                  Filter_ChannelType = 1
	Filter_UGC                  Filter_ChannelType = 2
	Filter_LIVE                 Filter_ChannelType = 3
	Filter_GUILD_PGC            Filter_ChannelType = 4
)

var Filter_ChannelType_name = map[int32]string{
	0: "INVALID_CHANNEL_TYPE",
	1: "PGC",
	2: "UGC",
	3: "LIVE",
	4: "GUILD_PGC",
}
var Filter_ChannelType_value = map[string]int32{
	"INVALID_CHANNEL_TYPE": 0,
	"PGC":       1,
	"UGC":       2,
	"LIVE":      3,
	"GUILD_PGC": 4,
}

func (x Filter_ChannelType) String() string {
	return proto.EnumName(Filter_ChannelType_name, int32(x))
}
func (Filter_ChannelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{28, 1}
}

type Filter_MarketId int32

const (
	Filter_MARKET_NONE      Filter_MarketId = 0
	Filter_MARKET_LITE      Filter_MarketId = 1
	Filter_MARKET_HUANYOU   Filter_MarketId = 2
	Filter_MARKET_ZAIYA     Filter_MarketId = 3
	Filter_MARKET_TOP_SPEED Filter_MarketId = 4
	Filter_MARKET_MAIKE     Filter_MarketId = 5
	Filter_MARKET_GAME_SDK  Filter_MarketId = 12
	Filter_MARKET_ALL       Filter_MarketId = 65534
	Filter_MARKET_MAX       Filter_MarketId = 65535
)

var Filter_MarketId_name = map[int32]string{
	0:     "MARKET_NONE",
	1:     "MARKET_LITE",
	2:     "MARKET_HUANYOU",
	3:     "MARKET_ZAIYA",
	4:     "MARKET_TOP_SPEED",
	5:     "MARKET_MAIKE",
	12:    "MARKET_GAME_SDK",
	65534: "MARKET_ALL",
	65535: "MARKET_MAX",
}
var Filter_MarketId_value = map[string]int32{
	"MARKET_NONE":      0,
	"MARKET_LITE":      1,
	"MARKET_HUANYOU":   2,
	"MARKET_ZAIYA":     3,
	"MARKET_TOP_SPEED": 4,
	"MARKET_MAIKE":     5,
	"MARKET_GAME_SDK":  12,
	"MARKET_ALL":       65534,
	"MARKET_MAX":       65535,
}

func (x Filter_MarketId) String() string {
	return proto.EnumName(Filter_MarketId_name, int32(x))
}
func (Filter_MarketId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{28, 2}
}

type Policy_PolicyType int32

const (
	// 普通
	Policy_NORMAL Policy_PolicyType = 0
	// 赛马
	Policy_OPTIMIZE Policy_PolicyType = 1
	// ab测
	Policy_ABTEST Policy_PolicyType = 2
)

var Policy_PolicyType_name = map[int32]string{
	0: "NORMAL",
	1: "OPTIMIZE",
	2: "ABTEST",
}
var Policy_PolicyType_value = map[string]int32{
	"NORMAL":   0,
	"OPTIMIZE": 1,
	"ABTEST":   2,
}

func (x Policy_PolicyType) String() string {
	return proto.EnumName(Policy_PolicyType_name, int32(x))
}
func (Policy_PolicyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{42, 0}
}

// 批量获取广告展示数据
type BatchGetAdReq struct {
	Uid           uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AdIdList      []uint32 `protobuf:"varint,2,rep,packed,name=ad_id_list,json=adIdList,proto3" json:"ad_id_list,omitempty"`
	ChannelId     uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AppId         uint32   `protobuf:"varint,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId      uint32   `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientIp      uint32   `protobuf:"varint,6,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	ClientType    uint32   `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion uint32   `protobuf:"varint,8,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	// 平台
	Platform             uint32   `protobuf:"varint,9,opt,name=platform,proto3" json:"platform,omitempty"`
	Os                   uint32   `protobuf:"varint,10,opt,name=os,proto3" json:"os,omitempty"`
	ChannelType          uint32   `protobuf:"varint,11,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,12,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ChannelTabId         uint32   `protobuf:"varint,13,opt,name=channel_tab_id,json=channelTabId,proto3" json:"channel_tab_id,omitempty"`
	AreaId               uint32   `protobuf:"varint,14,opt,name=area_id,json=areaId,proto3" json:"area_id,omitempty"`
	MicMode              uint32   `protobuf:"varint,15,opt,name=mic_mode,json=micMode,proto3" json:"mic_mode,omitempty"`
	MicCount             uint32   `protobuf:"varint,16,opt,name=mic_count,json=micCount,proto3" json:"mic_count,omitempty"`
	ChannelTagId         uint32   `protobuf:"varint,17,opt,name=channel_tag_id,json=channelTagId,proto3" json:"channel_tag_id,omitempty"`
	MarketChannelId      string   `protobuf:"bytes,18,opt,name=market_channel_id,json=marketChannelId,proto3" json:"market_channel_id,omitempty"`
	GetConfigExtraType   uint32   `protobuf:"varint,19,opt,name=get_config_extra_type,json=getConfigExtraType,proto3" json:"get_config_extra_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAdReq) Reset()         { *m = BatchGetAdReq{} }
func (m *BatchGetAdReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetAdReq) ProtoMessage()    {}
func (*BatchGetAdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{0}
}
func (m *BatchGetAdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAdReq.Unmarshal(m, b)
}
func (m *BatchGetAdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAdReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetAdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAdReq.Merge(dst, src)
}
func (m *BatchGetAdReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetAdReq.Size(m)
}
func (m *BatchGetAdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAdReq proto.InternalMessageInfo

func (m *BatchGetAdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetAdReq) GetAdIdList() []uint32 {
	if m != nil {
		return m.AdIdList
	}
	return nil
}

func (m *BatchGetAdReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatchGetAdReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *BatchGetAdReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *BatchGetAdReq) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

func (m *BatchGetAdReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *BatchGetAdReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *BatchGetAdReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *BatchGetAdReq) GetOs() uint32 {
	if m != nil {
		return m.Os
	}
	return 0
}

func (m *BatchGetAdReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *BatchGetAdReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *BatchGetAdReq) GetChannelTabId() uint32 {
	if m != nil {
		return m.ChannelTabId
	}
	return 0
}

func (m *BatchGetAdReq) GetAreaId() uint32 {
	if m != nil {
		return m.AreaId
	}
	return 0
}

func (m *BatchGetAdReq) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

func (m *BatchGetAdReq) GetMicCount() uint32 {
	if m != nil {
		return m.MicCount
	}
	return 0
}

func (m *BatchGetAdReq) GetChannelTagId() uint32 {
	if m != nil {
		return m.ChannelTagId
	}
	return 0
}

func (m *BatchGetAdReq) GetMarketChannelId() string {
	if m != nil {
		return m.MarketChannelId
	}
	return ""
}

func (m *BatchGetAdReq) GetGetConfigExtraType() uint32 {
	if m != nil {
		return m.GetConfigExtraType
	}
	return 0
}

type ConfigExtra struct {
	AreaInfos            []*AreaInfo `protobuf:"bytes,1,rep,name=area_infos,json=areaInfos,proto3" json:"area_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ConfigExtra) Reset()         { *m = ConfigExtra{} }
func (m *ConfigExtra) String() string { return proto.CompactTextString(m) }
func (*ConfigExtra) ProtoMessage()    {}
func (*ConfigExtra) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{1}
}
func (m *ConfigExtra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfigExtra.Unmarshal(m, b)
}
func (m *ConfigExtra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfigExtra.Marshal(b, m, deterministic)
}
func (dst *ConfigExtra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfigExtra.Merge(dst, src)
}
func (m *ConfigExtra) XXX_Size() int {
	return xxx_messageInfo_ConfigExtra.Size(m)
}
func (m *ConfigExtra) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfigExtra.DiscardUnknown(m)
}

var xxx_messageInfo_ConfigExtra proto.InternalMessageInfo

func (m *ConfigExtra) GetAreaInfos() []*AreaInfo {
	if m != nil {
		return m.AreaInfos
	}
	return nil
}

type BatchGetAdResp struct {
	AdCampaignMap        map[uint32]*CampaignList `protobuf:"bytes,1,rep,name=ad_campaign_map,json=adCampaignMap,proto3" json:"ad_campaign_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	AreaInfo             *AreaInfo                `protobuf:"bytes,2,opt,name=area_info,json=areaInfo,proto3" json:"area_info,omitempty"`
	Extra                *ConfigExtra             `protobuf:"bytes,3,opt,name=extra,proto3" json:"extra,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchGetAdResp) Reset()         { *m = BatchGetAdResp{} }
func (m *BatchGetAdResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetAdResp) ProtoMessage()    {}
func (*BatchGetAdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{2}
}
func (m *BatchGetAdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAdResp.Unmarshal(m, b)
}
func (m *BatchGetAdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAdResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetAdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAdResp.Merge(dst, src)
}
func (m *BatchGetAdResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetAdResp.Size(m)
}
func (m *BatchGetAdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAdResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAdResp proto.InternalMessageInfo

func (m *BatchGetAdResp) GetAdCampaignMap() map[uint32]*CampaignList {
	if m != nil {
		return m.AdCampaignMap
	}
	return nil
}

func (m *BatchGetAdResp) GetAreaInfo() *AreaInfo {
	if m != nil {
		return m.AreaInfo
	}
	return nil
}

func (m *BatchGetAdResp) GetExtra() *ConfigExtra {
	if m != nil {
		return m.Extra
	}
	return nil
}

type CampaignList struct {
	CampaignList         []*Campaign `protobuf:"bytes,1,rep,name=campaign_list,json=campaignList,proto3" json:"campaign_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CampaignList) Reset()         { *m = CampaignList{} }
func (m *CampaignList) String() string { return proto.CompactTextString(m) }
func (*CampaignList) ProtoMessage()    {}
func (*CampaignList) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{3}
}
func (m *CampaignList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CampaignList.Unmarshal(m, b)
}
func (m *CampaignList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CampaignList.Marshal(b, m, deterministic)
}
func (dst *CampaignList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CampaignList.Merge(dst, src)
}
func (m *CampaignList) XXX_Size() int {
	return xxx_messageInfo_CampaignList.Size(m)
}
func (m *CampaignList) XXX_DiscardUnknown() {
	xxx_messageInfo_CampaignList.DiscardUnknown(m)
}

var xxx_messageInfo_CampaignList proto.InternalMessageInfo

func (m *CampaignList) GetCampaignList() []*Campaign {
	if m != nil {
		return m.CampaignList
	}
	return nil
}

type VisibleUser struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	Period               uint32   `protobuf:"varint,3,opt,name=period,proto3" json:"period,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VisibleUser) Reset()         { *m = VisibleUser{} }
func (m *VisibleUser) String() string { return proto.CompactTextString(m) }
func (*VisibleUser) ProtoMessage()    {}
func (*VisibleUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{4}
}
func (m *VisibleUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VisibleUser.Unmarshal(m, b)
}
func (m *VisibleUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VisibleUser.Marshal(b, m, deterministic)
}
func (dst *VisibleUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VisibleUser.Merge(dst, src)
}
func (m *VisibleUser) XXX_Size() int {
	return xxx_messageInfo_VisibleUser.Size(m)
}
func (m *VisibleUser) XXX_DiscardUnknown() {
	xxx_messageInfo_VisibleUser.DiscardUnknown(m)
}

var xxx_messageInfo_VisibleUser proto.InternalMessageInfo

func (m *VisibleUser) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *VisibleUser) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *VisibleUser) GetPeriod() uint32 {
	if m != nil {
		return m.Period
	}
	return 0
}

type AreaInfo struct {
	Id                   uint32       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	UpdateTime           int64        `protobuf:"varint,3,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime           int64        `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Operator             string       `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	AdIds                []uint32     `protobuf:"varint,6,rep,packed,name=ad_ids,json=adIds,proto3" json:"ad_ids,omitempty"`
	Delete               bool         `protobuf:"varint,7,opt,name=delete,proto3" json:"delete,omitempty"`
	Type                 uint32       `protobuf:"varint,8,opt,name=type,proto3" json:"type,omitempty"`
	Channels             []string     `protobuf:"bytes,9,rep,name=channels,proto3" json:"channels,omitempty"`
	User                 *VisibleUser `protobuf:"bytes,10,opt,name=user,proto3" json:"user,omitempty"`
	AreaPriority         uint32       `protobuf:"varint,27,opt,name=area_priority,json=areaPriority,proto3" json:"area_priority,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AreaInfo) Reset()         { *m = AreaInfo{} }
func (m *AreaInfo) String() string { return proto.CompactTextString(m) }
func (*AreaInfo) ProtoMessage()    {}
func (*AreaInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{5}
}
func (m *AreaInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AreaInfo.Unmarshal(m, b)
}
func (m *AreaInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AreaInfo.Marshal(b, m, deterministic)
}
func (dst *AreaInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AreaInfo.Merge(dst, src)
}
func (m *AreaInfo) XXX_Size() int {
	return xxx_messageInfo_AreaInfo.Size(m)
}
func (m *AreaInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AreaInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AreaInfo proto.InternalMessageInfo

func (m *AreaInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AreaInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AreaInfo) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *AreaInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *AreaInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *AreaInfo) GetAdIds() []uint32 {
	if m != nil {
		return m.AdIds
	}
	return nil
}

func (m *AreaInfo) GetDelete() bool {
	if m != nil {
		return m.Delete
	}
	return false
}

func (m *AreaInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AreaInfo) GetChannels() []string {
	if m != nil {
		return m.Channels
	}
	return nil
}

func (m *AreaInfo) GetUser() *VisibleUser {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *AreaInfo) GetAreaPriority() uint32 {
	if m != nil {
		return m.AreaPriority
	}
	return 0
}

type GetAreaInfoListReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAreaInfoListReq) Reset()         { *m = GetAreaInfoListReq{} }
func (m *GetAreaInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetAreaInfoListReq) ProtoMessage()    {}
func (*GetAreaInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{6}
}
func (m *GetAreaInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAreaInfoListReq.Unmarshal(m, b)
}
func (m *GetAreaInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAreaInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetAreaInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAreaInfoListReq.Merge(dst, src)
}
func (m *GetAreaInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetAreaInfoListReq.Size(m)
}
func (m *GetAreaInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAreaInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAreaInfoListReq proto.InternalMessageInfo

func (m *GetAreaInfoListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetAreaInfoListResp struct {
	AreaList             []*AreaInfo `protobuf:"bytes,1,rep,name=area_list,json=areaList,proto3" json:"area_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetAreaInfoListResp) Reset()         { *m = GetAreaInfoListResp{} }
func (m *GetAreaInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetAreaInfoListResp) ProtoMessage()    {}
func (*GetAreaInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{7}
}
func (m *GetAreaInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAreaInfoListResp.Unmarshal(m, b)
}
func (m *GetAreaInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAreaInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetAreaInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAreaInfoListResp.Merge(dst, src)
}
func (m *GetAreaInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetAreaInfoListResp.Size(m)
}
func (m *GetAreaInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAreaInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAreaInfoListResp proto.InternalMessageInfo

func (m *GetAreaInfoListResp) GetAreaList() []*AreaInfo {
	if m != nil {
		return m.AreaList
	}
	return nil
}

type SetAreaInfoReq struct {
	AreaInfos            []*AreaInfo `protobuf:"bytes,1,rep,name=area_infos,json=areaInfos,proto3" json:"area_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SetAreaInfoReq) Reset()         { *m = SetAreaInfoReq{} }
func (m *SetAreaInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetAreaInfoReq) ProtoMessage()    {}
func (*SetAreaInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{8}
}
func (m *SetAreaInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAreaInfoReq.Unmarshal(m, b)
}
func (m *SetAreaInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAreaInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetAreaInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAreaInfoReq.Merge(dst, src)
}
func (m *SetAreaInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetAreaInfoReq.Size(m)
}
func (m *SetAreaInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAreaInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetAreaInfoReq proto.InternalMessageInfo

func (m *SetAreaInfoReq) GetAreaInfos() []*AreaInfo {
	if m != nil {
		return m.AreaInfos
	}
	return nil
}

type SetAreaInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAreaInfoResp) Reset()         { *m = SetAreaInfoResp{} }
func (m *SetAreaInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetAreaInfoResp) ProtoMessage()    {}
func (*SetAreaInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{9}
}
func (m *SetAreaInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAreaInfoResp.Unmarshal(m, b)
}
func (m *SetAreaInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAreaInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetAreaInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAreaInfoResp.Merge(dst, src)
}
func (m *SetAreaInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetAreaInfoResp.Size(m)
}
func (m *SetAreaInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAreaInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetAreaInfoResp proto.InternalMessageInfo

// ============================== 广告
// 广告位
type Ad struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Height               uint32   `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Width                uint32   `protobuf:"varint,4,opt,name=width,proto3" json:"width,omitempty"`
	Type                 uint32   `protobuf:"varint,5,opt,name=type,proto3" json:"type,omitempty"`
	Status               uint32   `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	UpdateTime           int64    `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime           int64    `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Delete               bool     `protobuf:"varint,9,opt,name=delete,proto3" json:"delete,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ad) Reset()         { *m = Ad{} }
func (m *Ad) String() string { return proto.CompactTextString(m) }
func (*Ad) ProtoMessage()    {}
func (*Ad) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{10}
}
func (m *Ad) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ad.Unmarshal(m, b)
}
func (m *Ad) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ad.Marshal(b, m, deterministic)
}
func (dst *Ad) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ad.Merge(dst, src)
}
func (m *Ad) XXX_Size() int {
	return xxx_messageInfo_Ad.Size(m)
}
func (m *Ad) XXX_DiscardUnknown() {
	xxx_messageInfo_Ad.DiscardUnknown(m)
}

var xxx_messageInfo_Ad proto.InternalMessageInfo

func (m *Ad) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Ad) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Ad) GetHeight() uint32 {
	if m != nil {
		return m.Height
	}
	return 0
}

func (m *Ad) GetWidth() uint32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *Ad) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *Ad) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *Ad) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *Ad) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *Ad) GetDelete() bool {
	if m != nil {
		return m.Delete
	}
	return false
}

// 获取广告位数据
type GetAdReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAdReq) Reset()         { *m = GetAdReq{} }
func (m *GetAdReq) String() string { return proto.CompactTextString(m) }
func (*GetAdReq) ProtoMessage()    {}
func (*GetAdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{11}
}
func (m *GetAdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAdReq.Unmarshal(m, b)
}
func (m *GetAdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAdReq.Marshal(b, m, deterministic)
}
func (dst *GetAdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAdReq.Merge(dst, src)
}
func (m *GetAdReq) XXX_Size() int {
	return xxx_messageInfo_GetAdReq.Size(m)
}
func (m *GetAdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAdReq proto.InternalMessageInfo

func (m *GetAdReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetAdResp struct {
	Ad                   *Ad      `protobuf:"bytes,1,opt,name=Ad,proto3" json:"Ad,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAdResp) Reset()         { *m = GetAdResp{} }
func (m *GetAdResp) String() string { return proto.CompactTextString(m) }
func (*GetAdResp) ProtoMessage()    {}
func (*GetAdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{12}
}
func (m *GetAdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAdResp.Unmarshal(m, b)
}
func (m *GetAdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAdResp.Marshal(b, m, deterministic)
}
func (dst *GetAdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAdResp.Merge(dst, src)
}
func (m *GetAdResp) XXX_Size() int {
	return xxx_messageInfo_GetAdResp.Size(m)
}
func (m *GetAdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAdResp proto.InternalMessageInfo

func (m *GetAdResp) GetAd() *Ad {
	if m != nil {
		return m.Ad
	}
	return nil
}

// 设置广告位数据
type SetAdReq struct {
	Ad                   *Ad      `protobuf:"bytes,1,opt,name=ad,proto3" json:"ad,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAdReq) Reset()         { *m = SetAdReq{} }
func (m *SetAdReq) String() string { return proto.CompactTextString(m) }
func (*SetAdReq) ProtoMessage()    {}
func (*SetAdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{13}
}
func (m *SetAdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAdReq.Unmarshal(m, b)
}
func (m *SetAdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAdReq.Marshal(b, m, deterministic)
}
func (dst *SetAdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAdReq.Merge(dst, src)
}
func (m *SetAdReq) XXX_Size() int {
	return xxx_messageInfo_SetAdReq.Size(m)
}
func (m *SetAdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAdReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetAdReq proto.InternalMessageInfo

func (m *SetAdReq) GetAd() *Ad {
	if m != nil {
		return m.Ad
	}
	return nil
}

type SetAdResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAdResp) Reset()         { *m = SetAdResp{} }
func (m *SetAdResp) String() string { return proto.CompactTextString(m) }
func (*SetAdResp) ProtoMessage()    {}
func (*SetAdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{14}
}
func (m *SetAdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAdResp.Unmarshal(m, b)
}
func (m *SetAdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAdResp.Marshal(b, m, deterministic)
}
func (dst *SetAdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAdResp.Merge(dst, src)
}
func (m *SetAdResp) XXX_Size() int {
	return xxx_messageInfo_SetAdResp.Size(m)
}
func (m *SetAdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAdResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetAdResp proto.InternalMessageInfo

func (m *SetAdResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 查询广告位数据
type SearchAdsReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	BeginTime            int64    `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Name                 string   `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchAdsReq) Reset()         { *m = SearchAdsReq{} }
func (m *SearchAdsReq) String() string { return proto.CompactTextString(m) }
func (*SearchAdsReq) ProtoMessage()    {}
func (*SearchAdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{15}
}
func (m *SearchAdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchAdsReq.Unmarshal(m, b)
}
func (m *SearchAdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchAdsReq.Marshal(b, m, deterministic)
}
func (dst *SearchAdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchAdsReq.Merge(dst, src)
}
func (m *SearchAdsReq) XXX_Size() int {
	return xxx_messageInfo_SearchAdsReq.Size(m)
}
func (m *SearchAdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchAdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchAdsReq proto.InternalMessageInfo

func (m *SearchAdsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchAdsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchAdsReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *SearchAdsReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SearchAdsReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type SearchAdsResp struct {
	Ads                  []*Ad    `protobuf:"bytes,1,rep,name=Ads,proto3" json:"Ads,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchAdsResp) Reset()         { *m = SearchAdsResp{} }
func (m *SearchAdsResp) String() string { return proto.CompactTextString(m) }
func (*SearchAdsResp) ProtoMessage()    {}
func (*SearchAdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{16}
}
func (m *SearchAdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchAdsResp.Unmarshal(m, b)
}
func (m *SearchAdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchAdsResp.Marshal(b, m, deterministic)
}
func (dst *SearchAdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchAdsResp.Merge(dst, src)
}
func (m *SearchAdsResp) XXX_Size() int {
	return xxx_messageInfo_SearchAdsResp.Size(m)
}
func (m *SearchAdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchAdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchAdsResp proto.InternalMessageInfo

func (m *SearchAdsResp) GetAds() []*Ad {
	if m != nil {
		return m.Ads
	}
	return nil
}

// 活动
type Campaign struct {
	Id   uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 图片列表
	ImgUrlList []string `protobuf:"bytes,3,rep,name=img_url_list,json=imgUrlList,proto3" json:"img_url_list,omitempty"`
	// 视频地址
	VideoUrl string `protobuf:"bytes,4,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	// 内容
	Text      string `protobuf:"bytes,5,opt,name=text,proto3" json:"text,omitempty"`
	TextColor string `protobuf:"bytes,6,opt,name=text_color,json=textColor,proto3" json:"text_color,omitempty"`
	// 跳转链接
	JumpUrl string `protobuf:"bytes,7,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	// CampaignCreativeType
	Type uint32 `protobuf:"varint,8,opt,name=type,proto3" json:"type,omitempty"`
	// 开屏广告持续时间
	Duration   uint32 `protobuf:"varint,9,opt,name=duration,proto3" json:"duration,omitempty"`
	Status     uint32 `protobuf:"varint,10,opt,name=status,proto3" json:"status,omitempty"`
	UpdateTime int64  `protobuf:"varint,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime int64  `protobuf:"varint,12,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Delete     bool   `protobuf:"varint,13,opt,name=delete,proto3" json:"delete,omitempty"`
	BeginTime  int64  `protobuf:"varint,14,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime    int64  `protobuf:"varint,15,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 额外信息参数，json
	Extra      string      `protobuf:"bytes,16,opt,name=extra,proto3" json:"extra,omitempty"`
	Materials  []*Material `protobuf:"bytes,17,rep,name=materials,proto3" json:"materials,omitempty"`
	PolicyId   uint32      `protobuf:"varint,18,opt,name=policy_id,json=policyId,proto3" json:"policy_id,omitempty"`
	PolicyType uint32      `protobuf:"varint,19,opt,name=policy_type,json=policyType,proto3" json:"policy_type,omitempty"`
	// 群组链接类型
	TypeJumpUrl          uint32   `protobuf:"varint,20,opt,name=type_jump_url,json=typeJumpUrl,proto3" json:"type_jump_url,omitempty"`
	TgroupIds            []uint32 `protobuf:"varint,21,rep,packed,name=tgroup_ids,json=tgroupIds,proto3" json:"tgroup_ids,omitempty"`
	TablistInsertN       uint32   `protobuf:"varint,22,opt,name=tablist_insert_n,json=tablistInsertN,proto3" json:"tablist_insert_n,omitempty"`
	ContentUrl           string   `protobuf:"bytes,24,opt,name=content_url,json=contentUrl,proto3" json:"content_url,omitempty"`
	SkipWhenN            uint32   `protobuf:"varint,25,opt,name=skip_when_n,json=skipWhenN,proto3" json:"skip_when_n,omitempty"`
	LastPushTime         int64    `protobuf:"varint,26,opt,name=last_push_time,json=lastPushTime,proto3" json:"last_push_time,omitempty"`
	AreaId               uint32   `protobuf:"varint,27,opt,name=area_id,json=areaId,proto3" json:"area_id,omitempty"`
	MysteryplaceId       uint32   `protobuf:"varint,28,opt,name=mysteryplace_id,json=mysteryplaceId,proto3" json:"mysteryplace_id,omitempty"`
	TabAliasName         string   `protobuf:"bytes,29,opt,name=tab_alias_name,json=tabAliasName,proto3" json:"tab_alias_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Campaign) Reset()         { *m = Campaign{} }
func (m *Campaign) String() string { return proto.CompactTextString(m) }
func (*Campaign) ProtoMessage()    {}
func (*Campaign) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{17}
}
func (m *Campaign) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Campaign.Unmarshal(m, b)
}
func (m *Campaign) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Campaign.Marshal(b, m, deterministic)
}
func (dst *Campaign) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Campaign.Merge(dst, src)
}
func (m *Campaign) XXX_Size() int {
	return xxx_messageInfo_Campaign.Size(m)
}
func (m *Campaign) XXX_DiscardUnknown() {
	xxx_messageInfo_Campaign.DiscardUnknown(m)
}

var xxx_messageInfo_Campaign proto.InternalMessageInfo

func (m *Campaign) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Campaign) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Campaign) GetImgUrlList() []string {
	if m != nil {
		return m.ImgUrlList
	}
	return nil
}

func (m *Campaign) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *Campaign) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *Campaign) GetTextColor() string {
	if m != nil {
		return m.TextColor
	}
	return ""
}

func (m *Campaign) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *Campaign) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *Campaign) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *Campaign) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *Campaign) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *Campaign) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *Campaign) GetDelete() bool {
	if m != nil {
		return m.Delete
	}
	return false
}

func (m *Campaign) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *Campaign) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *Campaign) GetExtra() string {
	if m != nil {
		return m.Extra
	}
	return ""
}

func (m *Campaign) GetMaterials() []*Material {
	if m != nil {
		return m.Materials
	}
	return nil
}

func (m *Campaign) GetPolicyId() uint32 {
	if m != nil {
		return m.PolicyId
	}
	return 0
}

func (m *Campaign) GetPolicyType() uint32 {
	if m != nil {
		return m.PolicyType
	}
	return 0
}

func (m *Campaign) GetTypeJumpUrl() uint32 {
	if m != nil {
		return m.TypeJumpUrl
	}
	return 0
}

func (m *Campaign) GetTgroupIds() []uint32 {
	if m != nil {
		return m.TgroupIds
	}
	return nil
}

func (m *Campaign) GetTablistInsertN() uint32 {
	if m != nil {
		return m.TablistInsertN
	}
	return 0
}

func (m *Campaign) GetContentUrl() string {
	if m != nil {
		return m.ContentUrl
	}
	return ""
}

func (m *Campaign) GetSkipWhenN() uint32 {
	if m != nil {
		return m.SkipWhenN
	}
	return 0
}

func (m *Campaign) GetLastPushTime() int64 {
	if m != nil {
		return m.LastPushTime
	}
	return 0
}

func (m *Campaign) GetAreaId() uint32 {
	if m != nil {
		return m.AreaId
	}
	return 0
}

func (m *Campaign) GetMysteryplaceId() uint32 {
	if m != nil {
		return m.MysteryplaceId
	}
	return 0
}

func (m *Campaign) GetTabAliasName() string {
	if m != nil {
		return m.TabAliasName
	}
	return ""
}

type Material struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 图片列表
	ImgUrlList []string `protobuf:"bytes,2,rep,name=img_url_list,json=imgUrlList,proto3" json:"img_url_list,omitempty"`
	// 视频地址
	VideoUrl string `protobuf:"bytes,3,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	// 内容
	TextList      []string `protobuf:"bytes,4,rep,name=text_list,json=textList,proto3" json:"text_list,omitempty"`
	TextColorList []string `protobuf:"bytes,5,rep,name=text_color_list,json=textColorList,proto3" json:"text_color_list,omitempty"`
	OptimizeIndex int32    `protobuf:"varint,6,opt,name=optimize_index,json=optimizeIndex,proto3" json:"optimize_index,omitempty"`
	LabId         uint32   `protobuf:"varint,7,opt,name=lab_id,json=labId,proto3" json:"lab_id,omitempty"`
	JumpUrl       string   `protobuf:"bytes,8,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	WebId         string   `protobuf:"bytes,9,opt,name=web_id,json=webId,proto3" json:"web_id,omitempty"`
	IsHided       bool     `protobuf:"varint,10,opt,name=is_hided,json=isHided,proto3" json:"is_hided,omitempty"`
	TgroupIds     []uint32 `protobuf:"varint,11,rep,packed,name=tgroup_ids,json=tgroupIds,proto3" json:"tgroup_ids,omitempty"`
	// 要展示的人群 视角
	UidViewType          uint32   `protobuf:"varint,12,opt,name=uid_view_type,json=uidViewType,proto3" json:"uid_view_type,omitempty"`
	SkipWhenN            uint32   `protobuf:"varint,13,opt,name=skip_when_n,json=skipWhenN,proto3" json:"skip_when_n,omitempty"`
	ContentUrl           string   `protobuf:"bytes,14,opt,name=content_url,json=contentUrl,proto3" json:"content_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Material) Reset()         { *m = Material{} }
func (m *Material) String() string { return proto.CompactTextString(m) }
func (*Material) ProtoMessage()    {}
func (*Material) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{18}
}
func (m *Material) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Material.Unmarshal(m, b)
}
func (m *Material) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Material.Marshal(b, m, deterministic)
}
func (dst *Material) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Material.Merge(dst, src)
}
func (m *Material) XXX_Size() int {
	return xxx_messageInfo_Material.Size(m)
}
func (m *Material) XXX_DiscardUnknown() {
	xxx_messageInfo_Material.DiscardUnknown(m)
}

var xxx_messageInfo_Material proto.InternalMessageInfo

func (m *Material) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Material) GetImgUrlList() []string {
	if m != nil {
		return m.ImgUrlList
	}
	return nil
}

func (m *Material) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *Material) GetTextList() []string {
	if m != nil {
		return m.TextList
	}
	return nil
}

func (m *Material) GetTextColorList() []string {
	if m != nil {
		return m.TextColorList
	}
	return nil
}

func (m *Material) GetOptimizeIndex() int32 {
	if m != nil {
		return m.OptimizeIndex
	}
	return 0
}

func (m *Material) GetLabId() uint32 {
	if m != nil {
		return m.LabId
	}
	return 0
}

func (m *Material) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *Material) GetWebId() string {
	if m != nil {
		return m.WebId
	}
	return ""
}

func (m *Material) GetIsHided() bool {
	if m != nil {
		return m.IsHided
	}
	return false
}

func (m *Material) GetTgroupIds() []uint32 {
	if m != nil {
		return m.TgroupIds
	}
	return nil
}

func (m *Material) GetUidViewType() uint32 {
	if m != nil {
		return m.UidViewType
	}
	return 0
}

func (m *Material) GetSkipWhenN() uint32 {
	if m != nil {
		return m.SkipWhenN
	}
	return 0
}

func (m *Material) GetContentUrl() string {
	if m != nil {
		return m.ContentUrl
	}
	return ""
}

// 获取广告投放数据
type GetCampaignReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCampaignReq) Reset()         { *m = GetCampaignReq{} }
func (m *GetCampaignReq) String() string { return proto.CompactTextString(m) }
func (*GetCampaignReq) ProtoMessage()    {}
func (*GetCampaignReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{19}
}
func (m *GetCampaignReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCampaignReq.Unmarshal(m, b)
}
func (m *GetCampaignReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCampaignReq.Marshal(b, m, deterministic)
}
func (dst *GetCampaignReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCampaignReq.Merge(dst, src)
}
func (m *GetCampaignReq) XXX_Size() int {
	return xxx_messageInfo_GetCampaignReq.Size(m)
}
func (m *GetCampaignReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCampaignReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCampaignReq proto.InternalMessageInfo

func (m *GetCampaignReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetCampaignResp struct {
	Campaign             *CampaignFilter `protobuf:"bytes,1,opt,name=campaign,proto3" json:"campaign,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetCampaignResp) Reset()         { *m = GetCampaignResp{} }
func (m *GetCampaignResp) String() string { return proto.CompactTextString(m) }
func (*GetCampaignResp) ProtoMessage()    {}
func (*GetCampaignResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{20}
}
func (m *GetCampaignResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCampaignResp.Unmarshal(m, b)
}
func (m *GetCampaignResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCampaignResp.Marshal(b, m, deterministic)
}
func (dst *GetCampaignResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCampaignResp.Merge(dst, src)
}
func (m *GetCampaignResp) XXX_Size() int {
	return xxx_messageInfo_GetCampaignResp.Size(m)
}
func (m *GetCampaignResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCampaignResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCampaignResp proto.InternalMessageInfo

func (m *GetCampaignResp) GetCampaign() *CampaignFilter {
	if m != nil {
		return m.Campaign
	}
	return nil
}

// 设置广告投放数据
type SetCampaignReq struct {
	Campaign             *CampaignFilter `protobuf:"bytes,1,opt,name=campaign,proto3" json:"campaign,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SetCampaignReq) Reset()         { *m = SetCampaignReq{} }
func (m *SetCampaignReq) String() string { return proto.CompactTextString(m) }
func (*SetCampaignReq) ProtoMessage()    {}
func (*SetCampaignReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{21}
}
func (m *SetCampaignReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCampaignReq.Unmarshal(m, b)
}
func (m *SetCampaignReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCampaignReq.Marshal(b, m, deterministic)
}
func (dst *SetCampaignReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCampaignReq.Merge(dst, src)
}
func (m *SetCampaignReq) XXX_Size() int {
	return xxx_messageInfo_SetCampaignReq.Size(m)
}
func (m *SetCampaignReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCampaignReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetCampaignReq proto.InternalMessageInfo

func (m *SetCampaignReq) GetCampaign() *CampaignFilter {
	if m != nil {
		return m.Campaign
	}
	return nil
}

type SetCampaignResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetCampaignResp) Reset()         { *m = SetCampaignResp{} }
func (m *SetCampaignResp) String() string { return proto.CompactTextString(m) }
func (*SetCampaignResp) ProtoMessage()    {}
func (*SetCampaignResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{22}
}
func (m *SetCampaignResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCampaignResp.Unmarshal(m, b)
}
func (m *SetCampaignResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCampaignResp.Marshal(b, m, deterministic)
}
func (dst *SetCampaignResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCampaignResp.Merge(dst, src)
}
func (m *SetCampaignResp) XXX_Size() int {
	return xxx_messageInfo_SetCampaignResp.Size(m)
}
func (m *SetCampaignResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCampaignResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetCampaignResp proto.InternalMessageInfo

func (m *SetCampaignResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type BatchSetCampaignReq struct {
	Campaign             []*CampaignFilter `protobuf:"bytes,1,rep,name=campaign,proto3" json:"campaign,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchSetCampaignReq) Reset()         { *m = BatchSetCampaignReq{} }
func (m *BatchSetCampaignReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetCampaignReq) ProtoMessage()    {}
func (*BatchSetCampaignReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{23}
}
func (m *BatchSetCampaignReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetCampaignReq.Unmarshal(m, b)
}
func (m *BatchSetCampaignReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetCampaignReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetCampaignReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetCampaignReq.Merge(dst, src)
}
func (m *BatchSetCampaignReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetCampaignReq.Size(m)
}
func (m *BatchSetCampaignReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetCampaignReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetCampaignReq proto.InternalMessageInfo

func (m *BatchSetCampaignReq) GetCampaign() []*CampaignFilter {
	if m != nil {
		return m.Campaign
	}
	return nil
}

type BatchSetCampaignResp struct {
	Ids                  []uint32 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetCampaignResp) Reset()         { *m = BatchSetCampaignResp{} }
func (m *BatchSetCampaignResp) String() string { return proto.CompactTextString(m) }
func (*BatchSetCampaignResp) ProtoMessage()    {}
func (*BatchSetCampaignResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{24}
}
func (m *BatchSetCampaignResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetCampaignResp.Unmarshal(m, b)
}
func (m *BatchSetCampaignResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetCampaignResp.Marshal(b, m, deterministic)
}
func (dst *BatchSetCampaignResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetCampaignResp.Merge(dst, src)
}
func (m *BatchSetCampaignResp) XXX_Size() int {
	return xxx_messageInfo_BatchSetCampaignResp.Size(m)
}
func (m *BatchSetCampaignResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetCampaignResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetCampaignResp proto.InternalMessageInfo

func (m *BatchSetCampaignResp) GetIds() []uint32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

// 查询广告投放数据
type SearchCampaignsReq struct {
	Offset            uint32                                      `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit             uint32                                      `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	BeginTime         int64                                       `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime           int64                                       `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Name              string                                      `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	AdId              uint32                                      `protobuf:"varint,6,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`
	UpdateTimeOrderBy int32                                       `protobuf:"varint,7,opt,name=update_time_order_by,json=updateTimeOrderBy,proto3" json:"update_time_order_by,omitempty"`
	BeginTimeOrderBy  int32                                       `protobuf:"varint,8,opt,name=begin_time_order_by,json=beginTimeOrderBy,proto3" json:"begin_time_order_by,omitempty"`
	PriorityOrderBy   int32                                       `protobuf:"varint,9,opt,name=priority_order_by,json=priorityOrderBy,proto3" json:"priority_order_by,omitempty"`
	StatusDesc        SearchCampaignsReq_CampaignFilterStatusDesc `protobuf:"varint,10,opt,name=status_desc,json=statusDesc,proto3,enum=ad_center.SearchCampaignsReq_CampaignFilterStatusDesc" json:"status_desc,omitempty"`
	// 房间类型 全部就是不传
	ChannelTypes []uint32 `protobuf:"varint,11,rep,packed,name=channel_types,json=channelTypes,proto3" json:"channel_types,omitempty"`
	// 主题类型 全部就是不传
	ChannelTabIds []uint32 `protobuf:"varint,12,rep,packed,name=channel_tab_ids,json=channelTabIds,proto3" json:"channel_tab_ids,omitempty"`
	// 类型
	PushMarketidList []uint32 `protobuf:"varint,13,rep,packed,name=push_marketid_list,json=pushMarketidList,proto3" json:"push_marketid_list,omitempty"`
	// 插入位置 大于1的进行筛选
	TablistInsertNs []uint32 `protobuf:"varint,14,rep,packed,name=tablist_insert_ns,json=tablistInsertNs,proto3" json:"tablist_insert_ns,omitempty"`
	// 专区id 版块管理
	AreaIds              []uint32 `protobuf:"varint,15,rep,packed,name=area_ids,json=areaIds,proto3" json:"area_ids,omitempty"`
	AreaType             uint32   `protobuf:"varint,16,opt,name=area_type,json=areaType,proto3" json:"area_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchCampaignsReq) Reset()         { *m = SearchCampaignsReq{} }
func (m *SearchCampaignsReq) String() string { return proto.CompactTextString(m) }
func (*SearchCampaignsReq) ProtoMessage()    {}
func (*SearchCampaignsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{25}
}
func (m *SearchCampaignsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchCampaignsReq.Unmarshal(m, b)
}
func (m *SearchCampaignsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchCampaignsReq.Marshal(b, m, deterministic)
}
func (dst *SearchCampaignsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchCampaignsReq.Merge(dst, src)
}
func (m *SearchCampaignsReq) XXX_Size() int {
	return xxx_messageInfo_SearchCampaignsReq.Size(m)
}
func (m *SearchCampaignsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchCampaignsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchCampaignsReq proto.InternalMessageInfo

func (m *SearchCampaignsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchCampaignsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchCampaignsReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *SearchCampaignsReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SearchCampaignsReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SearchCampaignsReq) GetAdId() uint32 {
	if m != nil {
		return m.AdId
	}
	return 0
}

func (m *SearchCampaignsReq) GetUpdateTimeOrderBy() int32 {
	if m != nil {
		return m.UpdateTimeOrderBy
	}
	return 0
}

func (m *SearchCampaignsReq) GetBeginTimeOrderBy() int32 {
	if m != nil {
		return m.BeginTimeOrderBy
	}
	return 0
}

func (m *SearchCampaignsReq) GetPriorityOrderBy() int32 {
	if m != nil {
		return m.PriorityOrderBy
	}
	return 0
}

func (m *SearchCampaignsReq) GetStatusDesc() SearchCampaignsReq_CampaignFilterStatusDesc {
	if m != nil {
		return m.StatusDesc
	}
	return SearchCampaignsReq_UNKNOW
}

func (m *SearchCampaignsReq) GetChannelTypes() []uint32 {
	if m != nil {
		return m.ChannelTypes
	}
	return nil
}

func (m *SearchCampaignsReq) GetChannelTabIds() []uint32 {
	if m != nil {
		return m.ChannelTabIds
	}
	return nil
}

func (m *SearchCampaignsReq) GetPushMarketidList() []uint32 {
	if m != nil {
		return m.PushMarketidList
	}
	return nil
}

func (m *SearchCampaignsReq) GetTablistInsertNs() []uint32 {
	if m != nil {
		return m.TablistInsertNs
	}
	return nil
}

func (m *SearchCampaignsReq) GetAreaIds() []uint32 {
	if m != nil {
		return m.AreaIds
	}
	return nil
}

func (m *SearchCampaignsReq) GetAreaType() uint32 {
	if m != nil {
		return m.AreaType
	}
	return 0
}

type SearchCampaignsResp struct {
	Campaigns            []*CampaignFilter `protobuf:"bytes,1,rep,name=campaigns,proto3" json:"campaigns,omitempty"`
	Count                uint32            `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SearchCampaignsResp) Reset()         { *m = SearchCampaignsResp{} }
func (m *SearchCampaignsResp) String() string { return proto.CompactTextString(m) }
func (*SearchCampaignsResp) ProtoMessage()    {}
func (*SearchCampaignsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{26}
}
func (m *SearchCampaignsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchCampaignsResp.Unmarshal(m, b)
}
func (m *SearchCampaignsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchCampaignsResp.Marshal(b, m, deterministic)
}
func (dst *SearchCampaignsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchCampaignsResp.Merge(dst, src)
}
func (m *SearchCampaignsResp) XXX_Size() int {
	return xxx_messageInfo_SearchCampaignsResp.Size(m)
}
func (m *SearchCampaignsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchCampaignsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchCampaignsResp proto.InternalMessageInfo

func (m *SearchCampaignsResp) GetCampaigns() []*CampaignFilter {
	if m != nil {
		return m.Campaigns
	}
	return nil
}

func (m *SearchCampaignsResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type CampaignFilter struct {
	Campaign             *Campaign                               `protobuf:"bytes,1,opt,name=campaign,proto3" json:"campaign,omitempty"`
	Filter               *Filter                                 `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	StatusDesc           CampaignFilter_CampaignFilterStatusDesc `protobuf:"varint,3,opt,name=status_desc,json=statusDesc,proto3,enum=ad_center.CampaignFilter_CampaignFilterStatusDesc" json:"status_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *CampaignFilter) Reset()         { *m = CampaignFilter{} }
func (m *CampaignFilter) String() string { return proto.CompactTextString(m) }
func (*CampaignFilter) ProtoMessage()    {}
func (*CampaignFilter) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{27}
}
func (m *CampaignFilter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CampaignFilter.Unmarshal(m, b)
}
func (m *CampaignFilter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CampaignFilter.Marshal(b, m, deterministic)
}
func (dst *CampaignFilter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CampaignFilter.Merge(dst, src)
}
func (m *CampaignFilter) XXX_Size() int {
	return xxx_messageInfo_CampaignFilter.Size(m)
}
func (m *CampaignFilter) XXX_DiscardUnknown() {
	xxx_messageInfo_CampaignFilter.DiscardUnknown(m)
}

var xxx_messageInfo_CampaignFilter proto.InternalMessageInfo

func (m *CampaignFilter) GetCampaign() *Campaign {
	if m != nil {
		return m.Campaign
	}
	return nil
}

func (m *CampaignFilter) GetFilter() *Filter {
	if m != nil {
		return m.Filter
	}
	return nil
}

func (m *CampaignFilter) GetStatusDesc() CampaignFilter_CampaignFilterStatusDesc {
	if m != nil {
		return m.StatusDesc
	}
	return CampaignFilter_UNKNOW
}

//
type Filter struct {
	Id          uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CampaignIds []uint32 `protobuf:"varint,2,rep,packed,name=campaign_ids,json=campaignIds,proto3" json:"campaign_ids,omitempty"`
	Name        string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	BeginTime   int64    `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime     int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	AdIdList    []uint32 `protobuf:"varint,6,rep,packed,name=AdIdList,proto3" json:"AdIdList,omitempty"`
	// 人群id ，-1 新用户，0 全部用户，正数 人群id
	TagId   int32  `protobuf:"varint,7,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName string `protobuf:"bytes,8,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	// 优先级
	Priority uint32 `protobuf:"varint,9,opt,name=priority,proto3" json:"priority,omitempty"`
	// 关联活动id
	WebId string `protobuf:"bytes,10,opt,name=web_id,json=webId,proto3" json:"web_id,omitempty"`
	// 设备过滤
	Platform uint32 `protobuf:"varint,11,opt,name=platform,proto3" json:"platform,omitempty"`
	// 房间类型过滤
	ChannelType          uint32                `protobuf:"varint,12,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	HourFrequency        uint32                `protobuf:"varint,13,opt,name=hour_frequency,json=hourFrequency,proto3" json:"hour_frequency,omitempty"`
	DayFrequency         uint32                `protobuf:"varint,14,opt,name=day_frequency,json=dayFrequency,proto3" json:"day_frequency,omitempty"`
	UpdateTime           int64                 `protobuf:"varint,15,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime           int64                 `protobuf:"varint,16,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Delete               bool                  `protobuf:"varint,17,opt,name=delete,proto3" json:"delete,omitempty"`
	NewUserByDay         uint32                `protobuf:"varint,18,opt,name=new_user_by_day,json=newUserByDay,proto3" json:"new_user_by_day,omitempty"`
	PushMarketidList     []uint32              `protobuf:"varint,19,rep,packed,name=push_marketid_list,json=pushMarketidList,proto3" json:"push_marketid_list,omitempty"`
	ChannelInfos         []*Filter_ChannelInfo `protobuf:"bytes,20,rep,name=channel_infos,json=channelInfos,proto3" json:"channel_infos,omitempty"`
	TagIdCount           int32                 `protobuf:"varint,21,opt,name=tag_id_count,json=tagIdCount,proto3" json:"tag_id_count,omitempty"`
	TablistInsertN       uint32                `protobuf:"varint,22,opt,name=tablist_insert_n,json=tablistInsertN,proto3" json:"tablist_insert_n,omitempty"`
	CampaignActivityType uint32                `protobuf:"varint,23,opt,name=campaign_activity_type,json=campaignActivityType,proto3" json:"campaign_activity_type,omitempty"`
	StatType             uint32                `protobuf:"varint,24,opt,name=stat_type,json=statType,proto3" json:"stat_type,omitempty"`
	AreaId               uint32                `protobuf:"varint,25,opt,name=area_id,json=areaId,proto3" json:"area_id,omitempty"`
	MysteryplaceId       uint32                `protobuf:"varint,26,opt,name=mysteryplace_id,json=mysteryplaceId,proto3" json:"mysteryplace_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *Filter) Reset()         { *m = Filter{} }
func (m *Filter) String() string { return proto.CompactTextString(m) }
func (*Filter) ProtoMessage()    {}
func (*Filter) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{28}
}
func (m *Filter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Filter.Unmarshal(m, b)
}
func (m *Filter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Filter.Marshal(b, m, deterministic)
}
func (dst *Filter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Filter.Merge(dst, src)
}
func (m *Filter) XXX_Size() int {
	return xxx_messageInfo_Filter.Size(m)
}
func (m *Filter) XXX_DiscardUnknown() {
	xxx_messageInfo_Filter.DiscardUnknown(m)
}

var xxx_messageInfo_Filter proto.InternalMessageInfo

func (m *Filter) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Filter) GetCampaignIds() []uint32 {
	if m != nil {
		return m.CampaignIds
	}
	return nil
}

func (m *Filter) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Filter) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *Filter) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *Filter) GetAdIdList() []uint32 {
	if m != nil {
		return m.AdIdList
	}
	return nil
}

func (m *Filter) GetTagId() int32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *Filter) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *Filter) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *Filter) GetWebId() string {
	if m != nil {
		return m.WebId
	}
	return ""
}

func (m *Filter) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *Filter) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *Filter) GetHourFrequency() uint32 {
	if m != nil {
		return m.HourFrequency
	}
	return 0
}

func (m *Filter) GetDayFrequency() uint32 {
	if m != nil {
		return m.DayFrequency
	}
	return 0
}

func (m *Filter) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *Filter) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *Filter) GetDelete() bool {
	if m != nil {
		return m.Delete
	}
	return false
}

func (m *Filter) GetNewUserByDay() uint32 {
	if m != nil {
		return m.NewUserByDay
	}
	return 0
}

func (m *Filter) GetPushMarketidList() []uint32 {
	if m != nil {
		return m.PushMarketidList
	}
	return nil
}

func (m *Filter) GetChannelInfos() []*Filter_ChannelInfo {
	if m != nil {
		return m.ChannelInfos
	}
	return nil
}

func (m *Filter) GetTagIdCount() int32 {
	if m != nil {
		return m.TagIdCount
	}
	return 0
}

func (m *Filter) GetTablistInsertN() uint32 {
	if m != nil {
		return m.TablistInsertN
	}
	return 0
}

func (m *Filter) GetCampaignActivityType() uint32 {
	if m != nil {
		return m.CampaignActivityType
	}
	return 0
}

func (m *Filter) GetStatType() uint32 {
	if m != nil {
		return m.StatType
	}
	return 0
}

func (m *Filter) GetAreaId() uint32 {
	if m != nil {
		return m.AreaId
	}
	return 0
}

func (m *Filter) GetMysteryplaceId() uint32 {
	if m != nil {
		return m.MysteryplaceId
	}
	return 0
}

// 房间主题  会有房间主题 房间主题id
type Filter_ChannelInfo struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	MicMode              uint32   `protobuf:"varint,3,opt,name=mic_mode,json=micMode,proto3" json:"mic_mode,omitempty"`
	ChannelType          uint32   `protobuf:"varint,5,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	CategoryId           uint32   `protobuf:"varint,6,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	MicCount             []uint32 `protobuf:"varint,7,rep,packed,name=mic_count,json=micCount,proto3" json:"mic_count,omitempty"`
	TagId                uint32   `protobuf:"varint,8,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName              string   `protobuf:"bytes,9,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Filter_ChannelInfo) Reset()         { *m = Filter_ChannelInfo{} }
func (m *Filter_ChannelInfo) String() string { return proto.CompactTextString(m) }
func (*Filter_ChannelInfo) ProtoMessage()    {}
func (*Filter_ChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{28, 0}
}
func (m *Filter_ChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Filter_ChannelInfo.Unmarshal(m, b)
}
func (m *Filter_ChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Filter_ChannelInfo.Marshal(b, m, deterministic)
}
func (dst *Filter_ChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Filter_ChannelInfo.Merge(dst, src)
}
func (m *Filter_ChannelInfo) XXX_Size() int {
	return xxx_messageInfo_Filter_ChannelInfo.Size(m)
}
func (m *Filter_ChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_Filter_ChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_Filter_ChannelInfo proto.InternalMessageInfo

func (m *Filter_ChannelInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *Filter_ChannelInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *Filter_ChannelInfo) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

func (m *Filter_ChannelInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *Filter_ChannelInfo) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *Filter_ChannelInfo) GetMicCount() []uint32 {
	if m != nil {
		return m.MicCount
	}
	return nil
}

func (m *Filter_ChannelInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *Filter_ChannelInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

type TGroupInfo struct {
	TgroupMemCnt         uint32   `protobuf:"varint,1,opt,name=tgroup_mem_cnt,json=tgroupMemCnt,proto3" json:"tgroup_mem_cnt,omitempty"`
	TgroupId             uint32   `protobuf:"varint,2,opt,name=tgroup_id,json=tgroupId,proto3" json:"tgroup_id,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TGroupInfo) Reset()         { *m = TGroupInfo{} }
func (m *TGroupInfo) String() string { return proto.CompactTextString(m) }
func (*TGroupInfo) ProtoMessage()    {}
func (*TGroupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{29}
}
func (m *TGroupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TGroupInfo.Unmarshal(m, b)
}
func (m *TGroupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TGroupInfo.Marshal(b, m, deterministic)
}
func (dst *TGroupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TGroupInfo.Merge(dst, src)
}
func (m *TGroupInfo) XXX_Size() int {
	return xxx_messageInfo_TGroupInfo.Size(m)
}
func (m *TGroupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TGroupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TGroupInfo proto.InternalMessageInfo

func (m *TGroupInfo) GetTgroupMemCnt() uint32 {
	if m != nil {
		return m.TgroupMemCnt
	}
	return 0
}

func (m *TGroupInfo) GetTgroupId() uint32 {
	if m != nil {
		return m.TgroupId
	}
	return 0
}

func (m *TGroupInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 获取广告过滤数据
type GetFilterReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFilterReq) Reset()         { *m = GetFilterReq{} }
func (m *GetFilterReq) String() string { return proto.CompactTextString(m) }
func (*GetFilterReq) ProtoMessage()    {}
func (*GetFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{30}
}
func (m *GetFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterReq.Unmarshal(m, b)
}
func (m *GetFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterReq.Marshal(b, m, deterministic)
}
func (dst *GetFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterReq.Merge(dst, src)
}
func (m *GetFilterReq) XXX_Size() int {
	return xxx_messageInfo_GetFilterReq.Size(m)
}
func (m *GetFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterReq proto.InternalMessageInfo

func (m *GetFilterReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetFilterResp struct {
	Filter               *Filter  `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFilterResp) Reset()         { *m = GetFilterResp{} }
func (m *GetFilterResp) String() string { return proto.CompactTextString(m) }
func (*GetFilterResp) ProtoMessage()    {}
func (*GetFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{31}
}
func (m *GetFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterResp.Unmarshal(m, b)
}
func (m *GetFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterResp.Marshal(b, m, deterministic)
}
func (dst *GetFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterResp.Merge(dst, src)
}
func (m *GetFilterResp) XXX_Size() int {
	return xxx_messageInfo_GetFilterResp.Size(m)
}
func (m *GetFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterResp proto.InternalMessageInfo

func (m *GetFilterResp) GetFilter() *Filter {
	if m != nil {
		return m.Filter
	}
	return nil
}

// 设置广告过滤数据
type SetFilterReq struct {
	Filter               *Filter  `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetFilterReq) Reset()         { *m = SetFilterReq{} }
func (m *SetFilterReq) String() string { return proto.CompactTextString(m) }
func (*SetFilterReq) ProtoMessage()    {}
func (*SetFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{32}
}
func (m *SetFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetFilterReq.Unmarshal(m, b)
}
func (m *SetFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetFilterReq.Marshal(b, m, deterministic)
}
func (dst *SetFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetFilterReq.Merge(dst, src)
}
func (m *SetFilterReq) XXX_Size() int {
	return xxx_messageInfo_SetFilterReq.Size(m)
}
func (m *SetFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetFilterReq proto.InternalMessageInfo

func (m *SetFilterReq) GetFilter() *Filter {
	if m != nil {
		return m.Filter
	}
	return nil
}

type SetFilterResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetFilterResp) Reset()         { *m = SetFilterResp{} }
func (m *SetFilterResp) String() string { return proto.CompactTextString(m) }
func (*SetFilterResp) ProtoMessage()    {}
func (*SetFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{33}
}
func (m *SetFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetFilterResp.Unmarshal(m, b)
}
func (m *SetFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetFilterResp.Marshal(b, m, deterministic)
}
func (dst *SetFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetFilterResp.Merge(dst, src)
}
func (m *SetFilterResp) XXX_Size() int {
	return xxx_messageInfo_SetFilterResp.Size(m)
}
func (m *SetFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetFilterResp proto.InternalMessageInfo

func (m *SetFilterResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 查询广告过滤数据
type SearchFiltersReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	BeginTime            int64    `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Name                 string   `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchFiltersReq) Reset()         { *m = SearchFiltersReq{} }
func (m *SearchFiltersReq) String() string { return proto.CompactTextString(m) }
func (*SearchFiltersReq) ProtoMessage()    {}
func (*SearchFiltersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{34}
}
func (m *SearchFiltersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchFiltersReq.Unmarshal(m, b)
}
func (m *SearchFiltersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchFiltersReq.Marshal(b, m, deterministic)
}
func (dst *SearchFiltersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchFiltersReq.Merge(dst, src)
}
func (m *SearchFiltersReq) XXX_Size() int {
	return xxx_messageInfo_SearchFiltersReq.Size(m)
}
func (m *SearchFiltersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchFiltersReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchFiltersReq proto.InternalMessageInfo

func (m *SearchFiltersReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchFiltersReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchFiltersReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *SearchFiltersReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SearchFiltersReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type SearchFiltersResp struct {
	Filters              []*Filter `protobuf:"bytes,1,rep,name=filters,proto3" json:"filters,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SearchFiltersResp) Reset()         { *m = SearchFiltersResp{} }
func (m *SearchFiltersResp) String() string { return proto.CompactTextString(m) }
func (*SearchFiltersResp) ProtoMessage()    {}
func (*SearchFiltersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{35}
}
func (m *SearchFiltersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchFiltersResp.Unmarshal(m, b)
}
func (m *SearchFiltersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchFiltersResp.Marshal(b, m, deterministic)
}
func (dst *SearchFiltersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchFiltersResp.Merge(dst, src)
}
func (m *SearchFiltersResp) XXX_Size() int {
	return xxx_messageInfo_SearchFiltersResp.Size(m)
}
func (m *SearchFiltersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchFiltersResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchFiltersResp proto.InternalMessageInfo

func (m *SearchFiltersResp) GetFilters() []*Filter {
	if m != nil {
		return m.Filters
	}
	return nil
}

// 获取策略数据
type GetPolicyReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPolicyReq) Reset()         { *m = GetPolicyReq{} }
func (m *GetPolicyReq) String() string { return proto.CompactTextString(m) }
func (*GetPolicyReq) ProtoMessage()    {}
func (*GetPolicyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{36}
}
func (m *GetPolicyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPolicyReq.Unmarshal(m, b)
}
func (m *GetPolicyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPolicyReq.Marshal(b, m, deterministic)
}
func (dst *GetPolicyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPolicyReq.Merge(dst, src)
}
func (m *GetPolicyReq) XXX_Size() int {
	return xxx_messageInfo_GetPolicyReq.Size(m)
}
func (m *GetPolicyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPolicyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPolicyReq proto.InternalMessageInfo

func (m *GetPolicyReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetPolicyResp struct {
	Policy               *Policy  `protobuf:"bytes,1,opt,name=policy,proto3" json:"policy,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPolicyResp) Reset()         { *m = GetPolicyResp{} }
func (m *GetPolicyResp) String() string { return proto.CompactTextString(m) }
func (*GetPolicyResp) ProtoMessage()    {}
func (*GetPolicyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{37}
}
func (m *GetPolicyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPolicyResp.Unmarshal(m, b)
}
func (m *GetPolicyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPolicyResp.Marshal(b, m, deterministic)
}
func (dst *GetPolicyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPolicyResp.Merge(dst, src)
}
func (m *GetPolicyResp) XXX_Size() int {
	return xxx_messageInfo_GetPolicyResp.Size(m)
}
func (m *GetPolicyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPolicyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPolicyResp proto.InternalMessageInfo

func (m *GetPolicyResp) GetPolicy() *Policy {
	if m != nil {
		return m.Policy
	}
	return nil
}

// 设置策略数据
type SetPolicyReq struct {
	Policy               *Policy  `protobuf:"bytes,1,opt,name=policy,proto3" json:"policy,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPolicyReq) Reset()         { *m = SetPolicyReq{} }
func (m *SetPolicyReq) String() string { return proto.CompactTextString(m) }
func (*SetPolicyReq) ProtoMessage()    {}
func (*SetPolicyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{38}
}
func (m *SetPolicyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPolicyReq.Unmarshal(m, b)
}
func (m *SetPolicyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPolicyReq.Marshal(b, m, deterministic)
}
func (dst *SetPolicyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPolicyReq.Merge(dst, src)
}
func (m *SetPolicyReq) XXX_Size() int {
	return xxx_messageInfo_SetPolicyReq.Size(m)
}
func (m *SetPolicyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPolicyReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPolicyReq proto.InternalMessageInfo

func (m *SetPolicyReq) GetPolicy() *Policy {
	if m != nil {
		return m.Policy
	}
	return nil
}

type SetPolicyResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPolicyResp) Reset()         { *m = SetPolicyResp{} }
func (m *SetPolicyResp) String() string { return proto.CompactTextString(m) }
func (*SetPolicyResp) ProtoMessage()    {}
func (*SetPolicyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{39}
}
func (m *SetPolicyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPolicyResp.Unmarshal(m, b)
}
func (m *SetPolicyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPolicyResp.Marshal(b, m, deterministic)
}
func (dst *SetPolicyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPolicyResp.Merge(dst, src)
}
func (m *SetPolicyResp) XXX_Size() int {
	return xxx_messageInfo_SetPolicyResp.Size(m)
}
func (m *SetPolicyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPolicyResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPolicyResp proto.InternalMessageInfo

func (m *SetPolicyResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 查询策略数据
type SearchPoliciesReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Id                   uint32   `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
	TypeJumpUrl          int32    `protobuf:"varint,5,opt,name=type_jump_url,json=typeJumpUrl,proto3" json:"type_jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchPoliciesReq) Reset()         { *m = SearchPoliciesReq{} }
func (m *SearchPoliciesReq) String() string { return proto.CompactTextString(m) }
func (*SearchPoliciesReq) ProtoMessage()    {}
func (*SearchPoliciesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{40}
}
func (m *SearchPoliciesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchPoliciesReq.Unmarshal(m, b)
}
func (m *SearchPoliciesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchPoliciesReq.Marshal(b, m, deterministic)
}
func (dst *SearchPoliciesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchPoliciesReq.Merge(dst, src)
}
func (m *SearchPoliciesReq) XXX_Size() int {
	return xxx_messageInfo_SearchPoliciesReq.Size(m)
}
func (m *SearchPoliciesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchPoliciesReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchPoliciesReq proto.InternalMessageInfo

func (m *SearchPoliciesReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchPoliciesReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchPoliciesReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SearchPoliciesReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SearchPoliciesReq) GetTypeJumpUrl() int32 {
	if m != nil {
		return m.TypeJumpUrl
	}
	return 0
}

type SearchPoliciesResp struct {
	Policies             []*Policy `protobuf:"bytes,1,rep,name=policies,proto3" json:"policies,omitempty"`
	Count                uint32    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SearchPoliciesResp) Reset()         { *m = SearchPoliciesResp{} }
func (m *SearchPoliciesResp) String() string { return proto.CompactTextString(m) }
func (*SearchPoliciesResp) ProtoMessage()    {}
func (*SearchPoliciesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{41}
}
func (m *SearchPoliciesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchPoliciesResp.Unmarshal(m, b)
}
func (m *SearchPoliciesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchPoliciesResp.Marshal(b, m, deterministic)
}
func (dst *SearchPoliciesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchPoliciesResp.Merge(dst, src)
}
func (m *SearchPoliciesResp) XXX_Size() int {
	return xxx_messageInfo_SearchPoliciesResp.Size(m)
}
func (m *SearchPoliciesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchPoliciesResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchPoliciesResp proto.InternalMessageInfo

func (m *SearchPoliciesResp) GetPolicies() []*Policy {
	if m != nil {
		return m.Policies
	}
	return nil
}

func (m *SearchPoliciesResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type Policy struct {
	Id              uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name            string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type            uint32 `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Delete          bool   `protobuf:"varint,4,opt,name=delete,proto3" json:"delete,omitempty"`
	AbTestId        uint32 `protobuf:"varint,5,opt,name=ab_test_id,json=abTestId,proto3" json:"ab_test_id,omitempty"`
	UpdateTime      int64  `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime      int64  `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Labs            []*Lab `protobuf:"bytes,8,rep,name=labs,proto3" json:"labs,omitempty"`
	NormalPolicyUrl string `protobuf:"bytes,9,opt,name=normal_policy_url,json=normalPolicyUrl,proto3" json:"normal_policy_url,omitempty"`
	// 群组链接类型
	TypeJumpUrl uint32 `protobuf:"varint,10,opt,name=type_jump_url,json=typeJumpUrl,proto3" json:"type_jump_url,omitempty"`
	// 群组所依赖的主题
	TgroupInfos          []*TGroupInfo `protobuf:"bytes,11,rep,name=tgroup_infos,json=tgroupInfos,proto3" json:"tgroup_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *Policy) Reset()         { *m = Policy{} }
func (m *Policy) String() string { return proto.CompactTextString(m) }
func (*Policy) ProtoMessage()    {}
func (*Policy) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{42}
}
func (m *Policy) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Policy.Unmarshal(m, b)
}
func (m *Policy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Policy.Marshal(b, m, deterministic)
}
func (dst *Policy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Policy.Merge(dst, src)
}
func (m *Policy) XXX_Size() int {
	return xxx_messageInfo_Policy.Size(m)
}
func (m *Policy) XXX_DiscardUnknown() {
	xxx_messageInfo_Policy.DiscardUnknown(m)
}

var xxx_messageInfo_Policy proto.InternalMessageInfo

func (m *Policy) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Policy) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Policy) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *Policy) GetDelete() bool {
	if m != nil {
		return m.Delete
	}
	return false
}

func (m *Policy) GetAbTestId() uint32 {
	if m != nil {
		return m.AbTestId
	}
	return 0
}

func (m *Policy) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *Policy) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *Policy) GetLabs() []*Lab {
	if m != nil {
		return m.Labs
	}
	return nil
}

func (m *Policy) GetNormalPolicyUrl() string {
	if m != nil {
		return m.NormalPolicyUrl
	}
	return ""
}

func (m *Policy) GetTypeJumpUrl() uint32 {
	if m != nil {
		return m.TypeJumpUrl
	}
	return 0
}

func (m *Policy) GetTgroupInfos() []*TGroupInfo {
	if m != nil {
		return m.TgroupInfos
	}
	return nil
}

// 查询ab测试的实验信息
type GetABTestInfoReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetABTestInfoReq) Reset()         { *m = GetABTestInfoReq{} }
func (m *GetABTestInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetABTestInfoReq) ProtoMessage()    {}
func (*GetABTestInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{43}
}
func (m *GetABTestInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetABTestInfoReq.Unmarshal(m, b)
}
func (m *GetABTestInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetABTestInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetABTestInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetABTestInfoReq.Merge(dst, src)
}
func (m *GetABTestInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetABTestInfoReq.Size(m)
}
func (m *GetABTestInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetABTestInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetABTestInfoReq proto.InternalMessageInfo

func (m *GetABTestInfoReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetABTestInfoResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Labs                 []*Lab   `protobuf:"bytes,3,rep,name=labs,proto3" json:"labs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetABTestInfoResp) Reset()         { *m = GetABTestInfoResp{} }
func (m *GetABTestInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetABTestInfoResp) ProtoMessage()    {}
func (*GetABTestInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{44}
}
func (m *GetABTestInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetABTestInfoResp.Unmarshal(m, b)
}
func (m *GetABTestInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetABTestInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetABTestInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetABTestInfoResp.Merge(dst, src)
}
func (m *GetABTestInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetABTestInfoResp.Size(m)
}
func (m *GetABTestInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetABTestInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetABTestInfoResp proto.InternalMessageInfo

func (m *GetABTestInfoResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetABTestInfoResp) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetABTestInfoResp) GetLabs() []*Lab {
	if m != nil {
		return m.Labs
	}
	return nil
}

type Lab struct {
	Id                   uint32        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	AllocRatio           uint32        `protobuf:"varint,3,opt,name=AllocRatio,proto3" json:"AllocRatio,omitempty"`
	PolicyUrl            string        `protobuf:"bytes,4,opt,name=policy_url,json=policyUrl,proto3" json:"policy_url,omitempty"`
	TgroupInfos          []*TGroupInfo `protobuf:"bytes,5,rep,name=tgroup_infos,json=tgroupInfos,proto3" json:"tgroup_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *Lab) Reset()         { *m = Lab{} }
func (m *Lab) String() string { return proto.CompactTextString(m) }
func (*Lab) ProtoMessage()    {}
func (*Lab) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{45}
}
func (m *Lab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Lab.Unmarshal(m, b)
}
func (m *Lab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Lab.Marshal(b, m, deterministic)
}
func (dst *Lab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Lab.Merge(dst, src)
}
func (m *Lab) XXX_Size() int {
	return xxx_messageInfo_Lab.Size(m)
}
func (m *Lab) XXX_DiscardUnknown() {
	xxx_messageInfo_Lab.DiscardUnknown(m)
}

var xxx_messageInfo_Lab proto.InternalMessageInfo

func (m *Lab) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Lab) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Lab) GetAllocRatio() uint32 {
	if m != nil {
		return m.AllocRatio
	}
	return 0
}

func (m *Lab) GetPolicyUrl() string {
	if m != nil {
		return m.PolicyUrl
	}
	return ""
}

func (m *Lab) GetTgroupInfos() []*TGroupInfo {
	if m != nil {
		return m.TgroupInfos
	}
	return nil
}

type SetABTestInfoReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Labs                 []*Lab   `protobuf:"bytes,3,rep,name=labs,proto3" json:"labs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetABTestInfoReq) Reset()         { *m = SetABTestInfoReq{} }
func (m *SetABTestInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetABTestInfoReq) ProtoMessage()    {}
func (*SetABTestInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{46}
}
func (m *SetABTestInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetABTestInfoReq.Unmarshal(m, b)
}
func (m *SetABTestInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetABTestInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetABTestInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetABTestInfoReq.Merge(dst, src)
}
func (m *SetABTestInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetABTestInfoReq.Size(m)
}
func (m *SetABTestInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetABTestInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetABTestInfoReq proto.InternalMessageInfo

func (m *SetABTestInfoReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SetABTestInfoReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SetABTestInfoReq) GetLabs() []*Lab {
	if m != nil {
		return m.Labs
	}
	return nil
}

type SetABTestInfoResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetABTestInfoResp) Reset()         { *m = SetABTestInfoResp{} }
func (m *SetABTestInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetABTestInfoResp) ProtoMessage()    {}
func (*SetABTestInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{47}
}
func (m *SetABTestInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetABTestInfoResp.Unmarshal(m, b)
}
func (m *SetABTestInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetABTestInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetABTestInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetABTestInfoResp.Merge(dst, src)
}
func (m *SetABTestInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetABTestInfoResp.Size(m)
}
func (m *SetABTestInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetABTestInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetABTestInfoResp proto.InternalMessageInfo

func (m *SetABTestInfoResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GeneralMsgReq struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GeneralMsgReq) Reset()         { *m = GeneralMsgReq{} }
func (m *GeneralMsgReq) String() string { return proto.CompactTextString(m) }
func (*GeneralMsgReq) ProtoMessage()    {}
func (*GeneralMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{48}
}
func (m *GeneralMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeneralMsgReq.Unmarshal(m, b)
}
func (m *GeneralMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeneralMsgReq.Marshal(b, m, deterministic)
}
func (dst *GeneralMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeneralMsgReq.Merge(dst, src)
}
func (m *GeneralMsgReq) XXX_Size() int {
	return xxx_messageInfo_GeneralMsgReq.Size(m)
}
func (m *GeneralMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GeneralMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GeneralMsgReq proto.InternalMessageInfo

func (m *GeneralMsgReq) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type GeneralMsgResp struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GeneralMsgResp) Reset()         { *m = GeneralMsgResp{} }
func (m *GeneralMsgResp) String() string { return proto.CompactTextString(m) }
func (*GeneralMsgResp) ProtoMessage()    {}
func (*GeneralMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{49}
}
func (m *GeneralMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeneralMsgResp.Unmarshal(m, b)
}
func (m *GeneralMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeneralMsgResp.Marshal(b, m, deterministic)
}
func (dst *GeneralMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeneralMsgResp.Merge(dst, src)
}
func (m *GeneralMsgResp) XXX_Size() int {
	return xxx_messageInfo_GeneralMsgResp.Size(m)
}
func (m *GeneralMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GeneralMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GeneralMsgResp proto.InternalMessageInfo

func (m *GeneralMsgResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

// 重合度计算
type CoincidenceCountReq struct {
	AdId                 uint32   `protobuf:"varint,1,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`
	BeginTime            int64    `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ChannelType          uint32   `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	TagId                int32    `protobuf:"varint,5,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	FilterId             uint32   `protobuf:"varint,6,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CoincidenceCountReq) Reset()         { *m = CoincidenceCountReq{} }
func (m *CoincidenceCountReq) String() string { return proto.CompactTextString(m) }
func (*CoincidenceCountReq) ProtoMessage()    {}
func (*CoincidenceCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{50}
}
func (m *CoincidenceCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoincidenceCountReq.Unmarshal(m, b)
}
func (m *CoincidenceCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoincidenceCountReq.Marshal(b, m, deterministic)
}
func (dst *CoincidenceCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoincidenceCountReq.Merge(dst, src)
}
func (m *CoincidenceCountReq) XXX_Size() int {
	return xxx_messageInfo_CoincidenceCountReq.Size(m)
}
func (m *CoincidenceCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CoincidenceCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_CoincidenceCountReq proto.InternalMessageInfo

func (m *CoincidenceCountReq) GetAdId() uint32 {
	if m != nil {
		return m.AdId
	}
	return 0
}

func (m *CoincidenceCountReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *CoincidenceCountReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *CoincidenceCountReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *CoincidenceCountReq) GetTagId() int32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *CoincidenceCountReq) GetFilterId() uint32 {
	if m != nil {
		return m.FilterId
	}
	return 0
}

type CoincidenceCount struct {
	JumpURL              string   `protobuf:"bytes,1,opt,name=jumpURL,proto3" json:"jumpURL,omitempty"`
	ThanGroupId          string   `protobuf:"bytes,2,opt,name=thanGroupId,proto3" json:"thanGroupId,omitempty"`
	ThanGroupName        string   `protobuf:"bytes,3,opt,name=thanGroupName,proto3" json:"thanGroupName,omitempty"`
	Priority             uint32   `protobuf:"varint,4,opt,name=priority,proto3" json:"priority,omitempty"`
	Count                uint32   `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	PolicyName           string   `protobuf:"bytes,6,opt,name=policy_name,json=policyName,proto3" json:"policy_name,omitempty"`
	CampaignId           uint32   `protobuf:"varint,7,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CoincidenceCount) Reset()         { *m = CoincidenceCount{} }
func (m *CoincidenceCount) String() string { return proto.CompactTextString(m) }
func (*CoincidenceCount) ProtoMessage()    {}
func (*CoincidenceCount) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{51}
}
func (m *CoincidenceCount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoincidenceCount.Unmarshal(m, b)
}
func (m *CoincidenceCount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoincidenceCount.Marshal(b, m, deterministic)
}
func (dst *CoincidenceCount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoincidenceCount.Merge(dst, src)
}
func (m *CoincidenceCount) XXX_Size() int {
	return xxx_messageInfo_CoincidenceCount.Size(m)
}
func (m *CoincidenceCount) XXX_DiscardUnknown() {
	xxx_messageInfo_CoincidenceCount.DiscardUnknown(m)
}

var xxx_messageInfo_CoincidenceCount proto.InternalMessageInfo

func (m *CoincidenceCount) GetJumpURL() string {
	if m != nil {
		return m.JumpURL
	}
	return ""
}

func (m *CoincidenceCount) GetThanGroupId() string {
	if m != nil {
		return m.ThanGroupId
	}
	return ""
}

func (m *CoincidenceCount) GetThanGroupName() string {
	if m != nil {
		return m.ThanGroupName
	}
	return ""
}

func (m *CoincidenceCount) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *CoincidenceCount) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *CoincidenceCount) GetPolicyName() string {
	if m != nil {
		return m.PolicyName
	}
	return ""
}

func (m *CoincidenceCount) GetCampaignId() uint32 {
	if m != nil {
		return m.CampaignId
	}
	return 0
}

type CoincidenceCountResp struct {
	ToastList            []string            `protobuf:"bytes,1,rep,name=toast_list,json=toastList,proto3" json:"toast_list,omitempty"`
	CoincidenceList      []*CoincidenceCount `protobuf:"bytes,2,rep,name=coincidence_list,json=coincidenceList,proto3" json:"coincidence_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *CoincidenceCountResp) Reset()         { *m = CoincidenceCountResp{} }
func (m *CoincidenceCountResp) String() string { return proto.CompactTextString(m) }
func (*CoincidenceCountResp) ProtoMessage()    {}
func (*CoincidenceCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{52}
}
func (m *CoincidenceCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoincidenceCountResp.Unmarshal(m, b)
}
func (m *CoincidenceCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoincidenceCountResp.Marshal(b, m, deterministic)
}
func (dst *CoincidenceCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoincidenceCountResp.Merge(dst, src)
}
func (m *CoincidenceCountResp) XXX_Size() int {
	return xxx_messageInfo_CoincidenceCountResp.Size(m)
}
func (m *CoincidenceCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CoincidenceCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_CoincidenceCountResp proto.InternalMessageInfo

func (m *CoincidenceCountResp) GetToastList() []string {
	if m != nil {
		return m.ToastList
	}
	return nil
}

func (m *CoincidenceCountResp) GetCoincidenceList() []*CoincidenceCount {
	if m != nil {
		return m.CoincidenceList
	}
	return nil
}

type CheckTagIdMateReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TagId                uint32   `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckTagIdMateReq) Reset()         { *m = CheckTagIdMateReq{} }
func (m *CheckTagIdMateReq) String() string { return proto.CompactTextString(m) }
func (*CheckTagIdMateReq) ProtoMessage()    {}
func (*CheckTagIdMateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{53}
}
func (m *CheckTagIdMateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTagIdMateReq.Unmarshal(m, b)
}
func (m *CheckTagIdMateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTagIdMateReq.Marshal(b, m, deterministic)
}
func (dst *CheckTagIdMateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTagIdMateReq.Merge(dst, src)
}
func (m *CheckTagIdMateReq) XXX_Size() int {
	return xxx_messageInfo_CheckTagIdMateReq.Size(m)
}
func (m *CheckTagIdMateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTagIdMateReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTagIdMateReq proto.InternalMessageInfo

func (m *CheckTagIdMateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckTagIdMateReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type CheckTagIdMateResp struct {
	Flag                 bool     `protobuf:"varint,1,opt,name=flag,proto3" json:"flag,omitempty"`
	TagId                uint32   `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckTagIdMateResp) Reset()         { *m = CheckTagIdMateResp{} }
func (m *CheckTagIdMateResp) String() string { return proto.CompactTextString(m) }
func (*CheckTagIdMateResp) ProtoMessage()    {}
func (*CheckTagIdMateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{54}
}
func (m *CheckTagIdMateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTagIdMateResp.Unmarshal(m, b)
}
func (m *CheckTagIdMateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTagIdMateResp.Marshal(b, m, deterministic)
}
func (dst *CheckTagIdMateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTagIdMateResp.Merge(dst, src)
}
func (m *CheckTagIdMateResp) XXX_Size() int {
	return xxx_messageInfo_CheckTagIdMateResp.Size(m)
}
func (m *CheckTagIdMateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTagIdMateResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTagIdMateResp proto.InternalMessageInfo

func (m *CheckTagIdMateResp) GetFlag() bool {
	if m != nil {
		return m.Flag
	}
	return false
}

func (m *CheckTagIdMateResp) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type DiagnosisCampaignReq struct {
	CampaignId           uint32   `protobuf:"varint,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	Minute               uint32   `protobuf:"varint,2,opt,name=minute,proto3" json:"minute,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiagnosisCampaignReq) Reset()         { *m = DiagnosisCampaignReq{} }
func (m *DiagnosisCampaignReq) String() string { return proto.CompactTextString(m) }
func (*DiagnosisCampaignReq) ProtoMessage()    {}
func (*DiagnosisCampaignReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{55}
}
func (m *DiagnosisCampaignReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiagnosisCampaignReq.Unmarshal(m, b)
}
func (m *DiagnosisCampaignReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiagnosisCampaignReq.Marshal(b, m, deterministic)
}
func (dst *DiagnosisCampaignReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiagnosisCampaignReq.Merge(dst, src)
}
func (m *DiagnosisCampaignReq) XXX_Size() int {
	return xxx_messageInfo_DiagnosisCampaignReq.Size(m)
}
func (m *DiagnosisCampaignReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiagnosisCampaignReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiagnosisCampaignReq proto.InternalMessageInfo

func (m *DiagnosisCampaignReq) GetCampaignId() uint32 {
	if m != nil {
		return m.CampaignId
	}
	return 0
}

func (m *DiagnosisCampaignReq) GetMinute() uint32 {
	if m != nil {
		return m.Minute
	}
	return 0
}

type DiagnosisCampaignResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DiagnosisCampaignResp) Reset()         { *m = DiagnosisCampaignResp{} }
func (m *DiagnosisCampaignResp) String() string { return proto.CompactTextString(m) }
func (*DiagnosisCampaignResp) ProtoMessage()    {}
func (*DiagnosisCampaignResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{56}
}
func (m *DiagnosisCampaignResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiagnosisCampaignResp.Unmarshal(m, b)
}
func (m *DiagnosisCampaignResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiagnosisCampaignResp.Marshal(b, m, deterministic)
}
func (dst *DiagnosisCampaignResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiagnosisCampaignResp.Merge(dst, src)
}
func (m *DiagnosisCampaignResp) XXX_Size() int {
	return xxx_messageInfo_DiagnosisCampaignResp.Size(m)
}
func (m *DiagnosisCampaignResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DiagnosisCampaignResp.DiscardUnknown(m)
}

var xxx_messageInfo_DiagnosisCampaignResp proto.InternalMessageInfo

type CommitAdExposureReq struct {
	Uid           uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AdId          uint32 `protobuf:"varint,2,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`
	ChannelId     uint32 `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AppId         uint32 `protobuf:"varint,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId      uint32 `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientIp      uint32 `protobuf:"varint,6,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	ClientType    uint32 `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion uint32 `protobuf:"varint,8,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	// 平台
	Platform             uint32   `protobuf:"varint,9,opt,name=platform,proto3" json:"platform,omitempty"`
	Os                   uint32   `protobuf:"varint,10,opt,name=os,proto3" json:"os,omitempty"`
	ChannelType          uint32   `protobuf:"varint,11,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	CampaignId           uint32   `protobuf:"varint,12,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	MaterialId           uint32   `protobuf:"varint,13,opt,name=material_id,json=materialId,proto3" json:"material_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommitAdExposureReq) Reset()         { *m = CommitAdExposureReq{} }
func (m *CommitAdExposureReq) String() string { return proto.CompactTextString(m) }
func (*CommitAdExposureReq) ProtoMessage()    {}
func (*CommitAdExposureReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{57}
}
func (m *CommitAdExposureReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommitAdExposureReq.Unmarshal(m, b)
}
func (m *CommitAdExposureReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommitAdExposureReq.Marshal(b, m, deterministic)
}
func (dst *CommitAdExposureReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommitAdExposureReq.Merge(dst, src)
}
func (m *CommitAdExposureReq) XXX_Size() int {
	return xxx_messageInfo_CommitAdExposureReq.Size(m)
}
func (m *CommitAdExposureReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommitAdExposureReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommitAdExposureReq proto.InternalMessageInfo

func (m *CommitAdExposureReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CommitAdExposureReq) GetAdId() uint32 {
	if m != nil {
		return m.AdId
	}
	return 0
}

func (m *CommitAdExposureReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CommitAdExposureReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *CommitAdExposureReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *CommitAdExposureReq) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

func (m *CommitAdExposureReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *CommitAdExposureReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *CommitAdExposureReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *CommitAdExposureReq) GetOs() uint32 {
	if m != nil {
		return m.Os
	}
	return 0
}

func (m *CommitAdExposureReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *CommitAdExposureReq) GetCampaignId() uint32 {
	if m != nil {
		return m.CampaignId
	}
	return 0
}

func (m *CommitAdExposureReq) GetMaterialId() uint32 {
	if m != nil {
		return m.MaterialId
	}
	return 0
}

type CommitAdExposureResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommitAdExposureResp) Reset()         { *m = CommitAdExposureResp{} }
func (m *CommitAdExposureResp) String() string { return proto.CompactTextString(m) }
func (*CommitAdExposureResp) ProtoMessage()    {}
func (*CommitAdExposureResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{58}
}
func (m *CommitAdExposureResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommitAdExposureResp.Unmarshal(m, b)
}
func (m *CommitAdExposureResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommitAdExposureResp.Marshal(b, m, deterministic)
}
func (dst *CommitAdExposureResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommitAdExposureResp.Merge(dst, src)
}
func (m *CommitAdExposureResp) XXX_Size() int {
	return xxx_messageInfo_CommitAdExposureResp.Size(m)
}
func (m *CommitAdExposureResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CommitAdExposureResp.DiscardUnknown(m)
}

var xxx_messageInfo_CommitAdExposureResp proto.InternalMessageInfo

type CommitAdClickReq struct {
	Uid           uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AdId          uint32 `protobuf:"varint,2,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`
	ChannelId     uint32 `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AppId         uint32 `protobuf:"varint,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId      uint32 `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientIp      uint32 `protobuf:"varint,6,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	ClientType    uint32 `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion uint32 `protobuf:"varint,8,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	// 平台
	Platform             uint32   `protobuf:"varint,9,opt,name=platform,proto3" json:"platform,omitempty"`
	Os                   uint32   `protobuf:"varint,10,opt,name=os,proto3" json:"os,omitempty"`
	ChannelType          uint32   `protobuf:"varint,11,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	CampaignId           uint32   `protobuf:"varint,12,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	MaterialId           uint32   `protobuf:"varint,13,opt,name=material_id,json=materialId,proto3" json:"material_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommitAdClickReq) Reset()         { *m = CommitAdClickReq{} }
func (m *CommitAdClickReq) String() string { return proto.CompactTextString(m) }
func (*CommitAdClickReq) ProtoMessage()    {}
func (*CommitAdClickReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{59}
}
func (m *CommitAdClickReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommitAdClickReq.Unmarshal(m, b)
}
func (m *CommitAdClickReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommitAdClickReq.Marshal(b, m, deterministic)
}
func (dst *CommitAdClickReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommitAdClickReq.Merge(dst, src)
}
func (m *CommitAdClickReq) XXX_Size() int {
	return xxx_messageInfo_CommitAdClickReq.Size(m)
}
func (m *CommitAdClickReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommitAdClickReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommitAdClickReq proto.InternalMessageInfo

func (m *CommitAdClickReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CommitAdClickReq) GetAdId() uint32 {
	if m != nil {
		return m.AdId
	}
	return 0
}

func (m *CommitAdClickReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CommitAdClickReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *CommitAdClickReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *CommitAdClickReq) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

func (m *CommitAdClickReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *CommitAdClickReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *CommitAdClickReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *CommitAdClickReq) GetOs() uint32 {
	if m != nil {
		return m.Os
	}
	return 0
}

func (m *CommitAdClickReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *CommitAdClickReq) GetCampaignId() uint32 {
	if m != nil {
		return m.CampaignId
	}
	return 0
}

func (m *CommitAdClickReq) GetMaterialId() uint32 {
	if m != nil {
		return m.MaterialId
	}
	return 0
}

type CommitAdClickResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommitAdClickResp) Reset()         { *m = CommitAdClickResp{} }
func (m *CommitAdClickResp) String() string { return proto.CompactTextString(m) }
func (*CommitAdClickResp) ProtoMessage()    {}
func (*CommitAdClickResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{60}
}
func (m *CommitAdClickResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommitAdClickResp.Unmarshal(m, b)
}
func (m *CommitAdClickResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommitAdClickResp.Marshal(b, m, deterministic)
}
func (dst *CommitAdClickResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommitAdClickResp.Merge(dst, src)
}
func (m *CommitAdClickResp) XXX_Size() int {
	return xxx_messageInfo_CommitAdClickResp.Size(m)
}
func (m *CommitAdClickResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CommitAdClickResp.DiscardUnknown(m)
}

var xxx_messageInfo_CommitAdClickResp proto.InternalMessageInfo

type QueryDataReq struct {
	AdId                 uint32   `protobuf:"varint,1,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`
	CampaignId           uint32   `protobuf:"varint,2,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	FromDate             int64    `protobuf:"varint,3,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	ToDate               int64    `protobuf:"varint,4,opt,name=to_date,json=toDate,proto3" json:"to_date,omitempty"`
	Unit                 string   `protobuf:"bytes,5,opt,name=unit,proto3" json:"unit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryDataReq) Reset()         { *m = QueryDataReq{} }
func (m *QueryDataReq) String() string { return proto.CompactTextString(m) }
func (*QueryDataReq) ProtoMessage()    {}
func (*QueryDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{61}
}
func (m *QueryDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryDataReq.Unmarshal(m, b)
}
func (m *QueryDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryDataReq.Marshal(b, m, deterministic)
}
func (dst *QueryDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryDataReq.Merge(dst, src)
}
func (m *QueryDataReq) XXX_Size() int {
	return xxx_messageInfo_QueryDataReq.Size(m)
}
func (m *QueryDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryDataReq proto.InternalMessageInfo

func (m *QueryDataReq) GetAdId() uint32 {
	if m != nil {
		return m.AdId
	}
	return 0
}

func (m *QueryDataReq) GetCampaignId() uint32 {
	if m != nil {
		return m.CampaignId
	}
	return 0
}

func (m *QueryDataReq) GetFromDate() int64 {
	if m != nil {
		return m.FromDate
	}
	return 0
}

func (m *QueryDataReq) GetToDate() int64 {
	if m != nil {
		return m.ToDate
	}
	return 0
}

func (m *QueryDataReq) GetUnit() string {
	if m != nil {
		return m.Unit
	}
	return ""
}

type QueryDataResp struct {
	Quotas               []string                `protobuf:"bytes,1,rep,name=quotas,proto3" json:"quotas,omitempty"`
	Metrics              []*QueryDataResp_Metric `protobuf:"bytes,2,rep,name=metrics,proto3" json:"metrics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *QueryDataResp) Reset()         { *m = QueryDataResp{} }
func (m *QueryDataResp) String() string { return proto.CompactTextString(m) }
func (*QueryDataResp) ProtoMessage()    {}
func (*QueryDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{62}
}
func (m *QueryDataResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryDataResp.Unmarshal(m, b)
}
func (m *QueryDataResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryDataResp.Marshal(b, m, deterministic)
}
func (dst *QueryDataResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryDataResp.Merge(dst, src)
}
func (m *QueryDataResp) XXX_Size() int {
	return xxx_messageInfo_QueryDataResp.Size(m)
}
func (m *QueryDataResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryDataResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryDataResp proto.InternalMessageInfo

func (m *QueryDataResp) GetQuotas() []string {
	if m != nil {
		return m.Quotas
	}
	return nil
}

func (m *QueryDataResp) GetMetrics() []*QueryDataResp_Metric {
	if m != nil {
		return m.Metrics
	}
	return nil
}

type QueryDataResp_Metric struct {
	Metric               []string `protobuf:"bytes,1,rep,name=metric,proto3" json:"metric,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryDataResp_Metric) Reset()         { *m = QueryDataResp_Metric{} }
func (m *QueryDataResp_Metric) String() string { return proto.CompactTextString(m) }
func (*QueryDataResp_Metric) ProtoMessage()    {}
func (*QueryDataResp_Metric) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{62, 0}
}
func (m *QueryDataResp_Metric) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryDataResp_Metric.Unmarshal(m, b)
}
func (m *QueryDataResp_Metric) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryDataResp_Metric.Marshal(b, m, deterministic)
}
func (dst *QueryDataResp_Metric) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryDataResp_Metric.Merge(dst, src)
}
func (m *QueryDataResp_Metric) XXX_Size() int {
	return xxx_messageInfo_QueryDataResp_Metric.Size(m)
}
func (m *QueryDataResp_Metric) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryDataResp_Metric.DiscardUnknown(m)
}

var xxx_messageInfo_QueryDataResp_Metric proto.InternalMessageInfo

func (m *QueryDataResp_Metric) GetMetric() []string {
	if m != nil {
		return m.Metric
	}
	return nil
}

type CheckABTestReq struct {
	Tag                  string   `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckABTestReq) Reset()         { *m = CheckABTestReq{} }
func (m *CheckABTestReq) String() string { return proto.CompactTextString(m) }
func (*CheckABTestReq) ProtoMessage()    {}
func (*CheckABTestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{63}
}
func (m *CheckABTestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckABTestReq.Unmarshal(m, b)
}
func (m *CheckABTestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckABTestReq.Marshal(b, m, deterministic)
}
func (dst *CheckABTestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckABTestReq.Merge(dst, src)
}
func (m *CheckABTestReq) XXX_Size() int {
	return xxx_messageInfo_CheckABTestReq.Size(m)
}
func (m *CheckABTestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckABTestReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckABTestReq proto.InternalMessageInfo

func (m *CheckABTestReq) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *CheckABTestReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckABTestResp struct {
	LabId                int64    `protobuf:"varint,2,opt,name=lab_id,json=labId,proto3" json:"lab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckABTestResp) Reset()         { *m = CheckABTestResp{} }
func (m *CheckABTestResp) String() string { return proto.CompactTextString(m) }
func (*CheckABTestResp) ProtoMessage()    {}
func (*CheckABTestResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{64}
}
func (m *CheckABTestResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckABTestResp.Unmarshal(m, b)
}
func (m *CheckABTestResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckABTestResp.Marshal(b, m, deterministic)
}
func (dst *CheckABTestResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckABTestResp.Merge(dst, src)
}
func (m *CheckABTestResp) XXX_Size() int {
	return xxx_messageInfo_CheckABTestResp.Size(m)
}
func (m *CheckABTestResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckABTestResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckABTestResp proto.InternalMessageInfo

func (m *CheckABTestResp) GetLabId() int64 {
	if m != nil {
		return m.LabId
	}
	return 0
}

type RecommendCampaignReq struct {
	CampaignId           uint32   `protobuf:"varint,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendCampaignReq) Reset()         { *m = RecommendCampaignReq{} }
func (m *RecommendCampaignReq) String() string { return proto.CompactTextString(m) }
func (*RecommendCampaignReq) ProtoMessage()    {}
func (*RecommendCampaignReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{65}
}
func (m *RecommendCampaignReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendCampaignReq.Unmarshal(m, b)
}
func (m *RecommendCampaignReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendCampaignReq.Marshal(b, m, deterministic)
}
func (dst *RecommendCampaignReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendCampaignReq.Merge(dst, src)
}
func (m *RecommendCampaignReq) XXX_Size() int {
	return xxx_messageInfo_RecommendCampaignReq.Size(m)
}
func (m *RecommendCampaignReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendCampaignReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendCampaignReq proto.InternalMessageInfo

func (m *RecommendCampaignReq) GetCampaignId() uint32 {
	if m != nil {
		return m.CampaignId
	}
	return 0
}

type RecommendCampaignResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendCampaignResp) Reset()         { *m = RecommendCampaignResp{} }
func (m *RecommendCampaignResp) String() string { return proto.CompactTextString(m) }
func (*RecommendCampaignResp) ProtoMessage()    {}
func (*RecommendCampaignResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_71f272735b4ae933, []int{66}
}
func (m *RecommendCampaignResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendCampaignResp.Unmarshal(m, b)
}
func (m *RecommendCampaignResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendCampaignResp.Marshal(b, m, deterministic)
}
func (dst *RecommendCampaignResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendCampaignResp.Merge(dst, src)
}
func (m *RecommendCampaignResp) XXX_Size() int {
	return xxx_messageInfo_RecommendCampaignResp.Size(m)
}
func (m *RecommendCampaignResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendCampaignResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendCampaignResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*BatchGetAdReq)(nil), "ad_center.BatchGetAdReq")
	proto.RegisterType((*ConfigExtra)(nil), "ad_center.ConfigExtra")
	proto.RegisterType((*BatchGetAdResp)(nil), "ad_center.BatchGetAdResp")
	proto.RegisterMapType((map[uint32]*CampaignList)(nil), "ad_center.BatchGetAdResp.AdCampaignMapEntry")
	proto.RegisterType((*CampaignList)(nil), "ad_center.CampaignList")
	proto.RegisterType((*VisibleUser)(nil), "ad_center.VisibleUser")
	proto.RegisterType((*AreaInfo)(nil), "ad_center.AreaInfo")
	proto.RegisterType((*GetAreaInfoListReq)(nil), "ad_center.GetAreaInfoListReq")
	proto.RegisterType((*GetAreaInfoListResp)(nil), "ad_center.GetAreaInfoListResp")
	proto.RegisterType((*SetAreaInfoReq)(nil), "ad_center.SetAreaInfoReq")
	proto.RegisterType((*SetAreaInfoResp)(nil), "ad_center.SetAreaInfoResp")
	proto.RegisterType((*Ad)(nil), "ad_center.Ad")
	proto.RegisterType((*GetAdReq)(nil), "ad_center.GetAdReq")
	proto.RegisterType((*GetAdResp)(nil), "ad_center.GetAdResp")
	proto.RegisterType((*SetAdReq)(nil), "ad_center.SetAdReq")
	proto.RegisterType((*SetAdResp)(nil), "ad_center.SetAdResp")
	proto.RegisterType((*SearchAdsReq)(nil), "ad_center.SearchAdsReq")
	proto.RegisterType((*SearchAdsResp)(nil), "ad_center.SearchAdsResp")
	proto.RegisterType((*Campaign)(nil), "ad_center.Campaign")
	proto.RegisterType((*Material)(nil), "ad_center.Material")
	proto.RegisterType((*GetCampaignReq)(nil), "ad_center.GetCampaignReq")
	proto.RegisterType((*GetCampaignResp)(nil), "ad_center.GetCampaignResp")
	proto.RegisterType((*SetCampaignReq)(nil), "ad_center.SetCampaignReq")
	proto.RegisterType((*SetCampaignResp)(nil), "ad_center.SetCampaignResp")
	proto.RegisterType((*BatchSetCampaignReq)(nil), "ad_center.BatchSetCampaignReq")
	proto.RegisterType((*BatchSetCampaignResp)(nil), "ad_center.BatchSetCampaignResp")
	proto.RegisterType((*SearchCampaignsReq)(nil), "ad_center.SearchCampaignsReq")
	proto.RegisterType((*SearchCampaignsResp)(nil), "ad_center.SearchCampaignsResp")
	proto.RegisterType((*CampaignFilter)(nil), "ad_center.CampaignFilter")
	proto.RegisterType((*Filter)(nil), "ad_center.Filter")
	proto.RegisterType((*Filter_ChannelInfo)(nil), "ad_center.Filter.ChannelInfo")
	proto.RegisterType((*TGroupInfo)(nil), "ad_center.TGroupInfo")
	proto.RegisterType((*GetFilterReq)(nil), "ad_center.GetFilterReq")
	proto.RegisterType((*GetFilterResp)(nil), "ad_center.GetFilterResp")
	proto.RegisterType((*SetFilterReq)(nil), "ad_center.SetFilterReq")
	proto.RegisterType((*SetFilterResp)(nil), "ad_center.SetFilterResp")
	proto.RegisterType((*SearchFiltersReq)(nil), "ad_center.SearchFiltersReq")
	proto.RegisterType((*SearchFiltersResp)(nil), "ad_center.SearchFiltersResp")
	proto.RegisterType((*GetPolicyReq)(nil), "ad_center.GetPolicyReq")
	proto.RegisterType((*GetPolicyResp)(nil), "ad_center.GetPolicyResp")
	proto.RegisterType((*SetPolicyReq)(nil), "ad_center.SetPolicyReq")
	proto.RegisterType((*SetPolicyResp)(nil), "ad_center.SetPolicyResp")
	proto.RegisterType((*SearchPoliciesReq)(nil), "ad_center.SearchPoliciesReq")
	proto.RegisterType((*SearchPoliciesResp)(nil), "ad_center.SearchPoliciesResp")
	proto.RegisterType((*Policy)(nil), "ad_center.Policy")
	proto.RegisterType((*GetABTestInfoReq)(nil), "ad_center.GetABTestInfoReq")
	proto.RegisterType((*GetABTestInfoResp)(nil), "ad_center.GetABTestInfoResp")
	proto.RegisterType((*Lab)(nil), "ad_center.Lab")
	proto.RegisterType((*SetABTestInfoReq)(nil), "ad_center.SetABTestInfoReq")
	proto.RegisterType((*SetABTestInfoResp)(nil), "ad_center.SetABTestInfoResp")
	proto.RegisterType((*GeneralMsgReq)(nil), "ad_center.GeneralMsgReq")
	proto.RegisterType((*GeneralMsgResp)(nil), "ad_center.GeneralMsgResp")
	proto.RegisterType((*CoincidenceCountReq)(nil), "ad_center.CoincidenceCountReq")
	proto.RegisterType((*CoincidenceCount)(nil), "ad_center.CoincidenceCount")
	proto.RegisterType((*CoincidenceCountResp)(nil), "ad_center.CoincidenceCountResp")
	proto.RegisterType((*CheckTagIdMateReq)(nil), "ad_center.CheckTagIdMateReq")
	proto.RegisterType((*CheckTagIdMateResp)(nil), "ad_center.CheckTagIdMateResp")
	proto.RegisterType((*DiagnosisCampaignReq)(nil), "ad_center.DiagnosisCampaignReq")
	proto.RegisterType((*DiagnosisCampaignResp)(nil), "ad_center.DiagnosisCampaignResp")
	proto.RegisterType((*CommitAdExposureReq)(nil), "ad_center.CommitAdExposureReq")
	proto.RegisterType((*CommitAdExposureResp)(nil), "ad_center.CommitAdExposureResp")
	proto.RegisterType((*CommitAdClickReq)(nil), "ad_center.CommitAdClickReq")
	proto.RegisterType((*CommitAdClickResp)(nil), "ad_center.CommitAdClickResp")
	proto.RegisterType((*QueryDataReq)(nil), "ad_center.QueryDataReq")
	proto.RegisterType((*QueryDataResp)(nil), "ad_center.QueryDataResp")
	proto.RegisterType((*QueryDataResp_Metric)(nil), "ad_center.QueryDataResp.Metric")
	proto.RegisterType((*CheckABTestReq)(nil), "ad_center.CheckABTestReq")
	proto.RegisterType((*CheckABTestResp)(nil), "ad_center.CheckABTestResp")
	proto.RegisterType((*RecommendCampaignReq)(nil), "ad_center.RecommendCampaignReq")
	proto.RegisterType((*RecommendCampaignResp)(nil), "ad_center.RecommendCampaignResp")
	proto.RegisterEnum("ad_center.Ad_Mic_Mode_Type", Ad_Mic_Mode_Type_name, Ad_Mic_Mode_Type_value)
	proto.RegisterEnum("ad_center.VisibleUserType", VisibleUserType_name, VisibleUserType_value)
	proto.RegisterEnum("ad_center.Area_Type", Area_Type_name, Area_Type_value)
	proto.RegisterEnum("ad_center.CampaignCreativeType", CampaignCreativeType_name, CampaignCreativeType_value)
	proto.RegisterEnum("ad_center.AdCenterStatus", AdCenterStatus_name, AdCenterStatus_value)
	proto.RegisterEnum("ad_center.BatchGetAdReq_Ad_Config_Extra_Type", BatchGetAdReq_Ad_Config_Extra_Type_name, BatchGetAdReq_Ad_Config_Extra_Type_value)
	proto.RegisterEnum("ad_center.Campaign_PolicyType", Campaign_PolicyType_name, Campaign_PolicyType_value)
	proto.RegisterEnum("ad_center.SearchCampaignsReq_CampaignFilterStatusDesc", SearchCampaignsReq_CampaignFilterStatusDesc_name, SearchCampaignsReq_CampaignFilterStatusDesc_value)
	proto.RegisterEnum("ad_center.SearchCampaignsReq_ChannelType", SearchCampaignsReq_ChannelType_name, SearchCampaignsReq_ChannelType_value)
	proto.RegisterEnum("ad_center.CampaignFilter_CampaignFilterStatusDesc", CampaignFilter_CampaignFilterStatusDesc_name, CampaignFilter_CampaignFilterStatusDesc_value)
	proto.RegisterEnum("ad_center.Filter_Platform", Filter_Platform_name, Filter_Platform_value)
	proto.RegisterEnum("ad_center.Filter_ChannelType", Filter_ChannelType_name, Filter_ChannelType_value)
	proto.RegisterEnum("ad_center.Filter_MarketId", Filter_MarketId_name, Filter_MarketId_value)
	proto.RegisterEnum("ad_center.Policy_PolicyType", Policy_PolicyType_name, Policy_PolicyType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AdCenterClient is the client API for AdCenter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AdCenterClient interface {
	BatchGetAd(ctx context.Context, in *BatchGetAdReq, opts ...grpc.CallOption) (*BatchGetAdResp, error)
	GetAd(ctx context.Context, in *GetAdReq, opts ...grpc.CallOption) (*GetAdResp, error)
	SetAd(ctx context.Context, in *SetAdReq, opts ...grpc.CallOption) (*SetAdResp, error)
	SearchAds(ctx context.Context, in *SearchAdsReq, opts ...grpc.CallOption) (*SearchAdsResp, error)
	GetCampaign(ctx context.Context, in *GetCampaignReq, opts ...grpc.CallOption) (*GetCampaignResp, error)
	SetCampaign(ctx context.Context, in *SetCampaignReq, opts ...grpc.CallOption) (*SetCampaignResp, error)
	BatchSetCampaign(ctx context.Context, in *BatchSetCampaignReq, opts ...grpc.CallOption) (*BatchSetCampaignResp, error)
	SearchCampaigns(ctx context.Context, in *SearchCampaignsReq, opts ...grpc.CallOption) (*SearchCampaignsResp, error)
	GetFilter(ctx context.Context, in *GetFilterReq, opts ...grpc.CallOption) (*GetFilterResp, error)
	SetFilter(ctx context.Context, in *SetFilterReq, opts ...grpc.CallOption) (*SetFilterResp, error)
	SearchFilters(ctx context.Context, in *SearchFiltersReq, opts ...grpc.CallOption) (*SearchFiltersResp, error)
	CoincidenceCount(ctx context.Context, in *CoincidenceCountReq, opts ...grpc.CallOption) (*CoincidenceCountResp, error)
	CheckTagIdMate(ctx context.Context, in *CheckTagIdMateReq, opts ...grpc.CallOption) (*CheckTagIdMateResp, error)
	DiagnosisCampaign(ctx context.Context, in *DiagnosisCampaignReq, opts ...grpc.CallOption) (*DiagnosisCampaignResp, error)
	CommitAdExposure(ctx context.Context, in *CommitAdExposureReq, opts ...grpc.CallOption) (*CommitAdExposureResp, error)
	CommitAdClick(ctx context.Context, in *CommitAdClickReq, opts ...grpc.CallOption) (*CommitAdClickResp, error)
	QueryData(ctx context.Context, in *QueryDataReq, opts ...grpc.CallOption) (*QueryDataResp, error)
	GetPolicy(ctx context.Context, in *GetPolicyReq, opts ...grpc.CallOption) (*GetPolicyResp, error)
	SetPolicy(ctx context.Context, in *SetPolicyReq, opts ...grpc.CallOption) (*SetPolicyResp, error)
	SearchPolicies(ctx context.Context, in *SearchPoliciesReq, opts ...grpc.CallOption) (*SearchPoliciesResp, error)
	GetABTestInfo(ctx context.Context, in *GetABTestInfoReq, opts ...grpc.CallOption) (*GetABTestInfoResp, error)
	SetABTestInfo(ctx context.Context, in *SetABTestInfoReq, opts ...grpc.CallOption) (*SetABTestInfoResp, error)
	GeneralMsg(ctx context.Context, in *GeneralMsgReq, opts ...grpc.CallOption) (*GeneralMsgResp, error)
	CheckABTest(ctx context.Context, in *CheckABTestReq, opts ...grpc.CallOption) (*CheckABTestResp, error)
	// 专区相关 专区和广告位绑定 新建专区就是新建广告位
	GetAreaInfoList(ctx context.Context, in *GetAreaInfoListReq, opts ...grpc.CallOption) (*GetAreaInfoListResp, error)
	SetAreaInfo(ctx context.Context, in *SetAreaInfoReq, opts ...grpc.CallOption) (*SetAreaInfoResp, error)
	// 推小红点
	RecommendCampaign(ctx context.Context, in *RecommendCampaignReq, opts ...grpc.CallOption) (*RecommendCampaignResp, error)
}

type adCenterClient struct {
	cc *grpc.ClientConn
}

func NewAdCenterClient(cc *grpc.ClientConn) AdCenterClient {
	return &adCenterClient{cc}
}

func (c *adCenterClient) BatchGetAd(ctx context.Context, in *BatchGetAdReq, opts ...grpc.CallOption) (*BatchGetAdResp, error) {
	out := new(BatchGetAdResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/BatchGetAd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) GetAd(ctx context.Context, in *GetAdReq, opts ...grpc.CallOption) (*GetAdResp, error) {
	out := new(GetAdResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/GetAd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) SetAd(ctx context.Context, in *SetAdReq, opts ...grpc.CallOption) (*SetAdResp, error) {
	out := new(SetAdResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/SetAd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) SearchAds(ctx context.Context, in *SearchAdsReq, opts ...grpc.CallOption) (*SearchAdsResp, error) {
	out := new(SearchAdsResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/SearchAds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) GetCampaign(ctx context.Context, in *GetCampaignReq, opts ...grpc.CallOption) (*GetCampaignResp, error) {
	out := new(GetCampaignResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/GetCampaign", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) SetCampaign(ctx context.Context, in *SetCampaignReq, opts ...grpc.CallOption) (*SetCampaignResp, error) {
	out := new(SetCampaignResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/SetCampaign", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) BatchSetCampaign(ctx context.Context, in *BatchSetCampaignReq, opts ...grpc.CallOption) (*BatchSetCampaignResp, error) {
	out := new(BatchSetCampaignResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/BatchSetCampaign", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) SearchCampaigns(ctx context.Context, in *SearchCampaignsReq, opts ...grpc.CallOption) (*SearchCampaignsResp, error) {
	out := new(SearchCampaignsResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/SearchCampaigns", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) GetFilter(ctx context.Context, in *GetFilterReq, opts ...grpc.CallOption) (*GetFilterResp, error) {
	out := new(GetFilterResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/GetFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) SetFilter(ctx context.Context, in *SetFilterReq, opts ...grpc.CallOption) (*SetFilterResp, error) {
	out := new(SetFilterResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/SetFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) SearchFilters(ctx context.Context, in *SearchFiltersReq, opts ...grpc.CallOption) (*SearchFiltersResp, error) {
	out := new(SearchFiltersResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/SearchFilters", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) CoincidenceCount(ctx context.Context, in *CoincidenceCountReq, opts ...grpc.CallOption) (*CoincidenceCountResp, error) {
	out := new(CoincidenceCountResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/CoincidenceCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) CheckTagIdMate(ctx context.Context, in *CheckTagIdMateReq, opts ...grpc.CallOption) (*CheckTagIdMateResp, error) {
	out := new(CheckTagIdMateResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/CheckTagIdMate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) DiagnosisCampaign(ctx context.Context, in *DiagnosisCampaignReq, opts ...grpc.CallOption) (*DiagnosisCampaignResp, error) {
	out := new(DiagnosisCampaignResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/DiagnosisCampaign", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) CommitAdExposure(ctx context.Context, in *CommitAdExposureReq, opts ...grpc.CallOption) (*CommitAdExposureResp, error) {
	out := new(CommitAdExposureResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/CommitAdExposure", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) CommitAdClick(ctx context.Context, in *CommitAdClickReq, opts ...grpc.CallOption) (*CommitAdClickResp, error) {
	out := new(CommitAdClickResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/CommitAdClick", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) QueryData(ctx context.Context, in *QueryDataReq, opts ...grpc.CallOption) (*QueryDataResp, error) {
	out := new(QueryDataResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/QueryData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) GetPolicy(ctx context.Context, in *GetPolicyReq, opts ...grpc.CallOption) (*GetPolicyResp, error) {
	out := new(GetPolicyResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/GetPolicy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) SetPolicy(ctx context.Context, in *SetPolicyReq, opts ...grpc.CallOption) (*SetPolicyResp, error) {
	out := new(SetPolicyResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/SetPolicy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) SearchPolicies(ctx context.Context, in *SearchPoliciesReq, opts ...grpc.CallOption) (*SearchPoliciesResp, error) {
	out := new(SearchPoliciesResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/SearchPolicies", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) GetABTestInfo(ctx context.Context, in *GetABTestInfoReq, opts ...grpc.CallOption) (*GetABTestInfoResp, error) {
	out := new(GetABTestInfoResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/GetABTestInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) SetABTestInfo(ctx context.Context, in *SetABTestInfoReq, opts ...grpc.CallOption) (*SetABTestInfoResp, error) {
	out := new(SetABTestInfoResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/SetABTestInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) GeneralMsg(ctx context.Context, in *GeneralMsgReq, opts ...grpc.CallOption) (*GeneralMsgResp, error) {
	out := new(GeneralMsgResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/GeneralMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) CheckABTest(ctx context.Context, in *CheckABTestReq, opts ...grpc.CallOption) (*CheckABTestResp, error) {
	out := new(CheckABTestResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/CheckABTest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) GetAreaInfoList(ctx context.Context, in *GetAreaInfoListReq, opts ...grpc.CallOption) (*GetAreaInfoListResp, error) {
	out := new(GetAreaInfoListResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/GetAreaInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) SetAreaInfo(ctx context.Context, in *SetAreaInfoReq, opts ...grpc.CallOption) (*SetAreaInfoResp, error) {
	out := new(SetAreaInfoResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/SetAreaInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adCenterClient) RecommendCampaign(ctx context.Context, in *RecommendCampaignReq, opts ...grpc.CallOption) (*RecommendCampaignResp, error) {
	out := new(RecommendCampaignResp)
	err := c.cc.Invoke(ctx, "/ad_center.AdCenter/RecommendCampaign", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdCenterServer is the server API for AdCenter service.
type AdCenterServer interface {
	BatchGetAd(context.Context, *BatchGetAdReq) (*BatchGetAdResp, error)
	GetAd(context.Context, *GetAdReq) (*GetAdResp, error)
	SetAd(context.Context, *SetAdReq) (*SetAdResp, error)
	SearchAds(context.Context, *SearchAdsReq) (*SearchAdsResp, error)
	GetCampaign(context.Context, *GetCampaignReq) (*GetCampaignResp, error)
	SetCampaign(context.Context, *SetCampaignReq) (*SetCampaignResp, error)
	BatchSetCampaign(context.Context, *BatchSetCampaignReq) (*BatchSetCampaignResp, error)
	SearchCampaigns(context.Context, *SearchCampaignsReq) (*SearchCampaignsResp, error)
	GetFilter(context.Context, *GetFilterReq) (*GetFilterResp, error)
	SetFilter(context.Context, *SetFilterReq) (*SetFilterResp, error)
	SearchFilters(context.Context, *SearchFiltersReq) (*SearchFiltersResp, error)
	CoincidenceCount(context.Context, *CoincidenceCountReq) (*CoincidenceCountResp, error)
	CheckTagIdMate(context.Context, *CheckTagIdMateReq) (*CheckTagIdMateResp, error)
	DiagnosisCampaign(context.Context, *DiagnosisCampaignReq) (*DiagnosisCampaignResp, error)
	CommitAdExposure(context.Context, *CommitAdExposureReq) (*CommitAdExposureResp, error)
	CommitAdClick(context.Context, *CommitAdClickReq) (*CommitAdClickResp, error)
	QueryData(context.Context, *QueryDataReq) (*QueryDataResp, error)
	GetPolicy(context.Context, *GetPolicyReq) (*GetPolicyResp, error)
	SetPolicy(context.Context, *SetPolicyReq) (*SetPolicyResp, error)
	SearchPolicies(context.Context, *SearchPoliciesReq) (*SearchPoliciesResp, error)
	GetABTestInfo(context.Context, *GetABTestInfoReq) (*GetABTestInfoResp, error)
	SetABTestInfo(context.Context, *SetABTestInfoReq) (*SetABTestInfoResp, error)
	GeneralMsg(context.Context, *GeneralMsgReq) (*GeneralMsgResp, error)
	CheckABTest(context.Context, *CheckABTestReq) (*CheckABTestResp, error)
	// 专区相关 专区和广告位绑定 新建专区就是新建广告位
	GetAreaInfoList(context.Context, *GetAreaInfoListReq) (*GetAreaInfoListResp, error)
	SetAreaInfo(context.Context, *SetAreaInfoReq) (*SetAreaInfoResp, error)
	// 推小红点
	RecommendCampaign(context.Context, *RecommendCampaignReq) (*RecommendCampaignResp, error)
}

func RegisterAdCenterServer(s *grpc.Server, srv AdCenterServer) {
	s.RegisterService(&_AdCenter_serviceDesc, srv)
}

func _AdCenter_BatchGetAd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).BatchGetAd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/BatchGetAd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).BatchGetAd(ctx, req.(*BatchGetAdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_GetAd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).GetAd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/GetAd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).GetAd(ctx, req.(*GetAdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_SetAd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).SetAd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/SetAd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).SetAd(ctx, req.(*SetAdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_SearchAds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).SearchAds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/SearchAds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).SearchAds(ctx, req.(*SearchAdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_GetCampaign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCampaignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).GetCampaign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/GetCampaign",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).GetCampaign(ctx, req.(*GetCampaignReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_SetCampaign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCampaignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).SetCampaign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/SetCampaign",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).SetCampaign(ctx, req.(*SetCampaignReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_BatchSetCampaign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetCampaignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).BatchSetCampaign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/BatchSetCampaign",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).BatchSetCampaign(ctx, req.(*BatchSetCampaignReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_SearchCampaigns_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCampaignsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).SearchCampaigns(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/SearchCampaigns",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).SearchCampaigns(ctx, req.(*SearchCampaignsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_GetFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).GetFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/GetFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).GetFilter(ctx, req.(*GetFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_SetFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).SetFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/SetFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).SetFilter(ctx, req.(*SetFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_SearchFilters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchFiltersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).SearchFilters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/SearchFilters",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).SearchFilters(ctx, req.(*SearchFiltersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_CoincidenceCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CoincidenceCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).CoincidenceCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/CoincidenceCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).CoincidenceCount(ctx, req.(*CoincidenceCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_CheckTagIdMate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckTagIdMateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).CheckTagIdMate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/CheckTagIdMate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).CheckTagIdMate(ctx, req.(*CheckTagIdMateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_DiagnosisCampaign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiagnosisCampaignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).DiagnosisCampaign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/DiagnosisCampaign",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).DiagnosisCampaign(ctx, req.(*DiagnosisCampaignReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_CommitAdExposure_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommitAdExposureReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).CommitAdExposure(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/CommitAdExposure",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).CommitAdExposure(ctx, req.(*CommitAdExposureReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_CommitAdClick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommitAdClickReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).CommitAdClick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/CommitAdClick",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).CommitAdClick(ctx, req.(*CommitAdClickReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_QueryData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).QueryData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/QueryData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).QueryData(ctx, req.(*QueryDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_GetPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPolicyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).GetPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/GetPolicy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).GetPolicy(ctx, req.(*GetPolicyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_SetPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPolicyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).SetPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/SetPolicy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).SetPolicy(ctx, req.(*SetPolicyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_SearchPolicies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPoliciesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).SearchPolicies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/SearchPolicies",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).SearchPolicies(ctx, req.(*SearchPoliciesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_GetABTestInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetABTestInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).GetABTestInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/GetABTestInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).GetABTestInfo(ctx, req.(*GetABTestInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_SetABTestInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetABTestInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).SetABTestInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/SetABTestInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).SetABTestInfo(ctx, req.(*SetABTestInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_GeneralMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GeneralMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).GeneralMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/GeneralMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).GeneralMsg(ctx, req.(*GeneralMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_CheckABTest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckABTestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).CheckABTest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/CheckABTest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).CheckABTest(ctx, req.(*CheckABTestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_GetAreaInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAreaInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).GetAreaInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/GetAreaInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).GetAreaInfoList(ctx, req.(*GetAreaInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_SetAreaInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAreaInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).SetAreaInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/SetAreaInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).SetAreaInfo(ctx, req.(*SetAreaInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdCenter_RecommendCampaign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendCampaignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdCenterServer).RecommendCampaign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ad_center.AdCenter/RecommendCampaign",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdCenterServer).RecommendCampaign(ctx, req.(*RecommendCampaignReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AdCenter_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ad_center.AdCenter",
	HandlerType: (*AdCenterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchGetAd",
			Handler:    _AdCenter_BatchGetAd_Handler,
		},
		{
			MethodName: "GetAd",
			Handler:    _AdCenter_GetAd_Handler,
		},
		{
			MethodName: "SetAd",
			Handler:    _AdCenter_SetAd_Handler,
		},
		{
			MethodName: "SearchAds",
			Handler:    _AdCenter_SearchAds_Handler,
		},
		{
			MethodName: "GetCampaign",
			Handler:    _AdCenter_GetCampaign_Handler,
		},
		{
			MethodName: "SetCampaign",
			Handler:    _AdCenter_SetCampaign_Handler,
		},
		{
			MethodName: "BatchSetCampaign",
			Handler:    _AdCenter_BatchSetCampaign_Handler,
		},
		{
			MethodName: "SearchCampaigns",
			Handler:    _AdCenter_SearchCampaigns_Handler,
		},
		{
			MethodName: "GetFilter",
			Handler:    _AdCenter_GetFilter_Handler,
		},
		{
			MethodName: "SetFilter",
			Handler:    _AdCenter_SetFilter_Handler,
		},
		{
			MethodName: "SearchFilters",
			Handler:    _AdCenter_SearchFilters_Handler,
		},
		{
			MethodName: "CoincidenceCount",
			Handler:    _AdCenter_CoincidenceCount_Handler,
		},
		{
			MethodName: "CheckTagIdMate",
			Handler:    _AdCenter_CheckTagIdMate_Handler,
		},
		{
			MethodName: "DiagnosisCampaign",
			Handler:    _AdCenter_DiagnosisCampaign_Handler,
		},
		{
			MethodName: "CommitAdExposure",
			Handler:    _AdCenter_CommitAdExposure_Handler,
		},
		{
			MethodName: "CommitAdClick",
			Handler:    _AdCenter_CommitAdClick_Handler,
		},
		{
			MethodName: "QueryData",
			Handler:    _AdCenter_QueryData_Handler,
		},
		{
			MethodName: "GetPolicy",
			Handler:    _AdCenter_GetPolicy_Handler,
		},
		{
			MethodName: "SetPolicy",
			Handler:    _AdCenter_SetPolicy_Handler,
		},
		{
			MethodName: "SearchPolicies",
			Handler:    _AdCenter_SearchPolicies_Handler,
		},
		{
			MethodName: "GetABTestInfo",
			Handler:    _AdCenter_GetABTestInfo_Handler,
		},
		{
			MethodName: "SetABTestInfo",
			Handler:    _AdCenter_SetABTestInfo_Handler,
		},
		{
			MethodName: "GeneralMsg",
			Handler:    _AdCenter_GeneralMsg_Handler,
		},
		{
			MethodName: "CheckABTest",
			Handler:    _AdCenter_CheckABTest_Handler,
		},
		{
			MethodName: "GetAreaInfoList",
			Handler:    _AdCenter_GetAreaInfoList_Handler,
		},
		{
			MethodName: "SetAreaInfo",
			Handler:    _AdCenter_SetAreaInfo_Handler,
		},
		{
			MethodName: "RecommendCampaign",
			Handler:    _AdCenter_RecommendCampaign_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ad-center/ad-center.proto",
}

func init() {
	proto.RegisterFile("ad-center/ad-center.proto", fileDescriptor_ad_center_71f272735b4ae933)
}

var fileDescriptor_ad_center_71f272735b4ae933 = []byte{
	// 4088 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5b, 0xcd, 0x73, 0xdb, 0x48,
	0x76, 0x37, 0xbf, 0xc1, 0xc7, 0x2f, 0xa8, 0x25, 0xdb, 0x14, 0xe5, 0x0f, 0x0d, 0x76, 0x9c, 0x78,
	0xb5, 0x33, 0xf6, 0x8e, 0x32, 0x93, 0x9d, 0xdd, 0x4a, 0x25, 0x4b, 0x93, 0xb4, 0xcc, 0x8c, 0x48,
	0x2a, 0xa0, 0x64, 0x8f, 0x27, 0x07, 0x14, 0x44, 0xb4, 0x29, 0xc4, 0x24, 0x00, 0x03, 0xa0, 0x3d,
	0x4c, 0x55, 0x2e, 0xa9, 0xa4, 0x52, 0x49, 0xaa, 0x72, 0xdc, 0xc3, 0xde, 0x92, 0x7b, 0x4e, 0x39,
	0x26, 0x7f, 0x4a, 0xee, 0xc9, 0x25, 0xff, 0x42, 0x26, 0xd5, 0x5f, 0x40, 0x03, 0x20, 0x65, 0xcf,
	0x54, 0x92, 0xbd, 0xec, 0x49, 0xec, 0xf7, 0x5e, 0xbf, 0x6e, 0xbc, 0xaf, 0xfe, 0xf5, 0x03, 0x04,
	0xfb, 0xa6, 0xf5, 0xe9, 0x0c, 0x3b, 0x21, 0xf6, 0x1f, 0x47, 0xbf, 0x1e, 0x79, 0xbe, 0x1b, 0xba,
	0xa8, 0x6a, 0x5a, 0x06, 0x23, 0x68, 0xbf, 0x2a, 0x41, 0xe3, 0x89, 0x19, 0xce, 0xae, 0x4e, 0x70,
	0xd8, 0xb5, 0x74, 0xfc, 0x06, 0xa9, 0x50, 0x58, 0xd9, 0x56, 0x3b, 0x77, 0x98, 0x7b, 0xd8, 0xd0,
	0xc9, 0x4f, 0x74, 0x07, 0xc0, 0xb4, 0x0c, 0xdb, 0x32, 0x16, 0x76, 0x10, 0xb6, 0xf3, 0x87, 0x85,
	0x87, 0x0d, 0x5d, 0x31, 0xad, 0xa1, 0x75, 0x6a, 0x07, 0x21, 0xba, 0x0b, 0x30, 0xbb, 0x32, 0x1d,
	0x07, 0x2f, 0x0c, 0xdb, 0x6a, 0x17, 0xe8, 0xb4, 0x2a, 0xa7, 0x0c, 0x2d, 0x74, 0x13, 0xca, 0xa6,
	0xe7, 0x11, 0x56, 0x91, 0xb2, 0x4a, 0xa6, 0xe7, 0x0d, 0x2d, 0x74, 0x00, 0xd5, 0xa5, 0xe9, 0xbf,
	0xc6, 0x21, 0xe1, 0x94, 0x28, 0x47, 0x61, 0x04, 0xc6, 0x9c, 0x2d, 0x6c, 0xec, 0x84, 0x86, 0xed,
	0xb5, 0xcb, 0x8c, 0xc9, 0x08, 0x43, 0x0f, 0xdd, 0x87, 0x1a, 0x67, 0x86, 0x6b, 0x0f, 0xb7, 0x2b,
	0x94, 0x0d, 0x8c, 0x74, 0xbe, 0xf6, 0x30, 0x7a, 0x00, 0x4d, 0x2e, 0xf0, 0x16, 0xfb, 0x81, 0xed,
	0x3a, 0x6d, 0x85, 0xca, 0x34, 0x18, 0xf5, 0x39, 0x23, 0xa2, 0x0e, 0x28, 0xde, 0xc2, 0x0c, 0x5f,
	0xb9, 0xfe, 0xb2, 0x5d, 0x65, 0x6b, 0x88, 0x31, 0x6a, 0x42, 0xde, 0x0d, 0xda, 0x40, 0xa9, 0x79,
	0x37, 0x40, 0x1f, 0x41, 0x5d, 0x3c, 0x23, 0x5d, 0xb4, 0x46, 0x39, 0x35, 0x4e, 0xa3, 0xab, 0x1e,
	0x40, 0xd5, 0xc2, 0x6f, 0xed, 0x19, 0x26, 0x0f, 0x54, 0x3f, 0xcc, 0x3d, 0xac, 0xeb, 0x0a, 0x23,
	0x0c, 0x2d, 0xf4, 0x31, 0x34, 0xa3, 0xf9, 0xe6, 0x25, 0x91, 0x68, 0x50, 0x0d, 0x42, 0xeb, 0xb9,
	0x79, 0x39, 0xb4, 0xd0, 0x6d, 0xa8, 0x98, 0x3e, 0x36, 0x09, 0xbb, 0x49, 0xd9, 0x65, 0x32, 0x1c,
	0x5a, 0x68, 0x1f, 0x94, 0xa5, 0x3d, 0x33, 0x96, 0xae, 0x85, 0xdb, 0x2d, 0xca, 0xa9, 0x2c, 0xed,
	0xd9, 0xc8, 0xb5, 0xe8, 0xb2, 0x84, 0x35, 0x73, 0x57, 0x4e, 0xd8, 0x56, 0xb9, 0x1d, 0xed, 0x59,
	0x8f, 0x8c, 0x93, 0xcb, 0xce, 0x89, 0xde, 0x9d, 0xd4, 0xb2, 0xf3, 0xa1, 0x85, 0x8e, 0x60, 0x87,
	0xbb, 0x42, 0xf2, 0x23, 0x3a, 0xcc, 0x3d, 0xac, 0xea, 0x2d, 0xc6, 0xe8, 0x45, 0xde, 0xfc, 0x0c,
	0x6e, 0xce, 0x89, 0xa0, 0xeb, 0xbc, 0xb2, 0xe7, 0x06, 0xfe, 0x36, 0xf4, 0x4d, 0x66, 0x91, 0x5d,
	0xaa, 0x18, 0xcd, 0x71, 0xd8, 0xa3, 0xbc, 0x01, 0x61, 0x11, 0xc3, 0x68, 0x5f, 0xc0, 0x5e, 0xd7,
	0x32, 0x18, 0xd5, 0xa0, 0x64, 0x83, 0x1a, 0xac, 0x06, 0x95, 0xe1, 0xf8, 0x79, 0xf7, 0x74, 0xd8,
	0x57, 0x6f, 0x20, 0x15, 0xea, 0xdd, 0x59, 0x68, 0xbf, 0xb5, 0xc3, 0x75, 0xd7, 0xc7, 0xa6, 0x9a,
	0xd3, 0xba, 0x50, 0x93, 0x34, 0xa1, 0x63, 0x00, 0x66, 0x1b, 0xe7, 0x95, 0x1b, 0xb4, 0x73, 0x87,
	0x85, 0x87, 0xb5, 0xe3, 0xdd, 0x47, 0x51, 0x1c, 0x3f, 0x22, 0xb3, 0x86, 0xce, 0x2b, 0x57, 0xaf,
	0x9a, 0xfc, 0x57, 0xa0, 0xfd, 0x63, 0x1e, 0x9a, 0x72, 0x6c, 0x07, 0x1e, 0x3a, 0x87, 0x16, 0x99,
	0x63, 0x2e, 0x3d, 0xd3, 0x9e, 0x3b, 0xc6, 0xd2, 0xf4, 0xb8, 0xae, 0x4f, 0x24, 0x5d, 0xc9, 0x39,
	0x8f, 0xba, 0x56, 0x8f, 0xcb, 0x8f, 0x4c, 0x6f, 0xe0, 0x84, 0xfe, 0x5a, 0x6f, 0x98, 0x32, 0x0d,
	0xfd, 0x14, 0xaa, 0xd1, 0xe6, 0xda, 0xf9, 0xc3, 0xdc, 0xb6, 0xbd, 0x29, 0x62, 0x6f, 0xe8, 0x13,
	0x28, 0x51, 0xe3, 0xd1, 0x7c, 0xa9, 0x1d, 0xdf, 0x92, 0xa4, 0xa5, 0xa7, 0xd6, 0x99, 0x50, 0xe7,
	0x25, 0xa0, 0xec, 0x26, 0x48, 0xa2, 0xbe, 0xc6, 0x6b, 0x91, 0xa8, 0xaf, 0xf1, 0x1a, 0x7d, 0x0a,
	0xa5, 0xb7, 0xe6, 0x62, 0x85, 0xf9, 0x1e, 0x6e, 0xcb, 0x5a, 0xf9, 0x6c, 0x92, 0xb2, 0x3a, 0x93,
	0xfa, 0x45, 0xfe, 0xcb, 0x9c, 0xf6, 0x0c, 0xea, 0x32, 0x0b, 0x7d, 0x09, 0x8d, 0xc8, 0x3a, 0x34,
	0xdd, 0xb3, 0xa6, 0x16, 0xf2, 0x7a, 0x7d, 0x26, 0xcd, 0xd4, 0x46, 0x50, 0x7b, 0x6e, 0x07, 0xf6,
	0xe5, 0x02, 0x5f, 0x04, 0xd8, 0x47, 0x08, 0x8a, 0x34, 0x30, 0xd8, 0xf6, 0xe8, 0x6f, 0x42, 0xb3,
	0x70, 0x30, 0xa3, 0xdb, 0xab, 0xea, 0xf4, 0x37, 0xba, 0x05, 0x65, 0x0f, 0xfb, 0xb6, 0x2b, 0x4a,
	0x07, 0x1f, 0x69, 0xff, 0x96, 0x07, 0x45, 0x18, 0x8e, 0xe4, 0x63, 0x54, 0x92, 0xf2, 0xb6, 0x45,
	0x14, 0x39, 0xe6, 0x12, 0x0b, 0x45, 0xe4, 0x37, 0xa9, 0x0b, 0x2b, 0xcf, 0x32, 0x43, 0x6c, 0x84,
	0xf6, 0x12, 0x53, 0x6d, 0x05, 0x1d, 0x18, 0xe9, 0xdc, 0x66, 0x02, 0x33, 0x1f, 0x47, 0x02, 0x45,
	0x26, 0xc0, 0x48, 0x54, 0xa0, 0x03, 0x8a, 0xeb, 0x61, 0xdf, 0x0c, 0x5d, 0x9f, 0x96, 0xa4, 0xaa,
	0x1e, 0x8d, 0x69, 0x19, 0x23, 0x35, 0x30, 0x68, 0x97, 0x69, 0xfd, 0x2b, 0x91, 0xfa, 0x17, 0x90,
	0xdd, 0x5b, 0x78, 0x81, 0x43, 0x56, 0x87, 0x14, 0x9d, 0x8f, 0xa2, 0xa7, 0x57, 0xa4, 0xa7, 0xef,
	0x80, 0xc2, 0x13, 0x2c, 0x68, 0x57, 0x0f, 0x0b, 0x44, 0xbd, 0x18, 0xa3, 0x23, 0x28, 0xae, 0x02,
	0xec, 0xd3, 0x92, 0x93, 0x0c, 0x07, 0xc9, 0xa6, 0x3a, 0x95, 0x41, 0x3f, 0x82, 0x06, 0x8d, 0x36,
	0xcf, 0xb7, 0x5d, 0xdf, 0x0e, 0xd7, 0xed, 0x03, 0x96, 0xd4, 0x84, 0x78, 0xc6, 0x69, 0xda, 0x43,
	0x40, 0x24, 0x82, 0xb9, 0x01, 0xa9, 0xd7, 0xf1, 0x9b, 0x4d, 0x4e, 0xd1, 0x4e, 0x60, 0x37, 0x23,
	0x19, 0xc4, 0x31, 0xbd, 0x25, 0x08, 0x92, 0x31, 0x4d, 0x03, 0xa0, 0x0f, 0xcd, 0x69, 0xac, 0x88,
	0x2c, 0xf7, 0x43, 0x92, 0x76, 0x07, 0x5a, 0x09, 0x2d, 0x81, 0xa7, 0xfd, 0x7b, 0x0e, 0xf2, 0x5d,
	0xeb, 0x83, 0x82, 0xe0, 0x16, 0x94, 0xaf, 0xb0, 0x3d, 0xbf, 0x0a, 0x45, 0x34, 0xb1, 0x11, 0xda,
	0x83, 0xd2, 0x3b, 0xdb, 0x0a, 0xaf, 0xc4, 0x21, 0x44, 0x07, 0x91, 0x39, 0x4a, 0x92, 0x97, 0x6e,
	0x41, 0x39, 0x08, 0xcd, 0x70, 0x15, 0xf0, 0x83, 0x87, 0x8f, 0xd2, 0xe1, 0x55, 0x79, 0x5f, 0x78,
	0x29, 0x99, 0xf0, 0x8a, 0x63, 0xa5, 0x2a, 0xc7, 0x8a, 0xd6, 0x01, 0x25, 0x3a, 0x7c, 0x53, 0xcf,
	0xa8, 0x1d, 0x41, 0x35, 0x2e, 0x5e, 0x77, 0x89, 0x19, 0x28, 0xb3, 0x76, 0xdc, 0x90, 0xcd, 0x68,
	0xe9, 0xf9, 0xae, 0xa5, 0xfd, 0x18, 0x94, 0xa9, 0xd0, 0x73, 0x17, 0xf2, 0xe6, 0x36, 0x51, 0xd3,
	0xd2, 0x0e, 0xa0, 0x3a, 0x8d, 0xd4, 0xa6, 0xd7, 0xfc, 0xbb, 0x1c, 0xd4, 0xa7, 0xd8, 0xf4, 0x67,
	0x57, 0x5d, 0x2b, 0x20, 0xca, 0x6e, 0x41, 0xd9, 0x7d, 0xf5, 0x2a, 0xc0, 0x21, 0x17, 0xe2, 0x23,
	0x62, 0xd4, 0x85, 0xbd, 0xb4, 0x43, 0xea, 0x81, 0x86, 0xce, 0x06, 0x04, 0x0f, 0x5c, 0xe2, 0xb9,
	0xed, 0xc8, 0x69, 0x58, 0xa5, 0x14, 0x6a, 0x85, 0x7d, 0x50, 0xb0, 0x63, 0xc9, 0x29, 0x58, 0xc1,
	0x8e, 0x45, 0x59, 0xc2, 0xa1, 0xa5, 0xd8, 0xa1, 0xda, 0x4f, 0xa1, 0x21, 0xed, 0x25, 0x20, 0xc7,
	0x7f, 0xa1, 0x6b, 0x89, 0x60, 0x4a, 0x3d, 0x1a, 0xe1, 0x68, 0xbf, 0xae, 0x80, 0x22, 0x4a, 0xd4,
	0x07, 0xc5, 0xcc, 0x21, 0xd4, 0xed, 0xe5, 0xdc, 0x58, 0xf9, 0x0b, 0x16, 0xec, 0x05, 0x9a, 0x9b,
	0x60, 0x2f, 0xe7, 0x17, 0xfe, 0x82, 0x16, 0xc5, 0x03, 0xa8, 0xbe, 0xb5, 0x2d, 0xec, 0x12, 0x19,
	0xba, 0xe9, 0xaa, 0xae, 0x50, 0xc2, 0x85, 0xbf, 0xa0, 0x41, 0x84, 0xbf, 0x0d, 0xc5, 0xae, 0xc9,
	0x6f, 0x62, 0x03, 0xf2, 0xd7, 0x98, 0xb9, 0x0b, 0xd7, 0xa7, 0x81, 0x54, 0xd5, 0xab, 0x84, 0xd2,
	0x23, 0x04, 0x62, 0x83, 0x3f, 0x5b, 0x2d, 0x3d, 0xaa, 0xae, 0x42, 0x99, 0x15, 0x32, 0x16, 0xda,
	0x36, 0x14, 0x0e, 0x6b, 0xe5, 0x9b, 0x21, 0x81, 0x32, 0x1c, 0xa9, 0x88, 0xb1, 0x14, 0xae, 0x70,
	0x5d, 0xb8, 0xd6, 0xde, 0x17, 0xae, 0xf5, 0x6b, 0xc2, 0xb5, 0x91, 0x28, 0x6d, 0x49, 0xff, 0x36,
	0xaf, 0xf3, 0x6f, 0x2b, 0xe9, 0xdf, 0x3d, 0x71, 0xe8, 0xa9, 0xf4, 0x99, 0xd9, 0x00, 0x7d, 0x46,
	0x90, 0x60, 0x88, 0x7d, 0xdb, 0x5c, 0x04, 0xed, 0x9d, 0x4c, 0x8d, 0x18, 0x71, 0x9e, 0x1e, 0x4b,
	0x11, 0x7f, 0x78, 0xee, 0xc2, 0x9e, 0xad, 0x05, 0x52, 0x21, 0xd8, 0x8d, 0x12, 0x86, 0x16, 0x79,
	0x30, 0xce, 0x94, 0x80, 0x09, 0x30, 0x12, 0x05, 0x1e, 0x1a, 0x34, 0x08, 0xc7, 0x88, 0x5c, 0xb0,
	0xc7, 0xd0, 0x1c, 0x21, 0xfe, 0x31, 0x77, 0x03, 0x71, 0xe0, 0xdc, 0x77, 0x57, 0x1e, 0x2d, 0xf9,
	0x37, 0x69, 0xc9, 0xaf, 0x32, 0x0a, 0x29, 0xfb, 0x0f, 0x41, 0x0d, 0xcd, 0x4b, 0x12, 0x2d, 0x86,
	0xed, 0x04, 0xd8, 0x0f, 0x0d, 0xa7, 0x7d, 0x8b, 0x6a, 0x69, 0x72, 0xfa, 0x90, 0x92, 0xc7, 0xd4,
	0xcc, 0xae, 0x13, 0x12, 0x34, 0x4a, 0x96, 0x6a, 0xd3, 0x27, 0x07, 0x4e, 0x22, 0x2b, 0xdd, 0x83,
	0x5a, 0xf0, 0xda, 0xf6, 0x8c, 0x77, 0x57, 0xd8, 0x31, 0x9c, 0xf6, 0x3e, 0xc3, 0xcf, 0x84, 0xf4,
	0xe2, 0x0a, 0x3b, 0x63, 0x82, 0xe1, 0x16, 0x66, 0x10, 0x1a, 0xde, 0x2a, 0xb8, 0x62, 0x56, 0xed,
	0x50, 0xab, 0xd6, 0x09, 0xf5, 0x6c, 0x15, 0x5c, 0x51, 0xd3, 0x4a, 0xd0, 0xf1, 0x20, 0x01, 0x1d,
	0x7f, 0x17, 0x5a, 0xcb, 0x75, 0x10, 0x62, 0x7f, 0xed, 0x2d, 0x4c, 0x06, 0x4e, 0xef, 0xb0, 0x8d,
	0xca, 0x64, 0x06, 0x51, 0x09, 0x34, 0x35, 0x17, 0xb6, 0x19, 0x18, 0x34, 0x47, 0xee, 0xd2, 0xbd,
	0xd6, 0x43, 0xf3, 0xb2, 0x4b, 0x88, 0x63, 0x92, 0x8e, 0x7f, 0x08, 0x70, 0x16, 0x5b, 0x12, 0xa0,
	0x3c, 0x9e, 0xe8, 0xa3, 0xee, 0xa9, 0x7a, 0x03, 0xd5, 0x41, 0x99, 0x9c, 0x9d, 0x0f, 0x47, 0xc3,
	0x6f, 0x06, 0x6a, 0x8e, 0x70, 0xba, 0x4f, 0xce, 0x07, 0xd3, 0x73, 0x35, 0x4f, 0x7e, 0xeb, 0xdd,
	0x71, 0x7f, 0x32, 0x52, 0x0b, 0xda, 0x3f, 0x17, 0x40, 0x11, 0x1e, 0xcd, 0x24, 0x67, 0x3a, 0x11,
	0xf3, 0xd7, 0x27, 0x62, 0x21, 0x95, 0x88, 0x07, 0x40, 0x53, 0x8c, 0xcd, 0x2d, 0xb2, 0x03, 0x96,
	0x10, 0xe8, 0xcc, 0xdf, 0x81, 0x56, 0x9c, 0x91, 0x4c, 0xa4, 0x44, 0x45, 0x1a, 0x51, 0x5a, 0x52,
	0xb9, 0x07, 0xd0, 0x74, 0xbd, 0xd0, 0x5e, 0xda, 0x7f, 0x8e, 0x0d, 0xdb, 0xb1, 0xf0, 0xb7, 0x34,
	0x7b, 0x4b, 0x7a, 0x43, 0x50, 0x87, 0x84, 0x48, 0xe0, 0xc0, 0x82, 0x01, 0xf9, 0x0a, 0xaf, 0x7d,
	0x14, 0xc1, 0xcb, 0x89, 0xad, 0x24, 0x13, 0xfb, 0x26, 0x94, 0xdf, 0x61, 0x3a, 0xa3, 0xca, 0xa2,
	0xff, 0x1d, 0xe6, 0x33, 0xec, 0xc0, 0xb8, 0xb2, 0x2d, 0x6c, 0xd1, 0x0c, 0x56, 0xf4, 0x8a, 0x1d,
	0x3c, 0x23, 0xc3, 0x54, 0x0c, 0xd6, 0xd2, 0x31, 0xa8, 0x41, 0x63, 0x65, 0x5b, 0xc6, 0x5b, 0x1b,
	0xbf, 0x63, 0x91, 0x5e, 0x67, 0x61, 0xbc, 0xb2, 0xad, 0xe7, 0x36, 0x7e, 0x47, 0x1d, 0x94, 0x0a,
	0xae, 0x46, 0x3a, 0xb8, 0x52, 0xd1, 0xd9, 0x4c, 0x47, 0xa7, 0x76, 0x08, 0xcd, 0x13, 0x1c, 0x46,
	0x88, 0x6f, 0xc3, 0x09, 0xf5, 0x0c, 0x5a, 0x09, 0x89, 0xc0, 0x43, 0x5f, 0x80, 0x22, 0x90, 0x21,
	0x3f, 0x82, 0xf6, 0x37, 0xc0, 0xc7, 0xa7, 0xf6, 0x22, 0xc4, 0xbe, 0x1e, 0x89, 0x6a, 0x27, 0x14,
	0x3f, 0xc8, 0x6b, 0xfd, 0x40, 0x45, 0x1f, 0x51, 0x08, 0x91, 0xd8, 0x52, 0x7a, 0xd7, 0xa7, 0xb0,
	0x4b, 0x51, 0xfe, 0xb5, 0x0b, 0x16, 0x3e, 0x74, 0xc1, 0x87, 0xb0, 0x97, 0xd5, 0x16, 0x78, 0x04,
	0xa1, 0xdb, 0xfc, 0xac, 0x6a, 0xe8, 0xe4, 0xa7, 0xf6, 0x37, 0x65, 0x40, 0xec, 0x3c, 0x13, 0x82,
	0xbf, 0xe9, 0x13, 0x16, 0xed, 0x42, 0x89, 0x22, 0x5b, 0x8e, 0x77, 0x8a, 0x04, 0xd8, 0xa2, 0xc7,
	0xb0, 0x27, 0x1d, 0x1f, 0x86, 0xeb, 0x5b, 0xd8, 0x37, 0x2e, 0xd7, 0x34, 0xda, 0x4b, 0xfa, 0x4e,
	0x7c, 0x8e, 0x4c, 0x08, 0xe7, 0x09, 0xb9, 0x7a, 0xec, 0xc6, 0x7b, 0x8a, 0xe5, 0x15, 0x2a, 0xaf,
	0x46, 0x9b, 0x13, 0xe2, 0x47, 0xb0, 0x23, 0xe0, 0x6b, 0x2c, 0x5c, 0xa5, 0xc2, 0x2d, 0xc1, 0x10,
	0xb2, 0x2f, 0xa0, 0xc6, 0x0e, 0x35, 0x83, 0x5e, 0x1e, 0x48, 0x96, 0x34, 0x8f, 0x7f, 0x5f, 0xf2,
	0x4b, 0xd6, 0xa0, 0x29, 0x57, 0x4d, 0xe9, 0xf4, 0x3e, 0x0e, 0x66, 0x3a, 0x04, 0xd1, 0x6f, 0x02,
	0xa4, 0xe5, 0x5b, 0xbd, 0xc8, 0xb1, 0xba, 0x74, 0xad, 0x0f, 0x48, 0xe1, 0x48, 0x5e, 0xdd, 0x83,
	0x76, 0x9d, 0x8a, 0x35, 0xe4, 0xbb, 0x7b, 0x80, 0x3e, 0x01, 0x44, 0x4b, 0x34, 0xbb, 0x31, 0x8b,
	0x66, 0x49, 0x83, 0x8a, 0xaa, 0x84, 0x33, 0xe2, 0x0c, 0x5a, 0x66, 0x8e, 0x60, 0x27, 0x7d, 0x80,
	0x04, 0xed, 0x26, 0x15, 0x6e, 0x25, 0x4f, 0x90, 0x80, 0xf8, 0x93, 0xd7, 0xf6, 0xa0, 0xdd, 0xa2,
	0x22, 0x15, 0x56, 0xdc, 0xe9, 0x41, 0x48, 0x59, 0x34, 0xff, 0xf9, 0xed, 0x9f, 0x10, 0xe8, 0xc5,
	0xfb, 0x19, 0xb4, 0xb7, 0x99, 0x81, 0xd4, 0xe4, 0x8b, 0xf1, 0x57, 0xe3, 0xc9, 0x0b, 0xf5, 0x06,
	0xaa, 0x42, 0xa9, 0x77, 0x3a, 0x99, 0x92, 0xb2, 0xad, 0x40, 0x71, 0x72, 0x36, 0x18, 0xab, 0x79,
	0x72, 0x3b, 0x1f, 0x7c, 0x7d, 0x36, 0xd4, 0x07, 0x7d, 0xb5, 0xa0, 0xe9, 0x50, 0xeb, 0x49, 0xad,
	0x8e, 0x36, 0xec, 0xf1, 0x9b, 0xbb, 0xd1, 0x7b, 0xd6, 0x1d, 0x8f, 0x07, 0xa7, 0xc6, 0xf9, 0xcb,
	0xb3, 0x81, 0x7a, 0x03, 0x55, 0xa0, 0x70, 0x76, 0xd2, 0x53, 0x73, 0xe4, 0xc7, 0xc5, 0x49, 0x4f,
	0xcd, 0x13, 0x8d, 0xa7, 0xc3, 0xe7, 0x03, 0xb5, 0x80, 0x1a, 0x50, 0x3d, 0xb9, 0x18, 0x9e, 0xf6,
	0x0d, 0x22, 0x51, 0xd4, 0x2c, 0xd8, 0xcd, 0xf8, 0x2d, 0xf0, 0xd0, 0xcf, 0xa0, 0x2a, 0xd2, 0x2a,
	0x78, 0x7f, 0x0a, 0xc6, 0xb2, 0x24, 0x55, 0x58, 0x13, 0x84, 0xa7, 0x0a, 0x1d, 0x68, 0x7f, 0x9f,
	0x87, 0x66, 0x72, 0x0e, 0x7a, 0x9c, 0x29, 0x2a, 0x1b, 0x2f, 0xb7, 0x91, 0x10, 0xfa, 0x31, 0x94,
	0x5f, 0xd1, 0xa9, 0xfc, 0x5a, 0xbd, 0x23, 0x89, 0xf3, 0x7d, 0x70, 0x01, 0x34, 0x4d, 0x86, 0x6a,
	0x81, 0x86, 0xea, 0xf1, 0xd6, 0xfd, 0x7f, 0x50, 0x98, 0xfe, 0x2f, 0xfa, 0xf1, 0x57, 0x35, 0x28,
	0x73, 0x2b, 0xa4, 0xcf, 0xde, 0x8f, 0x20, 0xba, 0xcd, 0xd3, 0x40, 0x63, 0x5d, 0xbe, 0x9a, 0xa0,
	0x91, 0x60, 0x13, 0xc5, 0xa3, 0x20, 0x15, 0x8f, 0x64, 0x29, 0x2a, 0x5e, 0x57, 0x8a, 0x4a, 0xc9,
	0x52, 0xd4, 0x01, 0xa5, 0xcb, 0x5b, 0x88, 0xfc, 0x4a, 0x1d, 0x8d, 0xc9, 0x59, 0xc9, 0xfb, 0x55,
	0xac, 0xde, 0x94, 0x42, 0xda, 0xa8, 0xda, 0x07, 0x85, 0x90, 0xe9, 0x26, 0xf8, 0xe9, 0x1a, 0x9a,
	0x73, 0x82, 0x4b, 0x68, 0x33, 0x4f, 0x5c, 0x87, 0x45, 0x33, 0x8f, 0x8f, 0xa5, 0x93, 0x17, 0xe4,
	0x93, 0x57, 0xee, 0xff, 0xd5, 0x52, 0xfd, 0xbf, 0x74, 0xbf, 0xaf, 0x9e, 0xed, 0xf7, 0x3d, 0x80,
	0xe6, 0x95, 0xbb, 0xf2, 0x8d, 0x57, 0x3e, 0x7e, 0xb3, 0xc2, 0xce, 0x6c, 0xcd, 0x4f, 0xd7, 0x06,
	0xa1, 0x3e, 0x15, 0x44, 0x52, 0x63, 0x2c, 0x73, 0x2d, 0x49, 0xb1, 0xce, 0x5e, 0xdd, 0x32, 0xd7,
	0xb1, 0x50, 0x0a, 0xac, 0xb7, 0xde, 0x07, 0xd6, 0xd5, 0x6b, 0xc0, 0xfa, 0x4e, 0x02, 0xac, 0x3f,
	0x80, 0x96, 0x83, 0xdf, 0x19, 0xab, 0x80, 0x56, 0x58, 0xc3, 0x32, 0xd7, 0x1c, 0x2f, 0xd7, 0x1d,
	0xfc, 0xee, 0x22, 0x20, 0xf5, 0xb5, 0x6f, 0xae, 0xb7, 0x14, 0xaf, 0xdd, 0x2d, 0xc5, 0xeb, 0x49,
	0x5c, 0x37, 0xd9, 0xcd, 0x7e, 0x8f, 0xe6, 0xe9, 0xdd, 0x4c, 0x5e, 0x3c, 0x12, 0x8d, 0x43, 0x72,
	0x93, 0x17, 0x16, 0xa5, 0xd7, 0x7c, 0x82, 0xf5, 0x98, 0x8b, 0x79, 0xeb, 0xf2, 0x26, 0x75, 0x34,
	0x50, 0x47, 0xb3, 0xe6, 0xe5, 0x87, 0x63, 0xec, 0xcf, 0xe1, 0x56, 0x14, 0xbb, 0x26, 0xef, 0x22,
	0x32, 0xbf, 0xdd, 0xa6, 0xf2, 0x7b, 0x82, 0x2b, 0x5a, 0x8c, 0xa2, 0x61, 0x4b, 0x92, 0x8c, 0x09,
	0xb6, 0x59, 0x00, 0x10, 0x02, 0x65, 0x4a, 0x78, 0x7a, 0xff, 0x7d, 0x78, 0xba, 0xb3, 0x09, 0x4f,
	0x77, 0xfe, 0x2b, 0x17, 0x15, 0x4d, 0xda, 0xc2, 0xa2, 0x31, 0x4d, 0xa3, 0x90, 0x25, 0x5d, 0x29,
	0x14, 0x88, 0x91, 0x90, 0xa5, 0x4b, 0x69, 0x25, 0x34, 0x2f, 0x69, 0x4c, 0xcb, 0x5d, 0xdf, 0x42,
	0xb2, 0xeb, 0x9b, 0x8e, 0xcf, 0x52, 0x36, 0x3e, 0x49, 0xc8, 0x98, 0x21, 0x9e, 0xbb, 0xfe, 0x3a,
	0x3e, 0xdc, 0x41, 0x90, 0x78, 0x07, 0x3e, 0xea, 0x1c, 0x57, 0x58, 0x06, 0x46, 0x9d, 0xe3, 0x38,
	0x03, 0x15, 0xb1, 0xdb, 0x74, 0x06, 0x56, 0x13, 0x19, 0xa8, 0x7d, 0x06, 0xca, 0x99, 0x48, 0x9f,
	0x0a, 0x14, 0xba, 0xa7, 0xe4, 0x52, 0x50, 0x83, 0x4a, 0x77, 0xdc, 0xd7, 0x27, 0xc3, 0x3e, 0x3b,
	0x13, 0x86, 0x93, 0xa9, 0x9a, 0x47, 0x65, 0xc8, 0x9f, 0xf5, 0xfe, 0x8f, 0x8e, 0x95, 0x7f, 0xc9,
	0x91, 0x0b, 0x06, 0x7f, 0x8f, 0xd0, 0x82, 0xda, 0xa8, 0xab, 0x7f, 0x35, 0x38, 0x37, 0xc6, 0x93,
	0x31, 0x51, 0x14, 0x13, 0x4e, 0x87, 0xe7, 0xa4, 0x50, 0x22, 0x68, 0x72, 0xc2, 0xb3, 0x8b, 0xee,
	0xf8, 0xe5, 0xe4, 0x42, 0xcd, 0x23, 0x15, 0xea, 0x9c, 0xf6, 0x4d, 0x77, 0xf8, 0xb2, 0xab, 0x16,
	0xd0, 0x1e, 0xa8, 0x9c, 0x72, 0x3e, 0x39, 0x33, 0xa6, 0x67, 0x83, 0x41, 0x5f, 0x2d, 0x4a, 0x72,
	0xa3, 0xee, 0xf0, 0xab, 0x81, 0x5a, 0x42, 0xbb, 0xd0, 0xe2, 0x94, 0x93, 0xee, 0x68, 0x60, 0x4c,
	0xfb, 0x5f, 0xa9, 0x75, 0xa4, 0x02, 0x70, 0x22, 0xb1, 0xc9, 0x7f, 0x7f, 0x57, 0x90, 0x28, 0xa3,
	0xee, 0xd7, 0xea, 0x77, 0xdf, 0x15, 0xb4, 0x19, 0xc0, 0xf9, 0x09, 0x05, 0xf6, 0x24, 0x54, 0xc8,
	0x55, 0x8c, 0x01, 0xff, 0x25, 0x5e, 0x1a, 0x33, 0x47, 0xa0, 0xc2, 0x3a, 0xa3, 0x8e, 0xf0, 0xb2,
	0xe7, 0xd0, 0xbb, 0x50, 0x74, 0x3d, 0xe0, 0x87, 0x9e, 0x22, 0x6e, 0x07, 0x9b, 0x6a, 0xb5, 0x76,
	0x0f, 0xea, 0x27, 0x38, 0xe4, 0x27, 0xd6, 0x06, 0x24, 0xff, 0x0b, 0x68, 0x48, 0xfc, 0xc0, 0x93,
	0x0e, 0xbe, 0xdc, 0x7b, 0x0e, 0x3e, 0xed, 0xe7, 0x50, 0x9f, 0xca, 0xba, 0xbf, 0xc7, 0xd4, 0xfb,
	0xd0, 0x98, 0x26, 0x96, 0x4d, 0xef, 0xeb, 0x1f, 0x72, 0xa0, 0x32, 0xa8, 0xc0, 0x84, 0x7e, 0xe3,
	0x3d, 0xa9, 0x5f, 0xc2, 0x4e, 0x6a, 0x3f, 0x81, 0x87, 0x7e, 0x02, 0x15, 0xf6, 0x40, 0x02, 0xb6,
	0x6c, 0x78, 0x64, 0x21, 0xc1, 0x5d, 0xc1, 0x6e, 0xd2, 0xdb, 0x5d, 0x21, 0xf8, 0xcc, 0x15, 0xac,
	0x83, 0xb1, 0xc1, 0x9e, 0x5c, 0x8c, 0x0b, 0x70, 0x57, 0xc4, 0xba, 0xbf, 0xc7, 0x54, 0xe6, 0x0a,
	0x69, 0xd9, 0xf4, 0xbe, 0xfe, 0x36, 0x27, 0x1e, 0x9d, 0x0a, 0xd9, 0xf8, 0x07, 0xf8, 0x62, 0x13,
	0x8c, 0x60, 0xeb, 0x14, 0x23, 0x34, 0x92, 0x69, 0xd1, 0x94, 0xe8, 0xf1, 0x20, 0xb7, 0x68, 0xb4,
	0x97, 0xe2, 0x26, 0x15, 0x6f, 0x25, 0xf0, 0xd0, 0xa7, 0xc0, 0x3a, 0x41, 0x36, 0xde, 0xe4, 0x07,
	0xfe, 0x68, 0x91, 0xc8, 0x16, 0xd4, 0xf8, 0xeb, 0x02, 0x94, 0x99, 0xe8, 0x07, 0x35, 0x10, 0x45,
	0xcf, 0xae, 0x90, 0x6c, 0x23, 0xf3, 0x03, 0xb9, 0x98, 0x38, 0x90, 0xef, 0x00, 0x98, 0x97, 0x46,
	0x88, 0x03, 0xf9, 0xc5, 0xa7, 0x79, 0x79, 0x8e, 0x83, 0x90, 0xf5, 0xae, 0x64, 0x20, 0x50, 0x7e,
	0x1f, 0x10, 0xa8, 0x64, 0x80, 0x80, 0x06, 0xc5, 0x85, 0x79, 0x19, 0xb4, 0x15, 0xfa, 0xec, 0x4d,
	0xe9, 0xd9, 0x4f, 0xcd, 0x4b, 0x9d, 0xf2, 0xc8, 0xe5, 0xc3, 0x71, 0xfd, 0xa5, 0xb9, 0x30, 0x78,
	0xa3, 0x8c, 0x58, 0x98, 0x95, 0xf3, 0x16, 0x63, 0xb0, 0x87, 0xbf, 0xf0, 0x17, 0x59, 0x4f, 0x40,
	0xb6, 0x59, 0xf6, 0x25, 0xd4, 0x45, 0x25, 0xa2, 0x70, 0xa0, 0x46, 0xd7, 0xbe, 0x29, 0xad, 0x1d,
	0x17, 0x37, 0xbd, 0xc6, 0x6b, 0x14, 0x6d, 0xf6, 0x1f, 0x7f, 0xff, 0x76, 0x92, 0xa6, 0x81, 0x7a,
	0x82, 0xc3, 0xee, 0x13, 0x6a, 0x32, 0xfe, 0xa2, 0x21, 0x1d, 0xa7, 0x7f, 0x0a, 0x3b, 0x29, 0x99,
	0x6c, 0x30, 0x6f, 0x74, 0xa5, 0x30, 0x5f, 0x61, 0xbb, 0xf9, 0xb4, 0x7f, 0xca, 0x41, 0xe1, 0xd4,
	0xbc, 0xfc, 0x20, 0x7d, 0xf7, 0x00, 0xba, 0x8b, 0x85, 0x3b, 0xd3, 0xcd, 0xd0, 0x76, 0x79, 0x80,
	0x48, 0x14, 0x52, 0x98, 0x24, 0x1f, 0xb0, 0xd6, 0x32, 0xef, 0x6d, 0x6e, 0xb2, 0x6c, 0xe9, 0x83,
	0x2d, 0xfb, 0x0d, 0xa9, 0x99, 0xd7, 0x5b, 0xe9, 0x07, 0x1b, 0xe0, 0x47, 0xa4, 0x08, 0xbc, 0xc7,
	0xba, 0xda, 0x47, 0xa4, 0x84, 0x39, 0xd8, 0x37, 0x17, 0xa3, 0x60, 0xce, 0xbf, 0x2b, 0x58, 0x06,
	0x73, 0x2a, 0x51, 0xd5, 0xc9, 0x4f, 0x4d, 0x83, 0xa6, 0x2c, 0xc2, 0x1a, 0x26, 0x29, 0x99, 0x7f,
	0xcd, 0xc1, 0x6e, 0xcf, 0xb5, 0x9d, 0x99, 0x6d, 0x61, 0x67, 0x86, 0x29, 0x3a, 0x21, 0xda, 0xa2,
	0xae, 0x45, 0x4e, 0xea, 0x5a, 0x24, 0xcb, 0x7c, 0xfe, 0xba, 0x32, 0x5f, 0x48, 0x96, 0xf9, 0x34,
	0xa0, 0x2a, 0x66, 0x01, 0x55, 0x0c, 0x89, 0x4a, 0xf2, 0xa5, 0xe4, 0x00, 0xaa, 0xac, 0xaa, 0xc7,
	0x28, 0x4b, 0x61, 0x84, 0xa1, 0xa5, 0xfd, 0x67, 0x0e, 0xd4, 0xf4, 0xee, 0x51, 0x1b, 0x58, 0x53,
	0x50, 0x3f, 0xe5, 0x0f, 0x2a, 0x86, 0xe8, 0x10, 0x6a, 0xe1, 0x95, 0xe9, 0x30, 0x97, 0x5a, 0xdc,
	0x2f, 0x32, 0x09, 0x7d, 0x0c, 0x8d, 0x68, 0x38, 0x8e, 0xab, 0x68, 0x92, 0x98, 0xb8, 0x0d, 0x15,
	0x53, 0xb7, 0xa1, 0xa8, 0xe2, 0x95, 0xa4, 0x8a, 0x27, 0x35, 0xcd, 0x69, 0x44, 0xb0, 0x37, 0x16,
	0x3c, 0x34, 0xc7, 0xfc, 0xed, 0xaa, 0x74, 0x3f, 0x8c, 0xbe, 0xba, 0x88, 0xae, 0x87, 0xda, 0x5f,
	0xc0, 0x5e, 0xd6, 0x4f, 0xf4, 0xa5, 0x15, 0x84, 0xae, 0x19, 0x84, 0xf1, 0x8b, 0xc4, 0xaa, 0x5e,
	0xa5, 0x14, 0x7a, 0x97, 0x78, 0x0a, 0xea, 0x2c, 0x9e, 0x16, 0xf7, 0x7d, 0x6b, 0xc7, 0x07, 0x89,
	0x77, 0xe2, 0x29, 0xcd, 0x2d, 0x69, 0x12, 0x7d, 0xf9, 0xf8, 0x07, 0xb0, 0xd3, 0xbb, 0xc2, 0xb3,
	0xd7, 0xf4, 0x93, 0x86, 0x91, 0x19, 0xe2, 0xcd, 0x9f, 0xb2, 0xc4, 0x4e, 0xcc, 0x4b, 0xb8, 0x56,
	0xfb, 0x23, 0x40, 0xe9, 0xd9, 0x81, 0x47, 0xf2, 0xe3, 0xd5, 0xc2, 0x64, 0xe1, 0xa8, 0xe8, 0xf4,
	0xf7, 0x36, 0x05, 0x13, 0xd8, 0xeb, 0xdb, 0xe6, 0xdc, 0x71, 0x03, 0x3b, 0x90, 0x1b, 0x8a, 0x29,
	0xb3, 0xe5, 0xd2, 0x66, 0x23, 0xe7, 0xc4, 0xd2, 0x76, 0x56, 0x21, 0xe6, 0xfa, 0xf8, 0x48, 0xbb,
	0x0d, 0x37, 0x37, 0x28, 0x0c, 0x3c, 0xed, 0xaf, 0x0b, 0x24, 0x21, 0x96, 0x4b, 0x3b, 0xec, 0x5a,
	0x83, 0x6f, 0x3d, 0x37, 0x58, 0xf9, 0x5b, 0x9e, 0x35, 0x4a, 0x91, 0x7c, 0x32, 0x45, 0x7e, 0xfb,
	0xb5, 0x4e, 0x26, 0x9c, 0xeb, 0x19, 0xbf, 0xdc, 0x87, 0x9a, 0x78, 0xdf, 0x14, 0x7f, 0xae, 0x03,
	0x82, 0x34, 0xb4, 0xb4, 0x5b, 0x24, 0xde, 0xd3, 0x6e, 0x08, 0x3c, 0xed, 0x2f, 0x0b, 0x24, 0xe5,
	0x19, 0xa3, 0xb7, 0xb0, 0x67, 0xaf, 0x7f, 0xeb, 0x9c, 0xff, 0x7f, 0xe7, 0xec, 0xc2, 0x4e, 0xca,
	0x07, 0x81, 0x47, 0xee, 0x11, 0xf5, 0x3f, 0x59, 0x61, 0x7f, 0xdd, 0x37, 0x43, 0x73, 0xeb, 0x19,
	0x92, 0x5a, 0x3c, 0x9f, 0x59, 0x9c, 0x14, 0x7c, 0xdf, 0x5d, 0x1a, 0x04, 0x93, 0xf1, 0x63, 0x44,
	0x21, 0x84, 0xbe, 0x19, 0xd2, 0xbe, 0x41, 0xe8, 0x32, 0x16, 0xbb, 0x48, 0x94, 0x43, 0x97, 0x32,
	0x10, 0x14, 0x57, 0x8e, 0x1d, 0xbd, 0x25, 0x26, 0xbf, 0xb5, 0xbf, 0xca, 0x41, 0x43, 0xda, 0x50,
	0xe0, 0x91, 0x6a, 0xf0, 0x66, 0xe5, 0x86, 0x66, 0xc0, 0x0b, 0x25, 0x1f, 0xa1, 0x9f, 0x43, 0x65,
	0x89, 0x43, 0xdf, 0x9e, 0x05, 0xbc, 0x38, 0xde, 0x97, 0x8a, 0x63, 0x42, 0xc5, 0xa3, 0x11, 0x95,
	0xd3, 0x85, 0x7c, 0xe7, 0x10, 0xca, 0x8c, 0x44, 0x4b, 0x0d, 0xfd, 0x25, 0x94, 0xb3, 0x91, 0xf6,
	0x39, 0x34, 0x69, 0xf1, 0x63, 0x07, 0x3a, 0x0f, 0xd7, 0xd0, 0x8c, 0x8e, 0xe1, 0xd0, 0x9c, 0x8b,
	0x00, 0xce, 0x47, 0x01, 0xac, 0x3d, 0x84, 0x56, 0x62, 0x56, 0xe0, 0x49, 0x2f, 0xc5, 0xd8, 0xd1,
	0xcb, 0x5e, 0x8a, 0x69, 0x3f, 0x83, 0x3d, 0x1d, 0xcf, 0xdc, 0xe5, 0x12, 0x3b, 0xd6, 0xf7, 0xa9,
	0x8d, 0xa4, 0x06, 0x6e, 0x98, 0x18, 0x78, 0x47, 0x4b, 0x50, 0xbb, 0x96, 0x31, 0xb2, 0x67, 0xc6,
	0xc8, 0xb5, 0xf0, 0x86, 0xcf, 0xc9, 0x6a, 0x50, 0x39, 0x3b, 0xe9, 0x19, 0x4f, 0x57, 0x0e, 0x03,
	0x8f, 0x64, 0xd0, 0x3b, 0x53, 0xf3, 0xe8, 0x16, 0x20, 0xf2, 0xfb, 0x85, 0xe9, 0xe3, 0x77, 0xee,
	0xe2, 0x2d, 0x0e, 0x4e, 0xcc, 0x25, 0x56, 0x0b, 0x62, 0xc2, 0xd9, 0xb0, 0xab, 0x16, 0x51, 0x13,
	0x80, 0x0c, 0xfa, 0x66, 0x68, 0x3b, 0x73, 0xb5, 0x74, 0x34, 0x87, 0x96, 0xf4, 0x15, 0x0e, 0x5d,
	0xed, 0x2e, 0xec, 0xa7, 0x48, 0xc6, 0xc5, 0xb8, 0x3f, 0x78, 0x3a, 0x1c, 0x0f, 0xc8, 0xfa, 0xb7,
	0x61, 0x37, 0xcd, 0x26, 0x97, 0xff, 0x1c, 0xba, 0x03, 0xed, 0x34, 0xe3, 0xa9, 0x3e, 0x98, 0x3e,
	0x1b, 0x75, 0xc7, 0x6a, 0xfe, 0xe8, 0x63, 0xa8, 0x76, 0x7d, 0xcc, 0xbf, 0x8f, 0xab, 0x42, 0x69,
	0xb4, 0x0a, 0xec, 0x19, 0x03, 0xc3, 0xa2, 0x75, 0xa5, 0xe6, 0x8e, 0x9e, 0xc1, 0x9e, 0xb0, 0x46,
	0x8f, 0x00, 0x7f, 0xfb, 0x2d, 0xa6, 0x13, 0xea, 0xa0, 0x9c, 0x0f, 0x4e, 0x07, 0xe7, 0x83, 0xaf,
	0xcf, 0xd5, 0x1b, 0xa8, 0x01, 0xd5, 0xe9, 0xe9, 0xb0, 0x3f, 0x98, 0x3e, 0x9b, 0xbc, 0x60, 0x1d,
	0xe1, 0xa7, 0xa7, 0x93, 0x17, 0x6a, 0x81, 0xe8, 0x7d, 0x3e, 0xec, 0x0f, 0x26, 0x6a, 0xf1, 0xe8,
	0x01, 0x34, 0xbb, 0x56, 0x8f, 0x46, 0x11, 0xeb, 0x29, 0xc7, 0x3d, 0xe4, 0x1b, 0x51, 0x0f, 0x39,
	0x77, 0xfc, 0x1f, 0x2d, 0x50, 0x84, 0x1c, 0xea, 0x01, 0xc4, 0xdf, 0xc7, 0xa1, 0xf6, 0x96, 0xcf,
	0xe6, 0xde, 0x74, 0xf6, 0xb7, 0x7e, 0x50, 0xa7, 0xdd, 0x40, 0x9f, 0x43, 0x89, 0xcd, 0x97, 0x5b,
	0xef, 0xd1, 0xd4, 0xbd, 0x2c, 0x51, 0xcc, 0x9a, 0x66, 0x66, 0x4d, 0x37, 0xcd, 0x9a, 0x4a, 0xb3,
	0x7e, 0x09, 0xd5, 0xe8, 0x0b, 0x12, 0x74, 0x3b, 0xf3, 0xda, 0x88, 0x7d, 0xe3, 0xd2, 0x69, 0x6f,
	0x66, 0x50, 0x0d, 0x4f, 0xa1, 0x26, 0xbd, 0xe2, 0x44, 0xfb, 0xc9, 0xed, 0x49, 0x21, 0xdd, 0xe9,
	0x6c, 0x63, 0x09, 0x3d, 0xd3, 0x2d, 0x7a, 0xa6, 0xdb, 0xf5, 0x4c, 0x33, 0x7a, 0x2e, 0x40, 0x4d,
	0xbf, 0x6e, 0x44, 0xf7, 0xd2, 0xe6, 0x4e, 0x69, 0xbc, 0x7f, 0x2d, 0x9f, 0xaa, 0xd5, 0xa1, 0x95,
	0x7a, 0x23, 0x83, 0xee, 0x5e, 0xfb, 0x96, 0xad, 0x73, 0xef, 0x3a, 0xb6, 0x30, 0x7e, 0xd4, 0x53,
	0x4a, 0x18, 0x5f, 0xee, 0x44, 0x25, 0x8c, 0x9f, 0x68, 0x41, 0x09, 0xf7, 0x6d, 0xd2, 0x30, 0xdd,
	0xa6, 0x61, 0x9a, 0xd2, 0x70, 0x2a, 0x3e, 0x21, 0xe2, 0xed, 0x1a, 0x74, 0x90, 0xd9, 0x76, 0xdc,
	0x58, 0xea, 0xdc, 0xd9, 0xce, 0x14, 0xc6, 0xcf, 0x20, 0xfa, 0x7b, 0xd7, 0x41, 0xd5, 0x94, 0xf1,
	0x37, 0x81, 0x64, 0xed, 0x06, 0x9a, 0xf0, 0x22, 0x1c, 0x21, 0x50, 0x24, 0x6f, 0x24, 0x03, 0x6d,
	0x3b, 0x77, 0xaf, 0xe1, 0x52, 0x85, 0x5f, 0xc3, 0x4e, 0x06, 0x40, 0x22, 0x79, 0x23, 0x9b, 0xf0,
	0x6a, 0xe7, 0xf0, 0x7a, 0x81, 0xd8, 0x02, 0x49, 0xe4, 0x93, 0xb2, 0x40, 0x06, 0x9d, 0xa6, 0x2c,
	0xb0, 0x01, 0x36, 0x51, 0x37, 0x25, 0xce, 0x6c, 0x74, 0xb0, 0x61, 0x8e, 0x40, 0x54, 0x09, 0x37,
	0x65, 0x8f, 0x7a, 0x1a, 0x36, 0xd1, 0xb9, 0x98, 0x08, 0x1b, 0x19, 0x01, 0x24, 0xc2, 0x26, 0x71,
	0x8c, 0x46, 0xa1, 0xcb, 0xdb, 0x40, 0xa9, 0xd0, 0x8d, 0xba, 0x6b, 0xe9, 0xd0, 0x8d, 0x7b, 0x67,
	0x51, 0xe8, 0x6e, 0xd0, 0x30, 0xdd, 0xa6, 0x61, 0x9a, 0xd2, 0x30, 0x81, 0x66, 0xb2, 0xc7, 0x85,
	0xb2, 0xe1, 0x29, 0x75, 0xe2, 0x3a, 0x77, 0xaf, 0xe1, 0x0a, 0x23, 0x27, 0x1a, 0x23, 0x09, 0x23,
	0xa7, 0xdb, 0x2a, 0x09, 0x23, 0x67, 0xfa, 0x29, 0x22, 0xb3, 0xb6, 0x69, 0x9b, 0x5e, 0xa7, 0x6d,
	0xba, 0x41, 0x5b, 0x0f, 0x20, 0x6e, 0x07, 0xa0, 0xa4, 0x61, 0xa5, 0x46, 0x42, 0x67, 0x7f, 0x0b,
	0x47, 0xd4, 0x58, 0x09, 0x96, 0x24, 0x6a, 0x6c, 0x12, 0xe4, 0x24, 0x6a, 0x6c, 0x0a, 0xc9, 0xb0,
	0x62, 0x98, 0xfa, 0x2a, 0x36, 0x51, 0x0c, 0xb3, 0xdf, 0xd6, 0x26, 0x8a, 0xe1, 0x86, 0x0f, 0x6a,
	0xa3, 0xfa, 0x1f, 0x7d, 0xd4, 0x9c, 0xaa, 0xff, 0xd2, 0x87, 0xb3, 0xe9, 0xfa, 0x9f, 0xf8, 0x1a,
	0x96, 0xa6, 0x76, 0x06, 0x17, 0x25, 0x52, 0x7b, 0x13, 0xdc, 0x4a, 0xa4, 0xf6, 0x46, 0x58, 0xa5,
	0xdd, 0x78, 0xf2, 0xe9, 0x37, 0x3f, 0x99, 0xbb, 0x0b, 0xd3, 0x99, 0x3f, 0xfa, 0xe2, 0x38, 0x0c,
	0x1f, 0xcd, 0xdc, 0xe5, 0x63, 0xfa, 0x1f, 0x23, 0x33, 0x77, 0xf1, 0x38, 0xc0, 0xfe, 0x5b, 0x7b,
	0x86, 0x83, 0xf8, 0xbf, 0x49, 0x2e, 0xcb, 0x94, 0xf9, 0x7b, 0xff, 0x13, 0x00, 0x00, 0xff, 0xff,
	0x76, 0xa7, 0x30, 0x60, 0x6b, 0x32, 0x00, 0x00,
}
