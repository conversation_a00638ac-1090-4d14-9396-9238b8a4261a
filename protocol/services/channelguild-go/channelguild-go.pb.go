// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channelguild-go/channelguild-go.proto

package channelguild_go // import "golang.52tt.com/protocol/services/channelguild-go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type CreateGuildPubChannelReq struct {
	GuildIdList          []uint32 `protobuf:"varint,1,rep,packed,name=guild_id_list,json=guildIdList,proto3" json:"guild_id_list,omitempty"`
	Handler              string   `protobuf:"bytes,2,opt,name=handler,proto3" json:"handler,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGuildPubChannelReq) Reset()         { *m = CreateGuildPubChannelReq{} }
func (m *CreateGuildPubChannelReq) String() string { return proto.CompactTextString(m) }
func (*CreateGuildPubChannelReq) ProtoMessage()    {}
func (*CreateGuildPubChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{0}
}
func (m *CreateGuildPubChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGuildPubChannelReq.Unmarshal(m, b)
}
func (m *CreateGuildPubChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGuildPubChannelReq.Marshal(b, m, deterministic)
}
func (dst *CreateGuildPubChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGuildPubChannelReq.Merge(dst, src)
}
func (m *CreateGuildPubChannelReq) XXX_Size() int {
	return xxx_messageInfo_CreateGuildPubChannelReq.Size(m)
}
func (m *CreateGuildPubChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGuildPubChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGuildPubChannelReq proto.InternalMessageInfo

func (m *CreateGuildPubChannelReq) GetGuildIdList() []uint32 {
	if m != nil {
		return m.GuildIdList
	}
	return nil
}

func (m *CreateGuildPubChannelReq) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

type CreateGuildPubChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGuildPubChannelResp) Reset()         { *m = CreateGuildPubChannelResp{} }
func (m *CreateGuildPubChannelResp) String() string { return proto.CompactTextString(m) }
func (*CreateGuildPubChannelResp) ProtoMessage()    {}
func (*CreateGuildPubChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{1}
}
func (m *CreateGuildPubChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGuildPubChannelResp.Unmarshal(m, b)
}
func (m *CreateGuildPubChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGuildPubChannelResp.Marshal(b, m, deterministic)
}
func (dst *CreateGuildPubChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGuildPubChannelResp.Merge(dst, src)
}
func (m *CreateGuildPubChannelResp) XXX_Size() int {
	return xxx_messageInfo_CreateGuildPubChannelResp.Size(m)
}
func (m *CreateGuildPubChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGuildPubChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGuildPubChannelResp proto.InternalMessageInfo

type ListGuildPubChannelReq struct {
	GuildIdList          []uint32 `protobuf:"varint,1,rep,packed,name=guild_id_list,json=guildIdList,proto3" json:"guild_id_list,omitempty"`
	ChannelIdList        []uint32 `protobuf:"varint,2,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,4,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	ChannelViewIdList    []string `protobuf:"bytes,5,rep,name=channel_view_id_list,json=channelViewIdList,proto3" json:"channel_view_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListGuildPubChannelReq) Reset()         { *m = ListGuildPubChannelReq{} }
func (m *ListGuildPubChannelReq) String() string { return proto.CompactTextString(m) }
func (*ListGuildPubChannelReq) ProtoMessage()    {}
func (*ListGuildPubChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{2}
}
func (m *ListGuildPubChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListGuildPubChannelReq.Unmarshal(m, b)
}
func (m *ListGuildPubChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListGuildPubChannelReq.Marshal(b, m, deterministic)
}
func (dst *ListGuildPubChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListGuildPubChannelReq.Merge(dst, src)
}
func (m *ListGuildPubChannelReq) XXX_Size() int {
	return xxx_messageInfo_ListGuildPubChannelReq.Size(m)
}
func (m *ListGuildPubChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListGuildPubChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListGuildPubChannelReq proto.InternalMessageInfo

func (m *ListGuildPubChannelReq) GetGuildIdList() []uint32 {
	if m != nil {
		return m.GuildIdList
	}
	return nil
}

func (m *ListGuildPubChannelReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *ListGuildPubChannelReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ListGuildPubChannelReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *ListGuildPubChannelReq) GetChannelViewIdList() []string {
	if m != nil {
		return m.ChannelViewIdList
	}
	return nil
}

type ListChannelGuildResp struct {
	Total                uint32              `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	InfoList             []*ChannelGuildInfo `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ListChannelGuildResp) Reset()         { *m = ListChannelGuildResp{} }
func (m *ListChannelGuildResp) String() string { return proto.CompactTextString(m) }
func (*ListChannelGuildResp) ProtoMessage()    {}
func (*ListChannelGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{3}
}
func (m *ListChannelGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelGuildResp.Unmarshal(m, b)
}
func (m *ListChannelGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelGuildResp.Marshal(b, m, deterministic)
}
func (dst *ListChannelGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelGuildResp.Merge(dst, src)
}
func (m *ListChannelGuildResp) XXX_Size() int {
	return xxx_messageInfo_ListChannelGuildResp.Size(m)
}
func (m *ListChannelGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelGuildResp proto.InternalMessageInfo

func (m *ListChannelGuildResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ListChannelGuildResp) GetInfoList() []*ChannelGuildInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type ChannelGuildInfo struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildShortId         uint32   `protobuf:"varint,2,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id,omitempty"`
	GuildName            string   `protobuf:"bytes,3,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelDisplayId     uint32   `protobuf:"varint,5,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	CreateTime           uint32   `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Handler              string   `protobuf:"bytes,7,opt,name=handler,proto3" json:"handler,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,8,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelGuildInfo) Reset()         { *m = ChannelGuildInfo{} }
func (m *ChannelGuildInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelGuildInfo) ProtoMessage()    {}
func (*ChannelGuildInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{4}
}
func (m *ChannelGuildInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGuildInfo.Unmarshal(m, b)
}
func (m *ChannelGuildInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGuildInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelGuildInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGuildInfo.Merge(dst, src)
}
func (m *ChannelGuildInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelGuildInfo.Size(m)
}
func (m *ChannelGuildInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGuildInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGuildInfo proto.InternalMessageInfo

func (m *ChannelGuildInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ChannelGuildInfo) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

func (m *ChannelGuildInfo) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *ChannelGuildInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelGuildInfo) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *ChannelGuildInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ChannelGuildInfo) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

func (m *ChannelGuildInfo) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type BatGetChannelGuildListReq struct {
	GuildIds             []uint32 `protobuf:"varint,1,rep,packed,name=guild_ids,json=guildIds,proto3" json:"guild_ids,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetChannelGuildListReq) Reset()         { *m = BatGetChannelGuildListReq{} }
func (m *BatGetChannelGuildListReq) String() string { return proto.CompactTextString(m) }
func (*BatGetChannelGuildListReq) ProtoMessage()    {}
func (*BatGetChannelGuildListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{5}
}
func (m *BatGetChannelGuildListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetChannelGuildListReq.Unmarshal(m, b)
}
func (m *BatGetChannelGuildListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetChannelGuildListReq.Marshal(b, m, deterministic)
}
func (dst *BatGetChannelGuildListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetChannelGuildListReq.Merge(dst, src)
}
func (m *BatGetChannelGuildListReq) XXX_Size() int {
	return xxx_messageInfo_BatGetChannelGuildListReq.Size(m)
}
func (m *BatGetChannelGuildListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetChannelGuildListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetChannelGuildListReq proto.InternalMessageInfo

func (m *BatGetChannelGuildListReq) GetGuildIds() []uint32 {
	if m != nil {
		return m.GuildIds
	}
	return nil
}

func (m *BatGetChannelGuildListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type BatGetChannelGuildListEntry struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	TotalNum             uint32   `protobuf:"varint,2,opt,name=total_num,json=totalNum,proto3" json:"total_num,omitempty"`
	ChannelIds           []uint32 `protobuf:"varint,3,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetChannelGuildListEntry) Reset()         { *m = BatGetChannelGuildListEntry{} }
func (m *BatGetChannelGuildListEntry) String() string { return proto.CompactTextString(m) }
func (*BatGetChannelGuildListEntry) ProtoMessage()    {}
func (*BatGetChannelGuildListEntry) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{6}
}
func (m *BatGetChannelGuildListEntry) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetChannelGuildListEntry.Unmarshal(m, b)
}
func (m *BatGetChannelGuildListEntry) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetChannelGuildListEntry.Marshal(b, m, deterministic)
}
func (dst *BatGetChannelGuildListEntry) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetChannelGuildListEntry.Merge(dst, src)
}
func (m *BatGetChannelGuildListEntry) XXX_Size() int {
	return xxx_messageInfo_BatGetChannelGuildListEntry.Size(m)
}
func (m *BatGetChannelGuildListEntry) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetChannelGuildListEntry.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetChannelGuildListEntry proto.InternalMessageInfo

func (m *BatGetChannelGuildListEntry) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatGetChannelGuildListEntry) GetTotalNum() uint32 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

func (m *BatGetChannelGuildListEntry) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type BatGetChannelGuildListResp struct {
	ResultList           []*BatGetChannelGuildListEntry `protobuf:"bytes,1,rep,name=result_list,json=resultList,proto3" json:"result_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *BatGetChannelGuildListResp) Reset()         { *m = BatGetChannelGuildListResp{} }
func (m *BatGetChannelGuildListResp) String() string { return proto.CompactTextString(m) }
func (*BatGetChannelGuildListResp) ProtoMessage()    {}
func (*BatGetChannelGuildListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{7}
}
func (m *BatGetChannelGuildListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetChannelGuildListResp.Unmarshal(m, b)
}
func (m *BatGetChannelGuildListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetChannelGuildListResp.Marshal(b, m, deterministic)
}
func (dst *BatGetChannelGuildListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetChannelGuildListResp.Merge(dst, src)
}
func (m *BatGetChannelGuildListResp) XXX_Size() int {
	return xxx_messageInfo_BatGetChannelGuildListResp.Size(m)
}
func (m *BatGetChannelGuildListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetChannelGuildListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetChannelGuildListResp proto.InternalMessageInfo

func (m *BatGetChannelGuildListResp) GetResultList() []*BatGetChannelGuildListEntry {
	if m != nil {
		return m.ResultList
	}
	return nil
}

type AddChannelGuildReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelGuildReq) Reset()         { *m = AddChannelGuildReq{} }
func (m *AddChannelGuildReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelGuildReq) ProtoMessage()    {}
func (*AddChannelGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{8}
}
func (m *AddChannelGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelGuildReq.Unmarshal(m, b)
}
func (m *AddChannelGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelGuildReq.Marshal(b, m, deterministic)
}
func (dst *AddChannelGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelGuildReq.Merge(dst, src)
}
func (m *AddChannelGuildReq) XXX_Size() int {
	return xxx_messageInfo_AddChannelGuildReq.Size(m)
}
func (m *AddChannelGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelGuildReq proto.InternalMessageInfo

func (m *AddChannelGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AddChannelGuildReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddChannelGuildReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type AddChannelGuildResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelGuildResp) Reset()         { *m = AddChannelGuildResp{} }
func (m *AddChannelGuildResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelGuildResp) ProtoMessage()    {}
func (*AddChannelGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{9}
}
func (m *AddChannelGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelGuildResp.Unmarshal(m, b)
}
func (m *AddChannelGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelGuildResp.Marshal(b, m, deterministic)
}
func (dst *AddChannelGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelGuildResp.Merge(dst, src)
}
func (m *AddChannelGuildResp) XXX_Size() int {
	return xxx_messageInfo_AddChannelGuildResp.Size(m)
}
func (m *AddChannelGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelGuildResp proto.InternalMessageInfo

type DelChannelGuildReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelChannelGuildReq) Reset()         { *m = DelChannelGuildReq{} }
func (m *DelChannelGuildReq) String() string { return proto.CompactTextString(m) }
func (*DelChannelGuildReq) ProtoMessage()    {}
func (*DelChannelGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{10}
}
func (m *DelChannelGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelChannelGuildReq.Unmarshal(m, b)
}
func (m *DelChannelGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelChannelGuildReq.Marshal(b, m, deterministic)
}
func (dst *DelChannelGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelChannelGuildReq.Merge(dst, src)
}
func (m *DelChannelGuildReq) XXX_Size() int {
	return xxx_messageInfo_DelChannelGuildReq.Size(m)
}
func (m *DelChannelGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelChannelGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelChannelGuildReq proto.InternalMessageInfo

func (m *DelChannelGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DelChannelGuildReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type DelChannelGuildResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelChannelGuildResp) Reset()         { *m = DelChannelGuildResp{} }
func (m *DelChannelGuildResp) String() string { return proto.CompactTextString(m) }
func (*DelChannelGuildResp) ProtoMessage()    {}
func (*DelChannelGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{11}
}
func (m *DelChannelGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelChannelGuildResp.Unmarshal(m, b)
}
func (m *DelChannelGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelChannelGuildResp.Marshal(b, m, deterministic)
}
func (dst *DelChannelGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelChannelGuildResp.Merge(dst, src)
}
func (m *DelChannelGuildResp) XXX_Size() int {
	return xxx_messageInfo_DelChannelGuildResp.Size(m)
}
func (m *DelChannelGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelChannelGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelChannelGuildResp proto.InternalMessageInfo

type GetChannelGuildListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Size                 uint32   `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelGuildListReq) Reset()         { *m = GetChannelGuildListReq{} }
func (m *GetChannelGuildListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelGuildListReq) ProtoMessage()    {}
func (*GetChannelGuildListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{12}
}
func (m *GetChannelGuildListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelGuildListReq.Unmarshal(m, b)
}
func (m *GetChannelGuildListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelGuildListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelGuildListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelGuildListReq.Merge(dst, src)
}
func (m *GetChannelGuildListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelGuildListReq.Size(m)
}
func (m *GetChannelGuildListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelGuildListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelGuildListReq proto.InternalMessageInfo

func (m *GetChannelGuildListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetChannelGuildListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetChannelGuildListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

type GetChannelGuildListResp struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	Total                uint32   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelGuildListResp) Reset()         { *m = GetChannelGuildListResp{} }
func (m *GetChannelGuildListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelGuildListResp) ProtoMessage()    {}
func (*GetChannelGuildListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{13}
}
func (m *GetChannelGuildListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelGuildListResp.Unmarshal(m, b)
}
func (m *GetChannelGuildListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelGuildListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelGuildListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelGuildListResp.Merge(dst, src)
}
func (m *GetChannelGuildListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelGuildListResp.Size(m)
}
func (m *GetChannelGuildListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelGuildListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelGuildListResp proto.InternalMessageInfo

func (m *GetChannelGuildListResp) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

func (m *GetChannelGuildListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DismissChannelGuildReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DismissChannelGuildReq) Reset()         { *m = DismissChannelGuildReq{} }
func (m *DismissChannelGuildReq) String() string { return proto.CompactTextString(m) }
func (*DismissChannelGuildReq) ProtoMessage()    {}
func (*DismissChannelGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{14}
}
func (m *DismissChannelGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DismissChannelGuildReq.Unmarshal(m, b)
}
func (m *DismissChannelGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DismissChannelGuildReq.Marshal(b, m, deterministic)
}
func (dst *DismissChannelGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DismissChannelGuildReq.Merge(dst, src)
}
func (m *DismissChannelGuildReq) XXX_Size() int {
	return xxx_messageInfo_DismissChannelGuildReq.Size(m)
}
func (m *DismissChannelGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DismissChannelGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_DismissChannelGuildReq proto.InternalMessageInfo

func (m *DismissChannelGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type DismissChannelGuildResp struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DismissChannelGuildResp) Reset()         { *m = DismissChannelGuildResp{} }
func (m *DismissChannelGuildResp) String() string { return proto.CompactTextString(m) }
func (*DismissChannelGuildResp) ProtoMessage()    {}
func (*DismissChannelGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{15}
}
func (m *DismissChannelGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DismissChannelGuildResp.Unmarshal(m, b)
}
func (m *DismissChannelGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DismissChannelGuildResp.Marshal(b, m, deterministic)
}
func (dst *DismissChannelGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DismissChannelGuildResp.Merge(dst, src)
}
func (m *DismissChannelGuildResp) XXX_Size() int {
	return xxx_messageInfo_DismissChannelGuildResp.Size(m)
}
func (m *DismissChannelGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DismissChannelGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_DismissChannelGuildResp proto.InternalMessageInfo

func (m *DismissChannelGuildResp) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type AddGuildMemberChannelReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddGuildMemberChannelReq) Reset()         { *m = AddGuildMemberChannelReq{} }
func (m *AddGuildMemberChannelReq) String() string { return proto.CompactTextString(m) }
func (*AddGuildMemberChannelReq) ProtoMessage()    {}
func (*AddGuildMemberChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{16}
}
func (m *AddGuildMemberChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddGuildMemberChannelReq.Unmarshal(m, b)
}
func (m *AddGuildMemberChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddGuildMemberChannelReq.Marshal(b, m, deterministic)
}
func (dst *AddGuildMemberChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddGuildMemberChannelReq.Merge(dst, src)
}
func (m *AddGuildMemberChannelReq) XXX_Size() int {
	return xxx_messageInfo_AddGuildMemberChannelReq.Size(m)
}
func (m *AddGuildMemberChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddGuildMemberChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddGuildMemberChannelReq proto.InternalMessageInfo

func (m *AddGuildMemberChannelReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AddGuildMemberChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddGuildMemberChannelReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type AddGuildMemberChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddGuildMemberChannelResp) Reset()         { *m = AddGuildMemberChannelResp{} }
func (m *AddGuildMemberChannelResp) String() string { return proto.CompactTextString(m) }
func (*AddGuildMemberChannelResp) ProtoMessage()    {}
func (*AddGuildMemberChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{17}
}
func (m *AddGuildMemberChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddGuildMemberChannelResp.Unmarshal(m, b)
}
func (m *AddGuildMemberChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddGuildMemberChannelResp.Marshal(b, m, deterministic)
}
func (dst *AddGuildMemberChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddGuildMemberChannelResp.Merge(dst, src)
}
func (m *AddGuildMemberChannelResp) XXX_Size() int {
	return xxx_messageInfo_AddGuildMemberChannelResp.Size(m)
}
func (m *AddGuildMemberChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddGuildMemberChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddGuildMemberChannelResp proto.InternalMessageInfo

type DelGuildMemberChannelReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGuildMemberChannelReq) Reset()         { *m = DelGuildMemberChannelReq{} }
func (m *DelGuildMemberChannelReq) String() string { return proto.CompactTextString(m) }
func (*DelGuildMemberChannelReq) ProtoMessage()    {}
func (*DelGuildMemberChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{18}
}
func (m *DelGuildMemberChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGuildMemberChannelReq.Unmarshal(m, b)
}
func (m *DelGuildMemberChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGuildMemberChannelReq.Marshal(b, m, deterministic)
}
func (dst *DelGuildMemberChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGuildMemberChannelReq.Merge(dst, src)
}
func (m *DelGuildMemberChannelReq) XXX_Size() int {
	return xxx_messageInfo_DelGuildMemberChannelReq.Size(m)
}
func (m *DelGuildMemberChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGuildMemberChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelGuildMemberChannelReq proto.InternalMessageInfo

func (m *DelGuildMemberChannelReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DelGuildMemberChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DelGuildMemberChannelReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type DelGuildMemberChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGuildMemberChannelResp) Reset()         { *m = DelGuildMemberChannelResp{} }
func (m *DelGuildMemberChannelResp) String() string { return proto.CompactTextString(m) }
func (*DelGuildMemberChannelResp) ProtoMessage()    {}
func (*DelGuildMemberChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{19}
}
func (m *DelGuildMemberChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGuildMemberChannelResp.Unmarshal(m, b)
}
func (m *DelGuildMemberChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGuildMemberChannelResp.Marshal(b, m, deterministic)
}
func (dst *DelGuildMemberChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGuildMemberChannelResp.Merge(dst, src)
}
func (m *DelGuildMemberChannelResp) XXX_Size() int {
	return xxx_messageInfo_DelGuildMemberChannelResp.Size(m)
}
func (m *DelGuildMemberChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGuildMemberChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelGuildMemberChannelResp proto.InternalMessageInfo

type GuildMemberChannelInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildMemberChannelInfo) Reset()         { *m = GuildMemberChannelInfo{} }
func (m *GuildMemberChannelInfo) String() string { return proto.CompactTextString(m) }
func (*GuildMemberChannelInfo) ProtoMessage()    {}
func (*GuildMemberChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{20}
}
func (m *GuildMemberChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildMemberChannelInfo.Unmarshal(m, b)
}
func (m *GuildMemberChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildMemberChannelInfo.Marshal(b, m, deterministic)
}
func (dst *GuildMemberChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildMemberChannelInfo.Merge(dst, src)
}
func (m *GuildMemberChannelInfo) XXX_Size() int {
	return xxx_messageInfo_GuildMemberChannelInfo.Size(m)
}
func (m *GuildMemberChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildMemberChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildMemberChannelInfo proto.InternalMessageInfo

func (m *GuildMemberChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GuildMemberChannelInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type GetGuildMemberChannelReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	StartIndex           uint32   `protobuf:"varint,2,opt,name=start_index,json=startIndex,proto3" json:"start_index,omitempty"`
	Size                 uint32   `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildMemberChannelReq) Reset()         { *m = GetGuildMemberChannelReq{} }
func (m *GetGuildMemberChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildMemberChannelReq) ProtoMessage()    {}
func (*GetGuildMemberChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{21}
}
func (m *GetGuildMemberChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMemberChannelReq.Unmarshal(m, b)
}
func (m *GetGuildMemberChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMemberChannelReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildMemberChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMemberChannelReq.Merge(dst, src)
}
func (m *GetGuildMemberChannelReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildMemberChannelReq.Size(m)
}
func (m *GetGuildMemberChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMemberChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMemberChannelReq proto.InternalMessageInfo

func (m *GetGuildMemberChannelReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildMemberChannelReq) GetStartIndex() uint32 {
	if m != nil {
		return m.StartIndex
	}
	return 0
}

func (m *GetGuildMemberChannelReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

type GetGuildMemberChannelResp struct {
	ChannelList          []*GuildMemberChannelInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	Total                uint32                    `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetGuildMemberChannelResp) Reset()         { *m = GetGuildMemberChannelResp{} }
func (m *GetGuildMemberChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildMemberChannelResp) ProtoMessage()    {}
func (*GetGuildMemberChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{22}
}
func (m *GetGuildMemberChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMemberChannelResp.Unmarshal(m, b)
}
func (m *GetGuildMemberChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMemberChannelResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildMemberChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMemberChannelResp.Merge(dst, src)
}
func (m *GetGuildMemberChannelResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildMemberChannelResp.Size(m)
}
func (m *GetGuildMemberChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMemberChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMemberChannelResp proto.InternalMessageInfo

func (m *GetGuildMemberChannelResp) GetChannelList() []*GuildMemberChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetGuildMemberChannelResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type CheckGuildChannelCntLimitReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	GuildLevel           uint32   `protobuf:"varint,3,opt,name=guild_level,json=guildLevel,proto3" json:"guild_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckGuildChannelCntLimitReq) Reset()         { *m = CheckGuildChannelCntLimitReq{} }
func (m *CheckGuildChannelCntLimitReq) String() string { return proto.CompactTextString(m) }
func (*CheckGuildChannelCntLimitReq) ProtoMessage()    {}
func (*CheckGuildChannelCntLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{23}
}
func (m *CheckGuildChannelCntLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckGuildChannelCntLimitReq.Unmarshal(m, b)
}
func (m *CheckGuildChannelCntLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckGuildChannelCntLimitReq.Marshal(b, m, deterministic)
}
func (dst *CheckGuildChannelCntLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckGuildChannelCntLimitReq.Merge(dst, src)
}
func (m *CheckGuildChannelCntLimitReq) XXX_Size() int {
	return xxx_messageInfo_CheckGuildChannelCntLimitReq.Size(m)
}
func (m *CheckGuildChannelCntLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckGuildChannelCntLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckGuildChannelCntLimitReq proto.InternalMessageInfo

func (m *CheckGuildChannelCntLimitReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CheckGuildChannelCntLimitReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CheckGuildChannelCntLimitReq) GetGuildLevel() uint32 {
	if m != nil {
		return m.GuildLevel
	}
	return 0
}

type CheckGuildChannelCntLimitResp struct {
	CurrCount            uint32   `protobuf:"varint,1,opt,name=curr_count,json=currCount,proto3" json:"curr_count,omitempty"`
	RemainCount          uint32   `protobuf:"varint,2,opt,name=remain_count,json=remainCount,proto3" json:"remain_count,omitempty"`
	BanMsg               string   `protobuf:"bytes,3,opt,name=ban_msg,json=banMsg,proto3" json:"ban_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckGuildChannelCntLimitResp) Reset()         { *m = CheckGuildChannelCntLimitResp{} }
func (m *CheckGuildChannelCntLimitResp) String() string { return proto.CompactTextString(m) }
func (*CheckGuildChannelCntLimitResp) ProtoMessage()    {}
func (*CheckGuildChannelCntLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{24}
}
func (m *CheckGuildChannelCntLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckGuildChannelCntLimitResp.Unmarshal(m, b)
}
func (m *CheckGuildChannelCntLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckGuildChannelCntLimitResp.Marshal(b, m, deterministic)
}
func (dst *CheckGuildChannelCntLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckGuildChannelCntLimitResp.Merge(dst, src)
}
func (m *CheckGuildChannelCntLimitResp) XXX_Size() int {
	return xxx_messageInfo_CheckGuildChannelCntLimitResp.Size(m)
}
func (m *CheckGuildChannelCntLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckGuildChannelCntLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckGuildChannelCntLimitResp proto.InternalMessageInfo

func (m *CheckGuildChannelCntLimitResp) GetCurrCount() uint32 {
	if m != nil {
		return m.CurrCount
	}
	return 0
}

func (m *CheckGuildChannelCntLimitResp) GetRemainCount() uint32 {
	if m != nil {
		return m.RemainCount
	}
	return 0
}

func (m *CheckGuildChannelCntLimitResp) GetBanMsg() string {
	if m != nil {
		return m.BanMsg
	}
	return ""
}

type AddPartnerChannelGuildReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPartnerChannelGuildReq) Reset()         { *m = AddPartnerChannelGuildReq{} }
func (m *AddPartnerChannelGuildReq) String() string { return proto.CompactTextString(m) }
func (*AddPartnerChannelGuildReq) ProtoMessage()    {}
func (*AddPartnerChannelGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{25}
}
func (m *AddPartnerChannelGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPartnerChannelGuildReq.Unmarshal(m, b)
}
func (m *AddPartnerChannelGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPartnerChannelGuildReq.Marshal(b, m, deterministic)
}
func (dst *AddPartnerChannelGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPartnerChannelGuildReq.Merge(dst, src)
}
func (m *AddPartnerChannelGuildReq) XXX_Size() int {
	return xxx_messageInfo_AddPartnerChannelGuildReq.Size(m)
}
func (m *AddPartnerChannelGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPartnerChannelGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPartnerChannelGuildReq proto.InternalMessageInfo

func (m *AddPartnerChannelGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AddPartnerChannelGuildReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type AddPartnerChannelGuildResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPartnerChannelGuildResp) Reset()         { *m = AddPartnerChannelGuildResp{} }
func (m *AddPartnerChannelGuildResp) String() string { return proto.CompactTextString(m) }
func (*AddPartnerChannelGuildResp) ProtoMessage()    {}
func (*AddPartnerChannelGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{26}
}
func (m *AddPartnerChannelGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPartnerChannelGuildResp.Unmarshal(m, b)
}
func (m *AddPartnerChannelGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPartnerChannelGuildResp.Marshal(b, m, deterministic)
}
func (dst *AddPartnerChannelGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPartnerChannelGuildResp.Merge(dst, src)
}
func (m *AddPartnerChannelGuildResp) XXX_Size() int {
	return xxx_messageInfo_AddPartnerChannelGuildResp.Size(m)
}
func (m *AddPartnerChannelGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPartnerChannelGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPartnerChannelGuildResp proto.InternalMessageInfo

type GetPartnerChannelGuildReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPartnerChannelGuildReq) Reset()         { *m = GetPartnerChannelGuildReq{} }
func (m *GetPartnerChannelGuildReq) String() string { return proto.CompactTextString(m) }
func (*GetPartnerChannelGuildReq) ProtoMessage()    {}
func (*GetPartnerChannelGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{27}
}
func (m *GetPartnerChannelGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartnerChannelGuildReq.Unmarshal(m, b)
}
func (m *GetPartnerChannelGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartnerChannelGuildReq.Marshal(b, m, deterministic)
}
func (dst *GetPartnerChannelGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartnerChannelGuildReq.Merge(dst, src)
}
func (m *GetPartnerChannelGuildReq) XXX_Size() int {
	return xxx_messageInfo_GetPartnerChannelGuildReq.Size(m)
}
func (m *GetPartnerChannelGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartnerChannelGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartnerChannelGuildReq proto.InternalMessageInfo

func (m *GetPartnerChannelGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type PartnerChannelGuildInfo struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PartnerChannelGuildInfo) Reset()         { *m = PartnerChannelGuildInfo{} }
func (m *PartnerChannelGuildInfo) String() string { return proto.CompactTextString(m) }
func (*PartnerChannelGuildInfo) ProtoMessage()    {}
func (*PartnerChannelGuildInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{28}
}
func (m *PartnerChannelGuildInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PartnerChannelGuildInfo.Unmarshal(m, b)
}
func (m *PartnerChannelGuildInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PartnerChannelGuildInfo.Marshal(b, m, deterministic)
}
func (dst *PartnerChannelGuildInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PartnerChannelGuildInfo.Merge(dst, src)
}
func (m *PartnerChannelGuildInfo) XXX_Size() int {
	return xxx_messageInfo_PartnerChannelGuildInfo.Size(m)
}
func (m *PartnerChannelGuildInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PartnerChannelGuildInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PartnerChannelGuildInfo proto.InternalMessageInfo

func (m *PartnerChannelGuildInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *PartnerChannelGuildInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetPartnerChannelGuildResp struct {
	List                 []*PartnerChannelGuildInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetPartnerChannelGuildResp) Reset()         { *m = GetPartnerChannelGuildResp{} }
func (m *GetPartnerChannelGuildResp) String() string { return proto.CompactTextString(m) }
func (*GetPartnerChannelGuildResp) ProtoMessage()    {}
func (*GetPartnerChannelGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{29}
}
func (m *GetPartnerChannelGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartnerChannelGuildResp.Unmarshal(m, b)
}
func (m *GetPartnerChannelGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartnerChannelGuildResp.Marshal(b, m, deterministic)
}
func (dst *GetPartnerChannelGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartnerChannelGuildResp.Merge(dst, src)
}
func (m *GetPartnerChannelGuildResp) XXX_Size() int {
	return xxx_messageInfo_GetPartnerChannelGuildResp.Size(m)
}
func (m *GetPartnerChannelGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartnerChannelGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartnerChannelGuildResp proto.InternalMessageInfo

func (m *GetPartnerChannelGuildResp) GetList() []*PartnerChannelGuildInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type DelPartnerChannelGuildReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPartnerChannelGuildReq) Reset()         { *m = DelPartnerChannelGuildReq{} }
func (m *DelPartnerChannelGuildReq) String() string { return proto.CompactTextString(m) }
func (*DelPartnerChannelGuildReq) ProtoMessage()    {}
func (*DelPartnerChannelGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{30}
}
func (m *DelPartnerChannelGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPartnerChannelGuildReq.Unmarshal(m, b)
}
func (m *DelPartnerChannelGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPartnerChannelGuildReq.Marshal(b, m, deterministic)
}
func (dst *DelPartnerChannelGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPartnerChannelGuildReq.Merge(dst, src)
}
func (m *DelPartnerChannelGuildReq) XXX_Size() int {
	return xxx_messageInfo_DelPartnerChannelGuildReq.Size(m)
}
func (m *DelPartnerChannelGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPartnerChannelGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPartnerChannelGuildReq proto.InternalMessageInfo

func (m *DelPartnerChannelGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DelPartnerChannelGuildReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type DelPartnerChannelGuildResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPartnerChannelGuildResp) Reset()         { *m = DelPartnerChannelGuildResp{} }
func (m *DelPartnerChannelGuildResp) String() string { return proto.CompactTextString(m) }
func (*DelPartnerChannelGuildResp) ProtoMessage()    {}
func (*DelPartnerChannelGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelguild_go_296c98d20d6a0988, []int{31}
}
func (m *DelPartnerChannelGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPartnerChannelGuildResp.Unmarshal(m, b)
}
func (m *DelPartnerChannelGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPartnerChannelGuildResp.Marshal(b, m, deterministic)
}
func (dst *DelPartnerChannelGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPartnerChannelGuildResp.Merge(dst, src)
}
func (m *DelPartnerChannelGuildResp) XXX_Size() int {
	return xxx_messageInfo_DelPartnerChannelGuildResp.Size(m)
}
func (m *DelPartnerChannelGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPartnerChannelGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPartnerChannelGuildResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*CreateGuildPubChannelReq)(nil), "channelguild_go.CreateGuildPubChannelReq")
	proto.RegisterType((*CreateGuildPubChannelResp)(nil), "channelguild_go.CreateGuildPubChannelResp")
	proto.RegisterType((*ListGuildPubChannelReq)(nil), "channelguild_go.ListGuildPubChannelReq")
	proto.RegisterType((*ListChannelGuildResp)(nil), "channelguild_go.ListChannelGuildResp")
	proto.RegisterType((*ChannelGuildInfo)(nil), "channelguild_go.ChannelGuildInfo")
	proto.RegisterType((*BatGetChannelGuildListReq)(nil), "channelguild_go.BatGetChannelGuildListReq")
	proto.RegisterType((*BatGetChannelGuildListEntry)(nil), "channelguild_go.BatGetChannelGuildListEntry")
	proto.RegisterType((*BatGetChannelGuildListResp)(nil), "channelguild_go.BatGetChannelGuildListResp")
	proto.RegisterType((*AddChannelGuildReq)(nil), "channelguild_go.AddChannelGuildReq")
	proto.RegisterType((*AddChannelGuildResp)(nil), "channelguild_go.AddChannelGuildResp")
	proto.RegisterType((*DelChannelGuildReq)(nil), "channelguild_go.DelChannelGuildReq")
	proto.RegisterType((*DelChannelGuildResp)(nil), "channelguild_go.DelChannelGuildResp")
	proto.RegisterType((*GetChannelGuildListReq)(nil), "channelguild_go.GetChannelGuildListReq")
	proto.RegisterType((*GetChannelGuildListResp)(nil), "channelguild_go.GetChannelGuildListResp")
	proto.RegisterType((*DismissChannelGuildReq)(nil), "channelguild_go.DismissChannelGuildReq")
	proto.RegisterType((*DismissChannelGuildResp)(nil), "channelguild_go.DismissChannelGuildResp")
	proto.RegisterType((*AddGuildMemberChannelReq)(nil), "channelguild_go.AddGuildMemberChannelReq")
	proto.RegisterType((*AddGuildMemberChannelResp)(nil), "channelguild_go.AddGuildMemberChannelResp")
	proto.RegisterType((*DelGuildMemberChannelReq)(nil), "channelguild_go.DelGuildMemberChannelReq")
	proto.RegisterType((*DelGuildMemberChannelResp)(nil), "channelguild_go.DelGuildMemberChannelResp")
	proto.RegisterType((*GuildMemberChannelInfo)(nil), "channelguild_go.GuildMemberChannelInfo")
	proto.RegisterType((*GetGuildMemberChannelReq)(nil), "channelguild_go.GetGuildMemberChannelReq")
	proto.RegisterType((*GetGuildMemberChannelResp)(nil), "channelguild_go.GetGuildMemberChannelResp")
	proto.RegisterType((*CheckGuildChannelCntLimitReq)(nil), "channelguild_go.CheckGuildChannelCntLimitReq")
	proto.RegisterType((*CheckGuildChannelCntLimitResp)(nil), "channelguild_go.CheckGuildChannelCntLimitResp")
	proto.RegisterType((*AddPartnerChannelGuildReq)(nil), "channelguild_go.AddPartnerChannelGuildReq")
	proto.RegisterType((*AddPartnerChannelGuildResp)(nil), "channelguild_go.AddPartnerChannelGuildResp")
	proto.RegisterType((*GetPartnerChannelGuildReq)(nil), "channelguild_go.GetPartnerChannelGuildReq")
	proto.RegisterType((*PartnerChannelGuildInfo)(nil), "channelguild_go.PartnerChannelGuildInfo")
	proto.RegisterType((*GetPartnerChannelGuildResp)(nil), "channelguild_go.GetPartnerChannelGuildResp")
	proto.RegisterType((*DelPartnerChannelGuildReq)(nil), "channelguild_go.DelPartnerChannelGuildReq")
	proto.RegisterType((*DelPartnerChannelGuildResp)(nil), "channelguild_go.DelPartnerChannelGuildResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelGuildClient is the client API for ChannelGuild service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelGuildClient interface {
	CreateGuildPubChannel(ctx context.Context, in *CreateGuildPubChannelReq, opts ...grpc.CallOption) (*CreateGuildPubChannelResp, error)
	DismissChannelGuild(ctx context.Context, in *DismissChannelGuildReq, opts ...grpc.CallOption) (*DismissChannelGuildResp, error)
	AddChannelGuild(ctx context.Context, in *AddChannelGuildReq, opts ...grpc.CallOption) (*AddChannelGuildResp, error)
	DelChannelGuild(ctx context.Context, in *DelChannelGuildReq, opts ...grpc.CallOption) (*DelChannelGuildResp, error)
	GetChannelGuildList(ctx context.Context, in *GetChannelGuildListReq, opts ...grpc.CallOption) (*GetChannelGuildListResp, error)
	BatGetChannelGuildList(ctx context.Context, in *BatGetChannelGuildListReq, opts ...grpc.CallOption) (*BatGetChannelGuildListResp, error)
	ListGuildPubChannel(ctx context.Context, in *ListGuildPubChannelReq, opts ...grpc.CallOption) (*ListChannelGuildResp, error)
	AddGuildMemberChannel(ctx context.Context, in *AddGuildMemberChannelReq, opts ...grpc.CallOption) (*AddGuildMemberChannelResp, error)
	DelGuildMemberChannel(ctx context.Context, in *DelGuildMemberChannelReq, opts ...grpc.CallOption) (*DelGuildMemberChannelResp, error)
	AddPartnerChannelGuild(ctx context.Context, in *AddPartnerChannelGuildReq, opts ...grpc.CallOption) (*AddPartnerChannelGuildResp, error)
	GetPartnerChannelGuild(ctx context.Context, in *GetPartnerChannelGuildReq, opts ...grpc.CallOption) (*GetPartnerChannelGuildResp, error)
	DelPartnerChannelGuild(ctx context.Context, in *DelPartnerChannelGuildReq, opts ...grpc.CallOption) (*DelPartnerChannelGuildResp, error)
}

type channelGuildClient struct {
	cc *grpc.ClientConn
}

func NewChannelGuildClient(cc *grpc.ClientConn) ChannelGuildClient {
	return &channelGuildClient{cc}
}

func (c *channelGuildClient) CreateGuildPubChannel(ctx context.Context, in *CreateGuildPubChannelReq, opts ...grpc.CallOption) (*CreateGuildPubChannelResp, error) {
	out := new(CreateGuildPubChannelResp)
	err := c.cc.Invoke(ctx, "/channelguild_go.ChannelGuild/CreateGuildPubChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuildClient) DismissChannelGuild(ctx context.Context, in *DismissChannelGuildReq, opts ...grpc.CallOption) (*DismissChannelGuildResp, error) {
	out := new(DismissChannelGuildResp)
	err := c.cc.Invoke(ctx, "/channelguild_go.ChannelGuild/DismissChannelGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuildClient) AddChannelGuild(ctx context.Context, in *AddChannelGuildReq, opts ...grpc.CallOption) (*AddChannelGuildResp, error) {
	out := new(AddChannelGuildResp)
	err := c.cc.Invoke(ctx, "/channelguild_go.ChannelGuild/AddChannelGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuildClient) DelChannelGuild(ctx context.Context, in *DelChannelGuildReq, opts ...grpc.CallOption) (*DelChannelGuildResp, error) {
	out := new(DelChannelGuildResp)
	err := c.cc.Invoke(ctx, "/channelguild_go.ChannelGuild/DelChannelGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuildClient) GetChannelGuildList(ctx context.Context, in *GetChannelGuildListReq, opts ...grpc.CallOption) (*GetChannelGuildListResp, error) {
	out := new(GetChannelGuildListResp)
	err := c.cc.Invoke(ctx, "/channelguild_go.ChannelGuild/GetChannelGuildList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuildClient) BatGetChannelGuildList(ctx context.Context, in *BatGetChannelGuildListReq, opts ...grpc.CallOption) (*BatGetChannelGuildListResp, error) {
	out := new(BatGetChannelGuildListResp)
	err := c.cc.Invoke(ctx, "/channelguild_go.ChannelGuild/BatGetChannelGuildList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuildClient) ListGuildPubChannel(ctx context.Context, in *ListGuildPubChannelReq, opts ...grpc.CallOption) (*ListChannelGuildResp, error) {
	out := new(ListChannelGuildResp)
	err := c.cc.Invoke(ctx, "/channelguild_go.ChannelGuild/ListGuildPubChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuildClient) AddGuildMemberChannel(ctx context.Context, in *AddGuildMemberChannelReq, opts ...grpc.CallOption) (*AddGuildMemberChannelResp, error) {
	out := new(AddGuildMemberChannelResp)
	err := c.cc.Invoke(ctx, "/channelguild_go.ChannelGuild/AddGuildMemberChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuildClient) DelGuildMemberChannel(ctx context.Context, in *DelGuildMemberChannelReq, opts ...grpc.CallOption) (*DelGuildMemberChannelResp, error) {
	out := new(DelGuildMemberChannelResp)
	err := c.cc.Invoke(ctx, "/channelguild_go.ChannelGuild/DelGuildMemberChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuildClient) AddPartnerChannelGuild(ctx context.Context, in *AddPartnerChannelGuildReq, opts ...grpc.CallOption) (*AddPartnerChannelGuildResp, error) {
	out := new(AddPartnerChannelGuildResp)
	err := c.cc.Invoke(ctx, "/channelguild_go.ChannelGuild/AddPartnerChannelGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuildClient) GetPartnerChannelGuild(ctx context.Context, in *GetPartnerChannelGuildReq, opts ...grpc.CallOption) (*GetPartnerChannelGuildResp, error) {
	out := new(GetPartnerChannelGuildResp)
	err := c.cc.Invoke(ctx, "/channelguild_go.ChannelGuild/GetPartnerChannelGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuildClient) DelPartnerChannelGuild(ctx context.Context, in *DelPartnerChannelGuildReq, opts ...grpc.CallOption) (*DelPartnerChannelGuildResp, error) {
	out := new(DelPartnerChannelGuildResp)
	err := c.cc.Invoke(ctx, "/channelguild_go.ChannelGuild/DelPartnerChannelGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelGuildServer is the server API for ChannelGuild service.
type ChannelGuildServer interface {
	CreateGuildPubChannel(context.Context, *CreateGuildPubChannelReq) (*CreateGuildPubChannelResp, error)
	DismissChannelGuild(context.Context, *DismissChannelGuildReq) (*DismissChannelGuildResp, error)
	AddChannelGuild(context.Context, *AddChannelGuildReq) (*AddChannelGuildResp, error)
	DelChannelGuild(context.Context, *DelChannelGuildReq) (*DelChannelGuildResp, error)
	GetChannelGuildList(context.Context, *GetChannelGuildListReq) (*GetChannelGuildListResp, error)
	BatGetChannelGuildList(context.Context, *BatGetChannelGuildListReq) (*BatGetChannelGuildListResp, error)
	ListGuildPubChannel(context.Context, *ListGuildPubChannelReq) (*ListChannelGuildResp, error)
	AddGuildMemberChannel(context.Context, *AddGuildMemberChannelReq) (*AddGuildMemberChannelResp, error)
	DelGuildMemberChannel(context.Context, *DelGuildMemberChannelReq) (*DelGuildMemberChannelResp, error)
	AddPartnerChannelGuild(context.Context, *AddPartnerChannelGuildReq) (*AddPartnerChannelGuildResp, error)
	GetPartnerChannelGuild(context.Context, *GetPartnerChannelGuildReq) (*GetPartnerChannelGuildResp, error)
	DelPartnerChannelGuild(context.Context, *DelPartnerChannelGuildReq) (*DelPartnerChannelGuildResp, error)
}

func RegisterChannelGuildServer(s *grpc.Server, srv ChannelGuildServer) {
	s.RegisterService(&_ChannelGuild_serviceDesc, srv)
}

func _ChannelGuild_CreateGuildPubChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGuildPubChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildServer).CreateGuildPubChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelguild_go.ChannelGuild/CreateGuildPubChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildServer).CreateGuildPubChannel(ctx, req.(*CreateGuildPubChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuild_DismissChannelGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DismissChannelGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildServer).DismissChannelGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelguild_go.ChannelGuild/DismissChannelGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildServer).DismissChannelGuild(ctx, req.(*DismissChannelGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuild_AddChannelGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildServer).AddChannelGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelguild_go.ChannelGuild/AddChannelGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildServer).AddChannelGuild(ctx, req.(*AddChannelGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuild_DelChannelGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelChannelGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildServer).DelChannelGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelguild_go.ChannelGuild/DelChannelGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildServer).DelChannelGuild(ctx, req.(*DelChannelGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuild_GetChannelGuildList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelGuildListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildServer).GetChannelGuildList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelguild_go.ChannelGuild/GetChannelGuildList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildServer).GetChannelGuildList(ctx, req.(*GetChannelGuildListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuild_BatGetChannelGuildList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetChannelGuildListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildServer).BatGetChannelGuildList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelguild_go.ChannelGuild/BatGetChannelGuildList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildServer).BatGetChannelGuildList(ctx, req.(*BatGetChannelGuildListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuild_ListGuildPubChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGuildPubChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildServer).ListGuildPubChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelguild_go.ChannelGuild/ListGuildPubChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildServer).ListGuildPubChannel(ctx, req.(*ListGuildPubChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuild_AddGuildMemberChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddGuildMemberChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildServer).AddGuildMemberChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelguild_go.ChannelGuild/AddGuildMemberChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildServer).AddGuildMemberChannel(ctx, req.(*AddGuildMemberChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuild_DelGuildMemberChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelGuildMemberChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildServer).DelGuildMemberChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelguild_go.ChannelGuild/DelGuildMemberChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildServer).DelGuildMemberChannel(ctx, req.(*DelGuildMemberChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuild_AddPartnerChannelGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPartnerChannelGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildServer).AddPartnerChannelGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelguild_go.ChannelGuild/AddPartnerChannelGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildServer).AddPartnerChannelGuild(ctx, req.(*AddPartnerChannelGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuild_GetPartnerChannelGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPartnerChannelGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildServer).GetPartnerChannelGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelguild_go.ChannelGuild/GetPartnerChannelGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildServer).GetPartnerChannelGuild(ctx, req.(*GetPartnerChannelGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuild_DelPartnerChannelGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPartnerChannelGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildServer).DelPartnerChannelGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelguild_go.ChannelGuild/DelPartnerChannelGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildServer).DelPartnerChannelGuild(ctx, req.(*DelPartnerChannelGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelGuild_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channelguild_go.ChannelGuild",
	HandlerType: (*ChannelGuildServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateGuildPubChannel",
			Handler:    _ChannelGuild_CreateGuildPubChannel_Handler,
		},
		{
			MethodName: "DismissChannelGuild",
			Handler:    _ChannelGuild_DismissChannelGuild_Handler,
		},
		{
			MethodName: "AddChannelGuild",
			Handler:    _ChannelGuild_AddChannelGuild_Handler,
		},
		{
			MethodName: "DelChannelGuild",
			Handler:    _ChannelGuild_DelChannelGuild_Handler,
		},
		{
			MethodName: "GetChannelGuildList",
			Handler:    _ChannelGuild_GetChannelGuildList_Handler,
		},
		{
			MethodName: "BatGetChannelGuildList",
			Handler:    _ChannelGuild_BatGetChannelGuildList_Handler,
		},
		{
			MethodName: "ListGuildPubChannel",
			Handler:    _ChannelGuild_ListGuildPubChannel_Handler,
		},
		{
			MethodName: "AddGuildMemberChannel",
			Handler:    _ChannelGuild_AddGuildMemberChannel_Handler,
		},
		{
			MethodName: "DelGuildMemberChannel",
			Handler:    _ChannelGuild_DelGuildMemberChannel_Handler,
		},
		{
			MethodName: "AddPartnerChannelGuild",
			Handler:    _ChannelGuild_AddPartnerChannelGuild_Handler,
		},
		{
			MethodName: "GetPartnerChannelGuild",
			Handler:    _ChannelGuild_GetPartnerChannelGuild_Handler,
		},
		{
			MethodName: "DelPartnerChannelGuild",
			Handler:    _ChannelGuild_DelPartnerChannelGuild_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channelguild-go/channelguild-go.proto",
}

func init() {
	proto.RegisterFile("channelguild-go/channelguild-go.proto", fileDescriptor_channelguild_go_296c98d20d6a0988)
}

var fileDescriptor_channelguild_go_296c98d20d6a0988 = []byte{
	// 1112 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x58, 0x6d, 0x6f, 0x1b, 0x45,
	0x10, 0x8e, 0xed, 0x24, 0xb6, 0xc7, 0x31, 0x2d, 0x9b, 0xc4, 0xb9, 0x38, 0xad, 0x92, 0x2e, 0x2d,
	0x98, 0x52, 0x1c, 0x91, 0x08, 0x3e, 0x20, 0x84, 0xd4, 0xda, 0x28, 0x32, 0x4a, 0xa2, 0x70, 0x05,
	0x84, 0x8a, 0x84, 0x75, 0xf6, 0x6d, 0x9d, 0x6b, 0xef, 0xad, 0xb7, 0xe7, 0x94, 0x54, 0xe2, 0xcf,
	0xf0, 0x2f, 0xf8, 0xce, 0x0f, 0x43, 0xbb, 0xb7, 0xe7, 0x9c, 0x7d, 0xb3, 0x8e, 0x1b, 0x85, 0x7e,
	0xb2, 0x6f, 0x76, 0x76, 0x9e, 0xdd, 0x67, 0x9e, 0x9b, 0x19, 0x1b, 0x1e, 0x0d, 0xcf, 0x2d, 0xdf,
	0x67, 0xee, 0x68, 0xec, 0xb8, 0xf6, 0x97, 0xa3, 0x60, 0x7f, 0xe6, 0xb9, 0x1d, 0x46, 0x41, 0x1c,
	0x90, 0x3b, 0x59, 0x73, 0x7f, 0x14, 0xd0, 0xdf, 0xc0, 0xe8, 0x44, 0xcc, 0x8a, 0xd9, 0x91, 0xb0,
	0x9c, 0x8d, 0x07, 0x9d, 0xc4, 0xc1, 0x64, 0x6f, 0x08, 0x85, 0x7a, 0xe2, 0xe7, 0xd8, 0x7d, 0xd7,
	0xe1, 0xb1, 0x51, 0xd8, 0x2b, 0xb5, 0xea, 0x66, 0x4d, 0x1a, 0x7b, 0xf6, 0xb1, 0xc3, 0x63, 0x62,
	0x40, 0xf9, 0xdc, 0xf2, 0x6d, 0x97, 0x45, 0x46, 0x71, 0xaf, 0xd0, 0xaa, 0x9a, 0xe9, 0x23, 0xdd,
	0x81, 0x6d, 0x4d, 0x64, 0x1e, 0xd2, 0x7f, 0x0b, 0xd0, 0x10, 0xfb, 0x6f, 0x88, 0xfa, 0x29, 0xa4,
	0x17, 0x99, 0x78, 0x15, 0xa5, 0x57, 0x5d, 0x99, 0x95, 0x1f, 0x81, 0xe5, 0xd0, 0x1a, 0x31, 0xa3,
	0xb4, 0x57, 0x68, 0xd5, 0x4d, 0xf9, 0x9d, 0x6c, 0x43, 0x45, 0x7c, 0xf6, 0xfd, 0xb1, 0x67, 0x2c,
	0x4b, 0x7b, 0x59, 0x3c, 0x9f, 0x8e, 0x3d, 0xb2, 0x0f, 0x1b, 0x69, 0xd8, 0x0b, 0x87, 0xbd, 0x9d,
	0xc4, 0x5e, 0xd9, 0x2b, 0xb5, 0xaa, 0xe6, 0xc7, 0x6a, 0xed, 0x57, 0x87, 0xbd, 0x4d, 0xe2, 0x53,
	0x17, 0x36, 0xc4, 0xa7, 0x3a, 0xbd, 0xbc, 0x8c, 0xb8, 0x1e, 0xd9, 0x80, 0x95, 0x38, 0x88, 0x2d,
	0xd7, 0x28, 0x48, 0x80, 0xe4, 0x81, 0x7c, 0x0f, 0x55, 0xc7, 0x7f, 0x19, 0x5c, 0x9d, 0xb7, 0x76,
	0xf0, 0xa0, 0x3d, 0x93, 0x90, 0x76, 0x36, 0x56, 0xcf, 0x7f, 0x19, 0x98, 0x15, 0xb1, 0x47, 0xa2,
	0xfd, 0x5d, 0x84, 0xbb, 0xb3, 0xcb, 0xe2, 0x3a, 0x29, 0x5d, 0x0a, 0xad, 0xac, 0x98, 0x22, 0x0f,
	0xe1, 0xa3, 0x64, 0x89, 0x9f, 0x07, 0x51, 0x2c, 0x1c, 0x8a, 0xd2, 0x61, 0x4d, 0x5a, 0x9f, 0x0b,
	0x63, 0xcf, 0x26, 0xf7, 0x01, 0x12, 0x2f, 0xdf, 0xf2, 0x12, 0xa6, 0xaa, 0x66, 0x55, 0x5a, 0x4e,
	0x2d, 0x8f, 0x89, 0xe5, 0x2b, 0xaa, 0x15, 0x61, 0xd5, 0x09, 0xcb, 0xe4, 0x09, 0x90, 0x74, 0xd9,
	0x76, 0x78, 0xe8, 0x5a, 0x97, 0xc2, 0x6d, 0x45, 0xba, 0xdd, 0x55, 0x2b, 0xdd, 0x64, 0xa1, 0x67,
	0x93, 0x5d, 0xa8, 0x0d, 0xa5, 0x26, 0xfa, 0xb1, 0xe3, 0x31, 0x63, 0x55, 0xba, 0x41, 0x62, 0xfa,
	0xd9, 0xf1, 0x58, 0x56, 0x4e, 0xe5, 0x29, 0x39, 0x65, 0x53, 0xae, 0x72, 0x63, 0x54, 0xa4, 0x47,
	0x7d, 0x2a, 0x2d, 0xf4, 0x18, 0xb6, 0x9f, 0x59, 0xf1, 0x11, 0x9b, 0x4a, 0x8a, 0xa0, 0x4f, 0x68,
	0x6b, 0x07, 0xaa, 0x29, 0x59, 0x5c, 0xe9, 0xaa, 0xa2, 0xd8, 0xe2, 0x42, 0x2c, 0xf1, 0x65, 0xc8,
	0x14, 0x49, 0xf2, 0x3b, 0xbd, 0x80, 0x1d, 0x3c, 0xda, 0x0f, 0x7e, 0x1c, 0x5d, 0xce, 0x23, 0x7f,
	0x07, 0xaa, 0x32, 0xeb, 0x52, 0x67, 0x49, 0xc8, 0x8a, 0x34, 0x08, 0xa1, 0x09, 0x1e, 0x26, 0xa4,
	0x72, 0xa3, 0x24, 0x4f, 0x02, 0x13, 0x56, 0x39, 0x7d, 0x0d, 0x4d, 0xdd, 0x2d, 0x78, 0x48, 0x4e,
	0xa0, 0x16, 0x31, 0x3e, 0x76, 0xe3, 0xab, 0x17, 0xa4, 0x76, 0xf0, 0x24, 0x27, 0xa5, 0x39, 0x27,
	0x37, 0x21, 0x09, 0x20, 0x75, 0x35, 0x00, 0xf2, 0xd4, 0xb6, 0xa7, 0x45, 0xfc, 0x66, 0xde, 0xdd,
	0xa6, 0x35, 0x51, 0x9c, 0xd5, 0x44, 0x4a, 0x64, 0x29, 0x43, 0xe4, 0x26, 0xac, 0xe7, 0x30, 0x78,
	0x48, 0x4f, 0x81, 0x74, 0x99, 0x7b, 0x6b, 0xd0, 0x02, 0x26, 0x17, 0x8f, 0x87, 0xf4, 0x77, 0x68,
	0x68, 0x14, 0x31, 0x07, 0x0a, 0xd1, 0x83, 0xb0, 0x71, 0xe7, 0xdd, 0xe4, 0x6a, 0xe2, 0x3b, 0x3d,
	0x83, 0x2d, 0x5d, 0xa2, 0x66, 0xf2, 0x5c, 0x98, 0xcd, 0xf3, 0x55, 0xa1, 0x28, 0x66, 0x0a, 0x05,
	0x3d, 0x84, 0x46, 0xd7, 0xe1, 0x9e, 0xc3, 0xf9, 0xe2, 0xcc, 0xd0, 0x6f, 0x61, 0x0b, 0xdd, 0xb4,
	0xc0, 0x31, 0xa8, 0x0f, 0xc6, 0x53, 0xdb, 0x96, 0x1b, 0x4e, 0x98, 0x37, 0x60, 0x51, 0xa6, 0x1e,
	0xdf, 0x5c, 0x07, 0x06, 0x94, 0xad, 0xe1, 0x30, 0x18, 0xfb, 0xb1, 0x2a, 0x2b, 0xe9, 0xa3, 0xe8,
	0x0d, 0x1a, 0x3c, 0x1e, 0x8a, 0xc3, 0x74, 0xd5, 0xe9, 0x3f, 0xd4, 0x61, 0x34, 0x78, 0x3c, 0xa4,
	0x3f, 0x41, 0x23, 0xbf, 0x22, 0x0b, 0xef, 0x34, 0x5e, 0x61, 0x71, 0xbc, 0x57, 0x60, 0x1c, 0xb1,
	0xf8, 0xbd, 0xef, 0xb7, 0x0b, 0x35, 0x1e, 0x5b, 0xa2, 0x8e, 0xfb, 0x36, 0xfb, 0x53, 0x5d, 0x10,
	0xa4, 0xa9, 0x27, 0x2c, 0xa8, 0x36, 0xff, 0x82, 0x6d, 0x0d, 0x16, 0x0f, 0xc9, 0x8f, 0xb0, 0x96,
	0xde, 0x20, 0x53, 0x47, 0x3e, 0xcb, 0xd5, 0x11, 0x9c, 0x00, 0x33, 0xd5, 0x94, 0xec, 0xb4, 0xb8,
	0x90, 0x7d, 0xb8, 0xd7, 0x39, 0x67, 0xc3, 0xd7, 0x32, 0x82, 0xda, 0xdb, 0xf1, 0xe3, 0x63, 0xc7,
	0x73, 0x6e, 0xf2, 0xf6, 0xed, 0x42, 0x32, 0x05, 0xf4, 0x5d, 0x76, 0xc1, 0x5c, 0x75, 0xd1, 0xa4,
	0x7b, 0x1d, 0x0b, 0x0b, 0x7d, 0x07, 0xf7, 0xe7, 0xe0, 0xf1, 0x50, 0x26, 0x6d, 0x1c, 0x45, 0xfd,
	0x24, 0x31, 0x69, 0xd2, 0xc6, 0x51, 0xd4, 0x11, 0x06, 0xf2, 0x00, 0xd6, 0x22, 0xe6, 0x59, 0x8e,
	0xaf, 0x1c, 0x12, 0xf0, 0x5a, 0x62, 0x4b, 0x5c, 0xb6, 0xa0, 0x3c, 0xb0, 0xfc, 0xbe, 0xc7, 0x47,
	0x2a, 0xaf, 0xab, 0x03, 0xcb, 0x3f, 0xe1, 0x23, 0xfa, 0x8b, 0xd4, 0xf4, 0x99, 0x15, 0xc5, 0xfe,
	0x84, 0xa7, 0x5b, 0xa8, 0x68, 0xf7, 0xa0, 0xa9, 0x0b, 0xcb, 0x43, 0xfa, 0x8d, 0xcc, 0xef, 0x7b,
	0x83, 0xd2, 0xe7, 0xb0, 0x85, 0x6c, 0xba, 0x6e, 0xa0, 0xb8, 0xe6, 0xa8, 0x2f, 0xa0, 0xa9, 0x3b,
	0x0c, 0x0f, 0xc9, 0x77, 0xb0, 0x9c, 0x51, 0x59, 0x2b, 0xa7, 0x32, 0xcd, 0x79, 0x4c, 0xb9, 0x4b,
	0xb0, 0xdb, 0x65, 0xee, 0xff, 0xc1, 0xae, 0x2e, 0x2c, 0x0f, 0x0f, 0xfe, 0x01, 0x58, 0xcb, 0x1a,
	0x49, 0x08, 0x9b, 0xe8, 0x4c, 0x4b, 0x3e, 0xcf, 0xcf, 0x71, 0x9a, 0xa9, 0xba, 0xf9, 0x78, 0x51,
	0x57, 0x1e, 0xd2, 0x25, 0xf2, 0x0a, 0xd6, 0x91, 0xaa, 0x4e, 0xf2, 0x2f, 0x29, 0xde, 0x30, 0x9a,
	0xad, 0xc5, 0x1c, 0x25, 0xd6, 0x1f, 0x70, 0x67, 0xa6, 0x47, 0x93, 0x4f, 0x72, 0xdb, 0xf3, 0x93,
	0x42, 0xf3, 0xe1, 0xf5, 0x4e, 0x69, 0xfc, 0x99, 0xe6, 0x8c, 0xc4, 0xcf, 0x8f, 0x03, 0x48, 0x7c,
	0xac, 0xc7, 0x4b, 0xae, 0x90, 0x46, 0x8c, 0x70, 0x85, 0xcf, 0x02, 0x08, 0x57, 0x9a, 0xbe, 0x4e,
	0x97, 0x08, 0x87, 0x06, 0x3e, 0x5e, 0x91, 0xc7, 0x0b, 0xce, 0x61, 0x02, 0xf1, 0x8b, 0x85, 0x7d,
	0x25, 0xe8, 0x08, 0xd6, 0x91, 0x1f, 0x4d, 0xc8, 0x05, 0xf1, 0x9f, 0x56, 0xcd, 0x47, 0xa8, 0x23,
	0xc2, 0x64, 0x08, 0x9b, 0x68, 0x7f, 0x46, 0x74, 0xae, 0x9b, 0x1b, 0x10, 0x9d, 0xeb, 0x5b, 0xbe,
	0x44, 0x44, 0x9b, 0x30, 0x82, 0xa8, 0x1b, 0x0e, 0x10, 0x44, 0x7d, 0x5f, 0x97, 0x19, 0xc4, 0x0b,
	0x2b, 0x41, 0x4f, 0x8e, 0x97, 0x1e, 0x24, 0x83, 0x73, 0xaa, 0xb5, 0x04, 0xc5, 0x4b, 0x24, 0x02,
	0xaa, 0x2d, 0xec, 0x08, 0xa8, 0xbe, 0xee, 0x26, 0xa0, 0x78, 0x91, 0x23, 0x28, 0x63, 0x0b, 0x83,
	0xea, 0x2b, 0x27, 0x5d, 0x7a, 0x76, 0xf8, 0xe2, 0xab, 0x51, 0xe0, 0x5a, 0xfe, 0xa8, 0xfd, 0xf5,
	0x41, 0x1c, 0xb7, 0x87, 0x81, 0xb7, 0x2f, 0xff, 0x82, 0x18, 0x06, 0xee, 0x3e, 0x67, 0xd1, 0x85,
	0x33, 0x64, 0x7c, 0xf6, 0x4f, 0x8a, 0xc1, 0xaa, 0x74, 0x39, 0xfc, 0x2f, 0x00, 0x00, 0xff, 0xff,
	0xcf, 0x2c, 0xf4, 0x94, 0xce, 0x10, 0x00, 0x00,
}
