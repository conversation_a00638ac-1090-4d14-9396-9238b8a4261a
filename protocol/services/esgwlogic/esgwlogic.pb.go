// Code generated by protoc-gen-gogo.
// source: services/esgwlogic/esgwlogic.proto
// DO NOT EDIT!

/*
	Package esgwlogic is a generated protocol buffer package.

	It is generated from these files:
		services/esgwlogic/esgwlogic.proto

	It has these top-level messages:
		IndexData
*/
package esgwlogic

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type IndexType int32

const (
	IndexType_CHANNEL_INDEX IndexType = 0
	IndexType_GUILD_INDEX   IndexType = 1
)

var IndexType_name = map[int32]string{
	0: "CHANNEL_INDEX",
	1: "GUILD_INDEX",
}
var IndexType_value = map[string]int32{
	"CHANNEL_INDEX": 0,
	"GUILD_INDEX":   1,
}

func (x IndexType) Enum() *IndexType {
	p := new(IndexType)
	*p = x
	return p
}
func (x IndexType) String() string {
	return proto.EnumName(IndexType_name, int32(x))
}
func (x *IndexType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(IndexType_value, data, "IndexType")
	if err != nil {
		return err
	}
	*x = IndexType(value)
	return nil
}
func (IndexType) EnumDescriptor() ([]byte, []int) { return fileDescriptorEsgwlogic, []int{0} }

type IndexOperType int32

const (
	IndexOperType_ADD_SEARCH_INDEX     IndexOperType = 0
	IndexOperType_DEL_SEARCH_INDEX     IndexOperType = 1
	IndexOperType_MOD_SEARCH_INDEX     IndexOperType = 2
	IndexOperType_UNSUPPORT_OPER_INDEX IndexOperType = 255
)

var IndexOperType_name = map[int32]string{
	0:   "ADD_SEARCH_INDEX",
	1:   "DEL_SEARCH_INDEX",
	2:   "MOD_SEARCH_INDEX",
	255: "UNSUPPORT_OPER_INDEX",
}
var IndexOperType_value = map[string]int32{
	"ADD_SEARCH_INDEX":     0,
	"DEL_SEARCH_INDEX":     1,
	"MOD_SEARCH_INDEX":     2,
	"UNSUPPORT_OPER_INDEX": 255,
}

func (x IndexOperType) Enum() *IndexOperType {
	p := new(IndexOperType)
	*p = x
	return p
}
func (x IndexOperType) String() string {
	return proto.EnumName(IndexOperType_name, int32(x))
}
func (x *IndexOperType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(IndexOperType_value, data, "IndexOperType")
	if err != nil {
		return err
	}
	*x = IndexOperType(value)
	return nil
}
func (IndexOperType) EnumDescriptor() ([]byte, []int) { return fileDescriptorEsgwlogic, []int{1} }

type IndexData struct {
	IndexType    uint32 `protobuf:"varint,1,req,name=index_type,json=indexType" json:"index_type"`
	OperType     uint32 `protobuf:"varint,2,req,name=oper_type,json=operType" json:"oper_type"`
	Id           uint32 `protobuf:"varint,3,req,name=id" json:"id"`
	Name         string `protobuf:"bytes,4,opt,name=name" json:"name"`
	RemainMember uint32 `protobuf:"varint,5,opt,name=remain_member,json=remainMember" json:"remain_member"`
}

func (m *IndexData) Reset()                    { *m = IndexData{} }
func (m *IndexData) String() string            { return proto.CompactTextString(m) }
func (*IndexData) ProtoMessage()               {}
func (*IndexData) Descriptor() ([]byte, []int) { return fileDescriptorEsgwlogic, []int{0} }

func (m *IndexData) GetIndexType() uint32 {
	if m != nil {
		return m.IndexType
	}
	return 0
}

func (m *IndexData) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *IndexData) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *IndexData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *IndexData) GetRemainMember() uint32 {
	if m != nil {
		return m.RemainMember
	}
	return 0
}

func init() {
	proto.RegisterType((*IndexData)(nil), "esgwlogic.IndexData")
	proto.RegisterEnum("esgwlogic.IndexType", IndexType_name, IndexType_value)
	proto.RegisterEnum("esgwlogic.IndexOperType", IndexOperType_name, IndexOperType_value)
}
func (m *IndexData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IndexData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintEsgwlogic(dAtA, i, uint64(m.IndexType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintEsgwlogic(dAtA, i, uint64(m.OperType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintEsgwlogic(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x22
	i++
	i = encodeVarintEsgwlogic(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x28
	i++
	i = encodeVarintEsgwlogic(dAtA, i, uint64(m.RemainMember))
	return i, nil
}

func encodeFixed64Esgwlogic(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Esgwlogic(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintEsgwlogic(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *IndexData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovEsgwlogic(uint64(m.IndexType))
	n += 1 + sovEsgwlogic(uint64(m.OperType))
	n += 1 + sovEsgwlogic(uint64(m.Id))
	l = len(m.Name)
	n += 1 + l + sovEsgwlogic(uint64(l))
	n += 1 + sovEsgwlogic(uint64(m.RemainMember))
	return n
}

func sovEsgwlogic(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozEsgwlogic(x uint64) (n int) {
	return sovEsgwlogic(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *IndexData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEsgwlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IndexData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IndexData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IndexType", wireType)
			}
			m.IndexType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEsgwlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IndexType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEsgwlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEsgwlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEsgwlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEsgwlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainMember", wireType)
			}
			m.RemainMember = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEsgwlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainMember |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipEsgwlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEsgwlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("index_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("oper_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipEsgwlogic(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowEsgwlogic
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowEsgwlogic
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowEsgwlogic
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthEsgwlogic
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowEsgwlogic
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipEsgwlogic(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthEsgwlogic = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowEsgwlogic   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("services/esgwlogic/esgwlogic.proto", fileDescriptorEsgwlogic) }

var fileDescriptorEsgwlogic = []byte{
	// 344 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x52, 0x2a, 0x4e, 0x2d, 0x2a,
	0xcb, 0x4c, 0x4e, 0x2d, 0xd6, 0x4f, 0x2d, 0x4e, 0x2f, 0xcf, 0xc9, 0x4f, 0xcf, 0x4c, 0x46, 0xb0,
	0xf4, 0x0a, 0x8a, 0xf2, 0x4b, 0xf2, 0x85, 0x38, 0xe1, 0x02, 0x52, 0x2a, 0xc9, 0xf9, 0xb9, 0xb9,
	0xf9, 0x79, 0xfa, 0x25, 0x39, 0x65, 0x05, 0x99, 0xc9, 0xd9, 0x39, 0xa9, 0xfa, 0xc5, 0xd9, 0x49,
	0xa5, 0x99, 0x39, 0x25, 0x99, 0x79, 0x25, 0x95, 0x05, 0xa9, 0x10, 0x0d, 0x4a, 0xeb, 0x18, 0xb9,
	0x38, 0x3d, 0xf3, 0x52, 0x52, 0x2b, 0x5c, 0x12, 0x4b, 0x12, 0x85, 0x94, 0xb9, 0xb8, 0x32, 0x41,
	0x9c, 0x78, 0x90, 0x0a, 0x09, 0x46, 0x05, 0x26, 0x0d, 0x5e, 0x27, 0x96, 0x13, 0xf7, 0xe4, 0x19,
	0x82, 0x38, 0xc1, 0xe2, 0x21, 0x95, 0x05, 0xa9, 0x42, 0x8a, 0x5c, 0x9c, 0xf9, 0x05, 0xa9, 0x45,
	0x10, 0x35, 0x4c, 0x48, 0x6a, 0x38, 0x40, 0xc2, 0x60, 0x25, 0x22, 0x5c, 0x4c, 0x99, 0x29, 0x12,
	0xcc, 0x48, 0x72, 0x4c, 0x99, 0x29, 0x42, 0x12, 0x5c, 0x2c, 0x79, 0x89, 0xb9, 0xa9, 0x12, 0x2c,
	0x0a, 0x8c, 0x1a, 0x9c, 0x50, 0x71, 0xb0, 0x88, 0x90, 0x26, 0x17, 0x6f, 0x51, 0x6a, 0x6e, 0x62,
	0x66, 0x5e, 0x7c, 0x6e, 0x6a, 0x6e, 0x52, 0x6a, 0x91, 0x04, 0xab, 0x02, 0x23, 0x5c, 0x2b, 0x0f,
	0x44, 0xca, 0x17, 0x2c, 0xa3, 0xa5, 0x0f, 0x75, 0x2f, 0xd8, 0x1e, 0x41, 0x2e, 0x5e, 0x67, 0x0f,
	0x47, 0x3f, 0x3f, 0x57, 0x9f, 0x78, 0x4f, 0x3f, 0x17, 0xd7, 0x08, 0x01, 0x06, 0x21, 0x7e, 0x2e,
	0x6e, 0xf7, 0x50, 0x4f, 0x1f, 0x17, 0xa8, 0x00, 0xa3, 0x56, 0x0e, 0x17, 0x2f, 0x58, 0x83, 0x3f,
	0xc2, 0x71, 0x02, 0x8e, 0x2e, 0x2e, 0xf1, 0xc1, 0xae, 0x8e, 0x41, 0xce, 0x1e, 0x70, 0x7d, 0x22,
	0x5c, 0x02, 0x2e, 0xae, 0x3e, 0xa8, 0xa2, 0x8c, 0x20, 0x51, 0x5f, 0x7f, 0x34, 0xb5, 0x4c, 0x42,
	0x92, 0x5c, 0x22, 0xa1, 0x7e, 0xc1, 0xa1, 0x01, 0x01, 0xfe, 0x41, 0x21, 0xf1, 0xfe, 0x01, 0xae,
	0x41, 0x50, 0x99, 0xff, 0x8c, 0x46, 0xc2, 0x5c, 0x9c, 0xae, 0xc5, 0xe9, 0xe5, 0x3e, 0xe0, 0x28,
	0x60, 0xeb, 0x58, 0xf2, 0x82, 0xb9, 0xa1, 0xca, 0x49, 0xe0, 0xc4, 0x23, 0x39, 0xc6, 0x0b, 0x8f,
	0xe4, 0x18, 0x1f, 0x3c, 0x92, 0x63, 0x9c, 0xf0, 0x58, 0x8e, 0x01, 0x10, 0x00, 0x00, 0xff, 0xff,
	0xd5, 0x0e, 0x45, 0x7b, 0xcc, 0x01, 0x00, 0x00,
}
