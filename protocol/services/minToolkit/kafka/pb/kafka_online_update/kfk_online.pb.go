// Code generated by protoc-gen-gogo.
// source: src/minToolkit/kafka/pb/kafka_online_update/kfk_online.proto
// DO NOT EDIT!

/*
	Package kafkaonline is a generated protocol buffer package.

	It is generated from these files:
		src/minToolkit/kafka/pb/kafka_online_update/kfk_online.proto

	It has these top-level messages:
		FrinedUpdate
		GroupUpdate
		GroupUpdateEventCreateGroup
		GroupUpdateEventDismissGroup
		GroupUpdateEventVerifyChange
		GroupUpdateEventMemberChange
		GroupUpdateEventRoleChange
		OnlineUpdate
		OnlineUpdateInfo
*/
package kafkaonline

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"

import github_com_gogo_protobuf_proto "github.com/gogo/protobuf/proto"

import io "io"
import fmt1 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type GroupUpdateEventType int32

const (
	GroupUpdateEventType_GroupUpdateLegacy       GroupUpdateEventType = 0
	GroupUpdateEventType_GroupUpdateCreateGroup  GroupUpdateEventType = 1
	GroupUpdateEventType_GroupUpdateDismissGroup GroupUpdateEventType = 2
	GroupUpdateEventType_GroupUpdateVerifyChange GroupUpdateEventType = 3
	GroupUpdateEventType_GroupUpdateMemberChange GroupUpdateEventType = 4
	GroupUpdateEventType_GroupUpdateRoleChange   GroupUpdateEventType = 5
)

var GroupUpdateEventType_name = map[int32]string{
	0: "GroupUpdateLegacy",
	1: "GroupUpdateCreateGroup",
	2: "GroupUpdateDismissGroup",
	3: "GroupUpdateVerifyChange",
	4: "GroupUpdateMemberChange",
	5: "GroupUpdateRoleChange",
}
var GroupUpdateEventType_value = map[string]int32{
	"GroupUpdateLegacy":       0,
	"GroupUpdateCreateGroup":  1,
	"GroupUpdateDismissGroup": 2,
	"GroupUpdateVerifyChange": 3,
	"GroupUpdateMemberChange": 4,
	"GroupUpdateRoleChange":   5,
}

func (x GroupUpdateEventType) Enum() *GroupUpdateEventType {
	p := new(GroupUpdateEventType)
	*p = x
	return p
}
func (x GroupUpdateEventType) String() string {
	return proto.EnumName(GroupUpdateEventType_name, int32(x))
}
func (x *GroupUpdateEventType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GroupUpdateEventType_value, data, "GroupUpdateEventType")
	if err != nil {
		return err
	}
	*x = GroupUpdateEventType(value)
	return nil
}
func (GroupUpdateEventType) EnumDescriptor() ([]byte, []int) { return fileDescriptorKfkOnline, []int{0} }

type FrinedUpdate struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	TargetUid uint32 `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
	IsAdd     bool   `protobuf:"varint,3,req,name=is_add,json=isAdd" json:"is_add"`
	Sequence  uint64 `protobuf:"varint,4,opt,name=sequence" json:"sequence"`
	SrcType   uint32 `protobuf:"varint,5,opt,name=src_type,json=srcType" json:"src_type"`
	EvTs      uint32 `protobuf:"varint,6,opt,name=ev_ts,json=evTs" json:"ev_ts"`
}

func (m *FrinedUpdate) Reset()                    { *m = FrinedUpdate{} }
func (m *FrinedUpdate) String() string            { return proto.CompactTextString(m) }
func (*FrinedUpdate) ProtoMessage()               {}
func (*FrinedUpdate) Descriptor() ([]byte, []int) { return fileDescriptorKfkOnline, []int{0} }

func (m *FrinedUpdate) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FrinedUpdate) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *FrinedUpdate) GetIsAdd() bool {
	if m != nil {
		return m.IsAdd
	}
	return false
}

func (m *FrinedUpdate) GetSequence() uint64 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *FrinedUpdate) GetSrcType() uint32 {
	if m != nil {
		return m.SrcType
	}
	return 0
}

func (m *FrinedUpdate) GetEvTs() uint32 {
	if m != nil {
		return m.EvTs
	}
	return 0
}

// 为了兼容以前的时间，当event-type = 0 的时候，就是以前的老事件的数据
// 新事件的内容是在 event_payload 内定义
type GroupUpdate struct {
	GroupId         uint32 `protobuf:"varint,1,req,name=group_id,json=groupId" json:"group_id"`
	TargetUid       uint32 `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
	IsAdd           bool   `protobuf:"varint,3,req,name=is_add,json=isAdd" json:"is_add"`
	IsDismiss       bool   `protobuf:"varint,4,opt,name=is_dismiss,json=isDismiss" json:"is_dismiss"`
	GroupType       uint32 `protobuf:"varint,5,opt,name=group_type,json=groupType" json:"group_type"`
	Role            uint32 `protobuf:"varint,6,opt,name=role" json:"role"`
	EvTs            uint32 `protobuf:"varint,7,opt,name=ev_ts,json=evTs" json:"ev_ts"`
	GroupCreateFrom string `protobuf:"bytes,8,opt,name=group_create_from,json=groupCreateFrom" json:"group_create_from"`
	EventType       uint32 `protobuf:"varint,9,opt,name=event_type,json=eventType" json:"event_type"`
	// Types that are valid to be assigned to EventPayload:
	//	*GroupUpdate_CreateGroup
	//	*GroupUpdate_DismissGroup
	//	*GroupUpdate_VerifyChange
	//	*GroupUpdate_MemberChange
	//	*GroupUpdate_RoleChange
	EventPayload isGroupUpdate_EventPayload `protobuf_oneof:"event_payload"`
}

func (m *GroupUpdate) Reset()                    { *m = GroupUpdate{} }
func (m *GroupUpdate) String() string            { return proto.CompactTextString(m) }
func (*GroupUpdate) ProtoMessage()               {}
func (*GroupUpdate) Descriptor() ([]byte, []int) { return fileDescriptorKfkOnline, []int{1} }

type isGroupUpdate_EventPayload interface {
	isGroupUpdate_EventPayload()
	MarshalTo([]byte) (int, error)
	Size() int
}

type GroupUpdate_CreateGroup struct {
	CreateGroup *GroupUpdateEventCreateGroup `protobuf:"bytes,10,opt,name=create_group,json=createGroup,oneof"`
}
type GroupUpdate_DismissGroup struct {
	DismissGroup *GroupUpdateEventDismissGroup `protobuf:"bytes,11,opt,name=dismiss_group,json=dismissGroup,oneof"`
}
type GroupUpdate_VerifyChange struct {
	VerifyChange *GroupUpdateEventVerifyChange `protobuf:"bytes,12,opt,name=verify_change,json=verifyChange,oneof"`
}
type GroupUpdate_MemberChange struct {
	MemberChange *GroupUpdateEventMemberChange `protobuf:"bytes,13,opt,name=member_change,json=memberChange,oneof"`
}
type GroupUpdate_RoleChange struct {
	RoleChange *GroupUpdateEventRoleChange `protobuf:"bytes,14,opt,name=role_change,json=roleChange,oneof"`
}

func (*GroupUpdate_CreateGroup) isGroupUpdate_EventPayload()  {}
func (*GroupUpdate_DismissGroup) isGroupUpdate_EventPayload() {}
func (*GroupUpdate_VerifyChange) isGroupUpdate_EventPayload() {}
func (*GroupUpdate_MemberChange) isGroupUpdate_EventPayload() {}
func (*GroupUpdate_RoleChange) isGroupUpdate_EventPayload()   {}

func (m *GroupUpdate) GetEventPayload() isGroupUpdate_EventPayload {
	if m != nil {
		return m.EventPayload
	}
	return nil
}

func (m *GroupUpdate) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupUpdate) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GroupUpdate) GetIsAdd() bool {
	if m != nil {
		return m.IsAdd
	}
	return false
}

func (m *GroupUpdate) GetIsDismiss() bool {
	if m != nil {
		return m.IsDismiss
	}
	return false
}

func (m *GroupUpdate) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *GroupUpdate) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

func (m *GroupUpdate) GetEvTs() uint32 {
	if m != nil {
		return m.EvTs
	}
	return 0
}

func (m *GroupUpdate) GetGroupCreateFrom() string {
	if m != nil {
		return m.GroupCreateFrom
	}
	return ""
}

func (m *GroupUpdate) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

func (m *GroupUpdate) GetCreateGroup() *GroupUpdateEventCreateGroup {
	if x, ok := m.GetEventPayload().(*GroupUpdate_CreateGroup); ok {
		return x.CreateGroup
	}
	return nil
}

func (m *GroupUpdate) GetDismissGroup() *GroupUpdateEventDismissGroup {
	if x, ok := m.GetEventPayload().(*GroupUpdate_DismissGroup); ok {
		return x.DismissGroup
	}
	return nil
}

func (m *GroupUpdate) GetVerifyChange() *GroupUpdateEventVerifyChange {
	if x, ok := m.GetEventPayload().(*GroupUpdate_VerifyChange); ok {
		return x.VerifyChange
	}
	return nil
}

func (m *GroupUpdate) GetMemberChange() *GroupUpdateEventMemberChange {
	if x, ok := m.GetEventPayload().(*GroupUpdate_MemberChange); ok {
		return x.MemberChange
	}
	return nil
}

func (m *GroupUpdate) GetRoleChange() *GroupUpdateEventRoleChange {
	if x, ok := m.GetEventPayload().(*GroupUpdate_RoleChange); ok {
		return x.RoleChange
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*GroupUpdate) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _GroupUpdate_OneofMarshaler, _GroupUpdate_OneofUnmarshaler, _GroupUpdate_OneofSizer, []interface{}{
		(*GroupUpdate_CreateGroup)(nil),
		(*GroupUpdate_DismissGroup)(nil),
		(*GroupUpdate_VerifyChange)(nil),
		(*GroupUpdate_MemberChange)(nil),
		(*GroupUpdate_RoleChange)(nil),
	}
}

func _GroupUpdate_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*GroupUpdate)
	// event_payload
	switch x := m.EventPayload.(type) {
	case *GroupUpdate_CreateGroup:
		_ = b.EncodeVarint(10<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.CreateGroup); err != nil {
			return err
		}
	case *GroupUpdate_DismissGroup:
		_ = b.EncodeVarint(11<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.DismissGroup); err != nil {
			return err
		}
	case *GroupUpdate_VerifyChange:
		_ = b.EncodeVarint(12<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.VerifyChange); err != nil {
			return err
		}
	case *GroupUpdate_MemberChange:
		_ = b.EncodeVarint(13<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.MemberChange); err != nil {
			return err
		}
	case *GroupUpdate_RoleChange:
		_ = b.EncodeVarint(14<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.RoleChange); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("GroupUpdate.EventPayload has unexpected type %T", x)
	}
	return nil
}

func _GroupUpdate_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*GroupUpdate)
	switch tag {
	case 10: // event_payload.create_group
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GroupUpdateEventCreateGroup)
		err := b.DecodeMessage(msg)
		m.EventPayload = &GroupUpdate_CreateGroup{msg}
		return true, err
	case 11: // event_payload.dismiss_group
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GroupUpdateEventDismissGroup)
		err := b.DecodeMessage(msg)
		m.EventPayload = &GroupUpdate_DismissGroup{msg}
		return true, err
	case 12: // event_payload.verify_change
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GroupUpdateEventVerifyChange)
		err := b.DecodeMessage(msg)
		m.EventPayload = &GroupUpdate_VerifyChange{msg}
		return true, err
	case 13: // event_payload.member_change
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GroupUpdateEventMemberChange)
		err := b.DecodeMessage(msg)
		m.EventPayload = &GroupUpdate_MemberChange{msg}
		return true, err
	case 14: // event_payload.role_change
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GroupUpdateEventRoleChange)
		err := b.DecodeMessage(msg)
		m.EventPayload = &GroupUpdate_RoleChange{msg}
		return true, err
	default:
		return false, nil
	}
}

func _GroupUpdate_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*GroupUpdate)
	// event_payload
	switch x := m.EventPayload.(type) {
	case *GroupUpdate_CreateGroup:
		s := proto.Size(x.CreateGroup)
		n += proto.SizeVarint(10<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *GroupUpdate_DismissGroup:
		s := proto.Size(x.DismissGroup)
		n += proto.SizeVarint(11<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *GroupUpdate_VerifyChange:
		s := proto.Size(x.VerifyChange)
		n += proto.SizeVarint(12<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *GroupUpdate_MemberChange:
		s := proto.Size(x.MemberChange)
		n += proto.SizeVarint(13<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *GroupUpdate_RoleChange:
		s := proto.Size(x.RoleChange)
		n += proto.SizeVarint(14<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type GroupUpdateEventCreateGroup struct {
	Creator uint64 `protobuf:"varint,1,opt,name=creator" json:"creator"`
}

func (m *GroupUpdateEventCreateGroup) Reset()         { *m = GroupUpdateEventCreateGroup{} }
func (m *GroupUpdateEventCreateGroup) String() string { return proto.CompactTextString(m) }
func (*GroupUpdateEventCreateGroup) ProtoMessage()    {}
func (*GroupUpdateEventCreateGroup) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkOnline, []int{2}
}

func (m *GroupUpdateEventCreateGroup) GetCreator() uint64 {
	if m != nil {
		return m.Creator
	}
	return 0
}

type GroupUpdateEventDismissGroup struct {
	Operator uint64 `protobuf:"varint,1,opt,name=operator" json:"operator"`
}

func (m *GroupUpdateEventDismissGroup) Reset()         { *m = GroupUpdateEventDismissGroup{} }
func (m *GroupUpdateEventDismissGroup) String() string { return proto.CompactTextString(m) }
func (*GroupUpdateEventDismissGroup) ProtoMessage()    {}
func (*GroupUpdateEventDismissGroup) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkOnline, []int{3}
}

func (m *GroupUpdateEventDismissGroup) GetOperator() uint64 {
	if m != nil {
		return m.Operator
	}
	return 0
}

type GroupUpdateEventVerifyChange struct {
	Operator      uint64 `protobuf:"varint,1,opt,name=operator" json:"operator"`
	CurrentStatus uint32 `protobuf:"varint,2,opt,name=current_status,json=currentStatus" json:"current_status"`
}

func (m *GroupUpdateEventVerifyChange) Reset()         { *m = GroupUpdateEventVerifyChange{} }
func (m *GroupUpdateEventVerifyChange) String() string { return proto.CompactTextString(m) }
func (*GroupUpdateEventVerifyChange) ProtoMessage()    {}
func (*GroupUpdateEventVerifyChange) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkOnline, []int{4}
}

func (m *GroupUpdateEventVerifyChange) GetOperator() uint64 {
	if m != nil {
		return m.Operator
	}
	return 0
}

func (m *GroupUpdateEventVerifyChange) GetCurrentStatus() uint32 {
	if m != nil {
		return m.CurrentStatus
	}
	return 0
}

type GroupUpdateEventMemberChange struct {
	Member             uint64 `protobuf:"varint,1,opt,name=member" json:"member"`
	Role               uint32 `protobuf:"varint,2,opt,name=role" json:"role"`
	IsJoin             bool   `protobuf:"varint,3,opt,name=is_join,json=isJoin" json:"is_join"`
	CurrentMemberCount uint32 `protobuf:"varint,4,opt,name=current_member_count,json=currentMemberCount" json:"current_member_count"`
	MaxMemberCount     uint32 `protobuf:"varint,5,opt,name=max_member_count,json=maxMemberCount" json:"max_member_count"`
	Operator           uint64 `protobuf:"varint,6,opt,name=operator" json:"operator"`
}

func (m *GroupUpdateEventMemberChange) Reset()         { *m = GroupUpdateEventMemberChange{} }
func (m *GroupUpdateEventMemberChange) String() string { return proto.CompactTextString(m) }
func (*GroupUpdateEventMemberChange) ProtoMessage()    {}
func (*GroupUpdateEventMemberChange) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkOnline, []int{5}
}

func (m *GroupUpdateEventMemberChange) GetMember() uint64 {
	if m != nil {
		return m.Member
	}
	return 0
}

func (m *GroupUpdateEventMemberChange) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

func (m *GroupUpdateEventMemberChange) GetIsJoin() bool {
	if m != nil {
		return m.IsJoin
	}
	return false
}

func (m *GroupUpdateEventMemberChange) GetCurrentMemberCount() uint32 {
	if m != nil {
		return m.CurrentMemberCount
	}
	return 0
}

func (m *GroupUpdateEventMemberChange) GetMaxMemberCount() uint32 {
	if m != nil {
		return m.MaxMemberCount
	}
	return 0
}

func (m *GroupUpdateEventMemberChange) GetOperator() uint64 {
	if m != nil {
		return m.Operator
	}
	return 0
}

type GroupUpdateEventRoleChange struct {
	Member  uint64 `protobuf:"varint,1,opt,name=member" json:"member"`
	Old     uint32 `protobuf:"varint,2,opt,name=old" json:"old"`
	Current uint32 `protobuf:"varint,3,opt,name=current" json:"current"`
}

func (m *GroupUpdateEventRoleChange) Reset()         { *m = GroupUpdateEventRoleChange{} }
func (m *GroupUpdateEventRoleChange) String() string { return proto.CompactTextString(m) }
func (*GroupUpdateEventRoleChange) ProtoMessage()    {}
func (*GroupUpdateEventRoleChange) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkOnline, []int{6}
}

func (m *GroupUpdateEventRoleChange) GetMember() uint64 {
	if m != nil {
		return m.Member
	}
	return 0
}

func (m *GroupUpdateEventRoleChange) GetOld() uint32 {
	if m != nil {
		return m.Old
	}
	return 0
}

func (m *GroupUpdateEventRoleChange) GetCurrent() uint32 {
	if m != nil {
		return m.Current
	}
	return 0
}

type OnlineUpdate struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IsOnline bool   `protobuf:"varint,2,req,name=is_online,json=isOnline" json:"is_online"`
	OsType   uint32 `protobuf:"varint,3,opt,name=os_type,json=osType" json:"os_type"`
	IsLogout bool   `protobuf:"varint,4,opt,name=is_logout,json=isLogout" json:"is_logout"`
	Ts       uint32 `protobuf:"varint,5,opt,name=ts" json:"ts"`
}

func (m *OnlineUpdate) Reset()                    { *m = OnlineUpdate{} }
func (m *OnlineUpdate) String() string            { return proto.CompactTextString(m) }
func (*OnlineUpdate) ProtoMessage()               {}
func (*OnlineUpdate) Descriptor() ([]byte, []int) { return fileDescriptorKfkOnline, []int{7} }

func (m *OnlineUpdate) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OnlineUpdate) GetIsOnline() bool {
	if m != nil {
		return m.IsOnline
	}
	return false
}

func (m *OnlineUpdate) GetOsType() uint32 {
	if m != nil {
		return m.OsType
	}
	return 0
}

func (m *OnlineUpdate) GetIsLogout() bool {
	if m != nil {
		return m.IsLogout
	}
	return false
}

func (m *OnlineUpdate) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

type OnlineUpdateInfo struct {
	Msg       *OnlineUpdate `protobuf:"bytes,1,req,name=msg" json:"msg,omitempty"`
	IsLastOne bool          `protobuf:"varint,2,opt,name=is_last_one,json=isLastOne" json:"is_last_one"`
}

func (m *OnlineUpdateInfo) Reset()                    { *m = OnlineUpdateInfo{} }
func (m *OnlineUpdateInfo) String() string            { return proto.CompactTextString(m) }
func (*OnlineUpdateInfo) ProtoMessage()               {}
func (*OnlineUpdateInfo) Descriptor() ([]byte, []int) { return fileDescriptorKfkOnline, []int{8} }

func (m *OnlineUpdateInfo) GetMsg() *OnlineUpdate {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *OnlineUpdateInfo) GetIsLastOne() bool {
	if m != nil {
		return m.IsLastOne
	}
	return false
}

func init() {
	proto.RegisterType((*FrinedUpdate)(nil), "kafkaonline.FrinedUpdate")
	proto.RegisterType((*GroupUpdate)(nil), "kafkaonline.GroupUpdate")
	proto.RegisterType((*GroupUpdateEventCreateGroup)(nil), "kafkaonline.GroupUpdateEventCreateGroup")
	proto.RegisterType((*GroupUpdateEventDismissGroup)(nil), "kafkaonline.GroupUpdateEventDismissGroup")
	proto.RegisterType((*GroupUpdateEventVerifyChange)(nil), "kafkaonline.GroupUpdateEventVerifyChange")
	proto.RegisterType((*GroupUpdateEventMemberChange)(nil), "kafkaonline.GroupUpdateEventMemberChange")
	proto.RegisterType((*GroupUpdateEventRoleChange)(nil), "kafkaonline.GroupUpdateEventRoleChange")
	proto.RegisterType((*OnlineUpdate)(nil), "kafkaonline.OnlineUpdate")
	proto.RegisterType((*OnlineUpdateInfo)(nil), "kafkaonline.OnlineUpdateInfo")
	proto.RegisterEnum("kafkaonline.GroupUpdateEventType", GroupUpdateEventType_name, GroupUpdateEventType_value)
}
func (m *FrinedUpdate) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FrinedUpdate) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x18
	i++
	if m.IsAdd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.Sequence))
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.SrcType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.EvTs))
	return i, nil
}

func (m *GroupUpdate) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupUpdate) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x18
	i++
	if m.IsAdd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	if m.IsDismiss {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.GroupType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.Role))
	dAtA[i] = 0x38
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.EvTs))
	dAtA[i] = 0x42
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(len(m.GroupCreateFrom)))
	i += copy(dAtA[i:], m.GroupCreateFrom)
	dAtA[i] = 0x48
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.EventType))
	if m.EventPayload != nil {
		nn1, err := m.EventPayload.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += nn1
	}
	return i, nil
}

func (m *GroupUpdate_CreateGroup) MarshalTo(dAtA []byte) (int, error) {
	i := 0
	if m.CreateGroup != nil {
		dAtA[i] = 0x52
		i++
		i = encodeVarintKfkOnline(dAtA, i, uint64(m.CreateGroup.Size()))
		n2, err := m.CreateGroup.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}
func (m *GroupUpdate_DismissGroup) MarshalTo(dAtA []byte) (int, error) {
	i := 0
	if m.DismissGroup != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintKfkOnline(dAtA, i, uint64(m.DismissGroup.Size()))
		n3, err := m.DismissGroup.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}
func (m *GroupUpdate_VerifyChange) MarshalTo(dAtA []byte) (int, error) {
	i := 0
	if m.VerifyChange != nil {
		dAtA[i] = 0x62
		i++
		i = encodeVarintKfkOnline(dAtA, i, uint64(m.VerifyChange.Size()))
		n4, err := m.VerifyChange.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}
func (m *GroupUpdate_MemberChange) MarshalTo(dAtA []byte) (int, error) {
	i := 0
	if m.MemberChange != nil {
		dAtA[i] = 0x6a
		i++
		i = encodeVarintKfkOnline(dAtA, i, uint64(m.MemberChange.Size()))
		n5, err := m.MemberChange.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}
func (m *GroupUpdate_RoleChange) MarshalTo(dAtA []byte) (int, error) {
	i := 0
	if m.RoleChange != nil {
		dAtA[i] = 0x72
		i++
		i = encodeVarintKfkOnline(dAtA, i, uint64(m.RoleChange.Size()))
		n6, err := m.RoleChange.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}
func (m *GroupUpdateEventCreateGroup) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupUpdateEventCreateGroup) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.Creator))
	return i, nil
}

func (m *GroupUpdateEventDismissGroup) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupUpdateEventDismissGroup) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.Operator))
	return i, nil
}

func (m *GroupUpdateEventVerifyChange) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupUpdateEventVerifyChange) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.Operator))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.CurrentStatus))
	return i, nil
}

func (m *GroupUpdateEventMemberChange) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupUpdateEventMemberChange) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.Member))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.Role))
	dAtA[i] = 0x18
	i++
	if m.IsJoin {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.CurrentMemberCount))
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.MaxMemberCount))
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.Operator))
	return i, nil
}

func (m *GroupUpdateEventRoleChange) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupUpdateEventRoleChange) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.Member))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.Old))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.Current))
	return i, nil
}

func (m *OnlineUpdate) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OnlineUpdate) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.IsOnline {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.OsType))
	dAtA[i] = 0x20
	i++
	if m.IsLogout {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkOnline(dAtA, i, uint64(m.Ts))
	return i, nil
}

func (m *OnlineUpdateInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OnlineUpdateInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintKfkOnline(dAtA, i, uint64(m.Msg.Size()))
		n7, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x10
	i++
	if m.IsLastOne {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func encodeFixed64KfkOnline(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32KfkOnline(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintKfkOnline(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *FrinedUpdate) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkOnline(uint64(m.Uid))
	n += 1 + sovKfkOnline(uint64(m.TargetUid))
	n += 2
	n += 1 + sovKfkOnline(uint64(m.Sequence))
	n += 1 + sovKfkOnline(uint64(m.SrcType))
	n += 1 + sovKfkOnline(uint64(m.EvTs))
	return n
}

func (m *GroupUpdate) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkOnline(uint64(m.GroupId))
	n += 1 + sovKfkOnline(uint64(m.TargetUid))
	n += 2
	n += 2
	n += 1 + sovKfkOnline(uint64(m.GroupType))
	n += 1 + sovKfkOnline(uint64(m.Role))
	n += 1 + sovKfkOnline(uint64(m.EvTs))
	l = len(m.GroupCreateFrom)
	n += 1 + l + sovKfkOnline(uint64(l))
	n += 1 + sovKfkOnline(uint64(m.EventType))
	if m.EventPayload != nil {
		n += m.EventPayload.Size()
	}
	return n
}

func (m *GroupUpdate_CreateGroup) Size() (n int) {
	var l int
	_ = l
	if m.CreateGroup != nil {
		l = m.CreateGroup.Size()
		n += 1 + l + sovKfkOnline(uint64(l))
	}
	return n
}
func (m *GroupUpdate_DismissGroup) Size() (n int) {
	var l int
	_ = l
	if m.DismissGroup != nil {
		l = m.DismissGroup.Size()
		n += 1 + l + sovKfkOnline(uint64(l))
	}
	return n
}
func (m *GroupUpdate_VerifyChange) Size() (n int) {
	var l int
	_ = l
	if m.VerifyChange != nil {
		l = m.VerifyChange.Size()
		n += 1 + l + sovKfkOnline(uint64(l))
	}
	return n
}
func (m *GroupUpdate_MemberChange) Size() (n int) {
	var l int
	_ = l
	if m.MemberChange != nil {
		l = m.MemberChange.Size()
		n += 1 + l + sovKfkOnline(uint64(l))
	}
	return n
}
func (m *GroupUpdate_RoleChange) Size() (n int) {
	var l int
	_ = l
	if m.RoleChange != nil {
		l = m.RoleChange.Size()
		n += 1 + l + sovKfkOnline(uint64(l))
	}
	return n
}
func (m *GroupUpdateEventCreateGroup) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkOnline(uint64(m.Creator))
	return n
}

func (m *GroupUpdateEventDismissGroup) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkOnline(uint64(m.Operator))
	return n
}

func (m *GroupUpdateEventVerifyChange) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkOnline(uint64(m.Operator))
	n += 1 + sovKfkOnline(uint64(m.CurrentStatus))
	return n
}

func (m *GroupUpdateEventMemberChange) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkOnline(uint64(m.Member))
	n += 1 + sovKfkOnline(uint64(m.Role))
	n += 2
	n += 1 + sovKfkOnline(uint64(m.CurrentMemberCount))
	n += 1 + sovKfkOnline(uint64(m.MaxMemberCount))
	n += 1 + sovKfkOnline(uint64(m.Operator))
	return n
}

func (m *GroupUpdateEventRoleChange) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkOnline(uint64(m.Member))
	n += 1 + sovKfkOnline(uint64(m.Old))
	n += 1 + sovKfkOnline(uint64(m.Current))
	return n
}

func (m *OnlineUpdate) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkOnline(uint64(m.Uid))
	n += 2
	n += 1 + sovKfkOnline(uint64(m.OsType))
	n += 2
	n += 1 + sovKfkOnline(uint64(m.Ts))
	return n
}

func (m *OnlineUpdateInfo) Size() (n int) {
	var l int
	_ = l
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovKfkOnline(uint64(l))
	}
	n += 2
	return n
}

func sovKfkOnline(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozKfkOnline(x uint64) (n int) {
	return sovKfkOnline(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *FrinedUpdate) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkOnline
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: FrinedUpdate: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: FrinedUpdate: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsAdd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdd = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Sequence", wireType)
			}
			m.Sequence = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sequence |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SrcType", wireType)
			}
			m.SrcType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SrcType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field EvTs", wireType)
			}
			m.EvTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EvTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkOnline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkOnline
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("target_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_add")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupUpdate) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkOnline
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GroupUpdate: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GroupUpdate: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsAdd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdd = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsDismiss", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDismiss = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Role", wireType)
			}
			m.Role = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Role |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field EvTs", wireType)
			}
			m.EvTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EvTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GroupCreateFrom", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkOnline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GroupCreateFrom = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field EventType", wireType)
			}
			m.EventType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EventType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CreateGroup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkOnline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &GroupUpdateEventCreateGroup{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.EventPayload = &GroupUpdate_CreateGroup{v}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DismissGroup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkOnline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &GroupUpdateEventDismissGroup{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.EventPayload = &GroupUpdate_DismissGroup{v}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field VerifyChange", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkOnline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &GroupUpdateEventVerifyChange{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.EventPayload = &GroupUpdate_VerifyChange{v}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MemberChange", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkOnline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &GroupUpdateEventMemberChange{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.EventPayload = &GroupUpdate_MemberChange{v}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RoleChange", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkOnline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &GroupUpdateEventRoleChange{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.EventPayload = &GroupUpdate_RoleChange{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkOnline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkOnline
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("target_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_add")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupUpdateEventCreateGroup) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkOnline
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GroupUpdateEventCreateGroup: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GroupUpdateEventCreateGroup: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			m.Creator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Creator |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkOnline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkOnline
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupUpdateEventDismissGroup) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkOnline
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GroupUpdateEventDismissGroup: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GroupUpdateEventDismissGroup: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			m.Operator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Operator |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkOnline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkOnline
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupUpdateEventVerifyChange) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkOnline
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GroupUpdateEventVerifyChange: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GroupUpdateEventVerifyChange: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			m.Operator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Operator |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CurrentStatus", wireType)
			}
			m.CurrentStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkOnline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkOnline
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupUpdateEventMemberChange) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkOnline
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GroupUpdateEventMemberChange: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GroupUpdateEventMemberChange: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Member", wireType)
			}
			m.Member = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Member |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Role", wireType)
			}
			m.Role = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Role |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsJoin", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsJoin = bool(v != 0)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CurrentMemberCount", wireType)
			}
			m.CurrentMemberCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentMemberCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MaxMemberCount", wireType)
			}
			m.MaxMemberCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxMemberCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			m.Operator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Operator |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkOnline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkOnline
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupUpdateEventRoleChange) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkOnline
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GroupUpdateEventRoleChange: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GroupUpdateEventRoleChange: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Member", wireType)
			}
			m.Member = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Member |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Old", wireType)
			}
			m.Old = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Old |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Current", wireType)
			}
			m.Current = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Current |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkOnline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkOnline
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OnlineUpdate) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkOnline
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: OnlineUpdate: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: OnlineUpdate: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsOnline", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsOnline = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OsType", wireType)
			}
			m.OsType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OsType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsLogout", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsLogout = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkOnline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkOnline
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_online")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OnlineUpdateInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkOnline
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: OnlineUpdateInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: OnlineUpdateInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkOnline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &OnlineUpdate{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsLastOne", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsLastOne = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkOnline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkOnline
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipKfkOnline(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowKfkOnline
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKfkOnline
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthKfkOnline
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowKfkOnline
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipKfkOnline(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt1.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthKfkOnline = fmt1.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowKfkOnline   = fmt1.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/minToolkit/kafka/pb/kafka_online_update/kfk_online.proto", fileDescriptorKfkOnline)
}

var fileDescriptorKfkOnline = []byte{
	// 840 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x55, 0xcd, 0x6e, 0xeb, 0x44,
	0x14, 0x8e, 0x9d, 0xff, 0xe3, 0xa4, 0xd7, 0x77, 0xd4, 0x5b, 0xdc, 0xdb, 0x4b, 0x1a, 0x7c, 0x91,
	0x08, 0x5c, 0x29, 0x41, 0x5d, 0xb0, 0x02, 0x09, 0x5a, 0x28, 0xb4, 0x6a, 0x55, 0x14, 0x5a, 0xb6,
	0x96, 0x6b, 0x4f, 0xc2, 0x90, 0xd8, 0x63, 0x66, 0xc6, 0x51, 0xf3, 0x16, 0xbc, 0x02, 0xcf, 0xc1,
	0x1e, 0x75, 0xc9, 0x13, 0x20, 0x54, 0x36, 0xbc, 0x05, 0x68, 0xc6, 0x76, 0x32, 0x49, 0x20, 0xed,
	0xe2, 0xee, 0x3c, 0xdf, 0x39, 0xf3, 0xcd, 0xf9, 0xbe, 0x39, 0x67, 0x0c, 0x9f, 0x72, 0x16, 0x0c,
	0x22, 0x12, 0x5f, 0x53, 0x3a, 0x9d, 0x10, 0x31, 0x98, 0xf8, 0xa3, 0x89, 0x3f, 0x48, 0x6e, 0xb3,
	0x0f, 0x8f, 0xc6, 0x53, 0x12, 0x63, 0x2f, 0x4d, 0x42, 0x5f, 0xe0, 0xc1, 0x64, 0x34, 0xc9, 0x91,
	0x7e, 0xc2, 0xa8, 0xa0, 0xc8, 0x52, 0x59, 0x19, 0xe4, 0xfe, 0x66, 0x40, 0xeb, 0x94, 0x91, 0x18,
	0x87, 0x37, 0x2a, 0x1d, 0xed, 0x41, 0x39, 0x25, 0xa1, 0x63, 0x74, 0xcd, 0x5e, 0xfb, 0xb8, 0x72,
	0xff, 0xc7, 0x61, 0x69, 0x28, 0x01, 0xf4, 0x1a, 0x40, 0xf8, 0x6c, 0x8c, 0x85, 0x27, 0xc3, 0xa6,
	0x16, 0x6e, 0x66, 0xf8, 0x0d, 0x09, 0xd1, 0x01, 0xd4, 0x08, 0xf7, 0xfc, 0x30, 0x74, 0xca, 0x5d,
	0xb3, 0xd7, 0xc8, 0x13, 0xaa, 0x84, 0x7f, 0x11, 0x86, 0xa8, 0x0b, 0x0d, 0x8e, 0x7f, 0x4a, 0x71,
	0x1c, 0x60, 0xa7, 0xd2, 0x35, 0x7a, 0x95, 0x3c, 0xbc, 0x40, 0xd1, 0x21, 0x34, 0x38, 0x0b, 0x3c,
	0x31, 0x4f, 0xb0, 0x53, 0xed, 0x1a, 0x8b, 0x13, 0xea, 0x9c, 0x05, 0xd7, 0xf3, 0x04, 0xa3, 0x7d,
	0xa8, 0xe2, 0x99, 0x27, 0xb8, 0x53, 0xd3, 0xa2, 0x15, 0x3c, 0xbb, 0xe6, 0xee, 0xdf, 0x55, 0xb0,
	0xbe, 0x66, 0x34, 0x4d, 0x72, 0x1d, 0x87, 0xd0, 0x18, 0xcb, 0xa5, 0xb7, 0x26, 0xa6, 0xae, 0xd0,
	0xb3, 0xb7, 0x21, 0xe8, 0x35, 0x00, 0xe1, 0x5e, 0x48, 0x78, 0x44, 0x38, 0x57, 0x92, 0x8a, 0x84,
	0x26, 0xe1, 0x5f, 0x66, 0xb0, 0x4c, 0xca, 0xea, 0xd8, 0x50, 0xd5, 0x54, 0xb8, 0xd2, 0xe5, 0x40,
	0x85, 0xd1, 0x29, 0x5e, 0x95, 0x25, 0x91, 0xa5, 0xe2, 0xfa, 0xba, 0x62, 0xf4, 0x31, 0x3c, 0xcf,
	0x98, 0x03, 0x86, 0x7d, 0x81, 0xbd, 0x11, 0xa3, 0x91, 0xd3, 0xe8, 0x1a, 0xbd, 0x66, 0x9e, 0xf6,
	0x4c, 0x85, 0x4f, 0x54, 0xf4, 0x94, 0xd1, 0x48, 0xd6, 0x82, 0x67, 0x38, 0x16, 0x59, 0x2d, 0x4d,
	0xbd, 0x16, 0x85, 0xab, 0x5a, 0x2e, 0xa1, 0x95, 0x13, 0xaa, 0xed, 0x0e, 0x74, 0x8d, 0x9e, 0x75,
	0xd4, 0xeb, 0x6b, 0x5d, 0xd3, 0xd7, 0x8c, 0xfe, 0x4a, 0x6e, 0xcc, 0xce, 0x50, 0xe8, 0x37, 0xa5,
	0xa1, 0x15, 0x2c, 0x97, 0xe8, 0x5b, 0x68, 0xe7, 0x0e, 0xe5, 0x7c, 0x96, 0xe2, 0xfb, 0x70, 0x2b,
	0x5f, 0x6e, 0x5e, 0x41, 0xd8, 0x0a, 0xb5, 0xb5, 0x64, 0x9c, 0x61, 0x46, 0x46, 0x73, 0x2f, 0xf8,
	0xc1, 0x8f, 0xc7, 0xd8, 0x69, 0x3d, 0x81, 0xf1, 0x7b, 0xb5, 0xe3, 0x44, 0x6d, 0x90, 0x8c, 0x33,
	0x6d, 0x2d, 0x19, 0x23, 0x1c, 0xdd, 0x62, 0x56, 0x30, 0xb6, 0x9f, 0xc0, 0x78, 0xa9, 0x76, 0x2c,
	0x19, 0x23, 0x6d, 0x8d, 0xce, 0xc1, 0x92, 0xd7, 0x57, 0xf0, 0xed, 0x28, 0xbe, 0x0f, 0xb6, 0xf2,
	0x0d, 0xe9, 0x14, 0x2f, 0xd8, 0x80, 0x2d, 0x56, 0xc7, 0xcf, 0xa0, 0x9d, 0xdd, 0x5a, 0xe2, 0xcf,
	0xa7, 0xd4, 0x0f, 0xdd, 0xcf, 0xe0, 0x60, 0xcb, 0x05, 0xa0, 0x0e, 0xd4, 0xd5, 0x05, 0x50, 0xe6,
	0x18, 0xda, 0x98, 0x15, 0xa0, 0xfb, 0x39, 0xbc, 0xda, 0xe6, 0xb7, 0x9c, 0x53, 0x9a, 0x60, 0xb6,
	0x41, 0xb0, 0x40, 0xdd, 0x68, 0x93, 0x41, 0xf7, 0xf7, 0x71, 0x06, 0xf4, 0x06, 0x76, 0x82, 0x94,
	0x31, 0xa9, 0x8a, 0x0b, 0x5f, 0xa4, 0xdc, 0x31, 0xb5, 0x6e, 0x6c, 0xe7, 0xb1, 0xef, 0x54, 0xc8,
	0xfd, 0xc7, 0xd8, 0x3c, 0x4f, 0x77, 0x1f, 0xbd, 0x82, 0x5a, 0xe6, 0xfe, 0xca, 0x69, 0x39, 0xb6,
	0x18, 0x2e, 0x73, 0x63, 0xb8, 0xde, 0x85, 0x3a, 0xe1, 0xde, 0x8f, 0x94, 0xc4, 0x4e, 0x59, 0x9b,
	0xde, 0x1a, 0xe1, 0xe7, 0x94, 0xc4, 0xe8, 0x13, 0xd8, 0x2d, 0x8a, 0x2c, 0xda, 0x83, 0xa6, 0xb1,
	0x50, 0x93, 0x5e, 0x10, 0xa1, 0x3c, 0x23, 0xaf, 0x47, 0xc6, 0x51, 0x1f, 0xec, 0xc8, 0xbf, 0x5b,
	0xdd, 0xa3, 0x0f, 0xfe, 0x4e, 0xe4, 0xdf, 0xe9, 0xf9, 0xba, 0x5d, 0xb5, 0xff, 0x34, 0x9c, 0xc1,
	0xcb, 0xff, 0x6f, 0x97, 0x47, 0xe4, 0xef, 0x41, 0x99, 0x4e, 0xc3, 0x15, 0xf5, 0x12, 0x50, 0x6d,
	0x92, 0xd5, 0xae, 0xc4, 0x2f, 0xde, 0xc7, 0x1c, 0x74, 0x7f, 0x31, 0xa0, 0x75, 0xa5, 0x5a, 0xf5,
	0x91, 0x3f, 0xc3, 0x7b, 0xd0, 0x24, 0x3c, 0xff, 0xc5, 0xa8, 0x77, 0xb4, 0xf0, 0xb1, 0x41, 0x78,
	0x46, 0x20, 0x8d, 0xa6, 0x3c, 0x7b, 0x75, 0xf4, 0xb3, 0x6a, 0x94, 0xab, 0x27, 0x27, 0x63, 0x98,
	0xd2, 0x31, 0x4d, 0xc5, 0xca, 0x3b, 0xda, 0x20, 0xfc, 0x42, 0xa1, 0x68, 0x17, 0x4c, 0xc1, 0x57,
	0x5c, 0x34, 0x05, 0x77, 0x31, 0xd8, 0x7a, 0x89, 0x67, 0xf1, 0x88, 0xa2, 0x37, 0x50, 0x8e, 0xf8,
	0x58, 0x95, 0x69, 0x1d, 0xed, 0xaf, 0x8c, 0x9c, 0x9e, 0x3b, 0x94, 0x59, 0xe8, 0x7d, 0xb0, 0xe4,
	0xc9, 0x3e, 0x17, 0x1e, 0x8d, 0xb3, 0x16, 0xd1, 0xde, 0xf0, 0x0b, 0x9f, 0x8b, 0xab, 0x18, 0x7f,
	0xf4, 0xab, 0x01, 0xbb, 0xeb, 0xfe, 0xab, 0xc2, 0x5f, 0xc0, 0x73, 0x0d, 0xbf, 0xc0, 0x63, 0x3f,
	0x98, 0xdb, 0x25, 0xf4, 0x12, 0xf6, 0x34, 0x58, 0x9b, 0x4d, 0xdb, 0x40, 0x07, 0xf0, 0x8e, 0x16,
	0xd3, 0x07, 0xcf, 0x36, 0xd7, 0x82, 0xfa, 0x4c, 0xd9, 0xe5, 0xb5, 0xa0, 0x3e, 0x00, 0x76, 0x05,
	0xed, 0xc3, 0x0b, 0x2d, 0xb8, 0x6c, 0x0e, 0xbb, 0x7a, 0x6c, 0xdf, 0x3f, 0x74, 0x8c, 0xdf, 0x1f,
	0x3a, 0xc6, 0x9f, 0x0f, 0x1d, 0xe3, 0xe7, 0xbf, 0x3a, 0xa5, 0x7f, 0x03, 0x00, 0x00, 0xff, 0xff,
	0x1c, 0xad, 0x31, 0x52, 0x40, 0x08, 0x00, 0x00,
}
