// Code generated by protoc-gen-gogo.
// source: src/minToolkit/kafka/pb/kfk_greenbaba.proto
// DO NOT EDIT!

/*
	Package kafkagreenbaba is a generated protocol buffer package.

	It is generated from these files:
		src/minToolkit/kafka/pb/kfk_greenbaba.proto

	It has these top-level messages:
		GreenBabaSanctionEvent
		ReportMessageContent
		GreenBabaReportEvent
		UserSimpleInfo
		ChannelReportEventOpt
		UserReportEventOpt
		MusicReportEvent
		ChatReportEvent
		GroupReportEventOpt
		UgcReportEventOpt
		GuildReportEventOpt
		ChatCardReportEventOpt
		MaskedCallAudio
		MaskedCallReportEventOpt
		SlipNoteReportEventOpt
		GamePalCardReportEventOpt
*/
package kafkagreenbaba

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"

import io "io"
import fmt1 "fmt"
import github_com_gogo_protobuf_proto "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ENUM_TARGET_TYPE int32

const (
	ENUM_TARGET_TYPE_E_TARGET_TYPE_CHANNEL       ENUM_TARGET_TYPE = 1
	ENUM_TARGET_TYPE_E_TARGET_TYPE_USER          ENUM_TARGET_TYPE = 2
	ENUM_TARGET_TYPE_E_TARGET_TYPE_MUSIC         ENUM_TARGET_TYPE = 3
	ENUM_TARGET_TYPE_E_TARGET_TYPE_GROUP         ENUM_TARGET_TYPE = 4
	ENUM_TARGET_TYPE_E_TARGET_TYPE_CHAT          ENUM_TARGET_TYPE = 5
	ENUM_TARGET_TYPE_E_TARGET_TYPE_UGC           ENUM_TARGET_TYPE = 6
	ENUM_TARGET_TYPE_E_TARGET_TYPE_GUILD         ENUM_TARGET_TYPE = 8
	ENUM_TARGET_TYPE_E_TARGET_TYPE_MASKED_CALL   ENUM_TARGET_TYPE = 9
	ENUM_TARGET_TYPE_E_TARGET_TYPE_SLIP_NOTE     ENUM_TARGET_TYPE = 10
	ENUM_TARGET_TYPE_E_TARGET_TYPE_CHAT_CARD     ENUM_TARGET_TYPE = 11
	ENUM_TARGET_TYPE_E_TARGET_TYPE_PIA           ENUM_TARGET_TYPE = 12
	ENUM_TARGET_TYPE_E_TARGET_TYPE_GAME_PAL_CARD ENUM_TARGET_TYPE = 13
)

var ENUM_TARGET_TYPE_name = map[int32]string{
	1:  "E_TARGET_TYPE_CHANNEL",
	2:  "E_TARGET_TYPE_USER",
	3:  "E_TARGET_TYPE_MUSIC",
	4:  "E_TARGET_TYPE_GROUP",
	5:  "E_TARGET_TYPE_CHAT",
	6:  "E_TARGET_TYPE_UGC",
	8:  "E_TARGET_TYPE_GUILD",
	9:  "E_TARGET_TYPE_MASKED_CALL",
	10: "E_TARGET_TYPE_SLIP_NOTE",
	11: "E_TARGET_TYPE_CHAT_CARD",
	12: "E_TARGET_TYPE_PIA",
	13: "E_TARGET_TYPE_GAME_PAL_CARD",
}
var ENUM_TARGET_TYPE_value = map[string]int32{
	"E_TARGET_TYPE_CHANNEL":       1,
	"E_TARGET_TYPE_USER":          2,
	"E_TARGET_TYPE_MUSIC":         3,
	"E_TARGET_TYPE_GROUP":         4,
	"E_TARGET_TYPE_CHAT":          5,
	"E_TARGET_TYPE_UGC":           6,
	"E_TARGET_TYPE_GUILD":         8,
	"E_TARGET_TYPE_MASKED_CALL":   9,
	"E_TARGET_TYPE_SLIP_NOTE":     10,
	"E_TARGET_TYPE_CHAT_CARD":     11,
	"E_TARGET_TYPE_PIA":           12,
	"E_TARGET_TYPE_GAME_PAL_CARD": 13,
}

func (x ENUM_TARGET_TYPE) Enum() *ENUM_TARGET_TYPE {
	p := new(ENUM_TARGET_TYPE)
	*p = x
	return p
}
func (x ENUM_TARGET_TYPE) String() string {
	return proto.EnumName(ENUM_TARGET_TYPE_name, int32(x))
}
func (x *ENUM_TARGET_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ENUM_TARGET_TYPE_value, data, "ENUM_TARGET_TYPE")
	if err != nil {
		return err
	}
	*x = ENUM_TARGET_TYPE(value)
	return nil
}
func (ENUM_TARGET_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorKfkGreenbaba, []int{0} }

// 制裁类型
type ENUM_SANCTION_OP_TYPE int32

const (
	ENUM_SANCTION_OP_TYPE_E_SANCTION_OP_TYPE_NONE    ENUM_SANCTION_OP_TYPE = 0
	ENUM_SANCTION_OP_TYPE_E_SANCTION_OP_TYPE_WARNING ENUM_SANCTION_OP_TYPE = 3
	ENUM_SANCTION_OP_TYPE_E_SANCTION_OP_TYPE_BANNED  ENUM_SANCTION_OP_TYPE = 4
	ENUM_SANCTION_OP_TYPE_E_SANCTION_OP_TYPE_IGNORE  ENUM_SANCTION_OP_TYPE = 25
)

var ENUM_SANCTION_OP_TYPE_name = map[int32]string{
	0:  "E_SANCTION_OP_TYPE_NONE",
	3:  "E_SANCTION_OP_TYPE_WARNING",
	4:  "E_SANCTION_OP_TYPE_BANNED",
	25: "E_SANCTION_OP_TYPE_IGNORE",
}
var ENUM_SANCTION_OP_TYPE_value = map[string]int32{
	"E_SANCTION_OP_TYPE_NONE":    0,
	"E_SANCTION_OP_TYPE_WARNING": 3,
	"E_SANCTION_OP_TYPE_BANNED":  4,
	"E_SANCTION_OP_TYPE_IGNORE":  25,
}

func (x ENUM_SANCTION_OP_TYPE) Enum() *ENUM_SANCTION_OP_TYPE {
	p := new(ENUM_SANCTION_OP_TYPE)
	*p = x
	return p
}
func (x ENUM_SANCTION_OP_TYPE) String() string {
	return proto.EnumName(ENUM_SANCTION_OP_TYPE_name, int32(x))
}
func (x *ENUM_SANCTION_OP_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ENUM_SANCTION_OP_TYPE_value, data, "ENUM_SANCTION_OP_TYPE")
	if err != nil {
		return err
	}
	*x = ENUM_SANCTION_OP_TYPE(value)
	return nil
}
func (ENUM_SANCTION_OP_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkGreenbaba, []int{1}
}

// 制裁类型中 封禁情况下 的封禁类型
type ENUM_BANNED_TYPE int32

const (
	ENUM_BANNED_TYPE_E_BANNED_NONE    ENUM_BANNED_TYPE = 0
	ENUM_BANNED_TYPE_E_BANNED_LOGIN   ENUM_BANNED_TYPE = 1
	ENUM_BANNED_TYPE_E_BANNED_CHANNEL ENUM_BANNED_TYPE = 2
)

var ENUM_BANNED_TYPE_name = map[int32]string{
	0: "E_BANNED_NONE",
	1: "E_BANNED_LOGIN",
	2: "E_BANNED_CHANNEL",
}
var ENUM_BANNED_TYPE_value = map[string]int32{
	"E_BANNED_NONE":    0,
	"E_BANNED_LOGIN":   1,
	"E_BANNED_CHANNEL": 2,
}

func (x ENUM_BANNED_TYPE) Enum() *ENUM_BANNED_TYPE {
	p := new(ENUM_BANNED_TYPE)
	*p = x
	return p
}
func (x ENUM_BANNED_TYPE) String() string {
	return proto.EnumName(ENUM_BANNED_TYPE_name, int32(x))
}
func (x *ENUM_BANNED_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ENUM_BANNED_TYPE_value, data, "ENUM_BANNED_TYPE")
	if err != nil {
		return err
	}
	*x = ENUM_BANNED_TYPE(value)
	return nil
}
func (ENUM_BANNED_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorKfkGreenbaba, []int{2} }

// 举报事件
type ENUM_REPORT_TYPE int32

const (
	ENUM_REPORT_TYPE_E_REPORT_PRIVACY         ENUM_REPORT_TYPE = 0
	ENUM_REPORT_TYPE_E_REPORT_PERSONAL_ATTACK ENUM_REPORT_TYPE = 1
	ENUM_REPORT_TYPE_E_REPORT_PORNOGRAPHY     ENUM_REPORT_TYPE = 2
	ENUM_REPORT_TYPE_E_REPORT_AD              ENUM_REPORT_TYPE = 3
	ENUM_REPORT_TYPE_E_REPORT_SENSITIVE_INFO  ENUM_REPORT_TYPE = 4
	ENUM_REPORT_TYPE_E_REPORT_TORT            ENUM_REPORT_TYPE = 5
	ENUM_REPORT_TYPE_E_REPORT_GAMBLING        ENUM_REPORT_TYPE = 6
)

var ENUM_REPORT_TYPE_name = map[int32]string{
	0: "E_REPORT_PRIVACY",
	1: "E_REPORT_PERSONAL_ATTACK",
	2: "E_REPORT_PORNOGRAPHY",
	3: "E_REPORT_AD",
	4: "E_REPORT_SENSITIVE_INFO",
	5: "E_REPORT_TORT",
	6: "E_REPORT_GAMBLING",
}
var ENUM_REPORT_TYPE_value = map[string]int32{
	"E_REPORT_PRIVACY":         0,
	"E_REPORT_PERSONAL_ATTACK": 1,
	"E_REPORT_PORNOGRAPHY":     2,
	"E_REPORT_AD":              3,
	"E_REPORT_SENSITIVE_INFO":  4,
	"E_REPORT_TORT":            5,
	"E_REPORT_GAMBLING":        6,
}

func (x ENUM_REPORT_TYPE) Enum() *ENUM_REPORT_TYPE {
	p := new(ENUM_REPORT_TYPE)
	*p = x
	return p
}
func (x ENUM_REPORT_TYPE) String() string {
	return proto.EnumName(ENUM_REPORT_TYPE_name, int32(x))
}
func (x *ENUM_REPORT_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ENUM_REPORT_TYPE_value, data, "ENUM_REPORT_TYPE")
	if err != nil {
		return err
	}
	*x = ENUM_REPORT_TYPE(value)
	return nil
}
func (ENUM_REPORT_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorKfkGreenbaba, []int{3} }

type ENUM_REPORT_MESSAGE_TYPE int32

const (
	ENUM_REPORT_MESSAGE_TYPE_E_REPORT_MESSAGE_TEXT ENUM_REPORT_MESSAGE_TYPE = 1
	ENUM_REPORT_MESSAGE_TYPE_E_REPORT_MESSAGE_IMG  ENUM_REPORT_MESSAGE_TYPE = 2
)

var ENUM_REPORT_MESSAGE_TYPE_name = map[int32]string{
	1: "E_REPORT_MESSAGE_TEXT",
	2: "E_REPORT_MESSAGE_IMG",
}
var ENUM_REPORT_MESSAGE_TYPE_value = map[string]int32{
	"E_REPORT_MESSAGE_TEXT": 1,
	"E_REPORT_MESSAGE_IMG":  2,
}

func (x ENUM_REPORT_MESSAGE_TYPE) Enum() *ENUM_REPORT_MESSAGE_TYPE {
	p := new(ENUM_REPORT_MESSAGE_TYPE)
	*p = x
	return p
}
func (x ENUM_REPORT_MESSAGE_TYPE) String() string {
	return proto.EnumName(ENUM_REPORT_MESSAGE_TYPE_name, int32(x))
}
func (x *ENUM_REPORT_MESSAGE_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ENUM_REPORT_MESSAGE_TYPE_value, data, "ENUM_REPORT_MESSAGE_TYPE")
	if err != nil {
		return err
	}
	*x = ENUM_REPORT_MESSAGE_TYPE(value)
	return nil
}
func (ENUM_REPORT_MESSAGE_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkGreenbaba, []int{4}
}

// 制裁事件
type GreenBabaSanctionEvent struct {
	TargetType     uint32 `protobuf:"varint,1,req,name=target_type,json=targetType" json:"target_type"`
	Id             uint32 `protobuf:"varint,2,req,name=id" json:"id"`
	BannedType     uint32 `protobuf:"varint,3,opt,name=banned_type,json=bannedType" json:"banned_type"`
	Second         uint32 `protobuf:"varint,4,opt,name=second" json:"second"`
	SanctionType   uint32 `protobuf:"varint,5,opt,name=sanction_type,json=sanctionType" json:"sanction_type"`
	SanctionReason string `protobuf:"bytes,6,opt,name=sanction_reason,json=sanctionReason" json:"sanction_reason"`
}

func (m *GreenBabaSanctionEvent) Reset()         { *m = GreenBabaSanctionEvent{} }
func (m *GreenBabaSanctionEvent) String() string { return proto.CompactTextString(m) }
func (*GreenBabaSanctionEvent) ProtoMessage()    {}
func (*GreenBabaSanctionEvent) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkGreenbaba, []int{0}
}

func (m *GreenBabaSanctionEvent) GetTargetType() uint32 {
	if m != nil {
		return m.TargetType
	}
	return 0
}

func (m *GreenBabaSanctionEvent) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GreenBabaSanctionEvent) GetBannedType() uint32 {
	if m != nil {
		return m.BannedType
	}
	return 0
}

func (m *GreenBabaSanctionEvent) GetSecond() uint32 {
	if m != nil {
		return m.Second
	}
	return 0
}

func (m *GreenBabaSanctionEvent) GetSanctionType() uint32 {
	if m != nil {
		return m.SanctionType
	}
	return 0
}

func (m *GreenBabaSanctionEvent) GetSanctionReason() string {
	if m != nil {
		return m.SanctionReason
	}
	return ""
}

type ReportMessageContent struct {
	Type        uint32 `protobuf:"varint,1,req,name=type" json:"type"`
	Msg         string `protobuf:"bytes,2,req,name=msg" json:"msg"`
	Timestamp   uint32 `protobuf:"varint,3,opt,name=timestamp" json:"timestamp"`
	FromUid     uint32 `protobuf:"varint,4,opt,name=from_uid,json=fromUid" json:"from_uid"`
	FromAccount string `protobuf:"bytes,5,opt,name=from_account,json=fromAccount" json:"from_account"`
	MsgSeqId    uint32 `protobuf:"varint,6,opt,name=msg_seq_id,json=msgSeqId" json:"msg_seq_id"`
}

func (m *ReportMessageContent) Reset()                    { *m = ReportMessageContent{} }
func (m *ReportMessageContent) String() string            { return proto.CompactTextString(m) }
func (*ReportMessageContent) ProtoMessage()               {}
func (*ReportMessageContent) Descriptor() ([]byte, []int) { return fileDescriptorKfkGreenbaba, []int{1} }

func (m *ReportMessageContent) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ReportMessageContent) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *ReportMessageContent) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *ReportMessageContent) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *ReportMessageContent) GetFromAccount() string {
	if m != nil {
		return m.FromAccount
	}
	return ""
}

func (m *ReportMessageContent) GetMsgSeqId() uint32 {
	if m != nil {
		return m.MsgSeqId
	}
	return 0
}

type GreenBabaReportEvent struct {
	TargetType     uint32                  `protobuf:"varint,1,req,name=target_type,json=targetType" json:"target_type"`
	Id             uint32                  `protobuf:"varint,2,req,name=id" json:"id"`
	ReportType     uint32                  `protobuf:"varint,3,opt,name=report_type,json=reportType" json:"report_type"`
	ReportReason   string                  `protobuf:"bytes,4,opt,name=report_reason,json=reportReason" json:"report_reason"`
	ReportImgUrl   string                  `protobuf:"bytes,5,opt,name=report_img_url,json=reportImgUrl" json:"report_img_url"`
	ReportUid      uint32                  `protobuf:"varint,6,opt,name=report_uid,json=reportUid" json:"report_uid"`
	OptBin         []byte                  `protobuf:"bytes,7,opt,name=opt_bin,json=optBin" json:"opt_bin"`
	Timestamp      uint32                  `protobuf:"varint,8,opt,name=timestamp" json:"timestamp"`
	ReportUser     *UserSimpleInfo         `protobuf:"bytes,9,opt,name=report_user,json=reportUser" json:"report_user,omitempty"`
	TargetUser     *UserSimpleInfo         `protobuf:"bytes,10,opt,name=target_user,json=targetUser" json:"target_user,omitempty"`
	ReportTypeList []uint32                `protobuf:"varint,11,rep,name=report_type_list,json=reportTypeList" json:"report_type_list,omitempty"`
	ContentList    []*ReportMessageContent `protobuf:"bytes,12,rep,name=content_list,json=contentList" json:"content_list,omitempty"`
	ReasonList     []string                `protobuf:"bytes,13,rep,name=reason_list,json=reasonList" json:"reason_list,omitempty"`
	MarketId       uint32                  `protobuf:"varint,14,opt,name=market_id,json=marketId" json:"market_id"`
}

func (m *GreenBabaReportEvent) Reset()                    { *m = GreenBabaReportEvent{} }
func (m *GreenBabaReportEvent) String() string            { return proto.CompactTextString(m) }
func (*GreenBabaReportEvent) ProtoMessage()               {}
func (*GreenBabaReportEvent) Descriptor() ([]byte, []int) { return fileDescriptorKfkGreenbaba, []int{2} }

func (m *GreenBabaReportEvent) GetTargetType() uint32 {
	if m != nil {
		return m.TargetType
	}
	return 0
}

func (m *GreenBabaReportEvent) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GreenBabaReportEvent) GetReportType() uint32 {
	if m != nil {
		return m.ReportType
	}
	return 0
}

func (m *GreenBabaReportEvent) GetReportReason() string {
	if m != nil {
		return m.ReportReason
	}
	return ""
}

func (m *GreenBabaReportEvent) GetReportImgUrl() string {
	if m != nil {
		return m.ReportImgUrl
	}
	return ""
}

func (m *GreenBabaReportEvent) GetReportUid() uint32 {
	if m != nil {
		return m.ReportUid
	}
	return 0
}

func (m *GreenBabaReportEvent) GetOptBin() []byte {
	if m != nil {
		return m.OptBin
	}
	return nil
}

func (m *GreenBabaReportEvent) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *GreenBabaReportEvent) GetReportUser() *UserSimpleInfo {
	if m != nil {
		return m.ReportUser
	}
	return nil
}

func (m *GreenBabaReportEvent) GetTargetUser() *UserSimpleInfo {
	if m != nil {
		return m.TargetUser
	}
	return nil
}

func (m *GreenBabaReportEvent) GetReportTypeList() []uint32 {
	if m != nil {
		return m.ReportTypeList
	}
	return nil
}

func (m *GreenBabaReportEvent) GetContentList() []*ReportMessageContent {
	if m != nil {
		return m.ContentList
	}
	return nil
}

func (m *GreenBabaReportEvent) GetReasonList() []string {
	if m != nil {
		return m.ReasonList
	}
	return nil
}

func (m *GreenBabaReportEvent) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type UserSimpleInfo struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Nickname string `protobuf:"bytes,2,req,name=nickname" json:"nickname"`
	Ttid     string `protobuf:"bytes,3,req,name=ttid" json:"ttid"`
}

func (m *UserSimpleInfo) Reset()                    { *m = UserSimpleInfo{} }
func (m *UserSimpleInfo) String() string            { return proto.CompactTextString(m) }
func (*UserSimpleInfo) ProtoMessage()               {}
func (*UserSimpleInfo) Descriptor() ([]byte, []int) { return fileDescriptorKfkGreenbaba, []int{3} }

func (m *UserSimpleInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserSimpleInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserSimpleInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

type ChannelReportEventOpt struct {
	ChannelId         uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	ChannelType       uint32 `protobuf:"varint,2,req,name=channel_type,json=channelType" json:"channel_type"`
	ChannelDisplayid  uint32 `protobuf:"varint,3,opt,name=channel_displayid,json=channelDisplayid" json:"channel_displayid"`
	ChannelName       string `protobuf:"bytes,4,opt,name=channel_name,json=channelName" json:"channel_name"`
	ChannelBindId     uint32 `protobuf:"varint,5,opt,name=channel_bind_id,json=channelBindId" json:"channel_bind_id"`
	ChannelSchemeId   uint32 `protobuf:"varint,6,opt,name=channel_scheme_id,json=channelSchemeId" json:"channel_scheme_id"`
	ChannelSchemeName string `protobuf:"bytes,7,opt,name=channel_scheme_name,json=channelSchemeName" json:"channel_scheme_name"`
	ChannelViewId     string `protobuf:"bytes,8,opt,name=channel_view_id,json=channelViewId" json:"channel_view_id"`
}

func (m *ChannelReportEventOpt) Reset()         { *m = ChannelReportEventOpt{} }
func (m *ChannelReportEventOpt) String() string { return proto.CompactTextString(m) }
func (*ChannelReportEventOpt) ProtoMessage()    {}
func (*ChannelReportEventOpt) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkGreenbaba, []int{4}
}

func (m *ChannelReportEventOpt) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelReportEventOpt) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelReportEventOpt) GetChannelDisplayid() uint32 {
	if m != nil {
		return m.ChannelDisplayid
	}
	return 0
}

func (m *ChannelReportEventOpt) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ChannelReportEventOpt) GetChannelBindId() uint32 {
	if m != nil {
		return m.ChannelBindId
	}
	return 0
}

func (m *ChannelReportEventOpt) GetChannelSchemeId() uint32 {
	if m != nil {
		return m.ChannelSchemeId
	}
	return 0
}

func (m *ChannelReportEventOpt) GetChannelSchemeName() string {
	if m != nil {
		return m.ChannelSchemeName
	}
	return ""
}

func (m *ChannelReportEventOpt) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type UserReportEventOpt struct {
	TargetUid      uint32 `protobuf:"varint,1,req,name=target_uid,json=targetUid" json:"target_uid"`
	TargetNickname string `protobuf:"bytes,2,req,name=target_nickname,json=targetNickname" json:"target_nickname"`
	TargetTtid     string `protobuf:"bytes,3,req,name=target_ttid,json=targetTtid" json:"target_ttid"`
}

func (m *UserReportEventOpt) Reset()                    { *m = UserReportEventOpt{} }
func (m *UserReportEventOpt) String() string            { return proto.CompactTextString(m) }
func (*UserReportEventOpt) ProtoMessage()               {}
func (*UserReportEventOpt) Descriptor() ([]byte, []int) { return fileDescriptorKfkGreenbaba, []int{5} }

func (m *UserReportEventOpt) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *UserReportEventOpt) GetTargetNickname() string {
	if m != nil {
		return m.TargetNickname
	}
	return ""
}

func (m *UserReportEventOpt) GetTargetTtid() string {
	if m != nil {
		return m.TargetTtid
	}
	return ""
}

type MusicReportEvent struct {
	ReportUid        uint32 `protobuf:"varint,1,req,name=report_uid,json=reportUid" json:"report_uid"`
	OwnerAccount     string `protobuf:"bytes,2,req,name=owner_account,json=ownerAccount" json:"owner_account"`
	ChannelId        uint32 `protobuf:"varint,3,req,name=channel_id,json=channelId" json:"channel_id"`
	MusicId          string `protobuf:"bytes,4,req,name=music_id,json=musicId" json:"music_id"`
	MusicName        string `protobuf:"bytes,5,req,name=music_name,json=musicName" json:"music_name"`
	MusicSinger      string `protobuf:"bytes,6,req,name=music_singer,json=musicSinger" json:"music_singer"`
	MusicUrl         string `protobuf:"bytes,7,opt,name=music_url,json=musicUrl" json:"music_url"`
	ChannelDisplayid uint32 `protobuf:"varint,8,opt,name=channel_displayid,json=channelDisplayid" json:"channel_displayid"`
	ChannelViewId    string `protobuf:"bytes,9,opt,name=channel_view_id,json=channelViewId" json:"channel_view_id"`
}

func (m *MusicReportEvent) Reset()                    { *m = MusicReportEvent{} }
func (m *MusicReportEvent) String() string            { return proto.CompactTextString(m) }
func (*MusicReportEvent) ProtoMessage()               {}
func (*MusicReportEvent) Descriptor() ([]byte, []int) { return fileDescriptorKfkGreenbaba, []int{6} }

func (m *MusicReportEvent) GetReportUid() uint32 {
	if m != nil {
		return m.ReportUid
	}
	return 0
}

func (m *MusicReportEvent) GetOwnerAccount() string {
	if m != nil {
		return m.OwnerAccount
	}
	return ""
}

func (m *MusicReportEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MusicReportEvent) GetMusicId() string {
	if m != nil {
		return m.MusicId
	}
	return ""
}

func (m *MusicReportEvent) GetMusicName() string {
	if m != nil {
		return m.MusicName
	}
	return ""
}

func (m *MusicReportEvent) GetMusicSinger() string {
	if m != nil {
		return m.MusicSinger
	}
	return ""
}

func (m *MusicReportEvent) GetMusicUrl() string {
	if m != nil {
		return m.MusicUrl
	}
	return ""
}

func (m *MusicReportEvent) GetChannelDisplayid() uint32 {
	if m != nil {
		return m.ChannelDisplayid
	}
	return 0
}

func (m *MusicReportEvent) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type ChatReportEvent struct {
	ReportUid      uint32 `protobuf:"varint,1,req,name=report_uid,json=reportUid" json:"report_uid"`
	TargetUid      uint32 `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
	TargetNickname string `protobuf:"bytes,3,req,name=target_nickname,json=targetNickname" json:"target_nickname"`
	Reason         string `protobuf:"bytes,4,req,name=reason" json:"reason"`
	Content        string `protobuf:"bytes,5,req,name=content" json:"content"`
}

func (m *ChatReportEvent) Reset()                    { *m = ChatReportEvent{} }
func (m *ChatReportEvent) String() string            { return proto.CompactTextString(m) }
func (*ChatReportEvent) ProtoMessage()               {}
func (*ChatReportEvent) Descriptor() ([]byte, []int) { return fileDescriptorKfkGreenbaba, []int{7} }

func (m *ChatReportEvent) GetReportUid() uint32 {
	if m != nil {
		return m.ReportUid
	}
	return 0
}

func (m *ChatReportEvent) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *ChatReportEvent) GetTargetNickname() string {
	if m != nil {
		return m.TargetNickname
	}
	return ""
}

func (m *ChatReportEvent) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *ChatReportEvent) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type GroupReportEventOpt struct {
	GroupId        uint32            `protobuf:"varint,1,req,name=group_id,json=groupId" json:"group_id"`
	GroupName      string            `protobuf:"bytes,2,req,name=group_name,json=groupName" json:"group_name"`
	GroupDisplayid uint32            `protobuf:"varint,3,req,name=group_displayid,json=groupDisplayid" json:"group_displayid"`
	GuildId        uint32            `protobuf:"varint,4,opt,name=guild_id,json=guildId" json:"guild_id"`
	HeadMd5        string            `protobuf:"bytes,5,opt,name=head_md5,json=headMd5" json:"head_md5"`
	GuildAdmin     []*UserSimpleInfo `protobuf:"bytes,6,rep,name=guild_admin,json=guildAdmin" json:"guild_admin,omitempty"`
	GroupType      uint32            `protobuf:"varint,7,opt,name=group_type,json=groupType" json:"group_type"`
}

func (m *GroupReportEventOpt) Reset()                    { *m = GroupReportEventOpt{} }
func (m *GroupReportEventOpt) String() string            { return proto.CompactTextString(m) }
func (*GroupReportEventOpt) ProtoMessage()               {}
func (*GroupReportEventOpt) Descriptor() ([]byte, []int) { return fileDescriptorKfkGreenbaba, []int{8} }

func (m *GroupReportEventOpt) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupReportEventOpt) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *GroupReportEventOpt) GetGroupDisplayid() uint32 {
	if m != nil {
		return m.GroupDisplayid
	}
	return 0
}

func (m *GroupReportEventOpt) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GroupReportEventOpt) GetHeadMd5() string {
	if m != nil {
		return m.HeadMd5
	}
	return ""
}

func (m *GroupReportEventOpt) GetGuildAdmin() []*UserSimpleInfo {
	if m != nil {
		return m.GuildAdmin
	}
	return nil
}

func (m *GroupReportEventOpt) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

type UgcReportEventOpt struct {
	PostId    string `protobuf:"bytes,1,req,name=post_id,json=postId" json:"post_id"`
	CommentId string `protobuf:"bytes,2,opt,name=comment_id,json=commentId" json:"comment_id"`
	Comment   string `protobuf:"bytes,3,opt,name=comment" json:"comment"`
}

func (m *UgcReportEventOpt) Reset()                    { *m = UgcReportEventOpt{} }
func (m *UgcReportEventOpt) String() string            { return proto.CompactTextString(m) }
func (*UgcReportEventOpt) ProtoMessage()               {}
func (*UgcReportEventOpt) Descriptor() ([]byte, []int) { return fileDescriptorKfkGreenbaba, []int{9} }

func (m *UgcReportEventOpt) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UgcReportEventOpt) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *UgcReportEventOpt) GetComment() string {
	if m != nil {
		return m.Comment
	}
	return ""
}

type GuildReportEventOpt struct {
	GuildId    uint32            `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GuildName  string            `protobuf:"bytes,2,req,name=guild_name,json=guildName" json:"guild_name"`
	ShortId    uint32            `protobuf:"varint,3,opt,name=short_id,json=shortId" json:"short_id"`
	MainGroup  uint32            `protobuf:"varint,4,opt,name=main_group,json=mainGroup" json:"main_group"`
	Intro      string            `protobuf:"bytes,5,opt,name=intro" json:"intro"`
	Manifesto  string            `protobuf:"bytes,6,opt,name=manifesto" json:"manifesto"`
	Notify     string            `protobuf:"bytes,7,opt,name=notify" json:"notify"`
	HeadMd5    string            `protobuf:"bytes,8,opt,name=head_md5,json=headMd5" json:"head_md5"`
	Content    string            `protobuf:"bytes,9,opt,name=content" json:"content"`
	GuildAdmin []*UserSimpleInfo `protobuf:"bytes,10,rep,name=guild_admin,json=guildAdmin" json:"guild_admin,omitempty"`
}

func (m *GuildReportEventOpt) Reset()                    { *m = GuildReportEventOpt{} }
func (m *GuildReportEventOpt) String() string            { return proto.CompactTextString(m) }
func (*GuildReportEventOpt) ProtoMessage()               {}
func (*GuildReportEventOpt) Descriptor() ([]byte, []int) { return fileDescriptorKfkGreenbaba, []int{10} }

func (m *GuildReportEventOpt) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildReportEventOpt) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *GuildReportEventOpt) GetShortId() uint32 {
	if m != nil {
		return m.ShortId
	}
	return 0
}

func (m *GuildReportEventOpt) GetMainGroup() uint32 {
	if m != nil {
		return m.MainGroup
	}
	return 0
}

func (m *GuildReportEventOpt) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *GuildReportEventOpt) GetManifesto() string {
	if m != nil {
		return m.Manifesto
	}
	return ""
}

func (m *GuildReportEventOpt) GetNotify() string {
	if m != nil {
		return m.Notify
	}
	return ""
}

func (m *GuildReportEventOpt) GetHeadMd5() string {
	if m != nil {
		return m.HeadMd5
	}
	return ""
}

func (m *GuildReportEventOpt) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GuildReportEventOpt) GetGuildAdmin() []*UserSimpleInfo {
	if m != nil {
		return m.GuildAdmin
	}
	return nil
}

type ChatCardReportEventOpt struct {
	Slogan  string `protobuf:"bytes,1,req,name=slogan" json:"slogan"`
	Bg      string `protobuf:"bytes,2,req,name=bg" json:"bg"`
	Version uint32 `protobuf:"varint,3,req,name=version" json:"version"`
}

func (m *ChatCardReportEventOpt) Reset()         { *m = ChatCardReportEventOpt{} }
func (m *ChatCardReportEventOpt) String() string { return proto.CompactTextString(m) }
func (*ChatCardReportEventOpt) ProtoMessage()    {}
func (*ChatCardReportEventOpt) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkGreenbaba, []int{11}
}

func (m *ChatCardReportEventOpt) GetSlogan() string {
	if m != nil {
		return m.Slogan
	}
	return ""
}

func (m *ChatCardReportEventOpt) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *ChatCardReportEventOpt) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

type MaskedCallAudio struct {
	Url            string `protobuf:"bytes,1,req,name=url" json:"url"`
	StartTimestamp uint64 `protobuf:"varint,2,req,name=start_timestamp,json=startTimestamp" json:"start_timestamp"`
}

func (m *MaskedCallAudio) Reset()                    { *m = MaskedCallAudio{} }
func (m *MaskedCallAudio) String() string            { return proto.CompactTextString(m) }
func (*MaskedCallAudio) ProtoMessage()               {}
func (*MaskedCallAudio) Descriptor() ([]byte, []int) { return fileDescriptorKfkGreenbaba, []int{12} }

func (m *MaskedCallAudio) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *MaskedCallAudio) GetStartTimestamp() uint64 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

type MaskedCallReportEventOpt struct {
	CallId      uint32             `protobuf:"varint,1,req,name=call_id,json=callId" json:"call_id"`
	ReportAudio []*MaskedCallAudio `protobuf:"bytes,2,rep,name=report_audio,json=reportAudio" json:"report_audio,omitempty"`
	TargetAudio []*MaskedCallAudio `protobuf:"bytes,3,rep,name=target_audio,json=targetAudio" json:"target_audio,omitempty"`
}

func (m *MaskedCallReportEventOpt) Reset()         { *m = MaskedCallReportEventOpt{} }
func (m *MaskedCallReportEventOpt) String() string { return proto.CompactTextString(m) }
func (*MaskedCallReportEventOpt) ProtoMessage()    {}
func (*MaskedCallReportEventOpt) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkGreenbaba, []int{13}
}

func (m *MaskedCallReportEventOpt) GetCallId() uint32 {
	if m != nil {
		return m.CallId
	}
	return 0
}

func (m *MaskedCallReportEventOpt) GetReportAudio() []*MaskedCallAudio {
	if m != nil {
		return m.ReportAudio
	}
	return nil
}

func (m *MaskedCallReportEventOpt) GetTargetAudio() []*MaskedCallAudio {
	if m != nil {
		return m.TargetAudio
	}
	return nil
}

type SlipNoteReportEventOpt struct {
	SlipNoteOwnerUid uint32 `protobuf:"varint,1,req,name=slip_note_owner_uid,json=slipNoteOwnerUid" json:"slip_note_owner_uid"`
	SlipNoteContent  string `protobuf:"bytes,2,req,name=slip_note_content,json=slipNoteContent" json:"slip_note_content"`
}

func (m *SlipNoteReportEventOpt) Reset()         { *m = SlipNoteReportEventOpt{} }
func (m *SlipNoteReportEventOpt) String() string { return proto.CompactTextString(m) }
func (*SlipNoteReportEventOpt) ProtoMessage()    {}
func (*SlipNoteReportEventOpt) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkGreenbaba, []int{14}
}

func (m *SlipNoteReportEventOpt) GetSlipNoteOwnerUid() uint32 {
	if m != nil {
		return m.SlipNoteOwnerUid
	}
	return 0
}

func (m *SlipNoteReportEventOpt) GetSlipNoteContent() string {
	if m != nil {
		return m.SlipNoteContent
	}
	return ""
}

type GamePalCardReportEventOpt struct {
	CardId       string   `protobuf:"bytes,1,opt,name=card_id,json=cardId" json:"card_id"`
	SocialDecl   string   `protobuf:"bytes,2,opt,name=social_decl,json=socialDecl" json:"social_decl"`
	ImageUrlList []string `protobuf:"bytes,3,rep,name=image_url_list,json=imageUrlList" json:"image_url_list,omitempty"`
	CardUid      uint32   `protobuf:"varint,4,opt,name=card_uid,json=cardUid" json:"card_uid"`
}

func (m *GamePalCardReportEventOpt) Reset()         { *m = GamePalCardReportEventOpt{} }
func (m *GamePalCardReportEventOpt) String() string { return proto.CompactTextString(m) }
func (*GamePalCardReportEventOpt) ProtoMessage()    {}
func (*GamePalCardReportEventOpt) Descriptor() ([]byte, []int) {
	return fileDescriptorKfkGreenbaba, []int{15}
}

func (m *GamePalCardReportEventOpt) GetCardId() string {
	if m != nil {
		return m.CardId
	}
	return ""
}

func (m *GamePalCardReportEventOpt) GetSocialDecl() string {
	if m != nil {
		return m.SocialDecl
	}
	return ""
}

func (m *GamePalCardReportEventOpt) GetImageUrlList() []string {
	if m != nil {
		return m.ImageUrlList
	}
	return nil
}

func (m *GamePalCardReportEventOpt) GetCardUid() uint32 {
	if m != nil {
		return m.CardUid
	}
	return 0
}

func init() {
	proto.RegisterType((*GreenBabaSanctionEvent)(nil), "kafkagreenbaba.GreenBabaSanctionEvent")
	proto.RegisterType((*ReportMessageContent)(nil), "kafkagreenbaba.ReportMessageContent")
	proto.RegisterType((*GreenBabaReportEvent)(nil), "kafkagreenbaba.GreenBabaReportEvent")
	proto.RegisterType((*UserSimpleInfo)(nil), "kafkagreenbaba.UserSimpleInfo")
	proto.RegisterType((*ChannelReportEventOpt)(nil), "kafkagreenbaba.ChannelReportEventOpt")
	proto.RegisterType((*UserReportEventOpt)(nil), "kafkagreenbaba.UserReportEventOpt")
	proto.RegisterType((*MusicReportEvent)(nil), "kafkagreenbaba.MusicReportEvent")
	proto.RegisterType((*ChatReportEvent)(nil), "kafkagreenbaba.ChatReportEvent")
	proto.RegisterType((*GroupReportEventOpt)(nil), "kafkagreenbaba.GroupReportEventOpt")
	proto.RegisterType((*UgcReportEventOpt)(nil), "kafkagreenbaba.UgcReportEventOpt")
	proto.RegisterType((*GuildReportEventOpt)(nil), "kafkagreenbaba.GuildReportEventOpt")
	proto.RegisterType((*ChatCardReportEventOpt)(nil), "kafkagreenbaba.ChatCardReportEventOpt")
	proto.RegisterType((*MaskedCallAudio)(nil), "kafkagreenbaba.MaskedCallAudio")
	proto.RegisterType((*MaskedCallReportEventOpt)(nil), "kafkagreenbaba.MaskedCallReportEventOpt")
	proto.RegisterType((*SlipNoteReportEventOpt)(nil), "kafkagreenbaba.SlipNoteReportEventOpt")
	proto.RegisterType((*GamePalCardReportEventOpt)(nil), "kafkagreenbaba.GamePalCardReportEventOpt")
	proto.RegisterEnum("kafkagreenbaba.ENUM_TARGET_TYPE", ENUM_TARGET_TYPE_name, ENUM_TARGET_TYPE_value)
	proto.RegisterEnum("kafkagreenbaba.ENUM_SANCTION_OP_TYPE", ENUM_SANCTION_OP_TYPE_name, ENUM_SANCTION_OP_TYPE_value)
	proto.RegisterEnum("kafkagreenbaba.ENUM_BANNED_TYPE", ENUM_BANNED_TYPE_name, ENUM_BANNED_TYPE_value)
	proto.RegisterEnum("kafkagreenbaba.ENUM_REPORT_TYPE", ENUM_REPORT_TYPE_name, ENUM_REPORT_TYPE_value)
	proto.RegisterEnum("kafkagreenbaba.ENUM_REPORT_MESSAGE_TYPE", ENUM_REPORT_MESSAGE_TYPE_name, ENUM_REPORT_MESSAGE_TYPE_value)
}
func (m *GreenBabaSanctionEvent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GreenBabaSanctionEvent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.TargetType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.BannedType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.Second))
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.SanctionType))
	dAtA[i] = 0x32
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.SanctionReason)))
	i += copy(dAtA[i:], m.SanctionReason)
	return i, nil
}

func (m *ReportMessageContent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportMessageContent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.Msg)))
	i += copy(dAtA[i:], m.Msg)
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.FromUid))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.FromAccount)))
	i += copy(dAtA[i:], m.FromAccount)
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.MsgSeqId))
	return i, nil
}

func (m *GreenBabaReportEvent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GreenBabaReportEvent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.TargetType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.ReportType))
	dAtA[i] = 0x22
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.ReportReason)))
	i += copy(dAtA[i:], m.ReportReason)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.ReportImgUrl)))
	i += copy(dAtA[i:], m.ReportImgUrl)
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.ReportUid))
	if m.OptBin != nil {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.OptBin)))
		i += copy(dAtA[i:], m.OptBin)
	}
	dAtA[i] = 0x40
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.Timestamp))
	if m.ReportUser != nil {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.ReportUser.Size()))
		n1, err := m.ReportUser.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.TargetUser != nil {
		dAtA[i] = 0x52
		i++
		i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.TargetUser.Size()))
		n2, err := m.TargetUser.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if len(m.ReportTypeList) > 0 {
		for _, num := range m.ReportTypeList {
			dAtA[i] = 0x58
			i++
			i = encodeVarintKfkGreenbaba(dAtA, i, uint64(num))
		}
	}
	if len(m.ContentList) > 0 {
		for _, msg := range m.ContentList {
			dAtA[i] = 0x62
			i++
			i = encodeVarintKfkGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.ReasonList) > 0 {
		for _, s := range m.ReasonList {
			dAtA[i] = 0x6a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x70
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.MarketId))
	return i, nil
}

func (m *UserSimpleInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserSimpleInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.Ttid)))
	i += copy(dAtA[i:], m.Ttid)
	return i, nil
}

func (m *ChannelReportEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelReportEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.ChannelDisplayid))
	dAtA[i] = 0x22
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.ChannelName)))
	i += copy(dAtA[i:], m.ChannelName)
	dAtA[i] = 0x28
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.ChannelBindId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.ChannelSchemeId))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.ChannelSchemeName)))
	i += copy(dAtA[i:], m.ChannelSchemeName)
	dAtA[i] = 0x42
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.ChannelViewId)))
	i += copy(dAtA[i:], m.ChannelViewId)
	return i, nil
}

func (m *UserReportEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserReportEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.TargetNickname)))
	i += copy(dAtA[i:], m.TargetNickname)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.TargetTtid)))
	i += copy(dAtA[i:], m.TargetTtid)
	return i, nil
}

func (m *MusicReportEvent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MusicReportEvent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.ReportUid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.OwnerAccount)))
	i += copy(dAtA[i:], m.OwnerAccount)
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.MusicId)))
	i += copy(dAtA[i:], m.MusicId)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.MusicName)))
	i += copy(dAtA[i:], m.MusicName)
	dAtA[i] = 0x32
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.MusicSinger)))
	i += copy(dAtA[i:], m.MusicSinger)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.MusicUrl)))
	i += copy(dAtA[i:], m.MusicUrl)
	dAtA[i] = 0x40
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.ChannelDisplayid))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.ChannelViewId)))
	i += copy(dAtA[i:], m.ChannelViewId)
	return i, nil
}

func (m *ChatReportEvent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChatReportEvent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.ReportUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.TargetNickname)))
	i += copy(dAtA[i:], m.TargetNickname)
	dAtA[i] = 0x22
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	return i, nil
}

func (m *GroupReportEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupReportEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.GroupName)))
	i += copy(dAtA[i:], m.GroupName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.GroupDisplayid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.HeadMd5)))
	i += copy(dAtA[i:], m.HeadMd5)
	if len(m.GuildAdmin) > 0 {
		for _, msg := range m.GuildAdmin {
			dAtA[i] = 0x32
			i++
			i = encodeVarintKfkGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.GroupType))
	return i, nil
}

func (m *UgcReportEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UgcReportEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.PostId)))
	i += copy(dAtA[i:], m.PostId)
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.CommentId)))
	i += copy(dAtA[i:], m.CommentId)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.Comment)))
	i += copy(dAtA[i:], m.Comment)
	return i, nil
}

func (m *GuildReportEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildReportEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.GuildName)))
	i += copy(dAtA[i:], m.GuildName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.ShortId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.MainGroup))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.Intro)))
	i += copy(dAtA[i:], m.Intro)
	dAtA[i] = 0x32
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.Manifesto)))
	i += copy(dAtA[i:], m.Manifesto)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.Notify)))
	i += copy(dAtA[i:], m.Notify)
	dAtA[i] = 0x42
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.HeadMd5)))
	i += copy(dAtA[i:], m.HeadMd5)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	if len(m.GuildAdmin) > 0 {
		for _, msg := range m.GuildAdmin {
			dAtA[i] = 0x52
			i++
			i = encodeVarintKfkGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ChatCardReportEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChatCardReportEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.Slogan)))
	i += copy(dAtA[i:], m.Slogan)
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.Bg)))
	i += copy(dAtA[i:], m.Bg)
	dAtA[i] = 0x18
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.Version))
	return i, nil
}

func (m *MaskedCallAudio) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MaskedCallAudio) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.StartTimestamp))
	return i, nil
}

func (m *MaskedCallReportEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MaskedCallReportEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.CallId))
	if len(m.ReportAudio) > 0 {
		for _, msg := range m.ReportAudio {
			dAtA[i] = 0x12
			i++
			i = encodeVarintKfkGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.TargetAudio) > 0 {
		for _, msg := range m.TargetAudio {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintKfkGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SlipNoteReportEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SlipNoteReportEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.SlipNoteOwnerUid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.SlipNoteContent)))
	i += copy(dAtA[i:], m.SlipNoteContent)
	return i, nil
}

func (m *GamePalCardReportEventOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GamePalCardReportEventOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.CardId)))
	i += copy(dAtA[i:], m.CardId)
	dAtA[i] = 0x12
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(len(m.SocialDecl)))
	i += copy(dAtA[i:], m.SocialDecl)
	if len(m.ImageUrlList) > 0 {
		for _, s := range m.ImageUrlList {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintKfkGreenbaba(dAtA, i, uint64(m.CardUid))
	return i, nil
}

func encodeFixed64KfkGreenbaba(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32KfkGreenbaba(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintKfkGreenbaba(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GreenBabaSanctionEvent) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkGreenbaba(uint64(m.TargetType))
	n += 1 + sovKfkGreenbaba(uint64(m.Id))
	n += 1 + sovKfkGreenbaba(uint64(m.BannedType))
	n += 1 + sovKfkGreenbaba(uint64(m.Second))
	n += 1 + sovKfkGreenbaba(uint64(m.SanctionType))
	l = len(m.SanctionReason)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	return n
}

func (m *ReportMessageContent) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkGreenbaba(uint64(m.Type))
	l = len(m.Msg)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	n += 1 + sovKfkGreenbaba(uint64(m.Timestamp))
	n += 1 + sovKfkGreenbaba(uint64(m.FromUid))
	l = len(m.FromAccount)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	n += 1 + sovKfkGreenbaba(uint64(m.MsgSeqId))
	return n
}

func (m *GreenBabaReportEvent) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkGreenbaba(uint64(m.TargetType))
	n += 1 + sovKfkGreenbaba(uint64(m.Id))
	n += 1 + sovKfkGreenbaba(uint64(m.ReportType))
	l = len(m.ReportReason)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.ReportImgUrl)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	n += 1 + sovKfkGreenbaba(uint64(m.ReportUid))
	if m.OptBin != nil {
		l = len(m.OptBin)
		n += 1 + l + sovKfkGreenbaba(uint64(l))
	}
	n += 1 + sovKfkGreenbaba(uint64(m.Timestamp))
	if m.ReportUser != nil {
		l = m.ReportUser.Size()
		n += 1 + l + sovKfkGreenbaba(uint64(l))
	}
	if m.TargetUser != nil {
		l = m.TargetUser.Size()
		n += 1 + l + sovKfkGreenbaba(uint64(l))
	}
	if len(m.ReportTypeList) > 0 {
		for _, e := range m.ReportTypeList {
			n += 1 + sovKfkGreenbaba(uint64(e))
		}
	}
	if len(m.ContentList) > 0 {
		for _, e := range m.ContentList {
			l = e.Size()
			n += 1 + l + sovKfkGreenbaba(uint64(l))
		}
	}
	if len(m.ReasonList) > 0 {
		for _, s := range m.ReasonList {
			l = len(s)
			n += 1 + l + sovKfkGreenbaba(uint64(l))
		}
	}
	n += 1 + sovKfkGreenbaba(uint64(m.MarketId))
	return n
}

func (m *UserSimpleInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkGreenbaba(uint64(m.Uid))
	l = len(m.Nickname)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.Ttid)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	return n
}

func (m *ChannelReportEventOpt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkGreenbaba(uint64(m.ChannelId))
	n += 1 + sovKfkGreenbaba(uint64(m.ChannelType))
	n += 1 + sovKfkGreenbaba(uint64(m.ChannelDisplayid))
	l = len(m.ChannelName)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	n += 1 + sovKfkGreenbaba(uint64(m.ChannelBindId))
	n += 1 + sovKfkGreenbaba(uint64(m.ChannelSchemeId))
	l = len(m.ChannelSchemeName)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.ChannelViewId)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	return n
}

func (m *UserReportEventOpt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkGreenbaba(uint64(m.TargetUid))
	l = len(m.TargetNickname)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.TargetTtid)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	return n
}

func (m *MusicReportEvent) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkGreenbaba(uint64(m.ReportUid))
	l = len(m.OwnerAccount)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	n += 1 + sovKfkGreenbaba(uint64(m.ChannelId))
	l = len(m.MusicId)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.MusicName)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.MusicSinger)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.MusicUrl)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	n += 1 + sovKfkGreenbaba(uint64(m.ChannelDisplayid))
	l = len(m.ChannelViewId)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	return n
}

func (m *ChatReportEvent) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkGreenbaba(uint64(m.ReportUid))
	n += 1 + sovKfkGreenbaba(uint64(m.TargetUid))
	l = len(m.TargetNickname)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.Reason)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	return n
}

func (m *GroupReportEventOpt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkGreenbaba(uint64(m.GroupId))
	l = len(m.GroupName)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	n += 1 + sovKfkGreenbaba(uint64(m.GroupDisplayid))
	n += 1 + sovKfkGreenbaba(uint64(m.GuildId))
	l = len(m.HeadMd5)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	if len(m.GuildAdmin) > 0 {
		for _, e := range m.GuildAdmin {
			l = e.Size()
			n += 1 + l + sovKfkGreenbaba(uint64(l))
		}
	}
	n += 1 + sovKfkGreenbaba(uint64(m.GroupType))
	return n
}

func (m *UgcReportEventOpt) Size() (n int) {
	var l int
	_ = l
	l = len(m.PostId)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.CommentId)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.Comment)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	return n
}

func (m *GuildReportEventOpt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkGreenbaba(uint64(m.GuildId))
	l = len(m.GuildName)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	n += 1 + sovKfkGreenbaba(uint64(m.ShortId))
	n += 1 + sovKfkGreenbaba(uint64(m.MainGroup))
	l = len(m.Intro)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.Manifesto)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.Notify)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.HeadMd5)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	if len(m.GuildAdmin) > 0 {
		for _, e := range m.GuildAdmin {
			l = e.Size()
			n += 1 + l + sovKfkGreenbaba(uint64(l))
		}
	}
	return n
}

func (m *ChatCardReportEventOpt) Size() (n int) {
	var l int
	_ = l
	l = len(m.Slogan)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.Bg)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	n += 1 + sovKfkGreenbaba(uint64(m.Version))
	return n
}

func (m *MaskedCallAudio) Size() (n int) {
	var l int
	_ = l
	l = len(m.Url)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	n += 1 + sovKfkGreenbaba(uint64(m.StartTimestamp))
	return n
}

func (m *MaskedCallReportEventOpt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkGreenbaba(uint64(m.CallId))
	if len(m.ReportAudio) > 0 {
		for _, e := range m.ReportAudio {
			l = e.Size()
			n += 1 + l + sovKfkGreenbaba(uint64(l))
		}
	}
	if len(m.TargetAudio) > 0 {
		for _, e := range m.TargetAudio {
			l = e.Size()
			n += 1 + l + sovKfkGreenbaba(uint64(l))
		}
	}
	return n
}

func (m *SlipNoteReportEventOpt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkGreenbaba(uint64(m.SlipNoteOwnerUid))
	l = len(m.SlipNoteContent)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	return n
}

func (m *GamePalCardReportEventOpt) Size() (n int) {
	var l int
	_ = l
	l = len(m.CardId)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	l = len(m.SocialDecl)
	n += 1 + l + sovKfkGreenbaba(uint64(l))
	if len(m.ImageUrlList) > 0 {
		for _, s := range m.ImageUrlList {
			l = len(s)
			n += 1 + l + sovKfkGreenbaba(uint64(l))
		}
	}
	n += 1 + sovKfkGreenbaba(uint64(m.CardUid))
	return n
}

func sovKfkGreenbaba(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozKfkGreenbaba(x uint64) (n int) {
	return sovKfkGreenbaba(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *GreenBabaSanctionEvent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GreenBabaSanctionEvent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GreenBabaSanctionEvent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetType", wireType)
			}
			m.TargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BannedType", wireType)
			}
			m.BannedType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BannedType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Second", wireType)
			}
			m.Second = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Second |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SanctionType", wireType)
			}
			m.SanctionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SanctionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SanctionReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SanctionReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("target_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportMessageContent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ReportMessageContent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ReportMessageContent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FromUid", wireType)
			}
			m.FromUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FromAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FromAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MsgSeqId", wireType)
			}
			m.MsgSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GreenBabaReportEvent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GreenBabaReportEvent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GreenBabaReportEvent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetType", wireType)
			}
			m.TargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ReportType", wireType)
			}
			m.ReportType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReportType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ReportReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ReportReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ReportImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ReportImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ReportUid", wireType)
			}
			m.ReportUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReportUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OptBin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OptBin = append(m.OptBin[:0], dAtA[iNdEx:postIndex]...)
			if m.OptBin == nil {
				m.OptBin = []byte{}
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ReportUser", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ReportUser == nil {
				m.ReportUser = &UserSimpleInfo{}
			}
			if err := m.ReportUser.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetUser", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TargetUser == nil {
				m.TargetUser = &UserSimpleInfo{}
			}
			if err := m.TargetUser.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowKfkGreenbaba
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ReportTypeList = append(m.ReportTypeList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowKfkGreenbaba
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthKfkGreenbaba
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowKfkGreenbaba
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ReportTypeList = append(m.ReportTypeList, v)
				}
			} else {
				return fmt1.Errorf("proto: wrong wireType = %d for field ReportTypeList", wireType)
			}
		case 12:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ContentList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ContentList = append(m.ContentList, &ReportMessageContent{})
			if err := m.ContentList[len(m.ContentList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ReasonList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ReasonList = append(m.ReasonList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("target_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserSimpleInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UserSimpleInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UserSimpleInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Ttid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ttid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("nickname")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("ttid")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelReportEventOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChannelReportEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChannelReportEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelDisplayid", wireType)
			}
			m.ChannelDisplayid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ChannelName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelBindId", wireType)
			}
			m.ChannelBindId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelBindId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelSchemeId", wireType)
			}
			m.ChannelSchemeId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelSchemeId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelSchemeName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ChannelSchemeName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelViewId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ChannelViewId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("channel_type")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserReportEventOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UserReportEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UserReportEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetTtid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetTtid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("target_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("target_nickname")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("target_ttid")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MusicReportEvent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: MusicReportEvent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: MusicReportEvent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ReportUid", wireType)
			}
			m.ReportUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReportUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OwnerAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OwnerAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MusicId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MusicId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MusicName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MusicName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MusicSinger", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MusicSinger = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MusicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MusicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelDisplayid", wireType)
			}
			m.ChannelDisplayid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelViewId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ChannelViewId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("report_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("owner_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("music_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("music_name")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("music_singer")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChatReportEvent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChatReportEvent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChatReportEvent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ReportUid", wireType)
			}
			m.ReportUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReportUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("report_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("target_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("target_nickname")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("reason")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupReportEventOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GroupReportEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GroupReportEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GroupName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GroupName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GroupDisplayid", wireType)
			}
			m.GroupDisplayid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupDisplayid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field HeadMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HeadMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GuildAdmin", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GuildAdmin = append(m.GuildAdmin, &UserSimpleInfo{})
			if err := m.GuildAdmin[len(m.GuildAdmin)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("group_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("group_displayid")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UgcReportEventOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UgcReportEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UgcReportEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field PostId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PostId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommentId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Comment", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Comment = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("post_id")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildReportEventOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GuildReportEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GuildReportEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GuildName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GuildName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ShortId", wireType)
			}
			m.ShortId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ShortId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MainGroup", wireType)
			}
			m.MainGroup = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MainGroup |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Intro", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Intro = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Manifesto", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Manifesto = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Notify", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Notify = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field HeadMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HeadMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field GuildAdmin", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GuildAdmin = append(m.GuildAdmin, &UserSimpleInfo{})
			if err := m.GuildAdmin[len(m.GuildAdmin)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("guild_name")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChatCardReportEventOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChatCardReportEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChatCardReportEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Slogan", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Slogan = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Bg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Bg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("slogan")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("bg")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("version")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MaskedCallAudio) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: MaskedCallAudio: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: MaskedCallAudio: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field StartTimestamp", wireType)
			}
			m.StartTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTimestamp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("start_timestamp")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MaskedCallReportEventOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: MaskedCallReportEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: MaskedCallReportEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CallId", wireType)
			}
			m.CallId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CallId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ReportAudio", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ReportAudio = append(m.ReportAudio, &MaskedCallAudio{})
			if err := m.ReportAudio[len(m.ReportAudio)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TargetAudio", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetAudio = append(m.TargetAudio, &MaskedCallAudio{})
			if err := m.TargetAudio[len(m.TargetAudio)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("call_id")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SlipNoteReportEventOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SlipNoteReportEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SlipNoteReportEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SlipNoteOwnerUid", wireType)
			}
			m.SlipNoteOwnerUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SlipNoteOwnerUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SlipNoteContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SlipNoteContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("slip_note_owner_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("slip_note_content")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GamePalCardReportEventOpt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GamePalCardReportEventOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GamePalCardReportEventOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CardId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SocialDecl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SocialDecl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ImageUrlList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImageUrlList = append(m.ImageUrlList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CardUid", wireType)
			}
			m.CardUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipKfkGreenbaba(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowKfkGreenbaba
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKfkGreenbaba
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthKfkGreenbaba
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowKfkGreenbaba
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipKfkGreenbaba(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt1.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthKfkGreenbaba = fmt1.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowKfkGreenbaba   = fmt1.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/minToolkit/kafka/pb/kfk_greenbaba.proto", fileDescriptorKfkGreenbaba)
}

var fileDescriptorKfkGreenbaba = []byte{
	// 1817 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x57, 0x3b, 0x8f, 0xdb, 0xd8,
	0x15, 0x36, 0x25, 0x79, 0x24, 0x1d, 0x3d, 0x86, 0x73, 0x67, 0x3c, 0x2b, 0xef, 0xda, 0x33, 0x8a,
	0xb2, 0xc1, 0x8e, 0x27, 0x59, 0x7b, 0xb3, 0xc9, 0xd6, 0x01, 0xa5, 0x61, 0x64, 0x62, 0x25, 0x52,
	0xa0, 0x24, 0x67, 0x5d, 0x11, 0x14, 0x79, 0x47, 0x73, 0x31, 0x7c, 0x68, 0x49, 0x8e, 0x0d, 0x57,
	0x41, 0x8a, 0xd4, 0x09, 0xf2, 0x2b, 0x52, 0xa7, 0x4a, 0x9b, 0xce, 0xe5, 0xd6, 0x29, 0x82, 0xc0,
	0xf9, 0x07, 0x41, 0xaa, 0x54, 0xc1, 0x7d, 0x50, 0x7c, 0x48, 0xbb, 0xb0, 0x81, 0x94, 0xfa, 0xbe,
	0x73, 0xcf, 0x3d, 0x8f, 0xef, 0x1e, 0x1e, 0xc1, 0x4f, 0xe3, 0xc8, 0x79, 0xe6, 0x93, 0x60, 0x11,
	0x86, 0xde, 0x2d, 0x49, 0x9e, 0xdd, 0xda, 0xd7, 0xb7, 0xf6, 0xb3, 0xcd, 0xea, 0xd9, 0xed, 0xf5,
	0xad, 0xb5, 0x8e, 0x30, 0x0e, 0x56, 0xf6, 0xca, 0x7e, 0xba, 0x89, 0xc2, 0x24, 0x44, 0x5d, 0xc6,
	0x6e, 0xd1, 0xc1, 0x7f, 0x24, 0x38, 0x1d, 0xd3, 0x5f, 0x43, 0x7b, 0x65, 0xcf, 0xed, 0xc0, 0x49,
	0x48, 0x18, 0xa8, 0xaf, 0x70, 0x90, 0xa0, 0x9f, 0x40, 0x2b, 0xb1, 0xa3, 0x35, 0x4e, 0xac, 0xe4,
	0xcd, 0x06, 0xf7, 0xa4, 0x7e, 0xe5, 0xa2, 0x33, 0xac, 0xbd, 0xfd, 0xc7, 0xf9, 0x3d, 0x13, 0x38,
	0xb1, 0x78, 0xb3, 0xc1, 0xe8, 0x04, 0x2a, 0xc4, 0xed, 0x55, 0x72, 0x6c, 0x85, 0xb8, 0xf4, 0xf0,
	0xca, 0x0e, 0x02, 0xec, 0xf2, 0xc3, 0xd5, 0xbe, 0x94, 0x1d, 0xe6, 0x04, 0x3b, 0xfc, 0x08, 0x0e,
	0x62, 0xec, 0x84, 0x81, 0xdb, 0xab, 0xe5, 0x2c, 0x04, 0x86, 0x9e, 0x40, 0x27, 0x16, 0x21, 0x71,
	0x37, 0xf7, 0x73, 0x46, 0xed, 0x94, 0x62, 0x8e, 0x3e, 0x87, 0xc3, 0xad, 0x69, 0x84, 0xed, 0x38,
	0x0c, 0x7a, 0x07, 0x7d, 0xe9, 0xa2, 0x29, 0x8c, 0xbb, 0x29, 0x69, 0x32, 0x6e, 0xf0, 0x77, 0x09,
	0x4e, 0x4c, 0xbc, 0x09, 0xa3, 0x64, 0x8a, 0xe3, 0xd8, 0x5e, 0xe3, 0x51, 0x18, 0x24, 0x34, 0xe9,
	0x1e, 0xd4, 0x76, 0xb2, 0x65, 0x08, 0x3a, 0x85, 0xaa, 0x1f, 0xaf, 0x59, 0xa2, 0xa9, 0x57, 0x0a,
	0xa0, 0x01, 0x34, 0x13, 0xe2, 0xe3, 0x38, 0xb1, 0xfd, 0x4d, 0x21, 0xcf, 0x0c, 0x46, 0xe7, 0xd0,
	0xb8, 0x8e, 0x42, 0xdf, 0xba, 0x23, 0xc5, 0x44, 0xeb, 0x14, 0x5d, 0x12, 0x17, 0x7d, 0x06, 0x6d,
	0x66, 0x60, 0x3b, 0x4e, 0x78, 0x17, 0x24, 0x2c, 0xd1, 0xf4, 0x96, 0x16, 0x65, 0x14, 0x4e, 0xa0,
	0x01, 0x80, 0x1f, 0xaf, 0xad, 0x18, 0x7f, 0x6b, 0x11, 0x97, 0xa5, 0x98, 0xfa, 0x6a, 0xf8, 0xf1,
	0x7a, 0x8e, 0xbf, 0xd5, 0xdc, 0xc1, 0xbf, 0x6b, 0x70, 0xb2, 0xed, 0x29, 0xcf, 0xf2, 0xff, 0xd3,
	0xd1, 0x88, 0xf9, 0xda, 0xd3, 0x51, 0x4e, 0xb0, 0xc3, 0x4f, 0xa0, 0x23, 0xcc, 0x44, 0x1b, 0x6a,
	0xb9, 0x54, 0xda, 0x9c, 0xe2, 0x4d, 0x40, 0x97, 0xd0, 0x15, 0xa6, 0xc4, 0x5f, 0x5b, 0x77, 0x91,
	0x57, 0x48, 0x5b, 0xd8, 0x6a, 0xfe, 0x7a, 0x19, 0x79, 0xe8, 0xc7, 0x20, 0x2e, 0x61, 0x35, 0xcc,
	0xe7, 0xdd, 0xe4, 0x38, 0xad, 0xe2, 0x63, 0xa8, 0x87, 0x9b, 0xc4, 0x5a, 0x91, 0xa0, 0x57, 0xef,
	0x4b, 0x17, 0xed, 0x54, 0x4e, 0xe1, 0x26, 0x19, 0x92, 0xa0, 0xd8, 0xa9, 0xc6, 0xfe, 0x4e, 0xfd,
	0x6a, 0x9b, 0xe5, 0x5d, 0x8c, 0xa3, 0x5e, 0xb3, 0x2f, 0x5d, 0xb4, 0xbe, 0x3c, 0x7b, 0x5a, 0x7c,
	0x35, 0x4f, 0x97, 0x31, 0x8e, 0xe6, 0xc4, 0xdf, 0x78, 0x58, 0x0b, 0xae, 0xc3, 0x34, 0x7f, 0x8a,
	0x52, 0x07, 0xa2, 0xc6, 0xcc, 0x01, 0xbc, 0x9f, 0x03, 0x7e, 0x84, 0x39, 0xb8, 0x00, 0x39, 0x57,
	0x67, 0xcb, 0x23, 0x71, 0xd2, 0x6b, 0xf5, 0xab, 0x17, 0x1d, 0xb3, 0x9b, 0x95, 0x79, 0x42, 0xe2,
	0x04, 0x8d, 0xa1, 0xed, 0x70, 0xd9, 0x72, 0xab, 0x76, 0xbf, 0x7a, 0xd1, 0xfa, 0xf2, 0xd3, 0xf2,
	0x5d, 0xfb, 0x74, 0x6e, 0xb6, 0xc4, 0x49, 0xe6, 0xe8, 0x9c, 0x26, 0x4d, 0x5b, 0xc2, 0xfd, 0x74,
	0xfa, 0xd5, 0x8b, 0x26, 0x4d, 0x8a, 0x42, 0xcc, 0xe0, 0x47, 0xd0, 0xf4, 0xed, 0xe8, 0x16, 0x27,
	0x54, 0x74, 0xdd, 0x82, 0xe8, 0x18, 0xac, 0xb9, 0x03, 0x17, 0xba, 0xc5, 0xa4, 0xe8, 0x83, 0xa1,
	0xbd, 0xca, 0xab, 0x8c, 0x02, 0xa8, 0x0f, 0x8d, 0x80, 0x38, 0xb7, 0x81, 0xed, 0xe3, 0xc2, 0x6b,
	0xda, 0xa2, 0xec, 0x11, 0x26, 0xc4, 0xed, 0x55, 0x73, 0x2c, 0x43, 0x06, 0xbf, 0xaf, 0xc2, 0x83,
	0xd1, 0x0d, 0x9d, 0x1f, 0x5e, 0x4e, 0xd8, 0xc6, 0x26, 0xa1, 0x02, 0x71, 0x38, 0x61, 0x95, 0x2e,
	0x6d, 0x0a, 0x5c, 0x63, 0xcf, 0x2c, 0x35, 0x62, 0x22, 0xce, 0x6b, 0xbc, 0x25, 0x18, 0xa6, 0xe2,
	0x9f, 0xc3, 0x51, 0x6a, 0xe8, 0x92, 0x78, 0xe3, 0xd9, 0x6f, 0x58, 0x38, 0x59, 0xe2, 0xb2, 0xa0,
	0xaf, 0x52, 0x36, 0xef, 0x9b, 0xa5, 0x96, 0xd7, 0x7d, 0xea, 0x5b, 0xa7, 0xd9, 0xfd, 0x0c, 0x0e,
	0x53, 0xc3, 0x15, 0x09, 0x5c, 0x1a, 0x6e, 0x7e, 0xae, 0x75, 0x04, 0x39, 0x24, 0x81, 0xab, 0xb9,
	0xe8, 0x8b, 0x2c, 0x92, 0xd8, 0xb9, 0xc1, 0x3e, 0x2e, 0xbf, 0xfb, 0xd4, 0xd9, 0x9c, 0xb1, 0x9a,
	0x8b, 0x7e, 0x09, 0xc7, 0xa5, 0x13, 0x2c, 0x9e, 0x7a, 0x2e, 0x9e, 0xa3, 0xc2, 0x99, 0x72, 0x54,
	0xaf, 0x08, 0x7e, 0x4d, 0x6f, 0x69, 0xe4, 0x4e, 0xa4, 0x51, 0xbd, 0x20, 0xf8, 0xb5, 0xe6, 0x0e,
	0xfe, 0x20, 0x01, 0xa2, 0xed, 0xde, 0x6d, 0x42, 0x2a, 0xfe, 0x72, 0x13, 0x84, 0xc2, 0x89, 0x4b,
	0x47, 0xb5, 0x30, 0xda, 0x2b, 0x83, 0x2e, 0x27, 0xf5, 0x54, 0x0c, 0xb9, 0xa1, 0x55, 0xd6, 0x44,
	0x3a, 0xb4, 0xa8, 0x32, 0xfe, 0x5b, 0x01, 0x79, 0x7a, 0x17, 0x13, 0x27, 0x3f, 0xf0, 0x8a, 0x53,
	0xa3, 0x10, 0x4f, 0x36, 0x35, 0x9e, 0x40, 0x27, 0x7c, 0x1d, 0xe0, 0x68, 0x3b, 0x7c, 0xf3, 0xd1,
	0xb4, 0x19, 0x95, 0x4e, 0xdf, 0xa2, 0xc8, 0xaa, 0xfb, 0x45, 0x76, 0x0e, 0x0d, 0x9f, 0x06, 0x62,
	0xb1, 0x61, 0x9f, 0xb9, 0xaa, 0x33, 0x54, 0x73, 0xa9, 0x17, 0x6e, 0xc0, 0x72, 0xbf, 0x9f, 0x33,
	0x69, 0x32, 0x9c, 0xf5, 0xe3, 0x33, 0x68, 0x73, 0xa3, 0x98, 0x04, 0x6b, 0x1c, 0xf5, 0x0e, 0x72,
	0x66, 0x2d, 0xc6, 0xcc, 0x19, 0xc1, 0xde, 0x26, 0x33, 0xa4, 0x03, 0x34, 0xdf, 0x64, 0x1e, 0x05,
	0x1d, 0x9e, 0x7b, 0xd5, 0xdc, 0xf8, 0x41, 0x35, 0xef, 0x91, 0x43, 0xf3, 0xfb, 0xe5, 0xf0, 0x56,
	0x82, 0xc3, 0xd1, 0x8d, 0x9d, 0x7c, 0x70, 0xed, 0x8b, 0x82, 0xa9, 0xbc, 0xb7, 0x60, 0xaa, 0x3f,
	0x20, 0x98, 0x47, 0x70, 0xb0, 0xfd, 0xf4, 0x64, 0x56, 0x02, 0x43, 0x67, 0x50, 0x17, 0xa3, 0xaf,
	0x50, 0xf9, 0x14, 0x1c, 0xfc, 0xa5, 0x02, 0xc7, 0xe3, 0x28, 0xbc, 0xdb, 0x94, 0xa4, 0x7d, 0x0e,
	0x8d, 0x35, 0x85, 0xcb, 0xd3, 0xa5, 0xce, 0x50, 0xde, 0x55, 0x6e, 0xb0, 0xa3, 0xe8, 0x26, 0xc3,
	0x59, 0x57, 0x3f, 0x87, 0x43, 0x6e, 0x94, 0x9f, 0x2a, 0x99, 0xb3, 0x2e, 0x23, 0xb3, 0x2e, 0xd0,
	0x4b, 0xef, 0x88, 0xe7, 0x5a, 0xe5, 0xbd, 0x81, 0xa1, 0x5c, 0x6b, 0x37, 0xd8, 0x76, 0x2d, 0xdf,
	0xfd, 0xaa, 0xf0, 0xf1, 0xac, 0x53, 0x74, 0xea, 0x7e, 0x45, 0x3f, 0x47, 0xdc, 0x83, 0xed, 0xfa,
	0x84, 0xee, 0x44, 0xd5, 0xf7, 0xf9, 0x1c, 0xb1, 0x23, 0x0a, 0x3d, 0x91, 0xa5, 0xc5, 0x06, 0x66,
	0x3d, 0xff, 0xd5, 0x64, 0x38, 0x1d, 0x97, 0x83, 0xd7, 0x70, 0xb4, 0x5c, 0x3b, 0xa5, 0x8a, 0x3d,
	0x86, 0xfa, 0x26, 0x8c, 0x93, 0xb4, 0x60, 0xdb, 0x46, 0x50, 0x90, 0xd7, 0xcb, 0x09, 0x7d, 0x9f,
	0x7e, 0xbd, 0x58, 0xeb, 0xb3, 0xe0, 0x9b, 0x02, 0xd7, 0x5c, 0xde, 0x2d, 0xf6, 0x83, 0x4d, 0xdf,
	0x5c, 0xb7, 0x18, 0x38, 0xf8, 0x5d, 0x15, 0x8e, 0xc7, 0x34, 0xd8, 0x3d, 0xdd, 0x4a, 0x0b, 0x57,
	0xec, 0x96, 0x28, 0x1c, 0x4d, 0x8b, 0x19, 0xec, 0xe9, 0x16, 0xc5, 0x59, 0xb7, 0xce, 0xa1, 0x11,
	0xdf, 0xb0, 0xfd, 0xa4, 0x38, 0xfc, 0xeb, 0x0c, 0x15, 0x2f, 0xd9, 0x26, 0x81, 0xc5, 0x2a, 0x51,
	0xe8, 0x50, 0x93, 0xe2, 0x4c, 0x47, 0xe8, 0x63, 0xb8, 0x4f, 0x82, 0x24, 0x0a, 0x0b, 0x0d, 0xe2,
	0x10, 0x5d, 0x49, 0x7c, 0x3b, 0x20, 0xd7, 0x38, 0x4e, 0xc2, 0xc2, 0xc2, 0x9a, 0xc1, 0x54, 0xcf,
	0x41, 0x98, 0x90, 0xeb, 0x37, 0x85, 0xd7, 0x2d, 0xb0, 0x82, 0x02, 0x1a, 0xfb, 0x14, 0x90, 0x13,
	0x7c, 0xb3, 0x58, 0x42, 0xbe, 0xf1, 0x96, 0x14, 0x02, 0x1f, 0xaa, 0x90, 0x81, 0x07, 0xa7, 0xf4,
	0xed, 0x8f, 0xec, 0xa8, 0xdc, 0x05, 0xba, 0xdd, 0x7b, 0xe1, 0xda, 0x0e, 0x8a, 0x02, 0xe0, 0x18,
	0x5d, 0x33, 0x57, 0xc5, 0x7d, 0xba, 0xb2, 0x5a, 0xd3, 0x70, 0x5f, 0xe1, 0x28, 0x26, 0x61, 0x50,
	0x78, 0x19, 0x29, 0x38, 0xf8, 0x06, 0x0e, 0xa7, 0x76, 0x7c, 0x8b, 0xdd, 0x91, 0xed, 0x79, 0xca,
	0x9d, 0x4b, 0xf8, 0xa2, 0x11, 0x79, 0x85, 0x3b, 0x28, 0xc0, 0xfe, 0x13, 0x24, 0x36, 0x5d, 0xa4,
	0xb6, 0x5b, 0x1f, 0xbd, 0xad, 0xb6, 0xfd, 0x4f, 0x40, 0xc9, 0x45, 0xca, 0x0d, 0xfe, 0x26, 0x41,
	0x2f, 0x73, 0xbd, 0x2b, 0x66, 0xc7, 0xf6, 0x76, 0x76, 0x8b, 0x03, 0x0a, 0x6a, 0x2e, 0x1a, 0x82,
	0x58, 0x57, 0x2d, 0x9b, 0x86, 0xd4, 0xab, 0xb0, 0x2a, 0x9e, 0x97, 0xab, 0x58, 0x8a, 0xdc, 0x14,
	0xbb, 0x26, 0x4f, 0x63, 0x08, 0x6d, 0x31, 0xe6, 0xb8, 0x8f, 0xea, 0x7b, 0xfa, 0xe0, 0x87, 0xd8,
	0x8f, 0xc1, 0x6f, 0xe1, 0x74, 0xee, 0x91, 0x8d, 0x1e, 0x26, 0xb8, 0x94, 0xc0, 0x2f, 0xe0, 0x38,
	0xf6, 0xc8, 0xc6, 0x0a, 0xc2, 0x04, 0x5b, 0xfc, 0x7b, 0x57, 0x9e, 0xcb, 0x72, 0x2c, 0x8e, 0x1a,
	0x94, 0xa6, 0x93, 0xf7, 0x0b, 0x38, 0xca, 0x0e, 0xa5, 0x2a, 0xca, 0x77, 0xec, 0x30, 0x3d, 0x22,
	0xf6, 0xca, 0xc1, 0x9f, 0x25, 0x78, 0x38, 0xb6, 0x7d, 0x3c, 0xb3, 0xbd, 0x3d, 0x82, 0x60, 0x55,
	0x8c, 0xc4, 0xab, 0xcc, 0x69, 0x99, 0x82, 0x1a, 0xfb, 0x8b, 0x11, 0x87, 0x0e, 0xb1, 0x3d, 0xcb,
	0xc5, 0x8e, 0x57, 0x98, 0x09, 0xc0, 0x89, 0x2b, 0xec, 0x78, 0xe8, 0x53, 0xe8, 0x12, 0xdf, 0x5e,
	0x63, 0xfa, 0xc5, 0xe3, 0x1b, 0x6b, 0x95, 0x6d, 0xac, 0x6d, 0x86, 0x2e, 0x23, 0x4f, 0x2c, 0xb5,
	0x0d, 0x76, 0xd7, 0xce, 0x7f, 0x2e, 0x8a, 0x2e, 0x89, 0x7b, 0xf9, 0x5d, 0x05, 0x64, 0x55, 0x5f,
	0x4e, 0xad, 0x85, 0x62, 0x8e, 0xd5, 0x85, 0xb5, 0x78, 0x39, 0x53, 0xd1, 0x43, 0x78, 0xa0, 0xe6,
	0x01, 0x6b, 0xf4, 0x5c, 0xd1, 0x75, 0x75, 0x22, 0x4b, 0xe8, 0x14, 0x50, 0x91, 0x5a, 0xce, 0x55,
	0x53, 0xae, 0xa0, 0x8f, 0xe0, 0xb8, 0x88, 0x4f, 0x97, 0x73, 0x6d, 0x24, 0x57, 0x77, 0x89, 0xb1,
	0x69, 0x2c, 0x67, 0x72, 0x6d, 0xd7, 0xd3, 0xe8, 0xb9, 0xb2, 0x90, 0xef, 0xa3, 0x07, 0x70, 0x54,
	0xba, 0x61, 0x3c, 0x92, 0x0f, 0xf6, 0xf8, 0x59, 0x6a, 0x93, 0x2b, 0xb9, 0x81, 0x1e, 0xc3, 0xc3,
	0xd2, 0xcd, 0xca, 0xfc, 0x6b, 0xf5, 0xca, 0x1a, 0x29, 0x93, 0x89, 0xdc, 0x44, 0x9f, 0xc0, 0x47,
	0x45, 0x7a, 0x3e, 0xd1, 0x66, 0x96, 0x6e, 0x2c, 0x54, 0x19, 0x76, 0x49, 0x1a, 0x83, 0x35, 0x52,
	0xcc, 0x2b, 0xb9, 0xb5, 0x1b, 0xc8, 0x4c, 0x53, 0xe4, 0x36, 0x3a, 0x87, 0x4f, 0x4a, 0x81, 0x28,
	0x53, 0xd5, 0x9a, 0x29, 0x13, 0x7e, 0xae, 0x73, 0xf9, 0x27, 0x09, 0x1e, 0xb0, 0x92, 0xce, 0x15,
	0x7d, 0xb4, 0xd0, 0x0c, 0xdd, 0x32, 0x66, 0xbc, 0xae, 0xec, 0xba, 0x32, 0x6a, 0xe9, 0x86, 0xae,
	0xca, 0xf7, 0xd0, 0x19, 0x7c, 0xbc, 0x87, 0xfc, 0x8d, 0x62, 0xea, 0x9a, 0x3e, 0x96, 0xab, 0x3c,
	0xcf, 0x1d, 0x7e, 0x48, 0x1b, 0x73, 0x25, 0xd7, 0xbe, 0x87, 0xd6, 0xc6, 0xba, 0x61, 0xaa, 0xf2,
	0xc3, 0x4b, 0x43, 0xb4, 0x99, 0xdb, 0xf3, 0x70, 0x8e, 0xa0, 0x93, 0x3a, 0x48, 0x83, 0x40, 0xd0,
	0xdd, 0x42, 0x13, 0x63, 0xac, 0xe9, 0xb2, 0x84, 0x4e, 0x40, 0xde, 0x62, 0xa9, 0x10, 0x2a, 0x97,
	0x7f, 0x95, 0x84, 0x47, 0x53, 0x9d, 0x19, 0xa6, 0x10, 0x0e, 0x33, 0x15, 0xc0, 0xcc, 0xd4, 0x5e,
	0x28, 0xa3, 0x97, 0xf2, 0x3d, 0xf4, 0x08, 0x7a, 0x19, 0xaa, 0x9a, 0x73, 0x43, 0x57, 0x26, 0x96,
	0xb2, 0x58, 0x28, 0xa3, 0xaf, 0x65, 0x09, 0xf5, 0xe0, 0x24, 0x63, 0x0d, 0x53, 0x37, 0xc6, 0xa6,
	0x32, 0x7b, 0xfe, 0x52, 0xae, 0xa0, 0x43, 0x68, 0x6d, 0x19, 0xe5, 0x4a, 0xae, 0xf2, 0xfa, 0x09,
	0x60, 0xae, 0xea, 0x73, 0x6d, 0xa1, 0xbd, 0x50, 0x2d, 0x4d, 0xff, 0xb5, 0x21, 0xd7, 0x78, 0x36,
	0x69, 0x30, 0x86, 0xb9, 0x95, 0x92, 0x80, 0xc6, 0xca, 0x74, 0x38, 0xa1, 0x95, 0x3c, 0xb8, 0x34,
	0xa0, 0x97, 0x8f, 0x7c, 0xaa, 0xce, 0xe7, 0xca, 0x58, 0xcd, 0x49, 0xbf, 0x4c, 0xa8, 0xdf, 0x2c,
	0x4a, 0x81, 0xa6, 0x94, 0x36, 0x1d, 0xcb, 0x95, 0xa1, 0xfc, 0xf6, 0xdd, 0x99, 0xf4, 0xdd, 0xbb,
	0x33, 0xe9, 0x9f, 0xef, 0xce, 0xa4, 0x3f, 0xfe, 0xeb, 0xec, 0xde, 0xff, 0x02, 0x00, 0x00, 0xff,
	0xff, 0x83, 0x59, 0x6b, 0xa2, 0x8f, 0x12, 0x00, 0x00,
}
