// Code generated by protoc-gen-go. DO NOT EDIT.
// source: gnobility/gnobility.proto

package gnobility // import "golang.52tt.com/protocol/services/gnobility"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type NobilitySwitch int32

const (
	NobilitySwitch_E_UNKOWN         NobilitySwitch = 0
	NobilitySwitch_E_ALL            NobilitySwitch = 1
	NobilitySwitch_E_FANS           NobilitySwitch = 2
	NobilitySwitch_E_FOLLOW_7DAY    NobilitySwitch = 4
	NobilitySwitch_E_FOLLOW_30DAY   NobilitySwitch = 8
	NobilitySwitch_E_ONLINE         NobilitySwitch = 16
	NobilitySwitch_E_ONLY_FOLLOWING NobilitySwitch = 32
)

var NobilitySwitch_name = map[int32]string{
	0:  "E_UNKOWN",
	1:  "E_ALL",
	2:  "E_FANS",
	4:  "E_FOLLOW_7DAY",
	8:  "E_FOLLOW_30DAY",
	16: "E_ONLINE",
	32: "E_ONLY_FOLLOWING",
}
var NobilitySwitch_value = map[string]int32{
	"E_UNKOWN":         0,
	"E_ALL":            1,
	"E_FANS":           2,
	"E_FOLLOW_7DAY":    4,
	"E_FOLLOW_30DAY":   8,
	"E_ONLINE":         16,
	"E_ONLY_FOLLOWING": 32,
}

func (x NobilitySwitch) String() string {
	return proto.EnumName(NobilitySwitch_name, int32(x))
}
func (NobilitySwitch) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{0}
}

// 贵族待充值差额
type NoilityInvestInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	GapValue             int64    `protobuf:"varint,2,opt,name=gap_value,json=gapValue,proto3" json:"gap_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NoilityInvestInfo) Reset()         { *m = NoilityInvestInfo{} }
func (m *NoilityInvestInfo) String() string { return proto.CompactTextString(m) }
func (*NoilityInvestInfo) ProtoMessage()    {}
func (*NoilityInvestInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{0}
}
func (m *NoilityInvestInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NoilityInvestInfo.Unmarshal(m, b)
}
func (m *NoilityInvestInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NoilityInvestInfo.Marshal(b, m, deterministic)
}
func (dst *NoilityInvestInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NoilityInvestInfo.Merge(dst, src)
}
func (m *NoilityInvestInfo) XXX_Size() int {
	return xxx_messageInfo_NoilityInvestInfo.Size(m)
}
func (m *NoilityInvestInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NoilityInvestInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NoilityInvestInfo proto.InternalMessageInfo

func (m *NoilityInvestInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *NoilityInvestInfo) GetGapValue() int64 {
	if m != nil {
		return m.GapValue
	}
	return 0
}

type NobilityInfo struct {
	Value                uint64               `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	KeepValue            uint64               `protobuf:"varint,2,opt,name=keep_value,json=keepValue,proto3" json:"keep_value,omitempty"`
	Level                uint32               `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	CycleTs              uint32               `protobuf:"varint,4,opt,name=cycle_ts,json=cycleTs,proto3" json:"cycle_ts,omitempty"`
	Uid                  uint32               `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	Invisible            bool                 `protobuf:"varint,6,opt,name=invisible,proto3" json:"invisible,omitempty"`
	WaitCostValue        uint32               `protobuf:"varint,7,opt,name=wait_cost_value,json=waitCostValue,proto3" json:"wait_cost_value,omitempty"`
	TotalWaitCostValue   uint32               `protobuf:"varint,8,opt,name=total_wait_cost_value,json=totalWaitCostValue,proto3" json:"total_wait_cost_value,omitempty"`
	LevelName            string               `protobuf:"bytes,9,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	InvestInfoList       []*NoilityInvestInfo `protobuf:"bytes,10,rep,name=invest_info_list,json=investInfoList,proto3" json:"invest_info_list,omitempty"`
	TempNobilityRemainTs uint32               `protobuf:"varint,11,opt,name=temp_nobility_remain_ts,json=tempNobilityRemainTs,proto3" json:"temp_nobility_remain_ts,omitempty"`
	RealLevel            uint32               `protobuf:"varint,12,opt,name=real_level,json=realLevel,proto3" json:"real_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *NobilityInfo) Reset()         { *m = NobilityInfo{} }
func (m *NobilityInfo) String() string { return proto.CompactTextString(m) }
func (*NobilityInfo) ProtoMessage()    {}
func (*NobilityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{1}
}
func (m *NobilityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NobilityInfo.Unmarshal(m, b)
}
func (m *NobilityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NobilityInfo.Marshal(b, m, deterministic)
}
func (dst *NobilityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NobilityInfo.Merge(dst, src)
}
func (m *NobilityInfo) XXX_Size() int {
	return xxx_messageInfo_NobilityInfo.Size(m)
}
func (m *NobilityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NobilityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NobilityInfo proto.InternalMessageInfo

func (m *NobilityInfo) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *NobilityInfo) GetKeepValue() uint64 {
	if m != nil {
		return m.KeepValue
	}
	return 0
}

func (m *NobilityInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *NobilityInfo) GetCycleTs() uint32 {
	if m != nil {
		return m.CycleTs
	}
	return 0
}

func (m *NobilityInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NobilityInfo) GetInvisible() bool {
	if m != nil {
		return m.Invisible
	}
	return false
}

func (m *NobilityInfo) GetWaitCostValue() uint32 {
	if m != nil {
		return m.WaitCostValue
	}
	return 0
}

func (m *NobilityInfo) GetTotalWaitCostValue() uint32 {
	if m != nil {
		return m.TotalWaitCostValue
	}
	return 0
}

func (m *NobilityInfo) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *NobilityInfo) GetInvestInfoList() []*NoilityInvestInfo {
	if m != nil {
		return m.InvestInfoList
	}
	return nil
}

func (m *NobilityInfo) GetTempNobilityRemainTs() uint32 {
	if m != nil {
		return m.TempNobilityRemainTs
	}
	return 0
}

func (m *NobilityInfo) GetRealLevel() uint32 {
	if m != nil {
		return m.RealLevel
	}
	return 0
}

type SetNobilitySwitchFlagReq struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MapSwitchFlag        map[uint32]bool `protobuf:"bytes,2,rep,name=map_switch_flag,json=mapSwitchFlag,proto3" json:"map_switch_flag,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SetNobilitySwitchFlagReq) Reset()         { *m = SetNobilitySwitchFlagReq{} }
func (m *SetNobilitySwitchFlagReq) String() string { return proto.CompactTextString(m) }
func (*SetNobilitySwitchFlagReq) ProtoMessage()    {}
func (*SetNobilitySwitchFlagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{2}
}
func (m *SetNobilitySwitchFlagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNobilitySwitchFlagReq.Unmarshal(m, b)
}
func (m *SetNobilitySwitchFlagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNobilitySwitchFlagReq.Marshal(b, m, deterministic)
}
func (dst *SetNobilitySwitchFlagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNobilitySwitchFlagReq.Merge(dst, src)
}
func (m *SetNobilitySwitchFlagReq) XXX_Size() int {
	return xxx_messageInfo_SetNobilitySwitchFlagReq.Size(m)
}
func (m *SetNobilitySwitchFlagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNobilitySwitchFlagReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetNobilitySwitchFlagReq proto.InternalMessageInfo

func (m *SetNobilitySwitchFlagReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetNobilitySwitchFlagReq) GetMapSwitchFlag() map[uint32]bool {
	if m != nil {
		return m.MapSwitchFlag
	}
	return nil
}

type SetNobilitySwitchFlagResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetNobilitySwitchFlagResp) Reset()         { *m = SetNobilitySwitchFlagResp{} }
func (m *SetNobilitySwitchFlagResp) String() string { return proto.CompactTextString(m) }
func (*SetNobilitySwitchFlagResp) ProtoMessage()    {}
func (*SetNobilitySwitchFlagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{3}
}
func (m *SetNobilitySwitchFlagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNobilitySwitchFlagResp.Unmarshal(m, b)
}
func (m *SetNobilitySwitchFlagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNobilitySwitchFlagResp.Marshal(b, m, deterministic)
}
func (dst *SetNobilitySwitchFlagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNobilitySwitchFlagResp.Merge(dst, src)
}
func (m *SetNobilitySwitchFlagResp) XXX_Size() int {
	return xxx_messageInfo_SetNobilitySwitchFlagResp.Size(m)
}
func (m *SetNobilitySwitchFlagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNobilitySwitchFlagResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetNobilitySwitchFlagResp proto.InternalMessageInfo

type GetNobilitySwitchFlagReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNobilitySwitchFlagReq) Reset()         { *m = GetNobilitySwitchFlagReq{} }
func (m *GetNobilitySwitchFlagReq) String() string { return proto.CompactTextString(m) }
func (*GetNobilitySwitchFlagReq) ProtoMessage()    {}
func (*GetNobilitySwitchFlagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{4}
}
func (m *GetNobilitySwitchFlagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNobilitySwitchFlagReq.Unmarshal(m, b)
}
func (m *GetNobilitySwitchFlagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNobilitySwitchFlagReq.Marshal(b, m, deterministic)
}
func (dst *GetNobilitySwitchFlagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNobilitySwitchFlagReq.Merge(dst, src)
}
func (m *GetNobilitySwitchFlagReq) XXX_Size() int {
	return xxx_messageInfo_GetNobilitySwitchFlagReq.Size(m)
}
func (m *GetNobilitySwitchFlagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNobilitySwitchFlagReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNobilitySwitchFlagReq proto.InternalMessageInfo

func (m *GetNobilitySwitchFlagReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type GetNobilitySwitchFlagResp struct {
	SwitchFlagList       []uint32 `protobuf:"varint,1,rep,packed,name=switch_flag_list,json=switchFlagList,proto3" json:"switch_flag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNobilitySwitchFlagResp) Reset()         { *m = GetNobilitySwitchFlagResp{} }
func (m *GetNobilitySwitchFlagResp) String() string { return proto.CompactTextString(m) }
func (*GetNobilitySwitchFlagResp) ProtoMessage()    {}
func (*GetNobilitySwitchFlagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{5}
}
func (m *GetNobilitySwitchFlagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNobilitySwitchFlagResp.Unmarshal(m, b)
}
func (m *GetNobilitySwitchFlagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNobilitySwitchFlagResp.Marshal(b, m, deterministic)
}
func (dst *GetNobilitySwitchFlagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNobilitySwitchFlagResp.Merge(dst, src)
}
func (m *GetNobilitySwitchFlagResp) XXX_Size() int {
	return xxx_messageInfo_GetNobilitySwitchFlagResp.Size(m)
}
func (m *GetNobilitySwitchFlagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNobilitySwitchFlagResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNobilitySwitchFlagResp proto.InternalMessageInfo

func (m *GetNobilitySwitchFlagResp) GetSwitchFlagList() []uint32 {
	if m != nil {
		return m.SwitchFlagList
	}
	return nil
}

type AddConsumeValueReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Value                uint32   `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddConsumeValueReq) Reset()         { *m = AddConsumeValueReq{} }
func (m *AddConsumeValueReq) String() string { return proto.CompactTextString(m) }
func (*AddConsumeValueReq) ProtoMessage()    {}
func (*AddConsumeValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{6}
}
func (m *AddConsumeValueReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddConsumeValueReq.Unmarshal(m, b)
}
func (m *AddConsumeValueReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddConsumeValueReq.Marshal(b, m, deterministic)
}
func (dst *AddConsumeValueReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddConsumeValueReq.Merge(dst, src)
}
func (m *AddConsumeValueReq) XXX_Size() int {
	return xxx_messageInfo_AddConsumeValueReq.Size(m)
}
func (m *AddConsumeValueReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddConsumeValueReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddConsumeValueReq proto.InternalMessageInfo

func (m *AddConsumeValueReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddConsumeValueReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddConsumeValueReq) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type AddConsumeValueResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddConsumeValueResp) Reset()         { *m = AddConsumeValueResp{} }
func (m *AddConsumeValueResp) String() string { return proto.CompactTextString(m) }
func (*AddConsumeValueResp) ProtoMessage()    {}
func (*AddConsumeValueResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{7}
}
func (m *AddConsumeValueResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddConsumeValueResp.Unmarshal(m, b)
}
func (m *AddConsumeValueResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddConsumeValueResp.Marshal(b, m, deterministic)
}
func (dst *AddConsumeValueResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddConsumeValueResp.Merge(dst, src)
}
func (m *AddConsumeValueResp) XXX_Size() int {
	return xxx_messageInfo_AddConsumeValueResp.Size(m)
}
func (m *AddConsumeValueResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddConsumeValueResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddConsumeValueResp proto.InternalMessageInfo

// for test
type ForTestAddRechargeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RechargeValue        uint32   `protobuf:"varint,2,opt,name=recharge_value,json=rechargeValue,proto3" json:"recharge_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ForTestAddRechargeReq) Reset()         { *m = ForTestAddRechargeReq{} }
func (m *ForTestAddRechargeReq) String() string { return proto.CompactTextString(m) }
func (*ForTestAddRechargeReq) ProtoMessage()    {}
func (*ForTestAddRechargeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{8}
}
func (m *ForTestAddRechargeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ForTestAddRechargeReq.Unmarshal(m, b)
}
func (m *ForTestAddRechargeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ForTestAddRechargeReq.Marshal(b, m, deterministic)
}
func (dst *ForTestAddRechargeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ForTestAddRechargeReq.Merge(dst, src)
}
func (m *ForTestAddRechargeReq) XXX_Size() int {
	return xxx_messageInfo_ForTestAddRechargeReq.Size(m)
}
func (m *ForTestAddRechargeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ForTestAddRechargeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ForTestAddRechargeReq proto.InternalMessageInfo

func (m *ForTestAddRechargeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ForTestAddRechargeReq) GetRechargeValue() uint32 {
	if m != nil {
		return m.RechargeValue
	}
	return 0
}

type ForTestAddRechargeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ForTestAddRechargeResp) Reset()         { *m = ForTestAddRechargeResp{} }
func (m *ForTestAddRechargeResp) String() string { return proto.CompactTextString(m) }
func (*ForTestAddRechargeResp) ProtoMessage()    {}
func (*ForTestAddRechargeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{9}
}
func (m *ForTestAddRechargeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ForTestAddRechargeResp.Unmarshal(m, b)
}
func (m *ForTestAddRechargeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ForTestAddRechargeResp.Marshal(b, m, deterministic)
}
func (dst *ForTestAddRechargeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ForTestAddRechargeResp.Merge(dst, src)
}
func (m *ForTestAddRechargeResp) XXX_Size() int {
	return xxx_messageInfo_ForTestAddRechargeResp.Size(m)
}
func (m *ForTestAddRechargeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ForTestAddRechargeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ForTestAddRechargeResp proto.InternalMessageInfo

type ModifyNobilityDataReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Value                uint32   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	WaitCost             uint32   `protobuf:"varint,3,opt,name=wait_cost,json=waitCost,proto3" json:"wait_cost,omitempty"`
	KeepValue            uint32   `protobuf:"varint,4,opt,name=keep_value,json=keepValue,proto3" json:"keep_value,omitempty"`
	CycleTs              uint32   `protobuf:"varint,5,opt,name=cycle_ts,json=cycleTs,proto3" json:"cycle_ts,omitempty"`
	Push                 bool     `protobuf:"varint,6,opt,name=push,proto3" json:"push,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyNobilityDataReq) Reset()         { *m = ModifyNobilityDataReq{} }
func (m *ModifyNobilityDataReq) String() string { return proto.CompactTextString(m) }
func (*ModifyNobilityDataReq) ProtoMessage()    {}
func (*ModifyNobilityDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{10}
}
func (m *ModifyNobilityDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyNobilityDataReq.Unmarshal(m, b)
}
func (m *ModifyNobilityDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyNobilityDataReq.Marshal(b, m, deterministic)
}
func (dst *ModifyNobilityDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyNobilityDataReq.Merge(dst, src)
}
func (m *ModifyNobilityDataReq) XXX_Size() int {
	return xxx_messageInfo_ModifyNobilityDataReq.Size(m)
}
func (m *ModifyNobilityDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyNobilityDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyNobilityDataReq proto.InternalMessageInfo

func (m *ModifyNobilityDataReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ModifyNobilityDataReq) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *ModifyNobilityDataReq) GetWaitCost() uint32 {
	if m != nil {
		return m.WaitCost
	}
	return 0
}

func (m *ModifyNobilityDataReq) GetKeepValue() uint32 {
	if m != nil {
		return m.KeepValue
	}
	return 0
}

func (m *ModifyNobilityDataReq) GetCycleTs() uint32 {
	if m != nil {
		return m.CycleTs
	}
	return 0
}

func (m *ModifyNobilityDataReq) GetPush() bool {
	if m != nil {
		return m.Push
	}
	return false
}

type ModifyNobilityDataResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyNobilityDataResp) Reset()         { *m = ModifyNobilityDataResp{} }
func (m *ModifyNobilityDataResp) String() string { return proto.CompactTextString(m) }
func (*ModifyNobilityDataResp) ProtoMessage()    {}
func (*ModifyNobilityDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{11}
}
func (m *ModifyNobilityDataResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyNobilityDataResp.Unmarshal(m, b)
}
func (m *ModifyNobilityDataResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyNobilityDataResp.Marshal(b, m, deterministic)
}
func (dst *ModifyNobilityDataResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyNobilityDataResp.Merge(dst, src)
}
func (m *ModifyNobilityDataResp) XXX_Size() int {
	return xxx_messageInfo_ModifyNobilityDataResp.Size(m)
}
func (m *ModifyNobilityDataResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyNobilityDataResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyNobilityDataResp proto.InternalMessageInfo

type NobilityChannelVisibleReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	NobLevel             uint32   `protobuf:"varint,3,opt,name=nob_level,json=nobLevel,proto3" json:"nob_level,omitempty"`
	Visible              bool     `protobuf:"varint,4,opt,name=visible,proto3" json:"visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NobilityChannelVisibleReq) Reset()         { *m = NobilityChannelVisibleReq{} }
func (m *NobilityChannelVisibleReq) String() string { return proto.CompactTextString(m) }
func (*NobilityChannelVisibleReq) ProtoMessage()    {}
func (*NobilityChannelVisibleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{12}
}
func (m *NobilityChannelVisibleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NobilityChannelVisibleReq.Unmarshal(m, b)
}
func (m *NobilityChannelVisibleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NobilityChannelVisibleReq.Marshal(b, m, deterministic)
}
func (dst *NobilityChannelVisibleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NobilityChannelVisibleReq.Merge(dst, src)
}
func (m *NobilityChannelVisibleReq) XXX_Size() int {
	return xxx_messageInfo_NobilityChannelVisibleReq.Size(m)
}
func (m *NobilityChannelVisibleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NobilityChannelVisibleReq.DiscardUnknown(m)
}

var xxx_messageInfo_NobilityChannelVisibleReq proto.InternalMessageInfo

func (m *NobilityChannelVisibleReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NobilityChannelVisibleReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NobilityChannelVisibleReq) GetNobLevel() uint32 {
	if m != nil {
		return m.NobLevel
	}
	return 0
}

func (m *NobilityChannelVisibleReq) GetVisible() bool {
	if m != nil {
		return m.Visible
	}
	return false
}

type NobilityChannelVisibleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NobilityChannelVisibleResp) Reset()         { *m = NobilityChannelVisibleResp{} }
func (m *NobilityChannelVisibleResp) String() string { return proto.CompactTextString(m) }
func (*NobilityChannelVisibleResp) ProtoMessage()    {}
func (*NobilityChannelVisibleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{13}
}
func (m *NobilityChannelVisibleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NobilityChannelVisibleResp.Unmarshal(m, b)
}
func (m *NobilityChannelVisibleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NobilityChannelVisibleResp.Marshal(b, m, deterministic)
}
func (dst *NobilityChannelVisibleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NobilityChannelVisibleResp.Merge(dst, src)
}
func (m *NobilityChannelVisibleResp) XXX_Size() int {
	return xxx_messageInfo_NobilityChannelVisibleResp.Size(m)
}
func (m *NobilityChannelVisibleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NobilityChannelVisibleResp.DiscardUnknown(m)
}

var xxx_messageInfo_NobilityChannelVisibleResp proto.InternalMessageInfo

type NobilityChannelVisibleEvent struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	NobLevel             uint32   `protobuf:"varint,3,opt,name=nob_level,json=nobLevel,proto3" json:"nob_level,omitempty"`
	Visible              bool     `protobuf:"varint,4,opt,name=visible,proto3" json:"visible,omitempty"`
	Ts                   uint32   `protobuf:"varint,5,opt,name=ts,proto3" json:"ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NobilityChannelVisibleEvent) Reset()         { *m = NobilityChannelVisibleEvent{} }
func (m *NobilityChannelVisibleEvent) String() string { return proto.CompactTextString(m) }
func (*NobilityChannelVisibleEvent) ProtoMessage()    {}
func (*NobilityChannelVisibleEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{14}
}
func (m *NobilityChannelVisibleEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NobilityChannelVisibleEvent.Unmarshal(m, b)
}
func (m *NobilityChannelVisibleEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NobilityChannelVisibleEvent.Marshal(b, m, deterministic)
}
func (dst *NobilityChannelVisibleEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NobilityChannelVisibleEvent.Merge(dst, src)
}
func (m *NobilityChannelVisibleEvent) XXX_Size() int {
	return xxx_messageInfo_NobilityChannelVisibleEvent.Size(m)
}
func (m *NobilityChannelVisibleEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_NobilityChannelVisibleEvent.DiscardUnknown(m)
}

var xxx_messageInfo_NobilityChannelVisibleEvent proto.InternalMessageInfo

func (m *NobilityChannelVisibleEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NobilityChannelVisibleEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NobilityChannelVisibleEvent) GetNobLevel() uint32 {
	if m != nil {
		return m.NobLevel
	}
	return 0
}

func (m *NobilityChannelVisibleEvent) GetVisible() bool {
	if m != nil {
		return m.Visible
	}
	return false
}

func (m *NobilityChannelVisibleEvent) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

type GetNobilityLevelCfgReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNobilityLevelCfgReq) Reset()         { *m = GetNobilityLevelCfgReq{} }
func (m *GetNobilityLevelCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetNobilityLevelCfgReq) ProtoMessage()    {}
func (*GetNobilityLevelCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{15}
}
func (m *GetNobilityLevelCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNobilityLevelCfgReq.Unmarshal(m, b)
}
func (m *GetNobilityLevelCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNobilityLevelCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetNobilityLevelCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNobilityLevelCfgReq.Merge(dst, src)
}
func (m *GetNobilityLevelCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetNobilityLevelCfgReq.Size(m)
}
func (m *GetNobilityLevelCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNobilityLevelCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNobilityLevelCfgReq proto.InternalMessageInfo

type GetNobilityLevelCfgResp struct {
	NobilityLevelList    []*NobilityLevel `protobuf:"bytes,1,rep,name=nobility_level_list,json=nobilityLevelList,proto3" json:"nobility_level_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetNobilityLevelCfgResp) Reset()         { *m = GetNobilityLevelCfgResp{} }
func (m *GetNobilityLevelCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetNobilityLevelCfgResp) ProtoMessage()    {}
func (*GetNobilityLevelCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{16}
}
func (m *GetNobilityLevelCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNobilityLevelCfgResp.Unmarshal(m, b)
}
func (m *GetNobilityLevelCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNobilityLevelCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetNobilityLevelCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNobilityLevelCfgResp.Merge(dst, src)
}
func (m *GetNobilityLevelCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetNobilityLevelCfgResp.Size(m)
}
func (m *GetNobilityLevelCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNobilityLevelCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNobilityLevelCfgResp proto.InternalMessageInfo

func (m *GetNobilityLevelCfgResp) GetNobilityLevelList() []*NobilityLevel {
	if m != nil {
		return m.NobilityLevelList
	}
	return nil
}

type NobilityLevel struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	LvName               string   `protobuf:"bytes,2,opt,name=lv_name,json=lvName,proto3" json:"lv_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NobilityLevel) Reset()         { *m = NobilityLevel{} }
func (m *NobilityLevel) String() string { return proto.CompactTextString(m) }
func (*NobilityLevel) ProtoMessage()    {}
func (*NobilityLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{17}
}
func (m *NobilityLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NobilityLevel.Unmarshal(m, b)
}
func (m *NobilityLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NobilityLevel.Marshal(b, m, deterministic)
}
func (dst *NobilityLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NobilityLevel.Merge(dst, src)
}
func (m *NobilityLevel) XXX_Size() int {
	return xxx_messageInfo_NobilityLevel.Size(m)
}
func (m *NobilityLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_NobilityLevel.DiscardUnknown(m)
}

var xxx_messageInfo_NobilityLevel proto.InternalMessageInfo

func (m *NobilityLevel) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *NobilityLevel) GetLvName() string {
	if m != nil {
		return m.LvName
	}
	return ""
}

type GetChannelInvisibleFlagReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelInvisibleFlagReq) Reset()         { *m = GetChannelInvisibleFlagReq{} }
func (m *GetChannelInvisibleFlagReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelInvisibleFlagReq) ProtoMessage()    {}
func (*GetChannelInvisibleFlagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{18}
}
func (m *GetChannelInvisibleFlagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelInvisibleFlagReq.Unmarshal(m, b)
}
func (m *GetChannelInvisibleFlagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelInvisibleFlagReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelInvisibleFlagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelInvisibleFlagReq.Merge(dst, src)
}
func (m *GetChannelInvisibleFlagReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelInvisibleFlagReq.Size(m)
}
func (m *GetChannelInvisibleFlagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelInvisibleFlagReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelInvisibleFlagReq proto.InternalMessageInfo

func (m *GetChannelInvisibleFlagReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelInvisibleFlagReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

type GetChannelInvisibleFlagResp struct {
	Invisible            bool     `protobuf:"varint,1,opt,name=invisible,proto3" json:"invisible,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelInvisibleFlagResp) Reset()         { *m = GetChannelInvisibleFlagResp{} }
func (m *GetChannelInvisibleFlagResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelInvisibleFlagResp) ProtoMessage()    {}
func (*GetChannelInvisibleFlagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gnobility_de4d7cdac6a7c75b, []int{19}
}
func (m *GetChannelInvisibleFlagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelInvisibleFlagResp.Unmarshal(m, b)
}
func (m *GetChannelInvisibleFlagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelInvisibleFlagResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelInvisibleFlagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelInvisibleFlagResp.Merge(dst, src)
}
func (m *GetChannelInvisibleFlagResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelInvisibleFlagResp.Size(m)
}
func (m *GetChannelInvisibleFlagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelInvisibleFlagResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelInvisibleFlagResp proto.InternalMessageInfo

func (m *GetChannelInvisibleFlagResp) GetInvisible() bool {
	if m != nil {
		return m.Invisible
	}
	return false
}

func init() {
	proto.RegisterType((*NoilityInvestInfo)(nil), "gnobility.NoilityInvestInfo")
	proto.RegisterType((*NobilityInfo)(nil), "gnobility.NobilityInfo")
	proto.RegisterType((*SetNobilitySwitchFlagReq)(nil), "gnobility.SetNobilitySwitchFlagReq")
	proto.RegisterMapType((map[uint32]bool)(nil), "gnobility.SetNobilitySwitchFlagReq.MapSwitchFlagEntry")
	proto.RegisterType((*SetNobilitySwitchFlagResp)(nil), "gnobility.SetNobilitySwitchFlagResp")
	proto.RegisterType((*GetNobilitySwitchFlagReq)(nil), "gnobility.GetNobilitySwitchFlagReq")
	proto.RegisterType((*GetNobilitySwitchFlagResp)(nil), "gnobility.GetNobilitySwitchFlagResp")
	proto.RegisterType((*AddConsumeValueReq)(nil), "gnobility.AddConsumeValueReq")
	proto.RegisterType((*AddConsumeValueResp)(nil), "gnobility.AddConsumeValueResp")
	proto.RegisterType((*ForTestAddRechargeReq)(nil), "gnobility.ForTestAddRechargeReq")
	proto.RegisterType((*ForTestAddRechargeResp)(nil), "gnobility.ForTestAddRechargeResp")
	proto.RegisterType((*ModifyNobilityDataReq)(nil), "gnobility.ModifyNobilityDataReq")
	proto.RegisterType((*ModifyNobilityDataResp)(nil), "gnobility.ModifyNobilityDataResp")
	proto.RegisterType((*NobilityChannelVisibleReq)(nil), "gnobility.NobilityChannelVisibleReq")
	proto.RegisterType((*NobilityChannelVisibleResp)(nil), "gnobility.NobilityChannelVisibleResp")
	proto.RegisterType((*NobilityChannelVisibleEvent)(nil), "gnobility.NobilityChannelVisibleEvent")
	proto.RegisterType((*GetNobilityLevelCfgReq)(nil), "gnobility.GetNobilityLevelCfgReq")
	proto.RegisterType((*GetNobilityLevelCfgResp)(nil), "gnobility.GetNobilityLevelCfgResp")
	proto.RegisterType((*NobilityLevel)(nil), "gnobility.NobilityLevel")
	proto.RegisterType((*GetChannelInvisibleFlagReq)(nil), "gnobility.GetChannelInvisibleFlagReq")
	proto.RegisterType((*GetChannelInvisibleFlagResp)(nil), "gnobility.GetChannelInvisibleFlagResp")
	proto.RegisterEnum("gnobility.NobilitySwitch", NobilitySwitch_name, NobilitySwitch_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GNobilityClient is the client API for GNobility service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GNobilityClient interface {
	SetNobilitySwitchFlag(ctx context.Context, in *SetNobilitySwitchFlagReq, opts ...grpc.CallOption) (*SetNobilitySwitchFlagResp, error)
	GetNobilitySwitchFlag(ctx context.Context, in *GetNobilitySwitchFlagReq, opts ...grpc.CallOption) (*GetNobilitySwitchFlagResp, error)
	AddConsumeValue(ctx context.Context, in *AddConsumeValueReq, opts ...grpc.CallOption) (*AddConsumeValueResp, error)
	ForTestAddRecharge(ctx context.Context, in *ForTestAddRechargeReq, opts ...grpc.CallOption) (*ForTestAddRechargeResp, error)
	ModifyNobilityData(ctx context.Context, in *ModifyNobilityDataReq, opts ...grpc.CallOption) (*ModifyNobilityDataResp, error)
	NobilityChannelVisible(ctx context.Context, in *NobilityChannelVisibleReq, opts ...grpc.CallOption) (*NobilityChannelVisibleResp, error)
	GetNobilityLevelCfg(ctx context.Context, in *GetNobilityLevelCfgReq, opts ...grpc.CallOption) (*GetNobilityLevelCfgResp, error)
	// 对账接口
	GetConsumeOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetConsumeOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	FixConsumeOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	GetChannelInvisibleFlag(ctx context.Context, in *GetChannelInvisibleFlagReq, opts ...grpc.CallOption) (*GetChannelInvisibleFlagResp, error)
}

type gNobilityClient struct {
	cc *grpc.ClientConn
}

func NewGNobilityClient(cc *grpc.ClientConn) GNobilityClient {
	return &gNobilityClient{cc}
}

func (c *gNobilityClient) SetNobilitySwitchFlag(ctx context.Context, in *SetNobilitySwitchFlagReq, opts ...grpc.CallOption) (*SetNobilitySwitchFlagResp, error) {
	out := new(SetNobilitySwitchFlagResp)
	err := c.cc.Invoke(ctx, "/gnobility.GNobility/SetNobilitySwitchFlag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gNobilityClient) GetNobilitySwitchFlag(ctx context.Context, in *GetNobilitySwitchFlagReq, opts ...grpc.CallOption) (*GetNobilitySwitchFlagResp, error) {
	out := new(GetNobilitySwitchFlagResp)
	err := c.cc.Invoke(ctx, "/gnobility.GNobility/GetNobilitySwitchFlag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gNobilityClient) AddConsumeValue(ctx context.Context, in *AddConsumeValueReq, opts ...grpc.CallOption) (*AddConsumeValueResp, error) {
	out := new(AddConsumeValueResp)
	err := c.cc.Invoke(ctx, "/gnobility.GNobility/AddConsumeValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gNobilityClient) ForTestAddRecharge(ctx context.Context, in *ForTestAddRechargeReq, opts ...grpc.CallOption) (*ForTestAddRechargeResp, error) {
	out := new(ForTestAddRechargeResp)
	err := c.cc.Invoke(ctx, "/gnobility.GNobility/ForTestAddRecharge", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gNobilityClient) ModifyNobilityData(ctx context.Context, in *ModifyNobilityDataReq, opts ...grpc.CallOption) (*ModifyNobilityDataResp, error) {
	out := new(ModifyNobilityDataResp)
	err := c.cc.Invoke(ctx, "/gnobility.GNobility/ModifyNobilityData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gNobilityClient) NobilityChannelVisible(ctx context.Context, in *NobilityChannelVisibleReq, opts ...grpc.CallOption) (*NobilityChannelVisibleResp, error) {
	out := new(NobilityChannelVisibleResp)
	err := c.cc.Invoke(ctx, "/gnobility.GNobility/NobilityChannelVisible", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gNobilityClient) GetNobilityLevelCfg(ctx context.Context, in *GetNobilityLevelCfgReq, opts ...grpc.CallOption) (*GetNobilityLevelCfgResp, error) {
	out := new(GetNobilityLevelCfgResp)
	err := c.cc.Invoke(ctx, "/gnobility.GNobility/GetNobilityLevelCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gNobilityClient) GetConsumeOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/gnobility.GNobility/GetConsumeOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gNobilityClient) GetConsumeOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/gnobility.GNobility/GetConsumeOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gNobilityClient) FixConsumeOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/gnobility.GNobility/FixConsumeOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gNobilityClient) GetChannelInvisibleFlag(ctx context.Context, in *GetChannelInvisibleFlagReq, opts ...grpc.CallOption) (*GetChannelInvisibleFlagResp, error) {
	out := new(GetChannelInvisibleFlagResp)
	err := c.cc.Invoke(ctx, "/gnobility.GNobility/GetChannelInvisibleFlag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GNobilityServer is the server API for GNobility service.
type GNobilityServer interface {
	SetNobilitySwitchFlag(context.Context, *SetNobilitySwitchFlagReq) (*SetNobilitySwitchFlagResp, error)
	GetNobilitySwitchFlag(context.Context, *GetNobilitySwitchFlagReq) (*GetNobilitySwitchFlagResp, error)
	AddConsumeValue(context.Context, *AddConsumeValueReq) (*AddConsumeValueResp, error)
	ForTestAddRecharge(context.Context, *ForTestAddRechargeReq) (*ForTestAddRechargeResp, error)
	ModifyNobilityData(context.Context, *ModifyNobilityDataReq) (*ModifyNobilityDataResp, error)
	NobilityChannelVisible(context.Context, *NobilityChannelVisibleReq) (*NobilityChannelVisibleResp, error)
	GetNobilityLevelCfg(context.Context, *GetNobilityLevelCfgReq) (*GetNobilityLevelCfgResp, error)
	// 对账接口
	GetConsumeOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetConsumeOrderList(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	FixConsumeOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	GetChannelInvisibleFlag(context.Context, *GetChannelInvisibleFlagReq) (*GetChannelInvisibleFlagResp, error)
}

func RegisterGNobilityServer(s *grpc.Server, srv GNobilityServer) {
	s.RegisterService(&_GNobility_serviceDesc, srv)
}

func _GNobility_SetNobilitySwitchFlag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetNobilitySwitchFlagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GNobilityServer).SetNobilitySwitchFlag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gnobility.GNobility/SetNobilitySwitchFlag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GNobilityServer).SetNobilitySwitchFlag(ctx, req.(*SetNobilitySwitchFlagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GNobility_GetNobilitySwitchFlag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNobilitySwitchFlagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GNobilityServer).GetNobilitySwitchFlag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gnobility.GNobility/GetNobilitySwitchFlag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GNobilityServer).GetNobilitySwitchFlag(ctx, req.(*GetNobilitySwitchFlagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GNobility_AddConsumeValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddConsumeValueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GNobilityServer).AddConsumeValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gnobility.GNobility/AddConsumeValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GNobilityServer).AddConsumeValue(ctx, req.(*AddConsumeValueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GNobility_ForTestAddRecharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ForTestAddRechargeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GNobilityServer).ForTestAddRecharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gnobility.GNobility/ForTestAddRecharge",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GNobilityServer).ForTestAddRecharge(ctx, req.(*ForTestAddRechargeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GNobility_ModifyNobilityData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyNobilityDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GNobilityServer).ModifyNobilityData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gnobility.GNobility/ModifyNobilityData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GNobilityServer).ModifyNobilityData(ctx, req.(*ModifyNobilityDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GNobility_NobilityChannelVisible_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NobilityChannelVisibleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GNobilityServer).NobilityChannelVisible(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gnobility.GNobility/NobilityChannelVisible",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GNobilityServer).NobilityChannelVisible(ctx, req.(*NobilityChannelVisibleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GNobility_GetNobilityLevelCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNobilityLevelCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GNobilityServer).GetNobilityLevelCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gnobility.GNobility/GetNobilityLevelCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GNobilityServer).GetNobilityLevelCfg(ctx, req.(*GetNobilityLevelCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GNobility_GetConsumeOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GNobilityServer).GetConsumeOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gnobility.GNobility/GetConsumeOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GNobilityServer).GetConsumeOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GNobility_GetConsumeOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GNobilityServer).GetConsumeOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gnobility.GNobility/GetConsumeOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GNobilityServer).GetConsumeOrderList(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GNobility_FixConsumeOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GNobilityServer).FixConsumeOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gnobility.GNobility/FixConsumeOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GNobilityServer).FixConsumeOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GNobility_GetChannelInvisibleFlag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelInvisibleFlagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GNobilityServer).GetChannelInvisibleFlag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gnobility.GNobility/GetChannelInvisibleFlag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GNobilityServer).GetChannelInvisibleFlag(ctx, req.(*GetChannelInvisibleFlagReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GNobility_serviceDesc = grpc.ServiceDesc{
	ServiceName: "gnobility.GNobility",
	HandlerType: (*GNobilityServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetNobilitySwitchFlag",
			Handler:    _GNobility_SetNobilitySwitchFlag_Handler,
		},
		{
			MethodName: "GetNobilitySwitchFlag",
			Handler:    _GNobility_GetNobilitySwitchFlag_Handler,
		},
		{
			MethodName: "AddConsumeValue",
			Handler:    _GNobility_AddConsumeValue_Handler,
		},
		{
			MethodName: "ForTestAddRecharge",
			Handler:    _GNobility_ForTestAddRecharge_Handler,
		},
		{
			MethodName: "ModifyNobilityData",
			Handler:    _GNobility_ModifyNobilityData_Handler,
		},
		{
			MethodName: "NobilityChannelVisible",
			Handler:    _GNobility_NobilityChannelVisible_Handler,
		},
		{
			MethodName: "GetNobilityLevelCfg",
			Handler:    _GNobility_GetNobilityLevelCfg_Handler,
		},
		{
			MethodName: "GetConsumeOrderCount",
			Handler:    _GNobility_GetConsumeOrderCount_Handler,
		},
		{
			MethodName: "GetConsumeOrderList",
			Handler:    _GNobility_GetConsumeOrderList_Handler,
		},
		{
			MethodName: "FixConsumeOrder",
			Handler:    _GNobility_FixConsumeOrder_Handler,
		},
		{
			MethodName: "GetChannelInvisibleFlag",
			Handler:    _GNobility_GetChannelInvisibleFlag_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gnobility/gnobility.proto",
}

func init() {
	proto.RegisterFile("gnobility/gnobility.proto", fileDescriptor_gnobility_de4d7cdac6a7c75b)
}

var fileDescriptor_gnobility_de4d7cdac6a7c75b = []byte{
	// 1167 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x57, 0x6f, 0x4f, 0xe3, 0xc6,
	0x13, 0x3e, 0x27, 0x01, 0xe2, 0x81, 0x04, 0xb3, 0xfc, 0x73, 0x02, 0xfc, 0x7e, 0x39, 0xf7, 0x38,
	0x45, 0xad, 0x2e, 0xb4, 0x39, 0xd1, 0x56, 0xad, 0x54, 0x1d, 0xe5, 0x92, 0x34, 0xba, 0x10, 0x2a,
	0x43, 0x41, 0xd7, 0x93, 0xce, 0x32, 0xf6, 0x12, 0xb6, 0x38, 0xb6, 0x9b, 0xdd, 0xe4, 0x9a, 0x77,
	0xad, 0xfa, 0x19, 0xfa, 0x25, 0xfa, 0x19, 0xfa, 0x19, 0xfa, 0x99, 0xaa, 0x5d, 0xc7, 0x89, 0x4d,
	0x6c, 0x40, 0x7d, 0xd1, 0x77, 0x9e, 0x99, 0x67, 0x9e, 0x99, 0x9d, 0xcd, 0x3c, 0x76, 0xa0, 0xd4,
	0x73, 0xbd, 0x2b, 0xe2, 0x10, 0x36, 0x3e, 0x98, 0x3e, 0xd5, 0xfc, 0x81, 0xc7, 0x3c, 0x24, 0x4f,
	0x1d, 0xe5, 0xff, 0x0f, 0xb0, 0xe5, 0xb9, 0x16, 0x71, 0xf0, 0x8b, 0x51, 0xfd, 0x20, 0x6a, 0x04,
	0x58, 0xad, 0x09, 0x6b, 0x5d, 0x4f, 0x60, 0xdb, 0xee, 0x08, 0x53, 0xd6, 0x76, 0xaf, 0x3d, 0xb4,
	0x01, 0x0b, 0x0e, 0x1e, 0x61, 0x47, 0x95, 0x2a, 0x52, 0xb5, 0xa0, 0x07, 0x06, 0xda, 0x01, 0xb9,
	0x67, 0xfa, 0xc6, 0xc8, 0x74, 0x86, 0x58, 0xcd, 0x54, 0xa4, 0x6a, 0x56, 0xcf, 0xf7, 0x4c, 0xff,
	0x82, 0xdb, 0xda, 0x5f, 0x59, 0x58, 0xe9, 0x4e, 0xaa, 0x86, 0x1c, 0x01, 0x92, 0x73, 0xe4, 0xf4,
	0xc0, 0x40, 0x7b, 0x00, 0xb7, 0x18, 0x47, 0x49, 0x72, 0xba, 0xcc, 0x3d, 0x82, 0x65, 0x56, 0x38,
	0x1b, 0x2d, 0x5c, 0x82, 0xbc, 0x35, 0xb6, 0x1c, 0x6c, 0x30, 0xaa, 0xe6, 0x44, 0x60, 0x49, 0xd8,
	0xe7, 0x14, 0x29, 0x90, 0x1d, 0x12, 0x5b, 0x5d, 0x10, 0x5e, 0xfe, 0x88, 0x76, 0x41, 0x26, 0xee,
	0x88, 0x50, 0x72, 0xe5, 0x60, 0x75, 0xb1, 0x22, 0x55, 0xf3, 0xfa, 0xcc, 0x81, 0x9e, 0xc3, 0xea,
	0x07, 0x93, 0x30, 0xc3, 0xf2, 0x28, 0x9b, 0x34, 0xb1, 0x24, 0x72, 0x0b, 0xdc, 0x7d, 0xec, 0x51,
	0x16, 0x34, 0xf2, 0x19, 0x6c, 0x32, 0x8f, 0x99, 0x8e, 0x71, 0x17, 0x9d, 0x17, 0x68, 0x24, 0x82,
	0x97, 0xb1, 0x94, 0x3d, 0x00, 0xd1, 0xae, 0xe1, 0x9a, 0x7d, 0xac, 0xca, 0x15, 0xa9, 0x2a, 0xeb,
	0xb2, 0xf0, 0x74, 0xcd, 0x3e, 0x46, 0x4d, 0x50, 0x88, 0x98, 0xb0, 0x41, 0xdc, 0x6b, 0xcf, 0x70,
	0x08, 0x65, 0x2a, 0x54, 0xb2, 0xd5, 0xe5, 0xfa, 0x6e, 0x6d, 0x76, 0x81, 0x73, 0x77, 0xa1, 0x17,
	0xc9, 0xf4, 0xb9, 0x43, 0x28, 0x43, 0x87, 0xb0, 0xcd, 0x70, 0xdf, 0x37, 0xc2, 0x14, 0x63, 0x80,
	0xfb, 0x26, 0x71, 0xf9, 0x6c, 0x96, 0x45, 0x6f, 0x1b, 0x3c, 0x1c, 0x5e, 0x85, 0x2e, 0x82, 0xe7,
	0x94, 0x77, 0x37, 0xc0, 0xa6, 0x63, 0x04, 0xe3, 0x5d, 0x11, 0x48, 0x99, 0x7b, 0x3a, 0xdc, 0xa1,
	0xfd, 0x2d, 0x81, 0x7a, 0x86, 0x59, 0x98, 0x76, 0xf6, 0x81, 0x30, 0xeb, 0xa6, 0xe9, 0x98, 0x3d,
	0x1d, 0xff, 0x1c, 0x0e, 0x59, 0x9a, 0x0d, 0xf9, 0x3d, 0xac, 0xf6, 0x4d, 0xdf, 0xa0, 0x02, 0x66,
	0x5c, 0x3b, 0x66, 0x4f, 0xcd, 0x88, 0xb3, 0x7c, 0x1e, 0x39, 0x4b, 0x1a, 0x5f, 0xed, 0xc4, 0xf4,
	0x67, 0x8e, 0x86, 0xcb, 0x06, 0x63, 0xbd, 0xd0, 0x8f, 0xfa, 0xca, 0xaf, 0x00, 0xcd, 0x83, 0x78,
	0x1f, 0xb7, 0x78, 0x1c, 0xf6, 0x71, 0x8b, 0xc7, 0xb3, 0x1f, 0x59, 0x46, 0x5c, 0x74, 0x60, 0x7c,
	0x95, 0xf9, 0x52, 0xd2, 0x76, 0xa0, 0x94, 0x52, 0x9f, 0xfa, 0xda, 0x21, 0xa8, 0xad, 0xb4, 0xc3,
	0x96, 0x20, 0x3f, 0x24, 0x76, 0x70, 0x3f, 0x52, 0x25, 0xcb, 0x7f, 0x6c, 0x43, 0x62, 0xf3, 0xd1,
	0x6b, 0x0d, 0x28, 0xb5, 0xd2, 0x38, 0x51, 0x15, 0x94, 0xc8, 0x38, 0xa2, 0xf9, 0x45, 0x3a, 0x45,
	0x0a, 0x9a, 0x77, 0x80, 0x8e, 0x6c, 0xfb, 0xd8, 0x73, 0xe9, 0xb0, 0x8f, 0xc5, 0x6f, 0x27, 0x79,
	0xc8, 0x7b, 0x00, 0xd6, 0x8d, 0xe9, 0xba, 0xd8, 0x31, 0x88, 0x2d, 0x4e, 0x58, 0xd0, 0xe5, 0x89,
	0xa7, 0x6d, 0xcf, 0xce, 0x3e, 0xd9, 0x15, 0x61, 0x68, 0x9b, 0xb0, 0x3e, 0x47, 0x4e, 0x7d, 0xed,
	0x7b, 0xd8, 0x6c, 0x7a, 0x83, 0x73, 0x4c, 0xd9, 0x91, 0x6d, 0xeb, 0xd8, 0xba, 0x31, 0x07, 0xbd,
	0x94, 0xb2, 0xfb, 0x50, 0x1c, 0x4c, 0x00, 0x91, 0x35, 0x2d, 0xe8, 0x85, 0xd0, 0x1b, 0x2c, 0xbc,
	0x0a, 0x5b, 0x49, 0x8c, 0xd4, 0xd7, 0xfe, 0x94, 0x60, 0xf3, 0xc4, 0xb3, 0xc9, 0xf5, 0x38, 0x1c,
	0xd5, 0x6b, 0x93, 0x99, 0xc9, 0xc5, 0x62, 0x17, 0x18, 0x1e, 0x82, 0x2b, 0xcd, 0x74, 0xef, 0x26,
	0xc7, 0xcb, 0x87, 0xfb, 0x79, 0x47, 0x42, 0x02, 0x3d, 0x88, 0x48, 0x48, 0x54, 0x2c, 0x16, 0xe2,
	0x62, 0x81, 0x20, 0xe7, 0x0f, 0xe9, 0xcd, 0x44, 0x15, 0xc4, 0x33, 0x3f, 0x46, 0x52, 0xaf, 0xd4,
	0xd7, 0x7e, 0x97, 0xa0, 0x14, 0x3a, 0x8f, 0x83, 0xa9, 0x5f, 0x04, 0x2a, 0xf2, 0xaf, 0xae, 0x6b,
	0x07, 0x64, 0xd7, 0xbb, 0x32, 0xa2, 0xf2, 0x96, 0x77, 0xbd, 0x2b, 0xb1, 0x7e, 0x48, 0x85, 0xa5,
	0x50, 0xb2, 0x72, 0xa2, 0xb9, 0xd0, 0xd4, 0x76, 0xa1, 0x9c, 0xd6, 0x04, 0xf5, 0xb5, 0x3f, 0x24,
	0xd8, 0x49, 0x0e, 0x37, 0x46, 0xd8, 0x65, 0xff, 0x55, 0x97, 0xa8, 0x08, 0x99, 0xe9, 0xb8, 0x33,
	0x8c, 0xf2, 0xa9, 0x46, 0x36, 0x45, 0x64, 0x1f, 0x5f, 0xf3, 0xf5, 0xd2, 0x2c, 0xd8, 0x4e, 0x8c,
	0x50, 0x1f, 0x7d, 0x07, 0xeb, 0x53, 0x51, 0x0b, 0x94, 0x74, 0xba, 0x44, 0xcb, 0x75, 0x35, 0x26,
	0x92, 0x91, 0x6c, 0x7d, 0xcd, 0x8d, 0x9a, 0x62, 0xc3, 0xbe, 0x81, 0x42, 0x0c, 0x93, 0xf2, 0x42,
	0xdb, 0x86, 0x25, 0x67, 0x14, 0xc8, 0x75, 0x46, 0xc8, 0xf5, 0xa2, 0x33, 0xe2, 0x5a, 0xad, 0xbd,
	0x82, 0x72, 0x0b, 0xb3, 0xc9, 0x40, 0xdb, 0xe1, 0xcb, 0x23, 0x5d, 0x0e, 0x15, 0xc8, 0x5a, 0xd3,
	0x69, 0xf2, 0x47, 0xed, 0x6b, 0xd8, 0x49, 0x65, 0xa0, 0x7e, 0xfc, 0x25, 0x25, 0xdd, 0x79, 0x49,
	0x7d, 0xfc, 0xab, 0x04, 0xc5, 0xb8, 0xca, 0xa0, 0x15, 0xc8, 0x37, 0x8c, 0x1f, 0xba, 0x6f, 0x4e,
	0x2f, 0xbb, 0xca, 0x13, 0x24, 0xc3, 0x42, 0xc3, 0x38, 0xea, 0x74, 0x14, 0x09, 0x01, 0x2c, 0x36,
	0x8c, 0xe6, 0x51, 0xf7, 0x4c, 0xc9, 0xa0, 0x35, 0x28, 0x34, 0x8c, 0xe6, 0x69, 0xa7, 0x73, 0x7a,
	0x69, 0x7c, 0xf1, 0xfa, 0xe8, 0xad, 0x92, 0x43, 0x08, 0x8a, 0x53, 0xd7, 0xcb, 0x4f, 0xb9, 0x2f,
	0x1f, 0x70, 0x9d, 0x76, 0x3b, 0xed, 0x6e, 0x43, 0x51, 0xd0, 0x06, 0x28, 0xc2, 0x7a, 0x3b, 0x81,
	0xb5, 0xbb, 0x2d, 0xa5, 0x52, 0xff, 0x2d, 0x0f, 0x72, 0x2b, 0xec, 0x01, 0xd9, 0xb0, 0x99, 0x28,
	0xa6, 0xe8, 0xa3, 0x47, 0xc8, 0x7d, 0xf9, 0xd9, 0xc3, 0x20, 0xea, 0x6b, 0x4f, 0x78, 0x95, 0xd6,
	0x83, 0x55, 0x5a, 0x8f, 0xa9, 0xd2, 0xba, 0xa7, 0x8a, 0x0e, 0xab, 0x77, 0x04, 0x12, 0xed, 0x45,
	0x52, 0xe7, 0x95, 0xb9, 0xfc, 0xbf, 0xfb, 0xc2, 0x82, 0xf3, 0x1d, 0xa0, 0x79, 0x2d, 0x44, 0x95,
	0x48, 0x5e, 0xa2, 0xf8, 0x96, 0x9f, 0x3e, 0x80, 0x08, 0xc9, 0xe7, 0x15, 0x2a, 0x46, 0x9e, 0x28,
	0xb6, 0x31, 0xf2, 0x14, 0x89, 0x7b, 0x82, 0x7a, 0xb0, 0x95, 0xac, 0x1f, 0xe8, 0x59, 0xc2, 0xc2,
	0xcd, 0xc9, 0x60, 0x79, 0xff, 0x11, 0x28, 0x51, 0xe8, 0x3d, 0xac, 0x27, 0xec, 0x3d, 0x7a, 0x9a,
	0x7c, 0x6b, 0x11, 0xc5, 0x28, 0x6b, 0x0f, 0x41, 0x04, 0xff, 0x1b, 0xd8, 0xe0, 0x0b, 0x17, 0xdc,
	0xcd, 0xe9, 0xc0, 0xc6, 0x83, 0x63, 0x6f, 0xe8, 0x32, 0x54, 0xaa, 0xe9, 0xe1, 0x47, 0xef, 0x45,
	0xbd, 0x76, 0x4e, 0xfa, 0x58, 0x37, 0xdd, 0x60, 0xfa, 0x5b, 0xb1, 0x90, 0x80, 0x4f, 0xc8, 0x4e,
	0x44, 0xb3, 0x51, 0x32, 0xf1, 0xe9, 0x75, 0x0f, 0x57, 0x3c, 0x24, 0x52, 0xda, 0x36, 0x9d, 0xd0,
	0xb5, 0x61, 0xb5, 0x49, 0x7e, 0x89, 0xd2, 0xa1, 0xdd, 0x18, 0x5e, 0xc7, 0xbe, 0x63, 0x5a, 0x41,
	0x68, 0xbe, 0xb3, 0x46, 0xdf, 0xe7, 0x1f, 0x72, 0x82, 0xea, 0x27, 0x21, 0x9f, 0x49, 0xba, 0x82,
	0xf6, 0xe3, 0x73, 0x4a, 0x51, 0xaf, 0xf2, 0xf3, 0xc7, 0xc0, 0x78, 0xad, 0x6f, 0x5f, 0xfc, 0xf8,
	0x49, 0xcf, 0x73, 0x4c, 0xb7, 0x57, 0x3b, 0xac, 0x33, 0x56, 0xb3, 0xbc, 0xfe, 0x81, 0xf8, 0xcf,
	0x60, 0x79, 0xce, 0x01, 0xc5, 0x83, 0x11, 0xb1, 0x30, 0x9d, 0xfd, 0xf7, 0xb8, 0x5a, 0x14, 0xc1,
	0x97, 0xff, 0x04, 0x00, 0x00, 0xff, 0xff, 0xd6, 0x4d, 0x11, 0x11, 0x99, 0x0c, 0x00, 0x00,
}
