// Code generated by protoc-gen-go. DO NOT EDIT.
// source: golddiamond-logic/golddiamond_api.proto

package golddiamond_logic

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RangeType int32

const (
	RangeType_DAY_RANGE_TYPE   RangeType = 0
	RangeType_MONTH_RANGE_TYPE RangeType = 1
)

var RangeType_name = map[int32]string{
	0: "DAY_RANGE_TYPE",
	1: "MONTH_RANGE_TYPE",
}
var RangeType_value = map[string]int32{
	"DAY_RANGE_TYPE":   0,
	"MONTH_RANGE_TYPE": 1,
}

func (x RangeType) String() string {
	return proto.EnumName(RangeType_name, int32(x))
}
func (RangeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{0}
}

type ErrorCode int32

const (
	ErrorCode_ERROR_CODE_DEFAULT   ErrorCode = 0
	ErrorCode_ERROR_CODE_PARR      ErrorCode = 3001
	ErrorCode_ERROR_CODE_UNMARSHAL ErrorCode = 3002
	ErrorCode_ERROR_CODE_INVALID   ErrorCode = 3003
	ErrorCode_ERROR_CODE_NETWORK   ErrorCode = 3004
)

var ErrorCode_name = map[int32]string{
	0:    "ERROR_CODE_DEFAULT",
	3001: "ERROR_CODE_PARR",
	3002: "ERROR_CODE_UNMARSHAL",
	3003: "ERROR_CODE_INVALID",
	3004: "ERROR_CODE_NETWORK",
}
var ErrorCode_value = map[string]int32{
	"ERROR_CODE_DEFAULT":   0,
	"ERROR_CODE_PARR":      3001,
	"ERROR_CODE_UNMARSHAL": 3002,
	"ERROR_CODE_INVALID":   3003,
	"ERROR_CODE_NETWORK":   3004,
}

func (x ErrorCode) String() string {
	return proto.EnumName(ErrorCode_name, int32(x))
}
func (ErrorCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{1}
}

// 获取考核标签
type ExamTagType int32

const (
	ExamTagType_TAG_DEFAULT   ExamTagType = 0
	ExamTagType_TAG_RECOMMEND ExamTagType = 1
	ExamTagType_TAG_YUYIN     ExamTagType = 2
)

var ExamTagType_name = map[int32]string{
	0: "TAG_DEFAULT",
	1: "TAG_RECOMMEND",
	2: "TAG_YUYIN",
}
var ExamTagType_value = map[string]int32{
	"TAG_DEFAULT":   0,
	"TAG_RECOMMEND": 1,
	"TAG_YUYIN":     2,
}

func (x ExamTagType) String() string {
	return proto.EnumName(ExamTagType_name, int32(x))
}
func (ExamTagType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{2}
}

// 语音直播
type YuyinExamStatus int32

const (
	YuyinExamStatus_YE_DEFAULT   YuyinExamStatus = 0
	YuyinExamStatus_YE_COMMITTED YuyinExamStatus = 1
	YuyinExamStatus_YE_REJECTED  YuyinExamStatus = 2
)

var YuyinExamStatus_name = map[int32]string{
	0: "YE_DEFAULT",
	1: "YE_COMMITTED",
	2: "YE_REJECTED",
}
var YuyinExamStatus_value = map[string]int32{
	"YE_DEFAULT":   0,
	"YE_COMMITTED": 1,
	"YE_REJECTED":  2,
}

func (x YuyinExamStatus) String() string {
	return proto.EnumName(YuyinExamStatus_name, int32(x))
}
func (YuyinExamStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{3}
}

type OperateInfoT struct {
	MonthIncome          string   `protobuf:"bytes,1,opt,name=month_income,json=monthIncome,proto3" json:"month_income,omitempty"`
	IncomeImgs           []string `protobuf:"bytes,2,rep,name=income_imgs,json=incomeImgs,proto3" json:"income_imgs,omitempty"`
	Consume              string   `protobuf:"bytes,3,opt,name=consume,proto3" json:"consume,omitempty"`
	ConsumeImgs          []string `protobuf:"bytes,4,rep,name=consume_imgs,json=consumeImgs,proto3" json:"consume_imgs,omitempty"`
	Teamsize             uint32   `protobuf:"varint,5,opt,name=teamsize,proto3" json:"teamsize,omitempty"`
	Accompanys           []string `protobuf:"bytes,6,rep,name=accompanys,proto3" json:"accompanys,omitempty"`
	EstimateTeams        uint32   `protobuf:"varint,7,opt,name=estimate_teams,json=estimateTeams,proto3" json:"estimate_teams,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OperateInfoT) Reset()         { *m = OperateInfoT{} }
func (m *OperateInfoT) String() string { return proto.CompactTextString(m) }
func (*OperateInfoT) ProtoMessage()    {}
func (*OperateInfoT) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{0}
}
func (m *OperateInfoT) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OperateInfoT.Unmarshal(m, b)
}
func (m *OperateInfoT) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OperateInfoT.Marshal(b, m, deterministic)
}
func (dst *OperateInfoT) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OperateInfoT.Merge(dst, src)
}
func (m *OperateInfoT) XXX_Size() int {
	return xxx_messageInfo_OperateInfoT.Size(m)
}
func (m *OperateInfoT) XXX_DiscardUnknown() {
	xxx_messageInfo_OperateInfoT.DiscardUnknown(m)
}

var xxx_messageInfo_OperateInfoT proto.InternalMessageInfo

func (m *OperateInfoT) GetMonthIncome() string {
	if m != nil {
		return m.MonthIncome
	}
	return ""
}

func (m *OperateInfoT) GetIncomeImgs() []string {
	if m != nil {
		return m.IncomeImgs
	}
	return nil
}

func (m *OperateInfoT) GetConsume() string {
	if m != nil {
		return m.Consume
	}
	return ""
}

func (m *OperateInfoT) GetConsumeImgs() []string {
	if m != nil {
		return m.ConsumeImgs
	}
	return nil
}

func (m *OperateInfoT) GetTeamsize() uint32 {
	if m != nil {
		return m.Teamsize
	}
	return 0
}

func (m *OperateInfoT) GetAccompanys() []string {
	if m != nil {
		return m.Accompanys
	}
	return nil
}

func (m *OperateInfoT) GetEstimateTeams() uint32 {
	if m != nil {
		return m.EstimateTeams
	}
	return 0
}

type ApplicationCooperateReq struct {
	Guildid               uint32        `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	ApplicationType       uint32        `protobuf:"varint,2,opt,name=application_type,json=applicationType,proto3" json:"application_type,omitempty"`
	PlatformName          string        `protobuf:"bytes,3,opt,name=platform_name,json=platformName,proto3" json:"platform_name,omitempty"`
	PlatformContent       string        `protobuf:"bytes,4,opt,name=platform_content,json=platformContent,proto3" json:"platform_content,omitempty"`
	PlatformId            uint32        `protobuf:"varint,5,opt,name=platform_id,json=platformId,proto3" json:"platform_id,omitempty"`
	PlatformCertification []string      `protobuf:"bytes,6,rep,name=platform_certification,json=platformCertification,proto3" json:"platform_certification,omitempty"`
	PlatformIdStr         string        `protobuf:"bytes,7,opt,name=platform_id_str,json=platformIdStr,proto3" json:"platform_id_str,omitempty"`
	ApplyType             uint32        `protobuf:"varint,8,opt,name=apply_type,json=applyType,proto3" json:"apply_type,omitempty"`
	Ttguild               uint32        `protobuf:"varint,9,opt,name=ttguild,proto3" json:"ttguild,omitempty"`
	PlatformGuildType     uint32        `protobuf:"varint,10,opt,name=platform_guild_type,json=platformGuildType,proto3" json:"platform_guild_type,omitempty"`
	PlatformGuildid       string        `protobuf:"bytes,11,opt,name=platform_guildid,json=platformGuildid,proto3" json:"platform_guildid,omitempty"`
	PlatformGuildOwnerId  string        `protobuf:"bytes,12,opt,name=platform_guild_ownerId,json=platformGuildOwnerId,proto3" json:"platform_guild_ownerId,omitempty"`
	PlatformGuildAuthimgs []string      `protobuf:"bytes,13,rep,name=platform_guild_authimgs,json=platformGuildAuthimgs,proto3" json:"platform_guild_authimgs,omitempty"`
	EstablishTime         string        `protobuf:"bytes,14,opt,name=establish_time,json=establishTime,proto3" json:"establish_time,omitempty"`
	Operate               *OperateInfoT `protobuf:"bytes,15,opt,name=operate,proto3" json:"operate,omitempty"`
	Estimates             uint32        `protobuf:"varint,16,opt,name=estimates,proto3" json:"estimates,omitempty"`
	Contact               string        `protobuf:"bytes,17,opt,name=contact,proto3" json:"contact,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}      `json:"-"`
	XXX_unrecognized      []byte        `json:"-"`
	XXX_sizecache         int32         `json:"-"`
}

func (m *ApplicationCooperateReq) Reset()         { *m = ApplicationCooperateReq{} }
func (m *ApplicationCooperateReq) String() string { return proto.CompactTextString(m) }
func (*ApplicationCooperateReq) ProtoMessage()    {}
func (*ApplicationCooperateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{1}
}
func (m *ApplicationCooperateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationCooperateReq.Unmarshal(m, b)
}
func (m *ApplicationCooperateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationCooperateReq.Marshal(b, m, deterministic)
}
func (dst *ApplicationCooperateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationCooperateReq.Merge(dst, src)
}
func (m *ApplicationCooperateReq) XXX_Size() int {
	return xxx_messageInfo_ApplicationCooperateReq.Size(m)
}
func (m *ApplicationCooperateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationCooperateReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationCooperateReq proto.InternalMessageInfo

func (m *ApplicationCooperateReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *ApplicationCooperateReq) GetApplicationType() uint32 {
	if m != nil {
		return m.ApplicationType
	}
	return 0
}

func (m *ApplicationCooperateReq) GetPlatformName() string {
	if m != nil {
		return m.PlatformName
	}
	return ""
}

func (m *ApplicationCooperateReq) GetPlatformContent() string {
	if m != nil {
		return m.PlatformContent
	}
	return ""
}

func (m *ApplicationCooperateReq) GetPlatformId() uint32 {
	if m != nil {
		return m.PlatformId
	}
	return 0
}

func (m *ApplicationCooperateReq) GetPlatformCertification() []string {
	if m != nil {
		return m.PlatformCertification
	}
	return nil
}

func (m *ApplicationCooperateReq) GetPlatformIdStr() string {
	if m != nil {
		return m.PlatformIdStr
	}
	return ""
}

func (m *ApplicationCooperateReq) GetApplyType() uint32 {
	if m != nil {
		return m.ApplyType
	}
	return 0
}

func (m *ApplicationCooperateReq) GetTtguild() uint32 {
	if m != nil {
		return m.Ttguild
	}
	return 0
}

func (m *ApplicationCooperateReq) GetPlatformGuildType() uint32 {
	if m != nil {
		return m.PlatformGuildType
	}
	return 0
}

func (m *ApplicationCooperateReq) GetPlatformGuildid() string {
	if m != nil {
		return m.PlatformGuildid
	}
	return ""
}

func (m *ApplicationCooperateReq) GetPlatformGuildOwnerId() string {
	if m != nil {
		return m.PlatformGuildOwnerId
	}
	return ""
}

func (m *ApplicationCooperateReq) GetPlatformGuildAuthimgs() []string {
	if m != nil {
		return m.PlatformGuildAuthimgs
	}
	return nil
}

func (m *ApplicationCooperateReq) GetEstablishTime() string {
	if m != nil {
		return m.EstablishTime
	}
	return ""
}

func (m *ApplicationCooperateReq) GetOperate() *OperateInfoT {
	if m != nil {
		return m.Operate
	}
	return nil
}

func (m *ApplicationCooperateReq) GetEstimates() uint32 {
	if m != nil {
		return m.Estimates
	}
	return 0
}

func (m *ApplicationCooperateReq) GetContact() string {
	if m != nil {
		return m.Contact
	}
	return ""
}

type ApplicationCooperateRsp struct {
	IsApp                bool     `protobuf:"varint,1,opt,name=is_app,json=isApp,proto3" json:"is_app,omitempty"`
	IsLimit              bool     `protobuf:"varint,2,opt,name=is_limit,json=isLimit,proto3" json:"is_limit,omitempty"`
	LimitEndTime         int64    `protobuf:"varint,3,opt,name=limit_end_time,json=limitEndTime,proto3" json:"limit_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplicationCooperateRsp) Reset()         { *m = ApplicationCooperateRsp{} }
func (m *ApplicationCooperateRsp) String() string { return proto.CompactTextString(m) }
func (*ApplicationCooperateRsp) ProtoMessage()    {}
func (*ApplicationCooperateRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{2}
}
func (m *ApplicationCooperateRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationCooperateRsp.Unmarshal(m, b)
}
func (m *ApplicationCooperateRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationCooperateRsp.Marshal(b, m, deterministic)
}
func (dst *ApplicationCooperateRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationCooperateRsp.Merge(dst, src)
}
func (m *ApplicationCooperateRsp) XXX_Size() int {
	return xxx_messageInfo_ApplicationCooperateRsp.Size(m)
}
func (m *ApplicationCooperateRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationCooperateRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationCooperateRsp proto.InternalMessageInfo

func (m *ApplicationCooperateRsp) GetIsApp() bool {
	if m != nil {
		return m.IsApp
	}
	return false
}

func (m *ApplicationCooperateRsp) GetIsLimit() bool {
	if m != nil {
		return m.IsLimit
	}
	return false
}

func (m *ApplicationCooperateRsp) GetLimitEndTime() int64 {
	if m != nil {
		return m.LimitEndTime
	}
	return 0
}

type CooperateReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	Applied              uint32   `protobuf:"varint,2,opt,name=applied,proto3" json:"applied,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CooperateReq) Reset()         { *m = CooperateReq{} }
func (m *CooperateReq) String() string { return proto.CompactTextString(m) }
func (*CooperateReq) ProtoMessage()    {}
func (*CooperateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{3}
}
func (m *CooperateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CooperateReq.Unmarshal(m, b)
}
func (m *CooperateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CooperateReq.Marshal(b, m, deterministic)
}
func (dst *CooperateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CooperateReq.Merge(dst, src)
}
func (m *CooperateReq) XXX_Size() int {
	return xxx_messageInfo_CooperateReq.Size(m)
}
func (m *CooperateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CooperateReq.DiscardUnknown(m)
}

var xxx_messageInfo_CooperateReq proto.InternalMessageInfo

func (m *CooperateReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *CooperateReq) GetApplied() uint32 {
	if m != nil {
		return m.Applied
	}
	return 0
}

type CooperateRsp struct {
	Applied              uint32   `protobuf:"varint,1,opt,name=applied,proto3" json:"applied,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CooperateRsp) Reset()         { *m = CooperateRsp{} }
func (m *CooperateRsp) String() string { return proto.CompactTextString(m) }
func (*CooperateRsp) ProtoMessage()    {}
func (*CooperateRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{4}
}
func (m *CooperateRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CooperateRsp.Unmarshal(m, b)
}
func (m *CooperateRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CooperateRsp.Marshal(b, m, deterministic)
}
func (dst *CooperateRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CooperateRsp.Merge(dst, src)
}
func (m *CooperateRsp) XXX_Size() int {
	return xxx_messageInfo_CooperateRsp.Size(m)
}
func (m *CooperateRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CooperateRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CooperateRsp proto.InternalMessageInfo

func (m *CooperateRsp) GetApplied() uint32 {
	if m != nil {
		return m.Applied
	}
	return 0
}

type ChannelListReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelListReq) Reset()         { *m = ChannelListReq{} }
func (m *ChannelListReq) String() string { return proto.CompactTextString(m) }
func (*ChannelListReq) ProtoMessage()    {}
func (*ChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{5}
}
func (m *ChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListReq.Unmarshal(m, b)
}
func (m *ChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListReq.Marshal(b, m, deterministic)
}
func (dst *ChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListReq.Merge(dst, src)
}
func (m *ChannelListReq) XXX_Size() int {
	return xxx_messageInfo_ChannelListReq.Size(m)
}
func (m *ChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListReq proto.InternalMessageInfo

func (m *ChannelListReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type Channelinfot struct {
	Channelid            uint32   `protobuf:"varint,1,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Displayid            uint32   `protobuf:"varint,2,opt,name=displayid,proto3" json:"displayid,omitempty"`
	ChannelName          string   `protobuf:"bytes,3,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,4,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Channelinfot) Reset()         { *m = Channelinfot{} }
func (m *Channelinfot) String() string { return proto.CompactTextString(m) }
func (*Channelinfot) ProtoMessage()    {}
func (*Channelinfot) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{6}
}
func (m *Channelinfot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Channelinfot.Unmarshal(m, b)
}
func (m *Channelinfot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Channelinfot.Marshal(b, m, deterministic)
}
func (dst *Channelinfot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Channelinfot.Merge(dst, src)
}
func (m *Channelinfot) XXX_Size() int {
	return xxx_messageInfo_Channelinfot.Size(m)
}
func (m *Channelinfot) XXX_DiscardUnknown() {
	xxx_messageInfo_Channelinfot.DiscardUnknown(m)
}

var xxx_messageInfo_Channelinfot proto.InternalMessageInfo

func (m *Channelinfot) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *Channelinfot) GetDisplayid() uint32 {
	if m != nil {
		return m.Displayid
	}
	return 0
}

func (m *Channelinfot) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *Channelinfot) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type ChannelListRsp struct {
	ChannelList          []*Channelinfot `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ChannelListRsp) Reset()         { *m = ChannelListRsp{} }
func (m *ChannelListRsp) String() string { return proto.CompactTextString(m) }
func (*ChannelListRsp) ProtoMessage()    {}
func (*ChannelListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{7}
}
func (m *ChannelListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListRsp.Unmarshal(m, b)
}
func (m *ChannelListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListRsp.Marshal(b, m, deterministic)
}
func (dst *ChannelListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListRsp.Merge(dst, src)
}
func (m *ChannelListRsp) XXX_Size() int {
	return xxx_messageInfo_ChannelListRsp.Size(m)
}
func (m *ChannelListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListRsp proto.InternalMessageInfo

func (m *ChannelListRsp) GetChannelList() []*Channelinfot {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type PendingReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PendingReq) Reset()         { *m = PendingReq{} }
func (m *PendingReq) String() string { return proto.CompactTextString(m) }
func (*PendingReq) ProtoMessage()    {}
func (*PendingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{8}
}
func (m *PendingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PendingReq.Unmarshal(m, b)
}
func (m *PendingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PendingReq.Marshal(b, m, deterministic)
}
func (dst *PendingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PendingReq.Merge(dst, src)
}
func (m *PendingReq) XXX_Size() int {
	return xxx_messageInfo_PendingReq.Size(m)
}
func (m *PendingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PendingReq.DiscardUnknown(m)
}

var xxx_messageInfo_PendingReq proto.InternalMessageInfo

func (m *PendingReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type PendingRsp struct {
	IncomeBalance        int64    `protobuf:"varint,1,opt,name=income_balance,json=incomeBalance,proto3" json:"income_balance,omitempty"`
	Members              int64    `protobuf:"varint,2,opt,name=members,proto3" json:"members,omitempty"`
	Consume              int64    `protobuf:"varint,3,opt,name=consume,proto3" json:"consume,omitempty"`
	StartTime            int64    `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PendingRsp) Reset()         { *m = PendingRsp{} }
func (m *PendingRsp) String() string { return proto.CompactTextString(m) }
func (*PendingRsp) ProtoMessage()    {}
func (*PendingRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{9}
}
func (m *PendingRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PendingRsp.Unmarshal(m, b)
}
func (m *PendingRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PendingRsp.Marshal(b, m, deterministic)
}
func (dst *PendingRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PendingRsp.Merge(dst, src)
}
func (m *PendingRsp) XXX_Size() int {
	return xxx_messageInfo_PendingRsp.Size(m)
}
func (m *PendingRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PendingRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PendingRsp proto.InternalMessageInfo

func (m *PendingRsp) GetIncomeBalance() int64 {
	if m != nil {
		return m.IncomeBalance
	}
	return 0
}

func (m *PendingRsp) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *PendingRsp) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *PendingRsp) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *PendingRsp) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type PendingDetailReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	QueryType            uint32   `protobuf:"varint,2,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,4,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PendingDetailReq) Reset()         { *m = PendingDetailReq{} }
func (m *PendingDetailReq) String() string { return proto.CompactTextString(m) }
func (*PendingDetailReq) ProtoMessage()    {}
func (*PendingDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{10}
}
func (m *PendingDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PendingDetailReq.Unmarshal(m, b)
}
func (m *PendingDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PendingDetailReq.Marshal(b, m, deterministic)
}
func (dst *PendingDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PendingDetailReq.Merge(dst, src)
}
func (m *PendingDetailReq) XXX_Size() int {
	return xxx_messageInfo_PendingDetailReq.Size(m)
}
func (m *PendingDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PendingDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_PendingDetailReq proto.InternalMessageInfo

func (m *PendingDetailReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *PendingDetailReq) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

func (m *PendingDetailReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *PendingDetailReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type PendingDetail struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	ChannelName          string   `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	Consume              int64    `protobuf:"varint,5,opt,name=consume,proto3" json:"consume,omitempty"`
	Income               int64    `protobuf:"varint,6,opt,name=income,proto3" json:"income,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,7,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PendingDetail) Reset()         { *m = PendingDetail{} }
func (m *PendingDetail) String() string { return proto.CompactTextString(m) }
func (*PendingDetail) ProtoMessage()    {}
func (*PendingDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{11}
}
func (m *PendingDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PendingDetail.Unmarshal(m, b)
}
func (m *PendingDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PendingDetail.Marshal(b, m, deterministic)
}
func (dst *PendingDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PendingDetail.Merge(dst, src)
}
func (m *PendingDetail) XXX_Size() int {
	return xxx_messageInfo_PendingDetail.Size(m)
}
func (m *PendingDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_PendingDetail.DiscardUnknown(m)
}

var xxx_messageInfo_PendingDetail proto.InternalMessageInfo

func (m *PendingDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PendingDetail) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *PendingDetail) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PendingDetail) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *PendingDetail) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *PendingDetail) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *PendingDetail) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type PendingDetailRsp struct {
	PendingList          []*PendingDetail `protobuf:"bytes,1,rep,name=pending_list,json=pendingList,proto3" json:"pending_list,omitempty"`
	NextPage             bool             `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PendingDetailRsp) Reset()         { *m = PendingDetailRsp{} }
func (m *PendingDetailRsp) String() string { return proto.CompactTextString(m) }
func (*PendingDetailRsp) ProtoMessage()    {}
func (*PendingDetailRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{12}
}
func (m *PendingDetailRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PendingDetailRsp.Unmarshal(m, b)
}
func (m *PendingDetailRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PendingDetailRsp.Marshal(b, m, deterministic)
}
func (dst *PendingDetailRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PendingDetailRsp.Merge(dst, src)
}
func (m *PendingDetailRsp) XXX_Size() int {
	return xxx_messageInfo_PendingDetailRsp.Size(m)
}
func (m *PendingDetailRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PendingDetailRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PendingDetailRsp proto.InternalMessageInfo

func (m *PendingDetailRsp) GetPendingList() []*PendingDetail {
	if m != nil {
		return m.PendingList
	}
	return nil
}

func (m *PendingDetailRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

type YuyinPendingDetail struct {
	PaidUid              uint32   `protobuf:"varint,1,opt,name=paid_uid,json=paidUid,proto3" json:"paid_uid,omitempty"`
	AnchorId             uint32   `protobuf:"varint,2,opt,name=anchor_id,json=anchorId,proto3" json:"anchor_id,omitempty"`
	PaidName             string   `protobuf:"bytes,3,opt,name=paid_name,json=paidName,proto3" json:"paid_name,omitempty"`
	AnchorName           string   `protobuf:"bytes,4,opt,name=anchor_name,json=anchorName,proto3" json:"anchor_name,omitempty"`
	Consume              int64    `protobuf:"varint,5,opt,name=consume,proto3" json:"consume,omitempty"`
	Income               int64    `protobuf:"varint,6,opt,name=income,proto3" json:"income,omitempty"`
	PaidTtid             string   `protobuf:"bytes,7,opt,name=paid_ttid,json=paidTtid,proto3" json:"paid_ttid,omitempty"`
	AnchorTtid           string   `protobuf:"bytes,8,opt,name=anchor_ttid,json=anchorTtid,proto3" json:"anchor_ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinPendingDetail) Reset()         { *m = YuyinPendingDetail{} }
func (m *YuyinPendingDetail) String() string { return proto.CompactTextString(m) }
func (*YuyinPendingDetail) ProtoMessage()    {}
func (*YuyinPendingDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{13}
}
func (m *YuyinPendingDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinPendingDetail.Unmarshal(m, b)
}
func (m *YuyinPendingDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinPendingDetail.Marshal(b, m, deterministic)
}
func (dst *YuyinPendingDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinPendingDetail.Merge(dst, src)
}
func (m *YuyinPendingDetail) XXX_Size() int {
	return xxx_messageInfo_YuyinPendingDetail.Size(m)
}
func (m *YuyinPendingDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinPendingDetail.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinPendingDetail proto.InternalMessageInfo

func (m *YuyinPendingDetail) GetPaidUid() uint32 {
	if m != nil {
		return m.PaidUid
	}
	return 0
}

func (m *YuyinPendingDetail) GetAnchorId() uint32 {
	if m != nil {
		return m.AnchorId
	}
	return 0
}

func (m *YuyinPendingDetail) GetPaidName() string {
	if m != nil {
		return m.PaidName
	}
	return ""
}

func (m *YuyinPendingDetail) GetAnchorName() string {
	if m != nil {
		return m.AnchorName
	}
	return ""
}

func (m *YuyinPendingDetail) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *YuyinPendingDetail) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *YuyinPendingDetail) GetPaidTtid() string {
	if m != nil {
		return m.PaidTtid
	}
	return ""
}

func (m *YuyinPendingDetail) GetAnchorTtid() string {
	if m != nil {
		return m.AnchorTtid
	}
	return ""
}

type YuyinPendingDetailRsp struct {
	PendingList          []*YuyinPendingDetail `protobuf:"bytes,1,rep,name=pending_list,json=pendingList,proto3" json:"pending_list,omitempty"`
	NextPage             bool                  `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *YuyinPendingDetailRsp) Reset()         { *m = YuyinPendingDetailRsp{} }
func (m *YuyinPendingDetailRsp) String() string { return proto.CompactTextString(m) }
func (*YuyinPendingDetailRsp) ProtoMessage()    {}
func (*YuyinPendingDetailRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{14}
}
func (m *YuyinPendingDetailRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinPendingDetailRsp.Unmarshal(m, b)
}
func (m *YuyinPendingDetailRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinPendingDetailRsp.Marshal(b, m, deterministic)
}
func (dst *YuyinPendingDetailRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinPendingDetailRsp.Merge(dst, src)
}
func (m *YuyinPendingDetailRsp) XXX_Size() int {
	return xxx_messageInfo_YuyinPendingDetailRsp.Size(m)
}
func (m *YuyinPendingDetailRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinPendingDetailRsp.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinPendingDetailRsp proto.InternalMessageInfo

func (m *YuyinPendingDetailRsp) GetPendingList() []*YuyinPendingDetail {
	if m != nil {
		return m.PendingList
	}
	return nil
}

func (m *YuyinPendingDetailRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

type TodayIncomeReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TodayIncomeReq) Reset()         { *m = TodayIncomeReq{} }
func (m *TodayIncomeReq) String() string { return proto.CompactTextString(m) }
func (*TodayIncomeReq) ProtoMessage()    {}
func (*TodayIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{15}
}
func (m *TodayIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TodayIncomeReq.Unmarshal(m, b)
}
func (m *TodayIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TodayIncomeReq.Marshal(b, m, deterministic)
}
func (dst *TodayIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TodayIncomeReq.Merge(dst, src)
}
func (m *TodayIncomeReq) XXX_Size() int {
	return xxx_messageInfo_TodayIncomeReq.Size(m)
}
func (m *TodayIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TodayIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_TodayIncomeReq proto.InternalMessageInfo

func (m *TodayIncomeReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type TodayIncomeInfo struct {
	Channelid            uint32   `protobuf:"varint,1,opt,name=channelid,proto3" json:"channelid,omitempty"`
	ChannelName          string   `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	Members              int64    `protobuf:"varint,3,opt,name=members,proto3" json:"members,omitempty"`
	Consume              int64    `protobuf:"varint,4,opt,name=consume,proto3" json:"consume,omitempty"`
	Income               int64    `protobuf:"varint,5,opt,name=income,proto3" json:"income,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,6,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TodayIncomeInfo) Reset()         { *m = TodayIncomeInfo{} }
func (m *TodayIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*TodayIncomeInfo) ProtoMessage()    {}
func (*TodayIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{16}
}
func (m *TodayIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TodayIncomeInfo.Unmarshal(m, b)
}
func (m *TodayIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TodayIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *TodayIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TodayIncomeInfo.Merge(dst, src)
}
func (m *TodayIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_TodayIncomeInfo.Size(m)
}
func (m *TodayIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TodayIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TodayIncomeInfo proto.InternalMessageInfo

func (m *TodayIncomeInfo) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *TodayIncomeInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *TodayIncomeInfo) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *TodayIncomeInfo) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *TodayIncomeInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *TodayIncomeInfo) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type TodayIncomeRsp struct {
	ChannelList          []*TodayIncomeInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *TodayIncomeRsp) Reset()         { *m = TodayIncomeRsp{} }
func (m *TodayIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*TodayIncomeRsp) ProtoMessage()    {}
func (*TodayIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{17}
}
func (m *TodayIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TodayIncomeRsp.Unmarshal(m, b)
}
func (m *TodayIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TodayIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *TodayIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TodayIncomeRsp.Merge(dst, src)
}
func (m *TodayIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_TodayIncomeRsp.Size(m)
}
func (m *TodayIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_TodayIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_TodayIncomeRsp proto.InternalMessageInfo

func (m *TodayIncomeRsp) GetChannelList() []*TodayIncomeInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type YuyinTodayIncomeReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinTodayIncomeReq) Reset()         { *m = YuyinTodayIncomeReq{} }
func (m *YuyinTodayIncomeReq) String() string { return proto.CompactTextString(m) }
func (*YuyinTodayIncomeReq) ProtoMessage()    {}
func (*YuyinTodayIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{18}
}
func (m *YuyinTodayIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinTodayIncomeReq.Unmarshal(m, b)
}
func (m *YuyinTodayIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinTodayIncomeReq.Marshal(b, m, deterministic)
}
func (dst *YuyinTodayIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinTodayIncomeReq.Merge(dst, src)
}
func (m *YuyinTodayIncomeReq) XXX_Size() int {
	return xxx_messageInfo_YuyinTodayIncomeReq.Size(m)
}
func (m *YuyinTodayIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinTodayIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinTodayIncomeReq proto.InternalMessageInfo

func (m *YuyinTodayIncomeReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type YuyinTodayIncomeInfo struct {
	AnchorName           string   `protobuf:"bytes,1,opt,name=anchor_name,json=anchorName,proto3" json:"anchor_name,omitempty"`
	Members              int64    `protobuf:"varint,3,opt,name=members,proto3" json:"members,omitempty"`
	Fee                  int64    `protobuf:"varint,4,opt,name=fee,proto3" json:"fee,omitempty"`
	PresentFee           int64    `protobuf:"varint,5,opt,name=present_fee,json=presentFee,proto3" json:"present_fee,omitempty"`
	KnightFee            int64    `protobuf:"varint,6,opt,name=knight_fee,json=knightFee,proto3" json:"knight_fee,omitempty"`
	Income               int64    `protobuf:"varint,7,opt,name=income,proto3" json:"income,omitempty"`
	Consume              int64    `protobuf:"varint,8,opt,name=consume,proto3" json:"consume,omitempty"`
	VirtualLiveFee       int64    `protobuf:"varint,9,opt,name=virtual_live_fee,json=virtualLiveFee,proto3" json:"virtual_live_fee,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinTodayIncomeInfo) Reset()         { *m = YuyinTodayIncomeInfo{} }
func (m *YuyinTodayIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*YuyinTodayIncomeInfo) ProtoMessage()    {}
func (*YuyinTodayIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{19}
}
func (m *YuyinTodayIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinTodayIncomeInfo.Unmarshal(m, b)
}
func (m *YuyinTodayIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinTodayIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *YuyinTodayIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinTodayIncomeInfo.Merge(dst, src)
}
func (m *YuyinTodayIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_YuyinTodayIncomeInfo.Size(m)
}
func (m *YuyinTodayIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinTodayIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinTodayIncomeInfo proto.InternalMessageInfo

func (m *YuyinTodayIncomeInfo) GetAnchorName() string {
	if m != nil {
		return m.AnchorName
	}
	return ""
}

func (m *YuyinTodayIncomeInfo) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *YuyinTodayIncomeInfo) GetFee() int64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *YuyinTodayIncomeInfo) GetPresentFee() int64 {
	if m != nil {
		return m.PresentFee
	}
	return 0
}

func (m *YuyinTodayIncomeInfo) GetKnightFee() int64 {
	if m != nil {
		return m.KnightFee
	}
	return 0
}

func (m *YuyinTodayIncomeInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *YuyinTodayIncomeInfo) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *YuyinTodayIncomeInfo) GetVirtualLiveFee() int64 {
	if m != nil {
		return m.VirtualLiveFee
	}
	return 0
}

type YuyinTodayIncomeRsp struct {
	List                 []*YuyinTodayIncomeInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *YuyinTodayIncomeRsp) Reset()         { *m = YuyinTodayIncomeRsp{} }
func (m *YuyinTodayIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*YuyinTodayIncomeRsp) ProtoMessage()    {}
func (*YuyinTodayIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{20}
}
func (m *YuyinTodayIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinTodayIncomeRsp.Unmarshal(m, b)
}
func (m *YuyinTodayIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinTodayIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *YuyinTodayIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinTodayIncomeRsp.Merge(dst, src)
}
func (m *YuyinTodayIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_YuyinTodayIncomeRsp.Size(m)
}
func (m *YuyinTodayIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinTodayIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinTodayIncomeRsp proto.InternalMessageInfo

func (m *YuyinTodayIncomeRsp) GetList() []*YuyinTodayIncomeInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type MonthIncomeReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	MonthTime            int64    `protobuf:"varint,2,opt,name=month_time,json=monthTime,proto3" json:"month_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MonthIncomeReq) Reset()         { *m = MonthIncomeReq{} }
func (m *MonthIncomeReq) String() string { return proto.CompactTextString(m) }
func (*MonthIncomeReq) ProtoMessage()    {}
func (*MonthIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{21}
}
func (m *MonthIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MonthIncomeReq.Unmarshal(m, b)
}
func (m *MonthIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MonthIncomeReq.Marshal(b, m, deterministic)
}
func (dst *MonthIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MonthIncomeReq.Merge(dst, src)
}
func (m *MonthIncomeReq) XXX_Size() int {
	return xxx_messageInfo_MonthIncomeReq.Size(m)
}
func (m *MonthIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MonthIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_MonthIncomeReq proto.InternalMessageInfo

func (m *MonthIncomeReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *MonthIncomeReq) GetMonthTime() int64 {
	if m != nil {
		return m.MonthTime
	}
	return 0
}

type MonthIncomeInfo struct {
	DateTime             int64    `protobuf:"varint,1,opt,name=date_time,json=dateTime,proto3" json:"date_time,omitempty"`
	Consume              int64    `protobuf:"varint,2,opt,name=consume,proto3" json:"consume,omitempty"`
	Members              int64    `protobuf:"varint,3,opt,name=members,proto3" json:"members,omitempty"`
	Income               int64    `protobuf:"varint,4,opt,name=income,proto3" json:"income,omitempty"`
	Fee                  int64    `protobuf:"varint,5,opt,name=fee,proto3" json:"fee,omitempty"`
	PresentFee           int64    `protobuf:"varint,6,opt,name=present_fee,json=presentFee,proto3" json:"present_fee,omitempty"`
	KnightFee            int64    `protobuf:"varint,7,opt,name=knight_fee,json=knightFee,proto3" json:"knight_fee,omitempty"`
	WerewolfFee          int64    `protobuf:"varint,8,opt,name=werewolf_fee,json=werewolfFee,proto3" json:"werewolf_fee,omitempty"`
	InteractGameFee      int64    `protobuf:"varint,9,opt,name=interact_game_fee,json=interactGameFee,proto3" json:"interact_game_fee,omitempty"`
	VirtualLiveFee       int64    `protobuf:"varint,10,opt,name=virtual_live_fee,json=virtualLiveFee,proto3" json:"virtual_live_fee,omitempty"`
	EsportFee            int64    `protobuf:"varint,11,opt,name=esport_fee,json=esportFee,proto3" json:"esport_fee,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MonthIncomeInfo) Reset()         { *m = MonthIncomeInfo{} }
func (m *MonthIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*MonthIncomeInfo) ProtoMessage()    {}
func (*MonthIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{22}
}
func (m *MonthIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MonthIncomeInfo.Unmarshal(m, b)
}
func (m *MonthIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MonthIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *MonthIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MonthIncomeInfo.Merge(dst, src)
}
func (m *MonthIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_MonthIncomeInfo.Size(m)
}
func (m *MonthIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MonthIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MonthIncomeInfo proto.InternalMessageInfo

func (m *MonthIncomeInfo) GetDateTime() int64 {
	if m != nil {
		return m.DateTime
	}
	return 0
}

func (m *MonthIncomeInfo) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *MonthIncomeInfo) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *MonthIncomeInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *MonthIncomeInfo) GetFee() int64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *MonthIncomeInfo) GetPresentFee() int64 {
	if m != nil {
		return m.PresentFee
	}
	return 0
}

func (m *MonthIncomeInfo) GetKnightFee() int64 {
	if m != nil {
		return m.KnightFee
	}
	return 0
}

func (m *MonthIncomeInfo) GetWerewolfFee() int64 {
	if m != nil {
		return m.WerewolfFee
	}
	return 0
}

func (m *MonthIncomeInfo) GetInteractGameFee() int64 {
	if m != nil {
		return m.InteractGameFee
	}
	return 0
}

func (m *MonthIncomeInfo) GetVirtualLiveFee() int64 {
	if m != nil {
		return m.VirtualLiveFee
	}
	return 0
}

func (m *MonthIncomeInfo) GetEsportFee() int64 {
	if m != nil {
		return m.EsportFee
	}
	return 0
}

type MonthIncomeRsp struct {
	MonthIncomeList      []*MonthIncomeInfo `protobuf:"bytes,1,rep,name=month_income_list,json=monthIncomeList,proto3" json:"month_income_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MonthIncomeRsp) Reset()         { *m = MonthIncomeRsp{} }
func (m *MonthIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*MonthIncomeRsp) ProtoMessage()    {}
func (*MonthIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{23}
}
func (m *MonthIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MonthIncomeRsp.Unmarshal(m, b)
}
func (m *MonthIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MonthIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *MonthIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MonthIncomeRsp.Merge(dst, src)
}
func (m *MonthIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_MonthIncomeRsp.Size(m)
}
func (m *MonthIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_MonthIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_MonthIncomeRsp proto.InternalMessageInfo

func (m *MonthIncomeRsp) GetMonthIncomeList() []*MonthIncomeInfo {
	if m != nil {
		return m.MonthIncomeList
	}
	return nil
}

type ConsumeSearchReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Channelid            uint32   `protobuf:"varint,3,opt,name=channelid,proto3" json:"channelid,omitempty"`
	StartTime            int64    `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Page                 uint32   `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,7,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsumeSearchReq) Reset()         { *m = ConsumeSearchReq{} }
func (m *ConsumeSearchReq) String() string { return proto.CompactTextString(m) }
func (*ConsumeSearchReq) ProtoMessage()    {}
func (*ConsumeSearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{24}
}
func (m *ConsumeSearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeSearchReq.Unmarshal(m, b)
}
func (m *ConsumeSearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeSearchReq.Marshal(b, m, deterministic)
}
func (dst *ConsumeSearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeSearchReq.Merge(dst, src)
}
func (m *ConsumeSearchReq) XXX_Size() int {
	return xxx_messageInfo_ConsumeSearchReq.Size(m)
}
func (m *ConsumeSearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeSearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeSearchReq proto.InternalMessageInfo

func (m *ConsumeSearchReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *ConsumeSearchReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ConsumeSearchReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *ConsumeSearchReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ConsumeSearchReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ConsumeSearchReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ConsumeSearchReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type ConsumeDetail struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Consume              int64    `protobuf:"varint,3,opt,name=consume,proto3" json:"consume,omitempty"`
	ConsumeTime          int64    `protobuf:"varint,4,opt,name=consume_time,json=consumeTime,proto3" json:"consume_time,omitempty"`
	ChannelName          string   `protobuf:"bytes,5,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsumeDetail) Reset()         { *m = ConsumeDetail{} }
func (m *ConsumeDetail) String() string { return proto.CompactTextString(m) }
func (*ConsumeDetail) ProtoMessage()    {}
func (*ConsumeDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{25}
}
func (m *ConsumeDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeDetail.Unmarshal(m, b)
}
func (m *ConsumeDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeDetail.Marshal(b, m, deterministic)
}
func (dst *ConsumeDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeDetail.Merge(dst, src)
}
func (m *ConsumeDetail) XXX_Size() int {
	return xxx_messageInfo_ConsumeDetail.Size(m)
}
func (m *ConsumeDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeDetail proto.InternalMessageInfo

func (m *ConsumeDetail) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ConsumeDetail) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ConsumeDetail) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *ConsumeDetail) GetConsumeTime() int64 {
	if m != nil {
		return m.ConsumeTime
	}
	return 0
}

func (m *ConsumeDetail) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

type ConsumeSearchRsp struct {
	NextPage             bool             `protobuf:"varint,1,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	IncomeList           []*ConsumeDetail `protobuf:"bytes,2,rep,name=income_list,json=incomeList,proto3" json:"income_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ConsumeSearchRsp) Reset()         { *m = ConsumeSearchRsp{} }
func (m *ConsumeSearchRsp) String() string { return proto.CompactTextString(m) }
func (*ConsumeSearchRsp) ProtoMessage()    {}
func (*ConsumeSearchRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{26}
}
func (m *ConsumeSearchRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeSearchRsp.Unmarshal(m, b)
}
func (m *ConsumeSearchRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeSearchRsp.Marshal(b, m, deterministic)
}
func (dst *ConsumeSearchRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeSearchRsp.Merge(dst, src)
}
func (m *ConsumeSearchRsp) XXX_Size() int {
	return xxx_messageInfo_ConsumeSearchRsp.Size(m)
}
func (m *ConsumeSearchRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeSearchRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeSearchRsp proto.InternalMessageInfo

func (m *ConsumeSearchRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

func (m *ConsumeSearchRsp) GetIncomeList() []*ConsumeDetail {
	if m != nil {
		return m.IncomeList
	}
	return nil
}

type YuyinConsumeSearchReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,3,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	StartTime            int64    `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Page                 uint32   `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,7,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinConsumeSearchReq) Reset()         { *m = YuyinConsumeSearchReq{} }
func (m *YuyinConsumeSearchReq) String() string { return proto.CompactTextString(m) }
func (*YuyinConsumeSearchReq) ProtoMessage()    {}
func (*YuyinConsumeSearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{27}
}
func (m *YuyinConsumeSearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinConsumeSearchReq.Unmarshal(m, b)
}
func (m *YuyinConsumeSearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinConsumeSearchReq.Marshal(b, m, deterministic)
}
func (dst *YuyinConsumeSearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinConsumeSearchReq.Merge(dst, src)
}
func (m *YuyinConsumeSearchReq) XXX_Size() int {
	return xxx_messageInfo_YuyinConsumeSearchReq.Size(m)
}
func (m *YuyinConsumeSearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinConsumeSearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinConsumeSearchReq proto.InternalMessageInfo

func (m *YuyinConsumeSearchReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *YuyinConsumeSearchReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *YuyinConsumeSearchReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *YuyinConsumeSearchReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *YuyinConsumeSearchReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *YuyinConsumeSearchReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *YuyinConsumeSearchReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type YuyinConsumeDetail struct {
	PaidAccount          string   `protobuf:"bytes,1,opt,name=paid_account,json=paidAccount,proto3" json:"paid_account,omitempty"`
	PaidName             string   `protobuf:"bytes,2,opt,name=paid_name,json=paidName,proto3" json:"paid_name,omitempty"`
	Consume              int64    `protobuf:"varint,3,opt,name=consume,proto3" json:"consume,omitempty"`
	ConsumeTime          int64    `protobuf:"varint,4,opt,name=consume_time,json=consumeTime,proto3" json:"consume_time,omitempty"`
	AnchorAccount        string   `protobuf:"bytes,5,opt,name=anchor_account,json=anchorAccount,proto3" json:"anchor_account,omitempty"`
	AnchorName           string   `protobuf:"bytes,6,opt,name=anchor_name,json=anchorName,proto3" json:"anchor_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinConsumeDetail) Reset()         { *m = YuyinConsumeDetail{} }
func (m *YuyinConsumeDetail) String() string { return proto.CompactTextString(m) }
func (*YuyinConsumeDetail) ProtoMessage()    {}
func (*YuyinConsumeDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{28}
}
func (m *YuyinConsumeDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinConsumeDetail.Unmarshal(m, b)
}
func (m *YuyinConsumeDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinConsumeDetail.Marshal(b, m, deterministic)
}
func (dst *YuyinConsumeDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinConsumeDetail.Merge(dst, src)
}
func (m *YuyinConsumeDetail) XXX_Size() int {
	return xxx_messageInfo_YuyinConsumeDetail.Size(m)
}
func (m *YuyinConsumeDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinConsumeDetail.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinConsumeDetail proto.InternalMessageInfo

func (m *YuyinConsumeDetail) GetPaidAccount() string {
	if m != nil {
		return m.PaidAccount
	}
	return ""
}

func (m *YuyinConsumeDetail) GetPaidName() string {
	if m != nil {
		return m.PaidName
	}
	return ""
}

func (m *YuyinConsumeDetail) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *YuyinConsumeDetail) GetConsumeTime() int64 {
	if m != nil {
		return m.ConsumeTime
	}
	return 0
}

func (m *YuyinConsumeDetail) GetAnchorAccount() string {
	if m != nil {
		return m.AnchorAccount
	}
	return ""
}

func (m *YuyinConsumeDetail) GetAnchorName() string {
	if m != nil {
		return m.AnchorName
	}
	return ""
}

type YuyinConsumeSearchRsp struct {
	NextPage             bool                  `protobuf:"varint,1,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	IncomeList           []*YuyinConsumeDetail `protobuf:"bytes,2,rep,name=income_list,json=incomeList,proto3" json:"income_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *YuyinConsumeSearchRsp) Reset()         { *m = YuyinConsumeSearchRsp{} }
func (m *YuyinConsumeSearchRsp) String() string { return proto.CompactTextString(m) }
func (*YuyinConsumeSearchRsp) ProtoMessage()    {}
func (*YuyinConsumeSearchRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{29}
}
func (m *YuyinConsumeSearchRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinConsumeSearchRsp.Unmarshal(m, b)
}
func (m *YuyinConsumeSearchRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinConsumeSearchRsp.Marshal(b, m, deterministic)
}
func (dst *YuyinConsumeSearchRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinConsumeSearchRsp.Merge(dst, src)
}
func (m *YuyinConsumeSearchRsp) XXX_Size() int {
	return xxx_messageInfo_YuyinConsumeSearchRsp.Size(m)
}
func (m *YuyinConsumeSearchRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinConsumeSearchRsp.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinConsumeSearchRsp proto.InternalMessageInfo

func (m *YuyinConsumeSearchRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

func (m *YuyinConsumeSearchRsp) GetIncomeList() []*YuyinConsumeDetail {
	if m != nil {
		return m.IncomeList
	}
	return nil
}

type ChannelIncomeReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	RangeType            uint32   `protobuf:"varint,3,opt,name=range_type,json=rangeType,proto3" json:"range_type,omitempty"`
	StartTime            int64    `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Page                 uint32   `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,7,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelIncomeReq) Reset()         { *m = ChannelIncomeReq{} }
func (m *ChannelIncomeReq) String() string { return proto.CompactTextString(m) }
func (*ChannelIncomeReq) ProtoMessage()    {}
func (*ChannelIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{30}
}
func (m *ChannelIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelIncomeReq.Unmarshal(m, b)
}
func (m *ChannelIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelIncomeReq.Marshal(b, m, deterministic)
}
func (dst *ChannelIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelIncomeReq.Merge(dst, src)
}
func (m *ChannelIncomeReq) XXX_Size() int {
	return xxx_messageInfo_ChannelIncomeReq.Size(m)
}
func (m *ChannelIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelIncomeReq proto.InternalMessageInfo

func (m *ChannelIncomeReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *ChannelIncomeReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *ChannelIncomeReq) GetRangeType() uint32 {
	if m != nil {
		return m.RangeType
	}
	return 0
}

func (m *ChannelIncomeReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ChannelIncomeReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ChannelIncomeReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ChannelIncomeReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type ChannelIncomeInfo struct {
	ConsumeTime          int64    `protobuf:"varint,1,opt,name=consume_time,json=consumeTime,proto3" json:"consume_time,omitempty"`
	Members              int64    `protobuf:"varint,2,opt,name=members,proto3" json:"members,omitempty"`
	Consume              int64    `protobuf:"varint,3,opt,name=consume,proto3" json:"consume,omitempty"`
	Income               int64    `protobuf:"varint,4,opt,name=income,proto3" json:"income,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelIncomeInfo) Reset()         { *m = ChannelIncomeInfo{} }
func (m *ChannelIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelIncomeInfo) ProtoMessage()    {}
func (*ChannelIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{31}
}
func (m *ChannelIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelIncomeInfo.Unmarshal(m, b)
}
func (m *ChannelIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelIncomeInfo.Merge(dst, src)
}
func (m *ChannelIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelIncomeInfo.Size(m)
}
func (m *ChannelIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelIncomeInfo proto.InternalMessageInfo

func (m *ChannelIncomeInfo) GetConsumeTime() int64 {
	if m != nil {
		return m.ConsumeTime
	}
	return 0
}

func (m *ChannelIncomeInfo) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *ChannelIncomeInfo) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *ChannelIncomeInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

type ChannelIncomeRsp struct {
	Consume              int64                `protobuf:"varint,1,opt,name=consume,proto3" json:"consume,omitempty"`
	Income               int64                `protobuf:"varint,2,opt,name=income,proto3" json:"income,omitempty"`
	NextPage             bool                 `protobuf:"varint,3,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	ChannelIncomeList    []*ChannelIncomeInfo `protobuf:"bytes,4,rep,name=channel_income_list,json=channelIncomeList,proto3" json:"channel_income_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ChannelIncomeRsp) Reset()         { *m = ChannelIncomeRsp{} }
func (m *ChannelIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*ChannelIncomeRsp) ProtoMessage()    {}
func (*ChannelIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{32}
}
func (m *ChannelIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelIncomeRsp.Unmarshal(m, b)
}
func (m *ChannelIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *ChannelIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelIncomeRsp.Merge(dst, src)
}
func (m *ChannelIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_ChannelIncomeRsp.Size(m)
}
func (m *ChannelIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelIncomeRsp proto.InternalMessageInfo

func (m *ChannelIncomeRsp) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *ChannelIncomeRsp) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *ChannelIncomeRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

func (m *ChannelIncomeRsp) GetChannelIncomeList() []*ChannelIncomeInfo {
	if m != nil {
		return m.ChannelIncomeList
	}
	return nil
}

type DayTrendInfo struct {
	Day                  uint32   `protobuf:"varint,1,opt,name=day,proto3" json:"day,omitempty"`
	Income               int64    `protobuf:"varint,2,opt,name=income,proto3" json:"income,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DayTrendInfo) Reset()         { *m = DayTrendInfo{} }
func (m *DayTrendInfo) String() string { return proto.CompactTextString(m) }
func (*DayTrendInfo) ProtoMessage()    {}
func (*DayTrendInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{33}
}
func (m *DayTrendInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DayTrendInfo.Unmarshal(m, b)
}
func (m *DayTrendInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DayTrendInfo.Marshal(b, m, deterministic)
}
func (dst *DayTrendInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DayTrendInfo.Merge(dst, src)
}
func (m *DayTrendInfo) XXX_Size() int {
	return xxx_messageInfo_DayTrendInfo.Size(m)
}
func (m *DayTrendInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DayTrendInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DayTrendInfo proto.InternalMessageInfo

func (m *DayTrendInfo) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *DayTrendInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

type GuildChannelInfo struct {
	Channelid            uint32   `protobuf:"varint,1,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Displayid            uint32   `protobuf:"varint,2,opt,name=displayid,proto3" json:"displayid,omitempty"`
	ChannelName          string   `protobuf:"bytes,3,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	Consume              int64    `protobuf:"varint,4,opt,name=consume,proto3" json:"consume,omitempty"`
	MonthTonowQoq        float32  `protobuf:"fixed32,5,opt,name=month_tonow_qoq,json=monthTonowQoq,proto3" json:"month_tonow_qoq,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,6,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildChannelInfo) Reset()         { *m = GuildChannelInfo{} }
func (m *GuildChannelInfo) String() string { return proto.CompactTextString(m) }
func (*GuildChannelInfo) ProtoMessage()    {}
func (*GuildChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{34}
}
func (m *GuildChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildChannelInfo.Unmarshal(m, b)
}
func (m *GuildChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildChannelInfo.Marshal(b, m, deterministic)
}
func (dst *GuildChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildChannelInfo.Merge(dst, src)
}
func (m *GuildChannelInfo) XXX_Size() int {
	return xxx_messageInfo_GuildChannelInfo.Size(m)
}
func (m *GuildChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildChannelInfo proto.InternalMessageInfo

func (m *GuildChannelInfo) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *GuildChannelInfo) GetDisplayid() uint32 {
	if m != nil {
		return m.Displayid
	}
	return 0
}

func (m *GuildChannelInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *GuildChannelInfo) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *GuildChannelInfo) GetMonthTonowQoq() float32 {
	if m != nil {
		return m.MonthTonowQoq
	}
	return 0
}

func (m *GuildChannelInfo) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type GuildInitInfoReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildInitInfoReq) Reset()         { *m = GuildInitInfoReq{} }
func (m *GuildInitInfoReq) String() string { return proto.CompactTextString(m) }
func (*GuildInitInfoReq) ProtoMessage()    {}
func (*GuildInitInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{35}
}
func (m *GuildInitInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildInitInfoReq.Unmarshal(m, b)
}
func (m *GuildInitInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildInitInfoReq.Marshal(b, m, deterministic)
}
func (dst *GuildInitInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildInitInfoReq.Merge(dst, src)
}
func (m *GuildInitInfoReq) XXX_Size() int {
	return xxx_messageInfo_GuildInitInfoReq.Size(m)
}
func (m *GuildInitInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildInitInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildInitInfoReq proto.InternalMessageInfo

func (m *GuildInitInfoReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type GuildInitInfoRsp struct {
	GuildName            string              `protobuf:"bytes,1,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	Guildid              uint32              `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid,omitempty"`
	Account              string              `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	GuildType            uint32              `protobuf:"varint,4,opt,name=guild_type,json=guildType,proto3" json:"guild_type,omitempty"`
	Applied              uint32              `protobuf:"varint,5,opt,name=applied,proto3" json:"applied,omitempty"`
	ServerTime           int64               `protobuf:"varint,6,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	TodayIncome          int64               `protobuf:"varint,7,opt,name=today_income,json=todayIncome,proto3" json:"today_income,omitempty"`
	YesterdayIncome      int64               `protobuf:"varint,8,opt,name=yesterday_income,json=yesterdayIncome,proto3" json:"yesterday_income,omitempty"`
	ThisMonthIncome      int64               `protobuf:"varint,9,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income,omitempty"`
	LastMonthIncome      int64               `protobuf:"varint,10,opt,name=last_month_income,json=lastMonthIncome,proto3" json:"last_month_income,omitempty"`
	DayList              []*DayTrendInfo     `protobuf:"bytes,11,rep,name=day_list,json=dayList,proto3" json:"day_list,omitempty"`
	ChannelList          []*GuildChannelInfo `protobuf:"bytes,12,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	ChannelCash          float64             `protobuf:"fixed64,13,opt,name=channel_cash,json=channelCash,proto3" json:"channel_cash,omitempty"`
	GameCash             float64             `protobuf:"fixed64,14,opt,name=game_cash,json=gameCash,proto3" json:"game_cash,omitempty"`
	LiveCash             float64             `protobuf:"fixed64,15,opt,name=live_cash,json=liveCash,proto3" json:"live_cash,omitempty"`
	DayQoq               float32             `protobuf:"fixed32,16,opt,name=day_qoq,json=dayQoq,proto3" json:"day_qoq,omitempty"`
	MonthQoq             float32             `protobuf:"fixed32,17,opt,name=month_qoq,json=monthQoq,proto3" json:"month_qoq,omitempty"`
	WealthValue          uint32              `protobuf:"varint,18,opt,name=wealth_value,json=wealthValue,proto3" json:"wealth_value,omitempty"`
	IsApplication        bool                `protobuf:"varint,19,opt,name=is_application,json=isApplication,proto3" json:"is_application,omitempty"`
	LastdayQoq           float32             `protobuf:"fixed32,20,opt,name=lastday_qoq,json=lastdayQoq,proto3" json:"lastday_qoq,omitempty"`
	MonthTonowQoq        float32             `protobuf:"fixed32,21,opt,name=month_tonow_qoq,json=monthTonowQoq,proto3" json:"month_tonow_qoq,omitempty"`
	LastMonthSamePeriod  int64               `protobuf:"varint,22,opt,name=last_month_same_period,json=lastMonthSamePeriod,proto3" json:"last_month_same_period,omitempty"`
	LastDayList          []*DayTrendInfo     `protobuf:"bytes,23,rep,name=last_day_list,json=lastDayList,proto3" json:"last_day_list,omitempty"`
	AudioBasicCash       float64             `protobuf:"fixed64,24,opt,name=audio_basic_cash,json=audioBasicCash,proto3" json:"audio_basic_cash,omitempty"`
	AudioExtraCash       float64             `protobuf:"fixed64,25,opt,name=audio_extra_cash,json=audioExtraCash,proto3" json:"audio_extra_cash,omitempty"`
	MonthSixIncome       int64               `protobuf:"varint,26,opt,name=month_six_income,json=monthSixIncome,proto3" json:"month_six_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GuildInitInfoRsp) Reset()         { *m = GuildInitInfoRsp{} }
func (m *GuildInitInfoRsp) String() string { return proto.CompactTextString(m) }
func (*GuildInitInfoRsp) ProtoMessage()    {}
func (*GuildInitInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{36}
}
func (m *GuildInitInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildInitInfoRsp.Unmarshal(m, b)
}
func (m *GuildInitInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildInitInfoRsp.Marshal(b, m, deterministic)
}
func (dst *GuildInitInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildInitInfoRsp.Merge(dst, src)
}
func (m *GuildInitInfoRsp) XXX_Size() int {
	return xxx_messageInfo_GuildInitInfoRsp.Size(m)
}
func (m *GuildInitInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildInitInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildInitInfoRsp proto.InternalMessageInfo

func (m *GuildInitInfoRsp) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *GuildInitInfoRsp) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *GuildInitInfoRsp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GuildInitInfoRsp) GetGuildType() uint32 {
	if m != nil {
		return m.GuildType
	}
	return 0
}

func (m *GuildInitInfoRsp) GetApplied() uint32 {
	if m != nil {
		return m.Applied
	}
	return 0
}

func (m *GuildInitInfoRsp) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *GuildInitInfoRsp) GetTodayIncome() int64 {
	if m != nil {
		return m.TodayIncome
	}
	return 0
}

func (m *GuildInitInfoRsp) GetYesterdayIncome() int64 {
	if m != nil {
		return m.YesterdayIncome
	}
	return 0
}

func (m *GuildInitInfoRsp) GetThisMonthIncome() int64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *GuildInitInfoRsp) GetLastMonthIncome() int64 {
	if m != nil {
		return m.LastMonthIncome
	}
	return 0
}

func (m *GuildInitInfoRsp) GetDayList() []*DayTrendInfo {
	if m != nil {
		return m.DayList
	}
	return nil
}

func (m *GuildInitInfoRsp) GetChannelList() []*GuildChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GuildInitInfoRsp) GetChannelCash() float64 {
	if m != nil {
		return m.ChannelCash
	}
	return 0
}

func (m *GuildInitInfoRsp) GetGameCash() float64 {
	if m != nil {
		return m.GameCash
	}
	return 0
}

func (m *GuildInitInfoRsp) GetLiveCash() float64 {
	if m != nil {
		return m.LiveCash
	}
	return 0
}

func (m *GuildInitInfoRsp) GetDayQoq() float32 {
	if m != nil {
		return m.DayQoq
	}
	return 0
}

func (m *GuildInitInfoRsp) GetMonthQoq() float32 {
	if m != nil {
		return m.MonthQoq
	}
	return 0
}

func (m *GuildInitInfoRsp) GetWealthValue() uint32 {
	if m != nil {
		return m.WealthValue
	}
	return 0
}

func (m *GuildInitInfoRsp) GetIsApplication() bool {
	if m != nil {
		return m.IsApplication
	}
	return false
}

func (m *GuildInitInfoRsp) GetLastdayQoq() float32 {
	if m != nil {
		return m.LastdayQoq
	}
	return 0
}

func (m *GuildInitInfoRsp) GetMonthTonowQoq() float32 {
	if m != nil {
		return m.MonthTonowQoq
	}
	return 0
}

func (m *GuildInitInfoRsp) GetLastMonthSamePeriod() int64 {
	if m != nil {
		return m.LastMonthSamePeriod
	}
	return 0
}

func (m *GuildInitInfoRsp) GetLastDayList() []*DayTrendInfo {
	if m != nil {
		return m.LastDayList
	}
	return nil
}

func (m *GuildInitInfoRsp) GetAudioBasicCash() float64 {
	if m != nil {
		return m.AudioBasicCash
	}
	return 0
}

func (m *GuildInitInfoRsp) GetAudioExtraCash() float64 {
	if m != nil {
		return m.AudioExtraCash
	}
	return 0
}

func (m *GuildInitInfoRsp) GetMonthSixIncome() int64 {
	if m != nil {
		return m.MonthSixIncome
	}
	return 0
}

type GuildCheckReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildCheckReq) Reset()         { *m = GuildCheckReq{} }
func (m *GuildCheckReq) String() string { return proto.CompactTextString(m) }
func (*GuildCheckReq) ProtoMessage()    {}
func (*GuildCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{37}
}
func (m *GuildCheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildCheckReq.Unmarshal(m, b)
}
func (m *GuildCheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildCheckReq.Marshal(b, m, deterministic)
}
func (dst *GuildCheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildCheckReq.Merge(dst, src)
}
func (m *GuildCheckReq) XXX_Size() int {
	return xxx_messageInfo_GuildCheckReq.Size(m)
}
func (m *GuildCheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildCheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildCheckReq proto.InternalMessageInfo

func (m *GuildCheckReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

// 检查公开房
type GuildCheckRsp struct {
	HasAmusement         bool     `protobuf:"varint,1,opt,name=has_amusement,json=hasAmusement,proto3" json:"has_amusement,omitempty"`
	MultiGuildType       uint32   `protobuf:"varint,2,opt,name=multi_guild_type,json=multiGuildType,proto3" json:"multi_guild_type,omitempty"`
	AudioGuildType       uint32   `protobuf:"varint,3,opt,name=audio_guild_type,json=audioGuildType,proto3" json:"audio_guild_type,omitempty"`
	MultiApplied         uint32   `protobuf:"varint,4,opt,name=multi_applied,json=multiApplied,proto3" json:"multi_applied,omitempty"`
	AudioApplied         uint32   `protobuf:"varint,5,opt,name=audio_applied,json=audioApplied,proto3" json:"audio_applied,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildCheckRsp) Reset()         { *m = GuildCheckRsp{} }
func (m *GuildCheckRsp) String() string { return proto.CompactTextString(m) }
func (*GuildCheckRsp) ProtoMessage()    {}
func (*GuildCheckRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{38}
}
func (m *GuildCheckRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildCheckRsp.Unmarshal(m, b)
}
func (m *GuildCheckRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildCheckRsp.Marshal(b, m, deterministic)
}
func (dst *GuildCheckRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildCheckRsp.Merge(dst, src)
}
func (m *GuildCheckRsp) XXX_Size() int {
	return xxx_messageInfo_GuildCheckRsp.Size(m)
}
func (m *GuildCheckRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildCheckRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildCheckRsp proto.InternalMessageInfo

func (m *GuildCheckRsp) GetHasAmusement() bool {
	if m != nil {
		return m.HasAmusement
	}
	return false
}

func (m *GuildCheckRsp) GetMultiGuildType() uint32 {
	if m != nil {
		return m.MultiGuildType
	}
	return 0
}

func (m *GuildCheckRsp) GetAudioGuildType() uint32 {
	if m != nil {
		return m.AudioGuildType
	}
	return 0
}

func (m *GuildCheckRsp) GetMultiApplied() uint32 {
	if m != nil {
		return m.MultiApplied
	}
	return 0
}

func (m *GuildCheckRsp) GetAudioApplied() uint32 {
	if m != nil {
		return m.AudioApplied
	}
	return 0
}

// 公会日流水环比(废弃)
type GuildConsumeDayQoqReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	Day                  int64    `protobuf:"varint,2,opt,name=day,proto3" json:"day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildConsumeDayQoqReq) Reset()         { *m = GuildConsumeDayQoqReq{} }
func (m *GuildConsumeDayQoqReq) String() string { return proto.CompactTextString(m) }
func (*GuildConsumeDayQoqReq) ProtoMessage()    {}
func (*GuildConsumeDayQoqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{39}
}
func (m *GuildConsumeDayQoqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildConsumeDayQoqReq.Unmarshal(m, b)
}
func (m *GuildConsumeDayQoqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildConsumeDayQoqReq.Marshal(b, m, deterministic)
}
func (dst *GuildConsumeDayQoqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildConsumeDayQoqReq.Merge(dst, src)
}
func (m *GuildConsumeDayQoqReq) XXX_Size() int {
	return xxx_messageInfo_GuildConsumeDayQoqReq.Size(m)
}
func (m *GuildConsumeDayQoqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildConsumeDayQoqReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildConsumeDayQoqReq proto.InternalMessageInfo

func (m *GuildConsumeDayQoqReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *GuildConsumeDayQoqReq) GetDay() int64 {
	if m != nil {
		return m.Day
	}
	return 0
}

type GuildConsumeDayQoqRsp struct {
	StatTime             int64    `protobuf:"varint,1,opt,name=stat_time,json=statTime,proto3" json:"stat_time,omitempty"`
	Consume              int64    `protobuf:"varint,2,opt,name=consume,proto3" json:"consume,omitempty"`
	CompareStatTime      int64    `protobuf:"varint,3,opt,name=compare_stat_time,json=compareStatTime,proto3" json:"compare_stat_time,omitempty"`
	CompareConsume       int64    `protobuf:"varint,4,opt,name=compare_consume,json=compareConsume,proto3" json:"compare_consume,omitempty"`
	Qoq                  float32  `protobuf:"fixed32,5,opt,name=qoq,proto3" json:"qoq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildConsumeDayQoqRsp) Reset()         { *m = GuildConsumeDayQoqRsp{} }
func (m *GuildConsumeDayQoqRsp) String() string { return proto.CompactTextString(m) }
func (*GuildConsumeDayQoqRsp) ProtoMessage()    {}
func (*GuildConsumeDayQoqRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{40}
}
func (m *GuildConsumeDayQoqRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildConsumeDayQoqRsp.Unmarshal(m, b)
}
func (m *GuildConsumeDayQoqRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildConsumeDayQoqRsp.Marshal(b, m, deterministic)
}
func (dst *GuildConsumeDayQoqRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildConsumeDayQoqRsp.Merge(dst, src)
}
func (m *GuildConsumeDayQoqRsp) XXX_Size() int {
	return xxx_messageInfo_GuildConsumeDayQoqRsp.Size(m)
}
func (m *GuildConsumeDayQoqRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildConsumeDayQoqRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildConsumeDayQoqRsp proto.InternalMessageInfo

func (m *GuildConsumeDayQoqRsp) GetStatTime() int64 {
	if m != nil {
		return m.StatTime
	}
	return 0
}

func (m *GuildConsumeDayQoqRsp) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *GuildConsumeDayQoqRsp) GetCompareStatTime() int64 {
	if m != nil {
		return m.CompareStatTime
	}
	return 0
}

func (m *GuildConsumeDayQoqRsp) GetCompareConsume() int64 {
	if m != nil {
		return m.CompareConsume
	}
	return 0
}

func (m *GuildConsumeDayQoqRsp) GetQoq() float32 {
	if m != nil {
		return m.Qoq
	}
	return 0
}

// 公会日流水环比 新
type GuildDayComparisonReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	Day                  int64    `protobuf:"varint,2,opt,name=day,proto3" json:"day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildDayComparisonReq) Reset()         { *m = GuildDayComparisonReq{} }
func (m *GuildDayComparisonReq) String() string { return proto.CompactTextString(m) }
func (*GuildDayComparisonReq) ProtoMessage()    {}
func (*GuildDayComparisonReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{41}
}
func (m *GuildDayComparisonReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildDayComparisonReq.Unmarshal(m, b)
}
func (m *GuildDayComparisonReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildDayComparisonReq.Marshal(b, m, deterministic)
}
func (dst *GuildDayComparisonReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildDayComparisonReq.Merge(dst, src)
}
func (m *GuildDayComparisonReq) XXX_Size() int {
	return xxx_messageInfo_GuildDayComparisonReq.Size(m)
}
func (m *GuildDayComparisonReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildDayComparisonReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildDayComparisonReq proto.InternalMessageInfo

func (m *GuildDayComparisonReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *GuildDayComparisonReq) GetDay() int64 {
	if m != nil {
		return m.Day
	}
	return 0
}

type DayComparisonInfo struct {
	StatTime             uint64   `protobuf:"varint,1,opt,name=stat_time,json=statTime,proto3" json:"stat_time,omitempty"`
	DayIncome            uint64   `protobuf:"varint,2,opt,name=day_income,json=dayIncome,proto3" json:"day_income,omitempty"`
	LastMonthSamePeriod  uint64   `protobuf:"varint,3,opt,name=last_month_same_period,json=lastMonthSamePeriod,proto3" json:"last_month_same_period,omitempty"`
	Qoq                  float32  `protobuf:"fixed32,5,opt,name=qoq,proto3" json:"qoq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DayComparisonInfo) Reset()         { *m = DayComparisonInfo{} }
func (m *DayComparisonInfo) String() string { return proto.CompactTextString(m) }
func (*DayComparisonInfo) ProtoMessage()    {}
func (*DayComparisonInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{42}
}
func (m *DayComparisonInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DayComparisonInfo.Unmarshal(m, b)
}
func (m *DayComparisonInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DayComparisonInfo.Marshal(b, m, deterministic)
}
func (dst *DayComparisonInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DayComparisonInfo.Merge(dst, src)
}
func (m *DayComparisonInfo) XXX_Size() int {
	return xxx_messageInfo_DayComparisonInfo.Size(m)
}
func (m *DayComparisonInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DayComparisonInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DayComparisonInfo proto.InternalMessageInfo

func (m *DayComparisonInfo) GetStatTime() uint64 {
	if m != nil {
		return m.StatTime
	}
	return 0
}

func (m *DayComparisonInfo) GetDayIncome() uint64 {
	if m != nil {
		return m.DayIncome
	}
	return 0
}

func (m *DayComparisonInfo) GetLastMonthSamePeriod() uint64 {
	if m != nil {
		return m.LastMonthSamePeriod
	}
	return 0
}

func (m *DayComparisonInfo) GetQoq() float32 {
	if m != nil {
		return m.Qoq
	}
	return 0
}

type GuildDayComparisonResp struct {
	// 根据合作库类型返回
	AmuseDayComparison   *DayComparisonInfo `protobuf:"bytes,1,opt,name=amuse_day_comparison,json=amuseDayComparison,proto3" json:"amuse_day_comparison,omitempty"`
	YuyinDayComparison   *DayComparisonInfo `protobuf:"bytes,2,opt,name=yuyin_day_comparison,json=yuyinDayComparison,proto3" json:"yuyin_day_comparison,omitempty"`
	EsportDayComparison  *DayComparisonInfo `protobuf:"bytes,3,opt,name=esport_day_comparison,json=esportDayComparison,proto3" json:"esport_day_comparison,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GuildDayComparisonResp) Reset()         { *m = GuildDayComparisonResp{} }
func (m *GuildDayComparisonResp) String() string { return proto.CompactTextString(m) }
func (*GuildDayComparisonResp) ProtoMessage()    {}
func (*GuildDayComparisonResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{43}
}
func (m *GuildDayComparisonResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildDayComparisonResp.Unmarshal(m, b)
}
func (m *GuildDayComparisonResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildDayComparisonResp.Marshal(b, m, deterministic)
}
func (dst *GuildDayComparisonResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildDayComparisonResp.Merge(dst, src)
}
func (m *GuildDayComparisonResp) XXX_Size() int {
	return xxx_messageInfo_GuildDayComparisonResp.Size(m)
}
func (m *GuildDayComparisonResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildDayComparisonResp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildDayComparisonResp proto.InternalMessageInfo

func (m *GuildDayComparisonResp) GetAmuseDayComparison() *DayComparisonInfo {
	if m != nil {
		return m.AmuseDayComparison
	}
	return nil
}

func (m *GuildDayComparisonResp) GetYuyinDayComparison() *DayComparisonInfo {
	if m != nil {
		return m.YuyinDayComparison
	}
	return nil
}

func (m *GuildDayComparisonResp) GetEsportDayComparison() *DayComparisonInfo {
	if m != nil {
		return m.EsportDayComparison
	}
	return nil
}

type ConsumeRankItem struct {
	Alias                string   `protobuf:"bytes,1,opt,name=alias,proto3" json:"alias,omitempty"`
	NickName             string   `protobuf:"bytes,2,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Consume              int64    `protobuf:"varint,4,opt,name=consume,proto3" json:"consume,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsumeRankItem) Reset()         { *m = ConsumeRankItem{} }
func (m *ConsumeRankItem) String() string { return proto.CompactTextString(m) }
func (*ConsumeRankItem) ProtoMessage()    {}
func (*ConsumeRankItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{44}
}
func (m *ConsumeRankItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeRankItem.Unmarshal(m, b)
}
func (m *ConsumeRankItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeRankItem.Marshal(b, m, deterministic)
}
func (dst *ConsumeRankItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeRankItem.Merge(dst, src)
}
func (m *ConsumeRankItem) XXX_Size() int {
	return xxx_messageInfo_ConsumeRankItem.Size(m)
}
func (m *ConsumeRankItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeRankItem.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeRankItem proto.InternalMessageInfo

func (m *ConsumeRankItem) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *ConsumeRankItem) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *ConsumeRankItem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ConsumeRankItem) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

// 公会推荐房消费Top10用户列表
type ConsumeRankReq struct {
	ConsumeType          RangeType `protobuf:"varint,1,opt,name=consume_type,json=consumeType,proto3,enum=golddiamond_logic.RangeType" json:"consume_type,omitempty"`
	Guildid              uint32    `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ConsumeRankReq) Reset()         { *m = ConsumeRankReq{} }
func (m *ConsumeRankReq) String() string { return proto.CompactTextString(m) }
func (*ConsumeRankReq) ProtoMessage()    {}
func (*ConsumeRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{45}
}
func (m *ConsumeRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeRankReq.Unmarshal(m, b)
}
func (m *ConsumeRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeRankReq.Marshal(b, m, deterministic)
}
func (dst *ConsumeRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeRankReq.Merge(dst, src)
}
func (m *ConsumeRankReq) XXX_Size() int {
	return xxx_messageInfo_ConsumeRankReq.Size(m)
}
func (m *ConsumeRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeRankReq proto.InternalMessageInfo

func (m *ConsumeRankReq) GetConsumeType() RangeType {
	if m != nil {
		return m.ConsumeType
	}
	return RangeType_DAY_RANGE_TYPE
}

func (m *ConsumeRankReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type ConsumeRankRsp struct {
	ConsumeRankList      []*ConsumeRankItem `protobuf:"bytes,1,rep,name=consume_rank_list,json=consumeRankList,proto3" json:"consume_rank_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ConsumeRankRsp) Reset()         { *m = ConsumeRankRsp{} }
func (m *ConsumeRankRsp) String() string { return proto.CompactTextString(m) }
func (*ConsumeRankRsp) ProtoMessage()    {}
func (*ConsumeRankRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{46}
}
func (m *ConsumeRankRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeRankRsp.Unmarshal(m, b)
}
func (m *ConsumeRankRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeRankRsp.Marshal(b, m, deterministic)
}
func (dst *ConsumeRankRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeRankRsp.Merge(dst, src)
}
func (m *ConsumeRankRsp) XXX_Size() int {
	return xxx_messageInfo_ConsumeRankRsp.Size(m)
}
func (m *ConsumeRankRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeRankRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeRankRsp proto.InternalMessageInfo

func (m *ConsumeRankRsp) GetConsumeRankList() []*ConsumeRankItem {
	if m != nil {
		return m.ConsumeRankList
	}
	return nil
}

// 公会推荐房日流水消费数据
type RoomConsumeQoqReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Day                  int64    `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoomConsumeQoqReq) Reset()         { *m = RoomConsumeQoqReq{} }
func (m *RoomConsumeQoqReq) String() string { return proto.CompactTextString(m) }
func (*RoomConsumeQoqReq) ProtoMessage()    {}
func (*RoomConsumeQoqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{47}
}
func (m *RoomConsumeQoqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoomConsumeQoqReq.Unmarshal(m, b)
}
func (m *RoomConsumeQoqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoomConsumeQoqReq.Marshal(b, m, deterministic)
}
func (dst *RoomConsumeQoqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoomConsumeQoqReq.Merge(dst, src)
}
func (m *RoomConsumeQoqReq) XXX_Size() int {
	return xxx_messageInfo_RoomConsumeQoqReq.Size(m)
}
func (m *RoomConsumeQoqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RoomConsumeQoqReq.DiscardUnknown(m)
}

var xxx_messageInfo_RoomConsumeQoqReq proto.InternalMessageInfo

func (m *RoomConsumeQoqReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *RoomConsumeQoqReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *RoomConsumeQoqReq) GetDay() int64 {
	if m != nil {
		return m.Day
	}
	return 0
}

type RoomConsumeQoqRsp struct {
	StatTime             int64    `protobuf:"varint,1,opt,name=stat_time,json=statTime,proto3" json:"stat_time,omitempty"`
	Consume              int64    `protobuf:"varint,2,opt,name=consume,proto3" json:"consume,omitempty"`
	CompareStatTime      int64    `protobuf:"varint,3,opt,name=compare_stat_time,json=compareStatTime,proto3" json:"compare_stat_time,omitempty"`
	CompareConsume       int64    `protobuf:"varint,4,opt,name=compare_consume,json=compareConsume,proto3" json:"compare_consume,omitempty"`
	Qoq                  float32  `protobuf:"fixed32,5,opt,name=qoq,proto3" json:"qoq,omitempty"`
	Channelid            uint32   `protobuf:"varint,6,opt,name=channelid,proto3" json:"channelid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoomConsumeQoqRsp) Reset()         { *m = RoomConsumeQoqRsp{} }
func (m *RoomConsumeQoqRsp) String() string { return proto.CompactTextString(m) }
func (*RoomConsumeQoqRsp) ProtoMessage()    {}
func (*RoomConsumeQoqRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{48}
}
func (m *RoomConsumeQoqRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoomConsumeQoqRsp.Unmarshal(m, b)
}
func (m *RoomConsumeQoqRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoomConsumeQoqRsp.Marshal(b, m, deterministic)
}
func (dst *RoomConsumeQoqRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoomConsumeQoqRsp.Merge(dst, src)
}
func (m *RoomConsumeQoqRsp) XXX_Size() int {
	return xxx_messageInfo_RoomConsumeQoqRsp.Size(m)
}
func (m *RoomConsumeQoqRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RoomConsumeQoqRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RoomConsumeQoqRsp proto.InternalMessageInfo

func (m *RoomConsumeQoqRsp) GetStatTime() int64 {
	if m != nil {
		return m.StatTime
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetCompareStatTime() int64 {
	if m != nil {
		return m.CompareStatTime
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetCompareConsume() int64 {
	if m != nil {
		return m.CompareConsume
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetQoq() float32 {
	if m != nil {
		return m.Qoq
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

type GetGuildAnchorListReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildAnchorListReq) Reset()         { *m = GetGuildAnchorListReq{} }
func (m *GetGuildAnchorListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildAnchorListReq) ProtoMessage()    {}
func (*GetGuildAnchorListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{49}
}
func (m *GetGuildAnchorListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAnchorListReq.Unmarshal(m, b)
}
func (m *GetGuildAnchorListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAnchorListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildAnchorListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAnchorListReq.Merge(dst, src)
}
func (m *GetGuildAnchorListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildAnchorListReq.Size(m)
}
func (m *GetGuildAnchorListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAnchorListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAnchorListReq proto.InternalMessageInfo

func (m *GetGuildAnchorListReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *GetGuildAnchorListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGuildAnchorListReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type GuildAnchorSimpleInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Ttid                 string   `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildAnchorSimpleInfo) Reset()         { *m = GuildAnchorSimpleInfo{} }
func (m *GuildAnchorSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*GuildAnchorSimpleInfo) ProtoMessage()    {}
func (*GuildAnchorSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{50}
}
func (m *GuildAnchorSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildAnchorSimpleInfo.Unmarshal(m, b)
}
func (m *GuildAnchorSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildAnchorSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *GuildAnchorSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildAnchorSimpleInfo.Merge(dst, src)
}
func (m *GuildAnchorSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_GuildAnchorSimpleInfo.Size(m)
}
func (m *GuildAnchorSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildAnchorSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildAnchorSimpleInfo proto.InternalMessageInfo

func (m *GuildAnchorSimpleInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GuildAnchorSimpleInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GuildAnchorSimpleInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

type GetGuildAnchorListRsp struct {
	List                 []*GuildAnchorSimpleInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32                   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetGuildAnchorListRsp) Reset()         { *m = GetGuildAnchorListRsp{} }
func (m *GetGuildAnchorListRsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildAnchorListRsp) ProtoMessage()    {}
func (*GetGuildAnchorListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{51}
}
func (m *GetGuildAnchorListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAnchorListRsp.Unmarshal(m, b)
}
func (m *GetGuildAnchorListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAnchorListRsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildAnchorListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAnchorListRsp.Merge(dst, src)
}
func (m *GetGuildAnchorListRsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildAnchorListRsp.Size(m)
}
func (m *GetGuildAnchorListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAnchorListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAnchorListRsp proto.InternalMessageInfo

func (m *GetGuildAnchorListRsp) GetList() []*GuildAnchorSimpleInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetGuildAnchorListRsp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetGuildIdReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildIdReq) Reset()         { *m = GetGuildIdReq{} }
func (m *GetGuildIdReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildIdReq) ProtoMessage()    {}
func (*GetGuildIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{52}
}
func (m *GetGuildIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildIdReq.Unmarshal(m, b)
}
func (m *GetGuildIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildIdReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildIdReq.Merge(dst, src)
}
func (m *GetGuildIdReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildIdReq.Size(m)
}
func (m *GetGuildIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildIdReq proto.InternalMessageInfo

type GetGuildIdRsp struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	GuildShortId         uint32   `protobuf:"varint,3,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildIdRsp) Reset()         { *m = GetGuildIdRsp{} }
func (m *GetGuildIdRsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildIdRsp) ProtoMessage()    {}
func (*GetGuildIdRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{53}
}
func (m *GetGuildIdRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildIdRsp.Unmarshal(m, b)
}
func (m *GetGuildIdRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildIdRsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildIdRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildIdRsp.Merge(dst, src)
}
func (m *GetGuildIdRsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildIdRsp.Size(m)
}
func (m *GetGuildIdRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildIdRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildIdRsp proto.InternalMessageInfo

func (m *GetGuildIdRsp) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *GetGuildIdRsp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetGuildIdRsp) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

// assessStatus多人合作库申请推荐房考核状态
type ExamUidGuildIdInfo struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExamUidGuildIdInfo) Reset()         { *m = ExamUidGuildIdInfo{} }
func (m *ExamUidGuildIdInfo) String() string { return proto.CompactTextString(m) }
func (*ExamUidGuildIdInfo) ProtoMessage()    {}
func (*ExamUidGuildIdInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{54}
}
func (m *ExamUidGuildIdInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamUidGuildIdInfo.Unmarshal(m, b)
}
func (m *ExamUidGuildIdInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamUidGuildIdInfo.Marshal(b, m, deterministic)
}
func (dst *ExamUidGuildIdInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamUidGuildIdInfo.Merge(dst, src)
}
func (m *ExamUidGuildIdInfo) XXX_Size() int {
	return xxx_messageInfo_ExamUidGuildIdInfo.Size(m)
}
func (m *ExamUidGuildIdInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamUidGuildIdInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ExamUidGuildIdInfo proto.InternalMessageInfo

func (m *ExamUidGuildIdInfo) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *ExamUidGuildIdInfo) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type RecommendExamLimitCheckResp struct {
	IsLimit              uint32   `protobuf:"varint,1,opt,name=is_limit,json=isLimit,proto3" json:"is_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendExamLimitCheckResp) Reset()         { *m = RecommendExamLimitCheckResp{} }
func (m *RecommendExamLimitCheckResp) String() string { return proto.CompactTextString(m) }
func (*RecommendExamLimitCheckResp) ProtoMessage()    {}
func (*RecommendExamLimitCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{55}
}
func (m *RecommendExamLimitCheckResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendExamLimitCheckResp.Unmarshal(m, b)
}
func (m *RecommendExamLimitCheckResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendExamLimitCheckResp.Marshal(b, m, deterministic)
}
func (dst *RecommendExamLimitCheckResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendExamLimitCheckResp.Merge(dst, src)
}
func (m *RecommendExamLimitCheckResp) XXX_Size() int {
	return xxx_messageInfo_RecommendExamLimitCheckResp.Size(m)
}
func (m *RecommendExamLimitCheckResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendExamLimitCheckResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendExamLimitCheckResp proto.InternalMessageInfo

func (m *RecommendExamLimitCheckResp) GetIsLimit() uint32 {
	if m != nil {
		return m.IsLimit
	}
	return 0
}

// openChannel 公会厅
type OpenChannelReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenChannelReq) Reset()         { *m = OpenChannelReq{} }
func (m *OpenChannelReq) String() string { return proto.CompactTextString(m) }
func (*OpenChannelReq) ProtoMessage()    {}
func (*OpenChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{56}
}
func (m *OpenChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenChannelReq.Unmarshal(m, b)
}
func (m *OpenChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenChannelReq.Marshal(b, m, deterministic)
}
func (dst *OpenChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenChannelReq.Merge(dst, src)
}
func (m *OpenChannelReq) XXX_Size() int {
	return xxx_messageInfo_OpenChannelReq.Size(m)
}
func (m *OpenChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_OpenChannelReq proto.InternalMessageInfo

func (m *OpenChannelReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *OpenChannelReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *OpenChannelReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *OpenChannelReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type TagInfo struct {
	Tagbg                string   `protobuf:"bytes,1,opt,name=tagbg,proto3" json:"tagbg,omitempty"`
	TagText              string   `protobuf:"bytes,2,opt,name=tagText,proto3" json:"tagText,omitempty"`
	TagId                uint32   `protobuf:"varint,3,opt,name=tagId,proto3" json:"tagId,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channelId,proto3" json:"channelId,omitempty"`
	ChannelName          string   `protobuf:"bytes,5,opt,name=channelName,proto3" json:"channelName,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagInfo) Reset()         { *m = TagInfo{} }
func (m *TagInfo) String() string { return proto.CompactTextString(m) }
func (*TagInfo) ProtoMessage()    {}
func (*TagInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{57}
}
func (m *TagInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagInfo.Unmarshal(m, b)
}
func (m *TagInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagInfo.Marshal(b, m, deterministic)
}
func (dst *TagInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagInfo.Merge(dst, src)
}
func (m *TagInfo) XXX_Size() int {
	return xxx_messageInfo_TagInfo.Size(m)
}
func (m *TagInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TagInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TagInfo proto.InternalMessageInfo

func (m *TagInfo) GetTagbg() string {
	if m != nil {
		return m.Tagbg
	}
	return ""
}

func (m *TagInfo) GetTagText() string {
	if m != nil {
		return m.TagText
	}
	return ""
}

func (m *TagInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *TagInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TagInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

type OpenChannelResp struct {
	List                 []*TagInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32     `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *OpenChannelResp) Reset()         { *m = OpenChannelResp{} }
func (m *OpenChannelResp) String() string { return proto.CompactTextString(m) }
func (*OpenChannelResp) ProtoMessage()    {}
func (*OpenChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{58}
}
func (m *OpenChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenChannelResp.Unmarshal(m, b)
}
func (m *OpenChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenChannelResp.Marshal(b, m, deterministic)
}
func (dst *OpenChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenChannelResp.Merge(dst, src)
}
func (m *OpenChannelResp) XXX_Size() int {
	return xxx_messageInfo_OpenChannelResp.Size(m)
}
func (m *OpenChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_OpenChannelResp proto.InternalMessageInfo

func (m *OpenChannelResp) GetList() []*TagInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *OpenChannelResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// multiLaymans 申请多人互动所有人列表
type RecommendUsersReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendUsersReq) Reset()         { *m = RecommendUsersReq{} }
func (m *RecommendUsersReq) String() string { return proto.CompactTextString(m) }
func (*RecommendUsersReq) ProtoMessage()    {}
func (*RecommendUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{59}
}
func (m *RecommendUsersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendUsersReq.Unmarshal(m, b)
}
func (m *RecommendUsersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendUsersReq.Marshal(b, m, deterministic)
}
func (dst *RecommendUsersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendUsersReq.Merge(dst, src)
}
func (m *RecommendUsersReq) XXX_Size() int {
	return xxx_messageInfo_RecommendUsersReq.Size(m)
}
func (m *RecommendUsersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendUsersReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendUsersReq proto.InternalMessageInfo

func (m *RecommendUsersReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *RecommendUsersReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *RecommendUsersReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecommendUsersReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *RecommendUsersReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type UserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName,omitempty"`
	Alias                string   `protobuf:"bytes,4,opt,name=alias,proto3" json:"alias,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{60}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(dst, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *UserInfo) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

// searchMultiLayman 搜索多人互动公开厅考核所有人
type SearchMultiLayman struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Keyword              string   `protobuf:"bytes,4,opt,name=keyword,proto3" json:"keyword,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchMultiLayman) Reset()         { *m = SearchMultiLayman{} }
func (m *SearchMultiLayman) String() string { return proto.CompactTextString(m) }
func (*SearchMultiLayman) ProtoMessage()    {}
func (*SearchMultiLayman) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{61}
}
func (m *SearchMultiLayman) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchMultiLayman.Unmarshal(m, b)
}
func (m *SearchMultiLayman) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchMultiLayman.Marshal(b, m, deterministic)
}
func (dst *SearchMultiLayman) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchMultiLayman.Merge(dst, src)
}
func (m *SearchMultiLayman) XXX_Size() int {
	return xxx_messageInfo_SearchMultiLayman.Size(m)
}
func (m *SearchMultiLayman) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchMultiLayman.DiscardUnknown(m)
}

var xxx_messageInfo_SearchMultiLayman proto.InternalMessageInfo

func (m *SearchMultiLayman) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *SearchMultiLayman) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *SearchMultiLayman) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SearchMultiLayman) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

type RecommendUsersResp struct {
	List                 []*UserInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32      `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *RecommendUsersResp) Reset()         { *m = RecommendUsersResp{} }
func (m *RecommendUsersResp) String() string { return proto.CompactTextString(m) }
func (*RecommendUsersResp) ProtoMessage()    {}
func (*RecommendUsersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{62}
}
func (m *RecommendUsersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendUsersResp.Unmarshal(m, b)
}
func (m *RecommendUsersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendUsersResp.Marshal(b, m, deterministic)
}
func (dst *RecommendUsersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendUsersResp.Merge(dst, src)
}
func (m *RecommendUsersResp) XXX_Size() int {
	return xxx_messageInfo_RecommendUsersResp.Size(m)
}
func (m *RecommendUsersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendUsersResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendUsersResp proto.InternalMessageInfo

func (m *RecommendUsersResp) GetList() []*UserInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *RecommendUsersResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// multiAssess 申请多人互动推荐房考核
type RecommendExamBaseInfo struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid,omitempty"`
	Purpose              uint32   `protobuf:"varint,3,opt,name=purpose,proto3" json:"purpose,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channelId,proto3" json:"channelId,omitempty"`
	CurrentTag           uint32   `protobuf:"varint,5,opt,name=currentTag,proto3" json:"currentTag,omitempty"`
	ExamTag              uint32   `protobuf:"varint,6,opt,name=examTag,proto3" json:"examTag,omitempty"`
	ExamUids             []uint32 `protobuf:"varint,7,rep,packed,name=examUids,proto3" json:"examUids,omitempty"`
	Password             string   `protobuf:"bytes,8,opt,name=password,proto3" json:"password,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendExamBaseInfo) Reset()         { *m = RecommendExamBaseInfo{} }
func (m *RecommendExamBaseInfo) String() string { return proto.CompactTextString(m) }
func (*RecommendExamBaseInfo) ProtoMessage()    {}
func (*RecommendExamBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{63}
}
func (m *RecommendExamBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendExamBaseInfo.Unmarshal(m, b)
}
func (m *RecommendExamBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendExamBaseInfo.Marshal(b, m, deterministic)
}
func (dst *RecommendExamBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendExamBaseInfo.Merge(dst, src)
}
func (m *RecommendExamBaseInfo) XXX_Size() int {
	return xxx_messageInfo_RecommendExamBaseInfo.Size(m)
}
func (m *RecommendExamBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendExamBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendExamBaseInfo proto.InternalMessageInfo

func (m *RecommendExamBaseInfo) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *RecommendExamBaseInfo) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetPurpose() uint32 {
	if m != nil {
		return m.Purpose
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetCurrentTag() uint32 {
	if m != nil {
		return m.CurrentTag
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetExamTag() uint32 {
	if m != nil {
		return m.ExamTag
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetExamUids() []uint32 {
	if m != nil {
		return m.ExamUids
	}
	return nil
}

func (m *RecommendExamBaseInfo) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

type ExamTagInfo struct {
	ExamId               uint32   `protobuf:"varint,1,opt,name=examId,proto3" json:"examId,omitempty"`
	Tag                  string   `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
	LimitNumber          uint32   `protobuf:"varint,3,opt,name=limit_number,json=limitNumber,proto3" json:"limit_number,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExamTagInfo) Reset()         { *m = ExamTagInfo{} }
func (m *ExamTagInfo) String() string { return proto.CompactTextString(m) }
func (*ExamTagInfo) ProtoMessage()    {}
func (*ExamTagInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{64}
}
func (m *ExamTagInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamTagInfo.Unmarshal(m, b)
}
func (m *ExamTagInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamTagInfo.Marshal(b, m, deterministic)
}
func (dst *ExamTagInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamTagInfo.Merge(dst, src)
}
func (m *ExamTagInfo) XXX_Size() int {
	return xxx_messageInfo_ExamTagInfo.Size(m)
}
func (m *ExamTagInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamTagInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ExamTagInfo proto.InternalMessageInfo

func (m *ExamTagInfo) GetExamId() uint32 {
	if m != nil {
		return m.ExamId
	}
	return 0
}

func (m *ExamTagInfo) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *ExamTagInfo) GetLimitNumber() uint32 {
	if m != nil {
		return m.LimitNumber
	}
	return 0
}

// mutliRoomExam 多人互动合作库推荐房考核项目
type GetExamTagListResp struct {
	ExamList             []*ExamTagInfo `protobuf:"bytes,1,rep,name=examList,proto3" json:"examList,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetExamTagListResp) Reset()         { *m = GetExamTagListResp{} }
func (m *GetExamTagListResp) String() string { return proto.CompactTextString(m) }
func (*GetExamTagListResp) ProtoMessage()    {}
func (*GetExamTagListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{65}
}
func (m *GetExamTagListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExamTagListResp.Unmarshal(m, b)
}
func (m *GetExamTagListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExamTagListResp.Marshal(b, m, deterministic)
}
func (dst *GetExamTagListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExamTagListResp.Merge(dst, src)
}
func (m *GetExamTagListResp) XXX_Size() int {
	return xxx_messageInfo_GetExamTagListResp.Size(m)
}
func (m *GetExamTagListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExamTagListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExamTagListResp proto.InternalMessageInfo

func (m *GetExamTagListResp) GetExamList() []*ExamTagInfo {
	if m != nil {
		return m.ExamList
	}
	return nil
}

type YuyinExamApplicationInfo struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName,omitempty"`
	Alias                string   `protobuf:"bytes,4,opt,name=alias,proto3" json:"alias,omitempty"`
	ExamTag              uint32   `protobuf:"varint,5,opt,name=examTag,proto3" json:"examTag,omitempty"`
	Link                 string   `protobuf:"bytes,6,opt,name=link,proto3" json:"link,omitempty"`
	BeginTime            uint32   `protobuf:"varint,7,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	EndTime              uint32   `protobuf:"varint,8,opt,name=endTime,proto3" json:"endTime,omitempty"`
	TagName              string   `protobuf:"bytes,9,opt,name=tagName,proto3" json:"tagName,omitempty"`
	AssessStatus         uint32   `protobuf:"varint,10,opt,name=assessStatus,proto3" json:"assessStatus,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinExamApplicationInfo) Reset()         { *m = YuyinExamApplicationInfo{} }
func (m *YuyinExamApplicationInfo) String() string { return proto.CompactTextString(m) }
func (*YuyinExamApplicationInfo) ProtoMessage()    {}
func (*YuyinExamApplicationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{66}
}
func (m *YuyinExamApplicationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamApplicationInfo.Unmarshal(m, b)
}
func (m *YuyinExamApplicationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamApplicationInfo.Marshal(b, m, deterministic)
}
func (dst *YuyinExamApplicationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamApplicationInfo.Merge(dst, src)
}
func (m *YuyinExamApplicationInfo) XXX_Size() int {
	return xxx_messageInfo_YuyinExamApplicationInfo.Size(m)
}
func (m *YuyinExamApplicationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamApplicationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamApplicationInfo proto.InternalMessageInfo

func (m *YuyinExamApplicationInfo) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetExamTag() uint32 {
	if m != nil {
		return m.ExamTag
	}
	return 0
}

func (m *YuyinExamApplicationInfo) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *YuyinExamApplicationInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *YuyinExamApplicationInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetAssessStatus() uint32 {
	if m != nil {
		return m.AssessStatus
	}
	return 0
}

// handleAssess
type YuyinExamGuildOwnerOperationReq struct {
	Uid                  string          `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AnchorUids           []string        `protobuf:"bytes,2,rep,name=anchorUids,proto3" json:"anchorUids,omitempty"`
	Guildid              uint32          `protobuf:"varint,3,opt,name=guildid,proto3" json:"guildid,omitempty"`
	YuyinStatus          YuyinExamStatus `protobuf:"varint,4,opt,name=yuyinStatus,proto3,enum=golddiamond_logic.YuyinExamStatus" json:"yuyinStatus,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *YuyinExamGuildOwnerOperationReq) Reset()         { *m = YuyinExamGuildOwnerOperationReq{} }
func (m *YuyinExamGuildOwnerOperationReq) String() string { return proto.CompactTextString(m) }
func (*YuyinExamGuildOwnerOperationReq) ProtoMessage()    {}
func (*YuyinExamGuildOwnerOperationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{67}
}
func (m *YuyinExamGuildOwnerOperationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationReq.Unmarshal(m, b)
}
func (m *YuyinExamGuildOwnerOperationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationReq.Marshal(b, m, deterministic)
}
func (dst *YuyinExamGuildOwnerOperationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamGuildOwnerOperationReq.Merge(dst, src)
}
func (m *YuyinExamGuildOwnerOperationReq) XXX_Size() int {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationReq.Size(m)
}
func (m *YuyinExamGuildOwnerOperationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamGuildOwnerOperationReq.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamGuildOwnerOperationReq proto.InternalMessageInfo

func (m *YuyinExamGuildOwnerOperationReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *YuyinExamGuildOwnerOperationReq) GetAnchorUids() []string {
	if m != nil {
		return m.AnchorUids
	}
	return nil
}

func (m *YuyinExamGuildOwnerOperationReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *YuyinExamGuildOwnerOperationReq) GetYuyinStatus() YuyinExamStatus {
	if m != nil {
		return m.YuyinStatus
	}
	return YuyinExamStatus_YE_DEFAULT
}

type YuyinExamGuildOwnerOperationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinExamGuildOwnerOperationResp) Reset()         { *m = YuyinExamGuildOwnerOperationResp{} }
func (m *YuyinExamGuildOwnerOperationResp) String() string { return proto.CompactTextString(m) }
func (*YuyinExamGuildOwnerOperationResp) ProtoMessage()    {}
func (*YuyinExamGuildOwnerOperationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{68}
}
func (m *YuyinExamGuildOwnerOperationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationResp.Unmarshal(m, b)
}
func (m *YuyinExamGuildOwnerOperationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationResp.Marshal(b, m, deterministic)
}
func (dst *YuyinExamGuildOwnerOperationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamGuildOwnerOperationResp.Merge(dst, src)
}
func (m *YuyinExamGuildOwnerOperationResp) XXX_Size() int {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationResp.Size(m)
}
func (m *YuyinExamGuildOwnerOperationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamGuildOwnerOperationResp.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamGuildOwnerOperationResp proto.InternalMessageInfo

// 主播提交考核
type YuyinExamReq struct {
	Info                 *YuyinExamApplicationInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *YuyinExamReq) Reset()         { *m = YuyinExamReq{} }
func (m *YuyinExamReq) String() string { return proto.CompactTextString(m) }
func (*YuyinExamReq) ProtoMessage()    {}
func (*YuyinExamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{69}
}
func (m *YuyinExamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamReq.Unmarshal(m, b)
}
func (m *YuyinExamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamReq.Marshal(b, m, deterministic)
}
func (dst *YuyinExamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamReq.Merge(dst, src)
}
func (m *YuyinExamReq) XXX_Size() int {
	return xxx_messageInfo_YuyinExamReq.Size(m)
}
func (m *YuyinExamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamReq.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamReq proto.InternalMessageInfo

func (m *YuyinExamReq) GetInfo() *YuyinExamApplicationInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type YuyinExamResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinExamResp) Reset()         { *m = YuyinExamResp{} }
func (m *YuyinExamResp) String() string { return proto.CompactTextString(m) }
func (*YuyinExamResp) ProtoMessage()    {}
func (*YuyinExamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{70}
}
func (m *YuyinExamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamResp.Unmarshal(m, b)
}
func (m *YuyinExamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamResp.Marshal(b, m, deterministic)
}
func (dst *YuyinExamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamResp.Merge(dst, src)
}
func (m *YuyinExamResp) XXX_Size() int {
	return xxx_messageInfo_YuyinExamResp.Size(m)
}
func (m *YuyinExamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamResp.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamResp proto.InternalMessageInfo

// 主播查看提交的考核信息
type GetYuyinExamReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinExamReq) Reset()         { *m = GetYuyinExamReq{} }
func (m *GetYuyinExamReq) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamReq) ProtoMessage()    {}
func (*GetYuyinExamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{71}
}
func (m *GetYuyinExamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamReq.Unmarshal(m, b)
}
func (m *GetYuyinExamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamReq.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamReq.Merge(dst, src)
}
func (m *GetYuyinExamReq) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamReq.Size(m)
}
func (m *GetYuyinExamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamReq proto.InternalMessageInfo

func (m *GetYuyinExamReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type GetYuyinExamResp struct {
	IsLimit              bool                      `protobuf:"varint,1,opt,name=isLimit,proto3" json:"isLimit,omitempty"`
	Info                 *YuyinExamApplicationInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetYuyinExamResp) Reset()         { *m = GetYuyinExamResp{} }
func (m *GetYuyinExamResp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamResp) ProtoMessage()    {}
func (*GetYuyinExamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{72}
}
func (m *GetYuyinExamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamResp.Unmarshal(m, b)
}
func (m *GetYuyinExamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamResp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamResp.Merge(dst, src)
}
func (m *GetYuyinExamResp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamResp.Size(m)
}
func (m *GetYuyinExamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamResp proto.InternalMessageInfo

func (m *GetYuyinExamResp) GetIsLimit() bool {
	if m != nil {
		return m.IsLimit
	}
	return false
}

func (m *GetYuyinExamResp) GetInfo() *YuyinExamApplicationInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// assessList
type GetYuyinExamForGuildResp struct {
	List                 []*YuyinExamApplicationInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32                      `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetYuyinExamForGuildResp) Reset()         { *m = GetYuyinExamForGuildResp{} }
func (m *GetYuyinExamForGuildResp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamForGuildResp) ProtoMessage()    {}
func (*GetYuyinExamForGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{73}
}
func (m *GetYuyinExamForGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamForGuildResp.Unmarshal(m, b)
}
func (m *GetYuyinExamForGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamForGuildResp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamForGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamForGuildResp.Merge(dst, src)
}
func (m *GetYuyinExamForGuildResp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamForGuildResp.Size(m)
}
func (m *GetYuyinExamForGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamForGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamForGuildResp proto.InternalMessageInfo

func (m *GetYuyinExamForGuildResp) GetList() []*YuyinExamApplicationInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetYuyinExamForGuildResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// appraisalStatus 考核状态
type GetYuyinExamStatusReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinExamStatusReq) Reset()         { *m = GetYuyinExamStatusReq{} }
func (m *GetYuyinExamStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamStatusReq) ProtoMessage()    {}
func (*GetYuyinExamStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{74}
}
func (m *GetYuyinExamStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamStatusReq.Unmarshal(m, b)
}
func (m *GetYuyinExamStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamStatusReq.Merge(dst, src)
}
func (m *GetYuyinExamStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamStatusReq.Size(m)
}
func (m *GetYuyinExamStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamStatusReq proto.InternalMessageInfo

func (m *GetYuyinExamStatusReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type GetYuyinExamStatusResp struct {
	Status               int32    `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinExamStatusResp) Reset()         { *m = GetYuyinExamStatusResp{} }
func (m *GetYuyinExamStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamStatusResp) ProtoMessage()    {}
func (*GetYuyinExamStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{75}
}
func (m *GetYuyinExamStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamStatusResp.Unmarshal(m, b)
}
func (m *GetYuyinExamStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamStatusResp.Merge(dst, src)
}
func (m *GetYuyinExamStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamStatusResp.Size(m)
}
func (m *GetYuyinExamStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamStatusResp proto.InternalMessageInfo

func (m *GetYuyinExamStatusResp) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type TagsInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagsInfo) Reset()         { *m = TagsInfo{} }
func (m *TagsInfo) String() string { return proto.CompactTextString(m) }
func (*TagsInfo) ProtoMessage()    {}
func (*TagsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{76}
}
func (m *TagsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagsInfo.Unmarshal(m, b)
}
func (m *TagsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagsInfo.Marshal(b, m, deterministic)
}
func (dst *TagsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagsInfo.Merge(dst, src)
}
func (m *TagsInfo) XXX_Size() int {
	return xxx_messageInfo_TagsInfo.Size(m)
}
func (m *TagsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TagsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TagsInfo proto.InternalMessageInfo

func (m *TagsInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TagsInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type SubmitInfo struct {
	Tag                  uint32   `protobuf:"varint,1,opt,name=tag,proto3" json:"tag,omitempty"`
	Link                 string   `protobuf:"bytes,2,opt,name=link,proto3" json:"link,omitempty"`
	Contact              string   `protobuf:"bytes,3,opt,name=contact,proto3" json:"contact,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitInfo) Reset()         { *m = SubmitInfo{} }
func (m *SubmitInfo) String() string { return proto.CompactTextString(m) }
func (*SubmitInfo) ProtoMessage()    {}
func (*SubmitInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{77}
}
func (m *SubmitInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitInfo.Unmarshal(m, b)
}
func (m *SubmitInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitInfo.Marshal(b, m, deterministic)
}
func (dst *SubmitInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitInfo.Merge(dst, src)
}
func (m *SubmitInfo) XXX_Size() int {
	return xxx_messageInfo_SubmitInfo.Size(m)
}
func (m *SubmitInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitInfo proto.InternalMessageInfo

func (m *SubmitInfo) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *SubmitInfo) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *SubmitInfo) GetContact() string {
	if m != nil {
		return m.Contact
	}
	return ""
}

// appraisalInit 信息提交初始化
type YuyinExamInfo struct {
	Tags                 []*TagsInfo `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
	Submit               *SubmitInfo `protobuf:"bytes,2,opt,name=submit,proto3" json:"submit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *YuyinExamInfo) Reset()         { *m = YuyinExamInfo{} }
func (m *YuyinExamInfo) String() string { return proto.CompactTextString(m) }
func (*YuyinExamInfo) ProtoMessage()    {}
func (*YuyinExamInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{78}
}
func (m *YuyinExamInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamInfo.Unmarshal(m, b)
}
func (m *YuyinExamInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamInfo.Marshal(b, m, deterministic)
}
func (dst *YuyinExamInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamInfo.Merge(dst, src)
}
func (m *YuyinExamInfo) XXX_Size() int {
	return xxx_messageInfo_YuyinExamInfo.Size(m)
}
func (m *YuyinExamInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamInfo.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamInfo proto.InternalMessageInfo

func (m *YuyinExamInfo) GetTags() []*TagsInfo {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *YuyinExamInfo) GetSubmit() *SubmitInfo {
	if m != nil {
		return m.Submit
	}
	return nil
}

// submitAppraisal 提交考核内容
type YuyinSubmit struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Tag                  uint32   `protobuf:"varint,2,opt,name=tag,proto3" json:"tag,omitempty"`
	Link                 string   `protobuf:"bytes,3,opt,name=link,proto3" json:"link,omitempty"`
	Contact              string   `protobuf:"bytes,4,opt,name=contact,proto3" json:"contact,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinSubmit) Reset()         { *m = YuyinSubmit{} }
func (m *YuyinSubmit) String() string { return proto.CompactTextString(m) }
func (*YuyinSubmit) ProtoMessage()    {}
func (*YuyinSubmit) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{79}
}
func (m *YuyinSubmit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinSubmit.Unmarshal(m, b)
}
func (m *YuyinSubmit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinSubmit.Marshal(b, m, deterministic)
}
func (dst *YuyinSubmit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinSubmit.Merge(dst, src)
}
func (m *YuyinSubmit) XXX_Size() int {
	return xxx_messageInfo_YuyinSubmit.Size(m)
}
func (m *YuyinSubmit) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinSubmit.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinSubmit proto.InternalMessageInfo

func (m *YuyinSubmit) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *YuyinSubmit) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *YuyinSubmit) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *YuyinSubmit) GetContact() string {
	if m != nil {
		return m.Contact
	}
	return ""
}

// 会长查看申请记录
type ApplicationRecordForGuildOwnerReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid,omitempty"`
	Keyword              string   `protobuf:"bytes,3,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplicationRecordForGuildOwnerReq) Reset()         { *m = ApplicationRecordForGuildOwnerReq{} }
func (m *ApplicationRecordForGuildOwnerReq) String() string { return proto.CompactTextString(m) }
func (*ApplicationRecordForGuildOwnerReq) ProtoMessage()    {}
func (*ApplicationRecordForGuildOwnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{80}
}
func (m *ApplicationRecordForGuildOwnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerReq.Unmarshal(m, b)
}
func (m *ApplicationRecordForGuildOwnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerReq.Marshal(b, m, deterministic)
}
func (dst *ApplicationRecordForGuildOwnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationRecordForGuildOwnerReq.Merge(dst, src)
}
func (m *ApplicationRecordForGuildOwnerReq) XXX_Size() int {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerReq.Size(m)
}
func (m *ApplicationRecordForGuildOwnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationRecordForGuildOwnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationRecordForGuildOwnerReq proto.InternalMessageInfo

func (m *ApplicationRecordForGuildOwnerReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *ApplicationRecordForGuildOwnerReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *ApplicationRecordForGuildOwnerReq) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *ApplicationRecordForGuildOwnerReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ApplicationRecordForGuildOwnerReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type ApplicationRecordInfo struct {
	SubmitTime           uint32                      `protobuf:"varint,1,opt,name=submitTime,proto3" json:"submitTime,omitempty"`
	Items                []*YuyinExamApplicationInfo `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *ApplicationRecordInfo) Reset()         { *m = ApplicationRecordInfo{} }
func (m *ApplicationRecordInfo) String() string { return proto.CompactTextString(m) }
func (*ApplicationRecordInfo) ProtoMessage()    {}
func (*ApplicationRecordInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{81}
}
func (m *ApplicationRecordInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationRecordInfo.Unmarshal(m, b)
}
func (m *ApplicationRecordInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationRecordInfo.Marshal(b, m, deterministic)
}
func (dst *ApplicationRecordInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationRecordInfo.Merge(dst, src)
}
func (m *ApplicationRecordInfo) XXX_Size() int {
	return xxx_messageInfo_ApplicationRecordInfo.Size(m)
}
func (m *ApplicationRecordInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationRecordInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationRecordInfo proto.InternalMessageInfo

func (m *ApplicationRecordInfo) GetSubmitTime() uint32 {
	if m != nil {
		return m.SubmitTime
	}
	return 0
}

func (m *ApplicationRecordInfo) GetItems() []*YuyinExamApplicationInfo {
	if m != nil {
		return m.Items
	}
	return nil
}

type ApplicationRecordForGuildOwnerResp struct {
	List                 []*ApplicationRecordInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	NextPage             uint32                   `protobuf:"varint,2,opt,name=nextPage,proto3" json:"nextPage,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ApplicationRecordForGuildOwnerResp) Reset()         { *m = ApplicationRecordForGuildOwnerResp{} }
func (m *ApplicationRecordForGuildOwnerResp) String() string { return proto.CompactTextString(m) }
func (*ApplicationRecordForGuildOwnerResp) ProtoMessage()    {}
func (*ApplicationRecordForGuildOwnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{82}
}
func (m *ApplicationRecordForGuildOwnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerResp.Unmarshal(m, b)
}
func (m *ApplicationRecordForGuildOwnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerResp.Marshal(b, m, deterministic)
}
func (dst *ApplicationRecordForGuildOwnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationRecordForGuildOwnerResp.Merge(dst, src)
}
func (m *ApplicationRecordForGuildOwnerResp) XXX_Size() int {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerResp.Size(m)
}
func (m *ApplicationRecordForGuildOwnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationRecordForGuildOwnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationRecordForGuildOwnerResp proto.InternalMessageInfo

func (m *ApplicationRecordForGuildOwnerResp) GetList() []*ApplicationRecordInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *ApplicationRecordForGuildOwnerResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

type CheckUidMainReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUidMainReq) Reset()         { *m = CheckUidMainReq{} }
func (m *CheckUidMainReq) String() string { return proto.CompactTextString(m) }
func (*CheckUidMainReq) ProtoMessage()    {}
func (*CheckUidMainReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{83}
}
func (m *CheckUidMainReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUidMainReq.Unmarshal(m, b)
}
func (m *CheckUidMainReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUidMainReq.Marshal(b, m, deterministic)
}
func (dst *CheckUidMainReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUidMainReq.Merge(dst, src)
}
func (m *CheckUidMainReq) XXX_Size() int {
	return xxx_messageInfo_CheckUidMainReq.Size(m)
}
func (m *CheckUidMainReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUidMainReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUidMainReq proto.InternalMessageInfo

func (m *CheckUidMainReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type CheckUidMainResp struct {
	IsMain               uint32   `protobuf:"varint,1,opt,name=is_main,json=isMain,proto3" json:"is_main,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUidMainResp) Reset()         { *m = CheckUidMainResp{} }
func (m *CheckUidMainResp) String() string { return proto.CompactTextString(m) }
func (*CheckUidMainResp) ProtoMessage()    {}
func (*CheckUidMainResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{84}
}
func (m *CheckUidMainResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUidMainResp.Unmarshal(m, b)
}
func (m *CheckUidMainResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUidMainResp.Marshal(b, m, deterministic)
}
func (dst *CheckUidMainResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUidMainResp.Merge(dst, src)
}
func (m *CheckUidMainResp) XXX_Size() int {
	return xxx_messageInfo_CheckUidMainResp.Size(m)
}
func (m *CheckUidMainResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUidMainResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUidMainResp proto.InternalMessageInfo

func (m *CheckUidMainResp) GetIsMain() uint32 {
	if m != nil {
		return m.IsMain
	}
	return 0
}

type AmuseExtraIncomeDetailReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	YearMonth            uint32   `protobuf:"varint,3,opt,name=year_month,json=yearMonth,proto3" json:"year_month,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AmuseExtraIncomeDetailReq) Reset()         { *m = AmuseExtraIncomeDetailReq{} }
func (m *AmuseExtraIncomeDetailReq) String() string { return proto.CompactTextString(m) }
func (*AmuseExtraIncomeDetailReq) ProtoMessage()    {}
func (*AmuseExtraIncomeDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{85}
}
func (m *AmuseExtraIncomeDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseExtraIncomeDetailReq.Unmarshal(m, b)
}
func (m *AmuseExtraIncomeDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseExtraIncomeDetailReq.Marshal(b, m, deterministic)
}
func (dst *AmuseExtraIncomeDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseExtraIncomeDetailReq.Merge(dst, src)
}
func (m *AmuseExtraIncomeDetailReq) XXX_Size() int {
	return xxx_messageInfo_AmuseExtraIncomeDetailReq.Size(m)
}
func (m *AmuseExtraIncomeDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseExtraIncomeDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseExtraIncomeDetailReq proto.InternalMessageInfo

func (m *AmuseExtraIncomeDetailReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *AmuseExtraIncomeDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AmuseExtraIncomeDetailReq) GetYearMonth() uint32 {
	if m != nil {
		return m.YearMonth
	}
	return 0
}

type AmuseExtraIncomeDetailResp struct {
	YearMonth            uint32                                    `protobuf:"varint,1,opt,name=year_month,json=yearMonth,proto3" json:"year_month,omitempty"`
	ThisMonthFee         uint64                                    `protobuf:"varint,2,opt,name=this_month_fee,json=thisMonthFee,proto3" json:"this_month_fee,omitempty"`
	LastMonthFee         uint64                                    `protobuf:"varint,3,opt,name=last_month_fee,json=lastMonthFee,proto3" json:"last_month_fee,omitempty"`
	ThisMonthIncome      uint64                                    `protobuf:"varint,4,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income,omitempty"`
	ThisMonthIncomeCny   string                                    `protobuf:"bytes,5,opt,name=this_month_income_cny,json=thisMonthIncomeCny,proto3" json:"this_month_income_cny,omitempty"`
	PrepaidMoney         uint64                                    `protobuf:"varint,6,opt,name=prepaid_money,json=prepaidMoney,proto3" json:"prepaid_money,omitempty"`
	PrepaidMoneyCny      string                                    `protobuf:"bytes,7,opt,name=prepaid_money_cny,json=prepaidMoneyCny,proto3" json:"prepaid_money_cny,omitempty"`
	Remark               string                                    `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark,omitempty"`
	GrowRate             string                                    `protobuf:"bytes,9,opt,name=grow_rate,json=growRate,proto3" json:"grow_rate,omitempty"`
	ChannelList          []*AmuseExtraIncomeDetailResp_ChannelItem `protobuf:"bytes,10,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *AmuseExtraIncomeDetailResp) Reset()         { *m = AmuseExtraIncomeDetailResp{} }
func (m *AmuseExtraIncomeDetailResp) String() string { return proto.CompactTextString(m) }
func (*AmuseExtraIncomeDetailResp) ProtoMessage()    {}
func (*AmuseExtraIncomeDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{86}
}
func (m *AmuseExtraIncomeDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseExtraIncomeDetailResp.Unmarshal(m, b)
}
func (m *AmuseExtraIncomeDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseExtraIncomeDetailResp.Marshal(b, m, deterministic)
}
func (dst *AmuseExtraIncomeDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseExtraIncomeDetailResp.Merge(dst, src)
}
func (m *AmuseExtraIncomeDetailResp) XXX_Size() int {
	return xxx_messageInfo_AmuseExtraIncomeDetailResp.Size(m)
}
func (m *AmuseExtraIncomeDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseExtraIncomeDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseExtraIncomeDetailResp proto.InternalMessageInfo

func (m *AmuseExtraIncomeDetailResp) GetYearMonth() uint32 {
	if m != nil {
		return m.YearMonth
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp) GetThisMonthFee() uint64 {
	if m != nil {
		return m.ThisMonthFee
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp) GetLastMonthFee() uint64 {
	if m != nil {
		return m.LastMonthFee
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp) GetThisMonthIncome() uint64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp) GetThisMonthIncomeCny() string {
	if m != nil {
		return m.ThisMonthIncomeCny
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp) GetPrepaidMoney() uint64 {
	if m != nil {
		return m.PrepaidMoney
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp) GetPrepaidMoneyCny() string {
	if m != nil {
		return m.PrepaidMoneyCny
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp) GetGrowRate() string {
	if m != nil {
		return m.GrowRate
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp) GetChannelList() []*AmuseExtraIncomeDetailResp_ChannelItem {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type AmuseExtraIncomeDetailResp_ChannelItem struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelDisplayId     uint32   `protobuf:"varint,2,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	ChannelName          string   `protobuf:"bytes,3,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelTag           string   `protobuf:"bytes,4,opt,name=channel_tag,json=channelTag,proto3" json:"channel_tag,omitempty"`
	ThisMonthFee         uint64   `protobuf:"varint,5,opt,name=this_month_fee,json=thisMonthFee,proto3" json:"this_month_fee,omitempty"`
	SettlementRate       string   `protobuf:"bytes,6,opt,name=settlement_rate,json=settlementRate,proto3" json:"settlement_rate,omitempty"`
	ThisMonthIncome      uint64   `protobuf:"varint,7,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income,omitempty"`
	GuildId              uint32   `protobuf:"varint,8,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildDisplayId       uint32   `protobuf:"varint,9,opt,name=guild_display_id,json=guildDisplayId,proto3" json:"guild_display_id,omitempty"`
	LastMonthFee         uint64   `protobuf:"varint,10,opt,name=last_month_fee,json=lastMonthFee,proto3" json:"last_month_fee,omitempty"`
	GrowRate             string   `protobuf:"bytes,11,opt,name=grow_rate,json=growRate,proto3" json:"grow_rate,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,12,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) Reset() {
	*m = AmuseExtraIncomeDetailResp_ChannelItem{}
}
func (m *AmuseExtraIncomeDetailResp_ChannelItem) String() string { return proto.CompactTextString(m) }
func (*AmuseExtraIncomeDetailResp_ChannelItem) ProtoMessage()    {}
func (*AmuseExtraIncomeDetailResp_ChannelItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{86, 0}
}
func (m *AmuseExtraIncomeDetailResp_ChannelItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseExtraIncomeDetailResp_ChannelItem.Unmarshal(m, b)
}
func (m *AmuseExtraIncomeDetailResp_ChannelItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseExtraIncomeDetailResp_ChannelItem.Marshal(b, m, deterministic)
}
func (dst *AmuseExtraIncomeDetailResp_ChannelItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseExtraIncomeDetailResp_ChannelItem.Merge(dst, src)
}
func (m *AmuseExtraIncomeDetailResp_ChannelItem) XXX_Size() int {
	return xxx_messageInfo_AmuseExtraIncomeDetailResp_ChannelItem.Size(m)
}
func (m *AmuseExtraIncomeDetailResp_ChannelItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseExtraIncomeDetailResp_ChannelItem.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseExtraIncomeDetailResp_ChannelItem proto.InternalMessageInfo

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetChannelTag() string {
	if m != nil {
		return m.ChannelTag
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetThisMonthFee() uint64 {
	if m != nil {
		return m.ThisMonthFee
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetSettlementRate() string {
	if m != nil {
		return m.SettlementRate
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetThisMonthIncome() uint64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetGuildDisplayId() uint32 {
	if m != nil {
		return m.GuildDisplayId
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetLastMonthFee() uint64 {
	if m != nil {
		return m.LastMonthFee
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetGrowRate() string {
	if m != nil {
		return m.GrowRate
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type InteractGameIncomeInfo struct {
	AnchorName           string   `protobuf:"bytes,1,opt,name=anchor_name,json=anchorName,proto3" json:"anchor_name,omitempty"`
	Members              int64    `protobuf:"varint,3,opt,name=members,proto3" json:"members,omitempty"`
	Fee                  int64    `protobuf:"varint,4,opt,name=fee,proto3" json:"fee,omitempty"`
	Income               int64    `protobuf:"varint,7,opt,name=income,proto3" json:"income,omitempty"`
	GameDuration         int64    `protobuf:"varint,8,opt,name=game_duration,json=gameDuration,proto3" json:"game_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InteractGameIncomeInfo) Reset()         { *m = InteractGameIncomeInfo{} }
func (m *InteractGameIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*InteractGameIncomeInfo) ProtoMessage()    {}
func (*InteractGameIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{87}
}
func (m *InteractGameIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractGameIncomeInfo.Unmarshal(m, b)
}
func (m *InteractGameIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractGameIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *InteractGameIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractGameIncomeInfo.Merge(dst, src)
}
func (m *InteractGameIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_InteractGameIncomeInfo.Size(m)
}
func (m *InteractGameIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractGameIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_InteractGameIncomeInfo proto.InternalMessageInfo

func (m *InteractGameIncomeInfo) GetAnchorName() string {
	if m != nil {
		return m.AnchorName
	}
	return ""
}

func (m *InteractGameIncomeInfo) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *InteractGameIncomeInfo) GetFee() int64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *InteractGameIncomeInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *InteractGameIncomeInfo) GetGameDuration() int64 {
	if m != nil {
		return m.GameDuration
	}
	return 0
}

type InteractGameIncomeRsp struct {
	List                 []*InteractGameIncomeInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *InteractGameIncomeRsp) Reset()         { *m = InteractGameIncomeRsp{} }
func (m *InteractGameIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*InteractGameIncomeRsp) ProtoMessage()    {}
func (*InteractGameIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{88}
}
func (m *InteractGameIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractGameIncomeRsp.Unmarshal(m, b)
}
func (m *InteractGameIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractGameIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *InteractGameIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractGameIncomeRsp.Merge(dst, src)
}
func (m *InteractGameIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_InteractGameIncomeRsp.Size(m)
}
func (m *InteractGameIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractGameIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_InteractGameIncomeRsp proto.InternalMessageInfo

func (m *InteractGameIncomeRsp) GetList() []*InteractGameIncomeInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type InteractGameExtraIncomeReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	MonthTime            uint32   `protobuf:"varint,2,opt,name=month_time,json=monthTime,proto3" json:"month_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InteractGameExtraIncomeReq) Reset()         { *m = InteractGameExtraIncomeReq{} }
func (m *InteractGameExtraIncomeReq) String() string { return proto.CompactTextString(m) }
func (*InteractGameExtraIncomeReq) ProtoMessage()    {}
func (*InteractGameExtraIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{89}
}
func (m *InteractGameExtraIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractGameExtraIncomeReq.Unmarshal(m, b)
}
func (m *InteractGameExtraIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractGameExtraIncomeReq.Marshal(b, m, deterministic)
}
func (dst *InteractGameExtraIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractGameExtraIncomeReq.Merge(dst, src)
}
func (m *InteractGameExtraIncomeReq) XXX_Size() int {
	return xxx_messageInfo_InteractGameExtraIncomeReq.Size(m)
}
func (m *InteractGameExtraIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractGameExtraIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_InteractGameExtraIncomeReq proto.InternalMessageInfo

func (m *InteractGameExtraIncomeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *InteractGameExtraIncomeReq) GetMonthTime() uint32 {
	if m != nil {
		return m.MonthTime
	}
	return 0
}

type InteractGameExtraIncomeResp struct {
	GameMonthTotalFee    uint64                           `protobuf:"varint,1,opt,name=game_month_total_fee,json=gameMonthTotalFee,proto3" json:"game_month_total_fee,omitempty"`
	GameMonthExtraIncome uint64                           `protobuf:"varint,2,opt,name=game_month_extra_income,json=gameMonthExtraIncome,proto3" json:"game_month_extra_income,omitempty"`
	List                 []*InteractGameExtraIncomeDetail `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *InteractGameExtraIncomeResp) Reset()         { *m = InteractGameExtraIncomeResp{} }
func (m *InteractGameExtraIncomeResp) String() string { return proto.CompactTextString(m) }
func (*InteractGameExtraIncomeResp) ProtoMessage()    {}
func (*InteractGameExtraIncomeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{90}
}
func (m *InteractGameExtraIncomeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractGameExtraIncomeResp.Unmarshal(m, b)
}
func (m *InteractGameExtraIncomeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractGameExtraIncomeResp.Marshal(b, m, deterministic)
}
func (dst *InteractGameExtraIncomeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractGameExtraIncomeResp.Merge(dst, src)
}
func (m *InteractGameExtraIncomeResp) XXX_Size() int {
	return xxx_messageInfo_InteractGameExtraIncomeResp.Size(m)
}
func (m *InteractGameExtraIncomeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractGameExtraIncomeResp.DiscardUnknown(m)
}

var xxx_messageInfo_InteractGameExtraIncomeResp proto.InternalMessageInfo

func (m *InteractGameExtraIncomeResp) GetGameMonthTotalFee() uint64 {
	if m != nil {
		return m.GameMonthTotalFee
	}
	return 0
}

func (m *InteractGameExtraIncomeResp) GetGameMonthExtraIncome() uint64 {
	if m != nil {
		return m.GameMonthExtraIncome
	}
	return 0
}

func (m *InteractGameExtraIncomeResp) GetList() []*InteractGameExtraIncomeDetail {
	if m != nil {
		return m.List
	}
	return nil
}

type InteractGameExtraIncomeDetail struct {
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	AnchorTtid           string   `protobuf:"bytes,3,opt,name=anchor_ttid,json=anchorTtid,proto3" json:"anchor_ttid,omitempty"`
	AnchorAccount        string   `protobuf:"bytes,4,opt,name=anchor_account,json=anchorAccount,proto3" json:"anchor_account,omitempty"`
	AnchorNickname       string   `protobuf:"bytes,5,opt,name=anchor_nickname,json=anchorNickname,proto3" json:"anchor_nickname,omitempty"`
	AnchorSex            uint32   `protobuf:"varint,6,opt,name=anchor_sex,json=anchorSex,proto3" json:"anchor_sex,omitempty"`
	GameMonthFee         uint64   `protobuf:"varint,7,opt,name=game_month_fee,json=gameMonthFee,proto3" json:"game_month_fee,omitempty"`
	GameIncomeRate       float32  `protobuf:"fixed32,8,opt,name=game_income_rate,json=gameIncomeRate,proto3" json:"game_income_rate,omitempty"`
	GameIncome           uint64   `protobuf:"varint,9,opt,name=game_income,json=gameIncome,proto3" json:"game_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InteractGameExtraIncomeDetail) Reset()         { *m = InteractGameExtraIncomeDetail{} }
func (m *InteractGameExtraIncomeDetail) String() string { return proto.CompactTextString(m) }
func (*InteractGameExtraIncomeDetail) ProtoMessage()    {}
func (*InteractGameExtraIncomeDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{91}
}
func (m *InteractGameExtraIncomeDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractGameExtraIncomeDetail.Unmarshal(m, b)
}
func (m *InteractGameExtraIncomeDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractGameExtraIncomeDetail.Marshal(b, m, deterministic)
}
func (dst *InteractGameExtraIncomeDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractGameExtraIncomeDetail.Merge(dst, src)
}
func (m *InteractGameExtraIncomeDetail) XXX_Size() int {
	return xxx_messageInfo_InteractGameExtraIncomeDetail.Size(m)
}
func (m *InteractGameExtraIncomeDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractGameExtraIncomeDetail.DiscardUnknown(m)
}

var xxx_messageInfo_InteractGameExtraIncomeDetail proto.InternalMessageInfo

func (m *InteractGameExtraIncomeDetail) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *InteractGameExtraIncomeDetail) GetAnchorTtid() string {
	if m != nil {
		return m.AnchorTtid
	}
	return ""
}

func (m *InteractGameExtraIncomeDetail) GetAnchorAccount() string {
	if m != nil {
		return m.AnchorAccount
	}
	return ""
}

func (m *InteractGameExtraIncomeDetail) GetAnchorNickname() string {
	if m != nil {
		return m.AnchorNickname
	}
	return ""
}

func (m *InteractGameExtraIncomeDetail) GetAnchorSex() uint32 {
	if m != nil {
		return m.AnchorSex
	}
	return 0
}

func (m *InteractGameExtraIncomeDetail) GetGameMonthFee() uint64 {
	if m != nil {
		return m.GameMonthFee
	}
	return 0
}

func (m *InteractGameExtraIncomeDetail) GetGameIncomeRate() float32 {
	if m != nil {
		return m.GameIncomeRate
	}
	return 0
}

func (m *InteractGameExtraIncomeDetail) GetGameIncome() uint64 {
	if m != nil {
		return m.GameIncome
	}
	return 0
}

type EsportIncomeInfo struct {
	AnchorName           string   `protobuf:"bytes,1,opt,name=anchor_name,json=anchorName,proto3" json:"anchor_name,omitempty"`
	Fee                  int64    `protobuf:"varint,4,opt,name=fee,proto3" json:"fee,omitempty"`
	Income               int64    `protobuf:"varint,7,opt,name=income,proto3" json:"income,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportIncomeInfo) Reset()         { *m = EsportIncomeInfo{} }
func (m *EsportIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*EsportIncomeInfo) ProtoMessage()    {}
func (*EsportIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{92}
}
func (m *EsportIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportIncomeInfo.Unmarshal(m, b)
}
func (m *EsportIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *EsportIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportIncomeInfo.Merge(dst, src)
}
func (m *EsportIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_EsportIncomeInfo.Size(m)
}
func (m *EsportIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EsportIncomeInfo proto.InternalMessageInfo

func (m *EsportIncomeInfo) GetAnchorName() string {
	if m != nil {
		return m.AnchorName
	}
	return ""
}

func (m *EsportIncomeInfo) GetFee() int64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *EsportIncomeInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

type EsportIncomeRsp struct {
	List                 []*EsportIncomeInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *EsportIncomeRsp) Reset()         { *m = EsportIncomeRsp{} }
func (m *EsportIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*EsportIncomeRsp) ProtoMessage()    {}
func (*EsportIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{93}
}
func (m *EsportIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportIncomeRsp.Unmarshal(m, b)
}
func (m *EsportIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *EsportIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportIncomeRsp.Merge(dst, src)
}
func (m *EsportIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_EsportIncomeRsp.Size(m)
}
func (m *EsportIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_EsportIncomeRsp proto.InternalMessageInfo

func (m *EsportIncomeRsp) GetList() []*EsportIncomeInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type EsportPendingRsp struct {
	ThisMonthFee         int64    `protobuf:"varint,1,opt,name=this_month_fee,json=thisMonthFee,proto3" json:"this_month_fee,omitempty"`
	ThisMonthIncome      int64    `protobuf:"varint,2,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income,omitempty"`
	StartTime            int64    `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportPendingRsp) Reset()         { *m = EsportPendingRsp{} }
func (m *EsportPendingRsp) String() string { return proto.CompactTextString(m) }
func (*EsportPendingRsp) ProtoMessage()    {}
func (*EsportPendingRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{94}
}
func (m *EsportPendingRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportPendingRsp.Unmarshal(m, b)
}
func (m *EsportPendingRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportPendingRsp.Marshal(b, m, deterministic)
}
func (dst *EsportPendingRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportPendingRsp.Merge(dst, src)
}
func (m *EsportPendingRsp) XXX_Size() int {
	return xxx_messageInfo_EsportPendingRsp.Size(m)
}
func (m *EsportPendingRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportPendingRsp.DiscardUnknown(m)
}

var xxx_messageInfo_EsportPendingRsp proto.InternalMessageInfo

func (m *EsportPendingRsp) GetThisMonthFee() int64 {
	if m != nil {
		return m.ThisMonthFee
	}
	return 0
}

func (m *EsportPendingRsp) GetThisMonthIncome() int64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *EsportPendingRsp) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *EsportPendingRsp) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type EsportCoachesMonthIncomeReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportCoachesMonthIncomeReq) Reset()         { *m = EsportCoachesMonthIncomeReq{} }
func (m *EsportCoachesMonthIncomeReq) String() string { return proto.CompactTextString(m) }
func (*EsportCoachesMonthIncomeReq) ProtoMessage()    {}
func (*EsportCoachesMonthIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{95}
}
func (m *EsportCoachesMonthIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportCoachesMonthIncomeReq.Unmarshal(m, b)
}
func (m *EsportCoachesMonthIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportCoachesMonthIncomeReq.Marshal(b, m, deterministic)
}
func (dst *EsportCoachesMonthIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportCoachesMonthIncomeReq.Merge(dst, src)
}
func (m *EsportCoachesMonthIncomeReq) XXX_Size() int {
	return xxx_messageInfo_EsportCoachesMonthIncomeReq.Size(m)
}
func (m *EsportCoachesMonthIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportCoachesMonthIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_EsportCoachesMonthIncomeReq proto.InternalMessageInfo

func (m *EsportCoachesMonthIncomeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *EsportCoachesMonthIncomeReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *EsportCoachesMonthIncomeReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type EsportCoachesMonthIncomeInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Fee                  uint64   `protobuf:"varint,4,opt,name=fee,proto3" json:"fee,omitempty"`
	Income               uint64   `protobuf:"varint,5,opt,name=income,proto3" json:"income,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportCoachesMonthIncomeInfo) Reset()         { *m = EsportCoachesMonthIncomeInfo{} }
func (m *EsportCoachesMonthIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*EsportCoachesMonthIncomeInfo) ProtoMessage()    {}
func (*EsportCoachesMonthIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{96}
}
func (m *EsportCoachesMonthIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportCoachesMonthIncomeInfo.Unmarshal(m, b)
}
func (m *EsportCoachesMonthIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportCoachesMonthIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *EsportCoachesMonthIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportCoachesMonthIncomeInfo.Merge(dst, src)
}
func (m *EsportCoachesMonthIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_EsportCoachesMonthIncomeInfo.Size(m)
}
func (m *EsportCoachesMonthIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportCoachesMonthIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EsportCoachesMonthIncomeInfo proto.InternalMessageInfo

func (m *EsportCoachesMonthIncomeInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *EsportCoachesMonthIncomeInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *EsportCoachesMonthIncomeInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *EsportCoachesMonthIncomeInfo) GetFee() uint64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *EsportCoachesMonthIncomeInfo) GetIncome() uint64 {
	if m != nil {
		return m.Income
	}
	return 0
}

type EsportCoachesMonthIncomeRsp struct {
	List                 []*EsportCoachesMonthIncomeInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32                          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *EsportCoachesMonthIncomeRsp) Reset()         { *m = EsportCoachesMonthIncomeRsp{} }
func (m *EsportCoachesMonthIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*EsportCoachesMonthIncomeRsp) ProtoMessage()    {}
func (*EsportCoachesMonthIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{97}
}
func (m *EsportCoachesMonthIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportCoachesMonthIncomeRsp.Unmarshal(m, b)
}
func (m *EsportCoachesMonthIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportCoachesMonthIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *EsportCoachesMonthIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportCoachesMonthIncomeRsp.Merge(dst, src)
}
func (m *EsportCoachesMonthIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_EsportCoachesMonthIncomeRsp.Size(m)
}
func (m *EsportCoachesMonthIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportCoachesMonthIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_EsportCoachesMonthIncomeRsp proto.InternalMessageInfo

func (m *EsportCoachesMonthIncomeRsp) GetList() []*EsportCoachesMonthIncomeInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *EsportCoachesMonthIncomeRsp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type EsportConsumeSearchReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	CoachUid             uint32   `protobuf:"varint,3,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	StartTime            int64    `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Page                 uint32   `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	PageNum              uint32   `protobuf:"varint,7,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportConsumeSearchReq) Reset()         { *m = EsportConsumeSearchReq{} }
func (m *EsportConsumeSearchReq) String() string { return proto.CompactTextString(m) }
func (*EsportConsumeSearchReq) ProtoMessage()    {}
func (*EsportConsumeSearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{98}
}
func (m *EsportConsumeSearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportConsumeSearchReq.Unmarshal(m, b)
}
func (m *EsportConsumeSearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportConsumeSearchReq.Marshal(b, m, deterministic)
}
func (dst *EsportConsumeSearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportConsumeSearchReq.Merge(dst, src)
}
func (m *EsportConsumeSearchReq) XXX_Size() int {
	return xxx_messageInfo_EsportConsumeSearchReq.Size(m)
}
func (m *EsportConsumeSearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportConsumeSearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_EsportConsumeSearchReq proto.InternalMessageInfo

func (m *EsportConsumeSearchReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *EsportConsumeSearchReq) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *EsportConsumeSearchReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *EsportConsumeSearchReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *EsportConsumeSearchReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *EsportConsumeSearchReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type EsportConsumeDetail struct {
	Date                 int64    `protobuf:"varint,1,opt,name=date,proto3" json:"date,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	CoachTtid            string   `protobuf:"bytes,3,opt,name=coach_ttid,json=coachTtid,proto3" json:"coach_ttid,omitempty"`
	CoachNickname        string   `protobuf:"bytes,4,opt,name=coach_nickname,json=coachNickname,proto3" json:"coach_nickname,omitempty"`
	Fee                  uint64   `protobuf:"varint,5,opt,name=fee,proto3" json:"fee,omitempty"`
	Income               uint64   `protobuf:"varint,6,opt,name=income,proto3" json:"income,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportConsumeDetail) Reset()         { *m = EsportConsumeDetail{} }
func (m *EsportConsumeDetail) String() string { return proto.CompactTextString(m) }
func (*EsportConsumeDetail) ProtoMessage()    {}
func (*EsportConsumeDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{99}
}
func (m *EsportConsumeDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportConsumeDetail.Unmarshal(m, b)
}
func (m *EsportConsumeDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportConsumeDetail.Marshal(b, m, deterministic)
}
func (dst *EsportConsumeDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportConsumeDetail.Merge(dst, src)
}
func (m *EsportConsumeDetail) XXX_Size() int {
	return xxx_messageInfo_EsportConsumeDetail.Size(m)
}
func (m *EsportConsumeDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportConsumeDetail.DiscardUnknown(m)
}

var xxx_messageInfo_EsportConsumeDetail proto.InternalMessageInfo

func (m *EsportConsumeDetail) GetDate() int64 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *EsportConsumeDetail) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *EsportConsumeDetail) GetCoachTtid() string {
	if m != nil {
		return m.CoachTtid
	}
	return ""
}

func (m *EsportConsumeDetail) GetCoachNickname() string {
	if m != nil {
		return m.CoachNickname
	}
	return ""
}

func (m *EsportConsumeDetail) GetFee() uint64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *EsportConsumeDetail) GetIncome() uint64 {
	if m != nil {
		return m.Income
	}
	return 0
}

type EsportConsumeSearchRsp struct {
	NextPage             bool                   `protobuf:"varint,1,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	IncomeList           []*EsportConsumeDetail `protobuf:"bytes,2,rep,name=income_list,json=incomeList,proto3" json:"income_list,omitempty"`
	TotalFee             uint64                 `protobuf:"varint,3,opt,name=total_fee,json=totalFee,proto3" json:"total_fee,omitempty"`
	TotalIncome          uint64                 `protobuf:"varint,4,opt,name=total_income,json=totalIncome,proto3" json:"total_income,omitempty"`
	StartTime            int64                  `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64                  `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *EsportConsumeSearchRsp) Reset()         { *m = EsportConsumeSearchRsp{} }
func (m *EsportConsumeSearchRsp) String() string { return proto.CompactTextString(m) }
func (*EsportConsumeSearchRsp) ProtoMessage()    {}
func (*EsportConsumeSearchRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{100}
}
func (m *EsportConsumeSearchRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportConsumeSearchRsp.Unmarshal(m, b)
}
func (m *EsportConsumeSearchRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportConsumeSearchRsp.Marshal(b, m, deterministic)
}
func (dst *EsportConsumeSearchRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportConsumeSearchRsp.Merge(dst, src)
}
func (m *EsportConsumeSearchRsp) XXX_Size() int {
	return xxx_messageInfo_EsportConsumeSearchRsp.Size(m)
}
func (m *EsportConsumeSearchRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportConsumeSearchRsp.DiscardUnknown(m)
}

var xxx_messageInfo_EsportConsumeSearchRsp proto.InternalMessageInfo

func (m *EsportConsumeSearchRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

func (m *EsportConsumeSearchRsp) GetIncomeList() []*EsportConsumeDetail {
	if m != nil {
		return m.IncomeList
	}
	return nil
}

func (m *EsportConsumeSearchRsp) GetTotalFee() uint64 {
	if m != nil {
		return m.TotalFee
	}
	return 0
}

func (m *EsportConsumeSearchRsp) GetTotalIncome() uint64 {
	if m != nil {
		return m.TotalIncome
	}
	return 0
}

func (m *EsportConsumeSearchRsp) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *EsportConsumeSearchRsp) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// 获取新增技能审核信息请求
type BatchGetAuditSkillRequest struct {
	GuildId   uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Ttid      string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	AuditType []uint32 `protobuf:"varint,3,rep,packed,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	OffSet    uint32   `protobuf:"varint,4,opt,name=off_set,json=offSet,proto3" json:"off_set,omitempty"`
	Limit     uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	// 查单个详情
	QueryUid             uint32   `protobuf:"varint,6,opt,name=query_uid,json=queryUid,proto3" json:"query_uid,omitempty"`
	QueryAuditSource     uint32   `protobuf:"varint,7,opt,name=query_audit_source,json=queryAuditSource,proto3" json:"query_audit_source,omitempty"`
	QueryAuditToken      string   `protobuf:"bytes,8,opt,name=query_audit_token,json=queryAuditToken,proto3" json:"query_audit_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAuditSkillRequest) Reset()         { *m = BatchGetAuditSkillRequest{} }
func (m *BatchGetAuditSkillRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetAuditSkillRequest) ProtoMessage()    {}
func (*BatchGetAuditSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{101}
}
func (m *BatchGetAuditSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAuditSkillRequest.Unmarshal(m, b)
}
func (m *BatchGetAuditSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAuditSkillRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetAuditSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAuditSkillRequest.Merge(dst, src)
}
func (m *BatchGetAuditSkillRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetAuditSkillRequest.Size(m)
}
func (m *BatchGetAuditSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAuditSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAuditSkillRequest proto.InternalMessageInfo

func (m *BatchGetAuditSkillRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *BatchGetAuditSkillRequest) GetAuditType() []uint32 {
	if m != nil {
		return m.AuditType
	}
	return nil
}

func (m *BatchGetAuditSkillRequest) GetOffSet() uint32 {
	if m != nil {
		return m.OffSet
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetQueryUid() uint32 {
	if m != nil {
		return m.QueryUid
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetQueryAuditSource() uint32 {
	if m != nil {
		return m.QueryAuditSource
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetQueryAuditToken() string {
	if m != nil {
		return m.QueryAuditToken
	}
	return ""
}

// 获取新增技能审核信息请求
type BatchGetAuditSkillResponse struct {
	GuildId              uint32              `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint32              `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	AuditType            []uint32            `protobuf:"varint,3,rep,packed,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	OffSet               uint32              `protobuf:"varint,4,opt,name=off_set,json=offSet,proto3" json:"off_set,omitempty"`
	Limit                uint32              `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	List                 []*AuditSkillRecord `protobuf:"bytes,6,rep,name=list,proto3" json:"list,omitempty"`
	TotalCount           uint32              `protobuf:"varint,7,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetAuditSkillResponse) Reset()         { *m = BatchGetAuditSkillResponse{} }
func (m *BatchGetAuditSkillResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetAuditSkillResponse) ProtoMessage()    {}
func (*BatchGetAuditSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{102}
}
func (m *BatchGetAuditSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAuditSkillResponse.Unmarshal(m, b)
}
func (m *BatchGetAuditSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAuditSkillResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetAuditSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAuditSkillResponse.Merge(dst, src)
}
func (m *BatchGetAuditSkillResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetAuditSkillResponse.Size(m)
}
func (m *BatchGetAuditSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAuditSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAuditSkillResponse proto.InternalMessageInfo

func (m *BatchGetAuditSkillResponse) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetAuditType() []uint32 {
	if m != nil {
		return m.AuditType
	}
	return nil
}

func (m *BatchGetAuditSkillResponse) GetOffSet() uint32 {
	if m != nil {
		return m.OffSet
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetList() []*AuditSkillRecord {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *BatchGetAuditSkillResponse) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type AuditSkillRecord struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string           `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string           `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Ttid                 string           `protobuf:"bytes,4,opt,name=ttid,proto3" json:"ttid,omitempty"`
	AuditToken           string           `protobuf:"bytes,5,opt,name=audit_token,json=auditToken,proto3" json:"audit_token,omitempty"`
	AuditType            uint32           `protobuf:"varint,6,opt,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	ApplyTime            uint32           `protobuf:"varint,7,opt,name=apply_time,json=applyTime,proto3" json:"apply_time,omitempty"`
	Skill                []*UserSkillInfo `protobuf:"bytes,8,rep,name=skill,proto3" json:"skill,omitempty"`
	AuditSource          uint32           `protobuf:"varint,9,opt,name=audit_source,json=auditSource,proto3" json:"audit_source,omitempty"`
	Reason               string           `protobuf:"bytes,10,opt,name=reason,proto3" json:"reason,omitempty"`
	GuildId              uint32           `protobuf:"varint,11,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Sex                  uint32           `protobuf:"varint,12,opt,name=sex,proto3" json:"sex,omitempty"`
	SignTime             uint32           `protobuf:"varint,13,opt,name=sign_time,json=signTime,proto3" json:"sign_time,omitempty"`
	SignExpireTime       uint32           `protobuf:"varint,14,opt,name=sign_expire_time,json=signExpireTime,proto3" json:"sign_expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AuditSkillRecord) Reset()         { *m = AuditSkillRecord{} }
func (m *AuditSkillRecord) String() string { return proto.CompactTextString(m) }
func (*AuditSkillRecord) ProtoMessage()    {}
func (*AuditSkillRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{103}
}
func (m *AuditSkillRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuditSkillRecord.Unmarshal(m, b)
}
func (m *AuditSkillRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuditSkillRecord.Marshal(b, m, deterministic)
}
func (dst *AuditSkillRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuditSkillRecord.Merge(dst, src)
}
func (m *AuditSkillRecord) XXX_Size() int {
	return xxx_messageInfo_AuditSkillRecord.Size(m)
}
func (m *AuditSkillRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_AuditSkillRecord.DiscardUnknown(m)
}

var xxx_messageInfo_AuditSkillRecord proto.InternalMessageInfo

func (m *AuditSkillRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AuditSkillRecord) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AuditSkillRecord) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *AuditSkillRecord) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AuditSkillRecord) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *AuditSkillRecord) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *AuditSkillRecord) GetApplyTime() uint32 {
	if m != nil {
		return m.ApplyTime
	}
	return 0
}

func (m *AuditSkillRecord) GetSkill() []*UserSkillInfo {
	if m != nil {
		return m.Skill
	}
	return nil
}

func (m *AuditSkillRecord) GetAuditSource() uint32 {
	if m != nil {
		return m.AuditSource
	}
	return 0
}

func (m *AuditSkillRecord) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *AuditSkillRecord) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AuditSkillRecord) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *AuditSkillRecord) GetSignTime() uint32 {
	if m != nil {
		return m.SignTime
	}
	return 0
}

func (m *AuditSkillRecord) GetSignExpireTime() uint32 {
	if m != nil {
		return m.SignExpireTime
	}
	return 0
}

// 用户游戏资料信息
type UserSkillInfo struct {
	GameId               uint32         `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string         `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	SkillEvidence        string         `protobuf:"bytes,3,opt,name=skill_evidence,json=skillEvidence,proto3" json:"skill_evidence,omitempty"`
	Audio                string         `protobuf:"bytes,4,opt,name=audio,proto3" json:"audio,omitempty"`
	AudioDuration        uint32         `protobuf:"varint,5,opt,name=audio_duration,json=audioDuration,proto3" json:"audio_duration,omitempty"`
	SectionList          []*SectionInfo `protobuf:"bytes,6,rep,name=section_list,json=sectionList,proto3" json:"section_list,omitempty"`
	TextDesc             string         `protobuf:"bytes,7,opt,name=text_desc,json=textDesc,proto3" json:"text_desc,omitempty"`
	GameIcon             string         `protobuf:"bytes,8,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserSkillInfo) Reset()         { *m = UserSkillInfo{} }
func (m *UserSkillInfo) String() string { return proto.CompactTextString(m) }
func (*UserSkillInfo) ProtoMessage()    {}
func (*UserSkillInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{104}
}
func (m *UserSkillInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSkillInfo.Unmarshal(m, b)
}
func (m *UserSkillInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSkillInfo.Marshal(b, m, deterministic)
}
func (dst *UserSkillInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSkillInfo.Merge(dst, src)
}
func (m *UserSkillInfo) XXX_Size() int {
	return xxx_messageInfo_UserSkillInfo.Size(m)
}
func (m *UserSkillInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSkillInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserSkillInfo proto.InternalMessageInfo

func (m *UserSkillInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserSkillInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *UserSkillInfo) GetSkillEvidence() string {
	if m != nil {
		return m.SkillEvidence
	}
	return ""
}

func (m *UserSkillInfo) GetAudio() string {
	if m != nil {
		return m.Audio
	}
	return ""
}

func (m *UserSkillInfo) GetAudioDuration() uint32 {
	if m != nil {
		return m.AudioDuration
	}
	return 0
}

func (m *UserSkillInfo) GetSectionList() []*SectionInfo {
	if m != nil {
		return m.SectionList
	}
	return nil
}

func (m *UserSkillInfo) GetTextDesc() string {
	if m != nil {
		return m.TextDesc
	}
	return ""
}

func (m *UserSkillInfo) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

type SectionInfo struct {
	SectionName          string   `protobuf:"bytes,1,opt,name=section_name,json=sectionName,proto3" json:"section_name,omitempty"`
	ItemList             []string `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SectionInfo) Reset()         { *m = SectionInfo{} }
func (m *SectionInfo) String() string { return proto.CompactTextString(m) }
func (*SectionInfo) ProtoMessage()    {}
func (*SectionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{105}
}
func (m *SectionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SectionInfo.Unmarshal(m, b)
}
func (m *SectionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SectionInfo.Marshal(b, m, deterministic)
}
func (dst *SectionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SectionInfo.Merge(dst, src)
}
func (m *SectionInfo) XXX_Size() int {
	return xxx_messageInfo_SectionInfo.Size(m)
}
func (m *SectionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SectionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SectionInfo proto.InternalMessageInfo

func (m *SectionInfo) GetSectionName() string {
	if m != nil {
		return m.SectionName
	}
	return ""
}

func (m *SectionInfo) GetItemList() []string {
	if m != nil {
		return m.ItemList
	}
	return nil
}

// 设置用户技能审核结果
type SetUserSkillAuditTypeRequest struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	AuditToken           string   `protobuf:"bytes,3,opt,name=audit_token,json=auditToken,proto3" json:"audit_token,omitempty"`
	AuditType            uint32   `protobuf:"varint,4,opt,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	Reason               string   `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSkillAuditTypeRequest) Reset()         { *m = SetUserSkillAuditTypeRequest{} }
func (m *SetUserSkillAuditTypeRequest) String() string { return proto.CompactTextString(m) }
func (*SetUserSkillAuditTypeRequest) ProtoMessage()    {}
func (*SetUserSkillAuditTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{106}
}
func (m *SetUserSkillAuditTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSkillAuditTypeRequest.Unmarshal(m, b)
}
func (m *SetUserSkillAuditTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSkillAuditTypeRequest.Marshal(b, m, deterministic)
}
func (dst *SetUserSkillAuditTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSkillAuditTypeRequest.Merge(dst, src)
}
func (m *SetUserSkillAuditTypeRequest) XXX_Size() int {
	return xxx_messageInfo_SetUserSkillAuditTypeRequest.Size(m)
}
func (m *SetUserSkillAuditTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSkillAuditTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSkillAuditTypeRequest proto.InternalMessageInfo

func (m *SetUserSkillAuditTypeRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SetUserSkillAuditTypeRequest) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *SetUserSkillAuditTypeRequest) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *SetUserSkillAuditTypeRequest) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *SetUserSkillAuditTypeRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type SetUserSkillAuditTypeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSkillAuditTypeResponse) Reset()         { *m = SetUserSkillAuditTypeResponse{} }
func (m *SetUserSkillAuditTypeResponse) String() string { return proto.CompactTextString(m) }
func (*SetUserSkillAuditTypeResponse) ProtoMessage()    {}
func (*SetUserSkillAuditTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_api_219263c32850ecae, []int{107}
}
func (m *SetUserSkillAuditTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSkillAuditTypeResponse.Unmarshal(m, b)
}
func (m *SetUserSkillAuditTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSkillAuditTypeResponse.Marshal(b, m, deterministic)
}
func (dst *SetUserSkillAuditTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSkillAuditTypeResponse.Merge(dst, src)
}
func (m *SetUserSkillAuditTypeResponse) XXX_Size() int {
	return xxx_messageInfo_SetUserSkillAuditTypeResponse.Size(m)
}
func (m *SetUserSkillAuditTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSkillAuditTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSkillAuditTypeResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*OperateInfoT)(nil), "golddiamond_logic.OperateInfoT")
	proto.RegisterType((*ApplicationCooperateReq)(nil), "golddiamond_logic.ApplicationCooperateReq")
	proto.RegisterType((*ApplicationCooperateRsp)(nil), "golddiamond_logic.ApplicationCooperateRsp")
	proto.RegisterType((*CooperateReq)(nil), "golddiamond_logic.CooperateReq")
	proto.RegisterType((*CooperateRsp)(nil), "golddiamond_logic.CooperateRsp")
	proto.RegisterType((*ChannelListReq)(nil), "golddiamond_logic.ChannelListReq")
	proto.RegisterType((*Channelinfot)(nil), "golddiamond_logic.channelinfot")
	proto.RegisterType((*ChannelListRsp)(nil), "golddiamond_logic.ChannelListRsp")
	proto.RegisterType((*PendingReq)(nil), "golddiamond_logic.PendingReq")
	proto.RegisterType((*PendingRsp)(nil), "golddiamond_logic.PendingRsp")
	proto.RegisterType((*PendingDetailReq)(nil), "golddiamond_logic.PendingDetailReq")
	proto.RegisterType((*PendingDetail)(nil), "golddiamond_logic.PendingDetail")
	proto.RegisterType((*PendingDetailRsp)(nil), "golddiamond_logic.PendingDetailRsp")
	proto.RegisterType((*YuyinPendingDetail)(nil), "golddiamond_logic.YuyinPendingDetail")
	proto.RegisterType((*YuyinPendingDetailRsp)(nil), "golddiamond_logic.YuyinPendingDetailRsp")
	proto.RegisterType((*TodayIncomeReq)(nil), "golddiamond_logic.TodayIncomeReq")
	proto.RegisterType((*TodayIncomeInfo)(nil), "golddiamond_logic.TodayIncomeInfo")
	proto.RegisterType((*TodayIncomeRsp)(nil), "golddiamond_logic.TodayIncomeRsp")
	proto.RegisterType((*YuyinTodayIncomeReq)(nil), "golddiamond_logic.YuyinTodayIncomeReq")
	proto.RegisterType((*YuyinTodayIncomeInfo)(nil), "golddiamond_logic.YuyinTodayIncomeInfo")
	proto.RegisterType((*YuyinTodayIncomeRsp)(nil), "golddiamond_logic.YuyinTodayIncomeRsp")
	proto.RegisterType((*MonthIncomeReq)(nil), "golddiamond_logic.MonthIncomeReq")
	proto.RegisterType((*MonthIncomeInfo)(nil), "golddiamond_logic.MonthIncomeInfo")
	proto.RegisterType((*MonthIncomeRsp)(nil), "golddiamond_logic.MonthIncomeRsp")
	proto.RegisterType((*ConsumeSearchReq)(nil), "golddiamond_logic.ConsumeSearchReq")
	proto.RegisterType((*ConsumeDetail)(nil), "golddiamond_logic.ConsumeDetail")
	proto.RegisterType((*ConsumeSearchRsp)(nil), "golddiamond_logic.ConsumeSearchRsp")
	proto.RegisterType((*YuyinConsumeSearchReq)(nil), "golddiamond_logic.YuyinConsumeSearchReq")
	proto.RegisterType((*YuyinConsumeDetail)(nil), "golddiamond_logic.YuyinConsumeDetail")
	proto.RegisterType((*YuyinConsumeSearchRsp)(nil), "golddiamond_logic.YuyinConsumeSearchRsp")
	proto.RegisterType((*ChannelIncomeReq)(nil), "golddiamond_logic.ChannelIncomeReq")
	proto.RegisterType((*ChannelIncomeInfo)(nil), "golddiamond_logic.ChannelIncomeInfo")
	proto.RegisterType((*ChannelIncomeRsp)(nil), "golddiamond_logic.ChannelIncomeRsp")
	proto.RegisterType((*DayTrendInfo)(nil), "golddiamond_logic.DayTrendInfo")
	proto.RegisterType((*GuildChannelInfo)(nil), "golddiamond_logic.GuildChannelInfo")
	proto.RegisterType((*GuildInitInfoReq)(nil), "golddiamond_logic.GuildInitInfoReq")
	proto.RegisterType((*GuildInitInfoRsp)(nil), "golddiamond_logic.GuildInitInfoRsp")
	proto.RegisterType((*GuildCheckReq)(nil), "golddiamond_logic.GuildCheckReq")
	proto.RegisterType((*GuildCheckRsp)(nil), "golddiamond_logic.GuildCheckRsp")
	proto.RegisterType((*GuildConsumeDayQoqReq)(nil), "golddiamond_logic.GuildConsumeDayQoqReq")
	proto.RegisterType((*GuildConsumeDayQoqRsp)(nil), "golddiamond_logic.GuildConsumeDayQoqRsp")
	proto.RegisterType((*GuildDayComparisonReq)(nil), "golddiamond_logic.GuildDayComparisonReq")
	proto.RegisterType((*DayComparisonInfo)(nil), "golddiamond_logic.DayComparisonInfo")
	proto.RegisterType((*GuildDayComparisonResp)(nil), "golddiamond_logic.GuildDayComparisonResp")
	proto.RegisterType((*ConsumeRankItem)(nil), "golddiamond_logic.ConsumeRankItem")
	proto.RegisterType((*ConsumeRankReq)(nil), "golddiamond_logic.ConsumeRankReq")
	proto.RegisterType((*ConsumeRankRsp)(nil), "golddiamond_logic.ConsumeRankRsp")
	proto.RegisterType((*RoomConsumeQoqReq)(nil), "golddiamond_logic.RoomConsumeQoqReq")
	proto.RegisterType((*RoomConsumeQoqRsp)(nil), "golddiamond_logic.RoomConsumeQoqRsp")
	proto.RegisterType((*GetGuildAnchorListReq)(nil), "golddiamond_logic.GetGuildAnchorListReq")
	proto.RegisterType((*GuildAnchorSimpleInfo)(nil), "golddiamond_logic.GuildAnchorSimpleInfo")
	proto.RegisterType((*GetGuildAnchorListRsp)(nil), "golddiamond_logic.GetGuildAnchorListRsp")
	proto.RegisterType((*GetGuildIdReq)(nil), "golddiamond_logic.GetGuildIdReq")
	proto.RegisterType((*GetGuildIdRsp)(nil), "golddiamond_logic.GetGuildIdRsp")
	proto.RegisterType((*ExamUidGuildIdInfo)(nil), "golddiamond_logic.ExamUidGuildIdInfo")
	proto.RegisterType((*RecommendExamLimitCheckResp)(nil), "golddiamond_logic.RecommendExamLimitCheckResp")
	proto.RegisterType((*OpenChannelReq)(nil), "golddiamond_logic.OpenChannelReq")
	proto.RegisterType((*TagInfo)(nil), "golddiamond_logic.TagInfo")
	proto.RegisterType((*OpenChannelResp)(nil), "golddiamond_logic.OpenChannelResp")
	proto.RegisterType((*RecommendUsersReq)(nil), "golddiamond_logic.RecommendUsersReq")
	proto.RegisterType((*UserInfo)(nil), "golddiamond_logic.UserInfo")
	proto.RegisterType((*SearchMultiLayman)(nil), "golddiamond_logic.SearchMultiLayman")
	proto.RegisterType((*RecommendUsersResp)(nil), "golddiamond_logic.RecommendUsersResp")
	proto.RegisterType((*RecommendExamBaseInfo)(nil), "golddiamond_logic.RecommendExamBaseInfo")
	proto.RegisterType((*ExamTagInfo)(nil), "golddiamond_logic.ExamTagInfo")
	proto.RegisterType((*GetExamTagListResp)(nil), "golddiamond_logic.GetExamTagListResp")
	proto.RegisterType((*YuyinExamApplicationInfo)(nil), "golddiamond_logic.YuyinExamApplicationInfo")
	proto.RegisterType((*YuyinExamGuildOwnerOperationReq)(nil), "golddiamond_logic.YuyinExamGuildOwnerOperationReq")
	proto.RegisterType((*YuyinExamGuildOwnerOperationResp)(nil), "golddiamond_logic.YuyinExamGuildOwnerOperationResp")
	proto.RegisterType((*YuyinExamReq)(nil), "golddiamond_logic.YuyinExamReq")
	proto.RegisterType((*YuyinExamResp)(nil), "golddiamond_logic.YuyinExamResp")
	proto.RegisterType((*GetYuyinExamReq)(nil), "golddiamond_logic.GetYuyinExamReq")
	proto.RegisterType((*GetYuyinExamResp)(nil), "golddiamond_logic.GetYuyinExamResp")
	proto.RegisterType((*GetYuyinExamForGuildResp)(nil), "golddiamond_logic.GetYuyinExamForGuildResp")
	proto.RegisterType((*GetYuyinExamStatusReq)(nil), "golddiamond_logic.GetYuyinExamStatusReq")
	proto.RegisterType((*GetYuyinExamStatusResp)(nil), "golddiamond_logic.GetYuyinExamStatusResp")
	proto.RegisterType((*TagsInfo)(nil), "golddiamond_logic.TagsInfo")
	proto.RegisterType((*SubmitInfo)(nil), "golddiamond_logic.SubmitInfo")
	proto.RegisterType((*YuyinExamInfo)(nil), "golddiamond_logic.YuyinExamInfo")
	proto.RegisterType((*YuyinSubmit)(nil), "golddiamond_logic.YuyinSubmit")
	proto.RegisterType((*ApplicationRecordForGuildOwnerReq)(nil), "golddiamond_logic.ApplicationRecordForGuildOwnerReq")
	proto.RegisterType((*ApplicationRecordInfo)(nil), "golddiamond_logic.ApplicationRecordInfo")
	proto.RegisterType((*ApplicationRecordForGuildOwnerResp)(nil), "golddiamond_logic.ApplicationRecordForGuildOwnerResp")
	proto.RegisterType((*CheckUidMainReq)(nil), "golddiamond_logic.CheckUidMainReq")
	proto.RegisterType((*CheckUidMainResp)(nil), "golddiamond_logic.CheckUidMainResp")
	proto.RegisterType((*AmuseExtraIncomeDetailReq)(nil), "golddiamond_logic.AmuseExtraIncomeDetailReq")
	proto.RegisterType((*AmuseExtraIncomeDetailResp)(nil), "golddiamond_logic.AmuseExtraIncomeDetailResp")
	proto.RegisterType((*AmuseExtraIncomeDetailResp_ChannelItem)(nil), "golddiamond_logic.AmuseExtraIncomeDetailResp.ChannelItem")
	proto.RegisterType((*InteractGameIncomeInfo)(nil), "golddiamond_logic.InteractGameIncomeInfo")
	proto.RegisterType((*InteractGameIncomeRsp)(nil), "golddiamond_logic.InteractGameIncomeRsp")
	proto.RegisterType((*InteractGameExtraIncomeReq)(nil), "golddiamond_logic.InteractGameExtraIncomeReq")
	proto.RegisterType((*InteractGameExtraIncomeResp)(nil), "golddiamond_logic.InteractGameExtraIncomeResp")
	proto.RegisterType((*InteractGameExtraIncomeDetail)(nil), "golddiamond_logic.InteractGameExtraIncomeDetail")
	proto.RegisterType((*EsportIncomeInfo)(nil), "golddiamond_logic.EsportIncomeInfo")
	proto.RegisterType((*EsportIncomeRsp)(nil), "golddiamond_logic.EsportIncomeRsp")
	proto.RegisterType((*EsportPendingRsp)(nil), "golddiamond_logic.EsportPendingRsp")
	proto.RegisterType((*EsportCoachesMonthIncomeReq)(nil), "golddiamond_logic.EsportCoachesMonthIncomeReq")
	proto.RegisterType((*EsportCoachesMonthIncomeInfo)(nil), "golddiamond_logic.EsportCoachesMonthIncomeInfo")
	proto.RegisterType((*EsportCoachesMonthIncomeRsp)(nil), "golddiamond_logic.EsportCoachesMonthIncomeRsp")
	proto.RegisterType((*EsportConsumeSearchReq)(nil), "golddiamond_logic.EsportConsumeSearchReq")
	proto.RegisterType((*EsportConsumeDetail)(nil), "golddiamond_logic.EsportConsumeDetail")
	proto.RegisterType((*EsportConsumeSearchRsp)(nil), "golddiamond_logic.EsportConsumeSearchRsp")
	proto.RegisterType((*BatchGetAuditSkillRequest)(nil), "golddiamond_logic.BatchGetAuditSkillRequest")
	proto.RegisterType((*BatchGetAuditSkillResponse)(nil), "golddiamond_logic.BatchGetAuditSkillResponse")
	proto.RegisterType((*AuditSkillRecord)(nil), "golddiamond_logic.AuditSkillRecord")
	proto.RegisterType((*UserSkillInfo)(nil), "golddiamond_logic.UserSkillInfo")
	proto.RegisterType((*SectionInfo)(nil), "golddiamond_logic.SectionInfo")
	proto.RegisterType((*SetUserSkillAuditTypeRequest)(nil), "golddiamond_logic.SetUserSkillAuditTypeRequest")
	proto.RegisterType((*SetUserSkillAuditTypeResponse)(nil), "golddiamond_logic.SetUserSkillAuditTypeResponse")
	proto.RegisterEnum("golddiamond_logic.RangeType", RangeType_name, RangeType_value)
	proto.RegisterEnum("golddiamond_logic.ErrorCode", ErrorCode_name, ErrorCode_value)
	proto.RegisterEnum("golddiamond_logic.ExamTagType", ExamTagType_name, ExamTagType_value)
	proto.RegisterEnum("golddiamond_logic.YuyinExamStatus", YuyinExamStatus_name, YuyinExamStatus_value)
}

func init() {
	proto.RegisterFile("golddiamond-logic/golddiamond_api.proto", fileDescriptor_golddiamond_api_219263c32850ecae)
}

var fileDescriptor_golddiamond_api_219263c32850ecae = []byte{
	// 4928 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5c, 0x4d, 0x8c, 0x1c, 0x49,
	0x56, 0x26, 0xab, 0xaa, 0xbb, 0xab, 0x5e, 0xfd, 0x76, 0xfa, 0xa7, 0xcb, 0xf6, 0x78, 0xec, 0x49,
	0xcf, 0x4f, 0x8f, 0x77, 0xf0, 0x2c, 0xbb, 0xcc, 0xb2, 0xcb, 0x8f, 0x76, 0xdb, 0xdd, 0x65, 0x4f,
	0x2d, 0x76, 0xdb, 0x9b, 0x5d, 0xf6, 0x60, 0x60, 0x55, 0x93, 0xae, 0x8c, 0xae, 0x4e, 0x75, 0xe5,
	0x8f, 0x33, 0xb2, 0xec, 0xae, 0x11, 0xda, 0x03, 0x07, 0x90, 0x90, 0xd0, 0x9e, 0x58, 0x09, 0x09,
	0x90, 0xd8, 0x03, 0x67, 0x24, 0xb4, 0xfc, 0x4b, 0xdc, 0x90, 0x00, 0x21, 0xd0, 0x72, 0x42, 0x5c,
	0xb9, 0x72, 0x80, 0x13, 0xe2, 0x02, 0x8a, 0x17, 0x11, 0x99, 0x11, 0x59, 0x99, 0xd5, 0xdd, 0xb3,
	0xc3, 0x68, 0x4f, 0xae, 0x7c, 0xf1, 0xe2, 0xe7, 0xbd, 0xf7, 0xbd, 0x17, 0x2f, 0x5e, 0x44, 0x1b,
	0xde, 0x99, 0x86, 0x33, 0xd7, 0xf5, 0x1c, 0x3f, 0x0c, 0xdc, 0x9f, 0x9c, 0x85, 0x53, 0x6f, 0xf2,
	0xbe, 0x42, 0x19, 0x3b, 0x91, 0x77, 0x27, 0x8a, 0xc3, 0x24, 0x34, 0x37, 0x55, 0x32, 0x32, 0x5a,
	0xff, 0x65, 0x40, 0xeb, 0x51, 0x44, 0x62, 0x27, 0x21, 0xc3, 0xe0, 0x30, 0x1c, 0x99, 0x6f, 0x40,
	0xcb, 0x0f, 0x83, 0xe4, 0x68, 0xec, 0x05, 0x93, 0xd0, 0x27, 0x7d, 0xe3, 0xa6, 0xb1, 0xdd, 0xb0,
	0x9b, 0x48, 0x1b, 0x22, 0xc9, 0xbc, 0x01, 0x4d, 0xde, 0x38, 0xf6, 0xfc, 0x29, 0xed, 0x57, 0x6e,
	0x56, 0xb7, 0x1b, 0x36, 0x70, 0xd2, 0xd0, 0x9f, 0x52, 0xb3, 0x0f, 0x1b, 0x93, 0x30, 0xa0, 0x73,
	0x9f, 0xf4, 0xab, 0xd8, 0x5d, 0x7e, 0xb2, 0xd1, 0xc5, 0x4f, 0xde, 0xb7, 0x86, 0x7d, 0x9b, 0x82,
	0x86, 0x9d, 0xaf, 0x42, 0x3d, 0x21, 0x8e, 0x4f, 0xbd, 0x4f, 0x48, 0x7f, 0xed, 0xa6, 0xb1, 0xdd,
	0xb6, 0xd3, 0x6f, 0xf3, 0x75, 0x00, 0x67, 0x32, 0x09, 0xfd, 0xc8, 0x09, 0x16, 0xb4, 0xbf, 0xce,
	0x27, 0xce, 0x28, 0xe6, 0x5b, 0xd0, 0x21, 0x34, 0xf1, 0x7c, 0x27, 0x21, 0x63, 0xec, 0xd4, 0xdf,
	0xc0, 0x11, 0xda, 0x92, 0x3a, 0x62, 0x44, 0xeb, 0xef, 0xd7, 0x60, 0x6b, 0x27, 0x8a, 0x66, 0xde,
	0xc4, 0x49, 0xbc, 0x30, 0xd8, 0x0d, 0x43, 0xae, 0x01, 0x9b, 0xbc, 0x60, 0x6b, 0x9f, 0xce, 0xbd,
	0x99, 0xeb, 0xb9, 0x28, 0x7a, 0xdb, 0x96, 0x9f, 0xe6, 0xbb, 0xd0, 0x73, 0xb2, 0x4e, 0xe3, 0x64,
	0x11, 0x91, 0x7e, 0x05, 0x59, 0xba, 0x0a, 0x7d, 0xb4, 0x88, 0x88, 0x79, 0x0b, 0xda, 0xd1, 0xcc,
	0x49, 0x0e, 0xc3, 0xd8, 0x1f, 0x07, 0x4e, 0xaa, 0x86, 0x96, 0x24, 0xee, 0x3b, 0x3e, 0x61, 0xe3,
	0xa5, 0x4c, 0x93, 0x30, 0x48, 0x48, 0x90, 0xf4, 0x6b, 0xc8, 0xd7, 0x95, 0xf4, 0x5d, 0x4e, 0x66,
	0x1a, 0x4f, 0x59, 0x3d, 0x57, 0xa8, 0x05, 0x24, 0x69, 0xe8, 0x9a, 0x1f, 0xc0, 0xe5, 0x6c, 0x2c,
	0x12, 0x27, 0xde, 0xa1, 0x58, 0x8e, 0x50, 0xd2, 0xa5, 0x74, 0x44, 0xb5, 0xd1, 0x7c, 0x1b, 0xba,
	0xca, 0xb8, 0x63, 0x9a, 0xc4, 0xa8, 0xb0, 0x86, 0xdd, 0xce, 0xc6, 0x3e, 0x48, 0x62, 0xf3, 0x3a,
	0x00, 0x13, 0x71, 0xc1, 0x85, 0xae, 0xe3, 0xf4, 0x0d, 0xa4, 0xa0, 0xb8, 0x7d, 0xd8, 0x48, 0x12,
	0x54, 0x53, 0xbf, 0xc1, 0x75, 0x26, 0x3e, 0xcd, 0x3b, 0x70, 0x21, 0x9d, 0x00, 0x29, 0x7c, 0x04,
	0x40, 0xae, 0x4d, 0xd9, 0x74, 0x9f, 0xb5, 0xe0, 0x48, 0xaa, 0x4e, 0xa4, 0x19, 0x9a, 0xba, 0x4e,
	0xee, 0x0b, 0x73, 0xfc, 0xb4, 0x22, 0x32, 0x1f, 0x3a, 0x7c, 0x15, 0x90, 0x78, 0xe8, 0xf6, 0x5b,
	0xd8, 0xe1, 0xa2, 0xd6, 0xe1, 0x11, 0x6f, 0x33, 0xbf, 0x02, 0x5b, 0xb9, 0x5e, 0xce, 0x3c, 0x39,
	0x42, 0x2c, 0xb6, 0x75, 0x4d, 0x61, 0xb7, 0x1d, 0xd1, 0x28, 0x90, 0xe5, 0x3c, 0x9f, 0x79, 0xf4,
	0x68, 0x9c, 0x78, 0x3e, 0xe9, 0x77, 0xb8, 0xa2, 0x52, 0xea, 0xc8, 0xf3, 0x89, 0xf9, 0x35, 0xd8,
	0x10, 0x58, 0xea, 0x77, 0x6f, 0x1a, 0xdb, 0xcd, 0x2f, 0xdd, 0xb8, 0xb3, 0xe4, 0x73, 0x77, 0x54,
	0x7f, 0xb3, 0x25, 0xbf, 0xf9, 0x1a, 0x34, 0x24, 0x4a, 0x69, 0xbf, 0xc7, 0x55, 0x9c, 0x12, 0x84,
	0x4b, 0x25, 0xce, 0x24, 0xe9, 0x6f, 0xa6, 0x2e, 0xc5, 0x3e, 0xad, 0x17, 0x25, 0x58, 0xa6, 0x91,
	0x79, 0x09, 0xd6, 0x3d, 0x3a, 0x76, 0xa2, 0x08, 0xa1, 0x5c, 0xb7, 0xd7, 0x3c, 0xba, 0x13, 0x45,
	0xe6, 0x15, 0xa8, 0x7b, 0x74, 0x3c, 0xf3, 0x7c, 0x2f, 0x41, 0x00, 0xd7, 0xed, 0x0d, 0x8f, 0x3e,
	0x60, 0x9f, 0xe6, 0x9b, 0xd0, 0x41, 0xfa, 0x98, 0x04, 0x2e, 0x17, 0x93, 0x21, 0xb7, 0x6a, 0xb7,
	0x90, 0x3a, 0x08, 0x5c, 0x26, 0xa5, 0x75, 0x17, 0x5a, 0x67, 0xf4, 0x99, 0x3e, 0x6c, 0xa0, 0x6f,
	0x10, 0x57, 0xb8, 0x8a, 0xfc, 0xb4, 0xb6, 0xd5, 0x31, 0x68, 0xa4, 0x72, 0x1a, 0x3a, 0xe7, 0x6d,
	0xe8, 0xec, 0x1e, 0x39, 0x41, 0x40, 0x66, 0x0f, 0x3c, 0x9a, 0xac, 0x9c, 0xcf, 0xfa, 0x1d, 0x03,
	0x5a, 0x13, 0xce, 0xec, 0x05, 0x87, 0x61, 0xc2, 0xb4, 0x2a, 0xbf, 0x25, 0x73, 0x46, 0x60, 0xad,
	0xae, 0x47, 0xa3, 0x99, 0xb3, 0xf0, 0xe4, 0x02, 0x33, 0x02, 0x06, 0x2b, 0xce, 0xaa, 0x3a, 0x71,
	0x53, 0xd0, 0xd0, 0x87, 0xdf, 0x86, 0xae, 0x64, 0x79, 0xe9, 0x91, 0x57, 0xcc, 0x39, 0xb9, 0x0b,
	0xb7, 0x05, 0xf9, 0xa9, 0x47, 0x5e, 0x0d, 0x5d, 0x6b, 0xa4, 0xcb, 0x40, 0x23, 0xf3, 0x6e, 0x36,
	0xf8, 0xcc, 0xa3, 0x49, 0xdf, 0xb8, 0x59, 0x2d, 0x81, 0x8b, 0x2a, 0x4f, 0x3a, 0x3b, 0x1b, 0xc6,
	0x7a, 0x1b, 0xe0, 0x31, 0x09, 0x5c, 0x2f, 0x98, 0xae, 0xd6, 0xca, 0x1f, 0x1a, 0x19, 0x23, 0x8d,
	0x18, 0x96, 0x45, 0xfc, 0x7e, 0xee, 0xcc, 0x9c, 0x60, 0xc2, 0x83, 0x7c, 0xd5, 0x6e, 0x73, 0xea,
	0x5d, 0x4e, 0x64, 0xe3, 0xf9, 0xc4, 0x7f, 0x4e, 0x62, 0x8a, 0xaa, 0xa9, 0xda, 0xf2, 0x33, 0x1f,
	0xdf, 0xab, 0x59, 0x7c, 0xbf, 0x0e, 0x40, 0x13, 0x27, 0x4e, 0x38, 0x76, 0x6a, 0xd8, 0xd8, 0x40,
	0x0a, 0xba, 0xc7, 0x15, 0xa8, 0xa7, 0xc0, 0x5a, 0xe3, 0x3d, 0x89, 0xc0, 0xd4, 0x27, 0xd0, 0x13,
	0x4b, 0xdc, 0x23, 0x89, 0xe3, 0xcd, 0x56, 0xe3, 0xea, 0x3a, 0xc0, 0x8b, 0x39, 0x89, 0x17, 0x6a,
	0x14, 0x6e, 0x20, 0x05, 0xc3, 0x88, 0x09, 0xb5, 0xc8, 0x99, 0xf2, 0xd5, 0xb5, 0x6d, 0xfc, 0xcd,
	0xe6, 0x66, 0xff, 0x8e, 0x83, 0xb9, 0x8f, 0x0b, 0x6b, 0xdb, 0x1b, 0xec, 0x7b, 0x7f, 0xee, 0x5b,
	0xff, 0x6c, 0x40, 0x5b, 0x9b, 0xdc, 0xec, 0x41, 0x75, 0x9e, 0xce, 0xca, 0x7e, 0xea, 0x40, 0xaa,
	0xe4, 0x81, 0x64, 0x42, 0x4d, 0x81, 0x08, 0xfe, 0x5e, 0x82, 0x4f, 0x6d, 0x19, 0x3e, 0x8a, 0x22,
	0xd7, 0x74, 0x45, 0x5e, 0x86, 0x75, 0xb1, 0x01, 0xaf, 0x63, 0x83, 0xf8, 0x2a, 0x02, 0xdc, 0x46,
	0x11, 0xe0, 0x92, 0xbc, 0x3a, 0x69, 0x64, 0xee, 0x42, 0x2b, 0xe2, 0x34, 0x15, 0x72, 0x37, 0x0b,
	0x20, 0xa7, 0x77, 0x6d, 0x8a, 0x5e, 0x0c, 0x73, 0xe6, 0x35, 0x68, 0x04, 0xe4, 0x24, 0x19, 0xa3,
	0x7e, 0x79, 0xf4, 0xa8, 0x33, 0xc2, 0x63, 0x67, 0x4a, 0xac, 0xff, 0x31, 0xc0, 0x7c, 0x36, 0x5f,
	0x78, 0x81, 0xae, 0x4d, 0x54, 0xbd, 0xe7, 0x8e, 0x33, 0x95, 0x6e, 0xb0, 0xef, 0x27, 0x9e, 0xcb,
	0x86, 0x73, 0x82, 0xc9, 0x51, 0x18, 0x8f, 0x53, 0xb5, 0xd6, 0x39, 0x61, 0x88, 0x8d, 0xd8, 0x4f,
	0x51, 0x2d, 0x0e, 0x84, 0xba, 0xbb, 0x01, 0x4d, 0xd1, 0x53, 0xd1, 0x2e, 0x70, 0xd2, 0xa7, 0x54,
	0xae, 0x9c, 0x2f, 0x49, 0x52, 0xb5, 0xe2, 0x7c, 0xa3, 0xc4, 0x73, 0x95, 0xf9, 0xb0, 0xb9, 0xae,
	0xce, 0xc7, 0x18, 0xac, 0xef, 0xc0, 0xa5, 0x65, 0xd9, 0x99, 0xde, 0x3f, 0x2c, 0xd4, 0xfb, 0x5b,
	0x05, 0x7a, 0x2f, 0xe8, 0x7f, 0x76, 0xe5, 0xdf, 0x86, 0xce, 0x28, 0x74, 0x9d, 0x05, 0xcf, 0xd2,
	0x56, 0x47, 0x84, 0xbf, 0x35, 0xa0, 0xab, 0x30, 0xb3, 0xad, 0xe8, 0x94, 0x50, 0x99, 0x47, 0x73,
	0xa5, 0x10, 0xcd, 0x32, 0x60, 0x54, 0x4b, 0x03, 0x46, 0xad, 0xcc, 0x14, 0x6b, 0xa7, 0xe1, 0x7c,
	0xbd, 0x08, 0xe7, 0x1f, 0xe9, 0x42, 0xd3, 0xc8, 0x1c, 0x14, 0x06, 0x56, 0xab, 0x40, 0xdb, 0x39,
	0x05, 0xe8, 0xb1, 0xf5, 0x7d, 0xb8, 0x80, 0xd6, 0x38, 0xb3, 0x4a, 0xff, 0xd7, 0x80, 0x8b, 0xf9,
	0x1e, 0xa8, 0xd7, 0x1c, 0x50, 0x8d, 0x22, 0xa0, 0x96, 0xe8, 0xad, 0x07, 0xd5, 0x43, 0x22, 0x75,
	0xc6, 0x7e, 0x62, 0x26, 0x18, 0x13, 0x4a, 0x82, 0x64, 0xcc, 0x5a, 0xb8, 0xd2, 0x40, 0x90, 0xee,
	0x11, 0x8c, 0xc0, 0xc7, 0x81, 0x37, 0x3d, 0xe2, 0xed, 0x1c, 0xdf, 0x0d, 0x4e, 0x61, 0xcd, 0x99,
	0xbe, 0x37, 0x34, 0x7d, 0x2b, 0x16, 0xaa, 0xeb, 0x16, 0xda, 0x86, 0xde, 0x4b, 0x2f, 0x4e, 0xe6,
	0x0e, 0xd3, 0xe7, 0x4b, 0x82, 0xc3, 0x36, 0x90, 0xa5, 0x23, 0xe8, 0x0f, 0xbc, 0x97, 0xe4, 0x1e,
	0x21, 0x96, 0x5d, 0xa0, 0x32, 0x1a, 0x99, 0x3f, 0x07, 0x35, 0xc5, 0x10, 0xef, 0x94, 0xc1, 0x3e,
	0x6f, 0x0d, 0xec, 0x64, 0x0d, 0xa1, 0xf3, 0x30, 0x3b, 0x7a, 0x9c, 0xba, 0x29, 0xf0, 0xa3, 0x0b,
	0xee, 0x2f, 0x7c, 0xcf, 0x6a, 0x20, 0x05, 0x77, 0x98, 0x7f, 0xaf, 0x40, 0x57, 0x19, 0x0b, 0x6d,
	0x73, 0x0d, 0x1a, 0x2e, 0x1e, 0x16, 0x3c, 0x5f, 0xee, 0x82, 0x75, 0x46, 0xc0, 0xdd, 0x4a, 0xd1,
	0x49, 0x45, 0xd7, 0x49, 0xb9, 0xc5, 0x32, 0xfd, 0xd6, 0x34, 0xfd, 0x0a, 0x4b, 0xae, 0x95, 0x5a,
	0x72, 0xfd, 0x14, 0x4b, 0x6e, 0xe4, 0x2d, 0xf9, 0x06, 0xb4, 0x5e, 0x91, 0x98, 0xbc, 0x0a, 0x67,
	0x87, 0xc8, 0xc0, 0xcd, 0xd6, 0x94, 0x34, 0xc6, 0x72, 0x1b, 0x36, 0xbd, 0x20, 0x21, 0xb1, 0x33,
	0x49, 0xc6, 0x53, 0xc7, 0x57, 0x6d, 0xd7, 0x95, 0x0d, 0xf7, 0x1d, 0x9f, 0x19, 0xaf, 0xd0, 0xcc,
	0x50, 0x64, 0x66, 0xb6, 0x2e, 0x42, 0xa3, 0x30, 0xe6, 0xeb, 0x6a, 0xf2, 0x75, 0x71, 0x0a, 0x43,
	0xc1, 0xc7, 0xba, 0xc5, 0x68, 0x64, 0xee, 0xc3, 0xa6, 0x7a, 0xa4, 0x3c, 0xcd, 0x2d, 0x73, 0x36,
	0xb2, 0xbb, 0xca, 0xd9, 0x13, 0x5d, 0xf3, 0x1f, 0x0d, 0xe8, 0xed, 0x72, 0x4b, 0x1c, 0x10, 0x27,
	0x9e, 0x1c, 0x9d, 0x9e, 0x83, 0x4e, 0x26, 0xe1, 0x3c, 0x48, 0x44, 0xd0, 0x92, 0x9f, 0x7a, 0xc4,
	0xab, 0xe6, 0x23, 0xde, 0xa7, 0xce, 0x65, 0xd2, 0xf4, 0x63, 0xbd, 0x24, 0xfd, 0xd8, 0xd0, 0xd3,
	0x8f, 0xdf, 0x37, 0xa0, 0x2d, 0xe4, 0x11, 0x1b, 0xa6, 0xb2, 0x64, 0x43, 0x5f, 0xb2, 0x4c, 0x34,
	0x2a, 0x4a, 0xa2, 0x51, 0x9e, 0x8e, 0x29, 0xc7, 0x6d, 0x45, 0x08, 0x79, 0xdc, 0xc6, 0xb5, 0xe6,
	0xe3, 0xfa, 0xda, 0x52, 0x5c, 0xb7, 0xe2, 0xbc, 0xba, 0x69, 0xa4, 0xef, 0x44, 0x86, 0xbe, 0x13,
	0x99, 0x3b, 0x69, 0x81, 0x00, 0x4d, 0x5d, 0x29, 0xcd, 0x33, 0x34, 0xa9, 0x65, 0x09, 0x01, 0x6d,
	0xfc, 0x2f, 0x86, 0xd8, 0x4d, 0x3f, 0x13, 0x43, 0xb3, 0xf3, 0x2b, 0x0f, 0xc1, 0xf3, 0xcc, 0xd2,
	0x9c, 0xf2, 0xe4, 0xf3, 0xb4, 0xf4, 0xbf, 0xca, 0xfc, 0x48, 0x37, 0xf7, 0x1b, 0xd0, 0xc2, 0xbc,
	0x43, 0xb7, 0x79, 0x93, 0xd1, 0x76, 0x84, 0x04, 0x5a, 0x2a, 0x54, 0xc9, 0xa5, 0x42, 0x3f, 0x12,
	0x00, 0xde, 0x82, 0x8e, 0xd0, 0x8d, 0x9c, 0x9e, 0x43, 0xa0, 0xcd, 0xa9, 0x72, 0x01, 0xb9, 0x5d,
	0x6c, 0x3d, 0xbf, 0x8b, 0x59, 0xbf, 0x56, 0x68, 0xb0, 0xd3, 0xa0, 0x72, 0xaf, 0x08, 0x2a, 0xa5,
	0xa9, 0x51, 0x39, 0x5e, 0x7e, 0xc8, 0x62, 0x02, 0xc7, 0xec, 0x59, 0xb6, 0x8a, 0xd5, 0xd9, 0xfc,
	0x75, 0x80, 0xd8, 0x09, 0xa6, 0x84, 0x9f, 0x2e, 0x04, 0x5c, 0x90, 0x82, 0xa7, 0x8b, 0xcf, 0x0d,
	0x2e, 0xbf, 0x6e, 0xc0, 0xa6, 0x26, 0x14, 0xee, 0x59, 0x79, 0x9b, 0x1a, 0xcb, 0x36, 0xfd, 0x34,
	0x47, 0xb7, 0x92, 0x9d, 0xcb, 0xfa, 0xb3, 0x25, 0xcd, 0xf2, 0xd3, 0xba, 0x1c, 0xc6, 0x28, 0x1b,
	0xa6, 0x92, 0xcf, 0xad, 0x33, 0x14, 0x54, 0x73, 0x28, 0x18, 0xc1, 0x05, 0x19, 0x84, 0x54, 0x34,
	0xd4, 0x10, 0x0d, 0x6f, 0x16, 0x05, 0x8e, 0xbc, 0x56, 0xec, 0xcd, 0x89, 0x4a, 0x42, 0x4c, 0x7c,
	0x15, 0x5a, 0x7b, 0xce, 0x62, 0x14, 0x93, 0xc0, 0x45, 0xc5, 0xf5, 0xa0, 0xea, 0x3a, 0x0b, 0x79,
	0xa8, 0x73, 0x9d, 0x45, 0xd9, 0x62, 0xad, 0x7f, 0x33, 0xa0, 0x87, 0xf5, 0x9f, 0x74, 0x9e, 0x53,
	0xf3, 0xe3, 0x1f, 0xb9, 0x94, 0x50, 0x9e, 0x23, 0xbf, 0x0d, 0x5d, 0x91, 0xd7, 0x84, 0x41, 0xf8,
	0x6a, 0xfc, 0x22, 0x7c, 0x81, 0xb8, 0xaa, 0xd8, 0x6d, 0x9e, 0xdc, 0x30, 0xea, 0xb7, 0xc2, 0x17,
	0x67, 0xce, 0x99, 0xdf, 0x13, 0xc2, 0x0d, 0x03, 0x2f, 0x41, 0xdd, 0xad, 0xcc, 0x6b, 0x7f, 0xb3,
	0x9e, 0x67, 0xa7, 0x11, 0x73, 0x01, 0x5e, 0x3d, 0x53, 0x52, 0xda, 0x06, 0x52, 0xa4, 0x2c, 0x72,
	0xb4, 0x4a, 0x69, 0x8c, 0xae, 0x2e, 0xc5, 0x68, 0xa5, 0x42, 0xc8, 0x4f, 0xe8, 0x7c, 0x48, 0x59,
	0x63, 0x94, 0xf5, 0xa1, 0x35, 0xad, 0x3e, 0xc4, 0x22, 0x13, 0x25, 0xf1, 0x4b, 0x12, 0x73, 0x77,
	0x10, 0x89, 0x14, 0x27, 0xc9, 0x2d, 0x2e, 0x61, 0xc9, 0xe5, 0x58, 0xcb, 0x7c, 0x9b, 0x49, 0x96,
	0x70, 0x9a, 0xef, 0x42, 0x6f, 0x41, 0x68, 0x42, 0x62, 0x85, 0x8d, 0x27, 0x54, 0xdd, 0x94, 0x2e,
	0x58, 0x6f, 0xc3, 0x66, 0x72, 0xe4, 0xd1, 0xb1, 0x56, 0x25, 0x17, 0x49, 0x15, 0x6b, 0x50, 0xd2,
	0x17, 0xc6, 0x3b, 0x73, 0x68, 0xa2, 0xf3, 0xf2, 0xac, 0xaa, 0xcb, 0x1a, 0x54, 0xde, 0x9f, 0x85,
	0x3a, 0x9b, 0x1c, 0x81, 0xdf, 0x2c, 0x2d, 0x06, 0xa9, 0x80, 0xb6, 0x37, 0x5c, 0x67, 0x81, 0xe7,
	0xc2, 0x7b, 0xb9, 0x33, 0x4f, 0x0b, 0xfb, 0xdf, 0x2a, 0xe8, 0x9f, 0x47, 0xb5, 0x76, 0xe8, 0x51,
	0x61, 0x3a, 0x71, 0xe8, 0x51, 0xbf, 0x7d, 0xd3, 0xd8, 0x36, 0x52, 0x96, 0x5d, 0x87, 0x1e, 0x31,
	0x3f, 0xc6, 0x54, 0x12, 0xdb, 0x3b, 0xd8, 0x5e, 0x67, 0x04, 0xd9, 0x88, 0xc9, 0x23, 0x36, 0x76,
	0x79, 0x23, 0x23, 0x60, 0xe3, 0x16, 0xb0, 0xf5, 0x22, 0x7c, 0x7b, 0x08, 0xdf, 0x75, 0xd7, 0x59,
	0x30, 0xdc, 0x5e, 0x03, 0x9e, 0xa5, 0x63, 0xd3, 0x26, 0x36, 0xd5, 0x91, 0xc0, 0x1a, 0x31, 0xcd,
	0x75, 0x66, 0xc9, 0xd1, 0xf8, 0xa5, 0x33, 0x9b, 0x93, 0xbe, 0x89, 0xc6, 0x6f, 0x72, 0xda, 0x53,
	0x46, 0xc2, 0x7a, 0x16, 0x96, 0x39, 0x65, 0x11, 0xb4, 0x7f, 0x01, 0xe3, 0x4b, 0x1b, 0xcb, 0x9d,
	0x92, 0xc8, 0x70, 0xc2, 0x74, 0x2e, 0xd7, 0x70, 0x11, 0x27, 0x02, 0x41, 0x12, 0xfe, 0x93, 0xf7,
	0xb3, 0x4b, 0x45, 0x7e, 0xf6, 0x65, 0xb8, 0xac, 0x58, 0x95, 0x32, 0x6d, 0x44, 0x24, 0xf6, 0x42,
	0xb7, 0x7f, 0x19, 0x4d, 0x7b, 0x21, 0x35, 0xed, 0x81, 0xe3, 0x93, 0xc7, 0xd8, 0x64, 0xee, 0x42,
	0x1b, 0x3b, 0xa5, 0x36, 0xde, 0x3a, 0x9b, 0x8d, 0x71, 0xcd, 0x7b, 0xc2, 0xce, 0xdb, 0xd0, 0x73,
	0xe6, 0xae, 0x17, 0x8e, 0x9f, 0x3b, 0xd4, 0x9b, 0x70, 0x35, 0xf7, 0x51, 0xcd, 0x1d, 0xa4, 0xdf,
	0x65, 0x64, 0x54, 0x76, 0xca, 0x49, 0x4e, 0x92, 0xd8, 0xe1, 0x9c, 0x57, 0x14, 0xce, 0x01, 0x23,
	0x4b, 0x4e, 0x21, 0x88, 0x77, 0x22, 0x21, 0x7a, 0x95, 0x27, 0xfe, 0x48, 0x3f, 0xf0, 0x4e, 0x38,
	0x42, 0xad, 0x77, 0xa1, 0x2d, 0xe0, 0x43, 0x26, 0xc7, 0xab, 0x83, 0xc6, 0x0f, 0x0d, 0x8d, 0x97,
	0x46, 0xe6, 0x2d, 0x68, 0x1f, 0x39, 0x74, 0xec, 0xf8, 0x73, 0x4a, 0x7c, 0x22, 0x92, 0x9c, 0xba,
	0xdd, 0x3a, 0x72, 0xe8, 0x8e, 0xa4, 0xe1, 0x5a, 0xe6, 0xb3, 0xc4, 0x53, 0xef, 0x0a, 0x78, 0x00,
	0xe9, 0x20, 0x3d, 0xbb, 0x28, 0x48, 0xe5, 0x53, 0x38, 0xf9, 0x46, 0xcd, 0xe5, 0xcb, 0x38, 0x6f,
	0x41, 0x9b, 0x8f, 0x29, 0xc3, 0x07, 0x0f, 0x2d, 0x2d, 0x24, 0xee, 0x88, 0x18, 0x72, 0x0b, 0xda,
	0x7c, 0x38, 0x3d, 0xc6, 0xb4, 0x90, 0x28, 0x98, 0xac, 0x5d, 0xb8, 0xc4, 0x65, 0x12, 0x59, 0x08,
	0xa2, 0x66, 0x75, 0x9e, 0x21, 0xb6, 0x1c, 0xbe, 0xbb, 0xb0, 0x9f, 0xd6, 0x0f, 0x8c, 0xc2, 0x51,
	0x78, 0x9e, 0x44, 0x13, 0x27, 0xd1, 0xce, 0xa2, 0x8c, 0x70, 0xca, 0x59, 0xf4, 0x36, 0x6c, 0xe2,
	0xfd, 0x57, 0x4c, 0xc6, 0x59, 0x77, 0xbe, 0xb7, 0x77, 0x45, 0xc3, 0x81, 0x1c, 0xe5, 0x1d, 0x90,
	0xa4, 0xb1, 0xbe, 0xd7, 0x74, 0x04, 0x59, 0x2c, 0x8a, 0xad, 0x3b, 0xdb, 0x66, 0xd8, 0xcf, 0x54,
	0xf8, 0x3d, 0x67, 0xb1, 0x8b, 0xbc, 0x1e, 0x0d, 0x83, 0xf3, 0x0a, 0xff, 0x3d, 0x03, 0x36, 0xb5,
	0x01, 0xe4, 0x21, 0x5c, 0x17, 0xbc, 0xa6, 0x08, 0x7e, 0x1d, 0x40, 0x89, 0xc9, 0x15, 0x6c, 0x6d,
	0x64, 0xd1, 0xb8, 0xdc, 0x17, 0xab, 0xc8, 0x5a, 0xe8, 0x8b, 0xcb, 0xd2, 0x7d, 0xbf, 0x02, 0x97,
	0x8b, 0xc4, 0xa3, 0x91, 0xf9, 0x14, 0x2e, 0x22, 0x68, 0xd1, 0x73, 0x27, 0x69, 0x1b, 0x2e, 0xb4,
	0x38, 0x39, 0x59, 0x92, 0xd0, 0x36, 0x71, 0x04, 0x8d, 0xce, 0xc6, 0x5d, 0xb0, 0x9c, 0x36, 0x3f,
	0x6e, 0xe5, 0x3c, 0xe3, 0xe2, 0x08, 0xfa, 0xb8, 0xbf, 0x04, 0x97, 0xc4, 0xf1, 0x3c, 0x37, 0x70,
	0xf5, 0x1c, 0x03, 0x5f, 0xe0, 0x43, 0x68, 0x0d, 0xd6, 0x09, 0x74, 0x05, 0x3e, 0x6c, 0x27, 0x38,
	0x1e, 0x26, 0xc4, 0x37, 0x2f, 0xc2, 0x9a, 0x33, 0xf3, 0x1c, 0x2a, 0x52, 0x00, 0xfe, 0x81, 0xb9,
	0x9e, 0x37, 0x39, 0xd6, 0x0e, 0x2b, 0x8c, 0x20, 0x73, 0x83, 0x92, 0x0c, 0xa0, 0x34, 0x03, 0xb2,
	0x8e, 0xa1, 0xa3, 0xcc, 0xcc, 0x50, 0xf7, 0x75, 0x25, 0x09, 0x66, 0xbe, 0xcf, 0xe6, 0xef, 0x7c,
	0xe9, 0xb5, 0x02, 0xe1, 0x6c, 0x99, 0xb7, 0x67, 0x29, 0xb2, 0xc8, 0x27, 0x8a, 0x53, 0x14, 0xeb,
	0x63, 0x7d, 0x32, 0x5e, 0xc0, 0x90, 0x93, 0xc5, 0x4e, 0x70, 0x7c, 0x5a, 0x01, 0x23, 0xa7, 0x24,
	0xe6, 0x86, 0x29, 0x01, 0x13, 0xd3, 0x6f, 0xc3, 0xa6, 0x1d, 0x86, 0xbe, 0xe0, 0x3b, 0x35, 0x88,
	0xac, 0x3e, 0xac, 0x08, 0x2f, 0xab, 0x66, 0x5e, 0xf6, 0x4f, 0xc6, 0xd2, 0xf8, 0x3f, 0xee, 0xe1,
	0x45, 0x97, 0x71, 0x3d, 0x27, 0xa3, 0xf5, 0x31, 0x5c, 0xba, 0x4f, 0x12, 0x7e, 0x23, 0x8b, 0x27,
	0xce, 0x53, 0x6f, 0x02, 0xd3, 0xa3, 0x56, 0xa5, 0xe4, 0xa8, 0x55, 0xd5, 0x8f, 0x5a, 0xdf, 0x12,
	0xe1, 0x8d, 0x0f, 0x7f, 0xe0, 0xf9, 0xd1, 0x8c, 0xc8, 0x43, 0x43, 0xee, 0x26, 0xa8, 0xa8, 0x04,
	0x63, 0x42, 0x0d, 0x6f, 0x05, 0xc4, 0xfd, 0x0f, 0xfb, 0x6d, 0x1d, 0x17, 0x2e, 0x9a, 0x46, 0xe6,
	0xcf, 0x6b, 0x05, 0xd1, 0xed, 0xb2, 0x2c, 0x2d, 0xbf, 0x14, 0x5e, 0x11, 0x65, 0x2e, 0x97, 0x84,
	0x89, 0x33, 0x13, 0x92, 0xf1, 0x0f, 0xab, 0x0b, 0x6d, 0x39, 0xd9, 0xd0, 0xb5, 0xc9, 0x0b, 0xcb,
	0xd3, 0x08, 0xfc, 0xc8, 0x76, 0xee, 0xba, 0xc9, 0x9b, 0xd0, 0xe1, 0xfb, 0x2b, 0x3d, 0x62, 0x01,
	0x25, 0xad, 0x9d, 0xb4, 0x90, 0x7a, 0xc0, 0x88, 0x43, 0xd7, 0xfa, 0x06, 0x98, 0x83, 0x13, 0xc7,
	0x7f, 0xe2, 0xb9, 0x62, 0xba, 0xbc, 0xe2, 0x1a, 0x5c, 0x71, 0xe5, 0x2e, 0xf7, 0x55, 0xb8, 0x66,
	0x93, 0x49, 0xe8, 0xfb, 0x24, 0x70, 0xd9, 0x50, 0x78, 0x19, 0x2d, 0xd2, 0x0c, 0xaa, 0x5f, 0x58,
	0x8b, 0xb5, 0x8b, 0x0b, 0x6b, 0x6b, 0x06, 0x9d, 0x47, 0x11, 0x09, 0x44, 0x46, 0xcb, 0x20, 0x71,
	0x8e, 0x79, 0x0b, 0xef, 0x09, 0xaf, 0x72, 0x90, 0x1c, 0x78, 0x9f, 0xc8, 0x53, 0x48, 0xfa, 0x6d,
	0x7d, 0xd7, 0x80, 0x8d, 0x91, 0x33, 0x45, 0xf9, 0x98, 0x1d, 0x9c, 0xe9, 0xf3, 0xa9, 0x0c, 0x7d,
	0xf8, 0x81, 0x4f, 0x21, 0x9c, 0xe9, 0x88, 0x9c, 0xa4, 0xba, 0x14, 0x9f, 0x82, 0x7f, 0x28, 0x55,
	0xc8, 0x3f, 0x14, 0xdc, 0x0f, 0x65, 0x66, 0x92, 0x11, 0xcc, 0x9b, 0xa0, 0x1e, 0x11, 0x8b, 0x6a,
	0x73, 0x1f, 0x41, 0x57, 0x93, 0x9f, 0x46, 0xe6, 0x1d, 0x0d, 0x5e, 0x57, 0x8b, 0x2e, 0x3e, 0xb8,
	0x08, 0x2b, 0x01, 0xf5, 0xdb, 0x2c, 0x88, 0x48, 0x9b, 0x3c, 0xa1, 0x24, 0xa6, 0xe7, 0x55, 0xae,
	0x26, 0x5a, 0x35, 0x2f, 0x9a, 0x54, 0x7d, 0xad, 0x44, 0xf5, 0x6b, 0x39, 0xd5, 0x1f, 0x41, 0x9d,
	0xad, 0xa2, 0xc4, 0x27, 0xcb, 0x21, 0x7c, 0x15, 0xd2, 0xad, 0x47, 0x5e, 0x21, 0xa6, 0x5b, 0x51,
	0xba, 0x7b, 0xd5, 0x94, 0xdd, 0xcb, 0x7a, 0x05, 0x9b, 0xbc, 0x78, 0xf5, 0x90, 0x65, 0x88, 0x0f,
	0x9c, 0x85, 0xef, 0x04, 0x9f, 0xa1, 0xe0, 0x7d, 0xd8, 0x38, 0x26, 0x8b, 0x57, 0x61, 0x2c, 0x9f,
	0x0a, 0xc8, 0x4f, 0xeb, 0x57, 0xc0, 0xcc, 0x6b, 0x9c, 0x46, 0xe6, 0xfb, 0x9a, 0x39, 0xaf, 0x15,
	0x98, 0x53, 0xea, 0x65, 0xa5, 0x3d, 0xff, 0xc3, 0x80, 0x4b, 0x9a, 0x8f, 0xdd, 0x75, 0x28, 0x39,
	0xaf, 0xa3, 0xb2, 0x96, 0x68, 0x1e, 0x47, 0x21, 0x25, 0x69, 0x00, 0xe5, 0x9f, 0xa7, 0x00, 0xf9,
	0x75, 0x80, 0xc9, 0x3c, 0x8e, 0x49, 0x90, 0x8c, 0x9c, 0xa9, 0x7c, 0xbf, 0x94, 0x51, 0xd8, 0xb8,
	0xe4, 0xc4, 0xf1, 0x59, 0x23, 0x0f, 0xfe, 0xf2, 0x93, 0xd9, 0x8f, 0xf0, 0xe0, 0x42, 0xfb, 0x1b,
	0x37, 0xab, 0x0c, 0x13, 0xf2, 0x9b, 0xe3, 0x85, 0x52, 0xd4, 0x65, 0x5d, 0xd6, 0x44, 0xf9, 0xb7,
	0xf5, 0xcb, 0xd0, 0x1c, 0xf0, 0x21, 0x50, 0xc8, 0xcb, 0xb0, 0xce, 0xba, 0x0d, 0x25, 0x6a, 0xc4,
	0x17, 0x13, 0x3e, 0x71, 0xa6, 0x02, 0x34, 0xec, 0x27, 0x3b, 0x70, 0xf2, 0x27, 0x30, 0xc1, 0xdc,
	0x7f, 0x4e, 0x62, 0x21, 0x67, 0x13, 0x69, 0xfb, 0x48, 0xb2, 0x1e, 0x83, 0x79, 0x9f, 0x24, 0x62,
	0x78, 0xbe, 0x15, 0xd1, 0x88, 0x1d, 0xe0, 0x09, 0xc6, 0xae, 0xd4, 0x58, 0xaf, 0x17, 0x18, 0x4b,
	0x59, 0x94, 0x9d, 0xf2, 0x5b, 0xbf, 0x57, 0x81, 0x3e, 0x56, 0x38, 0x59, 0xb3, 0x72, 0x68, 0x2d,
	0x37, 0xd0, 0x67, 0x05, 0x77, 0x55, 0xf5, 0x6b, 0xba, 0xea, 0x4d, 0x86, 0xbc, 0xe0, 0x58, 0x14,
	0x91, 0xf0, 0x37, 0x33, 0xf3, 0x73, 0x32, 0xf5, 0x02, 0xb6, 0xdf, 0x8b, 0x72, 0x65, 0x46, 0xc0,
	0xb1, 0x78, 0xa9, 0x53, 0x3c, 0x12, 0x4b, 0x2b, 0x9f, 0x3c, 0x2e, 0xe2, 0xb2, 0x1a, 0x69, 0x5c,
	0xc4, 0x55, 0x59, 0xd0, 0x72, 0x28, 0x25, 0x94, 0xb2, 0x34, 0x62, 0x4e, 0xc5, 0xdb, 0x30, 0x8d,
	0x66, 0xfd, 0x89, 0x01, 0x37, 0x52, 0xf5, 0x64, 0xef, 0xb9, 0xf8, 0x43, 0x2a, 0x8f, 0x9f, 0x43,
	0x96, 0xb5, 0xf4, 0xba, 0xac, 0xfa, 0x23, 0x78, 0xc4, 0x33, 0xc5, 0x8c, 0xa2, 0xc2, 0xbc, 0xaa,
	0xc3, 0x7c, 0x0f, 0x9a, 0x98, 0x59, 0x8b, 0x25, 0xd5, 0x30, 0xb9, 0xb4, 0xca, 0xaa, 0xd2, 0x6c,
	0x51, 0x9c, 0xd3, 0x56, 0xbb, 0x59, 0x16, 0xdc, 0x5c, 0xbd, 0x68, 0x1a, 0x59, 0x8f, 0xa0, 0x95,
	0xf2, 0xf0, 0xbc, 0xb6, 0xe6, 0x05, 0x87, 0xa1, 0x38, 0x5d, 0x7c, 0x61, 0xd5, 0x94, 0x39, 0x98,
	0xd8, 0xd8, 0x91, 0x25, 0x02, 0xca, 0x80, 0x34, 0xb2, 0x6e, 0x41, 0xf7, 0x3e, 0x49, 0xb4, 0x49,
	0x96, 0x54, 0x65, 0xf9, 0xd0, 0xd3, 0x99, 0x78, 0xc2, 0x20, 0x76, 0x59, 0x71, 0x56, 0x4f, 0x5f,
	0x89, 0xc9, 0x45, 0x56, 0x3e, 0xed, 0x22, 0x5f, 0x40, 0x5f, 0x9d, 0xee, 0x5e, 0x18, 0xa3, 0x7e,
	0x70, 0xda, 0xaf, 0x6b, 0xf1, 0xee, 0x7c, 0x83, 0xaf, 0x88, 0x7f, 0xef, 0x62, 0x36, 0x96, 0xb7,
	0x57, 0xa1, 0x32, 0xbe, 0x08, 0x97, 0x8b, 0x58, 0x69, 0xc4, 0xa2, 0x08, 0xe5, 0x90, 0x60, 0xec,
	0x6b, 0xb6, 0xf8, 0xb2, 0xee, 0x40, 0x7d, 0xe4, 0x4c, 0x29, 0x7a, 0x6b, 0x07, 0x2a, 0xe9, 0xde,
	0x54, 0x29, 0x4e, 0x17, 0xad, 0x07, 0x00, 0x07, 0xf3, 0xe7, 0x3e, 0xaf, 0xa7, 0xca, 0x18, 0x24,
	0xb6, 0xb3, 0x44, 0xf1, 0xbc, 0x8a, 0xe2, 0x79, 0xca, 0x0b, 0xc0, 0xaa, 0xfe, 0x02, 0xf0, 0x95,
	0x62, 0x72, 0x1c, 0xf0, 0x7d, 0xa8, 0x25, 0xce, 0x94, 0xae, 0xd8, 0x32, 0xe4, 0x6a, 0x6d, 0x64,
	0x34, 0x3f, 0x80, 0x75, 0x8a, 0xeb, 0x11, 0x26, 0xbd, 0x5e, 0xd0, 0x25, 0x5b, 0xb0, 0x2d, 0x98,
	0xad, 0x31, 0x34, 0x71, 0x62, 0xde, 0x54, 0xe0, 0x81, 0x4a, 0x74, 0xcd, 0x49, 0x56, 0x2d, 0x96,
	0xac, 0xa6, 0x4b, 0xf6, 0xbb, 0x06, 0xbc, 0xa1, 0x18, 0x99, 0xed, 0x5f, 0xb1, 0x2b, 0xd1, 0x82,
	0xde, 0x74, 0xde, 0xa4, 0x44, 0xd9, 0x7d, 0xab, 0xda, 0xee, 0x7b, 0xee, 0x84, 0xe4, 0x13, 0xb8,
	0xb4, 0xb4, 0x34, 0xd4, 0xfe, 0xeb, 0x00, 0x5c, 0x3f, 0x23, 0x79, 0xd2, 0x6a, 0xdb, 0x0a, 0xc5,
	0xdc, 0x81, 0x35, 0x2f, 0x21, 0x3e, 0x15, 0x97, 0x5d, 0xe7, 0x42, 0x38, 0xef, 0x69, 0x7d, 0x07,
	0xac, 0xd3, 0xd4, 0x72, 0xa6, 0x73, 0x46, 0xa1, 0x00, 0xc2, 0x8d, 0xd8, 0x4e, 0x22, 0xee, 0x67,
	0xe4, 0xc3, 0xac, 0xf4, 0xa9, 0xd1, 0x2d, 0xe8, 0x62, 0x76, 0xfe, 0xc4, 0x73, 0x1f, 0x3a, 0x5e,
	0x71, 0xf8, 0xb5, 0xbe, 0x00, 0x3d, 0x9d, 0x89, 0x46, 0xe6, 0x16, 0x8b, 0x29, 0x63, 0xdf, 0xf1,
	0x02, 0xb9, 0x0f, 0x7b, 0x94, 0x35, 0x5a, 0x53, 0xb8, 0x82, 0x65, 0x40, 0xac, 0x4b, 0xf2, 0xda,
	0x4e, 0xf6, 0x0e, 0x70, 0xd9, 0xc0, 0x57, 0xa0, 0xce, 0x0f, 0x26, 0x39, 0x0b, 0x0f, 0xf1, 0xf2,
	0x6e, 0x41, 0x9c, 0x98, 0x57, 0x84, 0x64, 0xfa, 0xc5, 0x28, 0x58, 0x05, 0xb2, 0xbe, 0xb7, 0x01,
	0x57, 0xcb, 0x66, 0xe2, 0x17, 0x1b, 0x4a, 0x6f, 0x23, 0xd7, 0x9b, 0x1d, 0x88, 0x94, 0xe2, 0xff,
	0x21, 0x91, 0x15, 0xa9, 0x56, 0x5a, 0xf9, 0xbf, 0x47, 0x08, 0xbe, 0xa2, 0xcd, 0x8a, 0x52, 0x8c,
	0x8b, 0x17, 0xa3, 0x5a, 0x69, 0x31, 0x4a, 0xbc, 0xce, 0x58, 0xbe, 0x48, 0xa8, 0x21, 0xe3, 0xd2,
	0x45, 0xc2, 0x4f, 0xc1, 0xa5, 0x25, 0xde, 0xf1, 0x24, 0x58, 0x88, 0x23, 0x81, 0x99, 0xe3, 0xdf,
	0x0d, 0x16, 0xf8, 0x06, 0x3d, 0x26, 0x78, 0x69, 0xec, 0x87, 0x01, 0x59, 0xe0, 0x36, 0x5e, 0xb3,
	0x5b, 0x82, 0xf8, 0x90, 0xd1, 0xd8, 0x1a, 0x34, 0x26, 0x1c, 0x73, 0x43, 0x3c, 0xb8, 0x56, 0x18,
	0xd9, 0x80, 0x97, 0x61, 0x3d, 0x26, 0xbe, 0x13, 0x1f, 0x8b, 0x5c, 0x4b, 0x7c, 0xe1, 0x8d, 0x40,
	0x1c, 0xbe, 0x1a, 0xe3, 0xab, 0x67, 0xbe, 0xb9, 0xd7, 0x19, 0xc1, 0x76, 0x12, 0x62, 0xfe, 0x6a,
	0xee, 0x66, 0x02, 0x10, 0x8b, 0x5f, 0x2b, 0xc2, 0x62, 0xa9, 0x51, 0xd2, 0xdb, 0xbe, 0x84, 0xf8,
	0xda, 0x7d, 0xc5, 0xd5, 0xbf, 0xab, 0x42, 0x53, 0x69, 0x64, 0xd6, 0x4b, 0xef, 0x11, 0xf3, 0x77,
	0x74, 0x43, 0xd7, 0x7c, 0x0f, 0x4c, 0xd9, 0x2c, 0xae, 0xe6, 0x32, 0xfc, 0xf4, 0x44, 0xcb, 0x1e,
	0x6f, 0x18, 0x9e, 0xe9, 0xce, 0xee, 0x46, 0x7a, 0x3e, 0x1b, 0xb3, 0x38, 0x27, 0xde, 0x20, 0x0a,
	0x12, 0x4b, 0xa1, 0x96, 0xf1, 0xb2, 0x56, 0x80, 0x97, 0x77, 0xa0, 0x4b, 0x49, 0x92, 0xcc, 0xb0,
	0x08, 0xce, 0xf5, 0xc8, 0x73, 0xae, 0x4e, 0x46, 0x46, 0x6d, 0x16, 0x42, 0x66, 0xa3, 0x18, 0x32,
	0xaa, 0x8b, 0xd4, 0x75, 0x17, 0xd9, 0x86, 0x1e, 0x6f, 0x52, 0xb4, 0xc0, 0x1f, 0xee, 0xf3, 0xe3,
	0x7e, 0xa6, 0x83, 0x65, 0x24, 0x43, 0x01, 0x92, 0x35, 0x04, 0x34, 0x73, 0x08, 0x28, 0xb8, 0x95,
	0x6c, 0x15, 0xdd, 0x4a, 0x7e, 0xdf, 0x80, 0xcb, 0x43, 0xe5, 0x51, 0xd2, 0xff, 0xd7, 0x0b, 0xba,
	0xb2, 0x17, 0x70, 0xb7, 0xa0, 0x8d, 0x17, 0x5b, 0xee, 0x9c, 0xa7, 0x67, 0xe2, 0xfe, 0xaf, 0xc5,
	0x88, 0x7b, 0x82, 0x66, 0x3d, 0x85, 0x4b, 0xcb, 0x6b, 0xb4, 0x69, 0x64, 0xfe, 0x82, 0x16, 0x6b,
	0xdf, 0x2d, 0xc0, 0x77, 0xb1, 0x6c, 0xe2, 0x99, 0xdb, 0x53, 0xb8, 0xaa, 0xb6, 0x2b, 0x6e, 0xc0,
	0xe2, 0x9f, 0x6a, 0x4a, 0x63, 0x29, 0xda, 0xe5, 0xde, 0xbc, 0xb5, 0xd5, 0x37, 0x6f, 0xff, 0x60,
	0xc0, 0xb5, 0xd2, 0x81, 0xf1, 0x70, 0x79, 0x11, 0x85, 0x96, 0xf7, 0x5e, 0x89, 0x33, 0x43, 0x2b,
	0xf3, 0x2a, 0xfc, 0x26, 0x6b, 0x7b, 0xc8, 0xef, 0xbe, 0x12, 0x67, 0xc6, 0x4c, 0xfd, 0x01, 0x6c,
	0x29, 0x1d, 0xf8, 0xe5, 0x92, 0x56, 0x9b, 0xbf, 0x98, 0xf6, 0x51, 0xe6, 0x32, 0xf7, 0x84, 0x7a,
	0xaa, 0xa8, 0x9e, 0x2f, 0x9e, 0xa2, 0x9e, 0xe5, 0x28, 0xc0, 0xb5, 0xf4, 0x37, 0x15, 0xb8, 0xbe,
	0x92, 0x2f, 0xf7, 0xd0, 0xa7, 0x92, 0x7f, 0xe8, 0x93, 0x7b, 0xc3, 0x5b, 0xcd, 0xbf, 0xe1, 0x2d,
	0x78, 0x0c, 0x53, 0x2b, 0x7a, 0x0c, 0xf3, 0x0e, 0x74, 0x25, 0x20, 0xbd, 0xc9, 0xb1, 0xf2, 0x6e,
	0x4a, 0xf4, 0xde, 0x17, 0x54, 0x65, 0x3d, 0x94, 0x9c, 0xc8, 0xba, 0x26, 0xa7, 0x1c, 0x90, 0x13,
	0xac, 0xaf, 0x65, 0xda, 0x94, 0xcf, 0xfc, 0x6a, 0x1c, 0x74, 0xa9, 0x7b, 0x31, 0x77, 0x65, 0x5c,
	0x22, 0xec, 0xa3, 0x97, 0xd5, 0xb1, 0x74, 0x8a, 0xbd, 0x85, 0x39, 0x99, 0xaf, 0xdd, 0x80, 0xa6,
	0xc2, 0x89, 0x3e, 0x5d, 0xb3, 0x21, 0x63, 0xb2, 0xbe, 0x0d, 0xbd, 0x01, 0x56, 0xf6, 0xcf, 0xe3,
	0x5d, 0x67, 0xf6, 0x21, 0xeb, 0x9b, 0xd0, 0x55, 0x87, 0x67, 0x8e, 0xf1, 0x33, 0x9a, 0x63, 0x14,
	0x5d, 0x49, 0xe7, 0x17, 0x24, 0x8c, 0xfd, 0x07, 0x86, 0x5c, 0xab, 0xf2, 0xa7, 0x0b, 0xcb, 0xf1,
	0x94, 0x57, 0xb2, 0xf5, 0x78, 0x5a, 0x18, 0x26, 0x2b, 0xc5, 0x57, 0xf4, 0xfa, 0x63, 0x9e, 0xea,
	0xaa, 0xc7, 0x3c, 0x35, 0xfd, 0x2f, 0x16, 0xa6, 0x70, 0x8d, 0xaf, 0x6f, 0x37, 0x74, 0x26, 0x47,
	0x84, 0xe6, 0xde, 0xa9, 0xae, 0x70, 0xda, 0x73, 0xd6, 0xa6, 0x7f, 0xcb, 0x80, 0xd7, 0xca, 0x66,
	0x2a, 0xaf, 0x51, 0x23, 0xc2, 0x2b, 0x59, 0x3d, 0x5a, 0x96, 0x06, 0x82, 0x5c, 0x69, 0x20, 0xc8,
	0x99, 0xb8, 0x96, 0x37, 0x31, 0xdf, 0xb1, 0xa4, 0x89, 0x4f, 0x56, 0x48, 0x8d, 0x7f, 0x63, 0xa0,
	0x9a, 0xfb, 0xfd, 0x52, 0x73, 0x17, 0x4b, 0xb2, 0xf2, 0x04, 0xf7, 0xe7, 0x06, 0x5c, 0x96, 0x9d,
	0xcf, 0xfc, 0x26, 0xf0, 0x1a, 0x34, 0x26, 0x6c, 0x2a, 0xe5, 0xe1, 0x5f, 0x1d, 0x09, 0x9f, 0xeb,
	0xbb, 0xbf, 0x3f, 0x35, 0xe0, 0x82, 0xb6, 0x74, 0x11, 0xae, 0x4c, 0xa8, 0xb9, 0xcc, 0x9b, 0x39,
	0x88, 0xf1, 0xb7, 0xbe, 0xe2, 0xca, 0xf2, 0x8a, 0x79, 0xa3, 0x12, 0xbf, 0x38, 0xbb, 0x0c, 0x5f,
	0xbc, 0x39, 0x35, 0xb4, 0xfc, 0x6b, 0x24, 0x46, 0xdd, 0xcf, 0x59, 0x7b, 0xad, 0xc8, 0xda, 0xeb,
	0x9a, 0xb5, 0xff, 0xbb, 0x44, 0xe7, 0xa7, 0x3d, 0xeb, 0xbb, 0x5f, 0xf4, 0xac, 0xef, 0xed, 0x15,
	0x68, 0x28, 0x79, 0xd7, 0xc7, 0x66, 0xc9, 0x76, 0x25, 0x9e, 0x45, 0xd7, 0x13, 0xb9, 0x19, 0xe1,
	0xc3, 0x1e, 0xd6, 0xa8, 0x25, 0xcf, 0x4d, 0xa4, 0x15, 0xba, 0xf7, 0xda, 0x2a, 0x13, 0xaf, 0xeb,
	0xee, 0xfd, 0xdd, 0x0a, 0x5c, 0xb9, 0xeb, 0x24, 0x93, 0xa3, 0xfb, 0x24, 0xd9, 0x99, 0xbb, 0x5e,
	0x72, 0x70, 0xec, 0xcd, 0xd8, 0x71, 0x64, 0x4e, 0x68, 0x72, 0x8a, 0x77, 0x2f, 0xf9, 0x1e, 0xdb,
	0x07, 0xd8, 0x18, 0xf2, 0xa1, 0x42, 0x15, 0xf7, 0x01, 0x46, 0xc1, 0xcb, 0xc8, 0x2d, 0xd8, 0x08,
	0x0f, 0x0f, 0xc7, 0x94, 0x24, 0xe2, 0xf8, 0xb9, 0x1e, 0x1e, 0x1e, 0x1e, 0x10, 0xf4, 0x04, 0x7e,
	0xed, 0xc1, 0x4f, 0x9f, 0xfc, 0x83, 0x29, 0x85, 0xff, 0xf5, 0xd3, 0x3c, 0xbd, 0x2c, 0xab, 0x23,
	0x81, 0x41, 0xe4, 0x3d, 0x30, 0x79, 0x23, 0x9f, 0x90, 0x86, 0xf3, 0x78, 0x22, 0x4b, 0x75, 0x3d,
	0x6c, 0xe1, 0xd2, 0x20, 0x9d, 0x85, 0x4a, 0x95, 0x3b, 0x09, 0x8f, 0x49, 0x20, 0xf2, 0xfb, 0x6e,
	0xc6, 0x3c, 0x62, 0x64, 0xeb, 0x3f, 0x0d, 0xb8, 0x5a, 0xa4, 0x11, 0x1a, 0x85, 0x01, 0x25, 0xab,
	0x54, 0x22, 0x02, 0x54, 0x25, 0x0b, 0x50, 0x9f, 0xad, 0x42, 0xe4, 0x26, 0xb3, 0x5e, 0xba, 0xc9,
	0xa8, 0xeb, 0x65, 0x07, 0x5d, 0x11, 0x69, 0x6e, 0x00, 0x47, 0xcb, 0x98, 0x6f, 0xf6, 0x5c, 0x4b,
	0x80, 0xa4, 0x5d, 0x46, 0xb1, 0x7e, 0x50, 0x85, 0x5e, 0xbe, 0xef, 0xb9, 0xee, 0x1f, 0xb4, 0xbb,
	0xf0, 0x7c, 0x45, 0x56, 0x42, 0xa5, 0xa6, 0x40, 0x85, 0x6d, 0xc7, 0x8a, 0x2d, 0xd6, 0xc4, 0x76,
	0x9c, 0x9a, 0x21, 0xa7, 0x3a, 0x99, 0x53, 0xa4, 0xaa, 0xcb, 0xfe, 0x56, 0x57, 0x29, 0xd1, 0xf2,
	0xbf, 0xd5, 0x65, 0x88, 0xff, 0x0a, 0xac, 0x51, 0x26, 0x4a, 0xbf, 0x5e, 0xfa, 0x2a, 0xfb, 0x09,
	0x25, 0x31, 0x8a, 0xcb, 0x4b, 0x0e, 0xc8, 0xce, 0x7c, 0x4d, 0x03, 0x14, 0x3f, 0x2f, 0xf0, 0xa5,
	0x0a, 0x2c, 0xe1, 0x01, 0xd1, 0xa1, 0x61, 0x80, 0x87, 0x04, 0x3c, 0x20, 0xb2, 0x2f, 0x0d, 0x18,
	0xcd, 0x25, 0x60, 0xb0, 0xc4, 0xa8, 0xc5, 0x35, 0x49, 0xc9, 0x09, 0x5e, 0x53, 0x7b, 0xd3, 0x80,
	0xaf, 0xbe, 0xcd, 0xb1, 0xcd, 0x08, 0xb8, 0xf8, 0x6d, 0xe8, 0x61, 0x23, 0x39, 0x89, 0xbc, 0x98,
	0x64, 0x7f, 0x87, 0xdb, 0xb6, 0x3b, 0x8c, 0x3e, 0x40, 0x32, 0x7a, 0xef, 0x1f, 0x55, 0xa0, 0xad,
	0xc9, 0xc1, 0x20, 0xc5, 0x73, 0xa3, 0xf4, 0x0a, 0x00, 0xf3, 0x22, 0x37, 0x7d, 0xd1, 0xa6, 0xbe,
	0x56, 0x60, 0x04, 0xb4, 0xd0, 0x5b, 0xd0, 0x41, 0xf9, 0xc7, 0xe4, 0xa5, 0xe7, 0x92, 0x60, 0x22,
	0x6d, 0xd8, 0x46, 0xea, 0x40, 0x10, 0xb1, 0xb4, 0x3e, 0x77, 0xbd, 0x30, 0x2d, 0xad, 0xb3, 0x0f,
	0xcc, 0x26, 0xf1, 0x55, 0x51, 0x7a, 0xa6, 0xe0, 0xa8, 0xe5, 0x6f, 0x8d, 0xe4, 0xa1, 0xc2, 0xdc,
	0x81, 0x16, 0x25, 0x13, 0xfc, 0xa3, 0x72, 0x05, 0xc5, 0x45, 0x97, 0x07, 0x07, 0x9c, 0x8d, 0x3f,
	0x0c, 0x13, 0x7d, 0xd2, 0x30, 0xc9, 0x82, 0xb1, 0x4b, 0xe8, 0x44, 0xfe, 0xe5, 0x1a, 0x23, 0xec,
	0x11, 0x3a, 0x49, 0x05, 0xf4, 0x26, 0xa1, 0xf4, 0x6d, 0x14, 0x70, 0x38, 0x09, 0x03, 0xeb, 0x21,
	0x34, 0x95, 0x51, 0x99, 0x99, 0xe5, 0x5a, 0x94, 0x6c, 0x50, 0xce, 0x85, 0x2a, 0xb9, 0x06, 0x0d,
	0x2f, 0x21, 0x7e, 0x16, 0xd9, 0x1b, 0x76, 0x9d, 0x11, 0xf0, 0x22, 0xe3, 0x8f, 0x0d, 0x78, 0xed,
	0x80, 0x24, 0xa9, 0xea, 0x77, 0x24, 0x2e, 0xcf, 0x10, 0x38, 0xcf, 0x90, 0xbc, 0x2b, 0x8e, 0x51,
	0x3d, 0xc5, 0x31, 0x6a, 0x79, 0xc7, 0xc8, 0xe0, 0xb9, 0xa6, 0xc2, 0xd3, 0xba, 0x01, 0xd7, 0x4b,
	0x56, 0xcc, 0x03, 0xdb, 0xed, 0x0f, 0xa0, 0x91, 0x3e, 0x22, 0x31, 0x4d, 0xe8, 0xec, 0xed, 0x3c,
	0x1b, 0xdb, 0x3b, 0xfb, 0xf7, 0x07, 0xe3, 0xd1, 0xb3, 0xc7, 0x83, 0xde, 0x4f, 0x98, 0x17, 0xa1,
	0xf7, 0xf0, 0xd1, 0xfe, 0xe8, 0x43, 0x95, 0x6a, 0xdc, 0xfe, 0x0d, 0x03, 0x1a, 0x83, 0x38, 0x0e,
	0xe3, 0xdd, 0xd0, 0x65, 0xb3, 0x9b, 0x03, 0xdb, 0x7e, 0x64, 0x8f, 0x77, 0x1f, 0xed, 0x0d, 0xc6,
	0x7b, 0x83, 0x7b, 0x3b, 0x4f, 0x1e, 0x8c, 0xb0, 0x6f, 0x57, 0xa1, 0x3f, 0xde, 0xb1, 0xed, 0xde,
	0x5f, 0x6c, 0x99, 0x57, 0xe0, 0xa2, 0x42, 0x7d, 0xb2, 0xff, 0x70, 0xc7, 0x3e, 0xf8, 0x70, 0xe7,
	0x41, 0xef, 0x2f, 0xb7, 0xcc, 0x2d, 0x6d, 0xa0, 0xe1, 0xfe, 0xd3, 0x9d, 0x07, 0xc3, 0xbd, 0xde,
	0x5f, 0xe5, 0x1b, 0xf6, 0x07, 0xa3, 0x8f, 0x1e, 0xd9, 0xbf, 0xd8, 0xfb, 0xeb, 0xad, 0xdb, 0xdf,
	0x48, 0xaf, 0xc2, 0x50, 0x82, 0x2e, 0x34, 0x47, 0x3b, 0xf7, 0x95, 0x25, 0x6c, 0x42, 0x9b, 0x11,
	0xec, 0xc1, 0xee, 0xa3, 0x87, 0x0f, 0x07, 0xfb, 0x7b, 0x3d, 0xc3, 0x6c, 0x43, 0x83, 0x91, 0x9e,
	0x3d, 0x79, 0x36, 0xdc, 0xef, 0x55, 0x6e, 0xef, 0x41, 0x37, 0x57, 0x0e, 0x37, 0x3b, 0x00, 0xcf,
	0x54, 0x39, 0x7a, 0xd0, 0x7a, 0x36, 0x18, 0xb3, 0x11, 0x86, 0xa3, 0xd1, 0x80, 0x8d, 0xd1, 0x85,
	0xe6, 0xb3, 0xc1, 0xd8, 0x1e, 0x7c, 0x73, 0xb0, 0xcb, 0x08, 0x95, 0xe7, 0xeb, 0xf8, 0xbf, 0x50,
	0x7c, 0xf9, 0xff, 0x02, 0x00, 0x00, 0xff, 0xff, 0x11, 0xce, 0xbd, 0x8a, 0xb0, 0x42, 0x00, 0x00,
}
