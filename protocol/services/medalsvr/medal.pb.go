// Code generated by protoc-gen-gogo.
// source: src/medalsvr/medal.proto
// DO NOT EDIT!

/*
	Package Medal is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/medalsvr/medal.proto

	It has these top-level messages:
		PMedal
		GetMedalConfigUpdateTimeReq
		GetMedalConfigUpdateTimeResp
		GetMedalConfigListReq
		GetMedalConfigListResp
		UserMedal
		UserTaillightMedal
		GetUserMedalListReq
		GetUserMedalListResp
		BatGetUserMedalListReq
		BatGetUserMedalListResp
		GetUserMedalListWhitTaillightReq
		GetUserMedalListWhitTaillightResp
		BatGetUserMedalListWhitTaillightReq
		BatGetUserMedalListWhitTaillightResp
		AwardMedalToUserReq
		AwardMedalToUserResp
		ReduceUserMedalReq
		ReduceUserMedalResp
		SetUserMedalTaillightReq
		SetUserMedalTaillightResp
		DeleteMedalReq
*/
package Medal

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// =============================================================================
//
// 勋章定义
//
// =============================================================================
type PMedal struct {
	MedalId             uint32   `protobuf:"varint,1,req,name=medal_id,json=medalId" json:"medal_id"`
	Name                string   `protobuf:"bytes,2,req,name=name" json:"name"`
	Desc                []string `protobuf:"bytes,3,rep,name=desc" json:"desc,omitempty"`
	Icon                string   `protobuf:"bytes,4,req,name=icon" json:"icon"`
	BorderIcon          string   `protobuf:"bytes,5,req,name=border_icon,json=borderIcon" json:"border_icon"`
	Url                 string   `protobuf:"bytes,6,req,name=url" json:"url"`
	ExpireTime          uint32   `protobuf:"varint,7,req,name=expire_time,json=expireTime" json:"expire_time"`
	BuffExpRate         uint32   `protobuf:"varint,8,req,name=buff_exp_rate,json=buffExpRate" json:"buff_exp_rate"`
	BuffCurrencyRate    uint32   `protobuf:"varint,9,req,name=buff_currency_rate,json=buffCurrencyRate" json:"buff_currency_rate"`
	Title               string   `protobuf:"bytes,10,req,name=title" json:"title"`
	Content             string   `protobuf:"bytes,11,req,name=content" json:"content"`
	TitleIcon           string   `protobuf:"bytes,12,opt,name=title_icon,json=titleIcon" json:"title_icon"`
	IsAllowSetTaillight bool     `protobuf:"varint,13,opt,name=is_allow_set_taillight,json=isAllowSetTaillight" json:"is_allow_set_taillight"`
}

func (m *PMedal) Reset()                    { *m = PMedal{} }
func (m *PMedal) String() string            { return proto.CompactTextString(m) }
func (*PMedal) ProtoMessage()               {}
func (*PMedal) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{0} }

func (m *PMedal) GetMedalId() uint32 {
	if m != nil {
		return m.MedalId
	}
	return 0
}

func (m *PMedal) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PMedal) GetDesc() []string {
	if m != nil {
		return m.Desc
	}
	return nil
}

func (m *PMedal) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *PMedal) GetBorderIcon() string {
	if m != nil {
		return m.BorderIcon
	}
	return ""
}

func (m *PMedal) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *PMedal) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *PMedal) GetBuffExpRate() uint32 {
	if m != nil {
		return m.BuffExpRate
	}
	return 0
}

func (m *PMedal) GetBuffCurrencyRate() uint32 {
	if m != nil {
		return m.BuffCurrencyRate
	}
	return 0
}

func (m *PMedal) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PMedal) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PMedal) GetTitleIcon() string {
	if m != nil {
		return m.TitleIcon
	}
	return ""
}

func (m *PMedal) GetIsAllowSetTaillight() bool {
	if m != nil {
		return m.IsAllowSetTaillight
	}
	return false
}

// =============================================================================
//
// 获取勋章定义最后更新时间
//
// =============================================================================
type GetMedalConfigUpdateTimeReq struct {
}

func (m *GetMedalConfigUpdateTimeReq) Reset()                    { *m = GetMedalConfigUpdateTimeReq{} }
func (m *GetMedalConfigUpdateTimeReq) String() string            { return proto.CompactTextString(m) }
func (*GetMedalConfigUpdateTimeReq) ProtoMessage()               {}
func (*GetMedalConfigUpdateTimeReq) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{1} }

type GetMedalConfigUpdateTimeResp struct {
	UpdateTime uint32 `protobuf:"varint,1,req,name=update_time,json=updateTime" json:"update_time"`
}

func (m *GetMedalConfigUpdateTimeResp) Reset()         { *m = GetMedalConfigUpdateTimeResp{} }
func (m *GetMedalConfigUpdateTimeResp) String() string { return proto.CompactTextString(m) }
func (*GetMedalConfigUpdateTimeResp) ProtoMessage()    {}
func (*GetMedalConfigUpdateTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMedal, []int{2}
}

func (m *GetMedalConfigUpdateTimeResp) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// =============================================================================
//
// 获取勋章定义列表
//
// =============================================================================
type GetMedalConfigListReq struct {
}

func (m *GetMedalConfigListReq) Reset()                    { *m = GetMedalConfigListReq{} }
func (m *GetMedalConfigListReq) String() string            { return proto.CompactTextString(m) }
func (*GetMedalConfigListReq) ProtoMessage()               {}
func (*GetMedalConfigListReq) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{3} }

type GetMedalConfigListResp struct {
	MedalList []*PMedal `protobuf:"bytes,1,rep,name=medal_list,json=medalList" json:"medal_list,omitempty"`
}

func (m *GetMedalConfigListResp) Reset()                    { *m = GetMedalConfigListResp{} }
func (m *GetMedalConfigListResp) String() string            { return proto.CompactTextString(m) }
func (*GetMedalConfigListResp) ProtoMessage()               {}
func (*GetMedalConfigListResp) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{4} }

func (m *GetMedalConfigListResp) GetMedalList() []*PMedal {
	if m != nil {
		return m.MedalList
	}
	return nil
}

type UserMedal struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MedalId      uint32 `protobuf:"varint,2,req,name=medal_id,json=medalId" json:"medal_id"`
	AwardTime    uint32 `protobuf:"varint,3,req,name=award_time,json=awardTime" json:"award_time"`
	ExpireTime   uint32 `protobuf:"varint,4,req,name=expire_time,json=expireTime" json:"expire_time"`
	BuffExp      uint32 `protobuf:"varint,5,req,name=buff_exp,json=buffExp" json:"buff_exp"`
	BuffCurrency uint32 `protobuf:"varint,6,req,name=buff_currency,json=buffCurrency" json:"buff_currency"`
}

func (m *UserMedal) Reset()                    { *m = UserMedal{} }
func (m *UserMedal) String() string            { return proto.CompactTextString(m) }
func (*UserMedal) ProtoMessage()               {}
func (*UserMedal) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{5} }

func (m *UserMedal) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserMedal) GetMedalId() uint32 {
	if m != nil {
		return m.MedalId
	}
	return 0
}

func (m *UserMedal) GetAwardTime() uint32 {
	if m != nil {
		return m.AwardTime
	}
	return 0
}

func (m *UserMedal) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *UserMedal) GetBuffExp() uint32 {
	if m != nil {
		return m.BuffExp
	}
	return 0
}

func (m *UserMedal) GetBuffCurrency() uint32 {
	if m != nil {
		return m.BuffCurrency
	}
	return 0
}

type UserTaillightMedal struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MedalId uint32 `protobuf:"varint,2,req,name=medal_id,json=medalId" json:"medal_id"`
}

func (m *UserTaillightMedal) Reset()                    { *m = UserTaillightMedal{} }
func (m *UserTaillightMedal) String() string            { return proto.CompactTextString(m) }
func (*UserTaillightMedal) ProtoMessage()               {}
func (*UserTaillightMedal) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{6} }

func (m *UserTaillightMedal) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserTaillightMedal) GetMedalId() uint32 {
	if m != nil {
		return m.MedalId
	}
	return 0
}

type GetUserMedalListReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserMedalListReq) Reset()                    { *m = GetUserMedalListReq{} }
func (m *GetUserMedalListReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserMedalListReq) ProtoMessage()               {}
func (*GetUserMedalListReq) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{7} }

func (m *GetUserMedalListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserMedalListResp struct {
	UserMedalList []*UserMedal `protobuf:"bytes,1,rep,name=user_medal_list,json=userMedalList" json:"user_medal_list,omitempty"`
}

func (m *GetUserMedalListResp) Reset()                    { *m = GetUserMedalListResp{} }
func (m *GetUserMedalListResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserMedalListResp) ProtoMessage()               {}
func (*GetUserMedalListResp) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{8} }

func (m *GetUserMedalListResp) GetUserMedalList() []*UserMedal {
	if m != nil {
		return m.UserMedalList
	}
	return nil
}

type BatGetUserMedalListReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatGetUserMedalListReq) Reset()                    { *m = BatGetUserMedalListReq{} }
func (m *BatGetUserMedalListReq) String() string            { return proto.CompactTextString(m) }
func (*BatGetUserMedalListReq) ProtoMessage()               {}
func (*BatGetUserMedalListReq) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{9} }

func (m *BatGetUserMedalListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatGetUserMedalListResp struct {
	UserMedalList []*UserMedal `protobuf:"bytes,1,rep,name=user_medal_list,json=userMedalList" json:"user_medal_list,omitempty"`
}

func (m *BatGetUserMedalListResp) Reset()                    { *m = BatGetUserMedalListResp{} }
func (m *BatGetUserMedalListResp) String() string            { return proto.CompactTextString(m) }
func (*BatGetUserMedalListResp) ProtoMessage()               {}
func (*BatGetUserMedalListResp) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{10} }

func (m *BatGetUserMedalListResp) GetUserMedalList() []*UserMedal {
	if m != nil {
		return m.UserMedalList
	}
	return nil
}

// 增加获取尾灯信息
type GetUserMedalListWhitTaillightReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserMedalListWhitTaillightReq) Reset()         { *m = GetUserMedalListWhitTaillightReq{} }
func (m *GetUserMedalListWhitTaillightReq) String() string { return proto.CompactTextString(m) }
func (*GetUserMedalListWhitTaillightReq) ProtoMessage()    {}
func (*GetUserMedalListWhitTaillightReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMedal, []int{11}
}

func (m *GetUserMedalListWhitTaillightReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserMedalListWhitTaillightResp struct {
	UserMedalList      []*UserMedal          `protobuf:"bytes,1,rep,name=user_medal_list,json=userMedalList" json:"user_medal_list,omitempty"`
	TaillightMedalList []*UserTaillightMedal `protobuf:"bytes,2,rep,name=taillight_medal_list,json=taillightMedalList" json:"taillight_medal_list,omitempty"`
}

func (m *GetUserMedalListWhitTaillightResp) Reset()         { *m = GetUserMedalListWhitTaillightResp{} }
func (m *GetUserMedalListWhitTaillightResp) String() string { return proto.CompactTextString(m) }
func (*GetUserMedalListWhitTaillightResp) ProtoMessage()    {}
func (*GetUserMedalListWhitTaillightResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMedal, []int{12}
}

func (m *GetUserMedalListWhitTaillightResp) GetUserMedalList() []*UserMedal {
	if m != nil {
		return m.UserMedalList
	}
	return nil
}

func (m *GetUserMedalListWhitTaillightResp) GetTaillightMedalList() []*UserTaillightMedal {
	if m != nil {
		return m.TaillightMedalList
	}
	return nil
}

type BatGetUserMedalListWhitTaillightReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatGetUserMedalListWhitTaillightReq) Reset()         { *m = BatGetUserMedalListWhitTaillightReq{} }
func (m *BatGetUserMedalListWhitTaillightReq) String() string { return proto.CompactTextString(m) }
func (*BatGetUserMedalListWhitTaillightReq) ProtoMessage()    {}
func (*BatGetUserMedalListWhitTaillightReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMedal, []int{13}
}

func (m *BatGetUserMedalListWhitTaillightReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatGetUserMedalListWhitTaillightResp struct {
	UserMedalList      []*UserMedal          `protobuf:"bytes,1,rep,name=user_medal_list,json=userMedalList" json:"user_medal_list,omitempty"`
	TaillightMedalList []*UserTaillightMedal `protobuf:"bytes,2,rep,name=taillight_medal_list,json=taillightMedalList" json:"taillight_medal_list,omitempty"`
}

func (m *BatGetUserMedalListWhitTaillightResp) Reset()         { *m = BatGetUserMedalListWhitTaillightResp{} }
func (m *BatGetUserMedalListWhitTaillightResp) String() string { return proto.CompactTextString(m) }
func (*BatGetUserMedalListWhitTaillightResp) ProtoMessage()    {}
func (*BatGetUserMedalListWhitTaillightResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMedal, []int{14}
}

func (m *BatGetUserMedalListWhitTaillightResp) GetUserMedalList() []*UserMedal {
	if m != nil {
		return m.UserMedalList
	}
	return nil
}

func (m *BatGetUserMedalListWhitTaillightResp) GetTaillightMedalList() []*UserTaillightMedal {
	if m != nil {
		return m.TaillightMedalList
	}
	return nil
}

type AwardMedalToUserReq struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MedalId     uint32 `protobuf:"varint,2,req,name=medal_id,json=medalId" json:"medal_id"`
	ExpireTime  uint32 `protobuf:"varint,3,req,name=expire_time,json=expireTime" json:"expire_time"`
	IsExtension bool   `protobuf:"varint,4,opt,name=is_extension,json=isExtension" json:"is_extension"`
}

func (m *AwardMedalToUserReq) Reset()                    { *m = AwardMedalToUserReq{} }
func (m *AwardMedalToUserReq) String() string            { return proto.CompactTextString(m) }
func (*AwardMedalToUserReq) ProtoMessage()               {}
func (*AwardMedalToUserReq) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{15} }

func (m *AwardMedalToUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AwardMedalToUserReq) GetMedalId() uint32 {
	if m != nil {
		return m.MedalId
	}
	return 0
}

func (m *AwardMedalToUserReq) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *AwardMedalToUserReq) GetIsExtension() bool {
	if m != nil {
		return m.IsExtension
	}
	return false
}

type AwardMedalToUserResp struct {
}

func (m *AwardMedalToUserResp) Reset()                    { *m = AwardMedalToUserResp{} }
func (m *AwardMedalToUserResp) String() string            { return proto.CompactTextString(m) }
func (*AwardMedalToUserResp) ProtoMessage()               {}
func (*AwardMedalToUserResp) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{16} }

// 回退用户勋章的时间
type ReduceUserMedalReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MedalId    uint32 `protobuf:"varint,2,req,name=medal_id,json=medalId" json:"medal_id"`
	ExpireTime uint32 `protobuf:"varint,3,req,name=expire_time,json=expireTime" json:"expire_time"`
}

func (m *ReduceUserMedalReq) Reset()                    { *m = ReduceUserMedalReq{} }
func (m *ReduceUserMedalReq) String() string            { return proto.CompactTextString(m) }
func (*ReduceUserMedalReq) ProtoMessage()               {}
func (*ReduceUserMedalReq) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{17} }

func (m *ReduceUserMedalReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReduceUserMedalReq) GetMedalId() uint32 {
	if m != nil {
		return m.MedalId
	}
	return 0
}

func (m *ReduceUserMedalReq) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type ReduceUserMedalResp struct {
}

func (m *ReduceUserMedalResp) Reset()                    { *m = ReduceUserMedalResp{} }
func (m *ReduceUserMedalResp) String() string            { return proto.CompactTextString(m) }
func (*ReduceUserMedalResp) ProtoMessage()               {}
func (*ReduceUserMedalResp) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{18} }

// 设置尾灯
type SetUserMedalTaillightReq struct {
	Uid                  uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	TaillightMedalidList []uint32 `protobuf:"varint,2,rep,name=taillight_medalid_list,json=taillightMedalidList" json:"taillight_medalid_list,omitempty"`
}

func (m *SetUserMedalTaillightReq) Reset()                    { *m = SetUserMedalTaillightReq{} }
func (m *SetUserMedalTaillightReq) String() string            { return proto.CompactTextString(m) }
func (*SetUserMedalTaillightReq) ProtoMessage()               {}
func (*SetUserMedalTaillightReq) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{19} }

func (m *SetUserMedalTaillightReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserMedalTaillightReq) GetTaillightMedalidList() []uint32 {
	if m != nil {
		return m.TaillightMedalidList
	}
	return nil
}

type SetUserMedalTaillightResp struct {
}

func (m *SetUserMedalTaillightResp) Reset()                    { *m = SetUserMedalTaillightResp{} }
func (m *SetUserMedalTaillightResp) String() string            { return proto.CompactTextString(m) }
func (*SetUserMedalTaillightResp) ProtoMessage()               {}
func (*SetUserMedalTaillightResp) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{20} }

// 删除勋章
type DeleteMedalReq struct {
	MedalId uint32 `protobuf:"varint,1,req,name=medal_id,json=medalId" json:"medal_id"`
}

func (m *DeleteMedalReq) Reset()                    { *m = DeleteMedalReq{} }
func (m *DeleteMedalReq) String() string            { return proto.CompactTextString(m) }
func (*DeleteMedalReq) ProtoMessage()               {}
func (*DeleteMedalReq) Descriptor() ([]byte, []int) { return fileDescriptorMedal, []int{21} }

func (m *DeleteMedalReq) GetMedalId() uint32 {
	if m != nil {
		return m.MedalId
	}
	return 0
}

func init() {
	proto.RegisterType((*PMedal)(nil), "Medal.PMedal")
	proto.RegisterType((*GetMedalConfigUpdateTimeReq)(nil), "Medal.GetMedalConfigUpdateTimeReq")
	proto.RegisterType((*GetMedalConfigUpdateTimeResp)(nil), "Medal.GetMedalConfigUpdateTimeResp")
	proto.RegisterType((*GetMedalConfigListReq)(nil), "Medal.GetMedalConfigListReq")
	proto.RegisterType((*GetMedalConfigListResp)(nil), "Medal.GetMedalConfigListResp")
	proto.RegisterType((*UserMedal)(nil), "Medal.UserMedal")
	proto.RegisterType((*UserTaillightMedal)(nil), "Medal.UserTaillightMedal")
	proto.RegisterType((*GetUserMedalListReq)(nil), "Medal.GetUserMedalListReq")
	proto.RegisterType((*GetUserMedalListResp)(nil), "Medal.GetUserMedalListResp")
	proto.RegisterType((*BatGetUserMedalListReq)(nil), "Medal.BatGetUserMedalListReq")
	proto.RegisterType((*BatGetUserMedalListResp)(nil), "Medal.BatGetUserMedalListResp")
	proto.RegisterType((*GetUserMedalListWhitTaillightReq)(nil), "Medal.GetUserMedalListWhitTaillightReq")
	proto.RegisterType((*GetUserMedalListWhitTaillightResp)(nil), "Medal.GetUserMedalListWhitTaillightResp")
	proto.RegisterType((*BatGetUserMedalListWhitTaillightReq)(nil), "Medal.BatGetUserMedalListWhitTaillightReq")
	proto.RegisterType((*BatGetUserMedalListWhitTaillightResp)(nil), "Medal.BatGetUserMedalListWhitTaillightResp")
	proto.RegisterType((*AwardMedalToUserReq)(nil), "Medal.AwardMedalToUserReq")
	proto.RegisterType((*AwardMedalToUserResp)(nil), "Medal.AwardMedalToUserResp")
	proto.RegisterType((*ReduceUserMedalReq)(nil), "Medal.ReduceUserMedalReq")
	proto.RegisterType((*ReduceUserMedalResp)(nil), "Medal.ReduceUserMedalResp")
	proto.RegisterType((*SetUserMedalTaillightReq)(nil), "Medal.SetUserMedalTaillightReq")
	proto.RegisterType((*SetUserMedalTaillightResp)(nil), "Medal.SetUserMedalTaillightResp")
	proto.RegisterType((*DeleteMedalReq)(nil), "Medal.DeleteMedalReq")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Medal service

type MedalClient interface {
	GetUserMedalList(ctx context.Context, in *GetUserMedalListReq, opts ...grpc.CallOption) (*GetUserMedalListResp, error)
	AwardMedalToUser(ctx context.Context, in *AwardMedalToUserReq, opts ...grpc.CallOption) (*AwardMedalToUserResp, error)
	GetMedalConfigUpdateTime(ctx context.Context, in *GetMedalConfigUpdateTimeReq, opts ...grpc.CallOption) (*GetMedalConfigUpdateTimeResp, error)
	GetMedalConfigList(ctx context.Context, in *GetMedalConfigListReq, opts ...grpc.CallOption) (*GetMedalConfigListResp, error)
	BatGetUserMedalList(ctx context.Context, in *BatGetUserMedalListReq, opts ...grpc.CallOption) (*BatGetUserMedalListResp, error)
	// 在获取勋章列表的基础上 获取用户的勋章尾灯设置
	GetUserMedalListWhitTaillight(ctx context.Context, in *GetUserMedalListWhitTaillightReq, opts ...grpc.CallOption) (*GetUserMedalListWhitTaillightResp, error)
	// 在批量获取勋章列表的基础上 获取用户的勋章尾灯设置
	BatGetUserMedalListWhitTaillight(ctx context.Context, in *BatGetUserMedalListWhitTaillightReq, opts ...grpc.CallOption) (*BatGetUserMedalListWhitTaillightResp, error)
	// 设置尾灯
	SetUserMedalTaillight(ctx context.Context, in *SetUserMedalTaillightReq, opts ...grpc.CallOption) (*SetUserMedalTaillightResp, error)
	AddMedal(ctx context.Context, in *PMedal, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateMedal(ctx context.Context, in *PMedal, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DeleteMedal(ctx context.Context, in *DeleteMedalReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ReduceUserMedal(ctx context.Context, in *ReduceUserMedalReq, opts ...grpc.CallOption) (*ReduceUserMedalResp, error)
}

type medalClient struct {
	cc *grpc.ClientConn
}

func NewMedalClient(cc *grpc.ClientConn) MedalClient {
	return &medalClient{cc}
}

func (c *medalClient) GetUserMedalList(ctx context.Context, in *GetUserMedalListReq, opts ...grpc.CallOption) (*GetUserMedalListResp, error) {
	out := new(GetUserMedalListResp)
	err := grpc.Invoke(ctx, "/Medal.Medal/GetUserMedalList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medalClient) AwardMedalToUser(ctx context.Context, in *AwardMedalToUserReq, opts ...grpc.CallOption) (*AwardMedalToUserResp, error) {
	out := new(AwardMedalToUserResp)
	err := grpc.Invoke(ctx, "/Medal.Medal/AwardMedalToUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medalClient) GetMedalConfigUpdateTime(ctx context.Context, in *GetMedalConfigUpdateTimeReq, opts ...grpc.CallOption) (*GetMedalConfigUpdateTimeResp, error) {
	out := new(GetMedalConfigUpdateTimeResp)
	err := grpc.Invoke(ctx, "/Medal.Medal/GetMedalConfigUpdateTime", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medalClient) GetMedalConfigList(ctx context.Context, in *GetMedalConfigListReq, opts ...grpc.CallOption) (*GetMedalConfigListResp, error) {
	out := new(GetMedalConfigListResp)
	err := grpc.Invoke(ctx, "/Medal.Medal/GetMedalConfigList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medalClient) BatGetUserMedalList(ctx context.Context, in *BatGetUserMedalListReq, opts ...grpc.CallOption) (*BatGetUserMedalListResp, error) {
	out := new(BatGetUserMedalListResp)
	err := grpc.Invoke(ctx, "/Medal.Medal/BatGetUserMedalList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medalClient) GetUserMedalListWhitTaillight(ctx context.Context, in *GetUserMedalListWhitTaillightReq, opts ...grpc.CallOption) (*GetUserMedalListWhitTaillightResp, error) {
	out := new(GetUserMedalListWhitTaillightResp)
	err := grpc.Invoke(ctx, "/Medal.Medal/GetUserMedalListWhitTaillight", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medalClient) BatGetUserMedalListWhitTaillight(ctx context.Context, in *BatGetUserMedalListWhitTaillightReq, opts ...grpc.CallOption) (*BatGetUserMedalListWhitTaillightResp, error) {
	out := new(BatGetUserMedalListWhitTaillightResp)
	err := grpc.Invoke(ctx, "/Medal.Medal/BatGetUserMedalListWhitTaillight", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medalClient) SetUserMedalTaillight(ctx context.Context, in *SetUserMedalTaillightReq, opts ...grpc.CallOption) (*SetUserMedalTaillightResp, error) {
	out := new(SetUserMedalTaillightResp)
	err := grpc.Invoke(ctx, "/Medal.Medal/SetUserMedalTaillight", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medalClient) AddMedal(ctx context.Context, in *PMedal, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Medal.Medal/AddMedal", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medalClient) UpdateMedal(ctx context.Context, in *PMedal, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Medal.Medal/UpdateMedal", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medalClient) DeleteMedal(ctx context.Context, in *DeleteMedalReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Medal.Medal/DeleteMedal", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medalClient) ReduceUserMedal(ctx context.Context, in *ReduceUserMedalReq, opts ...grpc.CallOption) (*ReduceUserMedalResp, error) {
	out := new(ReduceUserMedalResp)
	err := grpc.Invoke(ctx, "/Medal.Medal/ReduceUserMedal", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Medal service

type MedalServer interface {
	GetUserMedalList(context.Context, *GetUserMedalListReq) (*GetUserMedalListResp, error)
	AwardMedalToUser(context.Context, *AwardMedalToUserReq) (*AwardMedalToUserResp, error)
	GetMedalConfigUpdateTime(context.Context, *GetMedalConfigUpdateTimeReq) (*GetMedalConfigUpdateTimeResp, error)
	GetMedalConfigList(context.Context, *GetMedalConfigListReq) (*GetMedalConfigListResp, error)
	BatGetUserMedalList(context.Context, *BatGetUserMedalListReq) (*BatGetUserMedalListResp, error)
	// 在获取勋章列表的基础上 获取用户的勋章尾灯设置
	GetUserMedalListWhitTaillight(context.Context, *GetUserMedalListWhitTaillightReq) (*GetUserMedalListWhitTaillightResp, error)
	// 在批量获取勋章列表的基础上 获取用户的勋章尾灯设置
	BatGetUserMedalListWhitTaillight(context.Context, *BatGetUserMedalListWhitTaillightReq) (*BatGetUserMedalListWhitTaillightResp, error)
	// 设置尾灯
	SetUserMedalTaillight(context.Context, *SetUserMedalTaillightReq) (*SetUserMedalTaillightResp, error)
	AddMedal(context.Context, *PMedal) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateMedal(context.Context, *PMedal) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DeleteMedal(context.Context, *DeleteMedalReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ReduceUserMedal(context.Context, *ReduceUserMedalReq) (*ReduceUserMedalResp, error)
}

func RegisterMedalServer(s *grpc.Server, srv MedalServer) {
	s.RegisterService(&_Medal_serviceDesc, srv)
}

func _Medal_GetUserMedalList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMedalListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedalServer).GetUserMedalList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Medal.Medal/GetUserMedalList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedalServer).GetUserMedalList(ctx, req.(*GetUserMedalListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Medal_AwardMedalToUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AwardMedalToUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedalServer).AwardMedalToUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Medal.Medal/AwardMedalToUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedalServer).AwardMedalToUser(ctx, req.(*AwardMedalToUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Medal_GetMedalConfigUpdateTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMedalConfigUpdateTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedalServer).GetMedalConfigUpdateTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Medal.Medal/GetMedalConfigUpdateTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedalServer).GetMedalConfigUpdateTime(ctx, req.(*GetMedalConfigUpdateTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Medal_GetMedalConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMedalConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedalServer).GetMedalConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Medal.Medal/GetMedalConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedalServer).GetMedalConfigList(ctx, req.(*GetMedalConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Medal_BatGetUserMedalList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetUserMedalListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedalServer).BatGetUserMedalList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Medal.Medal/BatGetUserMedalList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedalServer).BatGetUserMedalList(ctx, req.(*BatGetUserMedalListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Medal_GetUserMedalListWhitTaillight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMedalListWhitTaillightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedalServer).GetUserMedalListWhitTaillight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Medal.Medal/GetUserMedalListWhitTaillight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedalServer).GetUserMedalListWhitTaillight(ctx, req.(*GetUserMedalListWhitTaillightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Medal_BatGetUserMedalListWhitTaillight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetUserMedalListWhitTaillightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedalServer).BatGetUserMedalListWhitTaillight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Medal.Medal/BatGetUserMedalListWhitTaillight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedalServer).BatGetUserMedalListWhitTaillight(ctx, req.(*BatGetUserMedalListWhitTaillightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Medal_SetUserMedalTaillight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserMedalTaillightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedalServer).SetUserMedalTaillight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Medal.Medal/SetUserMedalTaillight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedalServer).SetUserMedalTaillight(ctx, req.(*SetUserMedalTaillightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Medal_AddMedal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PMedal)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedalServer).AddMedal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Medal.Medal/AddMedal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedalServer).AddMedal(ctx, req.(*PMedal))
	}
	return interceptor(ctx, in, info, handler)
}

func _Medal_UpdateMedal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PMedal)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedalServer).UpdateMedal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Medal.Medal/UpdateMedal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedalServer).UpdateMedal(ctx, req.(*PMedal))
	}
	return interceptor(ctx, in, info, handler)
}

func _Medal_DeleteMedal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMedalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedalServer).DeleteMedal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Medal.Medal/DeleteMedal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedalServer).DeleteMedal(ctx, req.(*DeleteMedalReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Medal_ReduceUserMedal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReduceUserMedalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedalServer).ReduceUserMedal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Medal.Medal/ReduceUserMedal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedalServer).ReduceUserMedal(ctx, req.(*ReduceUserMedalReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Medal_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Medal.Medal",
	HandlerType: (*MedalServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserMedalList",
			Handler:    _Medal_GetUserMedalList_Handler,
		},
		{
			MethodName: "AwardMedalToUser",
			Handler:    _Medal_AwardMedalToUser_Handler,
		},
		{
			MethodName: "GetMedalConfigUpdateTime",
			Handler:    _Medal_GetMedalConfigUpdateTime_Handler,
		},
		{
			MethodName: "GetMedalConfigList",
			Handler:    _Medal_GetMedalConfigList_Handler,
		},
		{
			MethodName: "BatGetUserMedalList",
			Handler:    _Medal_BatGetUserMedalList_Handler,
		},
		{
			MethodName: "GetUserMedalListWhitTaillight",
			Handler:    _Medal_GetUserMedalListWhitTaillight_Handler,
		},
		{
			MethodName: "BatGetUserMedalListWhitTaillight",
			Handler:    _Medal_BatGetUserMedalListWhitTaillight_Handler,
		},
		{
			MethodName: "SetUserMedalTaillight",
			Handler:    _Medal_SetUserMedalTaillight_Handler,
		},
		{
			MethodName: "AddMedal",
			Handler:    _Medal_AddMedal_Handler,
		},
		{
			MethodName: "UpdateMedal",
			Handler:    _Medal_UpdateMedal_Handler,
		},
		{
			MethodName: "DeleteMedal",
			Handler:    _Medal_DeleteMedal_Handler,
		},
		{
			MethodName: "ReduceUserMedal",
			Handler:    _Medal_ReduceUserMedal_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/medalsvr/medal.proto",
}

func (m *PMedal) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PMedal) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.MedalId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMedal(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	if len(m.Desc) > 0 {
		for _, s := range m.Desc {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x22
	i++
	i = encodeVarintMedal(dAtA, i, uint64(len(m.Icon)))
	i += copy(dAtA[i:], m.Icon)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintMedal(dAtA, i, uint64(len(m.BorderIcon)))
	i += copy(dAtA[i:], m.BorderIcon)
	dAtA[i] = 0x32
	i++
	i = encodeVarintMedal(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x38
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.ExpireTime))
	dAtA[i] = 0x40
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.BuffExpRate))
	dAtA[i] = 0x48
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.BuffCurrencyRate))
	dAtA[i] = 0x52
	i++
	i = encodeVarintMedal(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x5a
	i++
	i = encodeVarintMedal(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x62
	i++
	i = encodeVarintMedal(dAtA, i, uint64(len(m.TitleIcon)))
	i += copy(dAtA[i:], m.TitleIcon)
	dAtA[i] = 0x68
	i++
	if m.IsAllowSetTaillight {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetMedalConfigUpdateTimeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMedalConfigUpdateTimeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetMedalConfigUpdateTimeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMedalConfigUpdateTimeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.UpdateTime))
	return i, nil
}

func (m *GetMedalConfigListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMedalConfigListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetMedalConfigListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMedalConfigListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MedalList) > 0 {
		for _, msg := range m.MedalList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMedal(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UserMedal) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserMedal) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.MedalId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.AwardTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.ExpireTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.BuffExp))
	dAtA[i] = 0x30
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.BuffCurrency))
	return i, nil
}

func (m *UserTaillightMedal) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserTaillightMedal) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.MedalId))
	return i, nil
}

func (m *GetUserMedalListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMedalListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserMedalListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMedalListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserMedalList) > 0 {
		for _, msg := range m.UserMedalList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMedal(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatGetUserMedalListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetUserMedalListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintMedal(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatGetUserMedalListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetUserMedalListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserMedalList) > 0 {
		for _, msg := range m.UserMedalList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMedal(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserMedalListWhitTaillightReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMedalListWhitTaillightReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserMedalListWhitTaillightResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMedalListWhitTaillightResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserMedalList) > 0 {
		for _, msg := range m.UserMedalList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMedal(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.TaillightMedalList) > 0 {
		for _, msg := range m.TaillightMedalList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMedal(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatGetUserMedalListWhitTaillightReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetUserMedalListWhitTaillightReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintMedal(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatGetUserMedalListWhitTaillightResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetUserMedalListWhitTaillightResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserMedalList) > 0 {
		for _, msg := range m.UserMedalList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMedal(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.TaillightMedalList) > 0 {
		for _, msg := range m.TaillightMedalList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMedal(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AwardMedalToUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AwardMedalToUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.MedalId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.ExpireTime))
	dAtA[i] = 0x20
	i++
	if m.IsExtension {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *AwardMedalToUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AwardMedalToUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ReduceUserMedalReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReduceUserMedalReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.MedalId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.ExpireTime))
	return i, nil
}

func (m *ReduceUserMedalResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReduceUserMedalResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SetUserMedalTaillightReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserMedalTaillightReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.Uid))
	if len(m.TaillightMedalidList) > 0 {
		for _, num := range m.TaillightMedalidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintMedal(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *SetUserMedalTaillightResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserMedalTaillightResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DeleteMedalReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteMedalReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMedal(dAtA, i, uint64(m.MedalId))
	return i, nil
}

func encodeFixed64Medal(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Medal(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintMedal(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *PMedal) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMedal(uint64(m.MedalId))
	l = len(m.Name)
	n += 1 + l + sovMedal(uint64(l))
	if len(m.Desc) > 0 {
		for _, s := range m.Desc {
			l = len(s)
			n += 1 + l + sovMedal(uint64(l))
		}
	}
	l = len(m.Icon)
	n += 1 + l + sovMedal(uint64(l))
	l = len(m.BorderIcon)
	n += 1 + l + sovMedal(uint64(l))
	l = len(m.Url)
	n += 1 + l + sovMedal(uint64(l))
	n += 1 + sovMedal(uint64(m.ExpireTime))
	n += 1 + sovMedal(uint64(m.BuffExpRate))
	n += 1 + sovMedal(uint64(m.BuffCurrencyRate))
	l = len(m.Title)
	n += 1 + l + sovMedal(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovMedal(uint64(l))
	l = len(m.TitleIcon)
	n += 1 + l + sovMedal(uint64(l))
	n += 2
	return n
}

func (m *GetMedalConfigUpdateTimeReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetMedalConfigUpdateTimeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMedal(uint64(m.UpdateTime))
	return n
}

func (m *GetMedalConfigListReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetMedalConfigListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MedalList) > 0 {
		for _, e := range m.MedalList {
			l = e.Size()
			n += 1 + l + sovMedal(uint64(l))
		}
	}
	return n
}

func (m *UserMedal) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMedal(uint64(m.Uid))
	n += 1 + sovMedal(uint64(m.MedalId))
	n += 1 + sovMedal(uint64(m.AwardTime))
	n += 1 + sovMedal(uint64(m.ExpireTime))
	n += 1 + sovMedal(uint64(m.BuffExp))
	n += 1 + sovMedal(uint64(m.BuffCurrency))
	return n
}

func (m *UserTaillightMedal) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMedal(uint64(m.Uid))
	n += 1 + sovMedal(uint64(m.MedalId))
	return n
}

func (m *GetUserMedalListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMedal(uint64(m.Uid))
	return n
}

func (m *GetUserMedalListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserMedalList) > 0 {
		for _, e := range m.UserMedalList {
			l = e.Size()
			n += 1 + l + sovMedal(uint64(l))
		}
	}
	return n
}

func (m *BatGetUserMedalListReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovMedal(uint64(e))
		}
	}
	return n
}

func (m *BatGetUserMedalListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserMedalList) > 0 {
		for _, e := range m.UserMedalList {
			l = e.Size()
			n += 1 + l + sovMedal(uint64(l))
		}
	}
	return n
}

func (m *GetUserMedalListWhitTaillightReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMedal(uint64(m.Uid))
	return n
}

func (m *GetUserMedalListWhitTaillightResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserMedalList) > 0 {
		for _, e := range m.UserMedalList {
			l = e.Size()
			n += 1 + l + sovMedal(uint64(l))
		}
	}
	if len(m.TaillightMedalList) > 0 {
		for _, e := range m.TaillightMedalList {
			l = e.Size()
			n += 1 + l + sovMedal(uint64(l))
		}
	}
	return n
}

func (m *BatGetUserMedalListWhitTaillightReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovMedal(uint64(e))
		}
	}
	return n
}

func (m *BatGetUserMedalListWhitTaillightResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserMedalList) > 0 {
		for _, e := range m.UserMedalList {
			l = e.Size()
			n += 1 + l + sovMedal(uint64(l))
		}
	}
	if len(m.TaillightMedalList) > 0 {
		for _, e := range m.TaillightMedalList {
			l = e.Size()
			n += 1 + l + sovMedal(uint64(l))
		}
	}
	return n
}

func (m *AwardMedalToUserReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMedal(uint64(m.Uid))
	n += 1 + sovMedal(uint64(m.MedalId))
	n += 1 + sovMedal(uint64(m.ExpireTime))
	n += 2
	return n
}

func (m *AwardMedalToUserResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ReduceUserMedalReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMedal(uint64(m.Uid))
	n += 1 + sovMedal(uint64(m.MedalId))
	n += 1 + sovMedal(uint64(m.ExpireTime))
	return n
}

func (m *ReduceUserMedalResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SetUserMedalTaillightReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMedal(uint64(m.Uid))
	if len(m.TaillightMedalidList) > 0 {
		for _, e := range m.TaillightMedalidList {
			n += 1 + sovMedal(uint64(e))
		}
	}
	return n
}

func (m *SetUserMedalTaillightResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DeleteMedalReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMedal(uint64(m.MedalId))
	return n
}

func sovMedal(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozMedal(x uint64) (n int) {
	return sovMedal(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *PMedal) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PMedal: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PMedal: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalId", wireType)
			}
			m.MedalId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MedalId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = append(m.Desc, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Icon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Icon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BorderIcon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BorderIcon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuffExpRate", wireType)
			}
			m.BuffExpRate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuffExpRate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuffCurrencyRate", wireType)
			}
			m.BuffCurrencyRate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuffCurrencyRate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000200)
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TitleIcon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TitleIcon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAllowSetTaillight", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAllowSetTaillight = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("medal_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("icon")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("border_icon")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("expire_time")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("buff_exp_rate")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("buff_currency_rate")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMedalConfigUpdateTimeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMedalConfigUpdateTimeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMedalConfigUpdateTimeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMedalConfigUpdateTimeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMedalConfigUpdateTimeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMedalConfigUpdateTimeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("update_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMedalConfigListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMedalConfigListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMedalConfigListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMedalConfigListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMedalConfigListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMedalConfigListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MedalList = append(m.MedalList, &PMedal{})
			if err := m.MedalList[len(m.MedalList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserMedal) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserMedal: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserMedal: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalId", wireType)
			}
			m.MedalId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MedalId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardTime", wireType)
			}
			m.AwardTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuffExp", wireType)
			}
			m.BuffExp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuffExp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuffCurrency", wireType)
			}
			m.BuffCurrency = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuffCurrency |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("medal_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("award_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("expire_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("buff_exp")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("buff_currency")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserTaillightMedal) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserTaillightMedal: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserTaillightMedal: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalId", wireType)
			}
			m.MedalId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MedalId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("medal_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMedalListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMedalListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMedalListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMedalListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMedalListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMedalListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMedalList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserMedalList = append(m.UserMedalList, &UserMedal{})
			if err := m.UserMedalList[len(m.UserMedalList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetUserMedalListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetUserMedalListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetUserMedalListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMedal
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMedal
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMedal
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMedal
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetUserMedalListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetUserMedalListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetUserMedalListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMedalList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserMedalList = append(m.UserMedalList, &UserMedal{})
			if err := m.UserMedalList[len(m.UserMedalList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMedalListWhitTaillightReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMedalListWhitTaillightReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMedalListWhitTaillightReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMedalListWhitTaillightResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMedalListWhitTaillightResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMedalListWhitTaillightResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMedalList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserMedalList = append(m.UserMedalList, &UserMedal{})
			if err := m.UserMedalList[len(m.UserMedalList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TaillightMedalList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TaillightMedalList = append(m.TaillightMedalList, &UserTaillightMedal{})
			if err := m.TaillightMedalList[len(m.TaillightMedalList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetUserMedalListWhitTaillightReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetUserMedalListWhitTaillightReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetUserMedalListWhitTaillightReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMedal
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMedal
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMedal
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMedal
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetUserMedalListWhitTaillightResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetUserMedalListWhitTaillightResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetUserMedalListWhitTaillightResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMedalList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserMedalList = append(m.UserMedalList, &UserMedal{})
			if err := m.UserMedalList[len(m.UserMedalList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TaillightMedalList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMedal
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TaillightMedalList = append(m.TaillightMedalList, &UserTaillightMedal{})
			if err := m.TaillightMedalList[len(m.TaillightMedalList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AwardMedalToUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AwardMedalToUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AwardMedalToUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalId", wireType)
			}
			m.MedalId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MedalId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsExtension", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsExtension = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("medal_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("expire_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AwardMedalToUserResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AwardMedalToUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AwardMedalToUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReduceUserMedalReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReduceUserMedalReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReduceUserMedalReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalId", wireType)
			}
			m.MedalId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MedalId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("medal_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("expire_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReduceUserMedalResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReduceUserMedalResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReduceUserMedalResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserMedalTaillightReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserMedalTaillightReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserMedalTaillightReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMedal
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TaillightMedalidList = append(m.TaillightMedalidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMedal
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMedal
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMedal
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TaillightMedalidList = append(m.TaillightMedalidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TaillightMedalidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserMedalTaillightResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserMedalTaillightResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserMedalTaillightResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteMedalReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteMedalReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteMedalReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalId", wireType)
			}
			m.MedalId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MedalId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMedal(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMedal
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("medal_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipMedal(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowMedal
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMedal
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthMedal
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowMedal
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipMedal(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthMedal = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowMedal   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/medalsvr/medal.proto", fileDescriptorMedal) }

var fileDescriptorMedal = []byte{
	// 1145 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x57, 0x5f, 0x4f, 0x23, 0x55,
	0x14, 0x67, 0x3a, 0x05, 0xda, 0x53, 0xea, 0x92, 0x0b, 0x74, 0x87, 0x01, 0xca, 0x38, 0xac, 0xa1,
	0xab, 0x52, 0x22, 0xfa, 0xa0, 0x84, 0x10, 0xe9, 0x8a, 0x9b, 0xcd, 0xba, 0x09, 0x19, 0x20, 0x3e,
	0x99, 0xc9, 0xd0, 0xb9, 0x2c, 0x37, 0x3b, 0xed, 0x8c, 0x73, 0xef, 0xec, 0xc2, 0x9b, 0x2f, 0x1a,
	0x35, 0x31, 0x31, 0xbe, 0x6a, 0x7c, 0xe2, 0xc9, 0xc4, 0xef, 0xb1, 0x8f, 0x9a, 0xf8, 0x68, 0x8c,
	0xc1, 0x17, 0x3e, 0x86, 0xb9, 0x77, 0xfe, 0x74, 0xda, 0xce, 0xb4, 0x64, 0x89, 0x89, 0x6f, 0xed,
	0x39, 0xbf, 0xf3, 0xff, 0x77, 0xce, 0x6d, 0x41, 0xa1, 0x7e, 0x7b, 0xb3, 0x83, 0x6d, 0xcb, 0xa1,
	0xcf, 0xfd, 0xf0, 0x43, 0xd3, 0xf3, 0x5d, 0xe6, 0xa2, 0xc9, 0x27, 0xfc, 0x8b, 0x7a, 0xaf, 0xed,
	0x76, 0x3a, 0x6e, 0x77, 0x93, 0x39, 0xcf, 0x3d, 0xd2, 0x7e, 0xe6, 0xe0, 0x4d, 0xfa, 0xec, 0x24,
	0x20, 0x0e, 0x23, 0x5d, 0x76, 0xe1, 0xe1, 0x10, 0xac, 0xff, 0x2e, 0xc3, 0xd4, 0x81, 0x30, 0x40,
	0xab, 0x50, 0x12, 0x6e, 0x4c, 0x62, 0x2b, 0x92, 0x56, 0x68, 0x54, 0x5b, 0xc5, 0x97, 0x7f, 0xad,
	0x4e, 0x18, 0xd3, 0x42, 0xfa, 0xc8, 0x46, 0x0a, 0x14, 0xbb, 0x56, 0x07, 0x2b, 0x05, 0xad, 0xd0,
	0x28, 0x47, 0x4a, 0x21, 0x41, 0x08, 0x8a, 0x36, 0xa6, 0x6d, 0x45, 0xd6, 0xe4, 0x46, 0xd9, 0x10,
	0x9f, 0x39, 0x9a, 0xb4, 0xdd, 0xae, 0x52, 0x4c, 0xa3, 0xb9, 0x04, 0xbd, 0x01, 0x95, 0x13, 0xd7,
	0xb7, 0xb1, 0x6f, 0x0a, 0xc0, 0x64, 0x0a, 0x00, 0xa1, 0xe2, 0x11, 0x87, 0xd5, 0x40, 0x0e, 0x7c,
	0x47, 0x99, 0x4a, 0xa9, 0xb9, 0x80, 0x9b, 0xe3, 0x73, 0x8f, 0xf8, 0xd8, 0x64, 0xa4, 0x83, 0x95,
	0xe9, 0x54, 0xaa, 0x10, 0x2a, 0x8e, 0x48, 0x07, 0xa3, 0x06, 0x54, 0x4f, 0x82, 0xd3, 0x53, 0x13,
	0x9f, 0x7b, 0xa6, 0x6f, 0x31, 0xac, 0x94, 0x52, 0xc0, 0x0a, 0x57, 0xed, 0x9f, 0x7b, 0x86, 0xc5,
	0x30, 0xda, 0x02, 0x24, 0x90, 0xed, 0xc0, 0xf7, 0x71, 0xb7, 0x7d, 0x11, 0xc2, 0xcb, 0x29, 0xf8,
	0x2c, 0xd7, 0x3f, 0x88, 0xd4, 0xc2, 0x46, 0x85, 0x49, 0x46, 0x98, 0x83, 0x15, 0x48, 0xa5, 0x17,
	0x8a, 0x50, 0x1d, 0xa6, 0xdb, 0x6e, 0x97, 0xe1, 0x2e, 0x53, 0x2a, 0x29, 0x6d, 0x2c, 0x44, 0x6b,
	0x00, 0x02, 0x18, 0x96, 0x3f, 0xa3, 0x49, 0x09, 0xa4, 0x2c, 0xe4, 0xa2, 0xfa, 0x0f, 0xa0, 0x46,
	0xa8, 0x69, 0x39, 0x8e, 0xfb, 0xc2, 0xa4, 0x98, 0x99, 0xcc, 0x22, 0x8e, 0x43, 0x9e, 0x9e, 0x31,
	0xa5, 0xaa, 0x49, 0x8d, 0x52, 0x64, 0x30, 0x47, 0xe8, 0x1e, 0x87, 0x1c, 0x62, 0x76, 0x14, 0x03,
	0xf4, 0x15, 0x58, 0x7a, 0x88, 0x99, 0x18, 0xea, 0x03, 0xb7, 0x7b, 0x4a, 0x9e, 0x1e, 0x7b, 0xb6,
	0xc5, 0x44, 0x57, 0x0c, 0xfc, 0xb9, 0xbe, 0x0f, 0xcb, 0xf9, 0x6a, 0xea, 0xf1, 0xfe, 0x06, 0x42,
	0x12, 0xf6, 0x37, 0x4d, 0x05, 0x08, 0x12, 0xa8, 0x7e, 0x17, 0x16, 0xfa, 0xdd, 0x7c, 0x42, 0x28,
	0xe3, 0xfe, 0x3f, 0x86, 0x5a, 0x96, 0x82, 0x7a, 0xe8, 0x6d, 0x80, 0x90, 0x61, 0x0e, 0xa1, 0x4c,
	0x91, 0x34, 0xb9, 0x51, 0xd9, 0xaa, 0x36, 0x05, 0xb2, 0x19, 0x92, 0xd0, 0x28, 0x0b, 0x00, 0xb7,
	0xd0, 0xff, 0x94, 0xa0, 0x7c, 0x4c, 0xb1, 0x1f, 0xb2, 0x93, 0xb3, 0x61, 0x80, 0x98, 0x5c, 0xd0,
	0xc7, 0xda, 0x42, 0x16, 0x6b, 0xd7, 0x00, 0xac, 0x17, 0x96, 0x6f, 0x87, 0xd5, 0xc8, 0x29, 0x48,
	0x59, 0xc8, 0x05, 0x59, 0x06, 0x38, 0x55, 0xcc, 0xe1, 0xd4, 0x2a, 0x94, 0x62, 0x4e, 0x09, 0xda,
	0x26, 0xc1, 0x22, 0x3a, 0xa1, 0xfb, 0x11, 0xe9, 0x62, 0x2a, 0x09, 0xf6, 0xc6, 0xa8, 0x99, 0x34,
	0x8b, 0xf4, 0x27, 0x80, 0x78, 0x75, 0xc9, 0xd8, 0x6e, 0x57, 0xa6, 0xbe, 0x01, 0x73, 0x0f, 0x31,
	0x4b, 0xfa, 0x15, 0x0d, 0x23, 0xcf, 0x9f, 0x7e, 0x00, 0xf3, 0xc3, 0x70, 0xea, 0xa1, 0xf7, 0xe1,
	0x4e, 0x40, 0xb1, 0x6f, 0x0e, 0xcd, 0x69, 0x36, 0x9a, 0x53, 0x62, 0x62, 0x54, 0x83, 0xb4, 0xb5,
	0xfe, 0x2e, 0xd4, 0x5a, 0x16, 0xcb, 0xca, 0x61, 0x11, 0x4a, 0x01, 0xb1, 0x7b, 0xce, 0xaa, 0xc6,
	0x74, 0x40, 0x6c, 0x61, 0x74, 0x08, 0x77, 0x33, 0x8d, 0x6e, 0x95, 0xc9, 0x36, 0x68, 0x83, 0x1e,
	0x3f, 0x3d, 0x23, 0xbd, 0x05, 0x19, 0xd5, 0x97, 0x5f, 0x24, 0x78, 0x7d, 0x8c, 0xf1, 0x6d, 0x72,
	0x43, 0x8f, 0x61, 0x3e, 0xd9, 0xe4, 0xb4, 0x79, 0x41, 0x98, 0x2f, 0xa6, 0xcc, 0xfb, 0x89, 0x61,
	0x20, 0xd6, 0xf7, 0x5d, 0x14, 0xfa, 0x21, 0xac, 0x65, 0x74, 0x6f, 0xa8, 0xd6, 0x11, 0xfd, 0xff,
	0x55, 0x82, 0x7b, 0xe3, 0x5d, 0xfc, 0x7f, 0x2a, 0xfe, 0x59, 0x82, 0xb9, 0x3d, 0xbe, 0xb5, 0x42,
	0x74, 0xe4, 0x72, 0xb3, 0x11, 0xe3, 0x1c, 0x7f, 0x1d, 0x06, 0x16, 0x5f, 0xce, 0x59, 0xfc, 0x75,
	0x98, 0x21, 0xd4, 0xc4, 0xe7, 0x0c, 0x77, 0x29, 0x11, 0x8f, 0x5a, 0xef, 0x06, 0x57, 0x08, 0xdd,
	0x8f, 0x15, 0x7a, 0x0d, 0xe6, 0x87, 0xf3, 0xa3, 0x9e, 0xce, 0x00, 0x19, 0xd8, 0x0e, 0xda, 0xb8,
	0xd7, 0xa7, 0xff, 0x3e, 0x6d, 0x7d, 0x01, 0xe6, 0x86, 0xa2, 0x52, 0x4f, 0x3f, 0x03, 0xe5, 0x30,
	0x35, 0xf1, 0x9b, 0x2c, 0x06, 0x7a, 0x0f, 0x6a, 0x03, 0x63, 0x8c, 0x29, 0x55, 0x10, 0x94, 0x9a,
	0xef, 0x9f, 0x56, 0xc4, 0xaf, 0x25, 0x58, 0xcc, 0x89, 0x44, 0x3d, 0xfd, 0x1d, 0x78, 0xed, 0x23,
	0xec, 0x60, 0x86, 0x93, 0x7e, 0x8c, 0xfb, 0x09, 0xb2, 0xf5, 0x53, 0x05, 0xc2, 0x9f, 0x37, 0xe8,
	0x14, 0x66, 0x07, 0x59, 0x8b, 0xd4, 0x88, 0x4c, 0x19, 0x47, 0x48, 0x5d, 0xca, 0xd5, 0x51, 0x4f,
	0x5f, 0xfc, 0xe2, 0xf2, 0x5a, 0x96, 0xbe, 0xbd, 0xbc, 0x96, 0x0b, 0xc1, 0xf6, 0x0f, 0x97, 0xd7,
	0x72, 0x69, 0x23, 0xd0, 0x76, 0x02, 0x62, 0xef, 0xa2, 0x6f, 0x24, 0x98, 0x1d, 0x9c, 0x68, 0x12,
	0x28, 0x83, 0x8a, 0x49, 0xa0, 0x4c, 0x1a, 0xec, 0xf0, 0x40, 0x05, 0x1e, 0x68, 0x2a, 0xd8, 0xee,
	0x6c, 0x33, 0x11, 0x6c, 0x3d, 0x0e, 0xa6, 0x6d, 0x74, 0xb4, 0x1d, 0x51, 0xaa, 0x26, 0xbe, 0x31,
	0x6d, 0x27, 0x1c, 0xa6, 0xc6, 0xa7, 0xbc, 0x8b, 0x18, 0x28, 0x79, 0x2f, 0x37, 0xd2, 0x7b, 0xf5,
	0xe5, 0xbd, 0xfc, 0xea, 0xda, 0x58, 0x0c, 0xf5, 0xf4, 0x3b, 0x3c, 0x45, 0x99, 0xa7, 0x38, 0xc1,
	0x93, 0x9b, 0x40, 0x27, 0x80, 0x86, 0xdf, 0x73, 0xb4, 0x9c, 0xe9, 0x2b, 0xee, 0xf6, 0xca, 0x08,
	0x6d, 0x1c, 0xa3, 0x98, 0x8a, 0xc1, 0x60, 0x2e, 0xe3, 0x0c, 0xa1, 0xd8, 0x4d, 0xf6, 0xc3, 0xa2,
	0xd6, 0x47, 0xa9, 0xa9, 0xa7, 0xd7, 0x79, 0x98, 0xc9, 0xd4, 0x58, 0xab, 0x51, 0xa7, 0x35, 0x4e,
	0xdc, 0x5d, 0xf4, 0x9d, 0x04, 0x2b, 0x23, 0x4f, 0x1f, 0x5a, 0xcf, 0x61, 0xcd, 0xe0, 0x8d, 0x55,
	0x1b, 0x37, 0x03, 0xc6, 0x5c, 0x9b, 0xca, 0xe4, 0xda, 0x8f, 0x12, 0x68, 0xe3, 0xae, 0x31, 0x7a,
	0x33, 0xbf, 0xe8, 0xa1, 0xac, 0xde, 0xba, 0x31, 0x36, 0xee, 0xd6, 0x74, 0x7e, 0xb7, 0xbe, 0x94,
	0x60, 0x21, 0x73, 0x99, 0xd1, 0x6a, 0x14, 0x26, 0xef, 0xa8, 0xa8, 0xda, 0x68, 0x00, 0xf5, 0xf4,
	0xfb, 0x3c, 0x78, 0x89, 0x07, 0x2f, 0xf2, 0xc5, 0xe0, 0xe1, 0x6b, 0xd9, 0x6b, 0x81, 0x0c, 0x28,
	0xed, 0xd9, 0xe1, 0x66, 0xa1, 0xfe, 0x5f, 0x8f, 0xea, 0x72, 0x33, 0xf9, 0xb7, 0xd3, 0x3c, 0x7c,
	0xdc, 0x0a, 0xff, 0xed, 0xec, 0x77, 0x3c, 0x76, 0x61, 0x1e, 0xb4, 0xc2, 0xce, 0x97, 0x33, 0x3b,
	0x7f, 0x0c, 0x95, 0x70, 0x0d, 0x5e, 0xd5, 0x2d, 0x64, 0xba, 0xfd, 0x0c, 0x2a, 0xa9, 0x0b, 0x87,
	0x16, 0x22, 0xb7, 0xfd, 0x57, 0xef, 0x26, 0xee, 0x2b, 0x99, 0xee, 0xbf, 0x92, 0xe0, 0xce, 0xc0,
	0x7d, 0x47, 0xf1, 0x83, 0x3a, 0xfc, 0xda, 0xa8, 0x6a, 0x9e, 0x2a, 0x3e, 0x4c, 0x33, 0xaf, 0x78,
	0x98, 0xd4, 0xa9, 0xaf, 0x2f, 0xaf, 0xe5, 0x3f, 0x82, 0xd6, 0xec, 0xcb, 0xab, 0xba, 0xf4, 0xdb,
	0x55, 0x5d, 0xfa, 0xfb, 0xaa, 0x2e, 0x7d, 0xff, 0x4f, 0x7d, 0xe2, 0xdf, 0x00, 0x00, 0x00, 0xff,
	0xff, 0x07, 0x04, 0x33, 0xc0, 0xa7, 0x0e, 0x00, 0x00,
}
