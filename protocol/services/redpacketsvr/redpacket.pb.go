// Code generated by protoc-gen-gogo.
// source: services/redpacket/redpacket.proto
// DO NOT EDIT!

/*
	Package redpacket is a generated protocol buffer package.

	It is generated from these files:
		services/redpacket/redpacket.proto

	It has these top-level messages:
		RedPacketActivityBase
		RedPacketActivityStage
		RedPacketGiftBase
		RedPacketGiftExter_Medal
		RedPacketGiftExter_RedDiamond
		RedPacketGiftExter_Debris
		GetGiftsByIDListReq
		GetGiftsByIDListResp
		GetActivityByIdReq
		GetActivityByIdResp
		RedPacketGift_Stock_ConfInf
		RedPacketGift_Stage_ConfInf
		RedPacketActivity_Stage_ConfInf
		CreateOrUpdateGiftReq
		CreateOrUpdateGiftResp
		CreateRedPacketActivityReq
		CreateRedPacketActivityResp
		ModifyRedPacketActivityReq
		ModifyRedPacketActivityResp
		DelRedPacketActivityReq
		DelRedPacketActivityResp
		CreateRedPacketStageReq
		CreateRedPacketStageResp
		ModifyRedPacketStageReq
		ModifyRedPacketStageResp
		ModifyRedPacketStageGiftConfReq
		ModifyRedPacketStageGiftConfResp
		DelRedPacketStageReq
		DelRedPacketStageResp
		GetRedPacketStageDetailReq
		GetRedPacketStageDetailResp
		AddConfigGiftStoreReq
		AddConfigGiftStoreResp
		SetConfigGiftStoreSizeReq
		SetConfigGiftStoreSizeResp
		GetAllConfigStoreGiftListReq
		GetAllConfigStoreGiftListResp
		DistributGiftToActivityReq
		DistributGiftToActivityResp
		GetActivityDistributedGiftListReq
		GetActivityDistributedGiftListResp
		GetAllConfigRedPacketActivityListReq
		GetAllConfigRedPacketActivityListResp
		GetRedPacketActivityAllConfigStageListReq
		GetRedPacketActivityAllConfigStageListResp
		RedPacketActivityDetail
		GetAllActivityDetailListReq
		GetAllActivityDetailListResp
		UserRedPacketGift
		DrawLotteryReq
		DrawLotteryResp
		MassGuildBuffReq
		MassGuildBuffResp
		GetGuildLotteryBuffReq
		GetGuildLotteryBuffResp
		GetUserLotteryGiftListReq
		GetUserLotteryGiftListResp
		SetUserVoucherGiftInfoReq
		SetUserVoucherGiftInfoResp
		GuildRankStatInfo
		GuildBuffTopN
		BatchGetGuildBuffTopNReq
		BatchGetGuildBuffTopNResp
		AsyncUnityCalcNotify
*/
package redpacket

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 红包活动事件的阶段
// 阶段类型
type REDPACKET_STAGE_TYPE int32

const (
	REDPACKET_STAGE_TYPE_REDPACKET_STAGE_DEFAULT        REDPACKET_STAGE_TYPE = 1
	REDPACKET_STAGE_TYPE_REDPACKET_STAGE_LOTTERY        REDPACKET_STAGE_TYPE = 2
	REDPACKET_STAGE_TYPE_REDPACKET_STAGE_MASS_GUILDBUFF REDPACKET_STAGE_TYPE = 3
	REDPACKET_STAGE_TYPE_REDPACKET_STAGE_PREPARE        REDPACKET_STAGE_TYPE = 4
)

var REDPACKET_STAGE_TYPE_name = map[int32]string{
	1: "REDPACKET_STAGE_DEFAULT",
	2: "REDPACKET_STAGE_LOTTERY",
	3: "REDPACKET_STAGE_MASS_GUILDBUFF",
	4: "REDPACKET_STAGE_PREPARE",
}
var REDPACKET_STAGE_TYPE_value = map[string]int32{
	"REDPACKET_STAGE_DEFAULT":        1,
	"REDPACKET_STAGE_LOTTERY":        2,
	"REDPACKET_STAGE_MASS_GUILDBUFF": 3,
	"REDPACKET_STAGE_PREPARE":        4,
}

func (x REDPACKET_STAGE_TYPE) Enum() *REDPACKET_STAGE_TYPE {
	p := new(REDPACKET_STAGE_TYPE)
	*p = x
	return p
}
func (x REDPACKET_STAGE_TYPE) String() string {
	return proto.EnumName(REDPACKET_STAGE_TYPE_name, int32(x))
}
func (x *REDPACKET_STAGE_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(REDPACKET_STAGE_TYPE_value, data, "REDPACKET_STAGE_TYPE")
	if err != nil {
		return err
	}
	*x = REDPACKET_STAGE_TYPE(value)
	return nil
}
func (REDPACKET_STAGE_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{0} }

// 礼物
// 礼物类型
type REDPACKET_GIFT_TYPE int32

const (
	REDPACKET_GIFT_TYPE_REDPACKET_GIFT_NOMAL        REDPACKET_GIFT_TYPE = 1
	REDPACKET_GIFT_TYPE_REDPACKET_GIFT_DEBRIS       REDPACKET_GIFT_TYPE = 2
	REDPACKET_GIFT_TYPE_REDPACKET_GIFT_VOUCHER      REDPACKET_GIFT_TYPE = 3
	REDPACKET_GIFT_TYPE_REDPACKET_GIFT_REDDIAMOND   REDPACKET_GIFT_TYPE = 4
	REDPACKET_GIFT_TYPE_REDPACKET_GIFT_GUILDSHORTID REDPACKET_GIFT_TYPE = 5
	REDPACKET_GIFT_TYPE_REDPACKET_GIFT_MEDAL        REDPACKET_GIFT_TYPE = 6
)

var REDPACKET_GIFT_TYPE_name = map[int32]string{
	1: "REDPACKET_GIFT_NOMAL",
	2: "REDPACKET_GIFT_DEBRIS",
	3: "REDPACKET_GIFT_VOUCHER",
	4: "REDPACKET_GIFT_REDDIAMOND",
	5: "REDPACKET_GIFT_GUILDSHORTID",
	6: "REDPACKET_GIFT_MEDAL",
}
var REDPACKET_GIFT_TYPE_value = map[string]int32{
	"REDPACKET_GIFT_NOMAL":        1,
	"REDPACKET_GIFT_DEBRIS":       2,
	"REDPACKET_GIFT_VOUCHER":      3,
	"REDPACKET_GIFT_REDDIAMOND":   4,
	"REDPACKET_GIFT_GUILDSHORTID": 5,
	"REDPACKET_GIFT_MEDAL":        6,
}

func (x REDPACKET_GIFT_TYPE) Enum() *REDPACKET_GIFT_TYPE {
	p := new(REDPACKET_GIFT_TYPE)
	*p = x
	return p
}
func (x REDPACKET_GIFT_TYPE) String() string {
	return proto.EnumName(REDPACKET_GIFT_TYPE_name, int32(x))
}
func (x *REDPACKET_GIFT_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(REDPACKET_GIFT_TYPE_value, data, "REDPACKET_GIFT_TYPE")
	if err != nil {
		return err
	}
	*x = REDPACKET_GIFT_TYPE(value)
	return nil
}
func (REDPACKET_GIFT_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{1} }

// 礼物支持的平台类型
type REDPACKET_GIFT_PLATFORM_TYPE int32

const (
	REDPACKET_GIFT_PLATFORM_TYPE_REDPACKET_GIFT_PLATFORM_ALL     REDPACKET_GIFT_PLATFORM_TYPE = 1
	REDPACKET_GIFT_PLATFORM_TYPE_REDPACKET_GIFT_PLATFORM_ANDROID REDPACKET_GIFT_PLATFORM_TYPE = 2
	REDPACKET_GIFT_PLATFORM_TYPE_REDPACKET_GIFT_PLATFORM_IOS     REDPACKET_GIFT_PLATFORM_TYPE = 3
)

var REDPACKET_GIFT_PLATFORM_TYPE_name = map[int32]string{
	1: "REDPACKET_GIFT_PLATFORM_ALL",
	2: "REDPACKET_GIFT_PLATFORM_ANDROID",
	3: "REDPACKET_GIFT_PLATFORM_IOS",
}
var REDPACKET_GIFT_PLATFORM_TYPE_value = map[string]int32{
	"REDPACKET_GIFT_PLATFORM_ALL":     1,
	"REDPACKET_GIFT_PLATFORM_ANDROID": 2,
	"REDPACKET_GIFT_PLATFORM_IOS":     3,
}

func (x REDPACKET_GIFT_PLATFORM_TYPE) Enum() *REDPACKET_GIFT_PLATFORM_TYPE {
	p := new(REDPACKET_GIFT_PLATFORM_TYPE)
	*p = x
	return p
}
func (x REDPACKET_GIFT_PLATFORM_TYPE) String() string {
	return proto.EnumName(REDPACKET_GIFT_PLATFORM_TYPE_name, int32(x))
}
func (x *REDPACKET_GIFT_PLATFORM_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(REDPACKET_GIFT_PLATFORM_TYPE_value, data, "REDPACKET_GIFT_PLATFORM_TYPE")
	if err != nil {
		return err
	}
	*x = REDPACKET_GIFT_PLATFORM_TYPE(value)
	return nil
}
func (REDPACKET_GIFT_PLATFORM_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{2}
}

// 红包活动事件
type RedPacketActivityBase struct {
	ActivityId        uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	ActivityName      string `protobuf:"bytes,2,req,name=activity_name,json=activityName" json:"activity_name"`
	ActivityBeginTime string `protobuf:"bytes,3,req,name=activity_begin_time,json=activityBeginTime" json:"activity_begin_time"`
	ActivityEndTime   string `protobuf:"bytes,4,req,name=activity_end_time,json=activityEndTime" json:"activity_end_time"`
}

func (m *RedPacketActivityBase) Reset()                    { *m = RedPacketActivityBase{} }
func (m *RedPacketActivityBase) String() string            { return proto.CompactTextString(m) }
func (*RedPacketActivityBase) ProtoMessage()               {}
func (*RedPacketActivityBase) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{0} }

func (m *RedPacketActivityBase) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *RedPacketActivityBase) GetActivityName() string {
	if m != nil {
		return m.ActivityName
	}
	return ""
}

func (m *RedPacketActivityBase) GetActivityBeginTime() string {
	if m != nil {
		return m.ActivityBeginTime
	}
	return ""
}

func (m *RedPacketActivityBase) GetActivityEndTime() string {
	if m != nil {
		return m.ActivityEndTime
	}
	return ""
}

type RedPacketActivityStage struct {
	StageId         uint32   `protobuf:"varint,1,req,name=stage_id,json=stageId" json:"stage_id"`
	ActivityId      uint32   `protobuf:"varint,2,req,name=activity_id,json=activityId" json:"activity_id"`
	StageType       uint32   `protobuf:"varint,3,req,name=stage_type,json=stageType" json:"stage_type"`
	StageName       string   `protobuf:"bytes,4,opt,name=stage_name,json=stageName" json:"stage_name"`
	StageBeginTime  string   `protobuf:"bytes,5,opt,name=stage_begin_time,json=stageBeginTime" json:"stage_begin_time"`
	StageEndTime    string   `protobuf:"bytes,6,opt,name=stage_end_time,json=stageEndTime" json:"stage_end_time"`
	StageAdTextList []string `protobuf:"bytes,7,rep,name=stage_ad_text_list,json=stageAdTextList" json:"stage_ad_text_list,omitempty"`
}

func (m *RedPacketActivityStage) Reset()                    { *m = RedPacketActivityStage{} }
func (m *RedPacketActivityStage) String() string            { return proto.CompactTextString(m) }
func (*RedPacketActivityStage) ProtoMessage()               {}
func (*RedPacketActivityStage) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{1} }

func (m *RedPacketActivityStage) GetStageId() uint32 {
	if m != nil {
		return m.StageId
	}
	return 0
}

func (m *RedPacketActivityStage) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *RedPacketActivityStage) GetStageType() uint32 {
	if m != nil {
		return m.StageType
	}
	return 0
}

func (m *RedPacketActivityStage) GetStageName() string {
	if m != nil {
		return m.StageName
	}
	return ""
}

func (m *RedPacketActivityStage) GetStageBeginTime() string {
	if m != nil {
		return m.StageBeginTime
	}
	return ""
}

func (m *RedPacketActivityStage) GetStageEndTime() string {
	if m != nil {
		return m.StageEndTime
	}
	return ""
}

func (m *RedPacketActivityStage) GetStageAdTextList() []string {
	if m != nil {
		return m.StageAdTextList
	}
	return nil
}

// 后台存储的礼物类型
type RedPacketGiftBase struct {
	GiftClassifyId   uint32 `protobuf:"varint,1,req,name=gift_classify_id,json=giftClassifyId" json:"gift_classify_id"`
	GiftClassifyName string `protobuf:"bytes,2,req,name=gift_classify_name,json=giftClassifyName" json:"gift_classify_name"`
	GiftType         uint32 `protobuf:"varint,3,req,name=gift_type,json=giftType" json:"gift_type"`
	GiftPlatform     uint32 `protobuf:"varint,4,req,name=gift_platform,json=giftPlatform" json:"gift_platform"`
	SplitCntPergift  uint32 `protobuf:"varint,5,req,name=split_cnt_pergift,json=splitCntPergift" json:"split_cnt_pergift"`
	GiftExternalInfo []byte `protobuf:"bytes,6,opt,name=gift_external_info,json=giftExternalInfo" json:"gift_external_info"`
}

func (m *RedPacketGiftBase) Reset()                    { *m = RedPacketGiftBase{} }
func (m *RedPacketGiftBase) String() string            { return proto.CompactTextString(m) }
func (*RedPacketGiftBase) ProtoMessage()               {}
func (*RedPacketGiftBase) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{2} }

func (m *RedPacketGiftBase) GetGiftClassifyId() uint32 {
	if m != nil {
		return m.GiftClassifyId
	}
	return 0
}

func (m *RedPacketGiftBase) GetGiftClassifyName() string {
	if m != nil {
		return m.GiftClassifyName
	}
	return ""
}

func (m *RedPacketGiftBase) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *RedPacketGiftBase) GetGiftPlatform() uint32 {
	if m != nil {
		return m.GiftPlatform
	}
	return 0
}

func (m *RedPacketGiftBase) GetSplitCntPergift() uint32 {
	if m != nil {
		return m.SplitCntPergift
	}
	return 0
}

func (m *RedPacketGiftBase) GetGiftExternalInfo() []byte {
	if m != nil {
		return m.GiftExternalInfo
	}
	return nil
}

// 礼物扩展信息
type RedPacketGiftExter_Medal struct {
	MedalId uint32 `protobuf:"varint,1,req,name=medal_id,json=medalId" json:"medal_id"`
}

func (m *RedPacketGiftExter_Medal) Reset()         { *m = RedPacketGiftExter_Medal{} }
func (m *RedPacketGiftExter_Medal) String() string { return proto.CompactTextString(m) }
func (*RedPacketGiftExter_Medal) ProtoMessage()    {}
func (*RedPacketGiftExter_Medal) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{3}
}

func (m *RedPacketGiftExter_Medal) GetMedalId() uint32 {
	if m != nil {
		return m.MedalId
	}
	return 0
}

type RedPacketGiftExter_RedDiamond struct {
	DiamondCnt uint32 `protobuf:"varint,1,req,name=diamond_cnt,json=diamondCnt" json:"diamond_cnt"`
}

func (m *RedPacketGiftExter_RedDiamond) Reset()         { *m = RedPacketGiftExter_RedDiamond{} }
func (m *RedPacketGiftExter_RedDiamond) String() string { return proto.CompactTextString(m) }
func (*RedPacketGiftExter_RedDiamond) ProtoMessage()    {}
func (*RedPacketGiftExter_RedDiamond) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{4}
}

func (m *RedPacketGiftExter_RedDiamond) GetDiamondCnt() uint32 {
	if m != nil {
		return m.DiamondCnt
	}
	return 0
}

type RedPacketGiftExter_Debris struct {
	DebrisDesc string `protobuf:"bytes,1,req,name=debris_desc,json=debrisDesc" json:"debris_desc"`
}

func (m *RedPacketGiftExter_Debris) Reset()         { *m = RedPacketGiftExter_Debris{} }
func (m *RedPacketGiftExter_Debris) String() string { return proto.CompactTextString(m) }
func (*RedPacketGiftExter_Debris) ProtoMessage()    {}
func (*RedPacketGiftExter_Debris) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{5}
}

func (m *RedPacketGiftExter_Debris) GetDebrisDesc() string {
	if m != nil {
		return m.DebrisDesc
	}
	return ""
}

type GetGiftsByIDListReq struct {
	GiftIdList []uint32 `protobuf:"varint,1,rep,name=gift_id_list,json=giftIdList" json:"gift_id_list,omitempty"`
}

func (m *GetGiftsByIDListReq) Reset()                    { *m = GetGiftsByIDListReq{} }
func (m *GetGiftsByIDListReq) String() string            { return proto.CompactTextString(m) }
func (*GetGiftsByIDListReq) ProtoMessage()               {}
func (*GetGiftsByIDListReq) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{6} }

func (m *GetGiftsByIDListReq) GetGiftIdList() []uint32 {
	if m != nil {
		return m.GiftIdList
	}
	return nil
}

type GetGiftsByIDListResp struct {
	GiftList []*RedPacketGiftBase `protobuf:"bytes,1,rep,name=gift_list,json=giftList" json:"gift_list,omitempty"`
}

func (m *GetGiftsByIDListResp) Reset()                    { *m = GetGiftsByIDListResp{} }
func (m *GetGiftsByIDListResp) String() string            { return proto.CompactTextString(m) }
func (*GetGiftsByIDListResp) ProtoMessage()               {}
func (*GetGiftsByIDListResp) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{7} }

func (m *GetGiftsByIDListResp) GetGiftList() []*RedPacketGiftBase {
	if m != nil {
		return m.GiftList
	}
	return nil
}

type GetActivityByIdReq struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
}

func (m *GetActivityByIdReq) Reset()                    { *m = GetActivityByIdReq{} }
func (m *GetActivityByIdReq) String() string            { return proto.CompactTextString(m) }
func (*GetActivityByIdReq) ProtoMessage()               {}
func (*GetActivityByIdReq) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{8} }

func (m *GetActivityByIdReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type GetActivityByIdResp struct {
	Activity *RedPacketActivityBase `protobuf:"bytes,1,req,name=activity" json:"activity,omitempty"`
}

func (m *GetActivityByIdResp) Reset()                    { *m = GetActivityByIdResp{} }
func (m *GetActivityByIdResp) String() string            { return proto.CompactTextString(m) }
func (*GetActivityByIdResp) ProtoMessage()               {}
func (*GetActivityByIdResp) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{9} }

func (m *GetActivityByIdResp) GetActivity() *RedPacketActivityBase {
	if m != nil {
		return m.Activity
	}
	return nil
}

// 礼物配置
type RedPacketGift_Stock_ConfInf struct {
	GiftBase *RedPacketGiftBase `protobuf:"bytes,1,req,name=gift_base,json=giftBase" json:"gift_base,omitempty"`
	GiftCnt  uint32             `protobuf:"varint,2,req,name=gift_cnt,json=giftCnt" json:"gift_cnt"`
}

func (m *RedPacketGift_Stock_ConfInf) Reset()         { *m = RedPacketGift_Stock_ConfInf{} }
func (m *RedPacketGift_Stock_ConfInf) String() string { return proto.CompactTextString(m) }
func (*RedPacketGift_Stock_ConfInf) ProtoMessage()    {}
func (*RedPacketGift_Stock_ConfInf) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{10}
}

func (m *RedPacketGift_Stock_ConfInf) GetGiftBase() *RedPacketGiftBase {
	if m != nil {
		return m.GiftBase
	}
	return nil
}

func (m *RedPacketGift_Stock_ConfInf) GetGiftCnt() uint32 {
	if m != nil {
		return m.GiftCnt
	}
	return 0
}

// 阶段礼物配置
type RedPacketGift_Stage_ConfInf struct {
	GiftId        uint32 `protobuf:"varint,1,req,name=gift_id,json=giftId" json:"gift_id"`
	DrawLimitSize uint32 `protobuf:"varint,2,req,name=draw_limit_size,json=drawLimitSize" json:"draw_limit_size"`
}

func (m *RedPacketGift_Stage_ConfInf) Reset()         { *m = RedPacketGift_Stage_ConfInf{} }
func (m *RedPacketGift_Stage_ConfInf) String() string { return proto.CompactTextString(m) }
func (*RedPacketGift_Stage_ConfInf) ProtoMessage()    {}
func (*RedPacketGift_Stage_ConfInf) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{11}
}

func (m *RedPacketGift_Stage_ConfInf) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *RedPacketGift_Stage_ConfInf) GetDrawLimitSize() uint32 {
	if m != nil {
		return m.DrawLimitSize
	}
	return 0
}

type RedPacketActivity_Stage_ConfInf struct {
	StageInfo *RedPacketActivityStage        `protobuf:"bytes,1,req,name=stage_info,json=stageInfo" json:"stage_info,omitempty"`
	GiftInfo  []*RedPacketGift_Stage_ConfInf `protobuf:"bytes,2,rep,name=gift_info,json=giftInfo" json:"gift_info,omitempty"`
}

func (m *RedPacketActivity_Stage_ConfInf) Reset()         { *m = RedPacketActivity_Stage_ConfInf{} }
func (m *RedPacketActivity_Stage_ConfInf) String() string { return proto.CompactTextString(m) }
func (*RedPacketActivity_Stage_ConfInf) ProtoMessage()    {}
func (*RedPacketActivity_Stage_ConfInf) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{12}
}

func (m *RedPacketActivity_Stage_ConfInf) GetStageInfo() *RedPacketActivityStage {
	if m != nil {
		return m.StageInfo
	}
	return nil
}

func (m *RedPacketActivity_Stage_ConfInf) GetGiftInfo() []*RedPacketGift_Stage_ConfInf {
	if m != nil {
		return m.GiftInfo
	}
	return nil
}

// 创建/更新礼品
type CreateOrUpdateGiftReq struct {
	Gift *RedPacketGiftBase `protobuf:"bytes,1,req,name=gift" json:"gift,omitempty"`
}

func (m *CreateOrUpdateGiftReq) Reset()                    { *m = CreateOrUpdateGiftReq{} }
func (m *CreateOrUpdateGiftReq) String() string            { return proto.CompactTextString(m) }
func (*CreateOrUpdateGiftReq) ProtoMessage()               {}
func (*CreateOrUpdateGiftReq) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{13} }

func (m *CreateOrUpdateGiftReq) GetGift() *RedPacketGiftBase {
	if m != nil {
		return m.Gift
	}
	return nil
}

type CreateOrUpdateGiftResp struct {
	GiftClassifyId uint32 `protobuf:"varint,1,req,name=gift_classify_id,json=giftClassifyId" json:"gift_classify_id"`
}

func (m *CreateOrUpdateGiftResp) Reset()                    { *m = CreateOrUpdateGiftResp{} }
func (m *CreateOrUpdateGiftResp) String() string            { return proto.CompactTextString(m) }
func (*CreateOrUpdateGiftResp) ProtoMessage()               {}
func (*CreateOrUpdateGiftResp) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{14} }

func (m *CreateOrUpdateGiftResp) GetGiftClassifyId() uint32 {
	if m != nil {
		return m.GiftClassifyId
	}
	return 0
}

// 创建活动
type CreateRedPacketActivityReq struct {
	ActivityInfo *RedPacketActivityBase `protobuf:"bytes,1,req,name=activity_info,json=activityInfo" json:"activity_info,omitempty"`
}

func (m *CreateRedPacketActivityReq) Reset()         { *m = CreateRedPacketActivityReq{} }
func (m *CreateRedPacketActivityReq) String() string { return proto.CompactTextString(m) }
func (*CreateRedPacketActivityReq) ProtoMessage()    {}
func (*CreateRedPacketActivityReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{15}
}

func (m *CreateRedPacketActivityReq) GetActivityInfo() *RedPacketActivityBase {
	if m != nil {
		return m.ActivityInfo
	}
	return nil
}

type CreateRedPacketActivityResp struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
}

func (m *CreateRedPacketActivityResp) Reset()         { *m = CreateRedPacketActivityResp{} }
func (m *CreateRedPacketActivityResp) String() string { return proto.CompactTextString(m) }
func (*CreateRedPacketActivityResp) ProtoMessage()    {}
func (*CreateRedPacketActivityResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{16}
}

func (m *CreateRedPacketActivityResp) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

// 修改活动
type ModifyRedPacketActivityReq struct {
	ActivityInfo  *RedPacketActivityBase             `protobuf:"bytes,1,req,name=activity_info,json=activityInfo" json:"activity_info,omitempty"`
	StageInfoList []*RedPacketActivity_Stage_ConfInf `protobuf:"bytes,2,rep,name=stage_info_list,json=stageInfoList" json:"stage_info_list,omitempty"`
}

func (m *ModifyRedPacketActivityReq) Reset()         { *m = ModifyRedPacketActivityReq{} }
func (m *ModifyRedPacketActivityReq) String() string { return proto.CompactTextString(m) }
func (*ModifyRedPacketActivityReq) ProtoMessage()    {}
func (*ModifyRedPacketActivityReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{17}
}

func (m *ModifyRedPacketActivityReq) GetActivityInfo() *RedPacketActivityBase {
	if m != nil {
		return m.ActivityInfo
	}
	return nil
}

func (m *ModifyRedPacketActivityReq) GetStageInfoList() []*RedPacketActivity_Stage_ConfInf {
	if m != nil {
		return m.StageInfoList
	}
	return nil
}

type ModifyRedPacketActivityResp struct {
	ActivityId  uint32   `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	StageIdList []uint32 `protobuf:"varint,2,rep,name=stage_id_list,json=stageIdList" json:"stage_id_list,omitempty"`
}

func (m *ModifyRedPacketActivityResp) Reset()         { *m = ModifyRedPacketActivityResp{} }
func (m *ModifyRedPacketActivityResp) String() string { return proto.CompactTextString(m) }
func (*ModifyRedPacketActivityResp) ProtoMessage()    {}
func (*ModifyRedPacketActivityResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{18}
}

func (m *ModifyRedPacketActivityResp) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *ModifyRedPacketActivityResp) GetStageIdList() []uint32 {
	if m != nil {
		return m.StageIdList
	}
	return nil
}

// 删除活动
type DelRedPacketActivityReq struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
}

func (m *DelRedPacketActivityReq) Reset()         { *m = DelRedPacketActivityReq{} }
func (m *DelRedPacketActivityReq) String() string { return proto.CompactTextString(m) }
func (*DelRedPacketActivityReq) ProtoMessage()    {}
func (*DelRedPacketActivityReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{19}
}

func (m *DelRedPacketActivityReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type DelRedPacketActivityResp struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
}

func (m *DelRedPacketActivityResp) Reset()         { *m = DelRedPacketActivityResp{} }
func (m *DelRedPacketActivityResp) String() string { return proto.CompactTextString(m) }
func (*DelRedPacketActivityResp) ProtoMessage()    {}
func (*DelRedPacketActivityResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{20}
}

func (m *DelRedPacketActivityResp) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

// 增加阶段
type CreateRedPacketStageReq struct {
	ActivityId uint32                  `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	Stage      *RedPacketActivityStage `protobuf:"bytes,2,req,name=stage" json:"stage,omitempty"`
}

func (m *CreateRedPacketStageReq) Reset()         { *m = CreateRedPacketStageReq{} }
func (m *CreateRedPacketStageReq) String() string { return proto.CompactTextString(m) }
func (*CreateRedPacketStageReq) ProtoMessage()    {}
func (*CreateRedPacketStageReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{21}
}

func (m *CreateRedPacketStageReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *CreateRedPacketStageReq) GetStage() *RedPacketActivityStage {
	if m != nil {
		return m.Stage
	}
	return nil
}

type CreateRedPacketStageResp struct {
	StageId uint32 `protobuf:"varint,1,req,name=stage_id,json=stageId" json:"stage_id"`
}

func (m *CreateRedPacketStageResp) Reset()         { *m = CreateRedPacketStageResp{} }
func (m *CreateRedPacketStageResp) String() string { return proto.CompactTextString(m) }
func (*CreateRedPacketStageResp) ProtoMessage()    {}
func (*CreateRedPacketStageResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{22}
}

func (m *CreateRedPacketStageResp) GetStageId() uint32 {
	if m != nil {
		return m.StageId
	}
	return 0
}

// 修改阶段
type ModifyRedPacketStageReq struct {
	ActivityId uint32                  `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	Stage      *RedPacketActivityStage `protobuf:"bytes,2,req,name=stage" json:"stage,omitempty"`
}

func (m *ModifyRedPacketStageReq) Reset()         { *m = ModifyRedPacketStageReq{} }
func (m *ModifyRedPacketStageReq) String() string { return proto.CompactTextString(m) }
func (*ModifyRedPacketStageReq) ProtoMessage()    {}
func (*ModifyRedPacketStageReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{23}
}

func (m *ModifyRedPacketStageReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *ModifyRedPacketStageReq) GetStage() *RedPacketActivityStage {
	if m != nil {
		return m.Stage
	}
	return nil
}

type ModifyRedPacketStageResp struct {
}

func (m *ModifyRedPacketStageResp) Reset()         { *m = ModifyRedPacketStageResp{} }
func (m *ModifyRedPacketStageResp) String() string { return proto.CompactTextString(m) }
func (*ModifyRedPacketStageResp) ProtoMessage()    {}
func (*ModifyRedPacketStageResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{24}
}

type ModifyRedPacketStageGiftConfReq struct {
	ActivityId   uint32                         `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	StageId      uint32                         `protobuf:"varint,2,req,name=stage_id,json=stageId" json:"stage_id"`
	GiftInfoList []*RedPacketGift_Stage_ConfInf `protobuf:"bytes,3,rep,name=gift_info_list,json=giftInfoList" json:"gift_info_list,omitempty"`
}

func (m *ModifyRedPacketStageGiftConfReq) Reset()         { *m = ModifyRedPacketStageGiftConfReq{} }
func (m *ModifyRedPacketStageGiftConfReq) String() string { return proto.CompactTextString(m) }
func (*ModifyRedPacketStageGiftConfReq) ProtoMessage()    {}
func (*ModifyRedPacketStageGiftConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{25}
}

func (m *ModifyRedPacketStageGiftConfReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *ModifyRedPacketStageGiftConfReq) GetStageId() uint32 {
	if m != nil {
		return m.StageId
	}
	return 0
}

func (m *ModifyRedPacketStageGiftConfReq) GetGiftInfoList() []*RedPacketGift_Stage_ConfInf {
	if m != nil {
		return m.GiftInfoList
	}
	return nil
}

type ModifyRedPacketStageGiftConfResp struct {
}

func (m *ModifyRedPacketStageGiftConfResp) Reset()         { *m = ModifyRedPacketStageGiftConfResp{} }
func (m *ModifyRedPacketStageGiftConfResp) String() string { return proto.CompactTextString(m) }
func (*ModifyRedPacketStageGiftConfResp) ProtoMessage()    {}
func (*ModifyRedPacketStageGiftConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{26}
}

// 删除阶段
type DelRedPacketStageReq struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	StageId    uint32 `protobuf:"varint,2,req,name=stage_id,json=stageId" json:"stage_id"`
}

func (m *DelRedPacketStageReq) Reset()                    { *m = DelRedPacketStageReq{} }
func (m *DelRedPacketStageReq) String() string            { return proto.CompactTextString(m) }
func (*DelRedPacketStageReq) ProtoMessage()               {}
func (*DelRedPacketStageReq) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{27} }

func (m *DelRedPacketStageReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DelRedPacketStageReq) GetStageId() uint32 {
	if m != nil {
		return m.StageId
	}
	return 0
}

type DelRedPacketStageResp struct {
}

func (m *DelRedPacketStageResp) Reset()                    { *m = DelRedPacketStageResp{} }
func (m *DelRedPacketStageResp) String() string            { return proto.CompactTextString(m) }
func (*DelRedPacketStageResp) ProtoMessage()               {}
func (*DelRedPacketStageResp) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{28} }

type GetRedPacketStageDetailReq struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	StageId    uint32 `protobuf:"varint,2,req,name=stage_id,json=stageId" json:"stage_id"`
}

func (m *GetRedPacketStageDetailReq) Reset()         { *m = GetRedPacketStageDetailReq{} }
func (m *GetRedPacketStageDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetRedPacketStageDetailReq) ProtoMessage()    {}
func (*GetRedPacketStageDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{29}
}

func (m *GetRedPacketStageDetailReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *GetRedPacketStageDetailReq) GetStageId() uint32 {
	if m != nil {
		return m.StageId
	}
	return 0
}

type GetRedPacketStageDetailResp struct {
	StageDetail *RedPacketActivity_Stage_ConfInf `protobuf:"bytes,1,opt,name=stage_detail,json=stageDetail" json:"stage_detail,omitempty"`
}

func (m *GetRedPacketStageDetailResp) Reset()         { *m = GetRedPacketStageDetailResp{} }
func (m *GetRedPacketStageDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetRedPacketStageDetailResp) ProtoMessage()    {}
func (*GetRedPacketStageDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{30}
}

func (m *GetRedPacketStageDetailResp) GetStageDetail() *RedPacketActivity_Stage_ConfInf {
	if m != nil {
		return m.StageDetail
	}
	return nil
}

// 增加礼物库存
type AddConfigGiftStoreReq struct {
	GiftInfo *RedPacketGift_Stock_ConfInf `protobuf:"bytes,1,req,name=gift_info,json=giftInfo" json:"gift_info,omitempty"`
}

func (m *AddConfigGiftStoreReq) Reset()                    { *m = AddConfigGiftStoreReq{} }
func (m *AddConfigGiftStoreReq) String() string            { return proto.CompactTextString(m) }
func (*AddConfigGiftStoreReq) ProtoMessage()               {}
func (*AddConfigGiftStoreReq) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{31} }

func (m *AddConfigGiftStoreReq) GetGiftInfo() *RedPacketGift_Stock_ConfInf {
	if m != nil {
		return m.GiftInfo
	}
	return nil
}

type AddConfigGiftStoreResp struct {
	GiftId uint32 `protobuf:"varint,1,req,name=gift_id,json=giftId" json:"gift_id"`
}

func (m *AddConfigGiftStoreResp) Reset()                    { *m = AddConfigGiftStoreResp{} }
func (m *AddConfigGiftStoreResp) String() string            { return proto.CompactTextString(m) }
func (*AddConfigGiftStoreResp) ProtoMessage()               {}
func (*AddConfigGiftStoreResp) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{32} }

func (m *AddConfigGiftStoreResp) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

// 修改礼物库存数目
type SetConfigGiftStoreSizeReq struct {
	GiftId        uint32 `protobuf:"varint,1,req,name=gift_id,json=giftId" json:"gift_id"`
	GiftStoreSize uint32 `protobuf:"varint,2,req,name=gift_store_size,json=giftStoreSize" json:"gift_store_size"`
}

func (m *SetConfigGiftStoreSizeReq) Reset()         { *m = SetConfigGiftStoreSizeReq{} }
func (m *SetConfigGiftStoreSizeReq) String() string { return proto.CompactTextString(m) }
func (*SetConfigGiftStoreSizeReq) ProtoMessage()    {}
func (*SetConfigGiftStoreSizeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{33}
}

func (m *SetConfigGiftStoreSizeReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *SetConfigGiftStoreSizeReq) GetGiftStoreSize() uint32 {
	if m != nil {
		return m.GiftStoreSize
	}
	return 0
}

type SetConfigGiftStoreSizeResp struct {
	GiftId        uint32 `protobuf:"varint,1,req,name=gift_id,json=giftId" json:"gift_id"`
	GiftStoreSize uint32 `protobuf:"varint,2,req,name=gift_store_size,json=giftStoreSize" json:"gift_store_size"`
}

func (m *SetConfigGiftStoreSizeResp) Reset()         { *m = SetConfigGiftStoreSizeResp{} }
func (m *SetConfigGiftStoreSizeResp) String() string { return proto.CompactTextString(m) }
func (*SetConfigGiftStoreSizeResp) ProtoMessage()    {}
func (*SetConfigGiftStoreSizeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{34}
}

func (m *SetConfigGiftStoreSizeResp) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *SetConfigGiftStoreSizeResp) GetGiftStoreSize() uint32 {
	if m != nil {
		return m.GiftStoreSize
	}
	return 0
}

// 获取当前全部库存礼物列表
type GetAllConfigStoreGiftListReq struct {
}

func (m *GetAllConfigStoreGiftListReq) Reset()         { *m = GetAllConfigStoreGiftListReq{} }
func (m *GetAllConfigStoreGiftListReq) String() string { return proto.CompactTextString(m) }
func (*GetAllConfigStoreGiftListReq) ProtoMessage()    {}
func (*GetAllConfigStoreGiftListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{35}
}

type GetAllConfigStoreGiftListResp struct {
	GiftList []*RedPacketGift_Stock_ConfInf `protobuf:"bytes,1,rep,name=gift_list,json=giftList" json:"gift_list,omitempty"`
}

func (m *GetAllConfigStoreGiftListResp) Reset()         { *m = GetAllConfigStoreGiftListResp{} }
func (m *GetAllConfigStoreGiftListResp) String() string { return proto.CompactTextString(m) }
func (*GetAllConfigStoreGiftListResp) ProtoMessage()    {}
func (*GetAllConfigStoreGiftListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{36}
}

func (m *GetAllConfigStoreGiftListResp) GetGiftList() []*RedPacketGift_Stock_ConfInf {
	if m != nil {
		return m.GiftList
	}
	return nil
}

// 将礼物分配给活动(已经分配给活动的礼物在活动结束之前不能回收)
type DistributGiftToActivityReq struct {
	ActivityId uint32                         `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	GiftList   []*RedPacketGift_Stock_ConfInf `protobuf:"bytes,2,rep,name=gift_list,json=giftList" json:"gift_list,omitempty"`
}

func (m *DistributGiftToActivityReq) Reset()         { *m = DistributGiftToActivityReq{} }
func (m *DistributGiftToActivityReq) String() string { return proto.CompactTextString(m) }
func (*DistributGiftToActivityReq) ProtoMessage()    {}
func (*DistributGiftToActivityReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{37}
}

func (m *DistributGiftToActivityReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DistributGiftToActivityReq) GetGiftList() []*RedPacketGift_Stock_ConfInf {
	if m != nil {
		return m.GiftList
	}
	return nil
}

type DistributGiftToActivityResp struct {
}

func (m *DistributGiftToActivityResp) Reset()         { *m = DistributGiftToActivityResp{} }
func (m *DistributGiftToActivityResp) String() string { return proto.CompactTextString(m) }
func (*DistributGiftToActivityResp) ProtoMessage()    {}
func (*DistributGiftToActivityResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{38}
}

// 获取已经分配给指定活动的礼物列表
type GetActivityDistributedGiftListReq struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
}

func (m *GetActivityDistributedGiftListReq) Reset()         { *m = GetActivityDistributedGiftListReq{} }
func (m *GetActivityDistributedGiftListReq) String() string { return proto.CompactTextString(m) }
func (*GetActivityDistributedGiftListReq) ProtoMessage()    {}
func (*GetActivityDistributedGiftListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{39}
}

func (m *GetActivityDistributedGiftListReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type GetActivityDistributedGiftListResp struct {
	ActivityId uint32                         `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	GiftList   []*RedPacketGift_Stock_ConfInf `protobuf:"bytes,2,rep,name=gift_list,json=giftList" json:"gift_list,omitempty"`
}

func (m *GetActivityDistributedGiftListResp) Reset()         { *m = GetActivityDistributedGiftListResp{} }
func (m *GetActivityDistributedGiftListResp) String() string { return proto.CompactTextString(m) }
func (*GetActivityDistributedGiftListResp) ProtoMessage()    {}
func (*GetActivityDistributedGiftListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{40}
}

func (m *GetActivityDistributedGiftListResp) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *GetActivityDistributedGiftListResp) GetGiftList() []*RedPacketGift_Stock_ConfInf {
	if m != nil {
		return m.GiftList
	}
	return nil
}

// 获取全部配置的活动列表
type GetAllConfigRedPacketActivityListReq struct {
}

func (m *GetAllConfigRedPacketActivityListReq) Reset()         { *m = GetAllConfigRedPacketActivityListReq{} }
func (m *GetAllConfigRedPacketActivityListReq) String() string { return proto.CompactTextString(m) }
func (*GetAllConfigRedPacketActivityListReq) ProtoMessage()    {}
func (*GetAllConfigRedPacketActivityListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{41}
}

type GetAllConfigRedPacketActivityListResp struct {
	ActivityList []*RedPacketActivityBase `protobuf:"bytes,1,rep,name=activity_list,json=activityList" json:"activity_list,omitempty"`
}

func (m *GetAllConfigRedPacketActivityListResp) Reset()         { *m = GetAllConfigRedPacketActivityListResp{} }
func (m *GetAllConfigRedPacketActivityListResp) String() string { return proto.CompactTextString(m) }
func (*GetAllConfigRedPacketActivityListResp) ProtoMessage()    {}
func (*GetAllConfigRedPacketActivityListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{42}
}

func (m *GetAllConfigRedPacketActivityListResp) GetActivityList() []*RedPacketActivityBase {
	if m != nil {
		return m.ActivityList
	}
	return nil
}

// 获取指定活动的全部配置的阶段列表
type GetRedPacketActivityAllConfigStageListReq struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
}

func (m *GetRedPacketActivityAllConfigStageListReq) Reset() {
	*m = GetRedPacketActivityAllConfigStageListReq{}
}
func (m *GetRedPacketActivityAllConfigStageListReq) String() string { return proto.CompactTextString(m) }
func (*GetRedPacketActivityAllConfigStageListReq) ProtoMessage()    {}
func (*GetRedPacketActivityAllConfigStageListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{43}
}

func (m *GetRedPacketActivityAllConfigStageListReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type GetRedPacketActivityAllConfigStageListResp struct {
	StageList []*RedPacketActivityStage `protobuf:"bytes,1,rep,name=stage_list,json=stageList" json:"stage_list,omitempty"`
}

func (m *GetRedPacketActivityAllConfigStageListResp) Reset() {
	*m = GetRedPacketActivityAllConfigStageListResp{}
}
func (m *GetRedPacketActivityAllConfigStageListResp) String() string {
	return proto.CompactTextString(m)
}
func (*GetRedPacketActivityAllConfigStageListResp) ProtoMessage() {}
func (*GetRedPacketActivityAllConfigStageListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{44}
}

func (m *GetRedPacketActivityAllConfigStageListResp) GetStageList() []*RedPacketActivityStage {
	if m != nil {
		return m.StageList
	}
	return nil
}

// 一个完整的活动结构体
type RedPacketActivityDetail struct {
	ActivityBase *RedPacketActivityBase    `protobuf:"bytes,1,req,name=activity_base,json=activityBase" json:"activity_base,omitempty"`
	StageList    []*RedPacketActivityStage `protobuf:"bytes,2,rep,name=stage_list,json=stageList" json:"stage_list,omitempty"`
}

func (m *RedPacketActivityDetail) Reset()         { *m = RedPacketActivityDetail{} }
func (m *RedPacketActivityDetail) String() string { return proto.CompactTextString(m) }
func (*RedPacketActivityDetail) ProtoMessage()    {}
func (*RedPacketActivityDetail) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{45}
}

func (m *RedPacketActivityDetail) GetActivityBase() *RedPacketActivityBase {
	if m != nil {
		return m.ActivityBase
	}
	return nil
}

func (m *RedPacketActivityDetail) GetStageList() []*RedPacketActivityStage {
	if m != nil {
		return m.StageList
	}
	return nil
}

// 获取当前有效活动详细信息列表
type GetAllActivityDetailListReq struct {
}

func (m *GetAllActivityDetailListReq) Reset()         { *m = GetAllActivityDetailListReq{} }
func (m *GetAllActivityDetailListReq) String() string { return proto.CompactTextString(m) }
func (*GetAllActivityDetailListReq) ProtoMessage()    {}
func (*GetAllActivityDetailListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{46}
}

type GetAllActivityDetailListResp struct {
	ActivityDetailList []*RedPacketActivityDetail `protobuf:"bytes,1,rep,name=activity_detail_list,json=activityDetailList" json:"activity_detail_list,omitempty"`
}

func (m *GetAllActivityDetailListResp) Reset()         { *m = GetAllActivityDetailListResp{} }
func (m *GetAllActivityDetailListResp) String() string { return proto.CompactTextString(m) }
func (*GetAllActivityDetailListResp) ProtoMessage()    {}
func (*GetAllActivityDetailListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{47}
}

func (m *GetAllActivityDetailListResp) GetActivityDetailList() []*RedPacketActivityDetail {
	if m != nil {
		return m.ActivityDetailList
	}
	return nil
}

// 用户取到的存储的礼物类型 与 RedPacketGiftBase 的区别主要在于碎片型礼物需要在完整礼物后面加上X号碎片
type UserRedPacketGift struct {
	UserGiftId       uint32 `protobuf:"varint,1,req,name=user_gift_id,json=userGiftId" json:"user_gift_id"`
	UserGiftName     string `protobuf:"bytes,2,req,name=user_gift_name,json=userGiftName" json:"user_gift_name"`
	GiftType         uint32 `protobuf:"varint,3,req,name=gift_type,json=giftType" json:"gift_type"`
	GiftPlatform     uint32 `protobuf:"varint,4,req,name=gift_platform,json=giftPlatform" json:"gift_platform"`
	GiftExternalInfo []byte `protobuf:"bytes,5,opt,name=gift_external_info,json=giftExternalInfo" json:"gift_external_info"`
	VoucherIsCash    bool   `protobuf:"varint,6,opt,name=voucher_is_cash,json=voucherIsCash" json:"voucher_is_cash"`
	VoucherCashInfo  string `protobuf:"bytes,7,opt,name=voucher_cash_info,json=voucherCashInfo" json:"voucher_cash_info"`
	Timestamp        uint32 `protobuf:"varint,8,opt,name=timestamp" json:"timestamp"`
}

func (m *UserRedPacketGift) Reset()                    { *m = UserRedPacketGift{} }
func (m *UserRedPacketGift) String() string            { return proto.CompactTextString(m) }
func (*UserRedPacketGift) ProtoMessage()               {}
func (*UserRedPacketGift) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{48} }

func (m *UserRedPacketGift) GetUserGiftId() uint32 {
	if m != nil {
		return m.UserGiftId
	}
	return 0
}

func (m *UserRedPacketGift) GetUserGiftName() string {
	if m != nil {
		return m.UserGiftName
	}
	return ""
}

func (m *UserRedPacketGift) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *UserRedPacketGift) GetGiftPlatform() uint32 {
	if m != nil {
		return m.GiftPlatform
	}
	return 0
}

func (m *UserRedPacketGift) GetGiftExternalInfo() []byte {
	if m != nil {
		return m.GiftExternalInfo
	}
	return nil
}

func (m *UserRedPacketGift) GetVoucherIsCash() bool {
	if m != nil {
		return m.VoucherIsCash
	}
	return false
}

func (m *UserRedPacketGift) GetVoucherCashInfo() string {
	if m != nil {
		return m.VoucherCashInfo
	}
	return ""
}

func (m *UserRedPacketGift) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

// 抽红包
type DrawLotteryReq struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ComboSize    uint32 `protobuf:"varint,2,req,name=combo_size,json=comboSize" json:"combo_size"`
	GuildId      uint32 `protobuf:"varint,3,req,name=guild_id,json=guildId" json:"guild_id"`
	IsGuildOwner bool   `protobuf:"varint,4,req,name=is_guild_owner,json=isGuildOwner" json:"is_guild_owner"`
	Platform     uint32 `protobuf:"varint,5,req,name=platform" json:"platform"`
}

func (m *DrawLotteryReq) Reset()                    { *m = DrawLotteryReq{} }
func (m *DrawLotteryReq) String() string            { return proto.CompactTextString(m) }
func (*DrawLotteryReq) ProtoMessage()               {}
func (*DrawLotteryReq) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{49} }

func (m *DrawLotteryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DrawLotteryReq) GetComboSize() uint32 {
	if m != nil {
		return m.ComboSize
	}
	return 0
}

func (m *DrawLotteryReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DrawLotteryReq) GetIsGuildOwner() bool {
	if m != nil {
		return m.IsGuildOwner
	}
	return false
}

func (m *DrawLotteryReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

type DrawLotteryResp struct {
	UserGift *UserRedPacketGift `protobuf:"bytes,1,opt,name=user_gift,json=userGift" json:"user_gift,omitempty"`
}

func (m *DrawLotteryResp) Reset()                    { *m = DrawLotteryResp{} }
func (m *DrawLotteryResp) String() string            { return proto.CompactTextString(m) }
func (*DrawLotteryResp) ProtoMessage()               {}
func (*DrawLotteryResp) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{50} }

func (m *DrawLotteryResp) GetUserGift() *UserRedPacketGift {
	if m != nil {
		return m.UserGift
	}
	return nil
}

// 攒BUFF
type MassGuildBuffReq struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *MassGuildBuffReq) Reset()                    { *m = MassGuildBuffReq{} }
func (m *MassGuildBuffReq) String() string            { return proto.CompactTextString(m) }
func (*MassGuildBuffReq) ProtoMessage()               {}
func (*MassGuildBuffReq) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{51} }

func (m *MassGuildBuffReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MassGuildBuffReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type MassGuildBuffResp struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId   uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	BuffValue uint32 `protobuf:"varint,3,req,name=buff_value,json=buffValue" json:"buff_value"`
}

func (m *MassGuildBuffResp) Reset()                    { *m = MassGuildBuffResp{} }
func (m *MassGuildBuffResp) String() string            { return proto.CompactTextString(m) }
func (*MassGuildBuffResp) ProtoMessage()               {}
func (*MassGuildBuffResp) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{52} }

func (m *MassGuildBuffResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MassGuildBuffResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *MassGuildBuffResp) GetBuffValue() uint32 {
	if m != nil {
		return m.BuffValue
	}
	return 0
}

// 获取公会抽奖属性(根据阶段如果是攒BUFF和抽奖阶段返回BUFF值 如果是其他阶段返回上一阶段的BUFF值和BUFF全网排名)
type GetGuildLotteryBuffReq struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetGuildLotteryBuffReq) Reset()                    { *m = GetGuildLotteryBuffReq{} }
func (m *GetGuildLotteryBuffReq) String() string            { return proto.CompactTextString(m) }
func (*GetGuildLotteryBuffReq) ProtoMessage()               {}
func (*GetGuildLotteryBuffReq) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{53} }

func (m *GetGuildLotteryBuffReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGuildLotteryBuffReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildLotteryBuffResp struct {
	GuildId            uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	MassbuffMemberSize uint32 `protobuf:"varint,2,opt,name=massbuff_member_size,json=massbuffMemberSize" json:"massbuff_member_size"`
	BuffValue          uint32 `protobuf:"varint,3,opt,name=buff_value,json=buffValue" json:"buff_value"`
	BuffRankValue      uint32 `protobuf:"varint,4,opt,name=buff_rank_value,json=buffRankValue" json:"buff_rank_value"`
	UnityValue         uint32 `protobuf:"varint,5,opt,name=unity_value,json=unityValue" json:"unity_value"`
	UnityRankValue     uint32 `protobuf:"varint,6,opt,name=unity_rank_value,json=unityRankValue" json:"unity_rank_value"`
	CurrActivityId     uint32 `protobuf:"varint,7,opt,name=curr_activity_id,json=currActivityId" json:"curr_activity_id"`
	CurrStageId        uint32 `protobuf:"varint,8,opt,name=curr_stage_id,json=currStageId" json:"curr_stage_id"`
	CurrStageType      uint32 `protobuf:"varint,9,opt,name=curr_stage_type,json=currStageType" json:"curr_stage_type"`
}

func (m *GetGuildLotteryBuffResp) Reset()         { *m = GetGuildLotteryBuffResp{} }
func (m *GetGuildLotteryBuffResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildLotteryBuffResp) ProtoMessage()    {}
func (*GetGuildLotteryBuffResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{54}
}

func (m *GetGuildLotteryBuffResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildLotteryBuffResp) GetMassbuffMemberSize() uint32 {
	if m != nil {
		return m.MassbuffMemberSize
	}
	return 0
}

func (m *GetGuildLotteryBuffResp) GetBuffValue() uint32 {
	if m != nil {
		return m.BuffValue
	}
	return 0
}

func (m *GetGuildLotteryBuffResp) GetBuffRankValue() uint32 {
	if m != nil {
		return m.BuffRankValue
	}
	return 0
}

func (m *GetGuildLotteryBuffResp) GetUnityValue() uint32 {
	if m != nil {
		return m.UnityValue
	}
	return 0
}

func (m *GetGuildLotteryBuffResp) GetUnityRankValue() uint32 {
	if m != nil {
		return m.UnityRankValue
	}
	return 0
}

func (m *GetGuildLotteryBuffResp) GetCurrActivityId() uint32 {
	if m != nil {
		return m.CurrActivityId
	}
	return 0
}

func (m *GetGuildLotteryBuffResp) GetCurrStageId() uint32 {
	if m != nil {
		return m.CurrStageId
	}
	return 0
}

func (m *GetGuildLotteryBuffResp) GetCurrStageType() uint32 {
	if m != nil {
		return m.CurrStageType
	}
	return 0
}

type GetUserLotteryGiftListReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserLotteryGiftListReq) Reset()         { *m = GetUserLotteryGiftListReq{} }
func (m *GetUserLotteryGiftListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLotteryGiftListReq) ProtoMessage()    {}
func (*GetUserLotteryGiftListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{55}
}

func (m *GetUserLotteryGiftListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserLotteryGiftListResp struct {
	Uid      uint32               `protobuf:"varint,1,req,name=uid" json:"uid"`
	GiftList []*UserRedPacketGift `protobuf:"bytes,2,rep,name=gift_list,json=giftList" json:"gift_list,omitempty"`
}

func (m *GetUserLotteryGiftListResp) Reset()         { *m = GetUserLotteryGiftListResp{} }
func (m *GetUserLotteryGiftListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLotteryGiftListResp) ProtoMessage()    {}
func (*GetUserLotteryGiftListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{56}
}

func (m *GetUserLotteryGiftListResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserLotteryGiftListResp) GetGiftList() []*UserRedPacketGift {
	if m != nil {
		return m.GiftList
	}
	return nil
}

// 用户设置代金券兑换信息
type SetUserVoucherGiftInfoReq struct {
	Uid             uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	UserGiftId      uint32 `protobuf:"varint,2,req,name=user_gift_id,json=userGiftId" json:"user_gift_id"`
	VoucherCashInfo string `protobuf:"bytes,3,req,name=voucher_cash_info,json=voucherCashInfo" json:"voucher_cash_info"`
}

func (m *SetUserVoucherGiftInfoReq) Reset()         { *m = SetUserVoucherGiftInfoReq{} }
func (m *SetUserVoucherGiftInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetUserVoucherGiftInfoReq) ProtoMessage()    {}
func (*SetUserVoucherGiftInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{57}
}

func (m *SetUserVoucherGiftInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserVoucherGiftInfoReq) GetUserGiftId() uint32 {
	if m != nil {
		return m.UserGiftId
	}
	return 0
}

func (m *SetUserVoucherGiftInfoReq) GetVoucherCashInfo() string {
	if m != nil {
		return m.VoucherCashInfo
	}
	return ""
}

type SetUserVoucherGiftInfoResp struct {
	Uid      uint32             `protobuf:"varint,1,req,name=uid" json:"uid"`
	GiftInfo *UserRedPacketGift `protobuf:"bytes,2,opt,name=gift_info,json=giftInfo" json:"gift_info,omitempty"`
}

func (m *SetUserVoucherGiftInfoResp) Reset()         { *m = SetUserVoucherGiftInfoResp{} }
func (m *SetUserVoucherGiftInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetUserVoucherGiftInfoResp) ProtoMessage()    {}
func (*SetUserVoucherGiftInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{58}
}

func (m *SetUserVoucherGiftInfoResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserVoucherGiftInfoResp) GetGiftInfo() *UserRedPacketGift {
	if m != nil {
		return m.GiftInfo
	}
	return nil
}

// 公会BUFF排行榜
type GuildRankStatInfo struct {
	GuildId        uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	UnityValue     uint32 `protobuf:"varint,2,opt,name=unity_value,json=unityValue" json:"unity_value"`
	BuffValue      uint32 `protobuf:"varint,3,opt,name=buff_value,json=buffValue" json:"buff_value"`
	WinShortidSize uint32 `protobuf:"varint,4,opt,name=win_shortid_size,json=winShortidSize" json:"win_shortid_size"`
}

func (m *GuildRankStatInfo) Reset()                    { *m = GuildRankStatInfo{} }
func (m *GuildRankStatInfo) String() string            { return proto.CompactTextString(m) }
func (*GuildRankStatInfo) ProtoMessage()               {}
func (*GuildRankStatInfo) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{59} }

func (m *GuildRankStatInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildRankStatInfo) GetUnityValue() uint32 {
	if m != nil {
		return m.UnityValue
	}
	return 0
}

func (m *GuildRankStatInfo) GetBuffValue() uint32 {
	if m != nil {
		return m.BuffValue
	}
	return 0
}

func (m *GuildRankStatInfo) GetWinShortidSize() uint32 {
	if m != nil {
		return m.WinShortidSize
	}
	return 0
}

type GuildBuffTopN struct {
	DayTime           string               `protobuf:"bytes,1,req,name=day_time,json=dayTime" json:"day_time"`
	GuildbuffstatList []*GuildRankStatInfo `protobuf:"bytes,2,rep,name=guildbuffstat_list,json=guildbuffstatList" json:"guildbuffstat_list,omitempty"`
}

func (m *GuildBuffTopN) Reset()                    { *m = GuildBuffTopN{} }
func (m *GuildBuffTopN) String() string            { return proto.CompactTextString(m) }
func (*GuildBuffTopN) ProtoMessage()               {}
func (*GuildBuffTopN) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{60} }

func (m *GuildBuffTopN) GetDayTime() string {
	if m != nil {
		return m.DayTime
	}
	return ""
}

func (m *GuildBuffTopN) GetGuildbuffstatList() []*GuildRankStatInfo {
	if m != nil {
		return m.GuildbuffstatList
	}
	return nil
}

type BatchGetGuildBuffTopNReq struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	BeginDayTime string `protobuf:"bytes,2,req,name=begin_day_time,json=beginDayTime" json:"begin_day_time"`
	EndDayTime   string `protobuf:"bytes,3,req,name=end_day_time,json=endDayTime" json:"end_day_time"`
	TopNSize     uint32 `protobuf:"varint,4,opt,name=top_n_size,json=topNSize" json:"top_n_size"`
}

func (m *BatchGetGuildBuffTopNReq) Reset()         { *m = BatchGetGuildBuffTopNReq{} }
func (m *BatchGetGuildBuffTopNReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetGuildBuffTopNReq) ProtoMessage()    {}
func (*BatchGetGuildBuffTopNReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{61}
}

func (m *BatchGetGuildBuffTopNReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetGuildBuffTopNReq) GetBeginDayTime() string {
	if m != nil {
		return m.BeginDayTime
	}
	return ""
}

func (m *BatchGetGuildBuffTopNReq) GetEndDayTime() string {
	if m != nil {
		return m.EndDayTime
	}
	return ""
}

func (m *BatchGetGuildBuffTopNReq) GetTopNSize() uint32 {
	if m != nil {
		return m.TopNSize
	}
	return 0
}

type BatchGetGuildBuffTopNResp struct {
	TopnList []*GuildBuffTopN `protobuf:"bytes,1,rep,name=topn_list,json=topnList" json:"topn_list,omitempty"`
}

func (m *BatchGetGuildBuffTopNResp) Reset()         { *m = BatchGetGuildBuffTopNResp{} }
func (m *BatchGetGuildBuffTopNResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetGuildBuffTopNResp) ProtoMessage()    {}
func (*BatchGetGuildBuffTopNResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRedpacket, []int{62}
}

func (m *BatchGetGuildBuffTopNResp) GetTopnList() []*GuildBuffTopN {
	if m != nil {
		return m.TopnList
	}
	return nil
}

type AsyncUnityCalcNotify struct {
	ActivityId       uint32   `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	SingleStageId    uint32   `protobuf:"varint,2,opt,name=single_stage_id,json=singleStageId" json:"single_stage_id"`
	Type             uint32   `protobuf:"varint,3,opt,name=type" json:"type"`
	BatchStageIdList []uint32 `protobuf:"varint,4,rep,name=batch_stage_id_list,json=batchStageIdList" json:"batch_stage_id_list,omitempty"`
	DayTime          string   `protobuf:"bytes,5,opt,name=day_time,json=dayTime" json:"day_time"`
}

func (m *AsyncUnityCalcNotify) Reset()                    { *m = AsyncUnityCalcNotify{} }
func (m *AsyncUnityCalcNotify) String() string            { return proto.CompactTextString(m) }
func (*AsyncUnityCalcNotify) ProtoMessage()               {}
func (*AsyncUnityCalcNotify) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket, []int{63} }

func (m *AsyncUnityCalcNotify) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *AsyncUnityCalcNotify) GetSingleStageId() uint32 {
	if m != nil {
		return m.SingleStageId
	}
	return 0
}

func (m *AsyncUnityCalcNotify) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AsyncUnityCalcNotify) GetBatchStageIdList() []uint32 {
	if m != nil {
		return m.BatchStageIdList
	}
	return nil
}

func (m *AsyncUnityCalcNotify) GetDayTime() string {
	if m != nil {
		return m.DayTime
	}
	return ""
}

func init() {
	proto.RegisterType((*RedPacketActivityBase)(nil), "redpacket.RedPacketActivityBase")
	proto.RegisterType((*RedPacketActivityStage)(nil), "redpacket.RedPacketActivityStage")
	proto.RegisterType((*RedPacketGiftBase)(nil), "redpacket.RedPacketGiftBase")
	proto.RegisterType((*RedPacketGiftExter_Medal)(nil), "redpacket.RedPacketGiftExter_Medal")
	proto.RegisterType((*RedPacketGiftExter_RedDiamond)(nil), "redpacket.RedPacketGiftExter_RedDiamond")
	proto.RegisterType((*RedPacketGiftExter_Debris)(nil), "redpacket.RedPacketGiftExter_Debris")
	proto.RegisterType((*GetGiftsByIDListReq)(nil), "redpacket.GetGiftsByIDListReq")
	proto.RegisterType((*GetGiftsByIDListResp)(nil), "redpacket.GetGiftsByIDListResp")
	proto.RegisterType((*GetActivityByIdReq)(nil), "redpacket.GetActivityByIdReq")
	proto.RegisterType((*GetActivityByIdResp)(nil), "redpacket.GetActivityByIdResp")
	proto.RegisterType((*RedPacketGift_Stock_ConfInf)(nil), "redpacket.RedPacketGift_Stock_ConfInf")
	proto.RegisterType((*RedPacketGift_Stage_ConfInf)(nil), "redpacket.RedPacketGift_Stage_ConfInf")
	proto.RegisterType((*RedPacketActivity_Stage_ConfInf)(nil), "redpacket.RedPacketActivity_Stage_ConfInf")
	proto.RegisterType((*CreateOrUpdateGiftReq)(nil), "redpacket.CreateOrUpdateGiftReq")
	proto.RegisterType((*CreateOrUpdateGiftResp)(nil), "redpacket.CreateOrUpdateGiftResp")
	proto.RegisterType((*CreateRedPacketActivityReq)(nil), "redpacket.CreateRedPacketActivityReq")
	proto.RegisterType((*CreateRedPacketActivityResp)(nil), "redpacket.CreateRedPacketActivityResp")
	proto.RegisterType((*ModifyRedPacketActivityReq)(nil), "redpacket.ModifyRedPacketActivityReq")
	proto.RegisterType((*ModifyRedPacketActivityResp)(nil), "redpacket.ModifyRedPacketActivityResp")
	proto.RegisterType((*DelRedPacketActivityReq)(nil), "redpacket.DelRedPacketActivityReq")
	proto.RegisterType((*DelRedPacketActivityResp)(nil), "redpacket.DelRedPacketActivityResp")
	proto.RegisterType((*CreateRedPacketStageReq)(nil), "redpacket.CreateRedPacketStageReq")
	proto.RegisterType((*CreateRedPacketStageResp)(nil), "redpacket.CreateRedPacketStageResp")
	proto.RegisterType((*ModifyRedPacketStageReq)(nil), "redpacket.ModifyRedPacketStageReq")
	proto.RegisterType((*ModifyRedPacketStageResp)(nil), "redpacket.ModifyRedPacketStageResp")
	proto.RegisterType((*ModifyRedPacketStageGiftConfReq)(nil), "redpacket.ModifyRedPacketStageGiftConfReq")
	proto.RegisterType((*ModifyRedPacketStageGiftConfResp)(nil), "redpacket.ModifyRedPacketStageGiftConfResp")
	proto.RegisterType((*DelRedPacketStageReq)(nil), "redpacket.DelRedPacketStageReq")
	proto.RegisterType((*DelRedPacketStageResp)(nil), "redpacket.DelRedPacketStageResp")
	proto.RegisterType((*GetRedPacketStageDetailReq)(nil), "redpacket.GetRedPacketStageDetailReq")
	proto.RegisterType((*GetRedPacketStageDetailResp)(nil), "redpacket.GetRedPacketStageDetailResp")
	proto.RegisterType((*AddConfigGiftStoreReq)(nil), "redpacket.AddConfigGiftStoreReq")
	proto.RegisterType((*AddConfigGiftStoreResp)(nil), "redpacket.AddConfigGiftStoreResp")
	proto.RegisterType((*SetConfigGiftStoreSizeReq)(nil), "redpacket.SetConfigGiftStoreSizeReq")
	proto.RegisterType((*SetConfigGiftStoreSizeResp)(nil), "redpacket.SetConfigGiftStoreSizeResp")
	proto.RegisterType((*GetAllConfigStoreGiftListReq)(nil), "redpacket.GetAllConfigStoreGiftListReq")
	proto.RegisterType((*GetAllConfigStoreGiftListResp)(nil), "redpacket.GetAllConfigStoreGiftListResp")
	proto.RegisterType((*DistributGiftToActivityReq)(nil), "redpacket.DistributGiftToActivityReq")
	proto.RegisterType((*DistributGiftToActivityResp)(nil), "redpacket.DistributGiftToActivityResp")
	proto.RegisterType((*GetActivityDistributedGiftListReq)(nil), "redpacket.GetActivityDistributedGiftListReq")
	proto.RegisterType((*GetActivityDistributedGiftListResp)(nil), "redpacket.GetActivityDistributedGiftListResp")
	proto.RegisterType((*GetAllConfigRedPacketActivityListReq)(nil), "redpacket.GetAllConfigRedPacketActivityListReq")
	proto.RegisterType((*GetAllConfigRedPacketActivityListResp)(nil), "redpacket.GetAllConfigRedPacketActivityListResp")
	proto.RegisterType((*GetRedPacketActivityAllConfigStageListReq)(nil), "redpacket.GetRedPacketActivityAllConfigStageListReq")
	proto.RegisterType((*GetRedPacketActivityAllConfigStageListResp)(nil), "redpacket.GetRedPacketActivityAllConfigStageListResp")
	proto.RegisterType((*RedPacketActivityDetail)(nil), "redpacket.RedPacketActivityDetail")
	proto.RegisterType((*GetAllActivityDetailListReq)(nil), "redpacket.GetAllActivityDetailListReq")
	proto.RegisterType((*GetAllActivityDetailListResp)(nil), "redpacket.GetAllActivityDetailListResp")
	proto.RegisterType((*UserRedPacketGift)(nil), "redpacket.UserRedPacketGift")
	proto.RegisterType((*DrawLotteryReq)(nil), "redpacket.DrawLotteryReq")
	proto.RegisterType((*DrawLotteryResp)(nil), "redpacket.DrawLotteryResp")
	proto.RegisterType((*MassGuildBuffReq)(nil), "redpacket.MassGuildBuffReq")
	proto.RegisterType((*MassGuildBuffResp)(nil), "redpacket.MassGuildBuffResp")
	proto.RegisterType((*GetGuildLotteryBuffReq)(nil), "redpacket.GetGuildLotteryBuffReq")
	proto.RegisterType((*GetGuildLotteryBuffResp)(nil), "redpacket.GetGuildLotteryBuffResp")
	proto.RegisterType((*GetUserLotteryGiftListReq)(nil), "redpacket.GetUserLotteryGiftListReq")
	proto.RegisterType((*GetUserLotteryGiftListResp)(nil), "redpacket.GetUserLotteryGiftListResp")
	proto.RegisterType((*SetUserVoucherGiftInfoReq)(nil), "redpacket.SetUserVoucherGiftInfoReq")
	proto.RegisterType((*SetUserVoucherGiftInfoResp)(nil), "redpacket.SetUserVoucherGiftInfoResp")
	proto.RegisterType((*GuildRankStatInfo)(nil), "redpacket.GuildRankStatInfo")
	proto.RegisterType((*GuildBuffTopN)(nil), "redpacket.GuildBuffTopN")
	proto.RegisterType((*BatchGetGuildBuffTopNReq)(nil), "redpacket.BatchGetGuildBuffTopNReq")
	proto.RegisterType((*BatchGetGuildBuffTopNResp)(nil), "redpacket.BatchGetGuildBuffTopNResp")
	proto.RegisterType((*AsyncUnityCalcNotify)(nil), "redpacket.AsyncUnityCalcNotify")
	proto.RegisterEnum("redpacket.REDPACKET_STAGE_TYPE", REDPACKET_STAGE_TYPE_name, REDPACKET_STAGE_TYPE_value)
	proto.RegisterEnum("redpacket.REDPACKET_GIFT_TYPE", REDPACKET_GIFT_TYPE_name, REDPACKET_GIFT_TYPE_value)
	proto.RegisterEnum("redpacket.REDPACKET_GIFT_PLATFORM_TYPE", REDPACKET_GIFT_PLATFORM_TYPE_name, REDPACKET_GIFT_PLATFORM_TYPE_value)
}
func (m *RedPacketActivityBase) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketActivityBase) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.ActivityName)))
	i += copy(dAtA[i:], m.ActivityName)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.ActivityBeginTime)))
	i += copy(dAtA[i:], m.ActivityBeginTime)
	dAtA[i] = 0x22
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.ActivityEndTime)))
	i += copy(dAtA[i:], m.ActivityEndTime)
	return i, nil
}

func (m *RedPacketActivityStage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketActivityStage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.StageId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.StageType))
	dAtA[i] = 0x22
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.StageName)))
	i += copy(dAtA[i:], m.StageName)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.StageBeginTime)))
	i += copy(dAtA[i:], m.StageBeginTime)
	dAtA[i] = 0x32
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.StageEndTime)))
	i += copy(dAtA[i:], m.StageEndTime)
	if len(m.StageAdTextList) > 0 {
		for _, s := range m.StageAdTextList {
			dAtA[i] = 0x3a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *RedPacketGiftBase) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketGiftBase) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftClassifyId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.GiftClassifyName)))
	i += copy(dAtA[i:], m.GiftClassifyName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftPlatform))
	dAtA[i] = 0x28
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.SplitCntPergift))
	if m.GiftExternalInfo != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(len(m.GiftExternalInfo)))
		i += copy(dAtA[i:], m.GiftExternalInfo)
	}
	return i, nil
}

func (m *RedPacketGiftExter_Medal) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketGiftExter_Medal) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.MedalId))
	return i, nil
}

func (m *RedPacketGiftExter_RedDiamond) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketGiftExter_RedDiamond) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.DiamondCnt))
	return i, nil
}

func (m *RedPacketGiftExter_Debris) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketGiftExter_Debris) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.DebrisDesc)))
	i += copy(dAtA[i:], m.DebrisDesc)
	return i, nil
}

func (m *GetGiftsByIDListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftsByIDListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GiftIdList) > 0 {
		for _, num := range m.GiftIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetGiftsByIDListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftsByIDListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GiftList) > 0 {
		for _, msg := range m.GiftList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetActivityByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActivityByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	return i, nil
}

func (m *GetActivityByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActivityByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Activity == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("activity")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(m.Activity.Size()))
		n1, err := m.Activity.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *RedPacketGift_Stock_ConfInf) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketGift_Stock_ConfInf) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GiftBase == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("gift_base")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftBase.Size()))
		n2, err := m.GiftBase.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftCnt))
	return i, nil
}

func (m *RedPacketGift_Stage_ConfInf) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketGift_Stage_ConfInf) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.DrawLimitSize))
	return i, nil
}

func (m *RedPacketActivity_Stage_ConfInf) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketActivity_Stage_ConfInf) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.StageInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("stage_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(m.StageInfo.Size()))
		n3, err := m.StageInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if len(m.GiftInfo) > 0 {
		for _, msg := range m.GiftInfo {
			dAtA[i] = 0x12
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CreateOrUpdateGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateOrUpdateGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Gift == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("gift")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(m.Gift.Size()))
		n4, err := m.Gift.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *CreateOrUpdateGiftResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateOrUpdateGiftResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftClassifyId))
	return i, nil
}

func (m *CreateRedPacketActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateRedPacketActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ActivityInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("activity_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityInfo.Size()))
		n5, err := m.ActivityInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *CreateRedPacketActivityResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateRedPacketActivityResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	return i, nil
}

func (m *ModifyRedPacketActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyRedPacketActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ActivityInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("activity_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityInfo.Size()))
		n6, err := m.ActivityInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if len(m.StageInfoList) > 0 {
		for _, msg := range m.StageInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ModifyRedPacketActivityResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyRedPacketActivityResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	if len(m.StageIdList) > 0 {
		for _, num := range m.StageIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *DelRedPacketActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelRedPacketActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	return i, nil
}

func (m *DelRedPacketActivityResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelRedPacketActivityResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	return i, nil
}

func (m *CreateRedPacketStageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateRedPacketStageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	if m.Stage == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("stage")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(m.Stage.Size()))
		n7, err := m.Stage.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *CreateRedPacketStageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateRedPacketStageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.StageId))
	return i, nil
}

func (m *ModifyRedPacketStageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyRedPacketStageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	if m.Stage == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("stage")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(m.Stage.Size()))
		n8, err := m.Stage.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *ModifyRedPacketStageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyRedPacketStageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ModifyRedPacketStageGiftConfReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyRedPacketStageGiftConfReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.StageId))
	if len(m.GiftInfoList) > 0 {
		for _, msg := range m.GiftInfoList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ModifyRedPacketStageGiftConfResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyRedPacketStageGiftConfResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DelRedPacketStageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelRedPacketStageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.StageId))
	return i, nil
}

func (m *DelRedPacketStageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelRedPacketStageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetRedPacketStageDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRedPacketStageDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.StageId))
	return i, nil
}

func (m *GetRedPacketStageDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRedPacketStageDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.StageDetail != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(m.StageDetail.Size()))
		n9, err := m.StageDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	return i, nil
}

func (m *AddConfigGiftStoreReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddConfigGiftStoreReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GiftInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("gift_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftInfo.Size()))
		n10, err := m.GiftInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	return i, nil
}

func (m *AddConfigGiftStoreResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddConfigGiftStoreResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftId))
	return i, nil
}

func (m *SetConfigGiftStoreSizeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetConfigGiftStoreSizeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftStoreSize))
	return i, nil
}

func (m *SetConfigGiftStoreSizeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetConfigGiftStoreSizeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftStoreSize))
	return i, nil
}

func (m *GetAllConfigStoreGiftListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllConfigStoreGiftListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAllConfigStoreGiftListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllConfigStoreGiftListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GiftList) > 0 {
		for _, msg := range m.GiftList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DistributGiftToActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DistributGiftToActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	if len(m.GiftList) > 0 {
		for _, msg := range m.GiftList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DistributGiftToActivityResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DistributGiftToActivityResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetActivityDistributedGiftListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActivityDistributedGiftListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	return i, nil
}

func (m *GetActivityDistributedGiftListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActivityDistributedGiftListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	if len(m.GiftList) > 0 {
		for _, msg := range m.GiftList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetAllConfigRedPacketActivityListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllConfigRedPacketActivityListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAllConfigRedPacketActivityListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllConfigRedPacketActivityListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ActivityList) > 0 {
		for _, msg := range m.ActivityList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetRedPacketActivityAllConfigStageListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRedPacketActivityAllConfigStageListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	return i, nil
}

func (m *GetRedPacketActivityAllConfigStageListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRedPacketActivityAllConfigStageListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.StageList) > 0 {
		for _, msg := range m.StageList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RedPacketActivityDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketActivityDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ActivityBase == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("activity_base")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityBase.Size()))
		n11, err := m.ActivityBase.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	if len(m.StageList) > 0 {
		for _, msg := range m.StageList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetAllActivityDetailListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllActivityDetailListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAllActivityDetailListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllActivityDetailListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ActivityDetailList) > 0 {
		for _, msg := range m.ActivityDetailList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UserRedPacketGift) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserRedPacketGift) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.UserGiftId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.UserGiftName)))
	i += copy(dAtA[i:], m.UserGiftName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftPlatform))
	if m.GiftExternalInfo != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(len(m.GiftExternalInfo)))
		i += copy(dAtA[i:], m.GiftExternalInfo)
	}
	dAtA[i] = 0x30
	i++
	if m.VoucherIsCash {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x3a
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.VoucherCashInfo)))
	i += copy(dAtA[i:], m.VoucherCashInfo)
	dAtA[i] = 0x40
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.Timestamp))
	return i, nil
}

func (m *DrawLotteryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DrawLotteryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ComboSize))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x20
	i++
	if m.IsGuildOwner {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.Platform))
	return i, nil
}

func (m *DrawLotteryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DrawLotteryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserGift != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(m.UserGift.Size()))
		n12, err := m.UserGift.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	return i, nil
}

func (m *MassGuildBuffReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MassGuildBuffReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *MassGuildBuffResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MassGuildBuffResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.BuffValue))
	return i, nil
}

func (m *GetGuildLotteryBuffReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildLotteryBuffReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGuildLotteryBuffResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildLotteryBuffResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.MassbuffMemberSize))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.BuffValue))
	dAtA[i] = 0x20
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.BuffRankValue))
	dAtA[i] = 0x28
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.UnityValue))
	dAtA[i] = 0x30
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.UnityRankValue))
	dAtA[i] = 0x38
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.CurrActivityId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.CurrStageId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.CurrStageType))
	return i, nil
}

func (m *GetUserLotteryGiftListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLotteryGiftListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserLotteryGiftListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLotteryGiftListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.Uid))
	if len(m.GiftList) > 0 {
		for _, msg := range m.GiftList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetUserVoucherGiftInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserVoucherGiftInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.UserGiftId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.VoucherCashInfo)))
	i += copy(dAtA[i:], m.VoucherCashInfo)
	return i, nil
}

func (m *SetUserVoucherGiftInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserVoucherGiftInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.Uid))
	if m.GiftInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRedpacket(dAtA, i, uint64(m.GiftInfo.Size()))
		n13, err := m.GiftInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	return i, nil
}

func (m *GuildRankStatInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildRankStatInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.UnityValue))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.BuffValue))
	dAtA[i] = 0x20
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.WinShortidSize))
	return i, nil
}

func (m *GuildBuffTopN) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildBuffTopN) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.DayTime)))
	i += copy(dAtA[i:], m.DayTime)
	if len(m.GuildbuffstatList) > 0 {
		for _, msg := range m.GuildbuffstatList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchGetGuildBuffTopNReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetGuildBuffTopNReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.BeginDayTime)))
	i += copy(dAtA[i:], m.BeginDayTime)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.EndDayTime)))
	i += copy(dAtA[i:], m.EndDayTime)
	dAtA[i] = 0x20
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.TopNSize))
	return i, nil
}

func (m *BatchGetGuildBuffTopNResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetGuildBuffTopNResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TopnList) > 0 {
		for _, msg := range m.TopnList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AsyncUnityCalcNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AsyncUnityCalcNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.SingleStageId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(m.Type))
	if len(m.BatchStageIdList) > 0 {
		for _, num := range m.BatchStageIdList {
			dAtA[i] = 0x20
			i++
			i = encodeVarintRedpacket(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRedpacket(dAtA, i, uint64(len(m.DayTime)))
	i += copy(dAtA[i:], m.DayTime)
	return i, nil
}

func encodeFixed64Redpacket(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Redpacket(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintRedpacket(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *RedPacketActivityBase) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	l = len(m.ActivityName)
	n += 1 + l + sovRedpacket(uint64(l))
	l = len(m.ActivityBeginTime)
	n += 1 + l + sovRedpacket(uint64(l))
	l = len(m.ActivityEndTime)
	n += 1 + l + sovRedpacket(uint64(l))
	return n
}

func (m *RedPacketActivityStage) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.StageId))
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	n += 1 + sovRedpacket(uint64(m.StageType))
	l = len(m.StageName)
	n += 1 + l + sovRedpacket(uint64(l))
	l = len(m.StageBeginTime)
	n += 1 + l + sovRedpacket(uint64(l))
	l = len(m.StageEndTime)
	n += 1 + l + sovRedpacket(uint64(l))
	if len(m.StageAdTextList) > 0 {
		for _, s := range m.StageAdTextList {
			l = len(s)
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *RedPacketGiftBase) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.GiftClassifyId))
	l = len(m.GiftClassifyName)
	n += 1 + l + sovRedpacket(uint64(l))
	n += 1 + sovRedpacket(uint64(m.GiftType))
	n += 1 + sovRedpacket(uint64(m.GiftPlatform))
	n += 1 + sovRedpacket(uint64(m.SplitCntPergift))
	if m.GiftExternalInfo != nil {
		l = len(m.GiftExternalInfo)
		n += 1 + l + sovRedpacket(uint64(l))
	}
	return n
}

func (m *RedPacketGiftExter_Medal) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.MedalId))
	return n
}

func (m *RedPacketGiftExter_RedDiamond) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.DiamondCnt))
	return n
}

func (m *RedPacketGiftExter_Debris) Size() (n int) {
	var l int
	_ = l
	l = len(m.DebrisDesc)
	n += 1 + l + sovRedpacket(uint64(l))
	return n
}

func (m *GetGiftsByIDListReq) Size() (n int) {
	var l int
	_ = l
	if len(m.GiftIdList) > 0 {
		for _, e := range m.GiftIdList {
			n += 1 + sovRedpacket(uint64(e))
		}
	}
	return n
}

func (m *GetGiftsByIDListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GiftList) > 0 {
		for _, e := range m.GiftList {
			l = e.Size()
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *GetActivityByIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	return n
}

func (m *GetActivityByIdResp) Size() (n int) {
	var l int
	_ = l
	if m.Activity != nil {
		l = m.Activity.Size()
		n += 1 + l + sovRedpacket(uint64(l))
	}
	return n
}

func (m *RedPacketGift_Stock_ConfInf) Size() (n int) {
	var l int
	_ = l
	if m.GiftBase != nil {
		l = m.GiftBase.Size()
		n += 1 + l + sovRedpacket(uint64(l))
	}
	n += 1 + sovRedpacket(uint64(m.GiftCnt))
	return n
}

func (m *RedPacketGift_Stage_ConfInf) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.GiftId))
	n += 1 + sovRedpacket(uint64(m.DrawLimitSize))
	return n
}

func (m *RedPacketActivity_Stage_ConfInf) Size() (n int) {
	var l int
	_ = l
	if m.StageInfo != nil {
		l = m.StageInfo.Size()
		n += 1 + l + sovRedpacket(uint64(l))
	}
	if len(m.GiftInfo) > 0 {
		for _, e := range m.GiftInfo {
			l = e.Size()
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *CreateOrUpdateGiftReq) Size() (n int) {
	var l int
	_ = l
	if m.Gift != nil {
		l = m.Gift.Size()
		n += 1 + l + sovRedpacket(uint64(l))
	}
	return n
}

func (m *CreateOrUpdateGiftResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.GiftClassifyId))
	return n
}

func (m *CreateRedPacketActivityReq) Size() (n int) {
	var l int
	_ = l
	if m.ActivityInfo != nil {
		l = m.ActivityInfo.Size()
		n += 1 + l + sovRedpacket(uint64(l))
	}
	return n
}

func (m *CreateRedPacketActivityResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	return n
}

func (m *ModifyRedPacketActivityReq) Size() (n int) {
	var l int
	_ = l
	if m.ActivityInfo != nil {
		l = m.ActivityInfo.Size()
		n += 1 + l + sovRedpacket(uint64(l))
	}
	if len(m.StageInfoList) > 0 {
		for _, e := range m.StageInfoList {
			l = e.Size()
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *ModifyRedPacketActivityResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	if len(m.StageIdList) > 0 {
		for _, e := range m.StageIdList {
			n += 1 + sovRedpacket(uint64(e))
		}
	}
	return n
}

func (m *DelRedPacketActivityReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	return n
}

func (m *DelRedPacketActivityResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	return n
}

func (m *CreateRedPacketStageReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	if m.Stage != nil {
		l = m.Stage.Size()
		n += 1 + l + sovRedpacket(uint64(l))
	}
	return n
}

func (m *CreateRedPacketStageResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.StageId))
	return n
}

func (m *ModifyRedPacketStageReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	if m.Stage != nil {
		l = m.Stage.Size()
		n += 1 + l + sovRedpacket(uint64(l))
	}
	return n
}

func (m *ModifyRedPacketStageResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ModifyRedPacketStageGiftConfReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	n += 1 + sovRedpacket(uint64(m.StageId))
	if len(m.GiftInfoList) > 0 {
		for _, e := range m.GiftInfoList {
			l = e.Size()
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *ModifyRedPacketStageGiftConfResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DelRedPacketStageReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	n += 1 + sovRedpacket(uint64(m.StageId))
	return n
}

func (m *DelRedPacketStageResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetRedPacketStageDetailReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	n += 1 + sovRedpacket(uint64(m.StageId))
	return n
}

func (m *GetRedPacketStageDetailResp) Size() (n int) {
	var l int
	_ = l
	if m.StageDetail != nil {
		l = m.StageDetail.Size()
		n += 1 + l + sovRedpacket(uint64(l))
	}
	return n
}

func (m *AddConfigGiftStoreReq) Size() (n int) {
	var l int
	_ = l
	if m.GiftInfo != nil {
		l = m.GiftInfo.Size()
		n += 1 + l + sovRedpacket(uint64(l))
	}
	return n
}

func (m *AddConfigGiftStoreResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.GiftId))
	return n
}

func (m *SetConfigGiftStoreSizeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.GiftId))
	n += 1 + sovRedpacket(uint64(m.GiftStoreSize))
	return n
}

func (m *SetConfigGiftStoreSizeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.GiftId))
	n += 1 + sovRedpacket(uint64(m.GiftStoreSize))
	return n
}

func (m *GetAllConfigStoreGiftListReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAllConfigStoreGiftListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GiftList) > 0 {
		for _, e := range m.GiftList {
			l = e.Size()
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *DistributGiftToActivityReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	if len(m.GiftList) > 0 {
		for _, e := range m.GiftList {
			l = e.Size()
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *DistributGiftToActivityResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetActivityDistributedGiftListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	return n
}

func (m *GetActivityDistributedGiftListResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	if len(m.GiftList) > 0 {
		for _, e := range m.GiftList {
			l = e.Size()
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *GetAllConfigRedPacketActivityListReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAllConfigRedPacketActivityListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ActivityList) > 0 {
		for _, e := range m.ActivityList {
			l = e.Size()
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *GetRedPacketActivityAllConfigStageListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	return n
}

func (m *GetRedPacketActivityAllConfigStageListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.StageList) > 0 {
		for _, e := range m.StageList {
			l = e.Size()
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *RedPacketActivityDetail) Size() (n int) {
	var l int
	_ = l
	if m.ActivityBase != nil {
		l = m.ActivityBase.Size()
		n += 1 + l + sovRedpacket(uint64(l))
	}
	if len(m.StageList) > 0 {
		for _, e := range m.StageList {
			l = e.Size()
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *GetAllActivityDetailListReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAllActivityDetailListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ActivityDetailList) > 0 {
		for _, e := range m.ActivityDetailList {
			l = e.Size()
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *UserRedPacketGift) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.UserGiftId))
	l = len(m.UserGiftName)
	n += 1 + l + sovRedpacket(uint64(l))
	n += 1 + sovRedpacket(uint64(m.GiftType))
	n += 1 + sovRedpacket(uint64(m.GiftPlatform))
	if m.GiftExternalInfo != nil {
		l = len(m.GiftExternalInfo)
		n += 1 + l + sovRedpacket(uint64(l))
	}
	n += 2
	l = len(m.VoucherCashInfo)
	n += 1 + l + sovRedpacket(uint64(l))
	n += 1 + sovRedpacket(uint64(m.Timestamp))
	return n
}

func (m *DrawLotteryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.Uid))
	n += 1 + sovRedpacket(uint64(m.ComboSize))
	n += 1 + sovRedpacket(uint64(m.GuildId))
	n += 2
	n += 1 + sovRedpacket(uint64(m.Platform))
	return n
}

func (m *DrawLotteryResp) Size() (n int) {
	var l int
	_ = l
	if m.UserGift != nil {
		l = m.UserGift.Size()
		n += 1 + l + sovRedpacket(uint64(l))
	}
	return n
}

func (m *MassGuildBuffReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.Uid))
	n += 1 + sovRedpacket(uint64(m.GuildId))
	return n
}

func (m *MassGuildBuffResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.Uid))
	n += 1 + sovRedpacket(uint64(m.GuildId))
	n += 1 + sovRedpacket(uint64(m.BuffValue))
	return n
}

func (m *GetGuildLotteryBuffReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.Uid))
	n += 1 + sovRedpacket(uint64(m.GuildId))
	return n
}

func (m *GetGuildLotteryBuffResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.GuildId))
	n += 1 + sovRedpacket(uint64(m.MassbuffMemberSize))
	n += 1 + sovRedpacket(uint64(m.BuffValue))
	n += 1 + sovRedpacket(uint64(m.BuffRankValue))
	n += 1 + sovRedpacket(uint64(m.UnityValue))
	n += 1 + sovRedpacket(uint64(m.UnityRankValue))
	n += 1 + sovRedpacket(uint64(m.CurrActivityId))
	n += 1 + sovRedpacket(uint64(m.CurrStageId))
	n += 1 + sovRedpacket(uint64(m.CurrStageType))
	return n
}

func (m *GetUserLotteryGiftListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.Uid))
	return n
}

func (m *GetUserLotteryGiftListResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.Uid))
	if len(m.GiftList) > 0 {
		for _, e := range m.GiftList {
			l = e.Size()
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *SetUserVoucherGiftInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.Uid))
	n += 1 + sovRedpacket(uint64(m.UserGiftId))
	l = len(m.VoucherCashInfo)
	n += 1 + l + sovRedpacket(uint64(l))
	return n
}

func (m *SetUserVoucherGiftInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.Uid))
	if m.GiftInfo != nil {
		l = m.GiftInfo.Size()
		n += 1 + l + sovRedpacket(uint64(l))
	}
	return n
}

func (m *GuildRankStatInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.GuildId))
	n += 1 + sovRedpacket(uint64(m.UnityValue))
	n += 1 + sovRedpacket(uint64(m.BuffValue))
	n += 1 + sovRedpacket(uint64(m.WinShortidSize))
	return n
}

func (m *GuildBuffTopN) Size() (n int) {
	var l int
	_ = l
	l = len(m.DayTime)
	n += 1 + l + sovRedpacket(uint64(l))
	if len(m.GuildbuffstatList) > 0 {
		for _, e := range m.GuildbuffstatList {
			l = e.Size()
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *BatchGetGuildBuffTopNReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.Uid))
	l = len(m.BeginDayTime)
	n += 1 + l + sovRedpacket(uint64(l))
	l = len(m.EndDayTime)
	n += 1 + l + sovRedpacket(uint64(l))
	n += 1 + sovRedpacket(uint64(m.TopNSize))
	return n
}

func (m *BatchGetGuildBuffTopNResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TopnList) > 0 {
		for _, e := range m.TopnList {
			l = e.Size()
			n += 1 + l + sovRedpacket(uint64(l))
		}
	}
	return n
}

func (m *AsyncUnityCalcNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket(uint64(m.ActivityId))
	n += 1 + sovRedpacket(uint64(m.SingleStageId))
	n += 1 + sovRedpacket(uint64(m.Type))
	if len(m.BatchStageIdList) > 0 {
		for _, e := range m.BatchStageIdList {
			n += 1 + sovRedpacket(uint64(e))
		}
	}
	l = len(m.DayTime)
	n += 1 + l + sovRedpacket(uint64(l))
	return n
}

func sovRedpacket(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozRedpacket(x uint64) (n int) {
	return sovRedpacket(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *RedPacketActivityBase) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketActivityBase: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketActivityBase: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityBeginTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityBeginTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityEndTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityEndTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_begin_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_end_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketActivityStage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketActivityStage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketActivityStage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageId", wireType)
			}
			m.StageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageType", wireType)
			}
			m.StageType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StageType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StageName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageBeginTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StageBeginTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageEndTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StageEndTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageAdTextList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StageAdTextList = append(m.StageAdTextList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stage_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stage_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketGiftBase) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketGiftBase: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketGiftBase: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftClassifyId", wireType)
			}
			m.GiftClassifyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftClassifyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftClassifyName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftClassifyName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftType", wireType)
			}
			m.GiftType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPlatform", wireType)
			}
			m.GiftPlatform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPlatform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SplitCntPergift", wireType)
			}
			m.SplitCntPergift = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SplitCntPergift |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftExternalInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftExternalInfo = append(m.GiftExternalInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.GiftExternalInfo == nil {
				m.GiftExternalInfo = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_classify_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_classify_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_platform")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("split_cnt_pergift")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketGiftExter_Medal) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketGiftExter_Medal: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketGiftExter_Medal: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalId", wireType)
			}
			m.MedalId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MedalId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("medal_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketGiftExter_RedDiamond) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketGiftExter_RedDiamond: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketGiftExter_RedDiamond: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DiamondCnt", wireType)
			}
			m.DiamondCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DiamondCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("diamond_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketGiftExter_Debris) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketGiftExter_Debris: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketGiftExter_Debris: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DebrisDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DebrisDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("debris_desc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftsByIDListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftsByIDListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftsByIDListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRedpacket
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GiftIdList = append(m.GiftIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRedpacket
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthRedpacket
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowRedpacket
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GiftIdList = append(m.GiftIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftsByIDListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftsByIDListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftsByIDListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftList = append(m.GiftList, &RedPacketGiftBase{})
			if err := m.GiftList[len(m.GiftList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActivityByIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActivityByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActivityByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActivityByIdResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActivityByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActivityByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Activity", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Activity == nil {
				m.Activity = &RedPacketActivityBase{}
			}
			if err := m.Activity.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketGift_Stock_ConfInf) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketGift_Stock_ConfInf: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketGift_Stock_ConfInf: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftBase", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GiftBase == nil {
				m.GiftBase = &RedPacketGiftBase{}
			}
			if err := m.GiftBase.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftCnt", wireType)
			}
			m.GiftCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_base")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketGift_Stage_ConfInf) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketGift_Stage_ConfInf: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketGift_Stage_ConfInf: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftId", wireType)
			}
			m.GiftId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DrawLimitSize", wireType)
			}
			m.DrawLimitSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DrawLimitSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("draw_limit_size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketActivity_Stage_ConfInf) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketActivity_Stage_ConfInf: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketActivity_Stage_ConfInf: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.StageInfo == nil {
				m.StageInfo = &RedPacketActivityStage{}
			}
			if err := m.StageInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftInfo = append(m.GiftInfo, &RedPacketGift_Stage_ConfInf{})
			if err := m.GiftInfo[len(m.GiftInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stage_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateOrUpdateGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateOrUpdateGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateOrUpdateGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Gift", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Gift == nil {
				m.Gift = &RedPacketGiftBase{}
			}
			if err := m.Gift.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateOrUpdateGiftResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateOrUpdateGiftResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateOrUpdateGiftResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftClassifyId", wireType)
			}
			m.GiftClassifyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftClassifyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_classify_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateRedPacketActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateRedPacketActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateRedPacketActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ActivityInfo == nil {
				m.ActivityInfo = &RedPacketActivityBase{}
			}
			if err := m.ActivityInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateRedPacketActivityResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateRedPacketActivityResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateRedPacketActivityResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyRedPacketActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyRedPacketActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyRedPacketActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ActivityInfo == nil {
				m.ActivityInfo = &RedPacketActivityBase{}
			}
			if err := m.ActivityInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StageInfoList = append(m.StageInfoList, &RedPacketActivity_Stage_ConfInf{})
			if err := m.StageInfoList[len(m.StageInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyRedPacketActivityResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyRedPacketActivityResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyRedPacketActivityResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRedpacket
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.StageIdList = append(m.StageIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRedpacket
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthRedpacket
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowRedpacket
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.StageIdList = append(m.StageIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelRedPacketActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelRedPacketActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelRedPacketActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelRedPacketActivityResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelRedPacketActivityResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelRedPacketActivityResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateRedPacketStageReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateRedPacketStageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateRedPacketStageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Stage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Stage == nil {
				m.Stage = &RedPacketActivityStage{}
			}
			if err := m.Stage.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stage")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateRedPacketStageResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateRedPacketStageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateRedPacketStageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageId", wireType)
			}
			m.StageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stage_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyRedPacketStageReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyRedPacketStageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyRedPacketStageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Stage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Stage == nil {
				m.Stage = &RedPacketActivityStage{}
			}
			if err := m.Stage.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stage")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyRedPacketStageResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyRedPacketStageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyRedPacketStageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyRedPacketStageGiftConfReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyRedPacketStageGiftConfReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyRedPacketStageGiftConfReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageId", wireType)
			}
			m.StageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftInfoList = append(m.GiftInfoList, &RedPacketGift_Stage_ConfInf{})
			if err := m.GiftInfoList[len(m.GiftInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stage_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyRedPacketStageGiftConfResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyRedPacketStageGiftConfResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyRedPacketStageGiftConfResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelRedPacketStageReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelRedPacketStageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelRedPacketStageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageId", wireType)
			}
			m.StageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stage_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelRedPacketStageResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelRedPacketStageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelRedPacketStageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRedPacketStageDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRedPacketStageDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRedPacketStageDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageId", wireType)
			}
			m.StageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stage_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRedPacketStageDetailResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRedPacketStageDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRedPacketStageDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.StageDetail == nil {
				m.StageDetail = &RedPacketActivity_Stage_ConfInf{}
			}
			if err := m.StageDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddConfigGiftStoreReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddConfigGiftStoreReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddConfigGiftStoreReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GiftInfo == nil {
				m.GiftInfo = &RedPacketGift_Stock_ConfInf{}
			}
			if err := m.GiftInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddConfigGiftStoreResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddConfigGiftStoreResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddConfigGiftStoreResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftId", wireType)
			}
			m.GiftId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetConfigGiftStoreSizeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetConfigGiftStoreSizeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetConfigGiftStoreSizeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftId", wireType)
			}
			m.GiftId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftStoreSize", wireType)
			}
			m.GiftStoreSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftStoreSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_store_size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetConfigGiftStoreSizeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetConfigGiftStoreSizeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetConfigGiftStoreSizeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftId", wireType)
			}
			m.GiftId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftStoreSize", wireType)
			}
			m.GiftStoreSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftStoreSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_store_size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllConfigStoreGiftListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllConfigStoreGiftListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllConfigStoreGiftListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllConfigStoreGiftListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllConfigStoreGiftListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllConfigStoreGiftListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftList = append(m.GiftList, &RedPacketGift_Stock_ConfInf{})
			if err := m.GiftList[len(m.GiftList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DistributGiftToActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DistributGiftToActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DistributGiftToActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftList = append(m.GiftList, &RedPacketGift_Stock_ConfInf{})
			if err := m.GiftList[len(m.GiftList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DistributGiftToActivityResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DistributGiftToActivityResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DistributGiftToActivityResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActivityDistributedGiftListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActivityDistributedGiftListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActivityDistributedGiftListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActivityDistributedGiftListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActivityDistributedGiftListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActivityDistributedGiftListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftList = append(m.GiftList, &RedPacketGift_Stock_ConfInf{})
			if err := m.GiftList[len(m.GiftList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllConfigRedPacketActivityListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllConfigRedPacketActivityListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllConfigRedPacketActivityListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllConfigRedPacketActivityListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllConfigRedPacketActivityListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllConfigRedPacketActivityListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityList = append(m.ActivityList, &RedPacketActivityBase{})
			if err := m.ActivityList[len(m.ActivityList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRedPacketActivityAllConfigStageListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRedPacketActivityAllConfigStageListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRedPacketActivityAllConfigStageListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRedPacketActivityAllConfigStageListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRedPacketActivityAllConfigStageListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRedPacketActivityAllConfigStageListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StageList = append(m.StageList, &RedPacketActivityStage{})
			if err := m.StageList[len(m.StageList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketActivityDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketActivityDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketActivityDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityBase", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ActivityBase == nil {
				m.ActivityBase = &RedPacketActivityBase{}
			}
			if err := m.ActivityBase.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StageList = append(m.StageList, &RedPacketActivityStage{})
			if err := m.StageList[len(m.StageList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_base")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllActivityDetailListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllActivityDetailListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllActivityDetailListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllActivityDetailListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllActivityDetailListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllActivityDetailListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityDetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityDetailList = append(m.ActivityDetailList, &RedPacketActivityDetail{})
			if err := m.ActivityDetailList[len(m.ActivityDetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserRedPacketGift) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserRedPacketGift: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserRedPacketGift: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserGiftId", wireType)
			}
			m.UserGiftId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserGiftId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserGiftName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserGiftName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftType", wireType)
			}
			m.GiftType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPlatform", wireType)
			}
			m.GiftPlatform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPlatform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftExternalInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftExternalInfo = append(m.GiftExternalInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.GiftExternalInfo == nil {
				m.GiftExternalInfo = []byte{}
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoucherIsCash", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.VoucherIsCash = bool(v != 0)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoucherCashInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VoucherCashInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_gift_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_gift_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_platform")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DrawLotteryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DrawLotteryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DrawLotteryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ComboSize", wireType)
			}
			m.ComboSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ComboSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsGuildOwner", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsGuildOwner = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("combo_size")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_guild_owner")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("platform")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DrawLotteryResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DrawLotteryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DrawLotteryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserGift", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserGift == nil {
				m.UserGift = &UserRedPacketGift{}
			}
			if err := m.UserGift.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MassGuildBuffReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MassGuildBuffReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MassGuildBuffReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MassGuildBuffResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MassGuildBuffResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MassGuildBuffResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuffValue", wireType)
			}
			m.BuffValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuffValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("buff_value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildLotteryBuffReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildLotteryBuffReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildLotteryBuffReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildLotteryBuffResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildLotteryBuffResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildLotteryBuffResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MassbuffMemberSize", wireType)
			}
			m.MassbuffMemberSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MassbuffMemberSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuffValue", wireType)
			}
			m.BuffValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuffValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuffRankValue", wireType)
			}
			m.BuffRankValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuffRankValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UnityValue", wireType)
			}
			m.UnityValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UnityValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UnityRankValue", wireType)
			}
			m.UnityRankValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UnityRankValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrActivityId", wireType)
			}
			m.CurrActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrStageId", wireType)
			}
			m.CurrStageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrStageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrStageType", wireType)
			}
			m.CurrStageType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrStageType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLotteryGiftListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLotteryGiftListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLotteryGiftListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLotteryGiftListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLotteryGiftListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLotteryGiftListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftList = append(m.GiftList, &UserRedPacketGift{})
			if err := m.GiftList[len(m.GiftList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserVoucherGiftInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserVoucherGiftInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserVoucherGiftInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserGiftId", wireType)
			}
			m.UserGiftId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserGiftId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoucherCashInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VoucherCashInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_gift_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("voucher_cash_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserVoucherGiftInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserVoucherGiftInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserVoucherGiftInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GiftInfo == nil {
				m.GiftInfo = &UserRedPacketGift{}
			}
			if err := m.GiftInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildRankStatInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildRankStatInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildRankStatInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UnityValue", wireType)
			}
			m.UnityValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UnityValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BuffValue", wireType)
			}
			m.BuffValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuffValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WinShortidSize", wireType)
			}
			m.WinShortidSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WinShortidSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildBuffTopN) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildBuffTopN: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildBuffTopN: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DayTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildbuffstatList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildbuffstatList = append(m.GuildbuffstatList, &GuildRankStatInfo{})
			if err := m.GuildbuffstatList[len(m.GuildbuffstatList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("day_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetGuildBuffTopNReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetGuildBuffTopNReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetGuildBuffTopNReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginDayTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BeginDayTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndDayTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EndDayTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopNSize", wireType)
			}
			m.TopNSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopNSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_day_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_day_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetGuildBuffTopNResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetGuildBuffTopNResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetGuildBuffTopNResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopnList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopnList = append(m.TopnList, &GuildBuffTopN{})
			if err := m.TopnList[len(m.TopnList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AsyncUnityCalcNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AsyncUnityCalcNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AsyncUnityCalcNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SingleStageId", wireType)
			}
			m.SingleStageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SingleStageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRedpacket
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.BatchStageIdList = append(m.BatchStageIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRedpacket
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthRedpacket
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowRedpacket
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.BatchStageIdList = append(m.BatchStageIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field BatchStageIdList", wireType)
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DayTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipRedpacket(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowRedpacket
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRedpacket
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthRedpacket
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowRedpacket
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipRedpacket(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthRedpacket = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowRedpacket   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("services/redpacket/redpacket.proto", fileDescriptorRedpacket) }

var fileDescriptorRedpacket = []byte{
	// 3228 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5a, 0xcf, 0x6f, 0x1b, 0xc7,
	0xf5, 0xf7, 0x92, 0xb2, 0x25, 0x3d, 0xfd, 0xa2, 0xc6, 0xb2, 0x44, 0xad, 0x2c, 0x69, 0xbd, 0xb6,
	0x15, 0x5b, 0xb1, 0x6c, 0x7f, 0xfd, 0x4d, 0x1a, 0x58, 0x11, 0x84, 0x50, 0x22, 0xcd, 0xb0, 0x91,
	0x2c, 0x85, 0xa4, 0x0d, 0x04, 0x28, 0xba, 0x5d, 0x71, 0x87, 0xd2, 0xd6, 0xe4, 0x72, 0xcd, 0x59,
	0xda, 0x66, 0xd0, 0x02, 0x29, 0xd0, 0x1f, 0x69, 0x81, 0xa0, 0x69, 0x2f, 0x05, 0x7a, 0x6a, 0x0b,
	0xa3, 0x28, 0x5a, 0x14, 0x08, 0xd0, 0x02, 0xbd, 0xf4, 0xd0, 0x63, 0x8e, 0x6d, 0x81, 0x5e, 0x8b,
	0x22, 0xb9, 0xf8, 0xd2, 0xff, 0xa1, 0x98, 0x99, 0xdd, 0xe5, 0xec, 0x72, 0x97, 0x5c, 0xb9, 0x49,
	0x6f, 0xd2, 0xbc, 0x37, 0x33, 0x9f, 0xf7, 0xe6, 0xbd, 0x37, 0x6f, 0x3e, 0x5c, 0x50, 0x09, 0x6e,
	0x3f, 0x31, 0x6b, 0x98, 0xdc, 0x6a, 0x63, 0xc3, 0xd6, 0x6b, 0x8f, 0xb0, 0xd3, 0xfb, 0xeb, 0xa6,
	0xdd, 0x6e, 0x39, 0x2d, 0x34, 0xee, 0x0f, 0xc8, 0x57, 0x6a, 0xad, 0x66, 0xb3, 0x65, 0xdd, 0x72,
	0x1a, 0x4f, 0x6c, 0xb3, 0xf6, 0xa8, 0x81, 0x6f, 0x91, 0x47, 0x47, 0x1d, 0xb3, 0xe1, 0x98, 0x96,
	0xd3, 0xb5, 0x31, 0x9f, 0xa0, 0xfe, 0x4d, 0x82, 0x0b, 0x65, 0x6c, 0x1c, 0xb2, 0x39, 0xb9, 0x9a,
	0x63, 0x3e, 0x31, 0x9d, 0xee, 0x8e, 0x4e, 0x30, 0xba, 0x0a, 0x13, 0xba, 0xfb, 0xbf, 0x66, 0x1a,
	0x59, 0x49, 0x49, 0x5d, 0x9b, 0xda, 0x19, 0xf9, 0xf4, 0x9f, 0xab, 0x67, 0xca, 0xe0, 0x09, 0x4a,
	0x06, 0xba, 0x0e, 0x53, 0xbe, 0x9a, 0xa5, 0x37, 0x71, 0x36, 0xa5, 0xa4, 0xae, 0x8d, 0xbb, 0x8a,
	0x93, 0x9e, 0xe8, 0xbe, 0xde, 0xc4, 0xe8, 0x35, 0x38, 0xef, 0xab, 0x1e, 0xe1, 0x63, 0xd3, 0xd2,
	0x1c, 0xb3, 0x89, 0xb3, 0x69, 0x61, 0xc2, 0xac, 0xa7, 0xb0, 0x43, 0xe5, 0x55, 0xb3, 0x89, 0xd1,
	0x6d, 0xf0, 0x07, 0x35, 0x6c, 0x19, 0x7c, 0xce, 0x88, 0x30, 0x67, 0xc6, 0x13, 0x17, 0x2c, 0x83,
	0xce, 0x50, 0xff, 0x90, 0x82, 0xf9, 0x3e, 0x9b, 0x2a, 0x8e, 0x7e, 0x8c, 0xd1, 0x2a, 0x8c, 0x11,
	0xfa, 0x47, 0xd8, 0xa2, 0x51, 0x36, 0x5a, 0x32, 0xc2, 0x56, 0xa7, 0x62, 0xac, 0xbe, 0x0c, 0xc0,
	0xd7, 0xa1, 0xae, 0x64, 0x16, 0x78, 0x5a, 0xe3, 0x6c, 0xbc, 0xda, 0xb5, 0x71, 0x4f, 0x89, 0xf9,
	0x65, 0x44, 0x91, 0x7c, 0xc8, 0x5c, 0x89, 0x39, 0xe5, 0x26, 0x64, 0xb8, 0x92, 0xe0, 0x91, 0xb3,
	0x82, 0xea, 0x34, 0x93, 0xf6, 0xdc, 0xb1, 0x0e, 0x7c, 0xa4, 0xe7, 0x8b, 0x73, 0x82, 0xf6, 0x24,
	0x93, 0xb9, 0x8e, 0x40, 0xaf, 0x02, 0xe2, 0xba, 0xba, 0xa1, 0x39, 0xf8, 0x99, 0xa3, 0x35, 0x4c,
	0xe2, 0x64, 0x47, 0x95, 0xf4, 0xb5, 0xf1, 0xf2, 0x0c, 0x93, 0xe4, 0x8c, 0x2a, 0x7e, 0xe6, 0xec,
	0x99, 0xc4, 0x51, 0x7f, 0x97, 0x82, 0x59, 0xdf, 0x6b, 0x45, 0xb3, 0xee, 0xb0, 0x28, 0xb8, 0x09,
	0x99, 0x63, 0xb3, 0xee, 0x68, 0xb5, 0x86, 0x4e, 0x88, 0x59, 0xef, 0x0b, 0x85, 0x69, 0x2a, 0xdd,
	0x75, 0x85, 0x25, 0x03, 0xdd, 0x01, 0x14, 0xd4, 0xef, 0x8b, 0x89, 0x8c, 0x38, 0x83, 0xb9, 0xe0,
	0x12, 0x8c, 0xb3, 0x39, 0x7d, 0xbe, 0x1c, 0xa3, 0xc3, 0xcc, 0x95, 0xd7, 0x61, 0x8a, 0xa9, 0xd8,
	0x0d, 0xdd, 0xa9, 0xb7, 0xda, 0x4d, 0x16, 0x00, 0x9e, 0xda, 0x24, 0x15, 0x1d, 0xba, 0x12, 0x1a,
	0x2f, 0xc4, 0x6e, 0x98, 0x8e, 0x56, 0xb3, 0x1c, 0xcd, 0xc6, 0x6d, 0x2a, 0xcc, 0x9e, 0x15, 0xd4,
	0x67, 0x98, 0x78, 0xd7, 0x72, 0x0e, 0xb9, 0xd0, 0xc7, 0x8c, 0x9f, 0x39, 0xb8, 0x6d, 0xe9, 0x0d,
	0xcd, 0xb4, 0xea, 0x2d, 0xe6, 0xd6, 0x49, 0x11, 0x73, 0xc1, 0x15, 0x97, 0xac, 0x7a, 0x4b, 0x7d,
	0x13, 0xb2, 0x01, 0x67, 0x31, 0xa1, 0xb6, 0x8f, 0x0d, 0xbd, 0x41, 0x83, 0xac, 0x49, 0xff, 0xe8,
	0x0b, 0x32, 0x36, 0x5a, 0x32, 0xd4, 0x7b, 0xb0, 0x1c, 0x31, 0xb9, 0x8c, 0x8d, 0xbc, 0xa9, 0x37,
	0x5b, 0x16, 0x8b, 0x42, 0x83, 0xff, 0x49, 0xad, 0x08, 0xe6, 0x9e, 0x2b, 0xd8, 0xb5, 0x1c, 0x75,
	0x07, 0x16, 0x23, 0xd6, 0xc9, 0xe3, 0xa3, 0xb6, 0x49, 0xd8, 0x1a, 0xec, 0x2f, 0xcd, 0xc0, 0xa4,
	0xc6, 0xd6, 0x18, 0xf7, 0xd7, 0x60, 0x82, 0x3c, 0x26, 0x35, 0xf5, 0x0d, 0x38, 0x5f, 0xe4, 0xb3,
	0xc9, 0x4e, 0xb7, 0x94, 0xa7, 0xa1, 0x50, 0xc6, 0x8f, 0x91, 0x02, 0xcc, 0xab, 0x9a, 0x69, 0xf0,
	0xa0, 0x91, 0x94, 0xf4, 0xb5, 0xa9, 0x32, 0xd0, 0xb1, 0x92, 0xc1, 0xe2, 0xe5, 0x5d, 0x98, 0xeb,
	0x9f, 0x48, 0x6c, 0x74, 0xd7, 0x3d, 0x4d, 0x7f, 0xda, 0xc4, 0x9d, 0x8b, 0x37, 0x7b, 0x75, 0xaa,
	0x2f, 0xc4, 0xf8, 0x29, 0xb3, 0x25, 0xdf, 0x04, 0x54, 0x14, 0xaa, 0x50, 0xb7, 0x64, 0x50, 0x28,
	0xc9, 0x0a, 0x91, 0x5a, 0x61, 0x86, 0x04, 0x27, 0x13, 0x1b, 0x6d, 0xc1, 0x98, 0xa7, 0xc4, 0xa6,
	0x4e, 0xdc, 0x51, 0xa2, 0xd0, 0x88, 0xa5, 0xaf, 0xec, 0xcf, 0x50, 0xbb, 0xb0, 0x14, 0x00, 0xac,
	0x55, 0x9c, 0x56, 0xed, 0x91, 0xb6, 0xdb, 0xb2, 0xea, 0x25, 0xab, 0xee, 0xdb, 0x7a, 0xa4, 0x13,
	0xec, 0xae, 0x9e, 0xc0, 0x56, 0x96, 0x58, 0xab, 0x30, 0xc6, 0x13, 0xc5, 0x72, 0x02, 0x55, 0x66,
	0x94, 0xa5, 0x87, 0xe5, 0xa8, 0xdf, 0xec, 0xdf, 0x9a, 0xa6, 0xb2, 0xb7, 0xf5, 0x32, 0x8c, 0xba,
	0x07, 0x14, 0xf0, 0xc8, 0x39, 0x7e, 0x42, 0xe8, 0x06, 0xcc, 0x18, 0x6d, 0xfd, 0xa9, 0xd6, 0x30,
	0x9b, 0xa6, 0xa3, 0x11, 0xf3, 0x7d, 0x1c, 0xd8, 0x65, 0x8a, 0x0a, 0xf7, 0xa8, 0xac, 0x62, 0xbe,
	0x8f, 0xd5, 0xdf, 0x48, 0xb0, 0xda, 0xe7, 0x8a, 0xd0, 0x86, 0x6f, 0x79, 0xd5, 0x8c, 0x65, 0x07,
	0x37, 0xf6, 0xd2, 0x20, 0x57, 0xb2, 0xe9, 0x6e, 0xa9, 0xa3, 0x39, 0x83, 0x76, 0x5d, 0x6f, 0xb1,
	0x05, 0x52, 0x2c, 0x32, 0xd6, 0xe2, 0xbc, 0x15, 0xdc, 0x9c, 0xfb, 0x8d, 0x25, 0x5e, 0x09, 0x2e,
	0xec, 0xb6, 0xb1, 0xee, 0xe0, 0x83, 0xf6, 0x03, 0xdb, 0xd0, 0x1d, 0x4c, 0xb5, 0x69, 0x98, 0xdc,
	0x86, 0x11, 0x96, 0xea, 0x49, 0x8e, 0x81, 0x69, 0xaa, 0x6f, 0xc3, 0x7c, 0xd4, 0x52, 0xc4, 0x3e,
	0x6d, 0xd5, 0x53, 0x6b, 0x20, 0xf3, 0x95, 0xfa, 0x9c, 0x40, 0x91, 0x15, 0x84, 0x2b, 0x52, 0x70,
	0xde, 0xf0, 0x38, 0xf4, 0xaf, 0x4f, 0x66, 0x79, 0x1e, 0x96, 0x62, 0x37, 0x21, 0x76, 0xd2, 0x34,
	0xf9, 0x93, 0x04, 0xf2, 0x7e, 0xcb, 0x30, 0xeb, 0xdd, 0x2f, 0x11, 0x2b, 0x2a, 0xc3, 0x4c, 0x2f,
	0x58, 0x78, 0x29, 0xe0, 0x07, 0xbe, 0x3e, 0x68, 0xa1, 0xd0, 0xa1, 0x4f, 0xf9, 0xa1, 0xc3, 0xaa,
	0xc3, 0x09, 0x2c, 0xc5, 0x02, 0x4f, 0x6c, 0x3f, 0x52, 0x61, 0xca, 0xeb, 0x00, 0x7a, 0xb8, 0xa6,
	0xca, 0x13, 0x6e, 0x03, 0xc0, 0x76, 0x7a, 0x0b, 0x16, 0xf2, 0xb8, 0x11, 0xe9, 0x9f, 0x84, 0x5e,
	0xce, 0x41, 0x36, 0x7a, 0x85, 0xe4, 0x07, 0xd5, 0x85, 0x85, 0xd0, 0x71, 0xf3, 0x84, 0x4a, 0x0c,
	0x02, 0xbd, 0x01, 0x67, 0x99, 0x55, 0x2c, 0xf3, 0x13, 0x25, 0x2b, 0xd7, 0xa7, 0x97, 0x5b, 0xf4,
	0xd6, 0xc4, 0x1e, 0xda, 0x41, 0x51, 0xdc, 0xa1, 0x63, 0xfa, 0x9f, 0xe1, 0x96, 0x21, 0x1b, 0xbd,
	0x35, 0xb1, 0x69, 0xdc, 0xaf, 0x46, 0x09, 0x69, 0xce, 0xd3, 0x80, 0x3b, 0x05, 0x3e, 0xd1, 0x05,
	0xa9, 0xa8, 0x26, 0x72, 0x0f, 0xa6, 0xfd, 0x42, 0xc7, 0x83, 0x2c, 0x7d, 0xaa, 0x6a, 0x37, 0xe9,
	0x55, 0x3b, 0x16, 0x8d, 0x2a, 0x28, 0x83, 0x81, 0x13, 0x5b, 0xfd, 0x3a, 0xcc, 0x89, 0xf1, 0x76,
	0x5a, 0x8f, 0x0f, 0xb3, 0x48, 0x5d, 0x80, 0x0b, 0x11, 0xeb, 0x13, 0x5b, 0x35, 0x40, 0x2e, 0x62,
	0x27, 0x28, 0xc8, 0x63, 0x47, 0x37, 0x1b, 0x5f, 0xe4, 0xf6, 0x0d, 0x58, 0x8a, 0xdd, 0x85, 0xd8,
	0x68, 0x1f, 0x78, 0xdf, 0xab, 0x19, 0x6c, 0x2c, 0x2b, 0x29, 0xd2, 0x29, 0x4b, 0x0d, 0x4f, 0x7f,
	0xbe, 0xa4, 0xfa, 0x35, 0xb8, 0x90, 0x33, 0x0c, 0x2a, 0x32, 0x8f, 0xa9, 0x97, 0x2b, 0x4e, 0xab,
	0xcd, 0xbc, 0x19, 0xb8, 0xc0, 0x78, 0x61, 0x1c, 0x74, 0xa4, 0x42, 0xa7, 0x20, 0x5c, 0x60, 0x6f,
	0xc0, 0x7c, 0xd4, 0xea, 0xc4, 0x1e, 0x72, 0xa5, 0xab, 0x27, 0xb0, 0x58, 0xc1, 0x4e, 0x68, 0x22,
	0xbd, 0xbe, 0x29, 0xb4, 0xe1, 0xed, 0x00, 0x13, 0x13, 0x3a, 0x27, 0xa2, 0x1d, 0x38, 0x16, 0xd7,
	0x53, 0x4d, 0x90, 0xe3, 0x76, 0x1a, 0x0a, 0xf3, 0x94, 0x5b, 0xad, 0xc0, 0x45, 0xda, 0xb5, 0x35,
	0x1a, 0x7c, 0x37, 0x26, 0x28, 0xba, 0xfd, 0x60, 0x19, 0x3f, 0x56, 0x0d, 0x58, 0x1e, 0x20, 0x27,
	0xb6, 0x7f, 0x26, 0x42, 0xbb, 0x79, 0xaa, 0x33, 0x61, 0x29, 0xf6, 0xa1, 0x04, 0x72, 0xde, 0x24,
	0x4e, 0xdb, 0x3c, 0xea, 0x30, 0xcd, 0x6a, 0xeb, 0xf4, 0x45, 0x3f, 0x08, 0x25, 0xf5, 0x92, 0x50,
	0x96, 0x61, 0x29, 0x16, 0x09, 0xb1, 0xd5, 0xaf, 0xc2, 0x25, 0xa1, 0xcb, 0xf5, 0x35, 0xb1, 0x21,
	0x38, 0x2d, 0xe9, 0x0d, 0xf3, 0xb1, 0x04, 0xea, 0xb0, 0xc5, 0x92, 0x5f, 0xac, 0x5f, 0x88, 0xf5,
	0x6b, 0x70, 0x45, 0x3c, 0xee, 0xbe, 0xb4, 0xf5, 0xc2, 0xc2, 0x82, 0xab, 0x09, 0xf4, 0x88, 0x1d,
	0xe8, 0x67, 0x84, 0x10, 0x39, 0x45, 0x3f, 0xc3, 0x70, 0x95, 0xe1, 0xba, 0x58, 0x80, 0x3c, 0x4d,
	0x21, 0x2e, 0xf5, 0x63, 0x7c, 0x4a, 0xf7, 0x5b, 0xb0, 0x9e, 0x74, 0x4d, 0x62, 0xf7, 0xda, 0x6f,
	0xc1, 0x8a, 0xc4, 0xed, 0x37, 0xb3, 0xe1, 0x57, 0x12, 0x2c, 0xf4, 0x69, 0xf1, 0x92, 0x17, 0x70,
	0x93, 0xf0, 0x98, 0x39, 0x85, 0x9b, 0xd8, 0xa3, 0x26, 0x08, 0x32, 0xf5, 0x12, 0x20, 0x97, 0x59,
	0xa5, 0xcf, 0x35, 0x1a, 0x41, 0x80, 0xde, 0xb9, 0x3b, 0x5e, 0xb9, 0x88, 0x12, 0x13, 0x1b, 0x55,
	0x61, 0xce, 0xb7, 0x83, 0x5f, 0x06, 0xa2, 0xbf, 0xd4, 0x41, 0x50, 0xdc, 0xfb, 0x04, 0xe9, 0x7d,
	0x2b, 0xab, 0xff, 0x4e, 0xc1, 0xec, 0x03, 0x82, 0xdb, 0x81, 0x18, 0x46, 0x6b, 0x30, 0xd9, 0x21,
	0xb8, 0xad, 0x45, 0x15, 0x43, 0xa0, 0x92, 0x22, 0x2f, 0x88, 0xeb, 0x30, 0xdd, 0xd3, 0xeb, 0xa7,
	0xc8, 0x3c, 0xcd, 0x2f, 0x81, 0x0a, 0x89, 0x26, 0x36, 0xce, 0x0e, 0x22, 0x36, 0x68, 0xf9, 0x7e,
	0xd2, 0xea, 0xd4, 0x4e, 0x70, 0x5b, 0x33, 0x89, 0x56, 0xd3, 0xc9, 0x09, 0x63, 0x42, 0xc6, 0xbc,
	0xf2, 0xed, 0x0a, 0x4b, 0x64, 0x57, 0x27, 0x27, 0xe8, 0x36, 0xcc, 0x7a, 0xda, 0x54, 0x95, 0x6f,
	0x30, 0x2a, 0x10, 0x52, 0xde, 0x62, 0x54, 0x9b, 0xad, 0xaf, 0xc2, 0xb8, 0x63, 0x36, 0x31, 0x71,
	0xf4, 0xa6, 0x9d, 0x1d, 0x53, 0xa4, 0x1e, 0x71, 0xe6, 0x0f, 0xab, 0x7f, 0x96, 0x60, 0x3a, 0x4f,
	0x1f, 0xa8, 0x2d, 0xc7, 0xc1, 0x6d, 0x56, 0x82, 0xe7, 0x21, 0xdd, 0x09, 0xf9, 0x98, 0x0e, 0xa0,
	0xcb, 0x00, 0xb5, 0x56, 0xf3, 0xa8, 0xd5, 0x7f, 0xd1, 0x8c, 0xb3, 0x71, 0x7a, 0xc9, 0xb0, 0xb7,
	0x76, 0xc7, 0x6c, 0x18, 0xf4, 0x94, 0xd2, 0x81, 0xb7, 0x36, 0x1d, 0xe5, 0x47, 0x64, 0x12, 0x8d,
	0xeb, 0xb4, 0x9e, 0x5a, 0xb8, 0xcd, 0x9c, 0xea, 0xd9, 0x3c, 0x69, 0x92, 0x22, 0x15, 0x1d, 0x50,
	0x09, 0x52, 0x60, 0xcc, 0x77, 0xbd, 0x48, 0x2b, 0xf9, 0xa3, 0xea, 0x1e, 0xcc, 0x04, 0xd0, 0x73,
	0x52, 0xc4, 0x8f, 0x01, 0xb7, 0x3d, 0x11, 0x5f, 0xa8, 0x7d, 0xc1, 0x55, 0x1e, 0xf3, 0xc2, 0x42,
	0x7d, 0x07, 0x32, 0xfb, 0x3a, 0xe1, 0x08, 0x76, 0x3a, 0xf5, 0xfa, 0x20, 0x6f, 0x88, 0x86, 0xa6,
	0x22, 0x0c, 0x55, 0x1f, 0xc3, 0x6c, 0x68, 0x31, 0x62, 0xbf, 0xf4, 0x6a, 0xd4, 0xf9, 0x47, 0x9d,
	0x7a, 0x5d, 0x7b, 0xa2, 0x37, 0x3a, 0x21, 0x16, 0x94, 0x8e, 0x3f, 0xa4, 0xc3, 0xea, 0xbb, 0x30,
	0x5f, 0xc4, 0x0e, 0xdb, 0xd1, 0xf5, 0xc8, 0x7f, 0x6d, 0xc5, 0xf3, 0x34, 0x2c, 0x44, 0xae, 0xc9,
	0xdf, 0x27, 0xfe, 0x64, 0x29, 0x0a, 0xf4, 0x57, 0x60, 0xae, 0xa9, 0x13, 0xc2, 0x80, 0x37, 0x71,
	0xf3, 0x08, 0xb7, 0xbd, 0xd8, 0xe9, 0xc5, 0x22, 0xf2, 0x34, 0xf6, 0x99, 0x02, 0x0b, 0xa2, 0xb0,
	0xb1, 0x52, 0x84, 0xb1, 0x34, 0x7b, 0x98, 0x52, 0x5b, 0xb7, 0x1e, 0xb9, 0x9a, 0x23, 0x82, 0xe6,
	0x14, 0x15, 0x96, 0x75, 0xeb, 0x11, 0xd7, 0xbe, 0x0a, 0x13, 0x1d, 0x8b, 0x96, 0x2a, 0xae, 0x79,
	0x56, 0xd0, 0x04, 0x26, 0xe0, 0x6a, 0x37, 0x21, 0xc3, 0xd5, 0x84, 0x55, 0xcf, 0x09, 0xba, 0xd3,
	0x4c, 0xda, 0x5b, 0xf6, 0x26, 0x64, 0x6a, 0x9d, 0x76, 0x5b, 0x13, 0x2f, 0xa1, 0x51, 0x51, 0x9f,
	0x4a, 0x73, 0xbd, 0x9b, 0xfb, 0x1a, 0x4c, 0x31, 0x7d, 0xbf, 0x07, 0x17, 0xd3, 0x72, 0x82, 0x8a,
	0x2a, 0xee, 0xc3, 0xe6, 0x06, 0xcc, 0x08, 0x9a, 0xac, 0x48, 0x8d, 0x8b, 0xe6, 0xf9, 0xba, 0xb4,
	0x52, 0xa9, 0xff, 0x0f, 0x8b, 0x45, 0xec, 0xd0, 0xd8, 0x76, 0x0f, 0x49, 0xec, 0x51, 0x62, 0x0e,
	0x5f, 0x6d, 0xb1, 0x07, 0x45, 0xe4, 0xa4, 0x01, 0xa1, 0x7a, 0xb7, 0xbf, 0xf9, 0x18, 0x92, 0x5f,
	0x7e, 0xcb, 0xf1, 0x91, 0xc4, 0xfa, 0x6a, 0xaa, 0xf2, 0x90, 0xd7, 0xaa, 0xa2, 0xdb, 0xaa, 0x0f,
	0x8a, 0xd1, 0x70, 0xf1, 0x4f, 0xc5, 0x14, 0xff, 0xc8, 0x02, 0x29, 0xfe, 0xe2, 0x11, 0x2e, 0x90,
	0xd4, 0x01, 0x71, 0x70, 0x12, 0x38, 0xc0, 0xe5, 0xd6, 0xa4, 0x64, 0x0e, 0x60, 0x1b, 0x7e, 0x22,
	0xc1, 0x2c, 0x4b, 0x25, 0x1a, 0x41, 0x15, 0x47, 0x67, 0xa3, 0xc3, 0xf3, 0x28, 0x14, 0xbc, 0xa9,
	0x98, 0xe0, 0x4d, 0x94, 0x36, 0x37, 0x21, 0xf3, 0xd4, 0xb4, 0x34, 0x72, 0xd2, 0x6a, 0x3b, 0xa6,
	0xc1, 0xf3, 0x51, 0xcc, 0x9b, 0xe9, 0xa7, 0xa6, 0x55, 0xe1, 0x42, 0xf6, 0x6a, 0xf8, 0x36, 0x4c,
	0xf9, 0x25, 0xac, 0xda, 0xb2, 0xef, 0x53, 0xb4, 0x86, 0xde, 0xe5, 0xbf, 0x87, 0x88, 0x4c, 0xf7,
	0xa8, 0xa1, 0x77, 0xd9, 0x4f, 0x21, 0xef, 0x00, 0x62, 0xc0, 0xe9, 0x9e, 0xc4, 0xd1, 0x63, 0x23,
	0xa5, 0xcf, 0x11, 0xe5, 0xd9, 0xc0, 0x3c, 0x16, 0x32, 0xbf, 0x95, 0x20, 0xbb, 0xa3, 0x3b, 0xb5,
	0x13, 0xaf, 0x08, 0x79, 0x38, 0x06, 0x45, 0xcc, 0x3a, 0x4c, 0xf3, 0x9f, 0x78, 0x7c, 0xa0, 0x81,
	0x36, 0x80, 0xc9, 0xf2, 0x2e, 0xda, 0x35, 0x98, 0xc4, 0x96, 0xd1, 0xd3, 0x14, 0x03, 0x06, 0xb0,
	0x65, 0x78, 0x7a, 0x2a, 0x80, 0xd3, 0xb2, 0x35, 0xab, 0xdf, 0x63, 0x63, 0x4e, 0xcb, 0xbe, 0xcf,
	0x7c, 0x55, 0x86, 0xc5, 0x18, 0xac, 0xc4, 0x46, 0xaf, 0xc3, 0xb8, 0xd3, 0xb2, 0x2d, 0xb1, 0x49,
	0xca, 0x86, 0xbd, 0xe1, 0x4f, 0xa0, 0x6b, 0x5a, 0xcc, 0x01, 0xff, 0x90, 0x60, 0x2e, 0x47, 0xba,
	0x56, 0xed, 0x01, 0x3d, 0xe8, 0x5d, 0xbd, 0x51, 0xbb, 0xdf, 0x72, 0xcc, 0x7a, 0x37, 0xe9, 0x5b,
	0xe1, 0x06, 0xcc, 0x10, 0xd3, 0x3a, 0x6e, 0x60, 0x4d, 0x78, 0xf7, 0x0b, 0x75, 0x84, 0x0b, 0xbd,
	0xaa, 0x93, 0x85, 0x11, 0xb7, 0x1f, 0xea, 0xa9, 0xb0, 0x11, 0xb4, 0x01, 0xe7, 0x8f, 0xa8, 0x6d,
	0x5a, 0x90, 0xd2, 0x1b, 0x61, 0x94, 0x5e, 0x86, 0x89, 0x2a, 0x3d, 0x5e, 0x2f, 0x10, 0x25, 0xe2,
	0x6f, 0x6c, 0x5e, 0x94, 0xac, 0xff, 0x44, 0x82, 0xb9, 0x72, 0x21, 0x7f, 0x98, 0xdb, 0x7d, 0xa7,
	0x50, 0xd5, 0x2a, 0xd5, 0x5c, 0xb1, 0xa0, 0x55, 0xdf, 0x3b, 0x2c, 0xa0, 0x25, 0x58, 0x08, 0x8f,
	0xe7, 0x0b, 0xf7, 0x72, 0x0f, 0xf6, 0xaa, 0x19, 0x29, 0x4a, 0xb8, 0x77, 0x50, 0xad, 0x16, 0xca,
	0xef, 0x65, 0x52, 0x48, 0x85, 0x95, 0xb0, 0x70, 0x3f, 0x57, 0xa9, 0x68, 0xc5, 0x07, 0xa5, 0xbd,
	0xfc, 0xce, 0x83, 0x7b, 0xf7, 0x32, 0xe9, 0xa8, 0x05, 0x0e, 0xcb, 0x85, 0xc3, 0x5c, 0xb9, 0x90,
	0x19, 0x59, 0xff, 0x8b, 0x04, 0xe7, 0x7b, 0xd2, 0x62, 0xe9, 0x5e, 0x95, 0x43, 0xca, 0x8a, 0x50,
	0xd9, 0xf0, 0xfd, 0x83, 0xfd, 0xdc, 0x5e, 0x46, 0x42, 0x8b, 0x70, 0x21, 0x24, 0xc9, 0x17, 0x76,
	0xca, 0xa5, 0x4a, 0x26, 0x85, 0x64, 0x98, 0x0f, 0x89, 0x1e, 0x1e, 0x3c, 0xd8, 0x7d, 0xbb, 0x50,
	0xce, 0xa4, 0xd1, 0x32, 0x2c, 0x86, 0x64, 0xe5, 0x42, 0x3e, 0x5f, 0xca, 0xed, 0x1f, 0xdc, 0xcf,
	0x67, 0x46, 0xd0, 0x2a, 0x2c, 0x85, 0xc4, 0xcc, 0x84, 0xca, 0xdb, 0x07, 0xe5, 0x6a, 0x29, 0x9f,
	0x39, 0x1b, 0x01, 0x68, 0xbf, 0x90, 0xcf, 0xed, 0x65, 0xce, 0xad, 0x7f, 0x4f, 0x82, 0x8b, 0x21,
	0xd1, 0xe1, 0x5e, 0xae, 0x7a, 0xef, 0xa0, 0xbc, 0xcf, 0x6d, 0xe9, 0x5f, 0xdb, 0x97, 0xe7, 0xf6,
	0xa8, 0x49, 0x97, 0x61, 0x35, 0x56, 0xe1, 0x7e, 0xbe, 0x7c, 0x50, 0xca, 0x67, 0x52, 0x83, 0x56,
	0x29, 0x1d, 0x54, 0x32, 0xe9, 0x3b, 0x3f, 0x5e, 0x86, 0x71, 0xbf, 0x0a, 0xa2, 0x5f, 0x4b, 0x7d,
	0x0c, 0xab, 0x77, 0x29, 0xa2, 0xab, 0x42, 0x12, 0xc4, 0x33, 0xfb, 0xf2, 0x5a, 0x12, 0x35, 0x62,
	0xab, 0x5b, 0x1f, 0x3c, 0x7f, 0x91, 0x96, 0x7e, 0xf4, 0xfc, 0x45, 0xfa, 0x9c, 0xb5, 0x79, 0xb4,
	0x89, 0x37, 0x7f, 0xfa, 0xfc, 0x45, 0xfa, 0x95, 0x0d, 0x4b, 0xd9, 0xa2, 0xef, 0x81, 0x6d, 0x65,
	0xe3, 0x48, 0xd9, 0x62, 0x05, 0x40, 0xa1, 0xb1, 0xb9, 0xad, 0x6c, 0x60, 0x65, 0x0b, 0x5b, 0x06,
	0xff, 0x0f, 0x7d, 0x22, 0xf5, 0x51, 0xaa, 0x91, 0x40, 0xe3, 0x69, 0xfd, 0x00, 0xd0, 0x01, 0x24,
	0xba, 0x7a, 0x8f, 0x02, 0x4d, 0x51, 0xa0, 0x63, 0xd6, 0xa6, 0xe1, 0x43, 0xbd, 0x45, 0xa1, 0x9a,
	0xc6, 0xb6, 0xb2, 0x61, 0x28, 0x5b, 0x06, 0x26, 0xb5, 0x61, 0x90, 0xdf, 0x0f, 0xf2, 0x91, 0x3e,
	0x5c, 0xf1, 0x05, 0x16, 0x43, 0xb1, 0xcb, 0x97, 0x87, 0xea, 0x10, 0x5b, 0x5d, 0xa4, 0x40, 0xd3,
	0x14, 0x68, 0xca, 0x62, 0x10, 0xc7, 0x3c, 0x88, 0xe8, 0xef, 0x12, 0xcc, 0x45, 0xd1, 0xd7, 0x81,
	0xcd, 0x63, 0xa8, 0xf5, 0xc0, 0xe6, 0x71, 0x1c, 0xb8, 0xfa, 0x98, 0x6e, 0x3e, 0x42, 0x37, 0x9f,
	0xd4, 0x37, 0x9d, 0x4d, 0x7e, 0xa4, 0xcf, 0x18, 0x8c, 0x87, 0x1b, 0xba, 0xb2, 0xa5, 0xd7, 0x68,
	0x4f, 0xb0, 0xad, 0x6c, 0x38, 0xca, 0x16, 0xad, 0x53, 0xdb, 0x4a, 0xc2, 0xa3, 0x56, 0x36, 0x9e,
	0x29, 0x5b, 0x3a, 0xaf, 0x62, 0x37, 0x14, 0xf6, 0x3b, 0xb5, 0x72, 0xd4, 0x55, 0xbe, 0xb5, 0x8d,
	0x3e, 0x97, 0x60, 0x2e, 0x8a, 0x05, 0x0e, 0x18, 0x15, 0xc3, 0xbb, 0x07, 0x8c, 0x8a, 0x25, 0xc8,
	0xbf, 0x23, 0x51, 0xab, 0xce, 0x52, 0xab, 0xa6, 0xf5, 0x4d, 0x12, 0xb2, 0xcb, 0x08, 0xd8, 0x45,
	0x94, 0x2d, 0xaf, 0xe2, 0x7e, 0xa1, 0x56, 0x7e, 0x57, 0x82, 0xd9, 0x3e, 0x9e, 0x19, 0xad, 0xc6,
	0x04, 0x84, 0x6f, 0x9f, 0x32, 0x58, 0x81, 0xd8, 0xea, 0x0d, 0x6a, 0xdb, 0x39, 0x6a, 0xdb, 0x08,
	0xb5, 0x8d, 0x5a, 0xb4, 0x18, 0x6b, 0x11, 0x72, 0x00, 0xf5, 0x53, 0xb4, 0x48, 0xdc, 0x25, 0x92,
	0x1f, 0x96, 0x2f, 0x0d, 0xd1, 0xf0, 0xe2, 0x76, 0x34, 0x32, 0x6e, 0x3f, 0x90, 0x60, 0x3e, 0x9a,
	0x76, 0x45, 0x57, 0x84, 0x85, 0x63, 0x39, 0x60, 0xf9, 0x6a, 0x02, 0x2d, 0x0f, 0xc2, 0x58, 0x24,
	0x84, 0x1f, 0x4a, 0xac, 0x65, 0x8f, 0xa6, 0x5b, 0xd1, 0x2b, 0x62, 0x67, 0x30, 0x80, 0xb4, 0x95,
	0xaf, 0x25, 0x53, 0xf4, 0xb0, 0x8c, 0x47, 0x62, 0xf9, 0xb9, 0x04, 0x0b, 0x31, 0x4c, 0x68, 0xa0,
	0xea, 0xc5, 0xf3, 0xb6, 0x81, 0xaa, 0x37, 0x88, 0x54, 0xbd, 0x4d, 0x51, 0x00, 0x8b, 0x0e, 0xcb,
	0x8d, 0xf7, 0x65, 0x8a, 0x83, 0xb6, 0xc8, 0x0a, 0x0b, 0x8f, 0x67, 0x2c, 0x54, 0xd8, 0x0c, 0x3a,
	0x80, 0x7e, 0x29, 0xc1, 0xca, 0x60, 0xea, 0x14, 0xdd, 0x08, 0x39, 0x61, 0x20, 0x65, 0x2b, 0x6f,
	0x9c, 0x42, 0x9b, 0xd8, 0xaa, 0x4a, 0x11, 0x4f, 0x08, 0x7e, 0x9b, 0xa5, 0x78, 0x45, 0x88, 0x0a,
	0xfa, 0x99, 0xc4, 0xb9, 0xe2, 0x81, 0x24, 0x29, 0xba, 0x15, 0x73, 0x56, 0x71, 0xd4, 0xab, 0x7c,
	0xfb, 0x74, 0x13, 0x88, 0xad, 0xce, 0x50, 0xb0, 0x93, 0x14, 0xec, 0x19, 0x0a, 0xf5, 0x0c, 0xfa,
	0xa3, 0x04, 0x6b, 0xc9, 0xa8, 0x4f, 0xf4, 0x5a, 0x70, 0xb7, 0x64, 0x0c, 0xac, 0xfc, 0xfa, 0x4b,
	0xcc, 0x22, 0xb6, 0xba, 0x4c, 0x81, 0x4e, 0x31, 0xaf, 0xea, 0xcc, 0xab, 0x93, 0x62, 0x8d, 0x40,
	0xdf, 0x97, 0x20, 0x1b, 0xc7, 0x3e, 0xa2, 0xb5, 0x3e, 0xb7, 0x44, 0x32, 0x98, 0xf2, 0x2b, 0x89,
	0xf4, 0xbc, 0xd4, 0x98, 0x8e, 0x4c, 0x8d, 0x6f, 0xc0, 0x84, 0x40, 0x30, 0xa1, 0x45, 0x31, 0xcc,
	0x03, 0xb4, 0x99, 0x2c, 0xc7, 0x89, 0x88, 0xad, 0xca, 0x74, 0x83, 0x19, 0xb6, 0x41, 0x87, 0x6d,
	0x30, 0xbe, 0xd1, 0x51, 0xb6, 0x3a, 0x6c, 0x87, 0x0e, 0x4c, 0x05, 0x78, 0x22, 0xb4, 0x24, 0x5e,
	0x21, 0x21, 0x3a, 0x4a, 0xbe, 0x18, 0x2f, 0x24, 0xb6, 0x7a, 0x9d, 0xee, 0x93, 0x61, 0xd9, 0xd5,
	0x71, 0xb3, 0x6b, 0xde, 0xdf, 0x89, 0x66, 0x16, 0x7b, 0x60, 0xb1, 0xb4, 0xfa, 0x81, 0xc4, 0xbf,
	0x46, 0x0a, 0x11, 0x3b, 0xe8, 0x52, 0xd0, 0x69, 0x11, 0x64, 0x92, 0xac, 0x0e, 0x53, 0xf1, 0x90,
	0xcc, 0x26, 0x42, 0x42, 0x8b, 0x71, 0x34, 0x0f, 0x11, 0x28, 0xc6, 0xb1, 0xfc, 0x46, 0xa0, 0x18,
	0xc7, 0x13, 0x1a, 0xfc, 0x94, 0x91, 0x70, 0x08, 0x63, 0x1e, 0x20, 0xf4, 0x0b, 0x7e, 0x1f, 0x44,
	0x30, 0x01, 0xe1, 0xfb, 0x20, 0x9a, 0xbb, 0x08, 0xdf, 0x07, 0x31, 0x94, 0x82, 0x7a, 0x97, 0x42,
	0x38, 0xcf, 0x9a, 0x53, 0xea, 0x95, 0x2e, 0x83, 0x71, 0x25, 0xe0, 0x17, 0x91, 0xea, 0xd8, 0x56,
	0x36, 0xba, 0xca, 0x96, 0x69, 0xd5, 0x5b, 0xdb, 0xe8, 0xf7, 0x12, 0xa0, 0xfe, 0x4f, 0x68, 0x02,
	0x37, 0x65, 0xe4, 0xc7, 0x3a, 0x81, 0x9b, 0x32, 0xfa, 0x1b, 0x1c, 0xb5, 0x4c, 0x61, 0xcd, 0x51,
	0x58, 0x70, 0xbc, 0x69, 0x6d, 0x3a, 0x9b, 0xb6, 0x7b, 0x71, 0xdf, 0xdd, 0x38, 0xe6, 0xa5, 0x99,
	0x83, 0xe9, 0xf5, 0x1c, 0xbd, 0x3e, 0xc4, 0x56, 0xb6, 0x3c, 0x36, 0xd6, 0xbd, 0xdb, 0x69, 0x93,
	0xb1, 0x4d, 0x33, 0x38, 0x13, 0xfe, 0x68, 0x0d, 0xad, 0x84, 0x22, 0x27, 0xf4, 0x29, 0x9c, 0xbc,
	0x3a, 0x50, 0x4e, 0x6c, 0xf5, 0xff, 0x28, 0xd2, 0x0b, 0xec, 0x0c, 0x8f, 0x19, 0x42, 0x45, 0x40,
	0xc8, 0xba, 0x1c, 0x8d, 0x7f, 0x91, 0x78, 0xd4, 0xd5, 0x6a, 0xad, 0x66, 0x53, 0xdf, 0x46, 0x16,
	0xcc, 0x84, 0x3e, 0x56, 0x43, 0xcb, 0xd1, 0x37, 0x80, 0xfb, 0x15, 0x9c, 0xbc, 0x32, 0x48, 0x4c,
	0x6c, 0xf5, 0x22, 0x05, 0x31, 0x2f, 0xd4, 0xae, 0x09, 0xa1, 0x76, 0xa1, 0x8f, 0x24, 0x98, 0x2e,
	0x06, 0x7e, 0x38, 0x47, 0x57, 0x63, 0x6a, 0x64, 0xf0, 0x27, 0x7c, 0x79, 0x2d, 0x89, 0x9a, 0xd7,
	0x61, 0x2d, 0x24, 0xed, 0xb0, 0x3e, 0x94, 0xe0, 0xe2, 0xa0, 0x8f, 0x1a, 0xd0, 0xfa, 0x90, 0x96,
	0x55, 0xf8, 0x6c, 0x43, 0x7e, 0x35, 0xb1, 0xae, 0x77, 0x19, 0x65, 0x85, 0xcb, 0x88, 0xc0, 0x85,
	0x48, 0x7e, 0x04, 0x89, 0x5d, 0x73, 0x1c, 0xdb, 0x23, 0x5f, 0x19, 0xae, 0xe4, 0x6d, 0xba, 0xd8,
	0xdb, 0x54, 0x3e, 0xf7, 0xe1, 0xf3, 0x17, 0xe9, 0x4f, 0x9e, 0xee, 0x64, 0x3e, 0xfd, 0x6c, 0x45,
	0xfa, 0xeb, 0x67, 0x2b, 0xd2, 0xbf, 0x3e, 0x5b, 0x91, 0x3e, 0xfe, 0x7c, 0xe5, 0xcc, 0x7f, 0x02,
	0x00, 0x00, 0xff, 0xff, 0xc7, 0xad, 0xbe, 0x41, 0xe6, 0x2d, 0x00, 0x00,
}
