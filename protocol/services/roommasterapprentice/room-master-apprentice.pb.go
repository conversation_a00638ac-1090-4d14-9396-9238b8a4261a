// Code generated by protoc-gen-go. DO NOT EDIT.
// source: room-master-apprentice/room-master-apprentice.proto

package roommasterapprentice // import "golang.52tt.com/protocol/services/roommasterapprentice"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BatchAddMasterReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddMasterReq) Reset()         { *m = BatchAddMasterReq{} }
func (m *BatchAddMasterReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddMasterReq) ProtoMessage()    {}
func (*BatchAddMasterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{0}
}
func (m *BatchAddMasterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddMasterReq.Unmarshal(m, b)
}
func (m *BatchAddMasterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddMasterReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddMasterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddMasterReq.Merge(dst, src)
}
func (m *BatchAddMasterReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddMasterReq.Size(m)
}
func (m *BatchAddMasterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddMasterReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddMasterReq proto.InternalMessageInfo

func (m *BatchAddMasterReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchAddMasterResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddMasterResp) Reset()         { *m = BatchAddMasterResp{} }
func (m *BatchAddMasterResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddMasterResp) ProtoMessage()    {}
func (*BatchAddMasterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{1}
}
func (m *BatchAddMasterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddMasterResp.Unmarshal(m, b)
}
func (m *BatchAddMasterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddMasterResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddMasterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddMasterResp.Merge(dst, src)
}
func (m *BatchAddMasterResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddMasterResp.Size(m)
}
func (m *BatchAddMasterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddMasterResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddMasterResp proto.InternalMessageInfo

type BatchDelMasterReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelMasterReq) Reset()         { *m = BatchDelMasterReq{} }
func (m *BatchDelMasterReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelMasterReq) ProtoMessage()    {}
func (*BatchDelMasterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{2}
}
func (m *BatchDelMasterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelMasterReq.Unmarshal(m, b)
}
func (m *BatchDelMasterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelMasterReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelMasterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelMasterReq.Merge(dst, src)
}
func (m *BatchDelMasterReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelMasterReq.Size(m)
}
func (m *BatchDelMasterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelMasterReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelMasterReq proto.InternalMessageInfo

func (m *BatchDelMasterReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchDelMasterResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelMasterResp) Reset()         { *m = BatchDelMasterResp{} }
func (m *BatchDelMasterResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelMasterResp) ProtoMessage()    {}
func (*BatchDelMasterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{3}
}
func (m *BatchDelMasterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelMasterResp.Unmarshal(m, b)
}
func (m *BatchDelMasterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelMasterResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelMasterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelMasterResp.Merge(dst, src)
}
func (m *BatchDelMasterResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelMasterResp.Size(m)
}
func (m *BatchDelMasterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelMasterResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelMasterResp proto.InternalMessageInfo

// 是师父&在收徒时间内
type IsMasterInValidTimeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsMasterInValidTimeReq) Reset()         { *m = IsMasterInValidTimeReq{} }
func (m *IsMasterInValidTimeReq) String() string { return proto.CompactTextString(m) }
func (*IsMasterInValidTimeReq) ProtoMessage()    {}
func (*IsMasterInValidTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{4}
}
func (m *IsMasterInValidTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsMasterInValidTimeReq.Unmarshal(m, b)
}
func (m *IsMasterInValidTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsMasterInValidTimeReq.Marshal(b, m, deterministic)
}
func (dst *IsMasterInValidTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsMasterInValidTimeReq.Merge(dst, src)
}
func (m *IsMasterInValidTimeReq) XXX_Size() int {
	return xxx_messageInfo_IsMasterInValidTimeReq.Size(m)
}
func (m *IsMasterInValidTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsMasterInValidTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsMasterInValidTimeReq proto.InternalMessageInfo

func (m *IsMasterInValidTimeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IsMasterInValidTimeResp struct {
	IsMaster             bool     `protobuf:"varint,1,opt,name=is_master,json=isMaster,proto3" json:"is_master,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsMasterInValidTimeResp) Reset()         { *m = IsMasterInValidTimeResp{} }
func (m *IsMasterInValidTimeResp) String() string { return proto.CompactTextString(m) }
func (*IsMasterInValidTimeResp) ProtoMessage()    {}
func (*IsMasterInValidTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{5}
}
func (m *IsMasterInValidTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsMasterInValidTimeResp.Unmarshal(m, b)
}
func (m *IsMasterInValidTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsMasterInValidTimeResp.Marshal(b, m, deterministic)
}
func (dst *IsMasterInValidTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsMasterInValidTimeResp.Merge(dst, src)
}
func (m *IsMasterInValidTimeResp) XXX_Size() int {
	return xxx_messageInfo_IsMasterInValidTimeResp.Size(m)
}
func (m *IsMasterInValidTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsMasterInValidTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsMasterInValidTimeResp proto.InternalMessageInfo

func (m *IsMasterInValidTimeResp) GetIsMaster() bool {
	if m != nil {
		return m.IsMaster
	}
	return false
}

type IsMasterReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsMasterReq) Reset()         { *m = IsMasterReq{} }
func (m *IsMasterReq) String() string { return proto.CompactTextString(m) }
func (*IsMasterReq) ProtoMessage()    {}
func (*IsMasterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{6}
}
func (m *IsMasterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsMasterReq.Unmarshal(m, b)
}
func (m *IsMasterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsMasterReq.Marshal(b, m, deterministic)
}
func (dst *IsMasterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsMasterReq.Merge(dst, src)
}
func (m *IsMasterReq) XXX_Size() int {
	return xxx_messageInfo_IsMasterReq.Size(m)
}
func (m *IsMasterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsMasterReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsMasterReq proto.InternalMessageInfo

func (m *IsMasterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IsMasterResp struct {
	IsMaster             bool     `protobuf:"varint,1,opt,name=is_master,json=isMaster,proto3" json:"is_master,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsMasterResp) Reset()         { *m = IsMasterResp{} }
func (m *IsMasterResp) String() string { return proto.CompactTextString(m) }
func (*IsMasterResp) ProtoMessage()    {}
func (*IsMasterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{7}
}
func (m *IsMasterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsMasterResp.Unmarshal(m, b)
}
func (m *IsMasterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsMasterResp.Marshal(b, m, deterministic)
}
func (dst *IsMasterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsMasterResp.Merge(dst, src)
}
func (m *IsMasterResp) XXX_Size() int {
	return xxx_messageInfo_IsMasterResp.Size(m)
}
func (m *IsMasterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsMasterResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsMasterResp proto.InternalMessageInfo

func (m *IsMasterResp) GetIsMaster() bool {
	if m != nil {
		return m.IsMaster
	}
	return false
}

type DrawBalanceReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DrawBalance          int64    `protobuf:"varint,2,opt,name=draw_balance,json=drawBalance,proto3" json:"draw_balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DrawBalanceReq) Reset()         { *m = DrawBalanceReq{} }
func (m *DrawBalanceReq) String() string { return proto.CompactTextString(m) }
func (*DrawBalanceReq) ProtoMessage()    {}
func (*DrawBalanceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{8}
}
func (m *DrawBalanceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DrawBalanceReq.Unmarshal(m, b)
}
func (m *DrawBalanceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DrawBalanceReq.Marshal(b, m, deterministic)
}
func (dst *DrawBalanceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DrawBalanceReq.Merge(dst, src)
}
func (m *DrawBalanceReq) XXX_Size() int {
	return xxx_messageInfo_DrawBalanceReq.Size(m)
}
func (m *DrawBalanceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DrawBalanceReq.DiscardUnknown(m)
}

var xxx_messageInfo_DrawBalanceReq proto.InternalMessageInfo

func (m *DrawBalanceReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *DrawBalanceReq) GetDrawBalance() int64 {
	if m != nil {
		return m.DrawBalance
	}
	return 0
}

type DrawBalanceResp struct {
	Message              string   `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DrawBalanceResp) Reset()         { *m = DrawBalanceResp{} }
func (m *DrawBalanceResp) String() string { return proto.CompactTextString(m) }
func (*DrawBalanceResp) ProtoMessage()    {}
func (*DrawBalanceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{9}
}
func (m *DrawBalanceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DrawBalanceResp.Unmarshal(m, b)
}
func (m *DrawBalanceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DrawBalanceResp.Marshal(b, m, deterministic)
}
func (dst *DrawBalanceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DrawBalanceResp.Merge(dst, src)
}
func (m *DrawBalanceResp) XXX_Size() int {
	return xxx_messageInfo_DrawBalanceResp.Size(m)
}
func (m *DrawBalanceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DrawBalanceResp.DiscardUnknown(m)
}

var xxx_messageInfo_DrawBalanceResp proto.InternalMessageInfo

func (m *DrawBalanceResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DrawBalanceResp) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetUserBalanceByUserIDReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBalanceByUserIDReq) Reset()         { *m = GetUserBalanceByUserIDReq{} }
func (m *GetUserBalanceByUserIDReq) String() string { return proto.CompactTextString(m) }
func (*GetUserBalanceByUserIDReq) ProtoMessage()    {}
func (*GetUserBalanceByUserIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{10}
}
func (m *GetUserBalanceByUserIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBalanceByUserIDReq.Unmarshal(m, b)
}
func (m *GetUserBalanceByUserIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBalanceByUserIDReq.Marshal(b, m, deterministic)
}
func (dst *GetUserBalanceByUserIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBalanceByUserIDReq.Merge(dst, src)
}
func (m *GetUserBalanceByUserIDReq) XXX_Size() int {
	return xxx_messageInfo_GetUserBalanceByUserIDReq.Size(m)
}
func (m *GetUserBalanceByUserIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBalanceByUserIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBalanceByUserIDReq proto.InternalMessageInfo

func (m *GetUserBalanceByUserIDReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type GetUserBalanceByUserIDResp struct {
	TotalBalance         int64    `protobuf:"varint,1,opt,name=total_balance,json=totalBalance,proto3" json:"total_balance,omitempty"`
	AvailableBalance     int64    `protobuf:"varint,2,opt,name=available_balance,json=availableBalance,proto3" json:"available_balance,omitempty"`
	UnavailableBalance   int64    `protobuf:"varint,3,opt,name=unavailable_balance,json=unavailableBalance,proto3" json:"unavailable_balance,omitempty"`
	Status               int64    `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBalanceByUserIDResp) Reset()         { *m = GetUserBalanceByUserIDResp{} }
func (m *GetUserBalanceByUserIDResp) String() string { return proto.CompactTextString(m) }
func (*GetUserBalanceByUserIDResp) ProtoMessage()    {}
func (*GetUserBalanceByUserIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{11}
}
func (m *GetUserBalanceByUserIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBalanceByUserIDResp.Unmarshal(m, b)
}
func (m *GetUserBalanceByUserIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBalanceByUserIDResp.Marshal(b, m, deterministic)
}
func (dst *GetUserBalanceByUserIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBalanceByUserIDResp.Merge(dst, src)
}
func (m *GetUserBalanceByUserIDResp) XXX_Size() int {
	return xxx_messageInfo_GetUserBalanceByUserIDResp.Size(m)
}
func (m *GetUserBalanceByUserIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBalanceByUserIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBalanceByUserIDResp proto.InternalMessageInfo

func (m *GetUserBalanceByUserIDResp) GetTotalBalance() int64 {
	if m != nil {
		return m.TotalBalance
	}
	return 0
}

func (m *GetUserBalanceByUserIDResp) GetAvailableBalance() int64 {
	if m != nil {
		return m.AvailableBalance
	}
	return 0
}

func (m *GetUserBalanceByUserIDResp) GetUnavailableBalance() int64 {
	if m != nil {
		return m.UnavailableBalance
	}
	return 0
}

func (m *GetUserBalanceByUserIDResp) GetStatus() int64 {
	if m != nil {
		return m.Status
	}
	return 0
}

type OrderInfo struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Status               int64    `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	NowBalance           int64    `protobuf:"varint,4,opt,name=now_balance,json=nowBalance,proto3" json:"now_balance,omitempty"`
	Amount               int64    `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	Type                 int64    `protobuf:"varint,6,opt,name=type,proto3" json:"type,omitempty"`
	Describe             string   `protobuf:"bytes,7,opt,name=describe,proto3" json:"describe,omitempty"`
	Reason               string   `protobuf:"bytes,8,opt,name=reason,proto3" json:"reason,omitempty"`
	CreateTime           int64    `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           int64    `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderInfo) Reset()         { *m = OrderInfo{} }
func (m *OrderInfo) String() string { return proto.CompactTextString(m) }
func (*OrderInfo) ProtoMessage()    {}
func (*OrderInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{12}
}
func (m *OrderInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderInfo.Unmarshal(m, b)
}
func (m *OrderInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderInfo.Marshal(b, m, deterministic)
}
func (dst *OrderInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderInfo.Merge(dst, src)
}
func (m *OrderInfo) XXX_Size() int {
	return xxx_messageInfo_OrderInfo.Size(m)
}
func (m *OrderInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OrderInfo proto.InternalMessageInfo

func (m *OrderInfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *OrderInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *OrderInfo) GetStatus() int64 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *OrderInfo) GetNowBalance() int64 {
	if m != nil {
		return m.NowBalance
	}
	return 0
}

func (m *OrderInfo) GetAmount() int64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *OrderInfo) GetType() int64 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *OrderInfo) GetDescribe() string {
	if m != nil {
		return m.Describe
	}
	return ""
}

func (m *OrderInfo) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *OrderInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *OrderInfo) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetOrderListByUserIDReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Limit                int64    `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	LastId               int64    `protobuf:"varint,3,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderListByUserIDReq) Reset()         { *m = GetOrderListByUserIDReq{} }
func (m *GetOrderListByUserIDReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderListByUserIDReq) ProtoMessage()    {}
func (*GetOrderListByUserIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{13}
}
func (m *GetOrderListByUserIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderListByUserIDReq.Unmarshal(m, b)
}
func (m *GetOrderListByUserIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderListByUserIDReq.Marshal(b, m, deterministic)
}
func (dst *GetOrderListByUserIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderListByUserIDReq.Merge(dst, src)
}
func (m *GetOrderListByUserIDReq) XXX_Size() int {
	return xxx_messageInfo_GetOrderListByUserIDReq.Size(m)
}
func (m *GetOrderListByUserIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderListByUserIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderListByUserIDReq proto.InternalMessageInfo

func (m *GetOrderListByUserIDReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetOrderListByUserIDReq) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetOrderListByUserIDReq) GetLastId() int64 {
	if m != nil {
		return m.LastId
	}
	return 0
}

type LoadMore struct {
	HasNext              bool     `protobuf:"varint,1,opt,name=has_next,json=hasNext,proto3" json:"has_next,omitempty"`
	LastId               int64    `protobuf:"varint,2,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	Limit                int64    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LoadMore) Reset()         { *m = LoadMore{} }
func (m *LoadMore) String() string { return proto.CompactTextString(m) }
func (*LoadMore) ProtoMessage()    {}
func (*LoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{14}
}
func (m *LoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LoadMore.Unmarshal(m, b)
}
func (m *LoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LoadMore.Marshal(b, m, deterministic)
}
func (dst *LoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LoadMore.Merge(dst, src)
}
func (m *LoadMore) XXX_Size() int {
	return xxx_messageInfo_LoadMore.Size(m)
}
func (m *LoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_LoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_LoadMore proto.InternalMessageInfo

func (m *LoadMore) GetHasNext() bool {
	if m != nil {
		return m.HasNext
	}
	return false
}

func (m *LoadMore) GetLastId() int64 {
	if m != nil {
		return m.LastId
	}
	return 0
}

func (m *LoadMore) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetOrderListByUserIDResp struct {
	UserId               uint32       `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderList            []*OrderInfo `protobuf:"bytes,2,rep,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	LoadMore             *LoadMore    `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetOrderListByUserIDResp) Reset()         { *m = GetOrderListByUserIDResp{} }
func (m *GetOrderListByUserIDResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderListByUserIDResp) ProtoMessage()    {}
func (*GetOrderListByUserIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{15}
}
func (m *GetOrderListByUserIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderListByUserIDResp.Unmarshal(m, b)
}
func (m *GetOrderListByUserIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderListByUserIDResp.Marshal(b, m, deterministic)
}
func (dst *GetOrderListByUserIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderListByUserIDResp.Merge(dst, src)
}
func (m *GetOrderListByUserIDResp) XXX_Size() int {
	return xxx_messageInfo_GetOrderListByUserIDResp.Size(m)
}
func (m *GetOrderListByUserIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderListByUserIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderListByUserIDResp proto.InternalMessageInfo

func (m *GetOrderListByUserIDResp) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetOrderListByUserIDResp) GetOrderList() []*OrderInfo {
	if m != nil {
		return m.OrderList
	}
	return nil
}

func (m *GetOrderListByUserIDResp) GetLoadMore() *LoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetOrderInfoByUserIDOrderIDReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderInfoByUserIDOrderIDReq) Reset()         { *m = GetOrderInfoByUserIDOrderIDReq{} }
func (m *GetOrderInfoByUserIDOrderIDReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderInfoByUserIDOrderIDReq) ProtoMessage()    {}
func (*GetOrderInfoByUserIDOrderIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{16}
}
func (m *GetOrderInfoByUserIDOrderIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderInfoByUserIDOrderIDReq.Unmarshal(m, b)
}
func (m *GetOrderInfoByUserIDOrderIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderInfoByUserIDOrderIDReq.Marshal(b, m, deterministic)
}
func (dst *GetOrderInfoByUserIDOrderIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderInfoByUserIDOrderIDReq.Merge(dst, src)
}
func (m *GetOrderInfoByUserIDOrderIDReq) XXX_Size() int {
	return xxx_messageInfo_GetOrderInfoByUserIDOrderIDReq.Size(m)
}
func (m *GetOrderInfoByUserIDOrderIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderInfoByUserIDOrderIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderInfoByUserIDOrderIDReq proto.InternalMessageInfo

func (m *GetOrderInfoByUserIDOrderIDReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetOrderInfoByUserIDOrderIDReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetOrderInfoByUserIDOrderIDResp struct {
	OrderInfo            *OrderInfo `protobuf:"bytes,1,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetOrderInfoByUserIDOrderIDResp) Reset()         { *m = GetOrderInfoByUserIDOrderIDResp{} }
func (m *GetOrderInfoByUserIDOrderIDResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderInfoByUserIDOrderIDResp) ProtoMessage()    {}
func (*GetOrderInfoByUserIDOrderIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{17}
}
func (m *GetOrderInfoByUserIDOrderIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderInfoByUserIDOrderIDResp.Unmarshal(m, b)
}
func (m *GetOrderInfoByUserIDOrderIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderInfoByUserIDOrderIDResp.Marshal(b, m, deterministic)
}
func (dst *GetOrderInfoByUserIDOrderIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderInfoByUserIDOrderIDResp.Merge(dst, src)
}
func (m *GetOrderInfoByUserIDOrderIDResp) XXX_Size() int {
	return xxx_messageInfo_GetOrderInfoByUserIDOrderIDResp.Size(m)
}
func (m *GetOrderInfoByUserIDOrderIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderInfoByUserIDOrderIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderInfoByUserIDOrderIDResp proto.InternalMessageInfo

func (m *GetOrderInfoByUserIDOrderIDResp) GetOrderInfo() *OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

type GetOrderInfoByOrderIDReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderInfoByOrderIDReq) Reset()         { *m = GetOrderInfoByOrderIDReq{} }
func (m *GetOrderInfoByOrderIDReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderInfoByOrderIDReq) ProtoMessage()    {}
func (*GetOrderInfoByOrderIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{18}
}
func (m *GetOrderInfoByOrderIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderInfoByOrderIDReq.Unmarshal(m, b)
}
func (m *GetOrderInfoByOrderIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderInfoByOrderIDReq.Marshal(b, m, deterministic)
}
func (dst *GetOrderInfoByOrderIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderInfoByOrderIDReq.Merge(dst, src)
}
func (m *GetOrderInfoByOrderIDReq) XXX_Size() int {
	return xxx_messageInfo_GetOrderInfoByOrderIDReq.Size(m)
}
func (m *GetOrderInfoByOrderIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderInfoByOrderIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderInfoByOrderIDReq proto.InternalMessageInfo

func (m *GetOrderInfoByOrderIDReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetOrderInfoByOrderIDResp struct {
	OrderInfo            *OrderInfo `protobuf:"bytes,1,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetOrderInfoByOrderIDResp) Reset()         { *m = GetOrderInfoByOrderIDResp{} }
func (m *GetOrderInfoByOrderIDResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderInfoByOrderIDResp) ProtoMessage()    {}
func (*GetOrderInfoByOrderIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{19}
}
func (m *GetOrderInfoByOrderIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderInfoByOrderIDResp.Unmarshal(m, b)
}
func (m *GetOrderInfoByOrderIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderInfoByOrderIDResp.Marshal(b, m, deterministic)
}
func (dst *GetOrderInfoByOrderIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderInfoByOrderIDResp.Merge(dst, src)
}
func (m *GetOrderInfoByOrderIDResp) XXX_Size() int {
	return xxx_messageInfo_GetOrderInfoByOrderIDResp.Size(m)
}
func (m *GetOrderInfoByOrderIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderInfoByOrderIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderInfoByOrderIDResp proto.InternalMessageInfo

func (m *GetOrderInfoByOrderIDResp) GetOrderInfo() *OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

type UpdateOrderInfoByOrderIDReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Status               int64    `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	Reason               string   `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateOrderInfoByOrderIDReq) Reset()         { *m = UpdateOrderInfoByOrderIDReq{} }
func (m *UpdateOrderInfoByOrderIDReq) String() string { return proto.CompactTextString(m) }
func (*UpdateOrderInfoByOrderIDReq) ProtoMessage()    {}
func (*UpdateOrderInfoByOrderIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{20}
}
func (m *UpdateOrderInfoByOrderIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateOrderInfoByOrderIDReq.Unmarshal(m, b)
}
func (m *UpdateOrderInfoByOrderIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateOrderInfoByOrderIDReq.Marshal(b, m, deterministic)
}
func (dst *UpdateOrderInfoByOrderIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateOrderInfoByOrderIDReq.Merge(dst, src)
}
func (m *UpdateOrderInfoByOrderIDReq) XXX_Size() int {
	return xxx_messageInfo_UpdateOrderInfoByOrderIDReq.Size(m)
}
func (m *UpdateOrderInfoByOrderIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateOrderInfoByOrderIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateOrderInfoByOrderIDReq proto.InternalMessageInfo

func (m *UpdateOrderInfoByOrderIDReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UpdateOrderInfoByOrderIDReq) GetStatus() int64 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UpdateOrderInfoByOrderIDReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type UpdateOrderInfoByOrderIDResp struct {
	Message              string   `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateOrderInfoByOrderIDResp) Reset()         { *m = UpdateOrderInfoByOrderIDResp{} }
func (m *UpdateOrderInfoByOrderIDResp) String() string { return proto.CompactTextString(m) }
func (*UpdateOrderInfoByOrderIDResp) ProtoMessage()    {}
func (*UpdateOrderInfoByOrderIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{21}
}
func (m *UpdateOrderInfoByOrderIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateOrderInfoByOrderIDResp.Unmarshal(m, b)
}
func (m *UpdateOrderInfoByOrderIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateOrderInfoByOrderIDResp.Marshal(b, m, deterministic)
}
func (dst *UpdateOrderInfoByOrderIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateOrderInfoByOrderIDResp.Merge(dst, src)
}
func (m *UpdateOrderInfoByOrderIDResp) XXX_Size() int {
	return xxx_messageInfo_UpdateOrderInfoByOrderIDResp.Size(m)
}
func (m *UpdateOrderInfoByOrderIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateOrderInfoByOrderIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateOrderInfoByOrderIDResp proto.InternalMessageInfo

func (m *UpdateOrderInfoByOrderIDResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type BindWXUserPayInfoReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Code                 string   `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	State                string   `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindWXUserPayInfoReq) Reset()         { *m = BindWXUserPayInfoReq{} }
func (m *BindWXUserPayInfoReq) String() string { return proto.CompactTextString(m) }
func (*BindWXUserPayInfoReq) ProtoMessage()    {}
func (*BindWXUserPayInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{22}
}
func (m *BindWXUserPayInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindWXUserPayInfoReq.Unmarshal(m, b)
}
func (m *BindWXUserPayInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindWXUserPayInfoReq.Marshal(b, m, deterministic)
}
func (dst *BindWXUserPayInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindWXUserPayInfoReq.Merge(dst, src)
}
func (m *BindWXUserPayInfoReq) XXX_Size() int {
	return xxx_messageInfo_BindWXUserPayInfoReq.Size(m)
}
func (m *BindWXUserPayInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BindWXUserPayInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BindWXUserPayInfoReq proto.InternalMessageInfo

func (m *BindWXUserPayInfoReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *BindWXUserPayInfoReq) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *BindWXUserPayInfoReq) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

type BindWXUserPayInfoResp struct {
	Status               string   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	OpenId               string   `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Message              string   `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindWXUserPayInfoResp) Reset()         { *m = BindWXUserPayInfoResp{} }
func (m *BindWXUserPayInfoResp) String() string { return proto.CompactTextString(m) }
func (*BindWXUserPayInfoResp) ProtoMessage()    {}
func (*BindWXUserPayInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{23}
}
func (m *BindWXUserPayInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindWXUserPayInfoResp.Unmarshal(m, b)
}
func (m *BindWXUserPayInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindWXUserPayInfoResp.Marshal(b, m, deterministic)
}
func (dst *BindWXUserPayInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindWXUserPayInfoResp.Merge(dst, src)
}
func (m *BindWXUserPayInfoResp) XXX_Size() int {
	return xxx_messageInfo_BindWXUserPayInfoResp.Size(m)
}
func (m *BindWXUserPayInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BindWXUserPayInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BindWXUserPayInfoResp proto.InternalMessageInfo

func (m *BindWXUserPayInfoResp) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *BindWXUserPayInfoResp) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *BindWXUserPayInfoResp) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *BindWXUserPayInfoResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type GetBindingInfoReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Code                 string   `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	State                string   `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBindingInfoReq) Reset()         { *m = GetBindingInfoReq{} }
func (m *GetBindingInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetBindingInfoReq) ProtoMessage()    {}
func (*GetBindingInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{24}
}
func (m *GetBindingInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindingInfoReq.Unmarshal(m, b)
}
func (m *GetBindingInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindingInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetBindingInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindingInfoReq.Merge(dst, src)
}
func (m *GetBindingInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetBindingInfoReq.Size(m)
}
func (m *GetBindingInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindingInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindingInfoReq proto.InternalMessageInfo

func (m *GetBindingInfoReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetBindingInfoReq) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *GetBindingInfoReq) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

type GetBindingInfoResp struct {
	Status               string   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	OpenId               string   `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Message              string   `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBindingInfoResp) Reset()         { *m = GetBindingInfoResp{} }
func (m *GetBindingInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetBindingInfoResp) ProtoMessage()    {}
func (*GetBindingInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{25}
}
func (m *GetBindingInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindingInfoResp.Unmarshal(m, b)
}
func (m *GetBindingInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindingInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetBindingInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindingInfoResp.Merge(dst, src)
}
func (m *GetBindingInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetBindingInfoResp.Size(m)
}
func (m *GetBindingInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindingInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindingInfoResp proto.InternalMessageInfo

func (m *GetBindingInfoResp) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *GetBindingInfoResp) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *GetBindingInfoResp) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *GetBindingInfoResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type GetActConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActConfigReq) Reset()         { *m = GetActConfigReq{} }
func (m *GetActConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetActConfigReq) ProtoMessage()    {}
func (*GetActConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{26}
}
func (m *GetActConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActConfigReq.Unmarshal(m, b)
}
func (m *GetActConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetActConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActConfigReq.Merge(dst, src)
}
func (m *GetActConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetActConfigReq.Size(m)
}
func (m *GetActConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActConfigReq proto.InternalMessageInfo

type GetActConfigResp struct {
	BeginTime            string   `protobuf:"bytes,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              string   `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	EndGetApprenticeTime string   `protobuf:"bytes,3,opt,name=EndGetApprenticeTime,proto3" json:"EndGetApprenticeTime,omitempty"`
	EndTaskTime          string   `protobuf:"bytes,4,opt,name=EndTaskTime,proto3" json:"EndTaskTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActConfigResp) Reset()         { *m = GetActConfigResp{} }
func (m *GetActConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetActConfigResp) ProtoMessage()    {}
func (*GetActConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{27}
}
func (m *GetActConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActConfigResp.Unmarshal(m, b)
}
func (m *GetActConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetActConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActConfigResp.Merge(dst, src)
}
func (m *GetActConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetActConfigResp.Size(m)
}
func (m *GetActConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActConfigResp proto.InternalMessageInfo

func (m *GetActConfigResp) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *GetActConfigResp) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GetActConfigResp) GetEndGetApprenticeTime() string {
	if m != nil {
		return m.EndGetApprenticeTime
	}
	return ""
}

func (m *GetActConfigResp) GetEndTaskTime() string {
	if m != nil {
		return m.EndTaskTime
	}
	return ""
}

// web 首页数据
type MasterInitForWebReq struct {
	MasterUid            uint32   `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MasterInitForWebReq) Reset()         { *m = MasterInitForWebReq{} }
func (m *MasterInitForWebReq) String() string { return proto.CompactTextString(m) }
func (*MasterInitForWebReq) ProtoMessage()    {}
func (*MasterInitForWebReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{28}
}
func (m *MasterInitForWebReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterInitForWebReq.Unmarshal(m, b)
}
func (m *MasterInitForWebReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterInitForWebReq.Marshal(b, m, deterministic)
}
func (dst *MasterInitForWebReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterInitForWebReq.Merge(dst, src)
}
func (m *MasterInitForWebReq) XXX_Size() int {
	return xxx_messageInfo_MasterInitForWebReq.Size(m)
}
func (m *MasterInitForWebReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterInitForWebReq.DiscardUnknown(m)
}

var xxx_messageInfo_MasterInitForWebReq proto.InternalMessageInfo

func (m *MasterInitForWebReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

type ApprenticeInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  int32    `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApprenticeInfo) Reset()         { *m = ApprenticeInfo{} }
func (m *ApprenticeInfo) String() string { return proto.CompactTextString(m) }
func (*ApprenticeInfo) ProtoMessage()    {}
func (*ApprenticeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{29}
}
func (m *ApprenticeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApprenticeInfo.Unmarshal(m, b)
}
func (m *ApprenticeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApprenticeInfo.Marshal(b, m, deterministic)
}
func (dst *ApprenticeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApprenticeInfo.Merge(dst, src)
}
func (m *ApprenticeInfo) XXX_Size() int {
	return xxx_messageInfo_ApprenticeInfo.Size(m)
}
func (m *ApprenticeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ApprenticeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ApprenticeInfo proto.InternalMessageInfo

func (m *ApprenticeInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApprenticeInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ApprenticeInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type MasterInitForWebResp struct {
	AlreadyPlayerList    []*ApprenticeInfo `protobuf:"bytes,1,rep,name=already_player_list,json=alreadyPlayerList,proto3" json:"already_player_list,omitempty"`
	RecruitTime          uint32            `protobuf:"varint,2,opt,name=recruit_time,json=recruitTime,proto3" json:"recruit_time,omitempty"`
	WithdrawableIncome   uint32            `protobuf:"varint,3,opt,name=withdrawable_income,json=withdrawableIncome,proto3" json:"withdrawable_income,omitempty"`
	TotalIncome          uint32            `protobuf:"varint,4,opt,name=total_income,json=totalIncome,proto3" json:"total_income,omitempty"`
	AvailableNum         uint32            `protobuf:"varint,5,opt,name=available_num,json=availableNum,proto3" json:"available_num,omitempty"`
	AdditionalIncome     uint32            `protobuf:"varint,6,opt,name=additional_income,json=additionalIncome,proto3" json:"additional_income,omitempty"`
	TodayIncome          uint32            `protobuf:"varint,7,opt,name=today_income,json=todayIncome,proto3" json:"today_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MasterInitForWebResp) Reset()         { *m = MasterInitForWebResp{} }
func (m *MasterInitForWebResp) String() string { return proto.CompactTextString(m) }
func (*MasterInitForWebResp) ProtoMessage()    {}
func (*MasterInitForWebResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{30}
}
func (m *MasterInitForWebResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterInitForWebResp.Unmarshal(m, b)
}
func (m *MasterInitForWebResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterInitForWebResp.Marshal(b, m, deterministic)
}
func (dst *MasterInitForWebResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterInitForWebResp.Merge(dst, src)
}
func (m *MasterInitForWebResp) XXX_Size() int {
	return xxx_messageInfo_MasterInitForWebResp.Size(m)
}
func (m *MasterInitForWebResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterInitForWebResp.DiscardUnknown(m)
}

var xxx_messageInfo_MasterInitForWebResp proto.InternalMessageInfo

func (m *MasterInitForWebResp) GetAlreadyPlayerList() []*ApprenticeInfo {
	if m != nil {
		return m.AlreadyPlayerList
	}
	return nil
}

func (m *MasterInitForWebResp) GetRecruitTime() uint32 {
	if m != nil {
		return m.RecruitTime
	}
	return 0
}

func (m *MasterInitForWebResp) GetWithdrawableIncome() uint32 {
	if m != nil {
		return m.WithdrawableIncome
	}
	return 0
}

func (m *MasterInitForWebResp) GetTotalIncome() uint32 {
	if m != nil {
		return m.TotalIncome
	}
	return 0
}

func (m *MasterInitForWebResp) GetAvailableNum() uint32 {
	if m != nil {
		return m.AvailableNum
	}
	return 0
}

func (m *MasterInitForWebResp) GetAdditionalIncome() uint32 {
	if m != nil {
		return m.AdditionalIncome
	}
	return 0
}

func (m *MasterInitForWebResp) GetTodayIncome() uint32 {
	if m != nil {
		return m.TodayIncome
	}
	return 0
}

type GetHistoryApprenticeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                int64    `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	LastId               int64    `protobuf:"varint,3,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHistoryApprenticeReq) Reset()         { *m = GetHistoryApprenticeReq{} }
func (m *GetHistoryApprenticeReq) String() string { return proto.CompactTextString(m) }
func (*GetHistoryApprenticeReq) ProtoMessage()    {}
func (*GetHistoryApprenticeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{31}
}
func (m *GetHistoryApprenticeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHistoryApprenticeReq.Unmarshal(m, b)
}
func (m *GetHistoryApprenticeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHistoryApprenticeReq.Marshal(b, m, deterministic)
}
func (dst *GetHistoryApprenticeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHistoryApprenticeReq.Merge(dst, src)
}
func (m *GetHistoryApprenticeReq) XXX_Size() int {
	return xxx_messageInfo_GetHistoryApprenticeReq.Size(m)
}
func (m *GetHistoryApprenticeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHistoryApprenticeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHistoryApprenticeReq proto.InternalMessageInfo

func (m *GetHistoryApprenticeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetHistoryApprenticeReq) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetHistoryApprenticeReq) GetLastId() int64 {
	if m != nil {
		return m.LastId
	}
	return 0
}

type DayApprenticeInfo struct {
	MonthDay             string            `protobuf:"bytes,1,opt,name=month_day,json=monthDay,proto3" json:"month_day,omitempty"`
	ApprenticeList       []*ApprenticeInfo `protobuf:"bytes,2,rep,name=apprentice_list,json=apprenticeList,proto3" json:"apprentice_list,omitempty"`
	Income               int64             `protobuf:"varint,3,opt,name=income,proto3" json:"income,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *DayApprenticeInfo) Reset()         { *m = DayApprenticeInfo{} }
func (m *DayApprenticeInfo) String() string { return proto.CompactTextString(m) }
func (*DayApprenticeInfo) ProtoMessage()    {}
func (*DayApprenticeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{32}
}
func (m *DayApprenticeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DayApprenticeInfo.Unmarshal(m, b)
}
func (m *DayApprenticeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DayApprenticeInfo.Marshal(b, m, deterministic)
}
func (dst *DayApprenticeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DayApprenticeInfo.Merge(dst, src)
}
func (m *DayApprenticeInfo) XXX_Size() int {
	return xxx_messageInfo_DayApprenticeInfo.Size(m)
}
func (m *DayApprenticeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DayApprenticeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DayApprenticeInfo proto.InternalMessageInfo

func (m *DayApprenticeInfo) GetMonthDay() string {
	if m != nil {
		return m.MonthDay
	}
	return ""
}

func (m *DayApprenticeInfo) GetApprenticeList() []*ApprenticeInfo {
	if m != nil {
		return m.ApprenticeList
	}
	return nil
}

func (m *DayApprenticeInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

type GetHistoryApprenticeResp struct {
	ApprenticeList       []*DayApprenticeInfo `protobuf:"bytes,1,rep,name=apprentice_list,json=apprenticeList,proto3" json:"apprentice_list,omitempty"`
	LoadMore             *LoadMore            `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetHistoryApprenticeResp) Reset()         { *m = GetHistoryApprenticeResp{} }
func (m *GetHistoryApprenticeResp) String() string { return proto.CompactTextString(m) }
func (*GetHistoryApprenticeResp) ProtoMessage()    {}
func (*GetHistoryApprenticeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{33}
}
func (m *GetHistoryApprenticeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHistoryApprenticeResp.Unmarshal(m, b)
}
func (m *GetHistoryApprenticeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHistoryApprenticeResp.Marshal(b, m, deterministic)
}
func (dst *GetHistoryApprenticeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHistoryApprenticeResp.Merge(dst, src)
}
func (m *GetHistoryApprenticeResp) XXX_Size() int {
	return xxx_messageInfo_GetHistoryApprenticeResp.Size(m)
}
func (m *GetHistoryApprenticeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHistoryApprenticeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHistoryApprenticeResp proto.InternalMessageInfo

func (m *GetHistoryApprenticeResp) GetApprenticeList() []*DayApprenticeInfo {
	if m != nil {
		return m.ApprenticeList
	}
	return nil
}

func (m *GetHistoryApprenticeResp) GetLoadMore() *LoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

// 监控 师父的收益和提现
type MasterMonitorInTodayReq struct {
	LimitIncome          uint32   `protobuf:"varint,1,opt,name=limit_income,json=limitIncome,proto3" json:"limit_income,omitempty"`
	LimitWithdraw        uint32   `protobuf:"varint,2,opt,name=limit_withdraw,json=limitWithdraw,proto3" json:"limit_withdraw,omitempty"`
	CheckTs              int64    `protobuf:"varint,3,opt,name=check_ts,json=checkTs,proto3" json:"check_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MasterMonitorInTodayReq) Reset()         { *m = MasterMonitorInTodayReq{} }
func (m *MasterMonitorInTodayReq) String() string { return proto.CompactTextString(m) }
func (*MasterMonitorInTodayReq) ProtoMessage()    {}
func (*MasterMonitorInTodayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{34}
}
func (m *MasterMonitorInTodayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterMonitorInTodayReq.Unmarshal(m, b)
}
func (m *MasterMonitorInTodayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterMonitorInTodayReq.Marshal(b, m, deterministic)
}
func (dst *MasterMonitorInTodayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterMonitorInTodayReq.Merge(dst, src)
}
func (m *MasterMonitorInTodayReq) XXX_Size() int {
	return xxx_messageInfo_MasterMonitorInTodayReq.Size(m)
}
func (m *MasterMonitorInTodayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterMonitorInTodayReq.DiscardUnknown(m)
}

var xxx_messageInfo_MasterMonitorInTodayReq proto.InternalMessageInfo

func (m *MasterMonitorInTodayReq) GetLimitIncome() uint32 {
	if m != nil {
		return m.LimitIncome
	}
	return 0
}

func (m *MasterMonitorInTodayReq) GetLimitWithdraw() uint32 {
	if m != nil {
		return m.LimitWithdraw
	}
	return 0
}

func (m *MasterMonitorInTodayReq) GetCheckTs() int64 {
	if m != nil {
		return m.CheckTs
	}
	return 0
}

type MasterLimitInfo struct {
	MasterUid            uint32   `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	Amount               uint32   `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MasterLimitInfo) Reset()         { *m = MasterLimitInfo{} }
func (m *MasterLimitInfo) String() string { return proto.CompactTextString(m) }
func (*MasterLimitInfo) ProtoMessage()    {}
func (*MasterLimitInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{35}
}
func (m *MasterLimitInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterLimitInfo.Unmarshal(m, b)
}
func (m *MasterLimitInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterLimitInfo.Marshal(b, m, deterministic)
}
func (dst *MasterLimitInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterLimitInfo.Merge(dst, src)
}
func (m *MasterLimitInfo) XXX_Size() int {
	return xxx_messageInfo_MasterLimitInfo.Size(m)
}
func (m *MasterLimitInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterLimitInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MasterLimitInfo proto.InternalMessageInfo

func (m *MasterLimitInfo) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

func (m *MasterLimitInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

type MasterMonitorInTodayResp struct {
	ExceedIncomeMasters   []*MasterLimitInfo `protobuf:"bytes,1,rep,name=exceed_income_masters,json=exceedIncomeMasters,proto3" json:"exceed_income_masters,omitempty"`
	ExceedWithdrawMasters []*MasterLimitInfo `protobuf:"bytes,2,rep,name=exceed_withdraw_masters,json=exceedWithdrawMasters,proto3" json:"exceed_withdraw_masters,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}           `json:"-"`
	XXX_unrecognized      []byte             `json:"-"`
	XXX_sizecache         int32              `json:"-"`
}

func (m *MasterMonitorInTodayResp) Reset()         { *m = MasterMonitorInTodayResp{} }
func (m *MasterMonitorInTodayResp) String() string { return proto.CompactTextString(m) }
func (*MasterMonitorInTodayResp) ProtoMessage()    {}
func (*MasterMonitorInTodayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{36}
}
func (m *MasterMonitorInTodayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterMonitorInTodayResp.Unmarshal(m, b)
}
func (m *MasterMonitorInTodayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterMonitorInTodayResp.Marshal(b, m, deterministic)
}
func (dst *MasterMonitorInTodayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterMonitorInTodayResp.Merge(dst, src)
}
func (m *MasterMonitorInTodayResp) XXX_Size() int {
	return xxx_messageInfo_MasterMonitorInTodayResp.Size(m)
}
func (m *MasterMonitorInTodayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterMonitorInTodayResp.DiscardUnknown(m)
}

var xxx_messageInfo_MasterMonitorInTodayResp proto.InternalMessageInfo

func (m *MasterMonitorInTodayResp) GetExceedIncomeMasters() []*MasterLimitInfo {
	if m != nil {
		return m.ExceedIncomeMasters
	}
	return nil
}

func (m *MasterMonitorInTodayResp) GetExceedWithdrawMasters() []*MasterLimitInfo {
	if m != nil {
		return m.ExceedWithdrawMasters
	}
	return nil
}

// 活动数据统计（活动成本和师父收益）
type GetActivityStatisticsReq struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActivityStatisticsReq) Reset()         { *m = GetActivityStatisticsReq{} }
func (m *GetActivityStatisticsReq) String() string { return proto.CompactTextString(m) }
func (*GetActivityStatisticsReq) ProtoMessage()    {}
func (*GetActivityStatisticsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{37}
}
func (m *GetActivityStatisticsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityStatisticsReq.Unmarshal(m, b)
}
func (m *GetActivityStatisticsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityStatisticsReq.Marshal(b, m, deterministic)
}
func (dst *GetActivityStatisticsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityStatisticsReq.Merge(dst, src)
}
func (m *GetActivityStatisticsReq) XXX_Size() int {
	return xxx_messageInfo_GetActivityStatisticsReq.Size(m)
}
func (m *GetActivityStatisticsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityStatisticsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityStatisticsReq proto.InternalMessageInfo

func (m *GetActivityStatisticsReq) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

type ActivityStatistics struct {
	TotalBalance         int64    `protobuf:"varint,1,opt,name=TotalBalance,proto3" json:"TotalBalance,omitempty"`
	TotalWithdraw        int64    `protobuf:"varint,2,opt,name=TotalWithdraw,proto3" json:"TotalWithdraw,omitempty"`
	AvailableBalance     int64    `protobuf:"varint,3,opt,name=AvailableBalance,proto3" json:"AvailableBalance,omitempty"`
	AwardInDate          int64    `protobuf:"varint,4,opt,name=AwardInDate,proto3" json:"AwardInDate,omitempty"`
	WithdrawInDate       int64    `protobuf:"varint,5,opt,name=WithdrawInDate,proto3" json:"WithdrawInDate,omitempty"`
	AvgAward             int64    `protobuf:"varint,6,opt,name=AvgAward,proto3" json:"AvgAward,omitempty"`
	AvgAwardInDate       int64    `protobuf:"varint,7,opt,name=AvgAwardInDate,proto3" json:"AvgAwardInDate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActivityStatistics) Reset()         { *m = ActivityStatistics{} }
func (m *ActivityStatistics) String() string { return proto.CompactTextString(m) }
func (*ActivityStatistics) ProtoMessage()    {}
func (*ActivityStatistics) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{38}
}
func (m *ActivityStatistics) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityStatistics.Unmarshal(m, b)
}
func (m *ActivityStatistics) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityStatistics.Marshal(b, m, deterministic)
}
func (dst *ActivityStatistics) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityStatistics.Merge(dst, src)
}
func (m *ActivityStatistics) XXX_Size() int {
	return xxx_messageInfo_ActivityStatistics.Size(m)
}
func (m *ActivityStatistics) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityStatistics.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityStatistics proto.InternalMessageInfo

func (m *ActivityStatistics) GetTotalBalance() int64 {
	if m != nil {
		return m.TotalBalance
	}
	return 0
}

func (m *ActivityStatistics) GetTotalWithdraw() int64 {
	if m != nil {
		return m.TotalWithdraw
	}
	return 0
}

func (m *ActivityStatistics) GetAvailableBalance() int64 {
	if m != nil {
		return m.AvailableBalance
	}
	return 0
}

func (m *ActivityStatistics) GetAwardInDate() int64 {
	if m != nil {
		return m.AwardInDate
	}
	return 0
}

func (m *ActivityStatistics) GetWithdrawInDate() int64 {
	if m != nil {
		return m.WithdrawInDate
	}
	return 0
}

func (m *ActivityStatistics) GetAvgAward() int64 {
	if m != nil {
		return m.AvgAward
	}
	return 0
}

func (m *ActivityStatistics) GetAvgAwardInDate() int64 {
	if m != nil {
		return m.AvgAwardInDate
	}
	return 0
}

type GetActivityStatisticsResp struct {
	Statistics           *ActivityStatistics `protobuf:"bytes,1,opt,name=statistics,proto3" json:"statistics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetActivityStatisticsResp) Reset()         { *m = GetActivityStatisticsResp{} }
func (m *GetActivityStatisticsResp) String() string { return proto.CompactTextString(m) }
func (*GetActivityStatisticsResp) ProtoMessage()    {}
func (*GetActivityStatisticsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{39}
}
func (m *GetActivityStatisticsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityStatisticsResp.Unmarshal(m, b)
}
func (m *GetActivityStatisticsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityStatisticsResp.Marshal(b, m, deterministic)
}
func (dst *GetActivityStatisticsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityStatisticsResp.Merge(dst, src)
}
func (m *GetActivityStatisticsResp) XXX_Size() int {
	return xxx_messageInfo_GetActivityStatisticsResp.Size(m)
}
func (m *GetActivityStatisticsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityStatisticsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityStatisticsResp proto.InternalMessageInfo

func (m *GetActivityStatisticsResp) GetStatistics() *ActivityStatistics {
	if m != nil {
		return m.Statistics
	}
	return nil
}

type RecordPlayRoomReq struct {
	RoomOwnerUid         uint32   `protobuf:"varint,1,opt,name=room_owner_uid,json=roomOwnerUid,proto3" json:"room_owner_uid,omitempty"`
	PlayerList           []uint32 `protobuf:"varint,2,rep,packed,name=player_list,json=playerList,proto3" json:"player_list,omitempty"`
	Timestamp            int64    `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Extra                string   `protobuf:"bytes,4,opt,name=extra,proto3" json:"extra,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordPlayRoomReq) Reset()         { *m = RecordPlayRoomReq{} }
func (m *RecordPlayRoomReq) String() string { return proto.CompactTextString(m) }
func (*RecordPlayRoomReq) ProtoMessage()    {}
func (*RecordPlayRoomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{40}
}
func (m *RecordPlayRoomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordPlayRoomReq.Unmarshal(m, b)
}
func (m *RecordPlayRoomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordPlayRoomReq.Marshal(b, m, deterministic)
}
func (dst *RecordPlayRoomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordPlayRoomReq.Merge(dst, src)
}
func (m *RecordPlayRoomReq) XXX_Size() int {
	return xxx_messageInfo_RecordPlayRoomReq.Size(m)
}
func (m *RecordPlayRoomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordPlayRoomReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecordPlayRoomReq proto.InternalMessageInfo

func (m *RecordPlayRoomReq) GetRoomOwnerUid() uint32 {
	if m != nil {
		return m.RoomOwnerUid
	}
	return 0
}

func (m *RecordPlayRoomReq) GetPlayerList() []uint32 {
	if m != nil {
		return m.PlayerList
	}
	return nil
}

func (m *RecordPlayRoomReq) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *RecordPlayRoomReq) GetExtra() string {
	if m != nil {
		return m.Extra
	}
	return ""
}

type RecordPlayRoomResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordPlayRoomResp) Reset()         { *m = RecordPlayRoomResp{} }
func (m *RecordPlayRoomResp) String() string { return proto.CompactTextString(m) }
func (*RecordPlayRoomResp) ProtoMessage()    {}
func (*RecordPlayRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{41}
}
func (m *RecordPlayRoomResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordPlayRoomResp.Unmarshal(m, b)
}
func (m *RecordPlayRoomResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordPlayRoomResp.Marshal(b, m, deterministic)
}
func (dst *RecordPlayRoomResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordPlayRoomResp.Merge(dst, src)
}
func (m *RecordPlayRoomResp) XXX_Size() int {
	return xxx_messageInfo_RecordPlayRoomResp.Size(m)
}
func (m *RecordPlayRoomResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordPlayRoomResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecordPlayRoomResp proto.InternalMessageInfo

type CheckUserTypeListReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserTypeListReq) Reset()         { *m = CheckUserTypeListReq{} }
func (m *CheckUserTypeListReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserTypeListReq) ProtoMessage()    {}
func (*CheckUserTypeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{42}
}
func (m *CheckUserTypeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserTypeListReq.Unmarshal(m, b)
}
func (m *CheckUserTypeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserTypeListReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserTypeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserTypeListReq.Merge(dst, src)
}
func (m *CheckUserTypeListReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserTypeListReq.Size(m)
}
func (m *CheckUserTypeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserTypeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserTypeListReq proto.InternalMessageInfo

func (m *CheckUserTypeListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type UserTypeChecker struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	UserType             uint32   `protobuf:"varint,2,opt,name=user_type,json=userType,proto3" json:"user_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserTypeChecker) Reset()         { *m = UserTypeChecker{} }
func (m *UserTypeChecker) String() string { return proto.CompactTextString(m) }
func (*UserTypeChecker) ProtoMessage()    {}
func (*UserTypeChecker) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{43}
}
func (m *UserTypeChecker) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserTypeChecker.Unmarshal(m, b)
}
func (m *UserTypeChecker) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserTypeChecker.Marshal(b, m, deterministic)
}
func (dst *UserTypeChecker) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserTypeChecker.Merge(dst, src)
}
func (m *UserTypeChecker) XXX_Size() int {
	return xxx_messageInfo_UserTypeChecker.Size(m)
}
func (m *UserTypeChecker) XXX_DiscardUnknown() {
	xxx_messageInfo_UserTypeChecker.DiscardUnknown(m)
}

var xxx_messageInfo_UserTypeChecker proto.InternalMessageInfo

func (m *UserTypeChecker) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *UserTypeChecker) GetUserType() uint32 {
	if m != nil {
		return m.UserType
	}
	return 0
}

type CheckUserTypeListResp struct {
	Checkers             []*UserTypeChecker `protobuf:"bytes,1,rep,name=checkers,proto3" json:"checkers,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CheckUserTypeListResp) Reset()         { *m = CheckUserTypeListResp{} }
func (m *CheckUserTypeListResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserTypeListResp) ProtoMessage()    {}
func (*CheckUserTypeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_room_master_apprentice_d64e5f71cdf63f76, []int{44}
}
func (m *CheckUserTypeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserTypeListResp.Unmarshal(m, b)
}
func (m *CheckUserTypeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserTypeListResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserTypeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserTypeListResp.Merge(dst, src)
}
func (m *CheckUserTypeListResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserTypeListResp.Size(m)
}
func (m *CheckUserTypeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserTypeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserTypeListResp proto.InternalMessageInfo

func (m *CheckUserTypeListResp) GetCheckers() []*UserTypeChecker {
	if m != nil {
		return m.Checkers
	}
	return nil
}

func init() {
	proto.RegisterType((*BatchAddMasterReq)(nil), "roommasterapprentice.BatchAddMasterReq")
	proto.RegisterType((*BatchAddMasterResp)(nil), "roommasterapprentice.BatchAddMasterResp")
	proto.RegisterType((*BatchDelMasterReq)(nil), "roommasterapprentice.BatchDelMasterReq")
	proto.RegisterType((*BatchDelMasterResp)(nil), "roommasterapprentice.BatchDelMasterResp")
	proto.RegisterType((*IsMasterInValidTimeReq)(nil), "roommasterapprentice.IsMasterInValidTimeReq")
	proto.RegisterType((*IsMasterInValidTimeResp)(nil), "roommasterapprentice.IsMasterInValidTimeResp")
	proto.RegisterType((*IsMasterReq)(nil), "roommasterapprentice.IsMasterReq")
	proto.RegisterType((*IsMasterResp)(nil), "roommasterapprentice.IsMasterResp")
	proto.RegisterType((*DrawBalanceReq)(nil), "roommasterapprentice.DrawBalanceReq")
	proto.RegisterType((*DrawBalanceResp)(nil), "roommasterapprentice.DrawBalanceResp")
	proto.RegisterType((*GetUserBalanceByUserIDReq)(nil), "roommasterapprentice.GetUserBalanceByUserIDReq")
	proto.RegisterType((*GetUserBalanceByUserIDResp)(nil), "roommasterapprentice.GetUserBalanceByUserIDResp")
	proto.RegisterType((*OrderInfo)(nil), "roommasterapprentice.OrderInfo")
	proto.RegisterType((*GetOrderListByUserIDReq)(nil), "roommasterapprentice.GetOrderListByUserIDReq")
	proto.RegisterType((*LoadMore)(nil), "roommasterapprentice.LoadMore")
	proto.RegisterType((*GetOrderListByUserIDResp)(nil), "roommasterapprentice.GetOrderListByUserIDResp")
	proto.RegisterType((*GetOrderInfoByUserIDOrderIDReq)(nil), "roommasterapprentice.GetOrderInfoByUserIDOrderIDReq")
	proto.RegisterType((*GetOrderInfoByUserIDOrderIDResp)(nil), "roommasterapprentice.GetOrderInfoByUserIDOrderIDResp")
	proto.RegisterType((*GetOrderInfoByOrderIDReq)(nil), "roommasterapprentice.GetOrderInfoByOrderIDReq")
	proto.RegisterType((*GetOrderInfoByOrderIDResp)(nil), "roommasterapprentice.GetOrderInfoByOrderIDResp")
	proto.RegisterType((*UpdateOrderInfoByOrderIDReq)(nil), "roommasterapprentice.UpdateOrderInfoByOrderIDReq")
	proto.RegisterType((*UpdateOrderInfoByOrderIDResp)(nil), "roommasterapprentice.UpdateOrderInfoByOrderIDResp")
	proto.RegisterType((*BindWXUserPayInfoReq)(nil), "roommasterapprentice.BindWXUserPayInfoReq")
	proto.RegisterType((*BindWXUserPayInfoResp)(nil), "roommasterapprentice.BindWXUserPayInfoResp")
	proto.RegisterType((*GetBindingInfoReq)(nil), "roommasterapprentice.GetBindingInfoReq")
	proto.RegisterType((*GetBindingInfoResp)(nil), "roommasterapprentice.GetBindingInfoResp")
	proto.RegisterType((*GetActConfigReq)(nil), "roommasterapprentice.GetActConfigReq")
	proto.RegisterType((*GetActConfigResp)(nil), "roommasterapprentice.GetActConfigResp")
	proto.RegisterType((*MasterInitForWebReq)(nil), "roommasterapprentice.MasterInitForWebReq")
	proto.RegisterType((*ApprenticeInfo)(nil), "roommasterapprentice.ApprenticeInfo")
	proto.RegisterType((*MasterInitForWebResp)(nil), "roommasterapprentice.MasterInitForWebResp")
	proto.RegisterType((*GetHistoryApprenticeReq)(nil), "roommasterapprentice.GetHistoryApprenticeReq")
	proto.RegisterType((*DayApprenticeInfo)(nil), "roommasterapprentice.DayApprenticeInfo")
	proto.RegisterType((*GetHistoryApprenticeResp)(nil), "roommasterapprentice.GetHistoryApprenticeResp")
	proto.RegisterType((*MasterMonitorInTodayReq)(nil), "roommasterapprentice.MasterMonitorInTodayReq")
	proto.RegisterType((*MasterLimitInfo)(nil), "roommasterapprentice.MasterLimitInfo")
	proto.RegisterType((*MasterMonitorInTodayResp)(nil), "roommasterapprentice.MasterMonitorInTodayResp")
	proto.RegisterType((*GetActivityStatisticsReq)(nil), "roommasterapprentice.GetActivityStatisticsReq")
	proto.RegisterType((*ActivityStatistics)(nil), "roommasterapprentice.ActivityStatistics")
	proto.RegisterType((*GetActivityStatisticsResp)(nil), "roommasterapprentice.GetActivityStatisticsResp")
	proto.RegisterType((*RecordPlayRoomReq)(nil), "roommasterapprentice.RecordPlayRoomReq")
	proto.RegisterType((*RecordPlayRoomResp)(nil), "roommasterapprentice.RecordPlayRoomResp")
	proto.RegisterType((*CheckUserTypeListReq)(nil), "roommasterapprentice.CheckUserTypeListReq")
	proto.RegisterType((*UserTypeChecker)(nil), "roommasterapprentice.UserTypeChecker")
	proto.RegisterType((*CheckUserTypeListResp)(nil), "roommasterapprentice.CheckUserTypeListResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RoomMasterApprenticeClient is the client API for RoomMasterApprentice service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RoomMasterApprenticeClient interface {
	// 批量添加师傅
	BatchAddMaster(ctx context.Context, in *BatchAddMasterReq, opts ...grpc.CallOption) (*BatchAddMasterResp, error)
	// 批量删师傅
	BatchDelMaster(ctx context.Context, in *BatchDelMasterReq, opts ...grpc.CallOption) (*BatchDelMasterResp, error)
	// 是师父&在收徒时间内
	IsMasterInValidTime(ctx context.Context, in *IsMasterInValidTimeReq, opts ...grpc.CallOption) (*IsMasterInValidTimeResp, error)
	// web 首页数据
	MasterInitForWeb(ctx context.Context, in *MasterInitForWebReq, opts ...grpc.CallOption) (*MasterInitForWebResp, error)
	// 是否有收徒资格
	IsMaster(ctx context.Context, in *IsMasterReq, opts ...grpc.CallOption) (*IsMasterResp, error)
	// 完成任务的rpc
	RecordPlayRoom(ctx context.Context, in *RecordPlayRoomReq, opts ...grpc.CallOption) (*RecordPlayRoomResp, error)
	// 判断输入用户列表
	CheckUserTypeList(ctx context.Context, in *CheckUserTypeListReq, opts ...grpc.CallOption) (*CheckUserTypeListResp, error)
	// 获取历史徒弟列表
	GetHistoryApprentice(ctx context.Context, in *GetHistoryApprenticeReq, opts ...grpc.CallOption) (*GetHistoryApprenticeResp, error)
	// 用户提现
	DrawBalance(ctx context.Context, in *DrawBalanceReq, opts ...grpc.CallOption) (*DrawBalanceResp, error)
	// 获取余额
	GetUserBalanceByUserID(ctx context.Context, in *GetUserBalanceByUserIDReq, opts ...grpc.CallOption) (*GetUserBalanceByUserIDResp, error)
	// 获取其订单列表
	GetOrderListByUserID(ctx context.Context, in *GetOrderListByUserIDReq, opts ...grpc.CallOption) (*GetOrderListByUserIDResp, error)
	// 获取订单信息
	GetOrderInfoByUserIDOrderID(ctx context.Context, in *GetOrderInfoByUserIDOrderIDReq, opts ...grpc.CallOption) (*GetOrderInfoByUserIDOrderIDResp, error)
	// 获取订单信息 供佣金平台反查
	GetOrderInfoByOrderID(ctx context.Context, in *GetOrderInfoByOrderIDReq, opts ...grpc.CallOption) (*GetOrderInfoByOrderIDResp, error)
	// 佣金平台更新佣金信息 将订单状态标记
	UpdateOrderInfoByOrderID(ctx context.Context, in *UpdateOrderInfoByOrderIDReq, opts ...grpc.CallOption) (*UpdateOrderInfoByOrderIDResp, error)
	// 绑定微信信息
	BindWXUserPayInfo(ctx context.Context, in *BindWXUserPayInfoReq, opts ...grpc.CallOption) (*BindWXUserPayInfoResp, error)
	// 查询微信绑定信息
	GetBindingInfo(ctx context.Context, in *GetBindingInfoReq, opts ...grpc.CallOption) (*GetBindingInfoResp, error)
	// 佣金模块 end
	GetActConfig(ctx context.Context, in *GetActConfigReq, opts ...grpc.CallOption) (*GetActConfigResp, error)
	// 监控 师父的收益和提现
	MasterMonitorInToday(ctx context.Context, in *MasterMonitorInTodayReq, opts ...grpc.CallOption) (*MasterMonitorInTodayResp, error)
	// 活动数据统计（活动成本和师父收益）
	GetActivityStatistics(ctx context.Context, in *GetActivityStatisticsReq, opts ...grpc.CallOption) (*GetActivityStatisticsResp, error)
}

type roomMasterApprenticeClient struct {
	cc *grpc.ClientConn
}

func NewRoomMasterApprenticeClient(cc *grpc.ClientConn) RoomMasterApprenticeClient {
	return &roomMasterApprenticeClient{cc}
}

func (c *roomMasterApprenticeClient) BatchAddMaster(ctx context.Context, in *BatchAddMasterReq, opts ...grpc.CallOption) (*BatchAddMasterResp, error) {
	out := new(BatchAddMasterResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/BatchAddMaster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) BatchDelMaster(ctx context.Context, in *BatchDelMasterReq, opts ...grpc.CallOption) (*BatchDelMasterResp, error) {
	out := new(BatchDelMasterResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/BatchDelMaster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) IsMasterInValidTime(ctx context.Context, in *IsMasterInValidTimeReq, opts ...grpc.CallOption) (*IsMasterInValidTimeResp, error) {
	out := new(IsMasterInValidTimeResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/IsMasterInValidTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) MasterInitForWeb(ctx context.Context, in *MasterInitForWebReq, opts ...grpc.CallOption) (*MasterInitForWebResp, error) {
	out := new(MasterInitForWebResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/MasterInitForWeb", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) IsMaster(ctx context.Context, in *IsMasterReq, opts ...grpc.CallOption) (*IsMasterResp, error) {
	out := new(IsMasterResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/IsMaster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) RecordPlayRoom(ctx context.Context, in *RecordPlayRoomReq, opts ...grpc.CallOption) (*RecordPlayRoomResp, error) {
	out := new(RecordPlayRoomResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/RecordPlayRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) CheckUserTypeList(ctx context.Context, in *CheckUserTypeListReq, opts ...grpc.CallOption) (*CheckUserTypeListResp, error) {
	out := new(CheckUserTypeListResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/CheckUserTypeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) GetHistoryApprentice(ctx context.Context, in *GetHistoryApprenticeReq, opts ...grpc.CallOption) (*GetHistoryApprenticeResp, error) {
	out := new(GetHistoryApprenticeResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/GetHistoryApprentice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) DrawBalance(ctx context.Context, in *DrawBalanceReq, opts ...grpc.CallOption) (*DrawBalanceResp, error) {
	out := new(DrawBalanceResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/DrawBalance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) GetUserBalanceByUserID(ctx context.Context, in *GetUserBalanceByUserIDReq, opts ...grpc.CallOption) (*GetUserBalanceByUserIDResp, error) {
	out := new(GetUserBalanceByUserIDResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/GetUserBalanceByUserID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) GetOrderListByUserID(ctx context.Context, in *GetOrderListByUserIDReq, opts ...grpc.CallOption) (*GetOrderListByUserIDResp, error) {
	out := new(GetOrderListByUserIDResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/GetOrderListByUserID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) GetOrderInfoByUserIDOrderID(ctx context.Context, in *GetOrderInfoByUserIDOrderIDReq, opts ...grpc.CallOption) (*GetOrderInfoByUserIDOrderIDResp, error) {
	out := new(GetOrderInfoByUserIDOrderIDResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/GetOrderInfoByUserIDOrderID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) GetOrderInfoByOrderID(ctx context.Context, in *GetOrderInfoByOrderIDReq, opts ...grpc.CallOption) (*GetOrderInfoByOrderIDResp, error) {
	out := new(GetOrderInfoByOrderIDResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/GetOrderInfoByOrderID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) UpdateOrderInfoByOrderID(ctx context.Context, in *UpdateOrderInfoByOrderIDReq, opts ...grpc.CallOption) (*UpdateOrderInfoByOrderIDResp, error) {
	out := new(UpdateOrderInfoByOrderIDResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/UpdateOrderInfoByOrderID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) BindWXUserPayInfo(ctx context.Context, in *BindWXUserPayInfoReq, opts ...grpc.CallOption) (*BindWXUserPayInfoResp, error) {
	out := new(BindWXUserPayInfoResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/BindWXUserPayInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) GetBindingInfo(ctx context.Context, in *GetBindingInfoReq, opts ...grpc.CallOption) (*GetBindingInfoResp, error) {
	out := new(GetBindingInfoResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/GetBindingInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) GetActConfig(ctx context.Context, in *GetActConfigReq, opts ...grpc.CallOption) (*GetActConfigResp, error) {
	out := new(GetActConfigResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/GetActConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) MasterMonitorInToday(ctx context.Context, in *MasterMonitorInTodayReq, opts ...grpc.CallOption) (*MasterMonitorInTodayResp, error) {
	out := new(MasterMonitorInTodayResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/MasterMonitorInToday", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomMasterApprenticeClient) GetActivityStatistics(ctx context.Context, in *GetActivityStatisticsReq, opts ...grpc.CallOption) (*GetActivityStatisticsResp, error) {
	out := new(GetActivityStatisticsResp)
	err := c.cc.Invoke(ctx, "/roommasterapprentice.RoomMasterApprentice/GetActivityStatistics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RoomMasterApprenticeServer is the server API for RoomMasterApprentice service.
type RoomMasterApprenticeServer interface {
	// 批量添加师傅
	BatchAddMaster(context.Context, *BatchAddMasterReq) (*BatchAddMasterResp, error)
	// 批量删师傅
	BatchDelMaster(context.Context, *BatchDelMasterReq) (*BatchDelMasterResp, error)
	// 是师父&在收徒时间内
	IsMasterInValidTime(context.Context, *IsMasterInValidTimeReq) (*IsMasterInValidTimeResp, error)
	// web 首页数据
	MasterInitForWeb(context.Context, *MasterInitForWebReq) (*MasterInitForWebResp, error)
	// 是否有收徒资格
	IsMaster(context.Context, *IsMasterReq) (*IsMasterResp, error)
	// 完成任务的rpc
	RecordPlayRoom(context.Context, *RecordPlayRoomReq) (*RecordPlayRoomResp, error)
	// 判断输入用户列表
	CheckUserTypeList(context.Context, *CheckUserTypeListReq) (*CheckUserTypeListResp, error)
	// 获取历史徒弟列表
	GetHistoryApprentice(context.Context, *GetHistoryApprenticeReq) (*GetHistoryApprenticeResp, error)
	// 用户提现
	DrawBalance(context.Context, *DrawBalanceReq) (*DrawBalanceResp, error)
	// 获取余额
	GetUserBalanceByUserID(context.Context, *GetUserBalanceByUserIDReq) (*GetUserBalanceByUserIDResp, error)
	// 获取其订单列表
	GetOrderListByUserID(context.Context, *GetOrderListByUserIDReq) (*GetOrderListByUserIDResp, error)
	// 获取订单信息
	GetOrderInfoByUserIDOrderID(context.Context, *GetOrderInfoByUserIDOrderIDReq) (*GetOrderInfoByUserIDOrderIDResp, error)
	// 获取订单信息 供佣金平台反查
	GetOrderInfoByOrderID(context.Context, *GetOrderInfoByOrderIDReq) (*GetOrderInfoByOrderIDResp, error)
	// 佣金平台更新佣金信息 将订单状态标记
	UpdateOrderInfoByOrderID(context.Context, *UpdateOrderInfoByOrderIDReq) (*UpdateOrderInfoByOrderIDResp, error)
	// 绑定微信信息
	BindWXUserPayInfo(context.Context, *BindWXUserPayInfoReq) (*BindWXUserPayInfoResp, error)
	// 查询微信绑定信息
	GetBindingInfo(context.Context, *GetBindingInfoReq) (*GetBindingInfoResp, error)
	// 佣金模块 end
	GetActConfig(context.Context, *GetActConfigReq) (*GetActConfigResp, error)
	// 监控 师父的收益和提现
	MasterMonitorInToday(context.Context, *MasterMonitorInTodayReq) (*MasterMonitorInTodayResp, error)
	// 活动数据统计（活动成本和师父收益）
	GetActivityStatistics(context.Context, *GetActivityStatisticsReq) (*GetActivityStatisticsResp, error)
}

func RegisterRoomMasterApprenticeServer(s *grpc.Server, srv RoomMasterApprenticeServer) {
	s.RegisterService(&_RoomMasterApprentice_serviceDesc, srv)
}

func _RoomMasterApprentice_BatchAddMaster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddMasterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).BatchAddMaster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/BatchAddMaster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).BatchAddMaster(ctx, req.(*BatchAddMasterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_BatchDelMaster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelMasterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).BatchDelMaster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/BatchDelMaster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).BatchDelMaster(ctx, req.(*BatchDelMasterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_IsMasterInValidTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsMasterInValidTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).IsMasterInValidTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/IsMasterInValidTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).IsMasterInValidTime(ctx, req.(*IsMasterInValidTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_MasterInitForWeb_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MasterInitForWebReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).MasterInitForWeb(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/MasterInitForWeb",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).MasterInitForWeb(ctx, req.(*MasterInitForWebReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_IsMaster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsMasterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).IsMaster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/IsMaster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).IsMaster(ctx, req.(*IsMasterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_RecordPlayRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordPlayRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).RecordPlayRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/RecordPlayRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).RecordPlayRoom(ctx, req.(*RecordPlayRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_CheckUserTypeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserTypeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).CheckUserTypeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/CheckUserTypeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).CheckUserTypeList(ctx, req.(*CheckUserTypeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_GetHistoryApprentice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHistoryApprenticeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).GetHistoryApprentice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/GetHistoryApprentice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).GetHistoryApprentice(ctx, req.(*GetHistoryApprenticeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_DrawBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DrawBalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).DrawBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/DrawBalance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).DrawBalance(ctx, req.(*DrawBalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_GetUserBalanceByUserID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBalanceByUserIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).GetUserBalanceByUserID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/GetUserBalanceByUserID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).GetUserBalanceByUserID(ctx, req.(*GetUserBalanceByUserIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_GetOrderListByUserID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderListByUserIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).GetOrderListByUserID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/GetOrderListByUserID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).GetOrderListByUserID(ctx, req.(*GetOrderListByUserIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_GetOrderInfoByUserIDOrderID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderInfoByUserIDOrderIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).GetOrderInfoByUserIDOrderID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/GetOrderInfoByUserIDOrderID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).GetOrderInfoByUserIDOrderID(ctx, req.(*GetOrderInfoByUserIDOrderIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_GetOrderInfoByOrderID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderInfoByOrderIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).GetOrderInfoByOrderID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/GetOrderInfoByOrderID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).GetOrderInfoByOrderID(ctx, req.(*GetOrderInfoByOrderIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_UpdateOrderInfoByOrderID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOrderInfoByOrderIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).UpdateOrderInfoByOrderID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/UpdateOrderInfoByOrderID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).UpdateOrderInfoByOrderID(ctx, req.(*UpdateOrderInfoByOrderIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_BindWXUserPayInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindWXUserPayInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).BindWXUserPayInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/BindWXUserPayInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).BindWXUserPayInfo(ctx, req.(*BindWXUserPayInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_GetBindingInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBindingInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).GetBindingInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/GetBindingInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).GetBindingInfo(ctx, req.(*GetBindingInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_GetActConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).GetActConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/GetActConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).GetActConfig(ctx, req.(*GetActConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_MasterMonitorInToday_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MasterMonitorInTodayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).MasterMonitorInToday(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/MasterMonitorInToday",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).MasterMonitorInToday(ctx, req.(*MasterMonitorInTodayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomMasterApprentice_GetActivityStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivityStatisticsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomMasterApprenticeServer).GetActivityStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/roommasterapprentice.RoomMasterApprentice/GetActivityStatistics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomMasterApprenticeServer).GetActivityStatistics(ctx, req.(*GetActivityStatisticsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RoomMasterApprentice_serviceDesc = grpc.ServiceDesc{
	ServiceName: "roommasterapprentice.RoomMasterApprentice",
	HandlerType: (*RoomMasterApprenticeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchAddMaster",
			Handler:    _RoomMasterApprentice_BatchAddMaster_Handler,
		},
		{
			MethodName: "BatchDelMaster",
			Handler:    _RoomMasterApprentice_BatchDelMaster_Handler,
		},
		{
			MethodName: "IsMasterInValidTime",
			Handler:    _RoomMasterApprentice_IsMasterInValidTime_Handler,
		},
		{
			MethodName: "MasterInitForWeb",
			Handler:    _RoomMasterApprentice_MasterInitForWeb_Handler,
		},
		{
			MethodName: "IsMaster",
			Handler:    _RoomMasterApprentice_IsMaster_Handler,
		},
		{
			MethodName: "RecordPlayRoom",
			Handler:    _RoomMasterApprentice_RecordPlayRoom_Handler,
		},
		{
			MethodName: "CheckUserTypeList",
			Handler:    _RoomMasterApprentice_CheckUserTypeList_Handler,
		},
		{
			MethodName: "GetHistoryApprentice",
			Handler:    _RoomMasterApprentice_GetHistoryApprentice_Handler,
		},
		{
			MethodName: "DrawBalance",
			Handler:    _RoomMasterApprentice_DrawBalance_Handler,
		},
		{
			MethodName: "GetUserBalanceByUserID",
			Handler:    _RoomMasterApprentice_GetUserBalanceByUserID_Handler,
		},
		{
			MethodName: "GetOrderListByUserID",
			Handler:    _RoomMasterApprentice_GetOrderListByUserID_Handler,
		},
		{
			MethodName: "GetOrderInfoByUserIDOrderID",
			Handler:    _RoomMasterApprentice_GetOrderInfoByUserIDOrderID_Handler,
		},
		{
			MethodName: "GetOrderInfoByOrderID",
			Handler:    _RoomMasterApprentice_GetOrderInfoByOrderID_Handler,
		},
		{
			MethodName: "UpdateOrderInfoByOrderID",
			Handler:    _RoomMasterApprentice_UpdateOrderInfoByOrderID_Handler,
		},
		{
			MethodName: "BindWXUserPayInfo",
			Handler:    _RoomMasterApprentice_BindWXUserPayInfo_Handler,
		},
		{
			MethodName: "GetBindingInfo",
			Handler:    _RoomMasterApprentice_GetBindingInfo_Handler,
		},
		{
			MethodName: "GetActConfig",
			Handler:    _RoomMasterApprentice_GetActConfig_Handler,
		},
		{
			MethodName: "MasterMonitorInToday",
			Handler:    _RoomMasterApprentice_MasterMonitorInToday_Handler,
		},
		{
			MethodName: "GetActivityStatistics",
			Handler:    _RoomMasterApprentice_GetActivityStatistics_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "room-master-apprentice/room-master-apprentice.proto",
}

func init() {
	proto.RegisterFile("room-master-apprentice/room-master-apprentice.proto", fileDescriptor_room_master_apprentice_d64e5f71cdf63f76)
}

var fileDescriptor_room_master_apprentice_d64e5f71cdf63f76 = []byte{
	// 1970 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x19, 0x4d, 0x6f, 0x1b, 0xc7,
	0x55, 0x24, 0x65, 0x89, 0x7c, 0x14, 0xf5, 0x31, 0xa2, 0x2d, 0x9a, 0x4a, 0x22, 0x7b, 0x62, 0x27,
	0xaa, 0x5d, 0x53, 0x8d, 0x62, 0x07, 0x01, 0x0a, 0x14, 0x90, 0xa2, 0x44, 0x26, 0x60, 0x3b, 0xc6,
	0x86, 0x8e, 0x1b, 0xb7, 0x05, 0x31, 0xda, 0x1d, 0x4b, 0x03, 0x93, 0x3b, 0x9b, 0x9d, 0xa1, 0x25,
	0xb6, 0x68, 0x50, 0xa0, 0xd7, 0xa2, 0xd7, 0x16, 0x3d, 0xf6, 0xd6, 0x5b, 0x6f, 0xfd, 0x23, 0x3d,
	0xf4, 0xe7, 0x14, 0xf3, 0xb1, 0xdc, 0x25, 0x39, 0x4b, 0x93, 0x45, 0xd0, 0x1b, 0xe7, 0xcd, 0xfb,
	0xfe, 0x9a, 0xb7, 0x8f, 0xf0, 0x69, 0xcc, 0x79, 0xff, 0x41, 0x9f, 0x08, 0x49, 0xe3, 0x07, 0x24,
	0x8a, 0x62, 0x1a, 0x4a, 0xe6, 0xd3, 0x03, 0x37, 0xb8, 0x15, 0xc5, 0x5c, 0x72, 0x54, 0x57, 0xb7,
	0xe6, 0x32, 0xbd, 0xc3, 0x1f, 0xc3, 0xd6, 0x31, 0x91, 0xfe, 0xc5, 0x51, 0x10, 0x3c, 0xd5, 0x77,
	0x1e, 0xfd, 0x1e, 0x21, 0x58, 0x1e, 0xb0, 0x40, 0x34, 0x0a, 0xb7, 0x4a, 0xfb, 0x35, 0x4f, 0xff,
	0xc6, 0x75, 0x40, 0x93, 0x88, 0x22, 0x1a, 0x91, 0x9f, 0xd0, 0xde, 0x7c, 0xe4, 0x19, 0x44, 0x11,
	0xe1, 0x7b, 0x70, 0xa3, 0x2d, 0xcc, 0xb9, 0x1d, 0x7e, 0x4b, 0x7a, 0x2c, 0xe8, 0xb0, 0x3e, 0x55,
	0x3c, 0x36, 0xa1, 0x34, 0x60, 0x41, 0xa3, 0x70, 0xab, 0xb0, 0x5f, 0xf3, 0xd4, 0x4f, 0xfc, 0x19,
	0xec, 0x38, 0x71, 0x45, 0x84, 0x76, 0xa1, 0xc2, 0x44, 0xd7, 0xd8, 0xa6, 0x49, 0xca, 0x5e, 0x99,
	0x59, 0x5c, 0xbc, 0x07, 0xd5, 0x84, 0xce, 0xcd, 0xf8, 0x3e, 0xac, 0xa5, 0x08, 0xef, 0xe2, 0xf6,
	0x04, 0xd6, 0x4f, 0x62, 0x72, 0x79, 0x4c, 0x7a, 0x24, 0xf4, 0xb5, 0xa6, 0x3b, 0xb0, 0x3a, 0x10,
	0x34, 0xee, 0x8e, 0x98, 0xae, 0xa8, 0x63, 0x3b, 0x40, 0xb7, 0x61, 0x2d, 0x88, 0xc9, 0x65, 0xf7,
	0xcc, 0xe0, 0x36, 0x8a, 0xb7, 0x0a, 0xfb, 0x25, 0xaf, 0x1a, 0xa4, 0xe4, 0xf8, 0x2b, 0xd8, 0x18,
	0xe3, 0x26, 0x22, 0xd4, 0x80, 0xd5, 0x3e, 0x15, 0x82, 0x9c, 0x53, 0xcd, 0xae, 0xe2, 0x25, 0x47,
	0x74, 0x13, 0xca, 0x3c, 0x0e, 0x8c, 0xa4, 0xa2, 0xb9, 0xd2, 0xe7, 0x76, 0x80, 0x1f, 0xc2, 0xcd,
	0x53, 0x2a, 0x5f, 0x08, 0x1a, 0x5b, 0x56, 0xc7, 0x43, 0x75, 0x68, 0x9f, 0xcc, 0x52, 0x10, 0xff,
	0xab, 0x00, 0xcd, 0x3c, 0x32, 0x11, 0xa1, 0x0f, 0xa1, 0x26, 0xb9, 0x24, 0xbd, 0x91, 0x01, 0x05,
	0x6d, 0xc0, 0x9a, 0x06, 0x5a, 0x02, 0x74, 0x1f, 0xb6, 0xc8, 0x5b, 0xc2, 0x7a, 0xe4, 0xac, 0x47,
	0x27, 0x2c, 0xdd, 0x1c, 0x5d, 0x24, 0xc8, 0x07, 0xb0, 0x3d, 0x08, 0xa7, 0xd1, 0x4b, 0x1a, 0x1d,
	0x65, 0xae, 0x12, 0x82, 0x1b, 0xb0, 0x22, 0x24, 0x91, 0x03, 0xd1, 0x58, 0xd6, 0x38, 0xf6, 0x84,
	0xff, 0x56, 0x84, 0xca, 0xd7, 0xda, 0xf6, 0xf0, 0x35, 0xcf, 0x8f, 0x40, 0xbe, 0xc7, 0x32, 0x9c,
	0x4b, 0x59, 0xce, 0x68, 0x0f, 0xaa, 0x21, 0x4f, 0x63, 0x66, 0xc4, 0x42, 0xc8, 0x2f, 0x33, 0x2a,
	0x91, 0x3e, 0x1f, 0x84, 0xb2, 0x71, 0xcd, 0x10, 0x9a, 0x93, 0x4a, 0x7a, 0x39, 0x8c, 0x68, 0x63,
	0x45, 0x43, 0xf5, 0x6f, 0xd4, 0x84, 0x72, 0x40, 0x85, 0x1f, 0xb3, 0x33, 0xda, 0x58, 0xd5, 0xf2,
	0x47, 0x67, 0xc5, 0x27, 0xa6, 0x44, 0xf0, 0xb0, 0x51, 0xd6, 0x37, 0xf6, 0xa4, 0x14, 0xf0, 0x63,
	0x4a, 0x24, 0xed, 0x4a, 0xd6, 0xa7, 0x8d, 0x8a, 0x51, 0xc0, 0x80, 0x54, 0xc2, 0x2b, 0x84, 0x41,
	0x14, 0x8c, 0x10, 0xc0, 0x20, 0x18, 0x90, 0x42, 0xc0, 0x04, 0x76, 0x4e, 0xa9, 0xd4, 0xee, 0x79,
	0xc2, 0x84, 0x9c, 0x27, 0x15, 0x50, 0x1d, 0xae, 0xf5, 0x58, 0x9f, 0x49, 0x1b, 0x3a, 0x73, 0x50,
	0xe8, 0x3d, 0x22, 0xa4, 0x42, 0xb7, 0x5e, 0x52, 0xc7, 0x76, 0x80, 0x3b, 0x50, 0x7e, 0xc2, 0x49,
	0xf0, 0x94, 0xc7, 0x3a, 0x2d, 0x2f, 0x88, 0xe8, 0x86, 0xf4, 0x4a, 0xda, 0x6a, 0x59, 0xbd, 0x20,
	0xe2, 0x19, 0xbd, 0x1a, 0xa3, 0x2f, 0x66, 0xe9, 0x53, 0x71, 0xa5, 0x8c, 0x38, 0xfc, 0xcf, 0x02,
	0x34, 0xdc, 0x9a, 0x8b, 0x28, 0x5f, 0xf5, 0x5f, 0x00, 0x98, 0x20, 0xf7, 0x98, 0x50, 0xfa, 0x97,
	0xf6, 0xab, 0x87, 0x7b, 0x2d, 0x57, 0xb3, 0x6b, 0x8d, 0x52, 0xc6, 0xab, 0xf0, 0x44, 0x08, 0xfa,
	0x39, 0x54, 0x7a, 0x9c, 0x04, 0xdd, 0x3e, 0x8f, 0x4d, 0x2a, 0x56, 0x0f, 0x3f, 0x70, 0x93, 0x27,
	0x26, 0x7b, 0xe5, 0x9e, 0xfd, 0x85, 0x3b, 0xf0, 0x41, 0xa2, 0xb1, 0xe2, 0x9b, 0x68, 0x6c, 0x00,
	0xb3, 0x5d, 0x3e, 0xa3, 0x9c, 0x09, 0xec, 0xcd, 0xe4, 0x2a, 0xa2, 0xd4, 0x6a, 0x16, 0xbe, 0xe6,
	0x9a, 0xf3, 0xdc, 0x56, 0xab, 0x9f, 0xf8, 0x51, 0xea, 0x6a, 0x23, 0x22, 0xa3, 0x72, 0x56, 0xb3,
	0xc2, 0xb8, 0x66, 0xbf, 0xd2, 0x8d, 0xc6, 0x45, 0xf6, 0x23, 0xe8, 0x74, 0x01, 0xbb, 0x2f, 0x74,
	0x1a, 0x2f, 0xaa, 0x56, 0xa6, 0x9a, 0x8b, 0x63, 0xd5, 0x9c, 0x16, 0x59, 0x29, 0x5b, 0x64, 0xf8,
	0x73, 0x78, 0x2f, 0x5f, 0xd2, 0xac, 0x26, 0x8c, 0xbf, 0x83, 0xfa, 0x31, 0x0b, 0x83, 0x97, 0xbf,
	0x54, 0x21, 0x79, 0x4e, 0x86, 0xda, 0x86, 0x59, 0x61, 0x46, 0xb0, 0xec, 0xf3, 0x80, 0xda, 0x10,
	0xeb, 0xdf, 0x2a, 0xfd, 0x95, 0x82, 0xd4, 0x6a, 0x65, 0x0e, 0xf8, 0x07, 0xb8, 0xee, 0x60, 0x2d,
	0xa2, 0x8c, 0x75, 0x46, 0x99, 0x8c, 0x75, 0x3c, 0xa2, 0x61, 0x3b, 0xc9, 0x1f, 0x7b, 0x52, 0x0f,
	0x58, 0xc8, 0xfc, 0x37, 0xdd, 0x90, 0xf4, 0x13, 0x11, 0x65, 0x05, 0x78, 0x46, 0xfa, 0x34, 0x6b,
	0xda, 0xf2, 0xb8, 0x69, 0xdf, 0xc2, 0xd6, 0x29, 0x95, 0x4a, 0x05, 0x16, 0x9e, 0xff, 0x88, 0x76,
	0xfd, 0x0e, 0xd0, 0x24, 0xdf, 0xff, 0x9f, 0x51, 0x5b, 0xb0, 0x71, 0x4a, 0xe5, 0x91, 0x2f, 0xbf,
	0xe0, 0xe1, 0x6b, 0x76, 0xee, 0xd1, 0xef, 0xf1, 0xdf, 0x0b, 0xb0, 0x39, 0x0e, 0x13, 0x11, 0x7a,
	0x1f, 0xe0, 0x8c, 0x9e, 0xb3, 0xd0, 0x34, 0x55, 0xa3, 0x52, 0x45, 0x43, 0x74, 0xd3, 0xbd, 0x09,
	0x65, 0x1a, 0x06, 0xe6, 0xd2, 0x16, 0x2b, 0x0d, 0xf5, 0x00, 0x82, 0x0e, 0xa1, 0xfe, 0x65, 0x18,
	0x28, 0x86, 0xa3, 0xf4, 0x56, 0x70, 0xab, 0xa3, 0xf3, 0x0e, 0xdd, 0x82, 0xea, 0x97, 0x61, 0xd0,
	0x21, 0xe2, 0x8d, 0x46, 0x35, 0x3a, 0x67, 0x41, 0xf8, 0x21, 0x6c, 0x27, 0xb3, 0x0e, 0x93, 0x5f,
	0xf1, 0xf8, 0x25, 0x3d, 0x53, 0xe1, 0x78, 0x1f, 0xc0, 0xd4, 0x52, 0x37, 0x1d, 0x62, 0x2a, 0x06,
	0xf2, 0x82, 0x05, 0xf8, 0x39, 0xac, 0xa7, 0x92, 0xf4, 0xdb, 0x38, 0x35, 0xee, 0xa8, 0x47, 0x49,
	0xf9, 0x4d, 0xfb, 0xb1, 0x98, 0xfa, 0x51, 0x9d, 0x15, 0xb6, 0xa0, 0x57, 0x5a, 0xf5, 0x6b, 0x9e,
	0xfa, 0x89, 0xff, 0x53, 0x84, 0xfa, 0xb4, 0x22, 0x22, 0x42, 0x1d, 0xd8, 0x26, 0xbd, 0x98, 0x92,
	0x60, 0xd8, 0x8d, 0x7a, 0x64, 0x98, 0xf4, 0xdf, 0x82, 0xee, 0xbf, 0x77, 0xdc, 0x55, 0x3f, 0xae,
	0x9b, 0xb7, 0x65, 0x19, 0x3c, 0xd7, 0xf4, 0xba, 0x19, 0xdf, 0x86, 0xb5, 0x98, 0xfa, 0xf1, 0x80,
	0xc9, 0xd4, 0xd7, 0x35, 0xaf, 0x6a, 0x61, 0xda, 0x77, 0x07, 0xb0, 0x7d, 0xc9, 0xe4, 0x85, 0x1a,
	0xa3, 0xf4, 0x14, 0xc1, 0x42, 0x9f, 0x5b, 0x77, 0xd7, 0x3c, 0x94, 0xbd, 0x6a, 0xeb, 0x1b, 0xc5,
	0xd3, 0xcc, 0x31, 0x16, 0x73, 0xd9, 0xf0, 0xd4, 0x30, 0x8b, 0xf2, 0x21, 0xd4, 0xd2, 0xb1, 0x24,
	0x1c, 0xf4, 0xf5, 0xdb, 0x5e, 0xf3, 0xd6, 0x46, 0xc0, 0x67, 0x83, 0xbe, 0x1e, 0x75, 0x82, 0x80,
	0x49, 0xc6, 0xc3, 0x94, 0xd9, 0x8a, 0x46, 0xdc, 0x4c, 0x2f, 0xb2, 0x42, 0x03, 0x32, 0x4c, 0xf0,
	0x56, 0x13, 0xa1, 0x81, 0x2a, 0x6c, 0x05, 0xc2, 0xaf, 0xf4, 0x3b, 0xfd, 0x98, 0x09, 0xc9, 0xe3,
	0x61, 0xea, 0x1a, 0xe7, 0x90, 0xba, 0xe8, 0x03, 0xfd, 0x97, 0x02, 0x6c, 0x9d, 0x90, 0xe1, 0x44,
	0x32, 0xec, 0x42, 0xa5, 0xcf, 0x43, 0x79, 0xd1, 0x0d, 0xc8, 0xd0, 0xe6, 0x78, 0x59, 0x03, 0x4e,
	0xc8, 0x10, 0x3d, 0x85, 0x8d, 0x34, 0x54, 0xd9, 0xc7, 0x74, 0xbe, 0x60, 0xae, 0xa7, 0x57, 0x3a,
	0x92, 0x37, 0x60, 0x25, 0x13, 0x99, 0x92, 0x67, 0x4f, 0xf8, 0x1f, 0xe6, 0x91, 0x77, 0x98, 0x2d,
	0x22, 0xf4, 0x7c, 0x5a, 0x07, 0x93, 0x50, 0x1f, 0xbb, 0x75, 0x98, 0x32, 0x71, 0x4a, 0x8d, 0xb1,
	0xd7, 0xbd, 0xb8, 0xe0, 0xeb, 0xfe, 0x03, 0xec, 0x98, 0xdc, 0x7f, 0xca, 0x43, 0x26, 0x79, 0xdc,
	0x0e, 0x3b, 0x2a, 0x7e, 0x2a, 0x42, 0xb7, 0x61, 0x4d, 0x87, 0x20, 0x89, 0xaf, 0x09, 0x55, 0x55,
	0xc3, 0x6c, 0x0a, 0xdc, 0x85, 0x75, 0x83, 0x92, 0xe4, 0xa4, 0xcd, 0xe6, 0x9a, 0x86, 0xbe, 0xb4,
	0x40, 0xd5, 0x5a, 0xfc, 0x0b, 0xea, 0xbf, 0xe9, 0xca, 0x64, 0x16, 0x5d, 0xd5, 0xe7, 0x8e, 0xc0,
	0x8f, 0x61, 0xc3, 0xc8, 0x7f, 0x62, 0xd8, 0xbe, 0xe6, 0xef, 0x68, 0x00, 0x99, 0xe9, 0xd4, 0xc8,
	0xb2, 0x27, 0xfc, 0xef, 0x02, 0x34, 0xdc, 0xa6, 0x88, 0x08, 0x7d, 0x07, 0xd7, 0xe9, 0x95, 0x4f,
	0x69, 0x60, 0x8d, 0xb1, 0xdf, 0x3e, 0xc2, 0xfa, 0xfe, 0xae, 0xdb, 0x5f, 0x13, 0x9a, 0x79, 0xdb,
	0x86, 0x87, 0x31, 0xde, 0x5c, 0x0a, 0xf4, 0x1b, 0xd8, 0xb1, 0xac, 0x13, 0x27, 0x8c, 0x98, 0x17,
	0x17, 0x61, 0x6e, 0x15, 0x4c, 0x9c, 0x66, 0xd9, 0xe3, 0x96, 0xce, 0xa5, 0x23, 0x5f, 0xb2, 0xb7,
	0x4c, 0x0e, 0xbf, 0x91, 0x44, 0x32, 0x21, 0x99, 0x2f, 0xec, 0x57, 0xa8, 0x7a, 0xe1, 0x6d, 0x9e,
	0xeb, 0xdf, 0xf8, 0xaf, 0x45, 0x40, 0xd3, 0xd8, 0x08, 0xc3, 0x5a, 0x27, 0xf3, 0x51, 0x93, 0x7c,
	0xe8, 0x64, 0x61, 0xe8, 0x0e, 0xd4, 0xf4, 0xf9, 0x65, 0x36, 0x98, 0x25, 0x6f, 0x1c, 0x88, 0xee,
	0xc1, 0xe6, 0xd1, 0xc4, 0x47, 0x8c, 0x0d, 0xea, 0x14, 0x5c, 0x3d, 0x02, 0x47, 0x97, 0x24, 0x0e,
	0xda, 0xe1, 0x89, 0xd2, 0xd3, 0x7c, 0x6a, 0x64, 0x41, 0xe8, 0x23, 0x58, 0x4f, 0x38, 0x5b, 0x24,
	0xf3, 0xcd, 0x31, 0x01, 0x55, 0x2d, 0xfd, 0xe8, 0xed, 0xb9, 0xa6, 0xb4, 0xdf, 0x1f, 0xa3, 0xb3,
	0xe2, 0x91, 0xfc, 0xb6, 0x3c, 0x56, 0x0d, 0x8f, 0x71, 0x28, 0xa6, 0x7a, 0xb2, 0x73, 0xb9, 0x52,
	0x44, 0xe8, 0x31, 0x80, 0x18, 0x41, 0xec, 0x64, 0xb7, 0x9f, 0xd3, 0x16, 0xa6, 0x39, 0x64, 0x68,
	0xf1, 0x9f, 0x0b, 0xb0, 0xe5, 0x51, 0x9f, 0xc7, 0x81, 0xea, 0xfa, 0x1e, 0xe7, 0x7d, 0x15, 0xab,
	0x3b, 0xb0, 0xae, 0x98, 0x75, 0xf9, 0x65, 0x38, 0x96, 0xd9, 0x6b, 0x0a, 0xfa, 0xb5, 0x02, 0xaa,
	0xe4, 0xde, 0x83, 0x6a, 0xf6, 0xa9, 0x29, 0xea, 0xf5, 0x02, 0x44, 0xe9, 0xeb, 0xf1, 0x1e, 0x54,
	0xd4, 0xab, 0x21, 0x24, 0xe9, 0x47, 0xd6, 0xed, 0x29, 0x40, 0xb5, 0x50, 0x7a, 0x25, 0x63, 0x62,
	0x9f, 0x5b, 0x73, 0xc0, 0x75, 0x40, 0x93, 0xfa, 0x88, 0x08, 0x7f, 0x02, 0xf5, 0x2f, 0x54, 0x11,
	0xaa, 0x51, 0xac, 0x33, 0x8c, 0x74, 0x2f, 0xb1, 0x33, 0xe8, 0x80, 0x05, 0x69, 0x67, 0xaa, 0x79,
	0xab, 0x03, 0x16, 0xa8, 0x5b, 0x7c, 0x0a, 0x1b, 0x09, 0xb6, 0x26, 0xa5, 0x71, 0xfe, 0xf0, 0xb4,
	0x0b, 0x15, 0x7d, 0xa1, 0xbf, 0x18, 0x4d, 0xa5, 0x96, 0x07, 0x96, 0x18, 0xbf, 0x82, 0xeb, 0x0e,
	0xd9, 0x22, 0x42, 0x47, 0xb6, 0x53, 0xbc, 0xb3, 0x34, 0x27, 0xf4, 0xf0, 0x46, 0x64, 0x87, 0x7f,
	0xd8, 0x82, 0xba, 0x32, 0xd2, 0x14, 0x50, 0xda, 0x3b, 0x11, 0x85, 0xf5, 0xf1, 0xf5, 0x0e, 0xca,
	0x69, 0xb9, 0x53, 0xdb, 0xa2, 0xe6, 0xfe, 0x7c, 0x88, 0x22, 0xc2, 0x4b, 0x23, 0x31, 0xa3, 0x35,
	0xd0, 0x4c, 0x31, 0xd9, 0xad, 0xd2, 0x4c, 0x31, 0xe3, 0x5b, 0xa5, 0x25, 0x24, 0x61, 0xdb, 0xb1,
	0x2b, 0x42, 0x3f, 0x75, 0xb3, 0x70, 0xaf, 0xa0, 0x9a, 0x0f, 0x16, 0xc0, 0xd6, 0x52, 0xdf, 0xc0,
	0xe6, 0xe4, 0xa8, 0x84, 0x7e, 0x32, 0xab, 0xbf, 0x8d, 0xcd, 0x76, 0xcd, 0x7b, 0xf3, 0xa2, 0x6a,
	0x61, 0xdf, 0x40, 0x39, 0xd1, 0x04, 0xdd, 0x9e, 0xad, 0xa9, 0x62, 0x8e, 0xdf, 0x85, 0x92, 0x84,
	0x67, 0xbc, 0x18, 0xf2, 0xc2, 0x33, 0x55, 0xc2, 0x79, 0xe1, 0x71, 0xd4, 0xd6, 0x12, 0x0a, 0x61,
	0x6b, 0x2a, 0xc3, 0x51, 0x8e, 0xf9, 0xae, 0x32, 0x6c, 0xde, 0x9f, 0x1b, 0x57, 0xcb, 0xbb, 0x84,
	0xba, 0x6b, 0xe4, 0x40, 0x39, 0x11, 0xce, 0x99, 0xca, 0x9a, 0xad, 0x45, 0xd0, 0xb5, 0xe0, 0x5f,
	0x43, 0x35, 0xb3, 0xdf, 0x43, 0x39, 0x93, 0xd4, 0xf8, 0x42, 0xb1, 0x79, 0x77, 0x0e, 0x2c, 0xcd,
	0xfd, 0xf7, 0x70, 0xc3, 0xbd, 0xbe, 0x43, 0x07, 0xb9, 0x9a, 0xba, 0x77, 0x84, 0xcd, 0x9f, 0x2d,
	0x46, 0x90, 0xf1, 0xea, 0xd4, 0xb6, 0x66, 0x86, 0x57, 0x5d, 0x3b, 0xa9, 0x19, 0x5e, 0x75, 0x2e,
	0x82, 0xf0, 0x12, 0xfa, 0x53, 0x01, 0x76, 0x67, 0xec, 0x47, 0xd0, 0xc3, 0xd9, 0x1c, 0xdd, 0x8b,
	0x9a, 0xe6, 0xa3, 0xff, 0x81, 0x4a, 0xab, 0xf3, 0x5b, 0xb8, 0xee, 0xdc, 0x89, 0xa0, 0xd6, 0x3c,
	0x1c, 0x33, 0x1a, 0x1c, 0x2c, 0x84, 0xaf, 0x65, 0xff, 0xb1, 0x00, 0x8d, 0xbc, 0x4d, 0x06, 0xfa,
	0x24, 0xe7, 0x75, 0xc8, 0xdf, 0xb1, 0x34, 0x0f, 0x17, 0x25, 0x49, 0xea, 0x79, 0x6a, 0x73, 0x91,
	0x57, 0xcf, 0xae, 0xed, 0x49, 0x5e, 0x3d, 0x3b, 0xd7, 0x21, 0xa6, 0x4d, 0x8d, 0x6f, 0x14, 0xf2,
	0xda, 0xd4, 0xd4, 0x3e, 0x23, 0xaf, 0x4d, 0x4d, 0x2f, 0x28, 0xf0, 0x12, 0xea, 0xc2, 0x5a, 0x76,
	0x4f, 0x80, 0xee, 0xe6, 0xd2, 0x66, 0xf7, 0x0b, 0xcd, 0x8f, 0xe6, 0x41, 0x4b, 0x2a, 0xc8, 0x35,
	0x94, 0xe7, 0x55, 0x50, 0xce, 0xb7, 0x48, 0x5e, 0x05, 0xe5, 0xcd, 0xfb, 0xa3, 0x94, 0x75, 0x4c,
	0xc2, 0xad, 0x59, 0xba, 0x4f, 0x0f, 0xd9, 0x33, 0x52, 0xd6, 0x3d, 0x49, 0xe2, 0xa5, 0xe3, 0xcf,
	0x5f, 0x7d, 0x76, 0xce, 0x7b, 0x24, 0x3c, 0x6f, 0x3d, 0x3a, 0x94, 0xb2, 0xe5, 0xf3, 0xfe, 0x81,
	0xfe, 0x83, 0xca, 0xe7, 0xbd, 0x03, 0x41, 0xe3, 0xb7, 0xcc, 0xa7, 0xe2, 0xc0, 0xc5, 0xf5, 0x6c,
	0x45, 0xe3, 0x7d, 0xfa, 0xdf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x82, 0xca, 0x89, 0xd5, 0xff, 0x1a,
	0x00, 0x00,
}
