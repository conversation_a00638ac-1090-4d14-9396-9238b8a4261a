// Code generated by protoc-gen-go. DO NOT EDIT.
// source: masked-pk-live/masked-pk-live.proto

package masked_pk_live // import "golang.52tt.com/protocol/services/masked-pk-live"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type DivideType int32

const (
	DivideType_DIVIDE_TYPE_DEFAULT DivideType = 0
	DivideType_DIVIDE_TYPE_HALF    DivideType = 1
	DivideType_DIVIDE_TYPE_PERCENT DivideType = 2
)

var DivideType_name = map[int32]string{
	0: "DIVIDE_TYPE_DEFAULT",
	1: "DIVIDE_TYPE_HALF",
	2: "DIVIDE_TYPE_PERCENT",
}
var DivideType_value = map[string]int32{
	"DIVIDE_TYPE_DEFAULT": 0,
	"DIVIDE_TYPE_HALF":    1,
	"DIVIDE_TYPE_PERCENT": 2,
}

func (x DivideType) String() string {
	return proto.EnumName(DivideType_name, int32(x))
}
func (DivideType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{0}
}

type ChannelMaskedPKConf_ChipRole int32

const (
	ChannelMaskedPKConf_NoChip   ChannelMaskedPKConf_ChipRole = 0
	ChannelMaskedPKConf_HaveChip ChannelMaskedPKConf_ChipRole = 1
)

var ChannelMaskedPKConf_ChipRole_name = map[int32]string{
	0: "NoChip",
	1: "HaveChip",
}
var ChannelMaskedPKConf_ChipRole_value = map[string]int32{
	"NoChip":   0,
	"HaveChip": 1,
}

func (x ChannelMaskedPKConf_ChipRole) String() string {
	return proto.EnumName(ChannelMaskedPKConf_ChipRole_name, int32(x))
}
func (ChannelMaskedPKConf_ChipRole) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{2, 0}
}

type ChannelMaskedPKStatus_Status int32

const (
	ChannelMaskedPKStatus_NotParticipating ChannelMaskedPKStatus_Status = 0
	ChannelMaskedPKStatus_NotMatching      ChannelMaskedPKStatus_Status = 1
	ChannelMaskedPKStatus_PreMatching      ChannelMaskedPKStatus_Status = 2
	ChannelMaskedPKStatus_AutoInMatching   ChannelMaskedPKStatus_Status = 3
	ChannelMaskedPKStatus_ActiveInMatching ChannelMaskedPKStatus_Status = 4
	ChannelMaskedPKStatus_InPKing          ChannelMaskedPKStatus_Status = 5
	ChannelMaskedPKStatus_InReviving       ChannelMaskedPKStatus_Status = 6
	ChannelMaskedPKStatus_IsOut            ChannelMaskedPKStatus_Status = 7
)

var ChannelMaskedPKStatus_Status_name = map[int32]string{
	0: "NotParticipating",
	1: "NotMatching",
	2: "PreMatching",
	3: "AutoInMatching",
	4: "ActiveInMatching",
	5: "InPKing",
	6: "InReviving",
	7: "IsOut",
}
var ChannelMaskedPKStatus_Status_value = map[string]int32{
	"NotParticipating": 0,
	"NotMatching":      1,
	"PreMatching":      2,
	"AutoInMatching":   3,
	"ActiveInMatching": 4,
	"InPKing":          5,
	"InReviving":       6,
	"IsOut":            7,
}

func (x ChannelMaskedPKStatus_Status) String() string {
	return proto.EnumName(ChannelMaskedPKStatus_Status_name, int32(x))
}
func (ChannelMaskedPKStatus_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{3, 0}
}

type ChannelMaskedPKBattle_SubPhrase int32

const (
	ChannelMaskedPKBattle_Common    ChannelMaskedPKBattle_SubPhrase = 0
	ChannelMaskedPKBattle_QuickKill ChannelMaskedPKBattle_SubPhrase = 1
	ChannelMaskedPKBattle_PeakPk    ChannelMaskedPKBattle_SubPhrase = 2
)

var ChannelMaskedPKBattle_SubPhrase_name = map[int32]string{
	0: "Common",
	1: "QuickKill",
	2: "PeakPk",
}
var ChannelMaskedPKBattle_SubPhrase_value = map[string]int32{
	"Common":    0,
	"QuickKill": 1,
	"PeakPk":    2,
}

func (x ChannelMaskedPKBattle_SubPhrase) String() string {
	return proto.EnumName(ChannelMaskedPKBattle_SubPhrase_name, int32(x))
}
func (ChannelMaskedPKBattle_SubPhrase) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{8, 0}
}

type TestPushQuickKillChangeReq_EventType int32

const (
	TestPushQuickKillChangeReq_Unknown          TestPushQuickKillChangeReq_EventType = 0
	TestPushQuickKillChangeReq_TriggerQuickKill TestPushQuickKillChangeReq_EventType = 1
	TestPushQuickKillChangeReq_StopQuickKill    TestPushQuickKillChangeReq_EventType = 2
)

var TestPushQuickKillChangeReq_EventType_name = map[int32]string{
	0: "Unknown",
	1: "TriggerQuickKill",
	2: "StopQuickKill",
}
var TestPushQuickKillChangeReq_EventType_value = map[string]int32{
	"Unknown":          0,
	"TriggerQuickKill": 1,
	"StopQuickKill":    2,
}

func (x TestPushQuickKillChangeReq_EventType) String() string {
	return proto.EnumName(TestPushQuickKillChangeReq_EventType_name, int32(x))
}
func (TestPushQuickKillChangeReq_EventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{61, 0}
}

type UserPrivilege struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Options              []byte   `protobuf:"bytes,4,opt,name=options,proto3" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPrivilege) Reset()         { *m = UserPrivilege{} }
func (m *UserPrivilege) String() string { return proto.CompactTextString(m) }
func (*UserPrivilege) ProtoMessage()    {}
func (*UserPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{0}
}
func (m *UserPrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPrivilege.Unmarshal(m, b)
}
func (m *UserPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPrivilege.Marshal(b, m, deterministic)
}
func (dst *UserPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPrivilege.Merge(dst, src)
}
func (m *UserPrivilege) XXX_Size() int {
	return xxx_messageInfo_UserPrivilege.Size(m)
}
func (m *UserPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_UserPrivilege proto.InternalMessageInfo

func (m *UserPrivilege) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserPrivilege) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserPrivilege) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *UserPrivilege) GetOptions() []byte {
	if m != nil {
		return m.Options
	}
	return nil
}

type UserProfile struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string         `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string         `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	AccountAlias         string         `protobuf:"bytes,4,opt,name=account_alias,json=accountAlias,proto3" json:"account_alias,omitempty"`
	Sex                  uint32         `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	Privilege            *UserPrivilege `protobuf:"bytes,6,opt,name=privilege,proto3" json:"privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserProfile) Reset()         { *m = UserProfile{} }
func (m *UserProfile) String() string { return proto.CompactTextString(m) }
func (*UserProfile) ProtoMessage()    {}
func (*UserProfile) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{1}
}
func (m *UserProfile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserProfile.Unmarshal(m, b)
}
func (m *UserProfile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserProfile.Marshal(b, m, deterministic)
}
func (dst *UserProfile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserProfile.Merge(dst, src)
}
func (m *UserProfile) XXX_Size() int {
	return xxx_messageInfo_UserProfile.Size(m)
}
func (m *UserProfile) XXX_DiscardUnknown() {
	xxx_messageInfo_UserProfile.DiscardUnknown(m)
}

var xxx_messageInfo_UserProfile proto.InternalMessageInfo

func (m *UserProfile) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserProfile) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserProfile) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserProfile) GetAccountAlias() string {
	if m != nil {
		return m.AccountAlias
	}
	return ""
}

func (m *UserProfile) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserProfile) GetPrivilege() *UserPrivilege {
	if m != nil {
		return m.Privilege
	}
	return nil
}

// PK该场次配置信息
type ChannelMaskedPKConf struct {
	BeginTs              uint32   `protobuf:"varint,1,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,2,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	ChipRole             uint32   `protobuf:"varint,3,opt,name=chip_role,json=chipRole,proto3" json:"chip_role,omitempty"`
	Chip                 uint32   `protobuf:"varint,4,opt,name=chip,proto3" json:"chip,omitempty"`
	ContinueMatch        uint32   `protobuf:"varint,5,opt,name=continue_match,json=continueMatch,proto3" json:"continue_match,omitempty"`
	AutoMatchingCnt      uint32   `protobuf:"varint,6,opt,name=auto_matching_cnt,json=autoMatchingCnt,proto3" json:"auto_matching_cnt,omitempty"`
	JumpUrl              string   `protobuf:"bytes,7,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	ConfId               uint32   `protobuf:"varint,8,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	ChipNotEnough        bool     `protobuf:"varint,9,opt,name=chip_not_enough,json=chipNotEnough,proto3" json:"chip_not_enough,omitempty"`
	ServerNs             int64    `protobuf:"varint,10,opt,name=server_ns,json=serverNs,proto3" json:"server_ns,omitempty"`
	DivideType           uint32   `protobuf:"varint,11,opt,name=divide_type,json=divideType,proto3" json:"divide_type,omitempty"`
	UseBackpack          uint32   `protobuf:"varint,12,opt,name=use_backpack,json=useBackpack,proto3" json:"use_backpack,omitempty"`
	ChipReceiveEndTs     uint32   `protobuf:"varint,13,opt,name=chip_receive_end_ts,json=chipReceiveEndTs,proto3" json:"chip_receive_end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMaskedPKConf) Reset()         { *m = ChannelMaskedPKConf{} }
func (m *ChannelMaskedPKConf) String() string { return proto.CompactTextString(m) }
func (*ChannelMaskedPKConf) ProtoMessage()    {}
func (*ChannelMaskedPKConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{2}
}
func (m *ChannelMaskedPKConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMaskedPKConf.Unmarshal(m, b)
}
func (m *ChannelMaskedPKConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMaskedPKConf.Marshal(b, m, deterministic)
}
func (dst *ChannelMaskedPKConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMaskedPKConf.Merge(dst, src)
}
func (m *ChannelMaskedPKConf) XXX_Size() int {
	return xxx_messageInfo_ChannelMaskedPKConf.Size(m)
}
func (m *ChannelMaskedPKConf) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMaskedPKConf.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMaskedPKConf proto.InternalMessageInfo

func (m *ChannelMaskedPKConf) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetChipRole() uint32 {
	if m != nil {
		return m.ChipRole
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetChip() uint32 {
	if m != nil {
		return m.Chip
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetContinueMatch() uint32 {
	if m != nil {
		return m.ContinueMatch
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetAutoMatchingCnt() uint32 {
	if m != nil {
		return m.AutoMatchingCnt
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *ChannelMaskedPKConf) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetChipNotEnough() bool {
	if m != nil {
		return m.ChipNotEnough
	}
	return false
}

func (m *ChannelMaskedPKConf) GetServerNs() int64 {
	if m != nil {
		return m.ServerNs
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetDivideType() uint32 {
	if m != nil {
		return m.DivideType
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetUseBackpack() uint32 {
	if m != nil {
		return m.UseBackpack
	}
	return 0
}

func (m *ChannelMaskedPKConf) GetChipReceiveEndTs() uint32 {
	if m != nil {
		return m.ChipReceiveEndTs
	}
	return 0
}

// PK状态信息
type ChannelMaskedPKStatus struct {
	GameId               uint32               `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	ChannelId            uint32               `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CurrChip             uint32               `protobuf:"varint,3,opt,name=curr_chip,json=currChip,proto3" json:"curr_chip,omitempty"`
	WinCnt               uint32               `protobuf:"varint,4,opt,name=win_cnt,json=winCnt,proto3" json:"win_cnt,omitempty"`
	Status               uint32               `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	LossChip             uint32               `protobuf:"varint,6,opt,name=loss_chip,json=lossChip,proto3" json:"loss_chip,omitempty"`
	LossChipDesc         string               `protobuf:"bytes,7,opt,name=loss_chip_desc,json=lossChipDesc,proto3" json:"loss_chip_desc,omitempty"`
	StatusEndTs          uint32               `protobuf:"varint,8,opt,name=status_end_ts,json=statusEndTs,proto3" json:"status_end_ts,omitempty"`
	StatusDesc           string               `protobuf:"bytes,9,opt,name=status_desc,json=statusDesc,proto3" json:"status_desc,omitempty"`
	RestCancelCnt        uint32               `protobuf:"varint,10,opt,name=rest_cancel_cnt,json=restCancelCnt,proto3" json:"rest_cancel_cnt,omitempty"`
	RestReviveCnt        uint32               `protobuf:"varint,11,opt,name=rest_revive_cnt,json=restReviveCnt,proto3" json:"rest_revive_cnt,omitempty"`
	RevivedCnt           uint32               `protobuf:"varint,12,opt,name=revived_cnt,json=revivedCnt,proto3" json:"revived_cnt,omitempty"`
	PkConf               *ChannelMaskedPKConf `protobuf:"bytes,13,opt,name=pk_conf,json=pkConf,proto3" json:"pk_conf,omitempty"`
	ServerNs             int64                `protobuf:"varint,14,opt,name=server_ns,json=serverNs,proto3" json:"server_ns,omitempty"`
	LossCnt              uint32               `protobuf:"varint,15,opt,name=loss_cnt,json=lossCnt,proto3" json:"loss_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ChannelMaskedPKStatus) Reset()         { *m = ChannelMaskedPKStatus{} }
func (m *ChannelMaskedPKStatus) String() string { return proto.CompactTextString(m) }
func (*ChannelMaskedPKStatus) ProtoMessage()    {}
func (*ChannelMaskedPKStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{3}
}
func (m *ChannelMaskedPKStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMaskedPKStatus.Unmarshal(m, b)
}
func (m *ChannelMaskedPKStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMaskedPKStatus.Marshal(b, m, deterministic)
}
func (dst *ChannelMaskedPKStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMaskedPKStatus.Merge(dst, src)
}
func (m *ChannelMaskedPKStatus) XXX_Size() int {
	return xxx_messageInfo_ChannelMaskedPKStatus.Size(m)
}
func (m *ChannelMaskedPKStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMaskedPKStatus.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMaskedPKStatus proto.InternalMessageInfo

func (m *ChannelMaskedPKStatus) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetCurrChip() uint32 {
	if m != nil {
		return m.CurrChip
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetWinCnt() uint32 {
	if m != nil {
		return m.WinCnt
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetLossChip() uint32 {
	if m != nil {
		return m.LossChip
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetLossChipDesc() string {
	if m != nil {
		return m.LossChipDesc
	}
	return ""
}

func (m *ChannelMaskedPKStatus) GetStatusEndTs() uint32 {
	if m != nil {
		return m.StatusEndTs
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetStatusDesc() string {
	if m != nil {
		return m.StatusDesc
	}
	return ""
}

func (m *ChannelMaskedPKStatus) GetRestCancelCnt() uint32 {
	if m != nil {
		return m.RestCancelCnt
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetRestReviveCnt() uint32 {
	if m != nil {
		return m.RestReviveCnt
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetRevivedCnt() uint32 {
	if m != nil {
		return m.RevivedCnt
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetPkConf() *ChannelMaskedPKConf {
	if m != nil {
		return m.PkConf
	}
	return nil
}

func (m *ChannelMaskedPKStatus) GetServerNs() int64 {
	if m != nil {
		return m.ServerNs
	}
	return 0
}

func (m *ChannelMaskedPKStatus) GetLossCnt() uint32 {
	if m != nil {
		return m.LossCnt
	}
	return 0
}

// PK榜单成员简略信息
type ChannelMaskedPKRankMem struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Score                uint32       `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	Profile              *UserProfile `protobuf:"bytes,3,opt,name=profile,proto3" json:"profile,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChannelMaskedPKRankMem) Reset()         { *m = ChannelMaskedPKRankMem{} }
func (m *ChannelMaskedPKRankMem) String() string { return proto.CompactTextString(m) }
func (*ChannelMaskedPKRankMem) ProtoMessage()    {}
func (*ChannelMaskedPKRankMem) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{4}
}
func (m *ChannelMaskedPKRankMem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMaskedPKRankMem.Unmarshal(m, b)
}
func (m *ChannelMaskedPKRankMem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMaskedPKRankMem.Marshal(b, m, deterministic)
}
func (dst *ChannelMaskedPKRankMem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMaskedPKRankMem.Merge(dst, src)
}
func (m *ChannelMaskedPKRankMem) XXX_Size() int {
	return xxx_messageInfo_ChannelMaskedPKRankMem.Size(m)
}
func (m *ChannelMaskedPKRankMem) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMaskedPKRankMem.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMaskedPKRankMem proto.InternalMessageInfo

func (m *ChannelMaskedPKRankMem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMaskedPKRankMem) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ChannelMaskedPKRankMem) GetProfile() *UserProfile {
	if m != nil {
		return m.Profile
	}
	return nil
}

// PK进行PK中的信息
type ChannelMaskedPKInfo struct {
	ChannelId            uint32                    `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CurrChip             uint32                    `protobuf:"varint,2,opt,name=curr_chip,json=currChip,proto3" json:"curr_chip,omitempty"`
	WinCnt               uint32                    `protobuf:"varint,3,opt,name=win_cnt,json=winCnt,proto3" json:"win_cnt,omitempty"`
	ReviveCnt            uint32                    `protobuf:"varint,6,opt,name=revive_cnt,json=reviveCnt,proto3" json:"revive_cnt,omitempty"`
	Score                uint32                    `protobuf:"varint,7,opt,name=score,proto3" json:"score,omitempty"`
	LossDesc             string                    `protobuf:"bytes,8,opt,name=loss_desc,json=lossDesc,proto3" json:"loss_desc,omitempty"`
	TopAnchorList        []*ChannelMaskedPKRankMem `protobuf:"bytes,9,rep,name=top_anchor_list,json=topAnchorList,proto3" json:"top_anchor_list,omitempty"`
	LossChip             uint32                    `protobuf:"varint,10,opt,name=loss_chip,json=lossChip,proto3" json:"loss_chip,omitempty"`
	QuickKillInfo        *QuickKillInfo            `protobuf:"bytes,11,opt,name=quick_kill_info,json=quickKillInfo,proto3" json:"quick_kill_info,omitempty"`
	PeakPkInfo           *PeakPkInfo               `protobuf:"bytes,12,opt,name=peak_pk_info,json=peakPkInfo,proto3" json:"peak_pk_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *ChannelMaskedPKInfo) Reset()         { *m = ChannelMaskedPKInfo{} }
func (m *ChannelMaskedPKInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelMaskedPKInfo) ProtoMessage()    {}
func (*ChannelMaskedPKInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{5}
}
func (m *ChannelMaskedPKInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMaskedPKInfo.Unmarshal(m, b)
}
func (m *ChannelMaskedPKInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMaskedPKInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelMaskedPKInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMaskedPKInfo.Merge(dst, src)
}
func (m *ChannelMaskedPKInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelMaskedPKInfo.Size(m)
}
func (m *ChannelMaskedPKInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMaskedPKInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMaskedPKInfo proto.InternalMessageInfo

func (m *ChannelMaskedPKInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMaskedPKInfo) GetCurrChip() uint32 {
	if m != nil {
		return m.CurrChip
	}
	return 0
}

func (m *ChannelMaskedPKInfo) GetWinCnt() uint32 {
	if m != nil {
		return m.WinCnt
	}
	return 0
}

func (m *ChannelMaskedPKInfo) GetReviveCnt() uint32 {
	if m != nil {
		return m.ReviveCnt
	}
	return 0
}

func (m *ChannelMaskedPKInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ChannelMaskedPKInfo) GetLossDesc() string {
	if m != nil {
		return m.LossDesc
	}
	return ""
}

func (m *ChannelMaskedPKInfo) GetTopAnchorList() []*ChannelMaskedPKRankMem {
	if m != nil {
		return m.TopAnchorList
	}
	return nil
}

func (m *ChannelMaskedPKInfo) GetLossChip() uint32 {
	if m != nil {
		return m.LossChip
	}
	return 0
}

func (m *ChannelMaskedPKInfo) GetQuickKillInfo() *QuickKillInfo {
	if m != nil {
		return m.QuickKillInfo
	}
	return nil
}

func (m *ChannelMaskedPKInfo) GetPeakPkInfo() *PeakPkInfo {
	if m != nil {
		return m.PeakPkInfo
	}
	return nil
}

// 斩杀信息
type QuickKillInfo struct {
	QuickKillDescPrefix  string   `protobuf:"bytes,1,opt,name=quick_kill_desc_prefix,json=quickKillDescPrefix,proto3" json:"quick_kill_desc_prefix,omitempty"`
	QuickKillDesc        string   `protobuf:"bytes,2,opt,name=quick_kill_desc,json=quickKillDesc,proto3" json:"quick_kill_desc,omitempty"`
	QuickKillEndTs       uint32   `protobuf:"varint,3,opt,name=quick_kill_end_ts,json=quickKillEndTs,proto3" json:"quick_kill_end_ts,omitempty"`
	EnableMinPkSec       uint32   `protobuf:"varint,4,opt,name=enable_min_pk_sec,json=enableMinPkSec,proto3" json:"enable_min_pk_sec,omitempty"`
	EnableMaxPkSec       uint32   `protobuf:"varint,5,opt,name=enable_max_pk_sec,json=enableMaxPkSec,proto3" json:"enable_max_pk_sec,omitempty"`
	ConditionValue       uint32   `protobuf:"varint,6,opt,name=condition_value,json=conditionValue,proto3" json:"condition_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuickKillInfo) Reset()         { *m = QuickKillInfo{} }
func (m *QuickKillInfo) String() string { return proto.CompactTextString(m) }
func (*QuickKillInfo) ProtoMessage()    {}
func (*QuickKillInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{6}
}
func (m *QuickKillInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickKillInfo.Unmarshal(m, b)
}
func (m *QuickKillInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickKillInfo.Marshal(b, m, deterministic)
}
func (dst *QuickKillInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickKillInfo.Merge(dst, src)
}
func (m *QuickKillInfo) XXX_Size() int {
	return xxx_messageInfo_QuickKillInfo.Size(m)
}
func (m *QuickKillInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickKillInfo.DiscardUnknown(m)
}

var xxx_messageInfo_QuickKillInfo proto.InternalMessageInfo

func (m *QuickKillInfo) GetQuickKillDescPrefix() string {
	if m != nil {
		return m.QuickKillDescPrefix
	}
	return ""
}

func (m *QuickKillInfo) GetQuickKillDesc() string {
	if m != nil {
		return m.QuickKillDesc
	}
	return ""
}

func (m *QuickKillInfo) GetQuickKillEndTs() uint32 {
	if m != nil {
		return m.QuickKillEndTs
	}
	return 0
}

func (m *QuickKillInfo) GetEnableMinPkSec() uint32 {
	if m != nil {
		return m.EnableMinPkSec
	}
	return 0
}

func (m *QuickKillInfo) GetEnableMaxPkSec() uint32 {
	if m != nil {
		return m.EnableMaxPkSec
	}
	return 0
}

func (m *QuickKillInfo) GetConditionValue() uint32 {
	if m != nil {
		return m.ConditionValue
	}
	return 0
}

// 巅峰对决信息
type PeakPkInfo struct {
	PeakDesc             string   `protobuf:"bytes,1,opt,name=peak_desc,json=peakDesc,proto3" json:"peak_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PeakPkInfo) Reset()         { *m = PeakPkInfo{} }
func (m *PeakPkInfo) String() string { return proto.CompactTextString(m) }
func (*PeakPkInfo) ProtoMessage()    {}
func (*PeakPkInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{7}
}
func (m *PeakPkInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeakPkInfo.Unmarshal(m, b)
}
func (m *PeakPkInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeakPkInfo.Marshal(b, m, deterministic)
}
func (dst *PeakPkInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeakPkInfo.Merge(dst, src)
}
func (m *PeakPkInfo) XXX_Size() int {
	return xxx_messageInfo_PeakPkInfo.Size(m)
}
func (m *PeakPkInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PeakPkInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PeakPkInfo proto.InternalMessageInfo

func (m *PeakPkInfo) GetPeakDesc() string {
	if m != nil {
		return m.PeakDesc
	}
	return ""
}

// PK实时战况
type ChannelMaskedPKBattle struct {
	MemList              []*ChannelMaskedPKInfo `protobuf:"bytes,1,rep,name=mem_list,json=memList,proto3" json:"mem_list,omitempty"`
	PkEndTs              uint32                 `protobuf:"varint,2,opt,name=pk_end_ts,json=pkEndTs,proto3" json:"pk_end_ts,omitempty"`
	ChipRole             uint32                 `protobuf:"varint,3,opt,name=chip_role,json=chipRole,proto3" json:"chip_role,omitempty"`
	ServerNs             int64                  `protobuf:"varint,4,opt,name=server_ns,json=serverNs,proto3" json:"server_ns,omitempty"`
	ValidPkDesc          string                 `protobuf:"bytes,5,opt,name=valid_pk_desc,json=validPkDesc,proto3" json:"valid_pk_desc,omitempty"`
	ValidPk              bool                   `protobuf:"varint,6,opt,name=valid_pk,json=validPk,proto3" json:"valid_pk,omitempty"`
	PkId                 uint32                 `protobuf:"varint,7,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	SubPhrase            uint32                 `protobuf:"varint,8,opt,name=sub_phrase,json=subPhrase,proto3" json:"sub_phrase,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ChannelMaskedPKBattle) Reset()         { *m = ChannelMaskedPKBattle{} }
func (m *ChannelMaskedPKBattle) String() string { return proto.CompactTextString(m) }
func (*ChannelMaskedPKBattle) ProtoMessage()    {}
func (*ChannelMaskedPKBattle) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{8}
}
func (m *ChannelMaskedPKBattle) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMaskedPKBattle.Unmarshal(m, b)
}
func (m *ChannelMaskedPKBattle) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMaskedPKBattle.Marshal(b, m, deterministic)
}
func (dst *ChannelMaskedPKBattle) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMaskedPKBattle.Merge(dst, src)
}
func (m *ChannelMaskedPKBattle) XXX_Size() int {
	return xxx_messageInfo_ChannelMaskedPKBattle.Size(m)
}
func (m *ChannelMaskedPKBattle) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMaskedPKBattle.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMaskedPKBattle proto.InternalMessageInfo

func (m *ChannelMaskedPKBattle) GetMemList() []*ChannelMaskedPKInfo {
	if m != nil {
		return m.MemList
	}
	return nil
}

func (m *ChannelMaskedPKBattle) GetPkEndTs() uint32 {
	if m != nil {
		return m.PkEndTs
	}
	return 0
}

func (m *ChannelMaskedPKBattle) GetChipRole() uint32 {
	if m != nil {
		return m.ChipRole
	}
	return 0
}

func (m *ChannelMaskedPKBattle) GetServerNs() int64 {
	if m != nil {
		return m.ServerNs
	}
	return 0
}

func (m *ChannelMaskedPKBattle) GetValidPkDesc() string {
	if m != nil {
		return m.ValidPkDesc
	}
	return ""
}

func (m *ChannelMaskedPKBattle) GetValidPk() bool {
	if m != nil {
		return m.ValidPk
	}
	return false
}

func (m *ChannelMaskedPKBattle) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *ChannelMaskedPKBattle) GetSubPhrase() uint32 {
	if m != nil {
		return m.SubPhrase
	}
	return 0
}

// PK复活进度
type ChannelMaskedPKRevive struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Goal                 uint32   `protobuf:"varint,2,opt,name=goal,proto3" json:"goal,omitempty"`
	Curr                 uint32   `protobuf:"varint,3,opt,name=curr,proto3" json:"curr,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	ServerNs             int64    `protobuf:"varint,5,opt,name=server_ns,json=serverNs,proto3" json:"server_ns,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMaskedPKRevive) Reset()         { *m = ChannelMaskedPKRevive{} }
func (m *ChannelMaskedPKRevive) String() string { return proto.CompactTextString(m) }
func (*ChannelMaskedPKRevive) ProtoMessage()    {}
func (*ChannelMaskedPKRevive) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{9}
}
func (m *ChannelMaskedPKRevive) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMaskedPKRevive.Unmarshal(m, b)
}
func (m *ChannelMaskedPKRevive) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMaskedPKRevive.Marshal(b, m, deterministic)
}
func (dst *ChannelMaskedPKRevive) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMaskedPKRevive.Merge(dst, src)
}
func (m *ChannelMaskedPKRevive) XXX_Size() int {
	return xxx_messageInfo_ChannelMaskedPKRevive.Size(m)
}
func (m *ChannelMaskedPKRevive) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMaskedPKRevive.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMaskedPKRevive proto.InternalMessageInfo

func (m *ChannelMaskedPKRevive) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMaskedPKRevive) GetGoal() uint32 {
	if m != nil {
		return m.Goal
	}
	return 0
}

func (m *ChannelMaskedPKRevive) GetCurr() uint32 {
	if m != nil {
		return m.Curr
	}
	return 0
}

func (m *ChannelMaskedPKRevive) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *ChannelMaskedPKRevive) GetServerNs() int64 {
	if m != nil {
		return m.ServerNs
	}
	return 0
}

// 获取房间PK信息
type GetLiveChannelMaskedPKInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveChannelMaskedPKInfoReq) Reset()         { *m = GetLiveChannelMaskedPKInfoReq{} }
func (m *GetLiveChannelMaskedPKInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveChannelMaskedPKInfoReq) ProtoMessage()    {}
func (*GetLiveChannelMaskedPKInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{10}
}
func (m *GetLiveChannelMaskedPKInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveChannelMaskedPKInfoReq.Unmarshal(m, b)
}
func (m *GetLiveChannelMaskedPKInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveChannelMaskedPKInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveChannelMaskedPKInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveChannelMaskedPKInfoReq.Merge(dst, src)
}
func (m *GetLiveChannelMaskedPKInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveChannelMaskedPKInfoReq.Size(m)
}
func (m *GetLiveChannelMaskedPKInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveChannelMaskedPKInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveChannelMaskedPKInfoReq proto.InternalMessageInfo

func (m *GetLiveChannelMaskedPKInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLiveChannelMaskedPKInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetLiveChannelMaskedPKInfoResp struct {
	StatusInfo           *ChannelMaskedPKStatus `protobuf:"bytes,1,opt,name=status_info,json=statusInfo,proto3" json:"status_info,omitempty"`
	PkBattleInfo         *ChannelMaskedPKBattle `protobuf:"bytes,2,opt,name=pk_battle_info,json=pkBattleInfo,proto3" json:"pk_battle_info,omitempty"`
	ReviveInfo           *ChannelMaskedPKRevive `protobuf:"bytes,3,opt,name=revive_info,json=reviveInfo,proto3" json:"revive_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetLiveChannelMaskedPKInfoResp) Reset()         { *m = GetLiveChannelMaskedPKInfoResp{} }
func (m *GetLiveChannelMaskedPKInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveChannelMaskedPKInfoResp) ProtoMessage()    {}
func (*GetLiveChannelMaskedPKInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{11}
}
func (m *GetLiveChannelMaskedPKInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveChannelMaskedPKInfoResp.Unmarshal(m, b)
}
func (m *GetLiveChannelMaskedPKInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveChannelMaskedPKInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveChannelMaskedPKInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveChannelMaskedPKInfoResp.Merge(dst, src)
}
func (m *GetLiveChannelMaskedPKInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveChannelMaskedPKInfoResp.Size(m)
}
func (m *GetLiveChannelMaskedPKInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveChannelMaskedPKInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveChannelMaskedPKInfoResp proto.InternalMessageInfo

func (m *GetLiveChannelMaskedPKInfoResp) GetStatusInfo() *ChannelMaskedPKStatus {
	if m != nil {
		return m.StatusInfo
	}
	return nil
}

func (m *GetLiveChannelMaskedPKInfoResp) GetPkBattleInfo() *ChannelMaskedPKBattle {
	if m != nil {
		return m.PkBattleInfo
	}
	return nil
}

func (m *GetLiveChannelMaskedPKInfoResp) GetReviveInfo() *ChannelMaskedPKRevive {
	if m != nil {
		return m.ReviveInfo
	}
	return nil
}

// 开始PK匹配
type StartLiveChannelMaskedPKReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartLiveChannelMaskedPKReq) Reset()         { *m = StartLiveChannelMaskedPKReq{} }
func (m *StartLiveChannelMaskedPKReq) String() string { return proto.CompactTextString(m) }
func (*StartLiveChannelMaskedPKReq) ProtoMessage()    {}
func (*StartLiveChannelMaskedPKReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{12}
}
func (m *StartLiveChannelMaskedPKReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartLiveChannelMaskedPKReq.Unmarshal(m, b)
}
func (m *StartLiveChannelMaskedPKReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartLiveChannelMaskedPKReq.Marshal(b, m, deterministic)
}
func (dst *StartLiveChannelMaskedPKReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartLiveChannelMaskedPKReq.Merge(dst, src)
}
func (m *StartLiveChannelMaskedPKReq) XXX_Size() int {
	return xxx_messageInfo_StartLiveChannelMaskedPKReq.Size(m)
}
func (m *StartLiveChannelMaskedPKReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartLiveChannelMaskedPKReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartLiveChannelMaskedPKReq proto.InternalMessageInfo

func (m *StartLiveChannelMaskedPKReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartLiveChannelMaskedPKReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type StartLiveChannelMaskedPKResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartLiveChannelMaskedPKResp) Reset()         { *m = StartLiveChannelMaskedPKResp{} }
func (m *StartLiveChannelMaskedPKResp) String() string { return proto.CompactTextString(m) }
func (*StartLiveChannelMaskedPKResp) ProtoMessage()    {}
func (*StartLiveChannelMaskedPKResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{13}
}
func (m *StartLiveChannelMaskedPKResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartLiveChannelMaskedPKResp.Unmarshal(m, b)
}
func (m *StartLiveChannelMaskedPKResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartLiveChannelMaskedPKResp.Marshal(b, m, deterministic)
}
func (dst *StartLiveChannelMaskedPKResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartLiveChannelMaskedPKResp.Merge(dst, src)
}
func (m *StartLiveChannelMaskedPKResp) XXX_Size() int {
	return xxx_messageInfo_StartLiveChannelMaskedPKResp.Size(m)
}
func (m *StartLiveChannelMaskedPKResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartLiveChannelMaskedPKResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartLiveChannelMaskedPKResp proto.InternalMessageInfo

// 放弃参加本次比赛
type GiveUpLiveChannelMaskedPKReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ConfId               uint32   `protobuf:"varint,3,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiveUpLiveChannelMaskedPKReq) Reset()         { *m = GiveUpLiveChannelMaskedPKReq{} }
func (m *GiveUpLiveChannelMaskedPKReq) String() string { return proto.CompactTextString(m) }
func (*GiveUpLiveChannelMaskedPKReq) ProtoMessage()    {}
func (*GiveUpLiveChannelMaskedPKReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{14}
}
func (m *GiveUpLiveChannelMaskedPKReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiveUpLiveChannelMaskedPKReq.Unmarshal(m, b)
}
func (m *GiveUpLiveChannelMaskedPKReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiveUpLiveChannelMaskedPKReq.Marshal(b, m, deterministic)
}
func (dst *GiveUpLiveChannelMaskedPKReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiveUpLiveChannelMaskedPKReq.Merge(dst, src)
}
func (m *GiveUpLiveChannelMaskedPKReq) XXX_Size() int {
	return xxx_messageInfo_GiveUpLiveChannelMaskedPKReq.Size(m)
}
func (m *GiveUpLiveChannelMaskedPKReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GiveUpLiveChannelMaskedPKReq.DiscardUnknown(m)
}

var xxx_messageInfo_GiveUpLiveChannelMaskedPKReq proto.InternalMessageInfo

func (m *GiveUpLiveChannelMaskedPKReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GiveUpLiveChannelMaskedPKReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GiveUpLiveChannelMaskedPKReq) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

type GiveUpLiveChannelMaskedPKResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiveUpLiveChannelMaskedPKResp) Reset()         { *m = GiveUpLiveChannelMaskedPKResp{} }
func (m *GiveUpLiveChannelMaskedPKResp) String() string { return proto.CompactTextString(m) }
func (*GiveUpLiveChannelMaskedPKResp) ProtoMessage()    {}
func (*GiveUpLiveChannelMaskedPKResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{15}
}
func (m *GiveUpLiveChannelMaskedPKResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiveUpLiveChannelMaskedPKResp.Unmarshal(m, b)
}
func (m *GiveUpLiveChannelMaskedPKResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiveUpLiveChannelMaskedPKResp.Marshal(b, m, deterministic)
}
func (dst *GiveUpLiveChannelMaskedPKResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiveUpLiveChannelMaskedPKResp.Merge(dst, src)
}
func (m *GiveUpLiveChannelMaskedPKResp) XXX_Size() int {
	return xxx_messageInfo_GiveUpLiveChannelMaskedPKResp.Size(m)
}
func (m *GiveUpLiveChannelMaskedPKResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GiveUpLiveChannelMaskedPKResp.DiscardUnknown(m)
}

var xxx_messageInfo_GiveUpLiveChannelMaskedPKResp proto.InternalMessageInfo

// 取消PK匹配
type CancelLiveChannelMaskedPKReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelLiveChannelMaskedPKReq) Reset()         { *m = CancelLiveChannelMaskedPKReq{} }
func (m *CancelLiveChannelMaskedPKReq) String() string { return proto.CompactTextString(m) }
func (*CancelLiveChannelMaskedPKReq) ProtoMessage()    {}
func (*CancelLiveChannelMaskedPKReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{16}
}
func (m *CancelLiveChannelMaskedPKReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelLiveChannelMaskedPKReq.Unmarshal(m, b)
}
func (m *CancelLiveChannelMaskedPKReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelLiveChannelMaskedPKReq.Marshal(b, m, deterministic)
}
func (dst *CancelLiveChannelMaskedPKReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelLiveChannelMaskedPKReq.Merge(dst, src)
}
func (m *CancelLiveChannelMaskedPKReq) XXX_Size() int {
	return xxx_messageInfo_CancelLiveChannelMaskedPKReq.Size(m)
}
func (m *CancelLiveChannelMaskedPKReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelLiveChannelMaskedPKReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelLiveChannelMaskedPKReq proto.InternalMessageInfo

func (m *CancelLiveChannelMaskedPKReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CancelLiveChannelMaskedPKReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CancelLiveChannelMaskedPKResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelLiveChannelMaskedPKResp) Reset()         { *m = CancelLiveChannelMaskedPKResp{} }
func (m *CancelLiveChannelMaskedPKResp) String() string { return proto.CompactTextString(m) }
func (*CancelLiveChannelMaskedPKResp) ProtoMessage()    {}
func (*CancelLiveChannelMaskedPKResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{17}
}
func (m *CancelLiveChannelMaskedPKResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelLiveChannelMaskedPKResp.Unmarshal(m, b)
}
func (m *CancelLiveChannelMaskedPKResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelLiveChannelMaskedPKResp.Marshal(b, m, deterministic)
}
func (dst *CancelLiveChannelMaskedPKResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelLiveChannelMaskedPKResp.Merge(dst, src)
}
func (m *CancelLiveChannelMaskedPKResp) XXX_Size() int {
	return xxx_messageInfo_CancelLiveChannelMaskedPKResp.Size(m)
}
func (m *CancelLiveChannelMaskedPKResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelLiveChannelMaskedPKResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelLiveChannelMaskedPKResp proto.InternalMessageInfo

type GetLiveChannelMaskedPKStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveChannelMaskedPKStatusReq) Reset()         { *m = GetLiveChannelMaskedPKStatusReq{} }
func (m *GetLiveChannelMaskedPKStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveChannelMaskedPKStatusReq) ProtoMessage()    {}
func (*GetLiveChannelMaskedPKStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{18}
}
func (m *GetLiveChannelMaskedPKStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveChannelMaskedPKStatusReq.Unmarshal(m, b)
}
func (m *GetLiveChannelMaskedPKStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveChannelMaskedPKStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveChannelMaskedPKStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveChannelMaskedPKStatusReq.Merge(dst, src)
}
func (m *GetLiveChannelMaskedPKStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveChannelMaskedPKStatusReq.Size(m)
}
func (m *GetLiveChannelMaskedPKStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveChannelMaskedPKStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveChannelMaskedPKStatusReq proto.InternalMessageInfo

func (m *GetLiveChannelMaskedPKStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLiveChannelMaskedPKStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetLiveChannelMaskedPKStatusResp struct {
	Status               uint32   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveChannelMaskedPKStatusResp) Reset()         { *m = GetLiveChannelMaskedPKStatusResp{} }
func (m *GetLiveChannelMaskedPKStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveChannelMaskedPKStatusResp) ProtoMessage()    {}
func (*GetLiveChannelMaskedPKStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{19}
}
func (m *GetLiveChannelMaskedPKStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveChannelMaskedPKStatusResp.Unmarshal(m, b)
}
func (m *GetLiveChannelMaskedPKStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveChannelMaskedPKStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveChannelMaskedPKStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveChannelMaskedPKStatusResp.Merge(dst, src)
}
func (m *GetLiveChannelMaskedPKStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveChannelMaskedPKStatusResp.Size(m)
}
func (m *GetLiveChannelMaskedPKStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveChannelMaskedPKStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveChannelMaskedPKStatusResp proto.InternalMessageInfo

func (m *GetLiveChannelMaskedPKStatusResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type BatchGetLiveChannelMaskedPKStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	ChannelIdList        []uint32 `protobuf:"varint,3,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetLiveChannelMaskedPKStatusReq) Reset()         { *m = BatchGetLiveChannelMaskedPKStatusReq{} }
func (m *BatchGetLiveChannelMaskedPKStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetLiveChannelMaskedPKStatusReq) ProtoMessage()    {}
func (*BatchGetLiveChannelMaskedPKStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{20}
}
func (m *BatchGetLiveChannelMaskedPKStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetLiveChannelMaskedPKStatusReq.Unmarshal(m, b)
}
func (m *BatchGetLiveChannelMaskedPKStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetLiveChannelMaskedPKStatusReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetLiveChannelMaskedPKStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetLiveChannelMaskedPKStatusReq.Merge(dst, src)
}
func (m *BatchGetLiveChannelMaskedPKStatusReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetLiveChannelMaskedPKStatusReq.Size(m)
}
func (m *BatchGetLiveChannelMaskedPKStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetLiveChannelMaskedPKStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetLiveChannelMaskedPKStatusReq proto.InternalMessageInfo

func (m *BatchGetLiveChannelMaskedPKStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetLiveChannelMaskedPKStatusReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *BatchGetLiveChannelMaskedPKStatusReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatchGetLiveChannelMaskedPKStatusResp struct {
	ChannelStatusMap     map[uint32]uint32 `protobuf:"bytes,1,rep,name=channel_status_map,json=channelStatusMap,proto3" json:"channel_status_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetLiveChannelMaskedPKStatusResp) Reset()         { *m = BatchGetLiveChannelMaskedPKStatusResp{} }
func (m *BatchGetLiveChannelMaskedPKStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetLiveChannelMaskedPKStatusResp) ProtoMessage()    {}
func (*BatchGetLiveChannelMaskedPKStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{21}
}
func (m *BatchGetLiveChannelMaskedPKStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetLiveChannelMaskedPKStatusResp.Unmarshal(m, b)
}
func (m *BatchGetLiveChannelMaskedPKStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetLiveChannelMaskedPKStatusResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetLiveChannelMaskedPKStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetLiveChannelMaskedPKStatusResp.Merge(dst, src)
}
func (m *BatchGetLiveChannelMaskedPKStatusResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetLiveChannelMaskedPKStatusResp.Size(m)
}
func (m *BatchGetLiveChannelMaskedPKStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetLiveChannelMaskedPKStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetLiveChannelMaskedPKStatusResp proto.InternalMessageInfo

func (m *BatchGetLiveChannelMaskedPKStatusResp) GetChannelStatusMap() map[uint32]uint32 {
	if m != nil {
		return m.ChannelStatusMap
	}
	return nil
}

// 获取当前PK配置信息(仅是符合资格的人能获取到)
type GetLiveChannelMaskedPKCurrConfWithUserReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveChannelMaskedPKCurrConfWithUserReq) Reset() {
	*m = GetLiveChannelMaskedPKCurrConfWithUserReq{}
}
func (m *GetLiveChannelMaskedPKCurrConfWithUserReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveChannelMaskedPKCurrConfWithUserReq) ProtoMessage()    {}
func (*GetLiveChannelMaskedPKCurrConfWithUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{22}
}
func (m *GetLiveChannelMaskedPKCurrConfWithUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveChannelMaskedPKCurrConfWithUserReq.Unmarshal(m, b)
}
func (m *GetLiveChannelMaskedPKCurrConfWithUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveChannelMaskedPKCurrConfWithUserReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveChannelMaskedPKCurrConfWithUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveChannelMaskedPKCurrConfWithUserReq.Merge(dst, src)
}
func (m *GetLiveChannelMaskedPKCurrConfWithUserReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveChannelMaskedPKCurrConfWithUserReq.Size(m)
}
func (m *GetLiveChannelMaskedPKCurrConfWithUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveChannelMaskedPKCurrConfWithUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveChannelMaskedPKCurrConfWithUserReq proto.InternalMessageInfo

func (m *GetLiveChannelMaskedPKCurrConfWithUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLiveChannelMaskedPKCurrConfWithUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetLiveChannelMaskedPKCurrConfWithUserResp struct {
	Conf                 *ChannelMaskedPKConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	IsGiveUp             bool                 `protobuf:"varint,2,opt,name=is_give_up,json=isGiveUp,proto3" json:"is_give_up,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetLiveChannelMaskedPKCurrConfWithUserResp) Reset() {
	*m = GetLiveChannelMaskedPKCurrConfWithUserResp{}
}
func (m *GetLiveChannelMaskedPKCurrConfWithUserResp) String() string {
	return proto.CompactTextString(m)
}
func (*GetLiveChannelMaskedPKCurrConfWithUserResp) ProtoMessage() {}
func (*GetLiveChannelMaskedPKCurrConfWithUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{23}
}
func (m *GetLiveChannelMaskedPKCurrConfWithUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveChannelMaskedPKCurrConfWithUserResp.Unmarshal(m, b)
}
func (m *GetLiveChannelMaskedPKCurrConfWithUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveChannelMaskedPKCurrConfWithUserResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveChannelMaskedPKCurrConfWithUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveChannelMaskedPKCurrConfWithUserResp.Merge(dst, src)
}
func (m *GetLiveChannelMaskedPKCurrConfWithUserResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveChannelMaskedPKCurrConfWithUserResp.Size(m)
}
func (m *GetLiveChannelMaskedPKCurrConfWithUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveChannelMaskedPKCurrConfWithUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveChannelMaskedPKCurrConfWithUserResp proto.InternalMessageInfo

func (m *GetLiveChannelMaskedPKCurrConfWithUserResp) GetConf() *ChannelMaskedPKConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

func (m *GetLiveChannelMaskedPKCurrConfWithUserResp) GetIsGiveUp() bool {
	if m != nil {
		return m.IsGiveUp
	}
	return false
}

// 获取当前PK配置信息(任何人都能获取到)
type GetLiveChannelMaskedPKCurrConfReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveChannelMaskedPKCurrConfReq) Reset()         { *m = GetLiveChannelMaskedPKCurrConfReq{} }
func (m *GetLiveChannelMaskedPKCurrConfReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveChannelMaskedPKCurrConfReq) ProtoMessage()    {}
func (*GetLiveChannelMaskedPKCurrConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{24}
}
func (m *GetLiveChannelMaskedPKCurrConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveChannelMaskedPKCurrConfReq.Unmarshal(m, b)
}
func (m *GetLiveChannelMaskedPKCurrConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveChannelMaskedPKCurrConfReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveChannelMaskedPKCurrConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveChannelMaskedPKCurrConfReq.Merge(dst, src)
}
func (m *GetLiveChannelMaskedPKCurrConfReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveChannelMaskedPKCurrConfReq.Size(m)
}
func (m *GetLiveChannelMaskedPKCurrConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveChannelMaskedPKCurrConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveChannelMaskedPKCurrConfReq proto.InternalMessageInfo

type GetLiveChannelMaskedPKCurrConfResp struct {
	Conf                 *ChannelMaskedPKConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetLiveChannelMaskedPKCurrConfResp) Reset()         { *m = GetLiveChannelMaskedPKCurrConfResp{} }
func (m *GetLiveChannelMaskedPKCurrConfResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveChannelMaskedPKCurrConfResp) ProtoMessage()    {}
func (*GetLiveChannelMaskedPKCurrConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{25}
}
func (m *GetLiveChannelMaskedPKCurrConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveChannelMaskedPKCurrConfResp.Unmarshal(m, b)
}
func (m *GetLiveChannelMaskedPKCurrConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveChannelMaskedPKCurrConfResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveChannelMaskedPKCurrConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveChannelMaskedPKCurrConfResp.Merge(dst, src)
}
func (m *GetLiveChannelMaskedPKCurrConfResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveChannelMaskedPKCurrConfResp.Size(m)
}
func (m *GetLiveChannelMaskedPKCurrConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveChannelMaskedPKCurrConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveChannelMaskedPKCurrConfResp proto.InternalMessageInfo

func (m *GetLiveChannelMaskedPKCurrConfResp) GetConf() *ChannelMaskedPKConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type ChannelMaskedPKAnchorRankMem struct {
	Uid                  uint32                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Score                uint32                    `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	Bonus                uint32                    `protobuf:"varint,3,opt,name=bonus,proto3" json:"bonus,omitempty"`
	TopAudienceList      []*ChannelMaskedPKRankMem `protobuf:"bytes,7,rep,name=top_audience_list,json=topAudienceList,proto3" json:"top_audience_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *ChannelMaskedPKAnchorRankMem) Reset()         { *m = ChannelMaskedPKAnchorRankMem{} }
func (m *ChannelMaskedPKAnchorRankMem) String() string { return proto.CompactTextString(m) }
func (*ChannelMaskedPKAnchorRankMem) ProtoMessage()    {}
func (*ChannelMaskedPKAnchorRankMem) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{26}
}
func (m *ChannelMaskedPKAnchorRankMem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMaskedPKAnchorRankMem.Unmarshal(m, b)
}
func (m *ChannelMaskedPKAnchorRankMem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMaskedPKAnchorRankMem.Marshal(b, m, deterministic)
}
func (dst *ChannelMaskedPKAnchorRankMem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMaskedPKAnchorRankMem.Merge(dst, src)
}
func (m *ChannelMaskedPKAnchorRankMem) XXX_Size() int {
	return xxx_messageInfo_ChannelMaskedPKAnchorRankMem.Size(m)
}
func (m *ChannelMaskedPKAnchorRankMem) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMaskedPKAnchorRankMem.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMaskedPKAnchorRankMem proto.InternalMessageInfo

func (m *ChannelMaskedPKAnchorRankMem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMaskedPKAnchorRankMem) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ChannelMaskedPKAnchorRankMem) GetBonus() uint32 {
	if m != nil {
		return m.Bonus
	}
	return 0
}

func (m *ChannelMaskedPKAnchorRankMem) GetTopAudienceList() []*ChannelMaskedPKRankMem {
	if m != nil {
		return m.TopAudienceList
	}
	return nil
}

// 获取PK战力榜
type GetAudienceRankListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Begin                uint32   `protobuf:"varint,3,opt,name=begin,proto3" json:"begin,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAudienceRankListReq) Reset()         { *m = GetAudienceRankListReq{} }
func (m *GetAudienceRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetAudienceRankListReq) ProtoMessage()    {}
func (*GetAudienceRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{27}
}
func (m *GetAudienceRankListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAudienceRankListReq.Unmarshal(m, b)
}
func (m *GetAudienceRankListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAudienceRankListReq.Marshal(b, m, deterministic)
}
func (dst *GetAudienceRankListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAudienceRankListReq.Merge(dst, src)
}
func (m *GetAudienceRankListReq) XXX_Size() int {
	return xxx_messageInfo_GetAudienceRankListReq.Size(m)
}
func (m *GetAudienceRankListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAudienceRankListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAudienceRankListReq proto.InternalMessageInfo

func (m *GetAudienceRankListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAudienceRankListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetAudienceRankListReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetAudienceRankListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetAudienceRankListResp struct {
	RankList             []*ChannelMaskedPKRankMem `protobuf:"bytes,2,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetAudienceRankListResp) Reset()         { *m = GetAudienceRankListResp{} }
func (m *GetAudienceRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetAudienceRankListResp) ProtoMessage()    {}
func (*GetAudienceRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{28}
}
func (m *GetAudienceRankListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAudienceRankListResp.Unmarshal(m, b)
}
func (m *GetAudienceRankListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAudienceRankListResp.Marshal(b, m, deterministic)
}
func (dst *GetAudienceRankListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAudienceRankListResp.Merge(dst, src)
}
func (m *GetAudienceRankListResp) XXX_Size() int {
	return xxx_messageInfo_GetAudienceRankListResp.Size(m)
}
func (m *GetAudienceRankListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAudienceRankListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAudienceRankListResp proto.InternalMessageInfo

func (m *GetAudienceRankListResp) GetRankList() []*ChannelMaskedPKRankMem {
	if m != nil {
		return m.RankList
	}
	return nil
}

type BatchAddUserToQualificationReq struct {
	Qualifications       []*Qualification `protobuf:"bytes,1,rep,name=qualifications,proto3" json:"qualifications,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchAddUserToQualificationReq) Reset()         { *m = BatchAddUserToQualificationReq{} }
func (m *BatchAddUserToQualificationReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddUserToQualificationReq) ProtoMessage()    {}
func (*BatchAddUserToQualificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{29}
}
func (m *BatchAddUserToQualificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddUserToQualificationReq.Unmarshal(m, b)
}
func (m *BatchAddUserToQualificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddUserToQualificationReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddUserToQualificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddUserToQualificationReq.Merge(dst, src)
}
func (m *BatchAddUserToQualificationReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddUserToQualificationReq.Size(m)
}
func (m *BatchAddUserToQualificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddUserToQualificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddUserToQualificationReq proto.InternalMessageInfo

func (m *BatchAddUserToQualificationReq) GetQualifications() []*Qualification {
	if m != nil {
		return m.Qualifications
	}
	return nil
}

type Qualification struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	UserNickname         string   `protobuf:"bytes,3,opt,name=user_nickname,json=userNickname,proto3" json:"user_nickname,omitempty"`
	UserAge              uint32   `protobuf:"varint,4,opt,name=user_age,json=userAge,proto3" json:"user_age,omitempty"`
	UserSex              string   `protobuf:"bytes,5,opt,name=user_sex,json=userSex,proto3" json:"user_sex,omitempty"`
	GuildName            string   `protobuf:"bytes,6,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Ttid                 string   `protobuf:"bytes,8,opt,name=ttid,proto3" json:"ttid,omitempty"`
	DisplayId            uint32   `protobuf:"varint,9,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	GuildId              uint32   `protobuf:"varint,10,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildShortId         uint32   `protobuf:"varint,11,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Qualification) Reset()         { *m = Qualification{} }
func (m *Qualification) String() string { return proto.CompactTextString(m) }
func (*Qualification) ProtoMessage()    {}
func (*Qualification) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{30}
}
func (m *Qualification) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Qualification.Unmarshal(m, b)
}
func (m *Qualification) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Qualification.Marshal(b, m, deterministic)
}
func (dst *Qualification) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Qualification.Merge(dst, src)
}
func (m *Qualification) XXX_Size() int {
	return xxx_messageInfo_Qualification.Size(m)
}
func (m *Qualification) XXX_DiscardUnknown() {
	xxx_messageInfo_Qualification.DiscardUnknown(m)
}

var xxx_messageInfo_Qualification proto.InternalMessageInfo

func (m *Qualification) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *Qualification) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Qualification) GetUserNickname() string {
	if m != nil {
		return m.UserNickname
	}
	return ""
}

func (m *Qualification) GetUserAge() uint32 {
	if m != nil {
		return m.UserAge
	}
	return 0
}

func (m *Qualification) GetUserSex() string {
	if m != nil {
		return m.UserSex
	}
	return ""
}

func (m *Qualification) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *Qualification) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *Qualification) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *Qualification) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *Qualification) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *Qualification) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

type BatchAddUserToQualificationResp struct {
	FailTtid             []string `protobuf:"bytes,1,rep,name=fail_ttid,json=failTtid,proto3" json:"fail_ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddUserToQualificationResp) Reset()         { *m = BatchAddUserToQualificationResp{} }
func (m *BatchAddUserToQualificationResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddUserToQualificationResp) ProtoMessage()    {}
func (*BatchAddUserToQualificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{31}
}
func (m *BatchAddUserToQualificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddUserToQualificationResp.Unmarshal(m, b)
}
func (m *BatchAddUserToQualificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddUserToQualificationResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddUserToQualificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddUserToQualificationResp.Merge(dst, src)
}
func (m *BatchAddUserToQualificationResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddUserToQualificationResp.Size(m)
}
func (m *BatchAddUserToQualificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddUserToQualificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddUserToQualificationResp proto.InternalMessageInfo

func (m *BatchAddUserToQualificationResp) GetFailTtid() []string {
	if m != nil {
		return m.FailTtid
	}
	return nil
}

type IsUserHasQualificationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsUserHasQualificationReq) Reset()         { *m = IsUserHasQualificationReq{} }
func (m *IsUserHasQualificationReq) String() string { return proto.CompactTextString(m) }
func (*IsUserHasQualificationReq) ProtoMessage()    {}
func (*IsUserHasQualificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{32}
}
func (m *IsUserHasQualificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsUserHasQualificationReq.Unmarshal(m, b)
}
func (m *IsUserHasQualificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsUserHasQualificationReq.Marshal(b, m, deterministic)
}
func (dst *IsUserHasQualificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsUserHasQualificationReq.Merge(dst, src)
}
func (m *IsUserHasQualificationReq) XXX_Size() int {
	return xxx_messageInfo_IsUserHasQualificationReq.Size(m)
}
func (m *IsUserHasQualificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsUserHasQualificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsUserHasQualificationReq proto.InternalMessageInfo

func (m *IsUserHasQualificationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IsUserHasQualificationResp struct {
	Exist                bool     `protobuf:"varint,1,opt,name=exist,proto3" json:"exist,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsUserHasQualificationResp) Reset()         { *m = IsUserHasQualificationResp{} }
func (m *IsUserHasQualificationResp) String() string { return proto.CompactTextString(m) }
func (*IsUserHasQualificationResp) ProtoMessage()    {}
func (*IsUserHasQualificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{33}
}
func (m *IsUserHasQualificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsUserHasQualificationResp.Unmarshal(m, b)
}
func (m *IsUserHasQualificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsUserHasQualificationResp.Marshal(b, m, deterministic)
}
func (dst *IsUserHasQualificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsUserHasQualificationResp.Merge(dst, src)
}
func (m *IsUserHasQualificationResp) XXX_Size() int {
	return xxx_messageInfo_IsUserHasQualificationResp.Size(m)
}
func (m *IsUserHasQualificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsUserHasQualificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsUserHasQualificationResp proto.InternalMessageInfo

func (m *IsUserHasQualificationResp) GetExist() bool {
	if m != nil {
		return m.Exist
	}
	return false
}

type AddMaskedGameConfigReq struct {
	Config               *MaskedGameConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddMaskedGameConfigReq) Reset()         { *m = AddMaskedGameConfigReq{} }
func (m *AddMaskedGameConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddMaskedGameConfigReq) ProtoMessage()    {}
func (*AddMaskedGameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{34}
}
func (m *AddMaskedGameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMaskedGameConfigReq.Unmarshal(m, b)
}
func (m *AddMaskedGameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMaskedGameConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddMaskedGameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMaskedGameConfigReq.Merge(dst, src)
}
func (m *AddMaskedGameConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddMaskedGameConfigReq.Size(m)
}
func (m *AddMaskedGameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMaskedGameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddMaskedGameConfigReq proto.InternalMessageInfo

func (m *AddMaskedGameConfigReq) GetConfig() *MaskedGameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type AddMaskedGameConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddMaskedGameConfigResp) Reset()         { *m = AddMaskedGameConfigResp{} }
func (m *AddMaskedGameConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddMaskedGameConfigResp) ProtoMessage()    {}
func (*AddMaskedGameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{35}
}
func (m *AddMaskedGameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMaskedGameConfigResp.Unmarshal(m, b)
}
func (m *AddMaskedGameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMaskedGameConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddMaskedGameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMaskedGameConfigResp.Merge(dst, src)
}
func (m *AddMaskedGameConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddMaskedGameConfigResp.Size(m)
}
func (m *AddMaskedGameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMaskedGameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddMaskedGameConfigResp proto.InternalMessageInfo

type MaskedGameConfig struct {
	BeginTime            uint32     `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32     `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ChipAvg              uint32     `protobuf:"varint,3,opt,name=chip_avg,json=chipAvg,proto3" json:"chip_avg,omitempty"`
	ChipCount            uint32     `protobuf:"varint,4,opt,name=chip_count,json=chipCount,proto3" json:"chip_count,omitempty"`
	WithDraw             uint32     `protobuf:"varint,5,opt,name=with_draw,json=withDraw,proto3" json:"with_draw,omitempty"`
	ActivityUrl          string     `protobuf:"bytes,6,opt,name=activity_url,json=activityUrl,proto3" json:"activity_url,omitempty"`
	DivideType           DivideType `protobuf:"varint,7,opt,name=divide_type,json=divideType,proto3,enum=masked_pk_live.DivideType" json:"divide_type,omitempty"`
	DivideCount          uint32     `protobuf:"varint,8,opt,name=divide_count,json=divideCount,proto3" json:"divide_count,omitempty"`
	ReviveChip           uint32     `protobuf:"varint,9,opt,name=revive_chip,json=reviveChip,proto3" json:"revive_chip,omitempty"`
	ProvideChip          uint32     `protobuf:"varint,10,opt,name=provide_chip,json=provideChip,proto3" json:"provide_chip,omitempty"`
	ContinueMatch        uint32     `protobuf:"varint,11,opt,name=continue_match,json=continueMatch,proto3" json:"continue_match,omitempty"`
	GameId               uint32     `protobuf:"varint,12,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	DieOutCount          uint32     `protobuf:"varint,13,opt,name=die_out_count,json=dieOutCount,proto3" json:"die_out_count,omitempty"`
	NeedQualification    uint32     `protobuf:"varint,14,opt,name=need_qualification,json=needQualification,proto3" json:"need_qualification,omitempty"`
	ReviveTbean          uint32     `protobuf:"varint,15,opt,name=revive_tbean,json=reviveTbean,proto3" json:"revive_tbean,omitempty"`
	CanBeModify          bool       `protobuf:"varint,16,opt,name=can_be_modify,json=canBeModify,proto3" json:"can_be_modify,omitempty"`
	UseBackpack          uint32     `protobuf:"varint,17,opt,name=use_backpack,json=useBackpack,proto3" json:"use_backpack,omitempty"`
	QuickKill            uint32     `protobuf:"varint,18,opt,name=quick_kill,json=quickKill,proto3" json:"quick_kill,omitempty"`
	ContinueMatchCount   uint32     `protobuf:"varint,19,opt,name=continue_match_count,json=continueMatchCount,proto3" json:"continue_match_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *MaskedGameConfig) Reset()         { *m = MaskedGameConfig{} }
func (m *MaskedGameConfig) String() string { return proto.CompactTextString(m) }
func (*MaskedGameConfig) ProtoMessage()    {}
func (*MaskedGameConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{36}
}
func (m *MaskedGameConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaskedGameConfig.Unmarshal(m, b)
}
func (m *MaskedGameConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaskedGameConfig.Marshal(b, m, deterministic)
}
func (dst *MaskedGameConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaskedGameConfig.Merge(dst, src)
}
func (m *MaskedGameConfig) XXX_Size() int {
	return xxx_messageInfo_MaskedGameConfig.Size(m)
}
func (m *MaskedGameConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_MaskedGameConfig.DiscardUnknown(m)
}

var xxx_messageInfo_MaskedGameConfig proto.InternalMessageInfo

func (m *MaskedGameConfig) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *MaskedGameConfig) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *MaskedGameConfig) GetChipAvg() uint32 {
	if m != nil {
		return m.ChipAvg
	}
	return 0
}

func (m *MaskedGameConfig) GetChipCount() uint32 {
	if m != nil {
		return m.ChipCount
	}
	return 0
}

func (m *MaskedGameConfig) GetWithDraw() uint32 {
	if m != nil {
		return m.WithDraw
	}
	return 0
}

func (m *MaskedGameConfig) GetActivityUrl() string {
	if m != nil {
		return m.ActivityUrl
	}
	return ""
}

func (m *MaskedGameConfig) GetDivideType() DivideType {
	if m != nil {
		return m.DivideType
	}
	return DivideType_DIVIDE_TYPE_DEFAULT
}

func (m *MaskedGameConfig) GetDivideCount() uint32 {
	if m != nil {
		return m.DivideCount
	}
	return 0
}

func (m *MaskedGameConfig) GetReviveChip() uint32 {
	if m != nil {
		return m.ReviveChip
	}
	return 0
}

func (m *MaskedGameConfig) GetProvideChip() uint32 {
	if m != nil {
		return m.ProvideChip
	}
	return 0
}

func (m *MaskedGameConfig) GetContinueMatch() uint32 {
	if m != nil {
		return m.ContinueMatch
	}
	return 0
}

func (m *MaskedGameConfig) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *MaskedGameConfig) GetDieOutCount() uint32 {
	if m != nil {
		return m.DieOutCount
	}
	return 0
}

func (m *MaskedGameConfig) GetNeedQualification() uint32 {
	if m != nil {
		return m.NeedQualification
	}
	return 0
}

func (m *MaskedGameConfig) GetReviveTbean() uint32 {
	if m != nil {
		return m.ReviveTbean
	}
	return 0
}

func (m *MaskedGameConfig) GetCanBeModify() bool {
	if m != nil {
		return m.CanBeModify
	}
	return false
}

func (m *MaskedGameConfig) GetUseBackpack() uint32 {
	if m != nil {
		return m.UseBackpack
	}
	return 0
}

func (m *MaskedGameConfig) GetQuickKill() uint32 {
	if m != nil {
		return m.QuickKill
	}
	return 0
}

func (m *MaskedGameConfig) GetContinueMatchCount() uint32 {
	if m != nil {
		return m.ContinueMatchCount
	}
	return 0
}

type DelMaskedGameConfigReq struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelMaskedGameConfigReq) Reset()         { *m = DelMaskedGameConfigReq{} }
func (m *DelMaskedGameConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelMaskedGameConfigReq) ProtoMessage()    {}
func (*DelMaskedGameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{37}
}
func (m *DelMaskedGameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelMaskedGameConfigReq.Unmarshal(m, b)
}
func (m *DelMaskedGameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelMaskedGameConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelMaskedGameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelMaskedGameConfigReq.Merge(dst, src)
}
func (m *DelMaskedGameConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelMaskedGameConfigReq.Size(m)
}
func (m *DelMaskedGameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelMaskedGameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelMaskedGameConfigReq proto.InternalMessageInfo

func (m *DelMaskedGameConfigReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type DelMaskedGameConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelMaskedGameConfigResp) Reset()         { *m = DelMaskedGameConfigResp{} }
func (m *DelMaskedGameConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelMaskedGameConfigResp) ProtoMessage()    {}
func (*DelMaskedGameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{38}
}
func (m *DelMaskedGameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelMaskedGameConfigResp.Unmarshal(m, b)
}
func (m *DelMaskedGameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelMaskedGameConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelMaskedGameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelMaskedGameConfigResp.Merge(dst, src)
}
func (m *DelMaskedGameConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelMaskedGameConfigResp.Size(m)
}
func (m *DelMaskedGameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelMaskedGameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelMaskedGameConfigResp proto.InternalMessageInfo

type BatchAddUserToWhiteListReq struct {
	Uid                  []uint32 `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddUserToWhiteListReq) Reset()         { *m = BatchAddUserToWhiteListReq{} }
func (m *BatchAddUserToWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddUserToWhiteListReq) ProtoMessage()    {}
func (*BatchAddUserToWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{39}
}
func (m *BatchAddUserToWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddUserToWhiteListReq.Unmarshal(m, b)
}
func (m *BatchAddUserToWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddUserToWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddUserToWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddUserToWhiteListReq.Merge(dst, src)
}
func (m *BatchAddUserToWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddUserToWhiteListReq.Size(m)
}
func (m *BatchAddUserToWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddUserToWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddUserToWhiteListReq proto.InternalMessageInfo

func (m *BatchAddUserToWhiteListReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

type BatchAddUserToWhiteListResp struct {
	FailTtid             []string `protobuf:"bytes,1,rep,name=fail_ttid,json=failTtid,proto3" json:"fail_ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddUserToWhiteListResp) Reset()         { *m = BatchAddUserToWhiteListResp{} }
func (m *BatchAddUserToWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddUserToWhiteListResp) ProtoMessage()    {}
func (*BatchAddUserToWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{40}
}
func (m *BatchAddUserToWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddUserToWhiteListResp.Unmarshal(m, b)
}
func (m *BatchAddUserToWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddUserToWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddUserToWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddUserToWhiteListResp.Merge(dst, src)
}
func (m *BatchAddUserToWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddUserToWhiteListResp.Size(m)
}
func (m *BatchAddUserToWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddUserToWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddUserToWhiteListResp proto.InternalMessageInfo

func (m *BatchAddUserToWhiteListResp) GetFailTtid() []string {
	if m != nil {
		return m.FailTtid
	}
	return nil
}

type BatchDelUserFromWhiteListReq struct {
	Uid                  []uint32 `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	DeleteAll            uint32   `protobuf:"varint,2,opt,name=delete_all,json=deleteAll,proto3" json:"delete_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelUserFromWhiteListReq) Reset()         { *m = BatchDelUserFromWhiteListReq{} }
func (m *BatchDelUserFromWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelUserFromWhiteListReq) ProtoMessage()    {}
func (*BatchDelUserFromWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{41}
}
func (m *BatchDelUserFromWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelUserFromWhiteListReq.Unmarshal(m, b)
}
func (m *BatchDelUserFromWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelUserFromWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelUserFromWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelUserFromWhiteListReq.Merge(dst, src)
}
func (m *BatchDelUserFromWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelUserFromWhiteListReq.Size(m)
}
func (m *BatchDelUserFromWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelUserFromWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelUserFromWhiteListReq proto.InternalMessageInfo

func (m *BatchDelUserFromWhiteListReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *BatchDelUserFromWhiteListReq) GetDeleteAll() uint32 {
	if m != nil {
		return m.DeleteAll
	}
	return 0
}

type BatchDelUserFromWhiteListResp struct {
	FailTtid             []string `protobuf:"bytes,1,rep,name=fail_ttid,json=failTtid,proto3" json:"fail_ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelUserFromWhiteListResp) Reset()         { *m = BatchDelUserFromWhiteListResp{} }
func (m *BatchDelUserFromWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelUserFromWhiteListResp) ProtoMessage()    {}
func (*BatchDelUserFromWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{42}
}
func (m *BatchDelUserFromWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelUserFromWhiteListResp.Unmarshal(m, b)
}
func (m *BatchDelUserFromWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelUserFromWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelUserFromWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelUserFromWhiteListResp.Merge(dst, src)
}
func (m *BatchDelUserFromWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelUserFromWhiteListResp.Size(m)
}
func (m *BatchDelUserFromWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelUserFromWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelUserFromWhiteListResp proto.InternalMessageInfo

func (m *BatchDelUserFromWhiteListResp) GetFailTtid() []string {
	if m != nil {
		return m.FailTtid
	}
	return nil
}

type GetAllWhiteListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllWhiteListReq) Reset()         { *m = GetAllWhiteListReq{} }
func (m *GetAllWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*GetAllWhiteListReq) ProtoMessage()    {}
func (*GetAllWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{43}
}
func (m *GetAllWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllWhiteListReq.Unmarshal(m, b)
}
func (m *GetAllWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *GetAllWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllWhiteListReq.Merge(dst, src)
}
func (m *GetAllWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_GetAllWhiteListReq.Size(m)
}
func (m *GetAllWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllWhiteListReq proto.InternalMessageInfo

type GetAllWhiteListResp struct {
	List                 []*Qualification `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetAllWhiteListResp) Reset()         { *m = GetAllWhiteListResp{} }
func (m *GetAllWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*GetAllWhiteListResp) ProtoMessage()    {}
func (*GetAllWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{44}
}
func (m *GetAllWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllWhiteListResp.Unmarshal(m, b)
}
func (m *GetAllWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *GetAllWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllWhiteListResp.Merge(dst, src)
}
func (m *GetAllWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_GetAllWhiteListResp.Size(m)
}
func (m *GetAllWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllWhiteListResp proto.InternalMessageInfo

func (m *GetAllWhiteListResp) GetList() []*Qualification {
	if m != nil {
		return m.List
	}
	return nil
}

type BatchDelUserFromQualificationReq struct {
	Uid                  []uint32 `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	DeleteAll            uint32   `protobuf:"varint,2,opt,name=delete_all,json=deleteAll,proto3" json:"delete_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelUserFromQualificationReq) Reset()         { *m = BatchDelUserFromQualificationReq{} }
func (m *BatchDelUserFromQualificationReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelUserFromQualificationReq) ProtoMessage()    {}
func (*BatchDelUserFromQualificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{45}
}
func (m *BatchDelUserFromQualificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelUserFromQualificationReq.Unmarshal(m, b)
}
func (m *BatchDelUserFromQualificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelUserFromQualificationReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelUserFromQualificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelUserFromQualificationReq.Merge(dst, src)
}
func (m *BatchDelUserFromQualificationReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelUserFromQualificationReq.Size(m)
}
func (m *BatchDelUserFromQualificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelUserFromQualificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelUserFromQualificationReq proto.InternalMessageInfo

func (m *BatchDelUserFromQualificationReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *BatchDelUserFromQualificationReq) GetDeleteAll() uint32 {
	if m != nil {
		return m.DeleteAll
	}
	return 0
}

type BatchDelUserFromQualificationResp struct {
	FailTtid             []string `protobuf:"bytes,1,rep,name=fail_ttid,json=failTtid,proto3" json:"fail_ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelUserFromQualificationResp) Reset()         { *m = BatchDelUserFromQualificationResp{} }
func (m *BatchDelUserFromQualificationResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelUserFromQualificationResp) ProtoMessage()    {}
func (*BatchDelUserFromQualificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{46}
}
func (m *BatchDelUserFromQualificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelUserFromQualificationResp.Unmarshal(m, b)
}
func (m *BatchDelUserFromQualificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelUserFromQualificationResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelUserFromQualificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelUserFromQualificationResp.Merge(dst, src)
}
func (m *BatchDelUserFromQualificationResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelUserFromQualificationResp.Size(m)
}
func (m *BatchDelUserFromQualificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelUserFromQualificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelUserFromQualificationResp proto.InternalMessageInfo

func (m *BatchDelUserFromQualificationResp) GetFailTtid() []string {
	if m != nil {
		return m.FailTtid
	}
	return nil
}

type GetUserFromQualificationReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserFromQualificationReq) Reset()         { *m = GetUserFromQualificationReq{} }
func (m *GetUserFromQualificationReq) String() string { return proto.CompactTextString(m) }
func (*GetUserFromQualificationReq) ProtoMessage()    {}
func (*GetUserFromQualificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{47}
}
func (m *GetUserFromQualificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFromQualificationReq.Unmarshal(m, b)
}
func (m *GetUserFromQualificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFromQualificationReq.Marshal(b, m, deterministic)
}
func (dst *GetUserFromQualificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFromQualificationReq.Merge(dst, src)
}
func (m *GetUserFromQualificationReq) XXX_Size() int {
	return xxx_messageInfo_GetUserFromQualificationReq.Size(m)
}
func (m *GetUserFromQualificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFromQualificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFromQualificationReq proto.InternalMessageInfo

func (m *GetUserFromQualificationReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetUserFromQualificationReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetUserFromQualificationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserFromQualificationResp struct {
	Qualifications       []*Qualification `protobuf:"bytes,1,rep,name=qualifications,proto3" json:"qualifications,omitempty"`
	SumCount             uint32           `protobuf:"varint,2,opt,name=sum_count,json=sumCount,proto3" json:"sum_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserFromQualificationResp) Reset()         { *m = GetUserFromQualificationResp{} }
func (m *GetUserFromQualificationResp) String() string { return proto.CompactTextString(m) }
func (*GetUserFromQualificationResp) ProtoMessage()    {}
func (*GetUserFromQualificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{48}
}
func (m *GetUserFromQualificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFromQualificationResp.Unmarshal(m, b)
}
func (m *GetUserFromQualificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFromQualificationResp.Marshal(b, m, deterministic)
}
func (dst *GetUserFromQualificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFromQualificationResp.Merge(dst, src)
}
func (m *GetUserFromQualificationResp) XXX_Size() int {
	return xxx_messageInfo_GetUserFromQualificationResp.Size(m)
}
func (m *GetUserFromQualificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFromQualificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFromQualificationResp proto.InternalMessageInfo

func (m *GetUserFromQualificationResp) GetQualifications() []*Qualification {
	if m != nil {
		return m.Qualifications
	}
	return nil
}

func (m *GetUserFromQualificationResp) GetSumCount() uint32 {
	if m != nil {
		return m.SumCount
	}
	return 0
}

type UpdateWhiteListConfigReq struct {
	UseWhiteList            uint32   `protobuf:"varint,1,opt,name=use_white_list,json=useWhiteList,proto3" json:"use_white_list,omitempty"`
	AllowMatchWithSameGuild uint32   `protobuf:"varint,2,opt,name=allow_match_with_same_guild,json=allowMatchWithSameGuild,proto3" json:"allow_match_with_same_guild,omitempty"`
	UsePrior                uint32   `protobuf:"varint,3,opt,name=use_prior,json=usePrior,proto3" json:"use_prior,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *UpdateWhiteListConfigReq) Reset()         { *m = UpdateWhiteListConfigReq{} }
func (m *UpdateWhiteListConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateWhiteListConfigReq) ProtoMessage()    {}
func (*UpdateWhiteListConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{49}
}
func (m *UpdateWhiteListConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateWhiteListConfigReq.Unmarshal(m, b)
}
func (m *UpdateWhiteListConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateWhiteListConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateWhiteListConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateWhiteListConfigReq.Merge(dst, src)
}
func (m *UpdateWhiteListConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateWhiteListConfigReq.Size(m)
}
func (m *UpdateWhiteListConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateWhiteListConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateWhiteListConfigReq proto.InternalMessageInfo

func (m *UpdateWhiteListConfigReq) GetUseWhiteList() uint32 {
	if m != nil {
		return m.UseWhiteList
	}
	return 0
}

func (m *UpdateWhiteListConfigReq) GetAllowMatchWithSameGuild() uint32 {
	if m != nil {
		return m.AllowMatchWithSameGuild
	}
	return 0
}

func (m *UpdateWhiteListConfigReq) GetUsePrior() uint32 {
	if m != nil {
		return m.UsePrior
	}
	return 0
}

type UpdateWhiteListConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateWhiteListConfigResp) Reset()         { *m = UpdateWhiteListConfigResp{} }
func (m *UpdateWhiteListConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateWhiteListConfigResp) ProtoMessage()    {}
func (*UpdateWhiteListConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{50}
}
func (m *UpdateWhiteListConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateWhiteListConfigResp.Unmarshal(m, b)
}
func (m *UpdateWhiteListConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateWhiteListConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateWhiteListConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateWhiteListConfigResp.Merge(dst, src)
}
func (m *UpdateWhiteListConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateWhiteListConfigResp.Size(m)
}
func (m *UpdateWhiteListConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateWhiteListConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateWhiteListConfigResp proto.InternalMessageInfo

type GetWhiteListConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWhiteListConfigReq) Reset()         { *m = GetWhiteListConfigReq{} }
func (m *GetWhiteListConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetWhiteListConfigReq) ProtoMessage()    {}
func (*GetWhiteListConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{51}
}
func (m *GetWhiteListConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteListConfigReq.Unmarshal(m, b)
}
func (m *GetWhiteListConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteListConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetWhiteListConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteListConfigReq.Merge(dst, src)
}
func (m *GetWhiteListConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetWhiteListConfigReq.Size(m)
}
func (m *GetWhiteListConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteListConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteListConfigReq proto.InternalMessageInfo

type GetWhiteListConfigResp struct {
	UseWhiteList            uint32   `protobuf:"varint,1,opt,name=use_white_list,json=useWhiteList,proto3" json:"use_white_list,omitempty"`
	AllowMatchWithSameGuild uint32   `protobuf:"varint,2,opt,name=allow_match_with_same_guild,json=allowMatchWithSameGuild,proto3" json:"allow_match_with_same_guild,omitempty"`
	UsePrior                uint32   `protobuf:"varint,3,opt,name=use_prior,json=usePrior,proto3" json:"use_prior,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *GetWhiteListConfigResp) Reset()         { *m = GetWhiteListConfigResp{} }
func (m *GetWhiteListConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetWhiteListConfigResp) ProtoMessage()    {}
func (*GetWhiteListConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{52}
}
func (m *GetWhiteListConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteListConfigResp.Unmarshal(m, b)
}
func (m *GetWhiteListConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteListConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetWhiteListConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteListConfigResp.Merge(dst, src)
}
func (m *GetWhiteListConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetWhiteListConfigResp.Size(m)
}
func (m *GetWhiteListConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteListConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteListConfigResp proto.InternalMessageInfo

func (m *GetWhiteListConfigResp) GetUseWhiteList() uint32 {
	if m != nil {
		return m.UseWhiteList
	}
	return 0
}

func (m *GetWhiteListConfigResp) GetAllowMatchWithSameGuild() uint32 {
	if m != nil {
		return m.AllowMatchWithSameGuild
	}
	return 0
}

func (m *GetWhiteListConfigResp) GetUsePrior() uint32 {
	if m != nil {
		return m.UsePrior
	}
	return 0
}

type SetMonthGiftValueReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Value                uint32   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMonthGiftValueReq) Reset()         { *m = SetMonthGiftValueReq{} }
func (m *SetMonthGiftValueReq) String() string { return proto.CompactTextString(m) }
func (*SetMonthGiftValueReq) ProtoMessage()    {}
func (*SetMonthGiftValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{53}
}
func (m *SetMonthGiftValueReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMonthGiftValueReq.Unmarshal(m, b)
}
func (m *SetMonthGiftValueReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMonthGiftValueReq.Marshal(b, m, deterministic)
}
func (dst *SetMonthGiftValueReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMonthGiftValueReq.Merge(dst, src)
}
func (m *SetMonthGiftValueReq) XXX_Size() int {
	return xxx_messageInfo_SetMonthGiftValueReq.Size(m)
}
func (m *SetMonthGiftValueReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMonthGiftValueReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMonthGiftValueReq proto.InternalMessageInfo

func (m *SetMonthGiftValueReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetMonthGiftValueReq) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type SetMonthGiftValueResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMonthGiftValueResp) Reset()         { *m = SetMonthGiftValueResp{} }
func (m *SetMonthGiftValueResp) String() string { return proto.CompactTextString(m) }
func (*SetMonthGiftValueResp) ProtoMessage()    {}
func (*SetMonthGiftValueResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{54}
}
func (m *SetMonthGiftValueResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMonthGiftValueResp.Unmarshal(m, b)
}
func (m *SetMonthGiftValueResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMonthGiftValueResp.Marshal(b, m, deterministic)
}
func (dst *SetMonthGiftValueResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMonthGiftValueResp.Merge(dst, src)
}
func (m *SetMonthGiftValueResp) XXX_Size() int {
	return xxx_messageInfo_SetMonthGiftValueResp.Size(m)
}
func (m *SetMonthGiftValueResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMonthGiftValueResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMonthGiftValueResp proto.InternalMessageInfo

type UpdateMaskedGameConfigReq struct {
	Config               *MaskedGameConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpdateMaskedGameConfigReq) Reset()         { *m = UpdateMaskedGameConfigReq{} }
func (m *UpdateMaskedGameConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateMaskedGameConfigReq) ProtoMessage()    {}
func (*UpdateMaskedGameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{55}
}
func (m *UpdateMaskedGameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMaskedGameConfigReq.Unmarshal(m, b)
}
func (m *UpdateMaskedGameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMaskedGameConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateMaskedGameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMaskedGameConfigReq.Merge(dst, src)
}
func (m *UpdateMaskedGameConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateMaskedGameConfigReq.Size(m)
}
func (m *UpdateMaskedGameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMaskedGameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMaskedGameConfigReq proto.InternalMessageInfo

func (m *UpdateMaskedGameConfigReq) GetConfig() *MaskedGameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpdateMaskedGameConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateMaskedGameConfigResp) Reset()         { *m = UpdateMaskedGameConfigResp{} }
func (m *UpdateMaskedGameConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateMaskedGameConfigResp) ProtoMessage()    {}
func (*UpdateMaskedGameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{56}
}
func (m *UpdateMaskedGameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMaskedGameConfigResp.Unmarshal(m, b)
}
func (m *UpdateMaskedGameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMaskedGameConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateMaskedGameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMaskedGameConfigResp.Merge(dst, src)
}
func (m *UpdateMaskedGameConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateMaskedGameConfigResp.Size(m)
}
func (m *UpdateMaskedGameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMaskedGameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMaskedGameConfigResp proto.InternalMessageInfo

type GetAllMaskedGameConfigReq struct {
	GetExpire            bool     `protobuf:"varint,1,opt,name=get_expire,json=getExpire,proto3" json:"get_expire,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllMaskedGameConfigReq) Reset()         { *m = GetAllMaskedGameConfigReq{} }
func (m *GetAllMaskedGameConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetAllMaskedGameConfigReq) ProtoMessage()    {}
func (*GetAllMaskedGameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{57}
}
func (m *GetAllMaskedGameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllMaskedGameConfigReq.Unmarshal(m, b)
}
func (m *GetAllMaskedGameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllMaskedGameConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetAllMaskedGameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllMaskedGameConfigReq.Merge(dst, src)
}
func (m *GetAllMaskedGameConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetAllMaskedGameConfigReq.Size(m)
}
func (m *GetAllMaskedGameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllMaskedGameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllMaskedGameConfigReq proto.InternalMessageInfo

func (m *GetAllMaskedGameConfigReq) GetGetExpire() bool {
	if m != nil {
		return m.GetExpire
	}
	return false
}

type GetAllMaskedGameConfigResp struct {
	Config               []*MaskedGameConfig `protobuf:"bytes,1,rep,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAllMaskedGameConfigResp) Reset()         { *m = GetAllMaskedGameConfigResp{} }
func (m *GetAllMaskedGameConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetAllMaskedGameConfigResp) ProtoMessage()    {}
func (*GetAllMaskedGameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{58}
}
func (m *GetAllMaskedGameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllMaskedGameConfigResp.Unmarshal(m, b)
}
func (m *GetAllMaskedGameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllMaskedGameConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetAllMaskedGameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllMaskedGameConfigResp.Merge(dst, src)
}
func (m *GetAllMaskedGameConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetAllMaskedGameConfigResp.Size(m)
}
func (m *GetAllMaskedGameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllMaskedGameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllMaskedGameConfigResp proto.InternalMessageInfo

func (m *GetAllMaskedGameConfigResp) GetConfig() []*MaskedGameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type GetLastMaskedGameConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastMaskedGameConfigReq) Reset()         { *m = GetLastMaskedGameConfigReq{} }
func (m *GetLastMaskedGameConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetLastMaskedGameConfigReq) ProtoMessage()    {}
func (*GetLastMaskedGameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{59}
}
func (m *GetLastMaskedGameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastMaskedGameConfigReq.Unmarshal(m, b)
}
func (m *GetLastMaskedGameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastMaskedGameConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetLastMaskedGameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastMaskedGameConfigReq.Merge(dst, src)
}
func (m *GetLastMaskedGameConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetLastMaskedGameConfigReq.Size(m)
}
func (m *GetLastMaskedGameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastMaskedGameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastMaskedGameConfigReq proto.InternalMessageInfo

type GetLastMaskedGameConfigResp struct {
	Config               *MaskedGameConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetLastMaskedGameConfigResp) Reset()         { *m = GetLastMaskedGameConfigResp{} }
func (m *GetLastMaskedGameConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetLastMaskedGameConfigResp) ProtoMessage()    {}
func (*GetLastMaskedGameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{60}
}
func (m *GetLastMaskedGameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastMaskedGameConfigResp.Unmarshal(m, b)
}
func (m *GetLastMaskedGameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastMaskedGameConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetLastMaskedGameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastMaskedGameConfigResp.Merge(dst, src)
}
func (m *GetLastMaskedGameConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetLastMaskedGameConfigResp.Size(m)
}
func (m *GetLastMaskedGameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastMaskedGameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastMaskedGameConfigResp proto.InternalMessageInfo

func (m *GetLastMaskedGameConfigResp) GetConfig() *MaskedGameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type TestPushQuickKillChangeReq struct {
	EventType            uint32   `protobuf:"varint,1,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	TriggerChannelId     uint32   `protobuf:"varint,2,opt,name=trigger_channel_id,json=triggerChannelId,proto3" json:"trigger_channel_id,omitempty"`
	KillChannelId        uint32   `protobuf:"varint,3,opt,name=kill_channel_id,json=killChannelId,proto3" json:"kill_channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestPushQuickKillChangeReq) Reset()         { *m = TestPushQuickKillChangeReq{} }
func (m *TestPushQuickKillChangeReq) String() string { return proto.CompactTextString(m) }
func (*TestPushQuickKillChangeReq) ProtoMessage()    {}
func (*TestPushQuickKillChangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{61}
}
func (m *TestPushQuickKillChangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestPushQuickKillChangeReq.Unmarshal(m, b)
}
func (m *TestPushQuickKillChangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestPushQuickKillChangeReq.Marshal(b, m, deterministic)
}
func (dst *TestPushQuickKillChangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestPushQuickKillChangeReq.Merge(dst, src)
}
func (m *TestPushQuickKillChangeReq) XXX_Size() int {
	return xxx_messageInfo_TestPushQuickKillChangeReq.Size(m)
}
func (m *TestPushQuickKillChangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestPushQuickKillChangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestPushQuickKillChangeReq proto.InternalMessageInfo

func (m *TestPushQuickKillChangeReq) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

func (m *TestPushQuickKillChangeReq) GetTriggerChannelId() uint32 {
	if m != nil {
		return m.TriggerChannelId
	}
	return 0
}

func (m *TestPushQuickKillChangeReq) GetKillChannelId() uint32 {
	if m != nil {
		return m.KillChannelId
	}
	return 0
}

type TestPushQuickKillChangeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestPushQuickKillChangeResp) Reset()         { *m = TestPushQuickKillChangeResp{} }
func (m *TestPushQuickKillChangeResp) String() string { return proto.CompactTextString(m) }
func (*TestPushQuickKillChangeResp) ProtoMessage()    {}
func (*TestPushQuickKillChangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{62}
}
func (m *TestPushQuickKillChangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestPushQuickKillChangeResp.Unmarshal(m, b)
}
func (m *TestPushQuickKillChangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestPushQuickKillChangeResp.Marshal(b, m, deterministic)
}
func (dst *TestPushQuickKillChangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestPushQuickKillChangeResp.Merge(dst, src)
}
func (m *TestPushQuickKillChangeResp) XXX_Size() int {
	return xxx_messageInfo_TestPushQuickKillChangeResp.Size(m)
}
func (m *TestPushQuickKillChangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestPushQuickKillChangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestPushQuickKillChangeResp proto.InternalMessageInfo

type GodLikeTopUsers struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Rank                 uint32   `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`
	Contribution         uint32   `protobuf:"varint,3,opt,name=contribution,proto3" json:"contribution,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GodLikeTopUsers) Reset()         { *m = GodLikeTopUsers{} }
func (m *GodLikeTopUsers) String() string { return proto.CompactTextString(m) }
func (*GodLikeTopUsers) ProtoMessage()    {}
func (*GodLikeTopUsers) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{63}
}
func (m *GodLikeTopUsers) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GodLikeTopUsers.Unmarshal(m, b)
}
func (m *GodLikeTopUsers) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GodLikeTopUsers.Marshal(b, m, deterministic)
}
func (dst *GodLikeTopUsers) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GodLikeTopUsers.Merge(dst, src)
}
func (m *GodLikeTopUsers) XXX_Size() int {
	return xxx_messageInfo_GodLikeTopUsers.Size(m)
}
func (m *GodLikeTopUsers) XXX_DiscardUnknown() {
	xxx_messageInfo_GodLikeTopUsers.DiscardUnknown(m)
}

var xxx_messageInfo_GodLikeTopUsers proto.InternalMessageInfo

func (m *GodLikeTopUsers) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GodLikeTopUsers) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *GodLikeTopUsers) GetContribution() uint32 {
	if m != nil {
		return m.Contribution
	}
	return 0
}

func (m *GodLikeTopUsers) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type NotifyGodLikeTopUsersReq struct {
	GameId               uint32             `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Users                []*GodLikeTopUsers `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
	ExpiredTime          uint32             `protobuf:"varint,3,opt,name=expired_time,json=expiredTime,proto3" json:"expired_time,omitempty"`
	ActivityEndTime      uint32             `protobuf:"varint,4,opt,name=activity_end_time,json=activityEndTime,proto3" json:"activity_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *NotifyGodLikeTopUsersReq) Reset()         { *m = NotifyGodLikeTopUsersReq{} }
func (m *NotifyGodLikeTopUsersReq) String() string { return proto.CompactTextString(m) }
func (*NotifyGodLikeTopUsersReq) ProtoMessage()    {}
func (*NotifyGodLikeTopUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{64}
}
func (m *NotifyGodLikeTopUsersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyGodLikeTopUsersReq.Unmarshal(m, b)
}
func (m *NotifyGodLikeTopUsersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyGodLikeTopUsersReq.Marshal(b, m, deterministic)
}
func (dst *NotifyGodLikeTopUsersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyGodLikeTopUsersReq.Merge(dst, src)
}
func (m *NotifyGodLikeTopUsersReq) XXX_Size() int {
	return xxx_messageInfo_NotifyGodLikeTopUsersReq.Size(m)
}
func (m *NotifyGodLikeTopUsersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyGodLikeTopUsersReq.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyGodLikeTopUsersReq proto.InternalMessageInfo

func (m *NotifyGodLikeTopUsersReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *NotifyGodLikeTopUsersReq) GetUsers() []*GodLikeTopUsers {
	if m != nil {
		return m.Users
	}
	return nil
}

func (m *NotifyGodLikeTopUsersReq) GetExpiredTime() uint32 {
	if m != nil {
		return m.ExpiredTime
	}
	return 0
}

func (m *NotifyGodLikeTopUsersReq) GetActivityEndTime() uint32 {
	if m != nil {
		return m.ActivityEndTime
	}
	return 0
}

type NotifyGodLikeTopUsersResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyGodLikeTopUsersResp) Reset()         { *m = NotifyGodLikeTopUsersResp{} }
func (m *NotifyGodLikeTopUsersResp) String() string { return proto.CompactTextString(m) }
func (*NotifyGodLikeTopUsersResp) ProtoMessage()    {}
func (*NotifyGodLikeTopUsersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{65}
}
func (m *NotifyGodLikeTopUsersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyGodLikeTopUsersResp.Unmarshal(m, b)
}
func (m *NotifyGodLikeTopUsersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyGodLikeTopUsersResp.Marshal(b, m, deterministic)
}
func (dst *NotifyGodLikeTopUsersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyGodLikeTopUsersResp.Merge(dst, src)
}
func (m *NotifyGodLikeTopUsersResp) XXX_Size() int {
	return xxx_messageInfo_NotifyGodLikeTopUsersResp.Size(m)
}
func (m *NotifyGodLikeTopUsersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyGodLikeTopUsersResp.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyGodLikeTopUsersResp proto.InternalMessageInfo

// 蒙面pk榜单入口
type CheckMaskedPkRankEntryReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckMaskedPkRankEntryReq) Reset()         { *m = CheckMaskedPkRankEntryReq{} }
func (m *CheckMaskedPkRankEntryReq) String() string { return proto.CompactTextString(m) }
func (*CheckMaskedPkRankEntryReq) ProtoMessage()    {}
func (*CheckMaskedPkRankEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{66}
}
func (m *CheckMaskedPkRankEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckMaskedPkRankEntryReq.Unmarshal(m, b)
}
func (m *CheckMaskedPkRankEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckMaskedPkRankEntryReq.Marshal(b, m, deterministic)
}
func (dst *CheckMaskedPkRankEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckMaskedPkRankEntryReq.Merge(dst, src)
}
func (m *CheckMaskedPkRankEntryReq) XXX_Size() int {
	return xxx_messageInfo_CheckMaskedPkRankEntryReq.Size(m)
}
func (m *CheckMaskedPkRankEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckMaskedPkRankEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckMaskedPkRankEntryReq proto.InternalMessageInfo

type CheckMaskedPkRankEntryResp struct {
	EntryEnable          bool     `protobuf:"varint,1,opt,name=entry_enable,json=entryEnable,proto3" json:"entry_enable,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckMaskedPkRankEntryResp) Reset()         { *m = CheckMaskedPkRankEntryResp{} }
func (m *CheckMaskedPkRankEntryResp) String() string { return proto.CompactTextString(m) }
func (*CheckMaskedPkRankEntryResp) ProtoMessage()    {}
func (*CheckMaskedPkRankEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{67}
}
func (m *CheckMaskedPkRankEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckMaskedPkRankEntryResp.Unmarshal(m, b)
}
func (m *CheckMaskedPkRankEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckMaskedPkRankEntryResp.Marshal(b, m, deterministic)
}
func (dst *CheckMaskedPkRankEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckMaskedPkRankEntryResp.Merge(dst, src)
}
func (m *CheckMaskedPkRankEntryResp) XXX_Size() int {
	return xxx_messageInfo_CheckMaskedPkRankEntryResp.Size(m)
}
func (m *CheckMaskedPkRankEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckMaskedPkRankEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckMaskedPkRankEntryResp proto.InternalMessageInfo

func (m *CheckMaskedPkRankEntryResp) GetEntryEnable() bool {
	if m != nil {
		return m.EntryEnable
	}
	return false
}

type UserTaillightInfo struct {
	// 尾灯业务id
	BizId uint32 `protobuf:"varint,1,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`
	// 尾灯数量
	Num                  uint32   `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserTaillightInfo) Reset()         { *m = UserTaillightInfo{} }
func (m *UserTaillightInfo) String() string { return proto.CompactTextString(m) }
func (*UserTaillightInfo) ProtoMessage()    {}
func (*UserTaillightInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{68}
}
func (m *UserTaillightInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserTaillightInfo.Unmarshal(m, b)
}
func (m *UserTaillightInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserTaillightInfo.Marshal(b, m, deterministic)
}
func (dst *UserTaillightInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserTaillightInfo.Merge(dst, src)
}
func (m *UserTaillightInfo) XXX_Size() int {
	return xxx_messageInfo_UserTaillightInfo.Size(m)
}
func (m *UserTaillightInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserTaillightInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserTaillightInfo proto.InternalMessageInfo

func (m *UserTaillightInfo) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *UserTaillightInfo) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type MaskedPKConsumeInfo struct {
	Uid                  uint32             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string             `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Score                uint32             `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	Rich                 uint32             `protobuf:"varint,4,opt,name=rich,proto3" json:"rich,omitempty"`
	Charm                uint32             `protobuf:"varint,5,opt,name=charm,proto3" json:"charm,omitempty"`
	Nickname             string             `protobuf:"bytes,6,opt,name=nickname,proto3" json:"nickname,omitempty"`
	NobilityLevel        uint32             `protobuf:"varint,7,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	GroupFansLevel       uint32             `protobuf:"varint,8,opt,name=group_fans_level,json=groupFansLevel,proto3" json:"group_fans_level,omitempty"`
	ChannelMemLevel      uint32             `protobuf:"varint,9,opt,name=channel_mem_level,json=channelMemLevel,proto3" json:"channel_mem_level,omitempty"`
	GroupName            string             `protobuf:"bytes,10,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	Sex                  uint32             `protobuf:"varint,12,opt,name=sex,proto3" json:"sex,omitempty"`
	Rank                 uint32             `protobuf:"varint,13,opt,name=rank,proto3" json:"rank,omitempty"`
	DValue               uint32             `protobuf:"varint,14,opt,name=d_value,json=dValue,proto3" json:"d_value,omitempty"`
	PlatformLight        *UserTaillightInfo `protobuf:"bytes,15,opt,name=platform_light,json=platformLight,proto3" json:"platform_light,omitempty"`
	ChannelLight         *UserTaillightInfo `protobuf:"bytes,16,opt,name=channel_light,json=channelLight,proto3" json:"channel_light,omitempty"`
	SuperPlayerLevel     uint32             `protobuf:"varint,17,opt,name=super_player_level,json=superPlayerLevel,proto3" json:"super_player_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MaskedPKConsumeInfo) Reset()         { *m = MaskedPKConsumeInfo{} }
func (m *MaskedPKConsumeInfo) String() string { return proto.CompactTextString(m) }
func (*MaskedPKConsumeInfo) ProtoMessage()    {}
func (*MaskedPKConsumeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{69}
}
func (m *MaskedPKConsumeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaskedPKConsumeInfo.Unmarshal(m, b)
}
func (m *MaskedPKConsumeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaskedPKConsumeInfo.Marshal(b, m, deterministic)
}
func (dst *MaskedPKConsumeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaskedPKConsumeInfo.Merge(dst, src)
}
func (m *MaskedPKConsumeInfo) XXX_Size() int {
	return xxx_messageInfo_MaskedPKConsumeInfo.Size(m)
}
func (m *MaskedPKConsumeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MaskedPKConsumeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MaskedPKConsumeInfo proto.InternalMessageInfo

func (m *MaskedPKConsumeInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MaskedPKConsumeInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetRich() uint32 {
	if m != nil {
		return m.Rich
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *MaskedPKConsumeInfo) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetGroupFansLevel() uint32 {
	if m != nil {
		return m.GroupFansLevel
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetChannelMemLevel() uint32 {
	if m != nil {
		return m.ChannelMemLevel
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *MaskedPKConsumeInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetDValue() uint32 {
	if m != nil {
		return m.DValue
	}
	return 0
}

func (m *MaskedPKConsumeInfo) GetPlatformLight() *UserTaillightInfo {
	if m != nil {
		return m.PlatformLight
	}
	return nil
}

func (m *MaskedPKConsumeInfo) GetChannelLight() *UserTaillightInfo {
	if m != nil {
		return m.ChannelLight
	}
	return nil
}

func (m *MaskedPKConsumeInfo) GetSuperPlayerLevel() uint32 {
	if m != nil {
		return m.SuperPlayerLevel
	}
	return 0
}

// 蒙面PK消费榜
type MaskedPkGetConsumeTopNReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BeginId              uint32   `protobuf:"varint,2,opt,name=begin_id,json=beginId,proto3" json:"begin_id,omitempty"`
	ReqCnt               uint32   `protobuf:"varint,3,opt,name=req_cnt,json=reqCnt,proto3" json:"req_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MaskedPkGetConsumeTopNReq) Reset()         { *m = MaskedPkGetConsumeTopNReq{} }
func (m *MaskedPkGetConsumeTopNReq) String() string { return proto.CompactTextString(m) }
func (*MaskedPkGetConsumeTopNReq) ProtoMessage()    {}
func (*MaskedPkGetConsumeTopNReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{70}
}
func (m *MaskedPkGetConsumeTopNReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaskedPkGetConsumeTopNReq.Unmarshal(m, b)
}
func (m *MaskedPkGetConsumeTopNReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaskedPkGetConsumeTopNReq.Marshal(b, m, deterministic)
}
func (dst *MaskedPkGetConsumeTopNReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaskedPkGetConsumeTopNReq.Merge(dst, src)
}
func (m *MaskedPkGetConsumeTopNReq) XXX_Size() int {
	return xxx_messageInfo_MaskedPkGetConsumeTopNReq.Size(m)
}
func (m *MaskedPkGetConsumeTopNReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MaskedPkGetConsumeTopNReq.DiscardUnknown(m)
}

var xxx_messageInfo_MaskedPkGetConsumeTopNReq proto.InternalMessageInfo

func (m *MaskedPkGetConsumeTopNReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MaskedPkGetConsumeTopNReq) GetBeginId() uint32 {
	if m != nil {
		return m.BeginId
	}
	return 0
}

func (m *MaskedPkGetConsumeTopNReq) GetReqCnt() uint32 {
	if m != nil {
		return m.ReqCnt
	}
	return 0
}

type MaskedPkGetConsumeTopNResp struct {
	ChannelId            uint32                 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BeginId              uint32                 `protobuf:"varint,2,opt,name=begin_id,json=beginId,proto3" json:"begin_id,omitempty"`
	MemberList           []*MaskedPKConsumeInfo `protobuf:"bytes,3,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	MyInfo               *MaskedPKConsumeInfo   `protobuf:"bytes,4,opt,name=my_info,json=myInfo,proto3" json:"my_info,omitempty"`
	ViewCnt              uint32                 `protobuf:"varint,5,opt,name=view_cnt,json=viewCnt,proto3" json:"view_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *MaskedPkGetConsumeTopNResp) Reset()         { *m = MaskedPkGetConsumeTopNResp{} }
func (m *MaskedPkGetConsumeTopNResp) String() string { return proto.CompactTextString(m) }
func (*MaskedPkGetConsumeTopNResp) ProtoMessage()    {}
func (*MaskedPkGetConsumeTopNResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_masked_pk_live_c95f011f20423a78, []int{71}
}
func (m *MaskedPkGetConsumeTopNResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaskedPkGetConsumeTopNResp.Unmarshal(m, b)
}
func (m *MaskedPkGetConsumeTopNResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaskedPkGetConsumeTopNResp.Marshal(b, m, deterministic)
}
func (dst *MaskedPkGetConsumeTopNResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaskedPkGetConsumeTopNResp.Merge(dst, src)
}
func (m *MaskedPkGetConsumeTopNResp) XXX_Size() int {
	return xxx_messageInfo_MaskedPkGetConsumeTopNResp.Size(m)
}
func (m *MaskedPkGetConsumeTopNResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MaskedPkGetConsumeTopNResp.DiscardUnknown(m)
}

var xxx_messageInfo_MaskedPkGetConsumeTopNResp proto.InternalMessageInfo

func (m *MaskedPkGetConsumeTopNResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MaskedPkGetConsumeTopNResp) GetBeginId() uint32 {
	if m != nil {
		return m.BeginId
	}
	return 0
}

func (m *MaskedPkGetConsumeTopNResp) GetMemberList() []*MaskedPKConsumeInfo {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *MaskedPkGetConsumeTopNResp) GetMyInfo() *MaskedPKConsumeInfo {
	if m != nil {
		return m.MyInfo
	}
	return nil
}

func (m *MaskedPkGetConsumeTopNResp) GetViewCnt() uint32 {
	if m != nil {
		return m.ViewCnt
	}
	return 0
}

func init() {
	proto.RegisterType((*UserPrivilege)(nil), "masked_pk_live.UserPrivilege")
	proto.RegisterType((*UserProfile)(nil), "masked_pk_live.UserProfile")
	proto.RegisterType((*ChannelMaskedPKConf)(nil), "masked_pk_live.ChannelMaskedPKConf")
	proto.RegisterType((*ChannelMaskedPKStatus)(nil), "masked_pk_live.ChannelMaskedPKStatus")
	proto.RegisterType((*ChannelMaskedPKRankMem)(nil), "masked_pk_live.ChannelMaskedPKRankMem")
	proto.RegisterType((*ChannelMaskedPKInfo)(nil), "masked_pk_live.ChannelMaskedPKInfo")
	proto.RegisterType((*QuickKillInfo)(nil), "masked_pk_live.QuickKillInfo")
	proto.RegisterType((*PeakPkInfo)(nil), "masked_pk_live.PeakPkInfo")
	proto.RegisterType((*ChannelMaskedPKBattle)(nil), "masked_pk_live.ChannelMaskedPKBattle")
	proto.RegisterType((*ChannelMaskedPKRevive)(nil), "masked_pk_live.ChannelMaskedPKRevive")
	proto.RegisterType((*GetLiveChannelMaskedPKInfoReq)(nil), "masked_pk_live.GetLiveChannelMaskedPKInfoReq")
	proto.RegisterType((*GetLiveChannelMaskedPKInfoResp)(nil), "masked_pk_live.GetLiveChannelMaskedPKInfoResp")
	proto.RegisterType((*StartLiveChannelMaskedPKReq)(nil), "masked_pk_live.StartLiveChannelMaskedPKReq")
	proto.RegisterType((*StartLiveChannelMaskedPKResp)(nil), "masked_pk_live.StartLiveChannelMaskedPKResp")
	proto.RegisterType((*GiveUpLiveChannelMaskedPKReq)(nil), "masked_pk_live.GiveUpLiveChannelMaskedPKReq")
	proto.RegisterType((*GiveUpLiveChannelMaskedPKResp)(nil), "masked_pk_live.GiveUpLiveChannelMaskedPKResp")
	proto.RegisterType((*CancelLiveChannelMaskedPKReq)(nil), "masked_pk_live.CancelLiveChannelMaskedPKReq")
	proto.RegisterType((*CancelLiveChannelMaskedPKResp)(nil), "masked_pk_live.CancelLiveChannelMaskedPKResp")
	proto.RegisterType((*GetLiveChannelMaskedPKStatusReq)(nil), "masked_pk_live.GetLiveChannelMaskedPKStatusReq")
	proto.RegisterType((*GetLiveChannelMaskedPKStatusResp)(nil), "masked_pk_live.GetLiveChannelMaskedPKStatusResp")
	proto.RegisterType((*BatchGetLiveChannelMaskedPKStatusReq)(nil), "masked_pk_live.BatchGetLiveChannelMaskedPKStatusReq")
	proto.RegisterType((*BatchGetLiveChannelMaskedPKStatusResp)(nil), "masked_pk_live.BatchGetLiveChannelMaskedPKStatusResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "masked_pk_live.BatchGetLiveChannelMaskedPKStatusResp.ChannelStatusMapEntry")
	proto.RegisterType((*GetLiveChannelMaskedPKCurrConfWithUserReq)(nil), "masked_pk_live.GetLiveChannelMaskedPKCurrConfWithUserReq")
	proto.RegisterType((*GetLiveChannelMaskedPKCurrConfWithUserResp)(nil), "masked_pk_live.GetLiveChannelMaskedPKCurrConfWithUserResp")
	proto.RegisterType((*GetLiveChannelMaskedPKCurrConfReq)(nil), "masked_pk_live.GetLiveChannelMaskedPKCurrConfReq")
	proto.RegisterType((*GetLiveChannelMaskedPKCurrConfResp)(nil), "masked_pk_live.GetLiveChannelMaskedPKCurrConfResp")
	proto.RegisterType((*ChannelMaskedPKAnchorRankMem)(nil), "masked_pk_live.ChannelMaskedPKAnchorRankMem")
	proto.RegisterType((*GetAudienceRankListReq)(nil), "masked_pk_live.GetAudienceRankListReq")
	proto.RegisterType((*GetAudienceRankListResp)(nil), "masked_pk_live.GetAudienceRankListResp")
	proto.RegisterType((*BatchAddUserToQualificationReq)(nil), "masked_pk_live.BatchAddUserToQualificationReq")
	proto.RegisterType((*Qualification)(nil), "masked_pk_live.Qualification")
	proto.RegisterType((*BatchAddUserToQualificationResp)(nil), "masked_pk_live.BatchAddUserToQualificationResp")
	proto.RegisterType((*IsUserHasQualificationReq)(nil), "masked_pk_live.IsUserHasQualificationReq")
	proto.RegisterType((*IsUserHasQualificationResp)(nil), "masked_pk_live.IsUserHasQualificationResp")
	proto.RegisterType((*AddMaskedGameConfigReq)(nil), "masked_pk_live.AddMaskedGameConfigReq")
	proto.RegisterType((*AddMaskedGameConfigResp)(nil), "masked_pk_live.AddMaskedGameConfigResp")
	proto.RegisterType((*MaskedGameConfig)(nil), "masked_pk_live.MaskedGameConfig")
	proto.RegisterType((*DelMaskedGameConfigReq)(nil), "masked_pk_live.DelMaskedGameConfigReq")
	proto.RegisterType((*DelMaskedGameConfigResp)(nil), "masked_pk_live.DelMaskedGameConfigResp")
	proto.RegisterType((*BatchAddUserToWhiteListReq)(nil), "masked_pk_live.BatchAddUserToWhiteListReq")
	proto.RegisterType((*BatchAddUserToWhiteListResp)(nil), "masked_pk_live.BatchAddUserToWhiteListResp")
	proto.RegisterType((*BatchDelUserFromWhiteListReq)(nil), "masked_pk_live.BatchDelUserFromWhiteListReq")
	proto.RegisterType((*BatchDelUserFromWhiteListResp)(nil), "masked_pk_live.BatchDelUserFromWhiteListResp")
	proto.RegisterType((*GetAllWhiteListReq)(nil), "masked_pk_live.GetAllWhiteListReq")
	proto.RegisterType((*GetAllWhiteListResp)(nil), "masked_pk_live.GetAllWhiteListResp")
	proto.RegisterType((*BatchDelUserFromQualificationReq)(nil), "masked_pk_live.BatchDelUserFromQualificationReq")
	proto.RegisterType((*BatchDelUserFromQualificationResp)(nil), "masked_pk_live.BatchDelUserFromQualificationResp")
	proto.RegisterType((*GetUserFromQualificationReq)(nil), "masked_pk_live.GetUserFromQualificationReq")
	proto.RegisterType((*GetUserFromQualificationResp)(nil), "masked_pk_live.GetUserFromQualificationResp")
	proto.RegisterType((*UpdateWhiteListConfigReq)(nil), "masked_pk_live.UpdateWhiteListConfigReq")
	proto.RegisterType((*UpdateWhiteListConfigResp)(nil), "masked_pk_live.UpdateWhiteListConfigResp")
	proto.RegisterType((*GetWhiteListConfigReq)(nil), "masked_pk_live.GetWhiteListConfigReq")
	proto.RegisterType((*GetWhiteListConfigResp)(nil), "masked_pk_live.GetWhiteListConfigResp")
	proto.RegisterType((*SetMonthGiftValueReq)(nil), "masked_pk_live.SetMonthGiftValueReq")
	proto.RegisterType((*SetMonthGiftValueResp)(nil), "masked_pk_live.SetMonthGiftValueResp")
	proto.RegisterType((*UpdateMaskedGameConfigReq)(nil), "masked_pk_live.UpdateMaskedGameConfigReq")
	proto.RegisterType((*UpdateMaskedGameConfigResp)(nil), "masked_pk_live.UpdateMaskedGameConfigResp")
	proto.RegisterType((*GetAllMaskedGameConfigReq)(nil), "masked_pk_live.GetAllMaskedGameConfigReq")
	proto.RegisterType((*GetAllMaskedGameConfigResp)(nil), "masked_pk_live.GetAllMaskedGameConfigResp")
	proto.RegisterType((*GetLastMaskedGameConfigReq)(nil), "masked_pk_live.GetLastMaskedGameConfigReq")
	proto.RegisterType((*GetLastMaskedGameConfigResp)(nil), "masked_pk_live.GetLastMaskedGameConfigResp")
	proto.RegisterType((*TestPushQuickKillChangeReq)(nil), "masked_pk_live.TestPushQuickKillChangeReq")
	proto.RegisterType((*TestPushQuickKillChangeResp)(nil), "masked_pk_live.TestPushQuickKillChangeResp")
	proto.RegisterType((*GodLikeTopUsers)(nil), "masked_pk_live.GodLikeTopUsers")
	proto.RegisterType((*NotifyGodLikeTopUsersReq)(nil), "masked_pk_live.NotifyGodLikeTopUsersReq")
	proto.RegisterType((*NotifyGodLikeTopUsersResp)(nil), "masked_pk_live.NotifyGodLikeTopUsersResp")
	proto.RegisterType((*CheckMaskedPkRankEntryReq)(nil), "masked_pk_live.CheckMaskedPkRankEntryReq")
	proto.RegisterType((*CheckMaskedPkRankEntryResp)(nil), "masked_pk_live.CheckMaskedPkRankEntryResp")
	proto.RegisterType((*UserTaillightInfo)(nil), "masked_pk_live.UserTaillightInfo")
	proto.RegisterType((*MaskedPKConsumeInfo)(nil), "masked_pk_live.MaskedPKConsumeInfo")
	proto.RegisterType((*MaskedPkGetConsumeTopNReq)(nil), "masked_pk_live.MaskedPkGetConsumeTopNReq")
	proto.RegisterType((*MaskedPkGetConsumeTopNResp)(nil), "masked_pk_live.MaskedPkGetConsumeTopNResp")
	proto.RegisterEnum("masked_pk_live.DivideType", DivideType_name, DivideType_value)
	proto.RegisterEnum("masked_pk_live.ChannelMaskedPKConf_ChipRole", ChannelMaskedPKConf_ChipRole_name, ChannelMaskedPKConf_ChipRole_value)
	proto.RegisterEnum("masked_pk_live.ChannelMaskedPKStatus_Status", ChannelMaskedPKStatus_Status_name, ChannelMaskedPKStatus_Status_value)
	proto.RegisterEnum("masked_pk_live.ChannelMaskedPKBattle_SubPhrase", ChannelMaskedPKBattle_SubPhrase_name, ChannelMaskedPKBattle_SubPhrase_value)
	proto.RegisterEnum("masked_pk_live.TestPushQuickKillChangeReq_EventType", TestPushQuickKillChangeReq_EventType_name, TestPushQuickKillChangeReq_EventType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MaskedPKLiveClient is the client API for MaskedPKLive service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MaskedPKLiveClient interface {
	StartLiveChannelMaskedPK(ctx context.Context, in *StartLiveChannelMaskedPKReq, opts ...grpc.CallOption) (*StartLiveChannelMaskedPKResp, error)
	GiveUpLiveChannelMaskedPK(ctx context.Context, in *GiveUpLiveChannelMaskedPKReq, opts ...grpc.CallOption) (*GiveUpLiveChannelMaskedPKResp, error)
	CancelLiveChannelMaskedPK(ctx context.Context, in *CancelLiveChannelMaskedPKReq, opts ...grpc.CallOption) (*CancelLiveChannelMaskedPKResp, error)
	GetLiveChannelMaskedPKCurrConfWithUser(ctx context.Context, in *GetLiveChannelMaskedPKCurrConfWithUserReq, opts ...grpc.CallOption) (*GetLiveChannelMaskedPKCurrConfWithUserResp, error)
	GetLiveChannelMaskedPKCurrConf(ctx context.Context, in *GetLiveChannelMaskedPKCurrConfReq, opts ...grpc.CallOption) (*GetLiveChannelMaskedPKCurrConfResp, error)
	GetLiveChannelMaskedPKInfo(ctx context.Context, in *GetLiveChannelMaskedPKInfoReq, opts ...grpc.CallOption) (*GetLiveChannelMaskedPKInfoResp, error)
	GetAudienceRankList(ctx context.Context, in *GetAudienceRankListReq, opts ...grpc.CallOption) (*GetAudienceRankListResp, error)
	GetLiveChannelMaskedPKStatus(ctx context.Context, in *GetLiveChannelMaskedPKStatusReq, opts ...grpc.CallOption) (*GetLiveChannelMaskedPKStatusResp, error)
	BatchGetLiveChannelMaskedPKStatus(ctx context.Context, in *BatchGetLiveChannelMaskedPKStatusReq, opts ...grpc.CallOption) (*BatchGetLiveChannelMaskedPKStatusResp, error)
	// 为（房间 ）添加参赛资格，只房间
	BatchAddUserToQualification(ctx context.Context, in *BatchAddUserToQualificationReq, opts ...grpc.CallOption) (*BatchAddUserToQualificationResp, error)
	// 删除（房间 ）的参赛资格
	BatchDelUserFromQualification(ctx context.Context, in *BatchDelUserFromQualificationReq, opts ...grpc.CallOption) (*BatchDelUserFromQualificationResp, error)
	// 获取参赛资格名单
	GetUserFromQualification(ctx context.Context, in *GetUserFromQualificationReq, opts ...grpc.CallOption) (*GetUserFromQualificationResp, error)
	// （房间）是否具有参赛资格
	IsUserHasQualification(ctx context.Context, in *IsUserHasQualificationReq, opts ...grpc.CallOption) (*IsUserHasQualificationResp, error)
	// 添加PK场次配置
	AddMaskedGameConfig(ctx context.Context, in *AddMaskedGameConfigReq, opts ...grpc.CallOption) (*AddMaskedGameConfigResp, error)
	// 删除PK场次配置
	DelMaskedGameConfig(ctx context.Context, in *DelMaskedGameConfigReq, opts ...grpc.CallOption) (*DelMaskedGameConfigResp, error)
	// 更新对应gameId的PK场次配置
	UpdateMaskedGameConfig(ctx context.Context, in *UpdateMaskedGameConfigReq, opts ...grpc.CallOption) (*UpdateMaskedGameConfigResp, error)
	// 获得所有PK场次配置
	GetAllMaskedGameConfig(ctx context.Context, in *GetAllMaskedGameConfigReq, opts ...grpc.CallOption) (*GetAllMaskedGameConfigResp, error)
	// 获取上一场PK的场次配置
	GetLastMaskedGameConfig(ctx context.Context, in *GetLastMaskedGameConfigReq, opts ...grpc.CallOption) (*GetLastMaskedGameConfigResp, error)
	// 添加房间到白名单
	BatchAddUserToWhiteList(ctx context.Context, in *BatchAddUserToWhiteListReq, opts ...grpc.CallOption) (*BatchAddUserToWhiteListResp, error)
	// 删除房间白名单
	BatchDelUserFromWhiteList(ctx context.Context, in *BatchDelUserFromWhiteListReq, opts ...grpc.CallOption) (*BatchDelUserFromWhiteListResp, error)
	// 获得所有白名单内房间
	GetAllWhiteList(ctx context.Context, in *GetAllWhiteListReq, opts ...grpc.CallOption) (*GetAllWhiteListResp, error)
	// 更新白名单配置（是否允许同工会匹配，是否使用白名单），全局配置所以只有更新接口
	UpdateWhiteListConfig(ctx context.Context, in *UpdateWhiteListConfigReq, opts ...grpc.CallOption) (*UpdateWhiteListConfigResp, error)
	// 获取白名单配置（是否允许同工会匹配，是否使用白名单）
	GetWhiteListConfig(ctx context.Context, in *GetWhiteListConfigReq, opts ...grpc.CallOption) (*GetWhiteListConfigResp, error)
	// 测试用接口
	SetMonthGiftValue(ctx context.Context, in *SetMonthGiftValueReq, opts ...grpc.CallOption) (*SetMonthGiftValueResp, error)
	// only for test
	TestPushQuickKillChange(ctx context.Context, in *TestPushQuickKillChangeReq, opts ...grpc.CallOption) (*TestPushQuickKillChangeResp, error)
	// 通知神仙局前三用户
	NotifyGodLikeTopUsers(ctx context.Context, in *NotifyGodLikeTopUsersReq, opts ...grpc.CallOption) (*NotifyGodLikeTopUsersResp, error)
	// 蒙面PK房间消费榜入口是否开放
	CheckMaskedPkRankEntry(ctx context.Context, in *CheckMaskedPkRankEntryReq, opts ...grpc.CallOption) (*CheckMaskedPkRankEntryResp, error)
	// 蒙面PK房间消费榜单
	MaskedPkGetConsumeTopN(ctx context.Context, in *MaskedPkGetConsumeTopNReq, opts ...grpc.CallOption) (*MaskedPkGetConsumeTopNResp, error)
}

type maskedPKLiveClient struct {
	cc *grpc.ClientConn
}

func NewMaskedPKLiveClient(cc *grpc.ClientConn) MaskedPKLiveClient {
	return &maskedPKLiveClient{cc}
}

func (c *maskedPKLiveClient) StartLiveChannelMaskedPK(ctx context.Context, in *StartLiveChannelMaskedPKReq, opts ...grpc.CallOption) (*StartLiveChannelMaskedPKResp, error) {
	out := new(StartLiveChannelMaskedPKResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/StartLiveChannelMaskedPK", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) GiveUpLiveChannelMaskedPK(ctx context.Context, in *GiveUpLiveChannelMaskedPKReq, opts ...grpc.CallOption) (*GiveUpLiveChannelMaskedPKResp, error) {
	out := new(GiveUpLiveChannelMaskedPKResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/GiveUpLiveChannelMaskedPK", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) CancelLiveChannelMaskedPK(ctx context.Context, in *CancelLiveChannelMaskedPKReq, opts ...grpc.CallOption) (*CancelLiveChannelMaskedPKResp, error) {
	out := new(CancelLiveChannelMaskedPKResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/CancelLiveChannelMaskedPK", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) GetLiveChannelMaskedPKCurrConfWithUser(ctx context.Context, in *GetLiveChannelMaskedPKCurrConfWithUserReq, opts ...grpc.CallOption) (*GetLiveChannelMaskedPKCurrConfWithUserResp, error) {
	out := new(GetLiveChannelMaskedPKCurrConfWithUserResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/GetLiveChannelMaskedPKCurrConfWithUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) GetLiveChannelMaskedPKCurrConf(ctx context.Context, in *GetLiveChannelMaskedPKCurrConfReq, opts ...grpc.CallOption) (*GetLiveChannelMaskedPKCurrConfResp, error) {
	out := new(GetLiveChannelMaskedPKCurrConfResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/GetLiveChannelMaskedPKCurrConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) GetLiveChannelMaskedPKInfo(ctx context.Context, in *GetLiveChannelMaskedPKInfoReq, opts ...grpc.CallOption) (*GetLiveChannelMaskedPKInfoResp, error) {
	out := new(GetLiveChannelMaskedPKInfoResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/GetLiveChannelMaskedPKInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) GetAudienceRankList(ctx context.Context, in *GetAudienceRankListReq, opts ...grpc.CallOption) (*GetAudienceRankListResp, error) {
	out := new(GetAudienceRankListResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/GetAudienceRankList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) GetLiveChannelMaskedPKStatus(ctx context.Context, in *GetLiveChannelMaskedPKStatusReq, opts ...grpc.CallOption) (*GetLiveChannelMaskedPKStatusResp, error) {
	out := new(GetLiveChannelMaskedPKStatusResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/GetLiveChannelMaskedPKStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) BatchGetLiveChannelMaskedPKStatus(ctx context.Context, in *BatchGetLiveChannelMaskedPKStatusReq, opts ...grpc.CallOption) (*BatchGetLiveChannelMaskedPKStatusResp, error) {
	out := new(BatchGetLiveChannelMaskedPKStatusResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/BatchGetLiveChannelMaskedPKStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) BatchAddUserToQualification(ctx context.Context, in *BatchAddUserToQualificationReq, opts ...grpc.CallOption) (*BatchAddUserToQualificationResp, error) {
	out := new(BatchAddUserToQualificationResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/BatchAddUserToQualification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) BatchDelUserFromQualification(ctx context.Context, in *BatchDelUserFromQualificationReq, opts ...grpc.CallOption) (*BatchDelUserFromQualificationResp, error) {
	out := new(BatchDelUserFromQualificationResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/BatchDelUserFromQualification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) GetUserFromQualification(ctx context.Context, in *GetUserFromQualificationReq, opts ...grpc.CallOption) (*GetUserFromQualificationResp, error) {
	out := new(GetUserFromQualificationResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/GetUserFromQualification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) IsUserHasQualification(ctx context.Context, in *IsUserHasQualificationReq, opts ...grpc.CallOption) (*IsUserHasQualificationResp, error) {
	out := new(IsUserHasQualificationResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/IsUserHasQualification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) AddMaskedGameConfig(ctx context.Context, in *AddMaskedGameConfigReq, opts ...grpc.CallOption) (*AddMaskedGameConfigResp, error) {
	out := new(AddMaskedGameConfigResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/AddMaskedGameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) DelMaskedGameConfig(ctx context.Context, in *DelMaskedGameConfigReq, opts ...grpc.CallOption) (*DelMaskedGameConfigResp, error) {
	out := new(DelMaskedGameConfigResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/DelMaskedGameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) UpdateMaskedGameConfig(ctx context.Context, in *UpdateMaskedGameConfigReq, opts ...grpc.CallOption) (*UpdateMaskedGameConfigResp, error) {
	out := new(UpdateMaskedGameConfigResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/UpdateMaskedGameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) GetAllMaskedGameConfig(ctx context.Context, in *GetAllMaskedGameConfigReq, opts ...grpc.CallOption) (*GetAllMaskedGameConfigResp, error) {
	out := new(GetAllMaskedGameConfigResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/GetAllMaskedGameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) GetLastMaskedGameConfig(ctx context.Context, in *GetLastMaskedGameConfigReq, opts ...grpc.CallOption) (*GetLastMaskedGameConfigResp, error) {
	out := new(GetLastMaskedGameConfigResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/GetLastMaskedGameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) BatchAddUserToWhiteList(ctx context.Context, in *BatchAddUserToWhiteListReq, opts ...grpc.CallOption) (*BatchAddUserToWhiteListResp, error) {
	out := new(BatchAddUserToWhiteListResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/BatchAddUserToWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) BatchDelUserFromWhiteList(ctx context.Context, in *BatchDelUserFromWhiteListReq, opts ...grpc.CallOption) (*BatchDelUserFromWhiteListResp, error) {
	out := new(BatchDelUserFromWhiteListResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/BatchDelUserFromWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) GetAllWhiteList(ctx context.Context, in *GetAllWhiteListReq, opts ...grpc.CallOption) (*GetAllWhiteListResp, error) {
	out := new(GetAllWhiteListResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/GetAllWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) UpdateWhiteListConfig(ctx context.Context, in *UpdateWhiteListConfigReq, opts ...grpc.CallOption) (*UpdateWhiteListConfigResp, error) {
	out := new(UpdateWhiteListConfigResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/UpdateWhiteListConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) GetWhiteListConfig(ctx context.Context, in *GetWhiteListConfigReq, opts ...grpc.CallOption) (*GetWhiteListConfigResp, error) {
	out := new(GetWhiteListConfigResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/GetWhiteListConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) SetMonthGiftValue(ctx context.Context, in *SetMonthGiftValueReq, opts ...grpc.CallOption) (*SetMonthGiftValueResp, error) {
	out := new(SetMonthGiftValueResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/SetMonthGiftValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) TestPushQuickKillChange(ctx context.Context, in *TestPushQuickKillChangeReq, opts ...grpc.CallOption) (*TestPushQuickKillChangeResp, error) {
	out := new(TestPushQuickKillChangeResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/TestPushQuickKillChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) NotifyGodLikeTopUsers(ctx context.Context, in *NotifyGodLikeTopUsersReq, opts ...grpc.CallOption) (*NotifyGodLikeTopUsersResp, error) {
	out := new(NotifyGodLikeTopUsersResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/NotifyGodLikeTopUsers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) CheckMaskedPkRankEntry(ctx context.Context, in *CheckMaskedPkRankEntryReq, opts ...grpc.CallOption) (*CheckMaskedPkRankEntryResp, error) {
	out := new(CheckMaskedPkRankEntryResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/CheckMaskedPkRankEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedPKLiveClient) MaskedPkGetConsumeTopN(ctx context.Context, in *MaskedPkGetConsumeTopNReq, opts ...grpc.CallOption) (*MaskedPkGetConsumeTopNResp, error) {
	out := new(MaskedPkGetConsumeTopNResp)
	err := c.cc.Invoke(ctx, "/masked_pk_live.MaskedPKLive/MaskedPkGetConsumeTopN", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MaskedPKLiveServer is the server API for MaskedPKLive service.
type MaskedPKLiveServer interface {
	StartLiveChannelMaskedPK(context.Context, *StartLiveChannelMaskedPKReq) (*StartLiveChannelMaskedPKResp, error)
	GiveUpLiveChannelMaskedPK(context.Context, *GiveUpLiveChannelMaskedPKReq) (*GiveUpLiveChannelMaskedPKResp, error)
	CancelLiveChannelMaskedPK(context.Context, *CancelLiveChannelMaskedPKReq) (*CancelLiveChannelMaskedPKResp, error)
	GetLiveChannelMaskedPKCurrConfWithUser(context.Context, *GetLiveChannelMaskedPKCurrConfWithUserReq) (*GetLiveChannelMaskedPKCurrConfWithUserResp, error)
	GetLiveChannelMaskedPKCurrConf(context.Context, *GetLiveChannelMaskedPKCurrConfReq) (*GetLiveChannelMaskedPKCurrConfResp, error)
	GetLiveChannelMaskedPKInfo(context.Context, *GetLiveChannelMaskedPKInfoReq) (*GetLiveChannelMaskedPKInfoResp, error)
	GetAudienceRankList(context.Context, *GetAudienceRankListReq) (*GetAudienceRankListResp, error)
	GetLiveChannelMaskedPKStatus(context.Context, *GetLiveChannelMaskedPKStatusReq) (*GetLiveChannelMaskedPKStatusResp, error)
	BatchGetLiveChannelMaskedPKStatus(context.Context, *BatchGetLiveChannelMaskedPKStatusReq) (*BatchGetLiveChannelMaskedPKStatusResp, error)
	// 为（房间 ）添加参赛资格，只房间
	BatchAddUserToQualification(context.Context, *BatchAddUserToQualificationReq) (*BatchAddUserToQualificationResp, error)
	// 删除（房间 ）的参赛资格
	BatchDelUserFromQualification(context.Context, *BatchDelUserFromQualificationReq) (*BatchDelUserFromQualificationResp, error)
	// 获取参赛资格名单
	GetUserFromQualification(context.Context, *GetUserFromQualificationReq) (*GetUserFromQualificationResp, error)
	// （房间）是否具有参赛资格
	IsUserHasQualification(context.Context, *IsUserHasQualificationReq) (*IsUserHasQualificationResp, error)
	// 添加PK场次配置
	AddMaskedGameConfig(context.Context, *AddMaskedGameConfigReq) (*AddMaskedGameConfigResp, error)
	// 删除PK场次配置
	DelMaskedGameConfig(context.Context, *DelMaskedGameConfigReq) (*DelMaskedGameConfigResp, error)
	// 更新对应gameId的PK场次配置
	UpdateMaskedGameConfig(context.Context, *UpdateMaskedGameConfigReq) (*UpdateMaskedGameConfigResp, error)
	// 获得所有PK场次配置
	GetAllMaskedGameConfig(context.Context, *GetAllMaskedGameConfigReq) (*GetAllMaskedGameConfigResp, error)
	// 获取上一场PK的场次配置
	GetLastMaskedGameConfig(context.Context, *GetLastMaskedGameConfigReq) (*GetLastMaskedGameConfigResp, error)
	// 添加房间到白名单
	BatchAddUserToWhiteList(context.Context, *BatchAddUserToWhiteListReq) (*BatchAddUserToWhiteListResp, error)
	// 删除房间白名单
	BatchDelUserFromWhiteList(context.Context, *BatchDelUserFromWhiteListReq) (*BatchDelUserFromWhiteListResp, error)
	// 获得所有白名单内房间
	GetAllWhiteList(context.Context, *GetAllWhiteListReq) (*GetAllWhiteListResp, error)
	// 更新白名单配置（是否允许同工会匹配，是否使用白名单），全局配置所以只有更新接口
	UpdateWhiteListConfig(context.Context, *UpdateWhiteListConfigReq) (*UpdateWhiteListConfigResp, error)
	// 获取白名单配置（是否允许同工会匹配，是否使用白名单）
	GetWhiteListConfig(context.Context, *GetWhiteListConfigReq) (*GetWhiteListConfigResp, error)
	// 测试用接口
	SetMonthGiftValue(context.Context, *SetMonthGiftValueReq) (*SetMonthGiftValueResp, error)
	// only for test
	TestPushQuickKillChange(context.Context, *TestPushQuickKillChangeReq) (*TestPushQuickKillChangeResp, error)
	// 通知神仙局前三用户
	NotifyGodLikeTopUsers(context.Context, *NotifyGodLikeTopUsersReq) (*NotifyGodLikeTopUsersResp, error)
	// 蒙面PK房间消费榜入口是否开放
	CheckMaskedPkRankEntry(context.Context, *CheckMaskedPkRankEntryReq) (*CheckMaskedPkRankEntryResp, error)
	// 蒙面PK房间消费榜单
	MaskedPkGetConsumeTopN(context.Context, *MaskedPkGetConsumeTopNReq) (*MaskedPkGetConsumeTopNResp, error)
}

func RegisterMaskedPKLiveServer(s *grpc.Server, srv MaskedPKLiveServer) {
	s.RegisterService(&_MaskedPKLive_serviceDesc, srv)
}

func _MaskedPKLive_StartLiveChannelMaskedPK_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartLiveChannelMaskedPKReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).StartLiveChannelMaskedPK(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/StartLiveChannelMaskedPK",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).StartLiveChannelMaskedPK(ctx, req.(*StartLiveChannelMaskedPKReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_GiveUpLiveChannelMaskedPK_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveUpLiveChannelMaskedPKReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).GiveUpLiveChannelMaskedPK(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/GiveUpLiveChannelMaskedPK",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).GiveUpLiveChannelMaskedPK(ctx, req.(*GiveUpLiveChannelMaskedPKReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_CancelLiveChannelMaskedPK_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelLiveChannelMaskedPKReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).CancelLiveChannelMaskedPK(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/CancelLiveChannelMaskedPK",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).CancelLiveChannelMaskedPK(ctx, req.(*CancelLiveChannelMaskedPKReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_GetLiveChannelMaskedPKCurrConfWithUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveChannelMaskedPKCurrConfWithUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).GetLiveChannelMaskedPKCurrConfWithUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/GetLiveChannelMaskedPKCurrConfWithUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).GetLiveChannelMaskedPKCurrConfWithUser(ctx, req.(*GetLiveChannelMaskedPKCurrConfWithUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_GetLiveChannelMaskedPKCurrConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveChannelMaskedPKCurrConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).GetLiveChannelMaskedPKCurrConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/GetLiveChannelMaskedPKCurrConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).GetLiveChannelMaskedPKCurrConf(ctx, req.(*GetLiveChannelMaskedPKCurrConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_GetLiveChannelMaskedPKInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveChannelMaskedPKInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).GetLiveChannelMaskedPKInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/GetLiveChannelMaskedPKInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).GetLiveChannelMaskedPKInfo(ctx, req.(*GetLiveChannelMaskedPKInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_GetAudienceRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAudienceRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).GetAudienceRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/GetAudienceRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).GetAudienceRankList(ctx, req.(*GetAudienceRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_GetLiveChannelMaskedPKStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveChannelMaskedPKStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).GetLiveChannelMaskedPKStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/GetLiveChannelMaskedPKStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).GetLiveChannelMaskedPKStatus(ctx, req.(*GetLiveChannelMaskedPKStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_BatchGetLiveChannelMaskedPKStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetLiveChannelMaskedPKStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).BatchGetLiveChannelMaskedPKStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/BatchGetLiveChannelMaskedPKStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).BatchGetLiveChannelMaskedPKStatus(ctx, req.(*BatchGetLiveChannelMaskedPKStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_BatchAddUserToQualification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddUserToQualificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).BatchAddUserToQualification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/BatchAddUserToQualification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).BatchAddUserToQualification(ctx, req.(*BatchAddUserToQualificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_BatchDelUserFromQualification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelUserFromQualificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).BatchDelUserFromQualification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/BatchDelUserFromQualification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).BatchDelUserFromQualification(ctx, req.(*BatchDelUserFromQualificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_GetUserFromQualification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFromQualificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).GetUserFromQualification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/GetUserFromQualification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).GetUserFromQualification(ctx, req.(*GetUserFromQualificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_IsUserHasQualification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsUserHasQualificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).IsUserHasQualification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/IsUserHasQualification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).IsUserHasQualification(ctx, req.(*IsUserHasQualificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_AddMaskedGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMaskedGameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).AddMaskedGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/AddMaskedGameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).AddMaskedGameConfig(ctx, req.(*AddMaskedGameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_DelMaskedGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelMaskedGameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).DelMaskedGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/DelMaskedGameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).DelMaskedGameConfig(ctx, req.(*DelMaskedGameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_UpdateMaskedGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMaskedGameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).UpdateMaskedGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/UpdateMaskedGameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).UpdateMaskedGameConfig(ctx, req.(*UpdateMaskedGameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_GetAllMaskedGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllMaskedGameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).GetAllMaskedGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/GetAllMaskedGameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).GetAllMaskedGameConfig(ctx, req.(*GetAllMaskedGameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_GetLastMaskedGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastMaskedGameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).GetLastMaskedGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/GetLastMaskedGameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).GetLastMaskedGameConfig(ctx, req.(*GetLastMaskedGameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_BatchAddUserToWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddUserToWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).BatchAddUserToWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/BatchAddUserToWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).BatchAddUserToWhiteList(ctx, req.(*BatchAddUserToWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_BatchDelUserFromWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelUserFromWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).BatchDelUserFromWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/BatchDelUserFromWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).BatchDelUserFromWhiteList(ctx, req.(*BatchDelUserFromWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_GetAllWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).GetAllWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/GetAllWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).GetAllWhiteList(ctx, req.(*GetAllWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_UpdateWhiteListConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWhiteListConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).UpdateWhiteListConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/UpdateWhiteListConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).UpdateWhiteListConfig(ctx, req.(*UpdateWhiteListConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_GetWhiteListConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWhiteListConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).GetWhiteListConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/GetWhiteListConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).GetWhiteListConfig(ctx, req.(*GetWhiteListConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_SetMonthGiftValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMonthGiftValueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).SetMonthGiftValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/SetMonthGiftValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).SetMonthGiftValue(ctx, req.(*SetMonthGiftValueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_TestPushQuickKillChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestPushQuickKillChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).TestPushQuickKillChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/TestPushQuickKillChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).TestPushQuickKillChange(ctx, req.(*TestPushQuickKillChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_NotifyGodLikeTopUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyGodLikeTopUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).NotifyGodLikeTopUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/NotifyGodLikeTopUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).NotifyGodLikeTopUsers(ctx, req.(*NotifyGodLikeTopUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_CheckMaskedPkRankEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckMaskedPkRankEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).CheckMaskedPkRankEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/CheckMaskedPkRankEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).CheckMaskedPkRankEntry(ctx, req.(*CheckMaskedPkRankEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedPKLive_MaskedPkGetConsumeTopN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MaskedPkGetConsumeTopNReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedPKLiveServer).MaskedPkGetConsumeTopN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masked_pk_live.MaskedPKLive/MaskedPkGetConsumeTopN",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedPKLiveServer).MaskedPkGetConsumeTopN(ctx, req.(*MaskedPkGetConsumeTopNReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MaskedPKLive_serviceDesc = grpc.ServiceDesc{
	ServiceName: "masked_pk_live.MaskedPKLive",
	HandlerType: (*MaskedPKLiveServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "StartLiveChannelMaskedPK",
			Handler:    _MaskedPKLive_StartLiveChannelMaskedPK_Handler,
		},
		{
			MethodName: "GiveUpLiveChannelMaskedPK",
			Handler:    _MaskedPKLive_GiveUpLiveChannelMaskedPK_Handler,
		},
		{
			MethodName: "CancelLiveChannelMaskedPK",
			Handler:    _MaskedPKLive_CancelLiveChannelMaskedPK_Handler,
		},
		{
			MethodName: "GetLiveChannelMaskedPKCurrConfWithUser",
			Handler:    _MaskedPKLive_GetLiveChannelMaskedPKCurrConfWithUser_Handler,
		},
		{
			MethodName: "GetLiveChannelMaskedPKCurrConf",
			Handler:    _MaskedPKLive_GetLiveChannelMaskedPKCurrConf_Handler,
		},
		{
			MethodName: "GetLiveChannelMaskedPKInfo",
			Handler:    _MaskedPKLive_GetLiveChannelMaskedPKInfo_Handler,
		},
		{
			MethodName: "GetAudienceRankList",
			Handler:    _MaskedPKLive_GetAudienceRankList_Handler,
		},
		{
			MethodName: "GetLiveChannelMaskedPKStatus",
			Handler:    _MaskedPKLive_GetLiveChannelMaskedPKStatus_Handler,
		},
		{
			MethodName: "BatchGetLiveChannelMaskedPKStatus",
			Handler:    _MaskedPKLive_BatchGetLiveChannelMaskedPKStatus_Handler,
		},
		{
			MethodName: "BatchAddUserToQualification",
			Handler:    _MaskedPKLive_BatchAddUserToQualification_Handler,
		},
		{
			MethodName: "BatchDelUserFromQualification",
			Handler:    _MaskedPKLive_BatchDelUserFromQualification_Handler,
		},
		{
			MethodName: "GetUserFromQualification",
			Handler:    _MaskedPKLive_GetUserFromQualification_Handler,
		},
		{
			MethodName: "IsUserHasQualification",
			Handler:    _MaskedPKLive_IsUserHasQualification_Handler,
		},
		{
			MethodName: "AddMaskedGameConfig",
			Handler:    _MaskedPKLive_AddMaskedGameConfig_Handler,
		},
		{
			MethodName: "DelMaskedGameConfig",
			Handler:    _MaskedPKLive_DelMaskedGameConfig_Handler,
		},
		{
			MethodName: "UpdateMaskedGameConfig",
			Handler:    _MaskedPKLive_UpdateMaskedGameConfig_Handler,
		},
		{
			MethodName: "GetAllMaskedGameConfig",
			Handler:    _MaskedPKLive_GetAllMaskedGameConfig_Handler,
		},
		{
			MethodName: "GetLastMaskedGameConfig",
			Handler:    _MaskedPKLive_GetLastMaskedGameConfig_Handler,
		},
		{
			MethodName: "BatchAddUserToWhiteList",
			Handler:    _MaskedPKLive_BatchAddUserToWhiteList_Handler,
		},
		{
			MethodName: "BatchDelUserFromWhiteList",
			Handler:    _MaskedPKLive_BatchDelUserFromWhiteList_Handler,
		},
		{
			MethodName: "GetAllWhiteList",
			Handler:    _MaskedPKLive_GetAllWhiteList_Handler,
		},
		{
			MethodName: "UpdateWhiteListConfig",
			Handler:    _MaskedPKLive_UpdateWhiteListConfig_Handler,
		},
		{
			MethodName: "GetWhiteListConfig",
			Handler:    _MaskedPKLive_GetWhiteListConfig_Handler,
		},
		{
			MethodName: "SetMonthGiftValue",
			Handler:    _MaskedPKLive_SetMonthGiftValue_Handler,
		},
		{
			MethodName: "TestPushQuickKillChange",
			Handler:    _MaskedPKLive_TestPushQuickKillChange_Handler,
		},
		{
			MethodName: "NotifyGodLikeTopUsers",
			Handler:    _MaskedPKLive_NotifyGodLikeTopUsers_Handler,
		},
		{
			MethodName: "CheckMaskedPkRankEntry",
			Handler:    _MaskedPKLive_CheckMaskedPkRankEntry_Handler,
		},
		{
			MethodName: "MaskedPkGetConsumeTopN",
			Handler:    _MaskedPKLive_MaskedPkGetConsumeTopN_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "masked-pk-live/masked-pk-live.proto",
}

func init() {
	proto.RegisterFile("masked-pk-live/masked-pk-live.proto", fileDescriptor_masked_pk_live_c95f011f20423a78)
}

var fileDescriptor_masked_pk_live_c95f011f20423a78 = []byte{
	// 3900 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x5b, 0xcd, 0x6f, 0x1b, 0x4b,
	0x72, 0x17, 0xf5, 0x45, 0xb2, 0x48, 0x4a, 0x54, 0xfb, 0x8b, 0xa2, 0xac, 0x67, 0x79, 0xfc, 0xb1,
	0xb2, 0xf7, 0xd9, 0x7e, 0x4f, 0x9b, 0x87, 0x6c, 0xbc, 0xc6, 0x26, 0xb2, 0x24, 0xcb, 0x82, 0x2d,
	0x3d, 0x2e, 0x25, 0xbf, 0x87, 0x0d, 0x36, 0x19, 0x8c, 0xc8, 0x16, 0xd9, 0xe1, 0x70, 0x66, 0x34,
	0x3d, 0x43, 0x59, 0x0f, 0x08, 0x10, 0x64, 0x81, 0x00, 0xb9, 0xee, 0x21, 0x40, 0x90, 0xd3, 0xfe,
	0x03, 0x41, 0x90, 0xd3, 0xe6, 0x4f, 0xc8, 0x29, 0x97, 0xbd, 0xe6, 0x9c, 0xbf, 0x20, 0xc7, 0x00,
	0x41, 0x55, 0xf7, 0xf0, 0x6b, 0x66, 0x28, 0xca, 0x6b, 0x60, 0x6f, 0xec, 0xea, 0xea, 0xaa, 0xea,
	0xea, 0xea, 0xea, 0xaa, 0xdf, 0x48, 0xf0, 0xa0, 0x6b, 0xc9, 0x0e, 0x6f, 0x3e, 0xf3, 0x3a, 0xcf,
	0x6c, 0xd1, 0xe3, 0x2f, 0x46, 0x87, 0xcf, 0x3d, 0xdf, 0x0d, 0x5c, 0xb6, 0xa4, 0xa8, 0xa6, 0xd7,
	0x31, 0x91, 0x6a, 0x48, 0x28, 0x7d, 0x90, 0xdc, 0xaf, 0xf9, 0xa2, 0x27, 0x6c, 0xde, 0xe2, 0xac,
	0x02, 0x59, 0xab, 0xd1, 0x70, 0x43, 0x27, 0xa8, 0x64, 0x36, 0x32, 0x9b, 0xf9, 0x7a, 0x34, 0x64,
	0x55, 0xc8, 0x39, 0xa2, 0xd1, 0x71, 0xac, 0x2e, 0xaf, 0xcc, 0xd2, 0x54, 0x7f, 0xcc, 0x18, 0xcc,
	0x07, 0x97, 0x1e, 0xaf, 0xcc, 0x6d, 0x64, 0x36, 0x4b, 0x75, 0xfa, 0x8d, 0x92, 0x5c, 0x2f, 0x10,
	0xae, 0x23, 0x2b, 0xf3, 0x1b, 0x99, 0xcd, 0x62, 0x3d, 0x1a, 0x1a, 0xff, 0x99, 0x81, 0x82, 0xd2,
	0xea, 0x9e, 0x09, 0x9b, 0xb3, 0x32, 0xcc, 0x85, 0xa2, 0x49, 0xfa, 0x4a, 0x75, 0xfc, 0x39, 0x6c,
	0xc5, 0x6c, 0xba, 0x15, 0x73, 0x63, 0x56, 0x3c, 0x80, 0x92, 0x66, 0x33, 0x2d, 0x5b, 0x58, 0x4a,
	0x6f, 0xbe, 0x5e, 0xd4, 0xc4, 0x6d, 0xa4, 0xa1, 0x32, 0xc9, 0x3f, 0x56, 0x16, 0x94, 0x32, 0xc9,
	0x3f, 0xb2, 0x9f, 0x41, 0xde, 0x8b, 0xf6, 0x5f, 0x59, 0xdc, 0xc8, 0x6c, 0x16, 0xb6, 0xd6, 0x9f,
	0x8f, 0xfa, 0xe9, 0xf9, 0x88, 0x93, 0xea, 0x03, 0x7e, 0xe3, 0xbf, 0xe7, 0xe0, 0xc6, 0x4e, 0xdb,
	0x72, 0x1c, 0x6e, 0x1f, 0xd2, 0x92, 0xda, 0xbb, 0x1d, 0xd7, 0x39, 0x63, 0xab, 0x90, 0x3b, 0xe5,
	0x2d, 0xe1, 0x98, 0x81, 0xd4, 0x1b, 0xcb, 0xd2, 0xf8, 0x44, 0xb2, 0x5b, 0xb0, 0xc8, 0x9d, 0x26,
	0x4e, 0xcc, 0xd2, 0xc4, 0x02, 0x77, 0x9a, 0x27, 0x92, 0xad, 0x41, 0xbe, 0xd1, 0x16, 0x9e, 0xe9,
	0xbb, 0x76, 0xe4, 0xc8, 0x1c, 0x12, 0xea, 0xae, 0x4d, 0x0e, 0xc6, 0xdf, 0xb4, 0xa3, 0x52, 0x9d,
	0x7e, 0xb3, 0x47, 0xb0, 0xd4, 0x70, 0x9d, 0x40, 0x38, 0x21, 0x37, 0xbb, 0x56, 0xd0, 0x68, 0xeb,
	0x4d, 0x95, 0x22, 0xea, 0x21, 0x12, 0xd9, 0x53, 0x58, 0xb1, 0xc2, 0xc0, 0x55, 0x2c, 0xc2, 0x69,
	0x99, 0x0d, 0x27, 0xa0, 0x6d, 0x96, 0xea, 0xcb, 0x38, 0x71, 0xa8, 0xe9, 0x3b, 0x4e, 0x80, 0x56,
	0xff, 0x4d, 0xd8, 0xf5, 0xcc, 0xd0, 0xb7, 0x2b, 0x59, 0xe5, 0x78, 0x1c, 0x7f, 0xf0, 0x6d, 0x76,
	0x07, 0xb2, 0x0d, 0xd7, 0x39, 0x33, 0x45, 0xb3, 0x92, 0xa3, 0xc5, 0x8b, 0x38, 0x3c, 0x68, 0xb2,
	0xc7, 0xb0, 0x4c, 0x76, 0x3b, 0x6e, 0x60, 0x72, 0xc7, 0x0d, 0x5b, 0xed, 0x4a, 0x7e, 0x23, 0xb3,
	0x99, 0xab, 0x97, 0x90, 0x7c, 0xe4, 0x06, 0x7b, 0x44, 0xc4, 0xfd, 0x49, 0xee, 0xf7, 0xb8, 0x6f,
	0x3a, 0xb2, 0x02, 0x1b, 0x99, 0xcd, 0xb9, 0x7a, 0x4e, 0x11, 0x8e, 0x24, 0xbb, 0x07, 0x85, 0xa6,
	0xe8, 0x89, 0x26, 0x37, 0x29, 0x8e, 0x0a, 0xa4, 0x01, 0x14, 0xe9, 0x04, 0xa3, 0xe9, 0x3e, 0x14,
	0x43, 0xc9, 0xcd, 0x53, 0xab, 0xd1, 0xf1, 0xac, 0x46, 0xa7, 0x52, 0x24, 0x8e, 0x42, 0x28, 0xf9,
	0x6b, 0x4d, 0x62, 0xcf, 0xe0, 0x86, 0x72, 0x20, 0x6f, 0x70, 0xd1, 0xe3, 0xa6, 0x76, 0x72, 0x89,
	0x38, 0xcb, 0xe4, 0x4a, 0x35, 0xb3, 0x87, 0xfe, 0x36, 0x1e, 0x42, 0x6e, 0x27, 0x72, 0x2f, 0xc0,
	0xe2, 0x91, 0x8b, 0xa3, 0xf2, 0x0c, 0x2b, 0x42, 0xee, 0xad, 0xd5, 0xe3, 0x34, 0xca, 0x18, 0xbf,
	0x5d, 0x80, 0x5b, 0x63, 0xe7, 0x7b, 0x1c, 0x58, 0x41, 0x28, 0xd1, 0x21, 0x2d, 0xab, 0xcb, 0xcd,
	0x7e, 0xe4, 0x2e, 0xe2, 0xf0, 0xa0, 0xc9, 0xd6, 0x01, 0x1a, 0x6a, 0x05, 0xce, 0xa9, 0x33, 0xce,
	0x6b, 0xca, 0x41, 0x93, 0xce, 0x39, 0xf4, 0x7d, 0x93, 0xce, 0x33, 0x3a, 0xe7, 0xd0, 0xf7, 0x51,
	0x21, 0x0a, 0xbd, 0x10, 0x0e, 0x1d, 0x91, 0x3a, 0xea, 0xc5, 0x0b, 0xe1, 0xe0, 0xc9, 0xdc, 0x86,
	0x45, 0x49, 0x7a, 0xf5, 0x21, 0xeb, 0x11, 0x4a, 0xb3, 0x5d, 0x29, 0x95, 0x34, 0x75, 0xaa, 0x39,
	0x24, 0x90, 0xb4, 0x87, 0xb0, 0xd4, 0x9f, 0x34, 0x9b, 0x5c, 0x36, 0xf4, 0xa1, 0x16, 0x23, 0x8e,
	0x5d, 0x2e, 0x1b, 0xcc, 0x80, 0x92, 0x12, 0x16, 0x79, 0x4c, 0x9d, 0x6f, 0x41, 0x11, 0xc9, 0x59,
	0x78, 0x3e, 0x9a, 0x87, 0xc4, 0xe4, 0x49, 0x0c, 0x28, 0x12, 0x09, 0x79, 0x0c, 0xcb, 0x3e, 0x97,
	0x81, 0xd9, 0xb0, 0x9c, 0x06, 0xb7, 0x69, 0x03, 0xa0, 0xa2, 0x11, 0xc9, 0x3b, 0x44, 0xc5, 0x7d,
	0x44, 0x7c, 0x3e, 0xef, 0xe1, 0x19, 0x21, 0x5f, 0x61, 0xc0, 0x57, 0x27, 0x2a, 0xf2, 0xdd, 0x83,
	0x82, 0x62, 0x69, 0x12, 0x8f, 0x3a, 0x6e, 0xd0, 0x24, 0x64, 0x78, 0x05, 0x59, 0xaf, 0x63, 0x62,
	0x0c, 0xd2, 0x09, 0x17, 0xb6, 0x1e, 0x8c, 0xdf, 0xd9, 0x84, 0x6b, 0x59, 0x5f, 0xf4, 0x3a, 0x74,
	0x3d, 0x47, 0x82, 0x71, 0x69, 0x2c, 0x18, 0x57, 0x21, 0xa7, 0xdc, 0xe6, 0x04, 0x95, 0x65, 0x75,
	0x77, 0xc9, 0x61, 0x4e, 0x60, 0xfc, 0x26, 0x03, 0x8b, 0xfa, 0xfc, 0x6f, 0x42, 0xf9, 0xc8, 0x0d,
	0x6a, 0x96, 0x1f, 0x88, 0x86, 0xf0, 0xac, 0x40, 0x38, 0xad, 0xf2, 0x0c, 0x5b, 0x86, 0xc2, 0x91,
	0x1b, 0x44, 0x77, 0xaa, 0x9c, 0x41, 0x42, 0xcd, 0xe7, 0x7d, 0xc2, 0x2c, 0x63, 0xb0, 0xb4, 0x1d,
	0x06, 0xee, 0x81, 0xd3, 0xa7, 0xcd, 0xa1, 0xac, 0xed, 0x46, 0x20, 0x7a, 0x7c, 0x88, 0x3a, 0xcf,
	0x0a, 0x90, 0x3d, 0x70, 0x6a, 0xef, 0x70, 0xb0, 0xc0, 0x96, 0x00, 0x0e, 0x1c, 0xf2, 0x0f, 0x8e,
	0x17, 0x59, 0x1e, 0x16, 0x0e, 0xe4, 0xb7, 0x61, 0x50, 0xce, 0x1a, 0x17, 0x70, 0x7b, 0x6c, 0xaf,
	0x75, 0xcb, 0xe9, 0x1c, 0xf2, 0x6e, 0x42, 0x66, 0xbd, 0x09, 0x0b, 0xb2, 0xe1, 0xfa, 0x3c, 0xca,
	0x3d, 0x34, 0x60, 0xdf, 0x40, 0xd6, 0x53, 0xc9, 0x98, 0x22, 0xb2, 0xb0, 0xb5, 0x96, 0x9c, 0x00,
	0x89, 0xa5, 0x1e, 0xf1, 0x1a, 0xff, 0x1a, 0x4f, 0x7e, 0x07, 0xce, 0x99, 0x3b, 0x76, 0x03, 0x32,
	0x13, 0x6f, 0xc0, 0x6c, 0xfa, 0x0d, 0x98, 0x1b, 0xb9, 0x01, 0xeb, 0x00, 0x43, 0x41, 0xa3, 0x42,
	0x3d, 0xef, 0xf7, 0x03, 0xa6, 0xbf, 0xb1, 0xec, 0xf0, 0xc6, 0xa2, 0xeb, 0x41, 0x51, 0x9b, 0x53,
	0xef, 0x05, 0x12, 0x28, 0x66, 0x8f, 0x60, 0x39, 0x70, 0x3d, 0xd3, 0x72, 0x1a, 0x6d, 0xd7, 0x37,
	0x6d, 0x21, 0x83, 0x4a, 0x7e, 0x63, 0x6e, 0xb3, 0xb0, 0xf5, 0xf8, 0x8a, 0x50, 0xd2, 0xee, 0xad,
	0x97, 0x02, 0xd7, 0xdb, 0xa6, 0xd5, 0xef, 0x85, 0x0c, 0x46, 0xef, 0x22, 0x8c, 0xdd, 0xc5, 0x3d,
	0x58, 0x3e, 0x0f, 0x45, 0xa3, 0x63, 0x76, 0x84, 0x6d, 0x9b, 0xc2, 0x39, 0x73, 0x29, 0xf0, 0x13,
	0xde, 0x9a, 0x5f, 0x20, 0xdb, 0x3b, 0x61, 0xdb, 0xe8, 0xcb, 0x7a, 0xe9, 0x7c, 0x78, 0xc8, 0x5e,
	0x41, 0xd1, 0xe3, 0x56, 0x07, 0x99, 0x49, 0x46, 0x91, 0x64, 0x54, 0xc7, 0x65, 0xd4, 0xb8, 0xd5,
	0xa9, 0x75, 0x48, 0x00, 0x78, 0xfd, 0xdf, 0xc6, 0x3f, 0xcd, 0x42, 0x69, 0x44, 0x3c, 0xfb, 0x09,
	0xdc, 0x1e, 0x32, 0x0b, 0xdd, 0x64, 0x7a, 0x3e, 0x3f, 0x13, 0x1f, 0xf5, 0xf3, 0x7f, 0xa3, 0xaf,
	0x1e, 0x5d, 0x56, 0xa3, 0x29, 0xbc, 0xc4, 0x63, 0x8b, 0xf4, 0x33, 0x5d, 0x1a, 0xe1, 0x66, 0x4f,
	0x60, 0x65, 0x88, 0x4f, 0x67, 0x17, 0x75, 0xaa, 0x4b, 0x7d, 0x4e, 0x95, 0x60, 0x9e, 0xc0, 0x0a,
	0x77, 0xac, 0x53, 0x9b, 0x9b, 0x5d, 0xe1, 0xe0, 0x36, 0x24, 0x6f, 0xe8, 0x14, 0xb8, 0xa4, 0x26,
	0x0e, 0x85, 0x53, 0xeb, 0x1c, 0xf3, 0xc6, 0x30, 0xab, 0xf5, 0x31, 0x62, 0x5d, 0x18, 0x61, 0xb5,
	0x3e, 0x2a, 0xd6, 0x1f, 0xc1, 0x72, 0xc3, 0x75, 0x9a, 0x02, 0xeb, 0x0e, 0xb3, 0x67, 0xd9, 0x21,
	0xd7, 0x81, 0xb3, 0xd4, 0x27, 0x7f, 0x87, 0x54, 0xe3, 0x09, 0xc0, 0xc0, 0x65, 0x78, 0x90, 0xe4,
	0x64, 0xda, 0x99, 0xf2, 0x43, 0x0e, 0x09, 0xb8, 0x29, 0xe3, 0xf7, 0xb3, 0xb1, 0x17, 0xe1, 0xb5,
	0x15, 0x04, 0x36, 0x67, 0x3f, 0x87, 0x5c, 0x97, 0x77, 0x55, 0x20, 0x65, 0x28, 0x90, 0xae, 0xca,
	0x49, 0x74, 0x40, 0xd9, 0x2e, 0xef, 0x52, 0xfc, 0x54, 0x21, 0xef, 0x75, 0xcc, 0x91, 0xda, 0x20,
	0xeb, 0x75, 0xf6, 0xae, 0xae, 0x0e, 0x46, 0xb2, 0xd9, 0xfc, 0x58, 0x36, 0x33, 0xa0, 0xd4, 0xb3,
	0x6c, 0x41, 0x36, 0xd0, 0x86, 0x16, 0x68, 0x43, 0x05, 0x22, 0xd6, 0x68, 0x4f, 0x98, 0xf1, 0x22,
	0x1e, 0x72, 0x50, 0xae, 0x9e, 0xd5, 0xd3, 0xec, 0x06, 0x2c, 0x60, 0xac, 0x35, 0xf5, 0xbd, 0x9a,
	0xf7, 0x3a, 0xea, 0x89, 0x93, 0xe1, 0xa9, 0xe9, 0xb5, 0x7d, 0x4b, 0x72, 0xfd, 0x5e, 0xe4, 0x65,
	0x78, 0x5a, 0x23, 0x82, 0xb1, 0x05, 0xf9, 0xe3, 0x68, 0x80, 0x6f, 0xeb, 0x8e, 0xdb, 0xed, 0xba,
	0x4e, 0x79, 0x86, 0x95, 0x20, 0xdf, 0x0f, 0xbf, 0x72, 0x06, 0xa7, 0x94, 0xd7, 0xcb, 0xb3, 0x98,
	0x59, 0xc7, 0xdd, 0xaa, 0x5e, 0x83, 0xab, 0xb2, 0x09, 0x83, 0xf9, 0x96, 0x6b, 0xd9, 0xda, 0x61,
	0xf4, 0x9b, 0xca, 0xa5, 0xd0, 0xf7, 0xa3, 0x7a, 0x14, 0x7f, 0x0f, 0x95, 0x5d, 0xf3, 0x63, 0x65,
	0xd7, 0xc0, 0x77, 0x0b, 0xa3, 0xbe, 0x33, 0x6a, 0xb0, 0xbe, 0xcf, 0x83, 0xf7, 0x98, 0x62, 0x12,
	0x0e, 0x8e, 0x9f, 0x27, 0x24, 0xd8, 0xc9, 0xaf, 0xbf, 0xf1, 0x7f, 0x19, 0xf8, 0x62, 0x92, 0x48,
	0xe9, 0xb1, 0x37, 0xfd, 0xb7, 0x96, 0x6e, 0x78, 0x86, 0x6e, 0xf8, 0xa3, 0x2b, 0x22, 0x49, 0x3d,
	0x4a, 0xd1, 0x93, 0x4c, 0x51, 0xfc, 0x0e, 0x96, 0xbc, 0x8e, 0x79, 0x4a, 0xb1, 0xa9, 0x44, 0xcd,
	0x4e, 0x25, 0x4a, 0x45, 0x73, 0xbd, 0xe8, 0x75, 0xd4, 0x2f, 0x12, 0xf6, 0x26, 0x7a, 0x8f, 0x95,
	0xa4, 0xb9, 0xa9, 0x24, 0xa9, 0x03, 0x8c, 0x9e, 0x6d, 0xca, 0x40, 0x47, 0xb0, 0x76, 0x1c, 0x58,
	0x7e, 0x92, 0x03, 0x3e, 0xc9, 0x9f, 0x5f, 0xc0, 0xdd, 0x74, 0x79, 0xd2, 0x33, 0xda, 0x70, 0x77,
	0x5f, 0xf4, 0xf8, 0x07, 0xef, 0x33, 0x29, 0x1c, 0xae, 0x83, 0xe7, 0x86, 0xeb, 0x60, 0xe3, 0x1e,
	0xac, 0x4f, 0xd0, 0x24, 0x3d, 0xe3, 0x5b, 0xb8, 0xab, 0xea, 0xa0, 0xcf, 0xb5, 0xf7, 0x7b, 0xb0,
	0x3e, 0x41, 0xa0, 0xf4, 0x8c, 0x3a, 0xdc, 0x4b, 0x8e, 0x35, 0x1d, 0x2d, 0x9f, 0xa2, 0xf4, 0x25,
	0x6c, 0x4c, 0x96, 0x29, 0xbd, 0xa1, 0x62, 0x35, 0x33, 0x5c, 0xac, 0x1a, 0x97, 0xf0, 0xf0, 0x35,
	0x96, 0x37, 0xd7, 0x37, 0x6a, 0xa8, 0xd8, 0x9e, 0x1d, 0x29, 0xb6, 0xa9, 0xfb, 0x88, 0xac, 0x55,
	0xa9, 0x77, 0x6e, 0x63, 0x8e, 0xba, 0xa0, 0xc8, 0x64, 0xcc, 0xad, 0xc6, 0xff, 0x64, 0xe0, 0xd1,
	0x14, 0xba, 0xa5, 0xc7, 0x2e, 0x81, 0x45, 0x12, 0xf5, 0x35, 0xec, 0x5a, 0x9e, 0xce, 0xe7, 0xef,
	0xc6, 0x03, 0x7e, 0x2a, 0x91, 0xd1, 0xb5, 0x50, 0x94, 0x43, 0xcb, 0xdb, 0x73, 0x02, 0xff, 0x12,
	0x5b, 0x92, 0x51, 0x72, 0x75, 0xa7, 0x9f, 0x02, 0x47, 0x59, 0xd1, 0x21, 0x1d, 0x7e, 0x19, 0x39,
	0xa4, 0xc3, 0x2f, 0xb1, 0xdc, 0x51, 0xef, 0x99, 0xae, 0xe3, 0x68, 0xf0, 0x72, 0xf6, 0xa7, 0x19,
	0xe3, 0x57, 0xf0, 0x24, 0xd9, 0xa0, 0x1d, 0x2c, 0xb1, 0x5c, 0xe7, 0xec, 0x7b, 0x11, 0xb4, 0xb1,
	0x9c, 0xfb, 0xa4, 0xe3, 0xff, 0x75, 0x06, 0x9e, 0x4e, 0x2b, 0x5e, 0x7a, 0xec, 0x4f, 0x61, 0x9e,
	0x4a, 0xf4, 0xcc, 0xf4, 0x25, 0x3a, 0x2d, 0x60, 0x77, 0x01, 0x84, 0x34, 0x5b, 0x98, 0x70, 0x42,
	0x55, 0x24, 0xe6, 0xea, 0x39, 0x21, 0xd5, 0x0d, 0x33, 0x1e, 0xc0, 0xfd, 0xc9, 0x46, 0xd4, 0xf9,
	0xb9, 0xf1, 0x57, 0x60, 0x5c, 0xc5, 0xf4, 0x07, 0x58, 0x68, 0xfc, 0x5b, 0x06, 0xee, 0x8e, 0xcd,
	0xaa, 0x5a, 0xf0, 0xba, 0xc5, 0xf7, 0x4d, 0x58, 0x38, 0x75, 0x9d, 0x30, 0xaa, 0x8c, 0xd4, 0x80,
	0xd5, 0x61, 0x85, 0x8a, 0xd3, 0xb0, 0x29, 0xb8, 0xd3, 0xe0, 0x2a, 0xb4, 0xb3, 0xd7, 0x2a, 0x4f,
	0xb1, 0xba, 0xdd, 0xd6, 0xeb, 0xe9, 0x12, 0x84, 0x70, 0x7b, 0x9f, 0x07, 0x11, 0x09, 0xd9, 0x90,
	0xfc, 0x49, 0x69, 0x10, 0x8d, 0xe6, 0x2d, 0xe1, 0xf4, 0x8d, 0xc6, 0x01, 0x52, 0x6d, 0xd1, 0x15,
	0x51, 0xf3, 0xaa, 0x06, 0xc6, 0x5f, 0xc3, 0x9d, 0x44, 0xb5, 0xd2, 0x63, 0x3b, 0x90, 0xf7, 0x2d,
	0xa7, 0xa3, 0x76, 0x37, 0x7b, 0xad, 0xdd, 0xe5, 0x7c, 0x2d, 0xc8, 0x68, 0xc1, 0x17, 0x74, 0x0f,
	0xb7, 0x9b, 0x4d, 0x0c, 0xbc, 0x13, 0xf7, 0x17, 0xa1, 0x65, 0x8b, 0x33, 0xd1, 0xb0, 0xb0, 0xc0,
	0xc3, 0xed, 0xed, 0xc1, 0xd2, 0xf9, 0x30, 0x4d, 0xea, 0xfb, 0x9c, 0x50, 0x7b, 0x0f, 0xaf, 0x1c,
	0x5b, 0x64, 0xfc, 0x17, 0x95, 0xcf, 0x43, 0xa4, 0xab, 0x6a, 0x13, 0xed, 0xd6, 0xd9, 0x81, 0x5b,
	0x1f, 0x40, 0x29, 0x94, 0x58, 0x6c, 0x8c, 0x82, 0x58, 0x45, 0x24, 0x1e, 0x45, 0x40, 0xd6, 0x2a,
	0xe4, 0x88, 0xc9, 0x6a, 0x71, 0xed, 0xc9, 0x2c, 0x8e, 0xb7, 0x5b, 0x83, 0xa9, 0x08, 0xc3, 0xca,
	0xab, 0xa9, 0x63, 0xfe, 0x11, 0x6d, 0x69, 0x85, 0xc2, 0x6e, 0x9a, 0x24, 0x77, 0x91, 0x26, 0xf3,
	0x44, 0x39, 0x42, 0xa1, 0xf7, 0xa0, 0x10, 0x7a, 0x4d, 0x2b, 0xe0, 0x66, 0x20, 0xba, 0x51, 0x9b,
	0x04, 0x8a, 0x74, 0x22, 0x34, 0x88, 0x17, 0x68, 0x78, 0x27, 0x5f, 0xa7, 0xdf, 0x28, 0xb3, 0x29,
	0xa4, 0x67, 0x5b, 0x97, 0xb8, 0xbf, 0xbc, 0xda, 0x9f, 0xa6, 0x1c, 0x34, 0xd1, 0x1a, 0xa5, 0x52,
	0x34, 0x75, 0xc3, 0x93, 0xa5, 0xf1, 0x41, 0x93, 0x3d, 0x84, 0x25, 0x35, 0x25, 0xdb, 0xae, 0x1f,
	0x20, 0x83, 0xea, 0xf3, 0x8b, 0x44, 0x3d, 0x46, 0xe2, 0x41, 0xd3, 0xf8, 0x39, 0xdc, 0x9b, 0x78,
	0x74, 0xd2, 0xc3, 0x02, 0xed, 0xcc, 0x12, 0xb6, 0x49, 0xb6, 0xe1, 0xb1, 0xe5, 0xeb, 0x39, 0x24,
	0x9c, 0x04, 0xa2, 0x69, 0x3c, 0x83, 0xd5, 0x03, 0x89, 0x2b, 0xdf, 0x5a, 0x32, 0x76, 0xea, 0xb1,
	0xa0, 0x36, 0xb6, 0xa0, 0x9a, 0xc6, 0x2e, 0x3d, 0x8c, 0x5e, 0xfe, 0x51, 0x15, 0xef, 0x98, 0x6e,
	0xd4, 0xc0, 0xa8, 0xc3, 0xed, 0xed, 0x66, 0x53, 0x45, 0xdf, 0xbe, 0xd5, 0xe5, 0x98, 0x02, 0x44,
	0x0b, 0xe5, 0xff, 0x14, 0xe8, 0xed, 0x17, 0x2d, 0x9d, 0x3c, 0x36, 0xc6, 0xa3, 0x29, 0xb6, 0x48,
	0xf3, 0x1b, 0xab, 0x70, 0x27, 0x51, 0xa6, 0xf4, 0x8c, 0xdf, 0x2d, 0x40, 0x79, 0x7c, 0x02, 0x8f,
	0x41, 0xa3, 0x89, 0x78, 0x74, 0x3a, 0xcc, 0x14, 0x9e, 0x28, 0x54, 0xbc, 0x50, 0x69, 0x2b, 0xba,
	0x51, 0x6a, 0xc9, 0x62, 0x71, 0xab, 0xa7, 0xa8, 0x6f, 0xb0, 0x7a, 0x2d, 0x7d, 0x55, 0xb3, 0x38,
	0xde, 0xee, 0xb5, 0x54, 0xec, 0x0a, 0xcf, 0x54, 0x38, 0xeb, 0x7c, 0x14, 0xbb, 0xc2, 0xdb, 0x21,
	0xa4, 0x75, 0x0d, 0xf2, 0x17, 0x22, 0x68, 0x9b, 0x4d, 0xdf, 0xba, 0xd0, 0xed, 0x55, 0x0e, 0x09,
	0xbb, 0xbe, 0x75, 0xc1, 0xee, 0x43, 0xd1, 0x6a, 0x04, 0xa2, 0x27, 0x82, 0x4b, 0x02, 0x0b, 0x55,
	0xb4, 0x15, 0x22, 0xda, 0x07, 0xdf, 0x66, 0x3f, 0x1b, 0x85, 0xf4, 0x30, 0xde, 0x96, 0xe2, 0x8d,
	0xea, 0x6e, 0x1f, 0xe2, 0x1b, 0x87, 0xfb, 0xf4, 0x62, 0x65, 0x9d, 0x86, 0xa4, 0x14, 0x4d, 0xd9,
	0xd7, 0x47, 0x88, 0x54, 0xbf, 0x9d, 0x1f, 0x46, 0x88, 0xa8, 0xe3, 0xbe, 0x0f, 0x45, 0xcf, 0x77,
	0x95, 0x90, 0x41, 0x47, 0x5e, 0xd0, 0xb4, 0x9d, 0x64, 0x08, 0xb5, 0x90, 0x04, 0xa1, 0x0e, 0x55,
	0x1f, 0xc5, 0x91, 0xea, 0xc3, 0x80, 0x52, 0x53, 0x70, 0xd3, 0x0d, 0x03, 0x6d, 0x67, 0x29, 0xb2,
	0x93, 0x7f, 0x1b, 0x06, 0xca, 0xce, 0x67, 0xc0, 0x1c, 0xce, 0x9b, 0xe6, 0x48, 0x2e, 0x21, 0xcc,
	0xa9, 0x54, 0x5f, 0xc1, 0x99, 0xd1, 0x8c, 0x72, 0x1f, 0x8a, 0x7a, 0x5b, 0xc1, 0x29, 0xb7, 0x1c,
	0x0d, 0x40, 0xe9, 0xad, 0x9e, 0x20, 0x09, 0xb5, 0x36, 0x2c, 0xc7, 0x3c, 0xe5, 0x66, 0xd7, 0x6d,
	0x8a, 0xb3, 0xcb, 0x4a, 0x99, 0xe2, 0xb5, 0xd0, 0xb0, 0x9c, 0xd7, 0xfc, 0x90, 0x48, 0x31, 0xbc,
	0x74, 0x25, 0x8e, 0x97, 0xae, 0x03, 0x0c, 0xba, 0xf3, 0x0a, 0x53, 0xe7, 0xdf, 0x6f, 0xcb, 0xd9,
	0x57, 0x70, 0x73, 0xd4, 0x37, 0x7a, 0x8b, 0x37, 0x88, 0x91, 0x8d, 0x78, 0x88, 0x76, 0x6a, 0x7c,
	0x0d, 0xb7, 0x77, 0xa3, 0x3c, 0x3d, 0x7a, 0x53, 0xd2, 0xb0, 0x52, 0xbc, 0x08, 0x89, 0x4b, 0xa4,
	0x67, 0x3c, 0x87, 0xea, 0x68, 0x6a, 0xf8, 0xbe, 0x2d, 0x02, 0x1e, 0x7b, 0xb0, 0xe6, 0xa2, 0xbb,
	0xfd, 0x12, 0xd6, 0x52, 0xf9, 0xaf, 0x4a, 0x23, 0xdf, 0xc2, 0x5d, 0x5a, 0xbb, 0xcb, 0x6d, 0x5c,
	0xfb, 0xc6, 0x77, 0xbb, 0x93, 0xb5, 0x51, 0x62, 0xe4, 0x36, 0x0f, 0xb8, 0x69, 0xd9, 0x51, 0xef,
	0x99, 0x57, 0x94, 0x6d, 0xdb, 0x36, 0x5e, 0xc1, 0xfa, 0x04, 0x81, 0x57, 0x99, 0x73, 0x13, 0x18,
	0x3e, 0x98, 0xb6, 0x3d, 0x6c, 0x84, 0xf1, 0x16, 0x6e, 0xc4, 0xa8, 0xd2, 0x63, 0x5f, 0xc3, 0xfc,
	0x10, 0xe2, 0x70, 0xc5, 0x8b, 0x46, 0xac, 0xc6, 0x31, 0x6c, 0x8c, 0x5b, 0x97, 0x9e, 0x3c, 0xa7,
	0xdd, 0xf2, 0x5f, 0xc0, 0xfd, 0x2b, 0x84, 0x5e, 0xb5, 0xed, 0x5f, 0xc2, 0xda, 0x3e, 0x0f, 0x52,
	0x2d, 0x62, 0x30, 0xef, 0xe1, 0x8b, 0x98, 0xd1, 0x40, 0x84, 0xd5, 0xa2, 0xda, 0x69, 0xf0, 0x99,
	0xa8, 0x54, 0x57, 0x83, 0xc8, 0xf6, 0xb9, 0x41, 0xe2, 0xff, 0xfb, 0x0c, 0xdc, 0x4d, 0x97, 0x2d,
	0xbd, 0xcf, 0x54, 0x21, 0x10, 0x9a, 0x10, 0x76, 0xcd, 0x61, 0x9b, 0x72, 0x32, 0xec, 0xaa, 0xfb,
	0xf1, 0x2f, 0x19, 0xa8, 0x7c, 0xa0, 0xf7, 0xb6, 0x7f, 0x82, 0x83, 0x2b, 0xf2, 0x10, 0x96, 0xf0,
	0xc2, 0x5e, 0xe0, 0x4c, 0x04, 0x21, 0xd1, 0x7b, 0x19, 0xca, 0x01, 0x3b, 0x7b, 0x05, 0x6b, 0x96,
	0x6d, 0xbb, 0x17, 0xfa, 0x46, 0x52, 0x82, 0x96, 0x78, 0xb3, 0xe8, 0x55, 0xd5, 0x1a, 0xef, 0x10,
	0x0b, 0x5d, 0x4c, 0x2c, 0xc6, 0x8f, 0xad, 0x2e, 0xdf, 0xc7, 0x69, 0xb4, 0x0e, 0x75, 0x78, 0xbe,
	0x70, 0x23, 0x6c, 0x04, 0xab, 0x89, 0x1a, 0x8e, 0x8d, 0x35, 0x58, 0x4d, 0x31, 0x4e, 0x7a, 0xc6,
	0x1d, 0xb8, 0xb5, 0xcf, 0x83, 0xb8, 0xd9, 0xc6, 0x3f, 0x67, 0xa8, 0xa6, 0x4c, 0x58, 0xf3, 0xc7,
	0xdf, 0xd1, 0x3b, 0xb8, 0x79, 0xcc, 0x83, 0x43, 0xd7, 0x09, 0xda, 0xfb, 0xe2, 0x2c, 0x20, 0xa4,
	0x0f, 0x5d, 0x7d, 0x45, 0xd1, 0x96, 0xd8, 0x5a, 0xa1, 0x07, 0x12, 0x84, 0x49, 0xcf, 0xf8, 0x10,
	0xf9, 0xed, 0xf3, 0x96, 0x08, 0x77, 0xa1, 0x9a, 0x26, 0x56, 0x7a, 0xc6, 0x4b, 0x58, 0x55, 0xb9,
	0x20, 0x49, 0x29, 0x16, 0x82, 0x3c, 0x30, 0xf9, 0x47, 0x4f, 0xf8, 0x5c, 0x17, 0x33, 0xf9, 0x16,
	0x0f, 0xf6, 0x88, 0x60, 0x7c, 0x07, 0xd5, 0xb4, 0xb5, 0xd2, 0x1b, 0xb1, 0x78, 0xee, 0xba, 0x16,
	0x63, 0xbf, 0x65, 0xc9, 0x20, 0xc1, 0x28, 0xe3, 0x7b, 0xba, 0xdc, 0xc9, 0xb3, 0x63, 0x6a, 0xaf,
	0xe7, 0xa8, 0xdf, 0x67, 0xa0, 0x7a, 0xc2, 0x65, 0x50, 0x0b, 0x65, 0xbb, 0x0f, 0x2e, 0x62, 0xcf,
	0xd0, 0x8a, 0x0e, 0x9b, 0xf7, 0xb8, 0x13, 0xa8, 0x2a, 0x44, 0x1f, 0x36, 0x51, 0xa8, 0xd0, 0xf8,
	0x12, 0x58, 0xe0, 0x8b, 0x56, 0x8b, 0xfb, 0x66, 0xac, 0xdd, 0x29, 0xeb, 0x99, 0x9d, 0x7e, 0x68,
	0x3c, 0x86, 0x65, 0x82, 0xb2, 0x87, 0x58, 0x55, 0xd0, 0x95, 0x3a, 0x5a, 0xa9, 0xea, 0x92, 0xb7,
	0x21, 0xbf, 0xd7, 0x57, 0x51, 0x80, 0xec, 0x07, 0xa7, 0xe3, 0xb8, 0x17, 0x4e, 0x79, 0x86, 0xdd,
	0x84, 0xf2, 0x89, 0x92, 0x3a, 0x0c, 0x84, 0xae, 0x40, 0xe9, 0x38, 0x70, 0xbd, 0x01, 0x69, 0xd6,
	0x58, 0x87, 0xb5, 0xd4, 0x5d, 0x49, 0xcf, 0x38, 0x87, 0xe5, 0x7d, 0xb7, 0xf9, 0x5e, 0x74, 0xf8,
	0x89, 0xeb, 0x61, 0x5a, 0x93, 0x09, 0x3d, 0x1c, 0x83, 0x79, 0x6c, 0x92, 0x22, 0x68, 0x14, 0x7f,
	0x33, 0x03, 0x8a, 0xf8, 0x74, 0xfb, 0xe2, 0x34, 0xa4, 0x42, 0x44, 0xd9, 0x3f, 0x42, 0x1b, 0x64,
	0xd5, 0xf9, 0xa1, 0xac, 0x6a, 0xfc, 0x47, 0x06, 0x2a, 0x47, 0x6e, 0x20, 0xce, 0x2e, 0xc7, 0x34,
	0x4f, 0x7a, 0xe1, 0xd9, 0x37, 0xb0, 0x80, 0x0d, 0x8a, 0xd4, 0xdd, 0xdd, 0xbd, 0xf1, 0x73, 0x1d,
	0x97, 0xa5, 0xb8, 0xb1, 0x7e, 0x51, 0xf1, 0xab, 0xcb, 0x5a, 0x65, 0x66, 0x41, 0xd3, 0xa8, 0xb4,
	0x7d, 0x0a, 0x2b, 0xfd, 0x1a, 0xb4, 0x5f, 0xfe, 0xce, 0xeb, 0x0f, 0xdb, 0x7a, 0x62, 0x4f, 0x95,
	0xc1, 0x98, 0xdc, 0x52, 0x4c, 0x97, 0x1e, 0x4e, 0xee, 0xb4, 0x79, 0xa3, 0xa3, 0x3b, 0xcc, 0x0e,
	0x76, 0x98, 0x0a, 0xa2, 0xe1, 0xe7, 0xc6, 0x9f, 0x43, 0x35, 0x6d, 0x52, 0x52, 0x8d, 0xc9, 0x71,
	0x60, 0xaa, 0x0f, 0x0f, 0xfa, 0xb2, 0x15, 0x88, 0xb6, 0x47, 0x24, 0xe3, 0x15, 0xac, 0x50, 0x3d,
	0x62, 0x09, 0xdb, 0x16, 0xad, 0x76, 0x40, 0x70, 0xea, 0x2d, 0x58, 0x3c, 0x15, 0x3f, 0x0c, 0xbc,
	0xb5, 0x70, 0x2a, 0x7e, 0x50, 0xfd, 0xa2, 0x13, 0x76, 0xa3, 0x7e, 0xd1, 0x09, 0xbb, 0xc6, 0xef,
	0xe6, 0xe1, 0xc6, 0x10, 0xf8, 0x20, 0xc3, 0xae, 0xc2, 0x63, 0xaf, 0xf3, 0x37, 0x13, 0x7d, 0xd8,
	0x61, 0x6e, 0x18, 0x76, 0xc0, 0xe0, 0x10, 0x8d, 0x76, 0xf4, 0x27, 0x05, 0xf8, 0x9b, 0x0e, 0xbe,
	0x6d, 0xf9, 0x5d, 0x5d, 0xef, 0xab, 0xc1, 0xc8, 0xdf, 0x5c, 0x2c, 0x8e, 0xfd, 0xcd, 0xc5, 0x23,
	0x58, 0x72, 0xdc, 0x53, 0x61, 0xe3, 0x21, 0xd8, 0xbc, 0xc7, 0x6d, 0xdd, 0x58, 0x96, 0x22, 0xea,
	0x7b, 0x24, 0xb2, 0x4d, 0x28, 0xb7, 0x7c, 0x37, 0xf4, 0xcc, 0x33, 0xcb, 0x91, 0x9a, 0x51, 0xd5,
	0xf4, 0x4b, 0x44, 0x7f, 0x63, 0x39, 0x52, 0x71, 0x3e, 0x85, 0x95, 0xe8, 0x76, 0xd1, 0xc7, 0x14,
	0x62, 0x55, 0xc5, 0x7d, 0x84, 0xf4, 0x1d, 0xf2, 0xae, 0xe2, 0xc5, 0x44, 0x47, 0x52, 0xc9, 0x34,
	0xd0, 0x1d, 0x2f, 0x52, 0xa8, 0xe3, 0xd5, 0x7f, 0xea, 0x51, 0x1c, 0xfc, 0xa9, 0x47, 0x74, 0x21,
	0x4a, 0x43, 0x17, 0xe2, 0x0e, 0x64, 0x9b, 0xfa, 0xdb, 0x90, 0x2a, 0xca, 0x17, 0x9b, 0x94, 0xdc,
	0xd9, 0x5b, 0x58, 0xf2, 0x6c, 0x2b, 0x38, 0x73, 0xfd, 0xae, 0x49, 0x27, 0x47, 0xb5, 0x78, 0x61,
	0xeb, 0x7e, 0xd2, 0xb7, 0xd1, 0x91, 0xe3, 0xad, 0x97, 0xa2, 0x85, 0xef, 0x91, 0xc4, 0xde, 0x40,
	0x84, 0x46, 0x6a, 0x41, 0xe5, 0x69, 0x05, 0x15, 0xf5, 0x3a, 0x25, 0xe7, 0x4b, 0x60, 0x32, 0xf4,
	0xb8, 0x6f, 0x62, 0xfb, 0xcd, 0x7d, 0xed, 0x1c, 0x55, 0xda, 0x97, 0x69, 0xa6, 0x46, 0x13, 0xe4,
	0x1d, 0xc3, 0x81, 0xd5, 0x28, 0x68, 0xf7, 0x79, 0xa0, 0x83, 0xe7, 0xc4, 0xf5, 0x8e, 0xa6, 0x78,
	0x03, 0xfb, 0x7f, 0xbe, 0xd2, 0x4f, 0x86, 0xea, 0xcf, 0x57, 0x14, 0x00, 0xee, 0xf3, 0xf3, 0xe1,
	0x0f, 0xb4, 0x3e, 0x3f, 0xdf, 0x71, 0x02, 0xe3, 0x7f, 0x33, 0x50, 0x4d, 0x53, 0x28, 0xbd, 0x3f,
	0x40, 0xe3, 0x2e, 0x14, 0xba, 0xbc, 0x7b, 0xca, 0xfd, 0x01, 0xbe, 0x9b, 0x80, 0xd4, 0x25, 0xdc,
	0x92, 0x3a, 0xa8, 0x75, 0xba, 0xd0, 0xc8, 0x76, 0x2f, 0xd5, 0xd7, 0x8b, 0xf9, 0x64, 0xac, 0x2f,
	0x49, 0xc2, 0x62, 0xf7, 0x92, 0xee, 0xdb, 0x2a, 0xe4, 0x7a, 0x82, 0x5f, 0xd0, 0xb6, 0xd5, 0xe5,
	0xc8, 0xe2, 0x78, 0xc7, 0x09, 0x9e, 0x9e, 0x00, 0x0c, 0xba, 0x58, 0x76, 0x07, 0x6e, 0xec, 0x1e,
	0x7c, 0x77, 0xb0, 0xbb, 0x67, 0x9e, 0xfc, 0xb2, 0xb6, 0x67, 0xee, 0xee, 0xbd, 0xd9, 0xfe, 0xf0,
	0xfe, 0x44, 0x65, 0xfe, 0xe1, 0x89, 0xb7, 0xdb, 0xef, 0xdf, 0x94, 0x33, 0xe3, 0xec, 0xb5, 0xbd,
	0xfa, 0xce, 0xde, 0xd1, 0x49, 0x79, 0x76, 0xeb, 0xdf, 0xab, 0x50, 0x8c, 0x0c, 0x7a, 0x2f, 0x7a,
	0x9c, 0x5d, 0x40, 0x25, 0xed, 0x4b, 0x07, 0xfb, 0xf1, 0xf8, 0x56, 0x26, 0x7c, 0x63, 0xa9, 0x7e,
	0x39, 0x3d, 0xb3, 0xf4, 0x8c, 0x19, 0xf6, 0x03, 0xac, 0xa6, 0x7e, 0xd8, 0x60, 0x31, 0x61, 0x93,
	0xbe, 0xb6, 0x54, 0x9f, 0x5d, 0x83, 0x3b, 0xd2, 0x9d, 0xfa, 0x89, 0x23, 0xae, 0x7b, 0xd2, 0xe7,
	0x95, 0xb8, 0xee, 0xc9, 0xdf, 0x4e, 0x66, 0xd8, 0x6f, 0x33, 0xf0, 0x78, 0x3a, 0xa8, 0x9b, 0xfd,
	0x59, 0x6c, 0x5f, 0xd3, 0x22, 0xf0, 0xd5, 0x97, 0x9f, 0xba, 0x94, 0x6c, 0xfc, 0xc7, 0xd4, 0xcf,
	0x89, 0xd1, 0x02, 0xf6, 0xf5, 0xf5, 0x14, 0xa0, 0x4d, 0x5b, 0xd7, 0x5d, 0x42, 0xb6, 0xfc, 0xad,
	0xaa, 0xff, 0x92, 0xbf, 0x6c, 0xb2, 0x67, 0xd3, 0xc9, 0xd4, 0x1f, 0x56, 0xab, 0xcf, 0xaf, 0xc3,
	0x4e, 0xea, 0xdb, 0xaa, 0x3d, 0x1e, 0x43, 0x99, 0xd9, 0xe3, 0x04, 0x41, 0x09, 0x08, 0x78, 0xf5,
	0x47, 0x53, 0xf1, 0x91, 0xa6, 0x5f, 0xab, 0x66, 0x32, 0xf5, 0x9b, 0x0f, 0x7b, 0x31, 0x9d, 0xf1,
	0xfd, 0x0f, 0x5e, 0xd5, 0xaf, 0xae, 0xb7, 0x80, 0xac, 0xf8, 0x4d, 0x46, 0x37, 0xdc, 0x13, 0x4d,
	0xf9, 0x93, 0x4f, 0xf8, 0x62, 0x75, 0x5e, 0xfd, 0xe6, 0x93, 0xbe, 0x73, 0x19, 0x33, 0xec, 0xef,
	0x32, 0xe3, 0x28, 0xcc, 0x28, 0xba, 0xf5, 0x3c, 0x51, 0x70, 0x2a, 0x70, 0x5f, 0x7d, 0x71, 0x2d,
	0x7e, 0x32, 0xe1, 0x1f, 0x32, 0x71, 0xec, 0x65, 0xd4, 0x88, 0xaf, 0x12, 0x85, 0x4e, 0x00, 0x43,
	0xaa, 0x5f, 0x5f, 0x73, 0x05, 0x19, 0x72, 0x01, 0x95, 0x34, 0xc8, 0x21, 0x9e, 0xb0, 0x27, 0x00,
	0x1f, 0xf1, 0x84, 0x3d, 0x09, 0xc9, 0x30, 0x66, 0xd8, 0x39, 0xdc, 0x4e, 0x46, 0xb9, 0xd9, 0x93,
	0x71, 0x49, 0xa9, 0xe0, 0x79, 0xf5, 0xe9, 0xb4, 0xac, 0xd1, 0xe5, 0x4b, 0x00, 0xb4, 0xe3, 0x97,
	0x2f, 0x19, 0x49, 0x8f, 0x5f, 0xbe, 0x34, 0x74, 0x9c, 0x34, 0x25, 0x20, 0x86, 0x71, 0x4d, 0xc9,
	0x48, 0x64, 0x5c, 0x53, 0x1a, 0xfc, 0x48, 0x6e, 0x4c, 0xee, 0xc0, 0xe3, 0x6e, 0x4c, 0x05, 0x00,
	0xe2, 0x6e, 0x9c, 0xd0, 0xd4, 0x93, 0xca, 0xe4, 0xd6, 0x3c, 0xae, 0x32, 0xb5, 0xfd, 0x8f, 0xab,
	0x4c, 0xef, 0xf6, 0x8d, 0x19, 0x16, 0xd0, 0xc7, 0xb9, 0xa4, 0xbe, 0x9c, 0x25, 0x09, 0x4a, 0x69,
	0xef, 0xab, 0x3f, 0x9e, 0x9a, 0x37, 0xd2, 0x9a, 0x02, 0xd6, 0xc6, 0xb5, 0xa6, 0xa3, 0xc0, 0x71,
	0xad, 0x13, 0x10, 0x60, 0x55, 0x4d, 0xa4, 0xa2, 0xb2, 0xf1, 0x6a, 0x62, 0x12, 0x22, 0x1c, 0xaf,
	0x26, 0x26, 0xc2, 0xbd, 0xc6, 0x0c, 0xfb, 0x15, 0x2c, 0x8f, 0xa1, 0xb7, 0xcc, 0x48, 0x3e, 0xa8,
	0x11, 0x3d, 0x0f, 0xae, 0xe4, 0x21, 0xe9, 0x0e, 0xdc, 0x4a, 0x04, 0xef, 0xd8, 0x66, 0x72, 0xfc,
	0xc5, 0x91, 0xbc, 0xea, 0x93, 0x29, 0x39, 0x49, 0x1f, 0x27, 0x84, 0x7a, 0x5c, 0xd9, 0xa3, 0x04,
	0x63, 0x13, 0x34, 0x3d, 0x9e, 0x86, 0x8d, 0xd4, 0x9c, 0xc2, 0x4a, 0x0c, 0x74, 0x63, 0x0f, 0x63,
	0xf5, 0x6b, 0x02, 0xc8, 0x57, 0x7d, 0x34, 0x05, 0x57, 0x14, 0x8a, 0x29, 0x40, 0x4b, 0x3c, 0x14,
	0xd3, 0x71, 0xa6, 0x78, 0x28, 0x4e, 0x42, 0x6f, 0xe8, 0xc0, 0x12, 0x01, 0x89, 0xf8, 0x81, 0xa5,
	0x41, 0x2e, 0xf1, 0x03, 0x4b, 0x47, 0x38, 0x28, 0xb3, 0x24, 0xc3, 0x18, 0xf1, 0xcc, 0x92, 0x8a,
	0x85, 0xc4, 0x33, 0x4b, 0x3a, 0x32, 0xa2, 0x54, 0x26, 0xb7, 0x83, 0x71, 0x95, 0xa9, 0x7d, 0x6a,
	0x5c, 0x65, 0x7a, 0x87, 0x69, 0xcc, 0xbc, 0xde, 0xfa, 0xcb, 0xaf, 0x5a, 0xae, 0x6d, 0x39, 0xad,
	0xe7, 0xdf, 0x6c, 0x05, 0xc1, 0xf3, 0x86, 0xdb, 0x7d, 0x41, 0xff, 0xf7, 0xd2, 0x70, 0xed, 0x17,
	0x92, 0xfb, 0x3d, 0xd1, 0xe0, 0x72, 0xec, 0x1f, 0x63, 0x4e, 0x17, 0x89, 0xe3, 0x27, 0xff, 0x1f,
	0x00, 0x00, 0xff, 0xff, 0xb8, 0x0b, 0x71, 0x46, 0x40, 0x33, 0x00, 0x00,
}
