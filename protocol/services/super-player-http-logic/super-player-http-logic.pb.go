// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/super-player-http-logic/super-player-http-logic.proto

package super_player_http_logic // import "golang.52tt.com/protocol/services/super-player-http-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 订单状态
type EnumOrderStatus int32

const (
	EnumOrderStatus_ORDER_INIT        EnumOrderStatus = 0
	EnumOrderStatus_ORDER_PAY_SUCCESS EnumOrderStatus = 1
	EnumOrderStatus_ORDER_PAY_FAILED  EnumOrderStatus = 2
)

var EnumOrderStatus_name = map[int32]string{
	0: "ORDER_INIT",
	1: "ORDER_PAY_SUCCESS",
	2: "ORDER_PAY_FAILED",
}
var EnumOrderStatus_value = map[string]int32{
	"ORDER_INIT":        0,
	"ORDER_PAY_SUCCESS": 1,
	"ORDER_PAY_FAILED":  2,
}

func (x EnumOrderStatus) String() string {
	return proto.EnumName(EnumOrderStatus_name, int32(x))
}
func (EnumOrderStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{0}
}

type MissionType int32

const (
	MissionType_Unknown   MissionType = 0
	MissionType_Daily     MissionType = 1
	MissionType_TimeLimit MissionType = 2
	MissionType_Activity  MissionType = 3
)

var MissionType_name = map[int32]string{
	0: "Unknown",
	1: "Daily",
	2: "TimeLimit",
	3: "Activity",
}
var MissionType_value = map[string]int32{
	"Unknown":   0,
	"Daily":     1,
	"TimeLimit": 2,
	"Activity":  3,
}

func (x MissionType) String() string {
	return proto.EnumName(MissionType_name, int32(x))
}
func (MissionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{1}
}

type MissionStatus int32

const (
	MissionStatus_Incomplete MissionStatus = 0
	MissionStatus_Completed  MissionStatus = 1
	MissionStatus_Finished   MissionStatus = 2
)

var MissionStatus_name = map[int32]string{
	0: "Incomplete",
	1: "Completed",
	2: "Finished",
}
var MissionStatus_value = map[string]int32{
	"Incomplete": 0,
	"Completed":  1,
	"Finished":   2,
}

func (x MissionStatus) String() string {
	return proto.EnumName(MissionStatus_name, int32(x))
}
func (MissionStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{2}
}

// 装扮类型
type DressType int32

const (
	DressType_DRESS_TYPE_UNSPECIFIC      DressType = 0
	DressType_DRESS_TYPE_ROOM_SUIT       DressType = 1
	DressType_DRESS_TYPE_SPECIAL_CONCERN DressType = 2
	DressType_DRESS_TYPE_CHAT_BACKGROUND DressType = 3
	DressType_DRESS_TYPE_CHAT_BUBBLE     DressType = 4
)

var DressType_name = map[int32]string{
	0: "DRESS_TYPE_UNSPECIFIC",
	1: "DRESS_TYPE_ROOM_SUIT",
	2: "DRESS_TYPE_SPECIAL_CONCERN",
	3: "DRESS_TYPE_CHAT_BACKGROUND",
	4: "DRESS_TYPE_CHAT_BUBBLE",
}
var DressType_value = map[string]int32{
	"DRESS_TYPE_UNSPECIFIC":      0,
	"DRESS_TYPE_ROOM_SUIT":       1,
	"DRESS_TYPE_SPECIAL_CONCERN": 2,
	"DRESS_TYPE_CHAT_BACKGROUND": 3,
	"DRESS_TYPE_CHAT_BUBBLE":     4,
}

func (x DressType) String() string {
	return proto.EnumName(DressType_name, int32(x))
}
func (DressType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{3}
}

type PlatFormType int32

const (
	PlatFormType_ENUM_PLATFORM_ALL     PlatFormType = 0
	PlatFormType_ENUM_PLATFORM_ANDROID PlatFormType = 1
	PlatFormType_ENUM_PLATFORM_IOS     PlatFormType = 2
)

var PlatFormType_name = map[int32]string{
	0: "ENUM_PLATFORM_ALL",
	1: "ENUM_PLATFORM_ANDROID",
	2: "ENUM_PLATFORM_IOS",
}
var PlatFormType_value = map[string]int32{
	"ENUM_PLATFORM_ALL":     0,
	"ENUM_PLATFORM_ANDROID": 1,
	"ENUM_PLATFORM_IOS":     2,
}

func (x PlatFormType) String() string {
	return proto.EnumName(PlatFormType_name, int32(x))
}
func (PlatFormType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{4}
}

type SuperPlayerStatus int32

const (
	SuperPlayerStatus_ENUM_STATUS_NO_OPEN     SuperPlayerStatus = 0
	SuperPlayerStatus_ENUM_STATUS_OPENING     SuperPlayerStatus = 1
	SuperPlayerStatus_ENUM_STATUS_SOON_EXPIRE SuperPlayerStatus = 2
	SuperPlayerStatus_ENUM_STATUS_EXPIRED     SuperPlayerStatus = 3
	SuperPlayerStatus_ENUM_STATUS_SIGN        SuperPlayerStatus = 4
	SuperPlayerStatus_ENUM_STATUS_PENDING     SuperPlayerStatus = 5
)

var SuperPlayerStatus_name = map[int32]string{
	0: "ENUM_STATUS_NO_OPEN",
	1: "ENUM_STATUS_OPENING",
	2: "ENUM_STATUS_SOON_EXPIRE",
	3: "ENUM_STATUS_EXPIRED",
	4: "ENUM_STATUS_SIGN",
	5: "ENUM_STATUS_PENDING",
}
var SuperPlayerStatus_value = map[string]int32{
	"ENUM_STATUS_NO_OPEN":     0,
	"ENUM_STATUS_OPENING":     1,
	"ENUM_STATUS_SOON_EXPIRE": 2,
	"ENUM_STATUS_EXPIRED":     3,
	"ENUM_STATUS_SIGN":        4,
	"ENUM_STATUS_PENDING":     5,
}

func (x SuperPlayerStatus) String() string {
	return proto.EnumName(SuperPlayerStatus_name, int32(x))
}
func (SuperPlayerStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{5}
}

type SuperPlayerType int32

const (
	SuperPlayerType_ENUM_TYPE_NORMAL SuperPlayerType = 0
	SuperPlayerType_ENUM_TYPE_SVIP   SuperPlayerType = 1
)

var SuperPlayerType_name = map[int32]string{
	0: "ENUM_TYPE_NORMAL",
	1: "ENUM_TYPE_SVIP",
}
var SuperPlayerType_value = map[string]int32{
	"ENUM_TYPE_NORMAL": 0,
	"ENUM_TYPE_SVIP":   1,
}

func (x SuperPlayerType) String() string {
	return proto.EnumName(SuperPlayerType_name, int32(x))
}
func (SuperPlayerType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{6}
}

// 会员优惠券弹窗场景
type CouponPopUpScene int32

const (
	CouponPopUpScene_ENUM_RENEWAL_POPUP_SCENE_UNSPECIFIED           CouponPopUpScene = 0
	CouponPopUpScene_ENUM_RENEWAL_POPUP_SCENE_PERSONAL_PAGE         CouponPopUpScene = 1
	CouponPopUpScene_ENUM_RENEWAL_POPUP_SCENE_ENTRY_BANNER          CouponPopUpScene = 2
	CouponPopUpScene_ENUM_RENEWAL_POPUP_SCENE_SUPER_PLAY_PAGE       CouponPopUpScene = 3
	CouponPopUpScene_ENUM_RENEWAL_POPUP_SCENE_SUPER_PLAY_PAGE_LEAVE CouponPopUpScene = 4
	CouponPopUpScene_ENUM_RENEWAL_POPUP_SCENE_WHO_MIND_ME           CouponPopUpScene = 7
)

var CouponPopUpScene_name = map[int32]string{
	0: "ENUM_RENEWAL_POPUP_SCENE_UNSPECIFIED",
	1: "ENUM_RENEWAL_POPUP_SCENE_PERSONAL_PAGE",
	2: "ENUM_RENEWAL_POPUP_SCENE_ENTRY_BANNER",
	3: "ENUM_RENEWAL_POPUP_SCENE_SUPER_PLAY_PAGE",
	4: "ENUM_RENEWAL_POPUP_SCENE_SUPER_PLAY_PAGE_LEAVE",
	7: "ENUM_RENEWAL_POPUP_SCENE_WHO_MIND_ME",
}
var CouponPopUpScene_value = map[string]int32{
	"ENUM_RENEWAL_POPUP_SCENE_UNSPECIFIED":           0,
	"ENUM_RENEWAL_POPUP_SCENE_PERSONAL_PAGE":         1,
	"ENUM_RENEWAL_POPUP_SCENE_ENTRY_BANNER":          2,
	"ENUM_RENEWAL_POPUP_SCENE_SUPER_PLAY_PAGE":       3,
	"ENUM_RENEWAL_POPUP_SCENE_SUPER_PLAY_PAGE_LEAVE": 4,
	"ENUM_RENEWAL_POPUP_SCENE_WHO_MIND_ME":           7,
}

func (x CouponPopUpScene) String() string {
	return proto.EnumName(CouponPopUpScene_name, int32(x))
}
func (CouponPopUpScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{7}
}

type SuperPlayerIdentityPopupReq_ReqType int32

const (
	SuperPlayerIdentityPopupReq_REQ_QUERY               SuperPlayerIdentityPopupReq_ReqType = 0
	SuperPlayerIdentityPopupReq_REQ_FINISHED            SuperPlayerIdentityPopupReq_ReqType = 1
	SuperPlayerIdentityPopupReq_REQ_VALUE_CHANGE_TIME   SuperPlayerIdentityPopupReq_ReqType = 2
	SuperPlayerIdentityPopupReq_REQ_WEB_CANCEL_CONTRACT SuperPlayerIdentityPopupReq_ReqType = 3
)

var SuperPlayerIdentityPopupReq_ReqType_name = map[int32]string{
	0: "REQ_QUERY",
	1: "REQ_FINISHED",
	2: "REQ_VALUE_CHANGE_TIME",
	3: "REQ_WEB_CANCEL_CONTRACT",
}
var SuperPlayerIdentityPopupReq_ReqType_value = map[string]int32{
	"REQ_QUERY":               0,
	"REQ_FINISHED":            1,
	"REQ_VALUE_CHANGE_TIME":   2,
	"REQ_WEB_CANCEL_CONTRACT": 3,
}

func (x SuperPlayerIdentityPopupReq_ReqType) String() string {
	return proto.EnumName(SuperPlayerIdentityPopupReq_ReqType_name, int32(x))
}
func (SuperPlayerIdentityPopupReq_ReqType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{51, 0}
}

// 补单
type ReplacementOrderReq struct {
	OldOrderId           string   `protobuf:"bytes,1,opt,name=old_order_id,json=oldOrderId,proto3" json:"old_order_id,"`
	CreateTime           int32    `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReplacementOrderReq) Reset()         { *m = ReplacementOrderReq{} }
func (m *ReplacementOrderReq) String() string { return proto.CompactTextString(m) }
func (*ReplacementOrderReq) ProtoMessage()    {}
func (*ReplacementOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{0}
}
func (m *ReplacementOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplacementOrderReq.Unmarshal(m, b)
}
func (m *ReplacementOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplacementOrderReq.Marshal(b, m, deterministic)
}
func (dst *ReplacementOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplacementOrderReq.Merge(dst, src)
}
func (m *ReplacementOrderReq) XXX_Size() int {
	return xxx_messageInfo_ReplacementOrderReq.Size(m)
}
func (m *ReplacementOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplacementOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReplacementOrderReq proto.InternalMessageInfo

func (m *ReplacementOrderReq) GetOldOrderId() string {
	if m != nil {
		return m.OldOrderId
	}
	return ""
}

func (m *ReplacementOrderReq) GetCreateTime() int32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type ReplacementOrderResp struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReplacementOrderResp) Reset()         { *m = ReplacementOrderResp{} }
func (m *ReplacementOrderResp) String() string { return proto.CompactTextString(m) }
func (*ReplacementOrderResp) ProtoMessage()    {}
func (*ReplacementOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{1}
}
func (m *ReplacementOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplacementOrderResp.Unmarshal(m, b)
}
func (m *ReplacementOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplacementOrderResp.Marshal(b, m, deterministic)
}
func (dst *ReplacementOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplacementOrderResp.Merge(dst, src)
}
func (m *ReplacementOrderResp) XXX_Size() int {
	return xxx_messageInfo_ReplacementOrderResp.Size(m)
}
func (m *ReplacementOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplacementOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReplacementOrderResp proto.InternalMessageInfo

func (m *ReplacementOrderResp) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

// 开通会员
type OpenSuperPlayerReq struct {
	SuperPlayerUid       uint32   `protobuf:"varint,1,opt,name=super_player_uid,json=superPlayerUid,proto3" json:"super_player_uid,"`
	Months               int64    `protobuf:"varint,2,opt,name=months,proto3" json:"months,"`
	Value                int64    `protobuf:"varint,3,opt,name=value,proto3" json:"value,"`
	ServerTime           int64    `protobuf:"varint,4,opt,name=server_time,json=serverTime,proto3" json:"server_time,"`
	OrderId              string   `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,"`
	PackageId            string   `protobuf:"bytes,6,opt,name=package_id,json=packageId,proto3" json:"package_id,"`
	Mins                 int64    `protobuf:"varint,7,opt,name=mins,proto3" json:"mins,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenSuperPlayerReq) Reset()         { *m = OpenSuperPlayerReq{} }
func (m *OpenSuperPlayerReq) String() string { return proto.CompactTextString(m) }
func (*OpenSuperPlayerReq) ProtoMessage()    {}
func (*OpenSuperPlayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{2}
}
func (m *OpenSuperPlayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenSuperPlayerReq.Unmarshal(m, b)
}
func (m *OpenSuperPlayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenSuperPlayerReq.Marshal(b, m, deterministic)
}
func (dst *OpenSuperPlayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenSuperPlayerReq.Merge(dst, src)
}
func (m *OpenSuperPlayerReq) XXX_Size() int {
	return xxx_messageInfo_OpenSuperPlayerReq.Size(m)
}
func (m *OpenSuperPlayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenSuperPlayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_OpenSuperPlayerReq proto.InternalMessageInfo

func (m *OpenSuperPlayerReq) GetSuperPlayerUid() uint32 {
	if m != nil {
		return m.SuperPlayerUid
	}
	return 0
}

func (m *OpenSuperPlayerReq) GetMonths() int64 {
	if m != nil {
		return m.Months
	}
	return 0
}

func (m *OpenSuperPlayerReq) GetValue() int64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *OpenSuperPlayerReq) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *OpenSuperPlayerReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *OpenSuperPlayerReq) GetPackageId() string {
	if m != nil {
		return m.PackageId
	}
	return ""
}

func (m *OpenSuperPlayerReq) GetMins() int64 {
	if m != nil {
		return m.Mins
	}
	return 0
}

type OpenSuperPlayerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenSuperPlayerResp) Reset()         { *m = OpenSuperPlayerResp{} }
func (m *OpenSuperPlayerResp) String() string { return proto.CompactTextString(m) }
func (*OpenSuperPlayerResp) ProtoMessage()    {}
func (*OpenSuperPlayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{3}
}
func (m *OpenSuperPlayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenSuperPlayerResp.Unmarshal(m, b)
}
func (m *OpenSuperPlayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenSuperPlayerResp.Marshal(b, m, deterministic)
}
func (dst *OpenSuperPlayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenSuperPlayerResp.Merge(dst, src)
}
func (m *OpenSuperPlayerResp) XXX_Size() int {
	return xxx_messageInfo_OpenSuperPlayerResp.Size(m)
}
func (m *OpenSuperPlayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenSuperPlayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_OpenSuperPlayerResp proto.InternalMessageInfo

// 套餐
type Package struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,"`
	ProductId            string   `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id,"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,"`
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,"`
	Label                string   `protobuf:"bytes,5,opt,name=label,proto3" json:"label,"`
	OriginalPrice        float32  `protobuf:"fixed32,6,opt,name=original_price,json=originalPrice,proto3" json:"original_price,"`
	Price                float32  `protobuf:"fixed32,7,opt,name=price,proto3" json:"price,"`
	Months               int32    `protobuf:"varint,8,opt,name=months,proto3" json:"months,"`
	Value                int32    `protobuf:"varint,9,opt,name=value,proto3" json:"value,"`
	Auto                 bool     `protobuf:"varint,10,opt,name=auto,proto3" json:"auto,"`
	PayChannelList       []string `protobuf:"bytes,11,rep,name=pay_channel_list,json=payChannelList,proto3" json:"pay_channel_list,"`
	DiscountPrice        float32  `protobuf:"fixed32,12,opt,name=discount_price,json=discountPrice,proto3" json:"discount_price,"`
	DailyPrice           float32  `protobuf:"fixed32,13,opt,name=daily_price,json=dailyPrice,proto3" json:"daily_price,"`
	IsShowCoupon         bool     `protobuf:"varint,14,opt,name=is_show_coupon,json=isShowCoupon,proto3" json:"is_show_coupon,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Package) Reset()         { *m = Package{} }
func (m *Package) String() string { return proto.CompactTextString(m) }
func (*Package) ProtoMessage()    {}
func (*Package) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{4}
}
func (m *Package) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Package.Unmarshal(m, b)
}
func (m *Package) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Package.Marshal(b, m, deterministic)
}
func (dst *Package) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Package.Merge(dst, src)
}
func (m *Package) XXX_Size() int {
	return xxx_messageInfo_Package.Size(m)
}
func (m *Package) XXX_DiscardUnknown() {
	xxx_messageInfo_Package.DiscardUnknown(m)
}

var xxx_messageInfo_Package proto.InternalMessageInfo

func (m *Package) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Package) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *Package) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Package) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *Package) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *Package) GetOriginalPrice() float32 {
	if m != nil {
		return m.OriginalPrice
	}
	return 0
}

func (m *Package) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *Package) GetMonths() int32 {
	if m != nil {
		return m.Months
	}
	return 0
}

func (m *Package) GetValue() int32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *Package) GetAuto() bool {
	if m != nil {
		return m.Auto
	}
	return false
}

func (m *Package) GetPayChannelList() []string {
	if m != nil {
		return m.PayChannelList
	}
	return nil
}

func (m *Package) GetDiscountPrice() float32 {
	if m != nil {
		return m.DiscountPrice
	}
	return 0
}

func (m *Package) GetDailyPrice() float32 {
	if m != nil {
		return m.DailyPrice
	}
	return 0
}

func (m *Package) GetIsShowCoupon() bool {
	if m != nil {
		return m.IsShowCoupon
	}
	return false
}

type UpgradePackage struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,"`
	ProductId            string   `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id,"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,"`
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,"`
	Price                float32  `protobuf:"fixed32,5,opt,name=price,proto3" json:"price,"`
	Days                 int32    `protobuf:"varint,6,opt,name=days,proto3" json:"days,"`
	Value                int32    `protobuf:"varint,7,opt,name=value,proto3" json:"value,"`
	PayChannelList       []string `protobuf:"bytes,8,rep,name=pay_channel_list,json=payChannelList,proto3" json:"pay_channel_list,"`
	DiscountPrice        float32  `protobuf:"fixed32,9,opt,name=discount_price,json=discountPrice,proto3" json:"discount_price,"`
	DailyPrice           float32  `protobuf:"fixed32,10,opt,name=daily_price,json=dailyPrice,proto3" json:"daily_price,"`
	Label                string   `protobuf:"bytes,11,opt,name=label,proto3" json:"label,"`
	IsShowCoupon         bool     `protobuf:"varint,12,opt,name=is_show_coupon,json=isShowCoupon,proto3" json:"is_show_coupon,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpgradePackage) Reset()         { *m = UpgradePackage{} }
func (m *UpgradePackage) String() string { return proto.CompactTextString(m) }
func (*UpgradePackage) ProtoMessage()    {}
func (*UpgradePackage) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{5}
}
func (m *UpgradePackage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpgradePackage.Unmarshal(m, b)
}
func (m *UpgradePackage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpgradePackage.Marshal(b, m, deterministic)
}
func (dst *UpgradePackage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpgradePackage.Merge(dst, src)
}
func (m *UpgradePackage) XXX_Size() int {
	return xxx_messageInfo_UpgradePackage.Size(m)
}
func (m *UpgradePackage) XXX_DiscardUnknown() {
	xxx_messageInfo_UpgradePackage.DiscardUnknown(m)
}

var xxx_messageInfo_UpgradePackage proto.InternalMessageInfo

func (m *UpgradePackage) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpgradePackage) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *UpgradePackage) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpgradePackage) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *UpgradePackage) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *UpgradePackage) GetDays() int32 {
	if m != nil {
		return m.Days
	}
	return 0
}

func (m *UpgradePackage) GetValue() int32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *UpgradePackage) GetPayChannelList() []string {
	if m != nil {
		return m.PayChannelList
	}
	return nil
}

func (m *UpgradePackage) GetDiscountPrice() float32 {
	if m != nil {
		return m.DiscountPrice
	}
	return 0
}

func (m *UpgradePackage) GetDailyPrice() float32 {
	if m != nil {
		return m.DailyPrice
	}
	return 0
}

func (m *UpgradePackage) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *UpgradePackage) GetIsShowCoupon() bool {
	if m != nil {
		return m.IsShowCoupon
	}
	return false
}

// 获取套餐配置
type GetPackageListReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,"`
	SuperPlayerStatus    uint32   `protobuf:"varint,2,opt,name=super_player_status,json=superPlayerStatus,proto3" json:"super_player_status,"`
	DeviceId             string   `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPackageListReq) Reset()         { *m = GetPackageListReq{} }
func (m *GetPackageListReq) String() string { return proto.CompactTextString(m) }
func (*GetPackageListReq) ProtoMessage()    {}
func (*GetPackageListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{6}
}
func (m *GetPackageListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPackageListReq.Unmarshal(m, b)
}
func (m *GetPackageListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPackageListReq.Marshal(b, m, deterministic)
}
func (dst *GetPackageListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPackageListReq.Merge(dst, src)
}
func (m *GetPackageListReq) XXX_Size() int {
	return xxx_messageInfo_GetPackageListReq.Size(m)
}
func (m *GetPackageListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPackageListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPackageListReq proto.InternalMessageInfo

func (m *GetPackageListReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetPackageListReq) GetSuperPlayerStatus() uint32 {
	if m != nil {
		return m.SuperPlayerStatus
	}
	return 0
}

func (m *GetPackageListReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type GetPackageListResp struct {
	PackageList          []*Package `protobuf:"bytes,1,rep,name=package_list,json=packageList,proto3" json:"package_list,"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetPackageListResp) Reset()         { *m = GetPackageListResp{} }
func (m *GetPackageListResp) String() string { return proto.CompactTextString(m) }
func (*GetPackageListResp) ProtoMessage()    {}
func (*GetPackageListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{7}
}
func (m *GetPackageListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPackageListResp.Unmarshal(m, b)
}
func (m *GetPackageListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPackageListResp.Marshal(b, m, deterministic)
}
func (dst *GetPackageListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPackageListResp.Merge(dst, src)
}
func (m *GetPackageListResp) XXX_Size() int {
	return xxx_messageInfo_GetPackageListResp.Size(m)
}
func (m *GetPackageListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPackageListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPackageListResp proto.InternalMessageInfo

func (m *GetPackageListResp) GetPackageList() []*Package {
	if m != nil {
		return m.PackageList
	}
	return nil
}

// 获取SVIP套餐配置
type GetSvipPackageListReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSvipPackageListReq) Reset()         { *m = GetSvipPackageListReq{} }
func (m *GetSvipPackageListReq) String() string { return proto.CompactTextString(m) }
func (*GetSvipPackageListReq) ProtoMessage()    {}
func (*GetSvipPackageListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{8}
}
func (m *GetSvipPackageListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSvipPackageListReq.Unmarshal(m, b)
}
func (m *GetSvipPackageListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSvipPackageListReq.Marshal(b, m, deterministic)
}
func (dst *GetSvipPackageListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSvipPackageListReq.Merge(dst, src)
}
func (m *GetSvipPackageListReq) XXX_Size() int {
	return xxx_messageInfo_GetSvipPackageListReq.Size(m)
}
func (m *GetSvipPackageListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSvipPackageListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSvipPackageListReq proto.InternalMessageInfo

func (m *GetSvipPackageListReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type GetSvipPackageListResp struct {
	UpgradePackageList   []*UpgradePackage `protobuf:"bytes,1,rep,name=upgrade_package_list,json=upgradePackageList,proto3" json:"upgrade_package_list,"`
	PackageList          []*Package        `protobuf:"bytes,2,rep,name=package_list,json=packageList,proto3" json:"package_list,"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetSvipPackageListResp) Reset()         { *m = GetSvipPackageListResp{} }
func (m *GetSvipPackageListResp) String() string { return proto.CompactTextString(m) }
func (*GetSvipPackageListResp) ProtoMessage()    {}
func (*GetSvipPackageListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{9}
}
func (m *GetSvipPackageListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSvipPackageListResp.Unmarshal(m, b)
}
func (m *GetSvipPackageListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSvipPackageListResp.Marshal(b, m, deterministic)
}
func (dst *GetSvipPackageListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSvipPackageListResp.Merge(dst, src)
}
func (m *GetSvipPackageListResp) XXX_Size() int {
	return xxx_messageInfo_GetSvipPackageListResp.Size(m)
}
func (m *GetSvipPackageListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSvipPackageListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSvipPackageListResp proto.InternalMessageInfo

func (m *GetSvipPackageListResp) GetUpgradePackageList() []*UpgradePackage {
	if m != nil {
		return m.UpgradePackageList
	}
	return nil
}

func (m *GetSvipPackageListResp) GetPackageList() []*Package {
	if m != nil {
		return m.PackageList
	}
	return nil
}

// 下单接口
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type PlaceOrderReq struct {
	SuperPlayerUid             uint32          `protobuf:"varint,1,opt,name=super_player_uid,json=superPlayerUid,proto3" json:"super_player_uid,"`
	PackageId                  string          `protobuf:"bytes,2,opt,name=package_id,json=packageId,proto3" json:"package_id,"`
	PayChannel                 string          `protobuf:"bytes,3,opt,name=pay_channel,json=payChannel,proto3" json:"pay_channel,"`
	OrderType                  string          `protobuf:"bytes,4,opt,name=order_type,json=orderType,proto3" json:"order_type,"`
	OsType                     string          `protobuf:"bytes,5,opt,name=os_type,json=osType,proto3" json:"os_type,"`
	Version                    string          `protobuf:"bytes,6,opt,name=version,proto3" json:"version,"`
	Remark                     string          `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark,"`
	BundleId                   string          `protobuf:"bytes,8,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,"`
	Code                       string          `protobuf:"bytes,9,opt,name=code,proto3" json:"code,"`
	Ttid                       string          `protobuf:"bytes,10,opt,name=ttid,proto3" json:"ttid,"`
	Token                      string          `protobuf:"bytes,11,opt,name=token,proto3" json:"token,"`
	ClientVersion              uint32          `protobuf:"varint,12,opt,name=client_version,json=clientVersion,proto3" json:"client_version,"`
	DeviceId                   string          `protobuf:"bytes,13,opt,name=device_id,json=deviceId,proto3" json:"device_id,"`
	RequestId                  string          `protobuf:"bytes,14,opt,name=request_id,json=requestId,proto3" json:"request_id,"`
	ClientType                 uint32          `protobuf:"varint,15,opt,name=client_type,json=clientType,proto3" json:"client_type,"`
	FaceAuthToken              string          `protobuf:"bytes,16,opt,name=FaceAuthToken,proto3" json:"FaceAuthToken,"`
	FaceAuthProviderCode       string          `protobuf:"bytes,17,opt,name=face_auth_provider_code,json=faceAuthProviderCode,proto3" json:"face_auth_provider_code,"`
	FaceAuthProviderResultData string          `protobuf:"bytes,18,opt,name=face_auth_provider_result_data,json=faceAuthProviderResultData,proto3" json:"face_auth_provider_result_data,"`
	FaceAuthResultToken        string          `protobuf:"bytes,19,opt,name=face_auth_result_token,json=faceAuthResultToken,proto3" json:"face_auth_result_token,"`
	SuperPlayerType            SuperPlayerType `protobuf:"varint,20,opt,name=super_player_type,json=superPlayerType,proto3,enum=super_player_http_logic.SuperPlayerType" json:"super_player_type,"`
	OriginalTransactionIds     []string        `protobuf:"bytes,21,rep,name=original_transaction_ids,json=originalTransactionIds,proto3" json:"original_transaction_ids,"`
	XXX_NoUnkeyedLiteral       struct{}        `json:"-"`
	XXX_unrecognized           []byte          `json:"-"`
	XXX_sizecache              int32           `json:"-"`
}

func (m *PlaceOrderReq) Reset()         { *m = PlaceOrderReq{} }
func (m *PlaceOrderReq) String() string { return proto.CompactTextString(m) }
func (*PlaceOrderReq) ProtoMessage()    {}
func (*PlaceOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{10}
}
func (m *PlaceOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaceOrderReq.Unmarshal(m, b)
}
func (m *PlaceOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaceOrderReq.Marshal(b, m, deterministic)
}
func (dst *PlaceOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaceOrderReq.Merge(dst, src)
}
func (m *PlaceOrderReq) XXX_Size() int {
	return xxx_messageInfo_PlaceOrderReq.Size(m)
}
func (m *PlaceOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaceOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_PlaceOrderReq proto.InternalMessageInfo

func (m *PlaceOrderReq) GetSuperPlayerUid() uint32 {
	if m != nil {
		return m.SuperPlayerUid
	}
	return 0
}

func (m *PlaceOrderReq) GetPackageId() string {
	if m != nil {
		return m.PackageId
	}
	return ""
}

func (m *PlaceOrderReq) GetPayChannel() string {
	if m != nil {
		return m.PayChannel
	}
	return ""
}

func (m *PlaceOrderReq) GetOrderType() string {
	if m != nil {
		return m.OrderType
	}
	return ""
}

func (m *PlaceOrderReq) GetOsType() string {
	if m != nil {
		return m.OsType
	}
	return ""
}

func (m *PlaceOrderReq) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *PlaceOrderReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *PlaceOrderReq) GetBundleId() string {
	if m != nil {
		return m.BundleId
	}
	return ""
}

func (m *PlaceOrderReq) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *PlaceOrderReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *PlaceOrderReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *PlaceOrderReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *PlaceOrderReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *PlaceOrderReq) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *PlaceOrderReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *PlaceOrderReq) GetFaceAuthToken() string {
	if m != nil {
		return m.FaceAuthToken
	}
	return ""
}

func (m *PlaceOrderReq) GetFaceAuthProviderCode() string {
	if m != nil {
		return m.FaceAuthProviderCode
	}
	return ""
}

func (m *PlaceOrderReq) GetFaceAuthProviderResultData() string {
	if m != nil {
		return m.FaceAuthProviderResultData
	}
	return ""
}

func (m *PlaceOrderReq) GetFaceAuthResultToken() string {
	if m != nil {
		return m.FaceAuthResultToken
	}
	return ""
}

func (m *PlaceOrderReq) GetSuperPlayerType() SuperPlayerType {
	if m != nil {
		return m.SuperPlayerType
	}
	return SuperPlayerType_ENUM_TYPE_NORMAL
}

func (m *PlaceOrderReq) GetOriginalTransactionIds() []string {
	if m != nil {
		return m.OriginalTransactionIds
	}
	return nil
}

type PlaceOrderResp struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,"`
	Token                string   `protobuf:"bytes,2,opt,name=token,proto3" json:"token,"`
	CliOrderNo           string   `protobuf:"bytes,3,opt,name=cli_order_no,json=cliOrderNo,proto3" json:"cli_order_no,"`
	CliOrderTitle        string   `protobuf:"bytes,4,opt,name=cli_order_title,json=cliOrderTitle,proto3" json:"cli_order_title,"`
	Tsk                  string   `protobuf:"bytes,5,opt,name=tsk,proto3" json:"tsk,"`
	ChannelMap           string   `protobuf:"bytes,6,opt,name=channel_map,json=channelMap,proto3" json:"channel_map,"`
	RequestId            string   `protobuf:"bytes,7,opt,name=request_id,json=requestId,proto3" json:"request_id,"`
	AuthScene            uint32   `protobuf:"varint,8,opt,name=auth_scene,json=authScene,proto3" json:"auth_scene,"`
	FaceAuthContextJson  string   `protobuf:"bytes,9,opt,name=face_auth_context_json,json=faceAuthContextJson,proto3" json:"face_auth_context_json,"`
	IsFirstOpen          bool     `protobuf:"varint,10,opt,name=is_first_open,json=isFirstOpen,proto3" json:"is_first_open,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaceOrderResp) Reset()         { *m = PlaceOrderResp{} }
func (m *PlaceOrderResp) String() string { return proto.CompactTextString(m) }
func (*PlaceOrderResp) ProtoMessage()    {}
func (*PlaceOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{11}
}
func (m *PlaceOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaceOrderResp.Unmarshal(m, b)
}
func (m *PlaceOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaceOrderResp.Marshal(b, m, deterministic)
}
func (dst *PlaceOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaceOrderResp.Merge(dst, src)
}
func (m *PlaceOrderResp) XXX_Size() int {
	return xxx_messageInfo_PlaceOrderResp.Size(m)
}
func (m *PlaceOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaceOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_PlaceOrderResp proto.InternalMessageInfo

func (m *PlaceOrderResp) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PlaceOrderResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *PlaceOrderResp) GetCliOrderNo() string {
	if m != nil {
		return m.CliOrderNo
	}
	return ""
}

func (m *PlaceOrderResp) GetCliOrderTitle() string {
	if m != nil {
		return m.CliOrderTitle
	}
	return ""
}

func (m *PlaceOrderResp) GetTsk() string {
	if m != nil {
		return m.Tsk
	}
	return ""
}

func (m *PlaceOrderResp) GetChannelMap() string {
	if m != nil {
		return m.ChannelMap
	}
	return ""
}

func (m *PlaceOrderResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *PlaceOrderResp) GetAuthScene() uint32 {
	if m != nil {
		return m.AuthScene
	}
	return 0
}

func (m *PlaceOrderResp) GetFaceAuthContextJson() string {
	if m != nil {
		return m.FaceAuthContextJson
	}
	return ""
}

func (m *PlaceOrderResp) GetIsFirstOpen() bool {
	if m != nil {
		return m.IsFirstOpen
	}
	return false
}

type CancelOrderReq struct {
	SuperPlayerUid       uint32   `protobuf:"varint,1,opt,name=super_player_uid,json=superPlayerUid,proto3" json:"super_player_uid,"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelOrderReq) Reset()         { *m = CancelOrderReq{} }
func (m *CancelOrderReq) String() string { return proto.CompactTextString(m) }
func (*CancelOrderReq) ProtoMessage()    {}
func (*CancelOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{12}
}
func (m *CancelOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelOrderReq.Unmarshal(m, b)
}
func (m *CancelOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelOrderReq.Marshal(b, m, deterministic)
}
func (dst *CancelOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelOrderReq.Merge(dst, src)
}
func (m *CancelOrderReq) XXX_Size() int {
	return xxx_messageInfo_CancelOrderReq.Size(m)
}
func (m *CancelOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelOrderReq proto.InternalMessageInfo

func (m *CancelOrderReq) GetSuperPlayerUid() uint32 {
	if m != nil {
		return m.SuperPlayerUid
	}
	return 0
}

func (m *CancelOrderReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type CancelOrderResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelOrderResp) Reset()         { *m = CancelOrderResp{} }
func (m *CancelOrderResp) String() string { return proto.CompactTextString(m) }
func (*CancelOrderResp) ProtoMessage()    {}
func (*CancelOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{13}
}
func (m *CancelOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelOrderResp.Unmarshal(m, b)
}
func (m *CancelOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelOrderResp.Marshal(b, m, deterministic)
}
func (dst *CancelOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelOrderResp.Merge(dst, src)
}
func (m *CancelOrderResp) XXX_Size() int {
	return xxx_messageInfo_CancelOrderResp.Size(m)
}
func (m *CancelOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelOrderResp proto.InternalMessageInfo

// 自动扣款下单接口
type PlaceAutoPayOrderReq struct {
	SuperPlayerUid       uint32   `protobuf:"varint,1,opt,name=super_player_uid,json=superPlayerUid,proto3" json:"super_player_uid,"`
	ProduceId            string   `protobuf:"bytes,2,opt,name=produce_id,json=produceId,proto3" json:"produce_id,"`
	ContractId           string   `protobuf:"bytes,3,opt,name=contract_id,json=contractId,proto3" json:"contract_id,"`
	HasDiscount          bool     `protobuf:"varint,4,opt,name=has_discount,json=hasDiscount,proto3" json:"has_discount,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaceAutoPayOrderReq) Reset()         { *m = PlaceAutoPayOrderReq{} }
func (m *PlaceAutoPayOrderReq) String() string { return proto.CompactTextString(m) }
func (*PlaceAutoPayOrderReq) ProtoMessage()    {}
func (*PlaceAutoPayOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{14}
}
func (m *PlaceAutoPayOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaceAutoPayOrderReq.Unmarshal(m, b)
}
func (m *PlaceAutoPayOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaceAutoPayOrderReq.Marshal(b, m, deterministic)
}
func (dst *PlaceAutoPayOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaceAutoPayOrderReq.Merge(dst, src)
}
func (m *PlaceAutoPayOrderReq) XXX_Size() int {
	return xxx_messageInfo_PlaceAutoPayOrderReq.Size(m)
}
func (m *PlaceAutoPayOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaceAutoPayOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_PlaceAutoPayOrderReq proto.InternalMessageInfo

func (m *PlaceAutoPayOrderReq) GetSuperPlayerUid() uint32 {
	if m != nil {
		return m.SuperPlayerUid
	}
	return 0
}

func (m *PlaceAutoPayOrderReq) GetProduceId() string {
	if m != nil {
		return m.ProduceId
	}
	return ""
}

func (m *PlaceAutoPayOrderReq) GetContractId() string {
	if m != nil {
		return m.ContractId
	}
	return ""
}

func (m *PlaceAutoPayOrderReq) GetHasDiscount() bool {
	if m != nil {
		return m.HasDiscount
	}
	return false
}

type PlaceAutoPayOrderResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaceAutoPayOrderResp) Reset()         { *m = PlaceAutoPayOrderResp{} }
func (m *PlaceAutoPayOrderResp) String() string { return proto.CompactTextString(m) }
func (*PlaceAutoPayOrderResp) ProtoMessage()    {}
func (*PlaceAutoPayOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{15}
}
func (m *PlaceAutoPayOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaceAutoPayOrderResp.Unmarshal(m, b)
}
func (m *PlaceAutoPayOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaceAutoPayOrderResp.Marshal(b, m, deterministic)
}
func (dst *PlaceAutoPayOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaceAutoPayOrderResp.Merge(dst, src)
}
func (m *PlaceAutoPayOrderResp) XXX_Size() int {
	return xxx_messageInfo_PlaceAutoPayOrderResp.Size(m)
}
func (m *PlaceAutoPayOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaceAutoPayOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_PlaceAutoPayOrderResp proto.InternalMessageInfo

// 支付回调
type PayCallBackReq struct {
	OrderNo              string   `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no,"`
	PayChannel           string   `protobuf:"bytes,2,opt,name=pay_channel,json=payChannel,proto3" json:"pay_channel,"`
	TotalFee             string   `protobuf:"bytes,3,opt,name=total_fee,json=totalFee,proto3" json:"total_fee,"`
	OrderStatus          string   `protobuf:"bytes,4,opt,name=order_status,json=orderStatus,proto3" json:"order_status,"`
	CliOrderNo           string   `protobuf:"bytes,5,opt,name=cli_order_no,json=cliOrderNo,proto3" json:"cli_order_no,"`
	OtherOrderNo         string   `protobuf:"bytes,6,opt,name=other_order_no,json=otherOrderNo,proto3" json:"other_order_no,"`
	OtherStatus          string   `protobuf:"bytes,7,opt,name=other_status,json=otherStatus,proto3" json:"other_status,"`
	NotifyTime           string   `protobuf:"bytes,8,opt,name=notify_time,json=notifyTime,proto3" json:"notify_time,"`
	Uid                  string   `protobuf:"bytes,9,opt,name=uid,proto3" json:"uid,"`
	BuyerId              string   `protobuf:"bytes,10,opt,name=buyer_id,json=buyerId,proto3" json:"buyer_id,"`
	BeginTime            string   `protobuf:"bytes,11,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,"`
	EndTime              string   `protobuf:"bytes,12,opt,name=end_time,json=endTime,proto3" json:"end_time,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayCallBackReq) Reset()         { *m = PayCallBackReq{} }
func (m *PayCallBackReq) String() string { return proto.CompactTextString(m) }
func (*PayCallBackReq) ProtoMessage()    {}
func (*PayCallBackReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{16}
}
func (m *PayCallBackReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayCallBackReq.Unmarshal(m, b)
}
func (m *PayCallBackReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayCallBackReq.Marshal(b, m, deterministic)
}
func (dst *PayCallBackReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayCallBackReq.Merge(dst, src)
}
func (m *PayCallBackReq) XXX_Size() int {
	return xxx_messageInfo_PayCallBackReq.Size(m)
}
func (m *PayCallBackReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PayCallBackReq.DiscardUnknown(m)
}

var xxx_messageInfo_PayCallBackReq proto.InternalMessageInfo

func (m *PayCallBackReq) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *PayCallBackReq) GetPayChannel() string {
	if m != nil {
		return m.PayChannel
	}
	return ""
}

func (m *PayCallBackReq) GetTotalFee() string {
	if m != nil {
		return m.TotalFee
	}
	return ""
}

func (m *PayCallBackReq) GetOrderStatus() string {
	if m != nil {
		return m.OrderStatus
	}
	return ""
}

func (m *PayCallBackReq) GetCliOrderNo() string {
	if m != nil {
		return m.CliOrderNo
	}
	return ""
}

func (m *PayCallBackReq) GetOtherOrderNo() string {
	if m != nil {
		return m.OtherOrderNo
	}
	return ""
}

func (m *PayCallBackReq) GetOtherStatus() string {
	if m != nil {
		return m.OtherStatus
	}
	return ""
}

func (m *PayCallBackReq) GetNotifyTime() string {
	if m != nil {
		return m.NotifyTime
	}
	return ""
}

func (m *PayCallBackReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *PayCallBackReq) GetBuyerId() string {
	if m != nil {
		return m.BuyerId
	}
	return ""
}

func (m *PayCallBackReq) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *PayCallBackReq) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

type PayCallBackResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayCallBackResp) Reset()         { *m = PayCallBackResp{} }
func (m *PayCallBackResp) String() string { return proto.CompactTextString(m) }
func (*PayCallBackResp) ProtoMessage()    {}
func (*PayCallBackResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{17}
}
func (m *PayCallBackResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayCallBackResp.Unmarshal(m, b)
}
func (m *PayCallBackResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayCallBackResp.Marshal(b, m, deterministic)
}
func (dst *PayCallBackResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayCallBackResp.Merge(dst, src)
}
func (m *PayCallBackResp) XXX_Size() int {
	return xxx_messageInfo_PayCallBackResp.Size(m)
}
func (m *PayCallBackResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PayCallBackResp.DiscardUnknown(m)
}

var xxx_messageInfo_PayCallBackResp proto.InternalMessageInfo

// 签约回调
type NotifyContractReq struct {
	BuyerId              string   `protobuf:"bytes,1,opt,name=buyer_id,json=buyerId,proto3" json:"buyer_id,"`
	Type                 string   `protobuf:"bytes,2,opt,name=type,proto3" json:"type,"`
	ClientId             string   `protobuf:"bytes,3,opt,name=client_id,json=clientId,proto3" json:"client_id,"`
	BusinessId           string   `protobuf:"bytes,4,opt,name=business_id,json=businessId,proto3" json:"business_id,"`
	PlanId               string   `protobuf:"bytes,5,opt,name=plan_id,json=planId,proto3" json:"plan_id,"`
	Id                   string   `protobuf:"bytes,6,opt,name=id,proto3" json:"id,"`
	ContractId           string   `protobuf:"bytes,7,opt,name=contract_id,json=contractId,proto3" json:"contract_id,"`
	ContractChannel      string   `protobuf:"bytes,8,opt,name=contract_channel,json=contractChannel,proto3" json:"contract_channel,"`
	OrderNo              string   `protobuf:"bytes,9,opt,name=order_no,json=orderNo,proto3" json:"order_no,"`
	ProductId            string   `protobuf:"bytes,10,opt,name=product_id,json=productId,proto3" json:"product_id,"`
	NextPayTime          string   `protobuf:"bytes,11,opt,name=next_pay_time,json=nextPayTime,proto3" json:"next_pay_time,"`
	RealBuyerId          string   `protobuf:"bytes,12,opt,name=real_buyer_id,json=realBuyerId,proto3" json:"real_buyer_id,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyContractReq) Reset()         { *m = NotifyContractReq{} }
func (m *NotifyContractReq) String() string { return proto.CompactTextString(m) }
func (*NotifyContractReq) ProtoMessage()    {}
func (*NotifyContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{18}
}
func (m *NotifyContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyContractReq.Unmarshal(m, b)
}
func (m *NotifyContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyContractReq.Marshal(b, m, deterministic)
}
func (dst *NotifyContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyContractReq.Merge(dst, src)
}
func (m *NotifyContractReq) XXX_Size() int {
	return xxx_messageInfo_NotifyContractReq.Size(m)
}
func (m *NotifyContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyContractReq proto.InternalMessageInfo

func (m *NotifyContractReq) GetBuyerId() string {
	if m != nil {
		return m.BuyerId
	}
	return ""
}

func (m *NotifyContractReq) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *NotifyContractReq) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

func (m *NotifyContractReq) GetBusinessId() string {
	if m != nil {
		return m.BusinessId
	}
	return ""
}

func (m *NotifyContractReq) GetPlanId() string {
	if m != nil {
		return m.PlanId
	}
	return ""
}

func (m *NotifyContractReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *NotifyContractReq) GetContractId() string {
	if m != nil {
		return m.ContractId
	}
	return ""
}

func (m *NotifyContractReq) GetContractChannel() string {
	if m != nil {
		return m.ContractChannel
	}
	return ""
}

func (m *NotifyContractReq) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *NotifyContractReq) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *NotifyContractReq) GetNextPayTime() string {
	if m != nil {
		return m.NextPayTime
	}
	return ""
}

func (m *NotifyContractReq) GetRealBuyerId() string {
	if m != nil {
		return m.RealBuyerId
	}
	return ""
}

type NotifyContractResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyContractResp) Reset()         { *m = NotifyContractResp{} }
func (m *NotifyContractResp) String() string { return proto.CompactTextString(m) }
func (*NotifyContractResp) ProtoMessage()    {}
func (*NotifyContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{19}
}
func (m *NotifyContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyContractResp.Unmarshal(m, b)
}
func (m *NotifyContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyContractResp.Marshal(b, m, deterministic)
}
func (dst *NotifyContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyContractResp.Merge(dst, src)
}
func (m *NotifyContractResp) XXX_Size() int {
	return xxx_messageInfo_NotifyContractResp.Size(m)
}
func (m *NotifyContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyContractResp proto.InternalMessageInfo

type GetSuperPlayerInfoReq struct {
	SuperPlayerUid       uint32   `protobuf:"varint,1,opt,name=super_player_uid,json=superPlayerUid,proto3" json:"super_player_uid,"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSuperPlayerInfoReq) Reset()         { *m = GetSuperPlayerInfoReq{} }
func (m *GetSuperPlayerInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetSuperPlayerInfoReq) ProtoMessage()    {}
func (*GetSuperPlayerInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{20}
}
func (m *GetSuperPlayerInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSuperPlayerInfoReq.Unmarshal(m, b)
}
func (m *GetSuperPlayerInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSuperPlayerInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetSuperPlayerInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSuperPlayerInfoReq.Merge(dst, src)
}
func (m *GetSuperPlayerInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetSuperPlayerInfoReq.Size(m)
}
func (m *GetSuperPlayerInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSuperPlayerInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSuperPlayerInfoReq proto.InternalMessageInfo

func (m *GetSuperPlayerInfoReq) GetSuperPlayerUid() uint32 {
	if m != nil {
		return m.SuperPlayerUid
	}
	return 0
}

func (m *GetSuperPlayerInfoReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type SuperPlayerContract struct {
	PayChannel           string          `protobuf:"bytes,1,opt,name=pay_channel,json=payChannel,proto3" json:"pay_channel,"`
	Status               string          `protobuf:"bytes,2,opt,name=status,proto3" json:"status,"`
	PackageId            string          `protobuf:"bytes,3,opt,name=package_id,json=packageId,proto3" json:"package_id,"`
	ExtraTag             string          `protobuf:"bytes,4,opt,name=extra_tag,json=extraTag,proto3" json:"extra_tag,"`
	NextPayTimestamp     uint32          `protobuf:"varint,5,opt,name=next_pay_timestamp,json=nextPayTimestamp,proto3" json:"next_pay_timestamp,"`
	MarketId             uint32          `protobuf:"varint,6,opt,name=market_id,json=marketId,proto3" json:"market_id,"`
	ContractId           string          `protobuf:"bytes,7,opt,name=contract_id,json=contractId,proto3" json:"contract_id,"`
	SignTime             uint32          `protobuf:"varint,8,opt,name=sign_time,json=signTime,proto3" json:"sign_time,"`
	Price                float32         `protobuf:"fixed32,9,opt,name=price,proto3" json:"price,"`
	SuperPlayerType      SuperPlayerType `protobuf:"varint,10,opt,name=super_player_type,json=superPlayerType,proto3,enum=super_player_http_logic.SuperPlayerType" json:"super_player_type,"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SuperPlayerContract) Reset()         { *m = SuperPlayerContract{} }
func (m *SuperPlayerContract) String() string { return proto.CompactTextString(m) }
func (*SuperPlayerContract) ProtoMessage()    {}
func (*SuperPlayerContract) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{21}
}
func (m *SuperPlayerContract) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperPlayerContract.Unmarshal(m, b)
}
func (m *SuperPlayerContract) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperPlayerContract.Marshal(b, m, deterministic)
}
func (dst *SuperPlayerContract) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperPlayerContract.Merge(dst, src)
}
func (m *SuperPlayerContract) XXX_Size() int {
	return xxx_messageInfo_SuperPlayerContract.Size(m)
}
func (m *SuperPlayerContract) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperPlayerContract.DiscardUnknown(m)
}

var xxx_messageInfo_SuperPlayerContract proto.InternalMessageInfo

func (m *SuperPlayerContract) GetPayChannel() string {
	if m != nil {
		return m.PayChannel
	}
	return ""
}

func (m *SuperPlayerContract) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *SuperPlayerContract) GetPackageId() string {
	if m != nil {
		return m.PackageId
	}
	return ""
}

func (m *SuperPlayerContract) GetExtraTag() string {
	if m != nil {
		return m.ExtraTag
	}
	return ""
}

func (m *SuperPlayerContract) GetNextPayTimestamp() uint32 {
	if m != nil {
		return m.NextPayTimestamp
	}
	return 0
}

func (m *SuperPlayerContract) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *SuperPlayerContract) GetContractId() string {
	if m != nil {
		return m.ContractId
	}
	return ""
}

func (m *SuperPlayerContract) GetSignTime() uint32 {
	if m != nil {
		return m.SignTime
	}
	return 0
}

func (m *SuperPlayerContract) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *SuperPlayerContract) GetSuperPlayerType() SuperPlayerType {
	if m != nil {
		return m.SuperPlayerType
	}
	return SuperPlayerType_ENUM_TYPE_NORMAL
}

type GetSuperPlayerInfoResp struct {
	SuperPlayerUid                uint32                 `protobuf:"varint,1,opt,name=super_player_uid,json=superPlayerUid,proto3" json:"super_player_uid,"`
	SuperPlayerValue              int64                  `protobuf:"varint,2,opt,name=super_player_value,json=superPlayerValue,proto3" json:"super_player_value,"`
	SuperPlayerLevel              int64                  `protobuf:"varint,3,opt,name=super_player_level,json=superPlayerLevel,proto3" json:"super_player_level,"`
	BeginTimestamp                int64                  `protobuf:"varint,4,opt,name=begin_timestamp,json=beginTimestamp,proto3" json:"begin_timestamp,"`
	ExpireTimestamp               int64                  `protobuf:"varint,5,opt,name=expire_timestamp,json=expireTimestamp,proto3" json:"expire_timestamp,"`
	ExpireNotifyHour              int64                  `protobuf:"varint,6,opt,name=expire_notify_hour,json=expireNotifyHour,proto3" json:"expire_notify_hour,"`
	Account                       string                 `protobuf:"bytes,7,opt,name=account,proto3" json:"account,"`
	Nickname                      string                 `protobuf:"bytes,8,opt,name=nickname,proto3" json:"nickname,"`
	SuperPlayerContractList       []*SuperPlayerContract `protobuf:"bytes,9,rep,name=super_player_contract_list,json=superPlayerContractList,proto3" json:"super_player_contract_list,"`
	BindPhone                     bool                   `protobuf:"varint,10,opt,name=bind_phone,json=bindPhone,proto3" json:"bind_phone,"`
	YearMemberExpireTs            uint32                 `protobuf:"varint,11,opt,name=year_member_expire_ts,json=yearMemberExpireTs,proto3" json:"year_member_expire_ts,"`
	IsShowManagerContractBtn      bool                   `protobuf:"varint,12,opt,name=is_show_manager_contract_btn,json=isShowManagerContractBtn,proto3" json:"is_show_manager_contract_btn,"`
	HasExpired                    bool                   `protobuf:"varint,13,opt,name=has_expired,json=hasExpired,proto3" json:"has_expired,"`
	SvipBeginTimestamp            int64                  `protobuf:"varint,14,opt,name=svip_begin_timestamp,json=svipBeginTimestamp,proto3" json:"svip_begin_timestamp,"`
	SvipExpireTimestamp           int64                  `protobuf:"varint,15,opt,name=svip_expire_timestamp,json=svipExpireTimestamp,proto3" json:"svip_expire_timestamp,"`
	SvipYearMemberExpireTimestamp int64                  `protobuf:"varint,16,opt,name=svip_year_member_expire_timestamp,json=svipYearMemberExpireTimestamp,proto3" json:"svip_year_member_expire_timestamp,"`
	Status                        SuperPlayerStatus      `protobuf:"varint,17,opt,name=status,proto3,enum=super_player_http_logic.SuperPlayerStatus" json:"status,"`
	SvipStatus                    SuperPlayerStatus      `protobuf:"varint,18,opt,name=svip_status,json=svipStatus,proto3,enum=super_player_http_logic.SuperPlayerStatus" json:"svip_status,"`
	IsSvipFirst                   bool                   `protobuf:"varint,19,opt,name=is_svip_first,json=isSvipFirst,proto3" json:"is_svip_first,"`
	SuperPlayerLeftDays           uint32                 `protobuf:"varint,20,opt,name=super_player_left_days,json=superPlayerLeftDays,proto3" json:"super_player_left_days,"`
	SvipLeftDays                  uint32                 `protobuf:"varint,21,opt,name=svip_left_days,json=svipLeftDays,proto3" json:"svip_left_days,"`
	XXX_NoUnkeyedLiteral          struct{}               `json:"-"`
	XXX_unrecognized              []byte                 `json:"-"`
	XXX_sizecache                 int32                  `json:"-"`
}

func (m *GetSuperPlayerInfoResp) Reset()         { *m = GetSuperPlayerInfoResp{} }
func (m *GetSuperPlayerInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetSuperPlayerInfoResp) ProtoMessage()    {}
func (*GetSuperPlayerInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{22}
}
func (m *GetSuperPlayerInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSuperPlayerInfoResp.Unmarshal(m, b)
}
func (m *GetSuperPlayerInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSuperPlayerInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetSuperPlayerInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSuperPlayerInfoResp.Merge(dst, src)
}
func (m *GetSuperPlayerInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetSuperPlayerInfoResp.Size(m)
}
func (m *GetSuperPlayerInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSuperPlayerInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSuperPlayerInfoResp proto.InternalMessageInfo

func (m *GetSuperPlayerInfoResp) GetSuperPlayerUid() uint32 {
	if m != nil {
		return m.SuperPlayerUid
	}
	return 0
}

func (m *GetSuperPlayerInfoResp) GetSuperPlayerValue() int64 {
	if m != nil {
		return m.SuperPlayerValue
	}
	return 0
}

func (m *GetSuperPlayerInfoResp) GetSuperPlayerLevel() int64 {
	if m != nil {
		return m.SuperPlayerLevel
	}
	return 0
}

func (m *GetSuperPlayerInfoResp) GetBeginTimestamp() int64 {
	if m != nil {
		return m.BeginTimestamp
	}
	return 0
}

func (m *GetSuperPlayerInfoResp) GetExpireTimestamp() int64 {
	if m != nil {
		return m.ExpireTimestamp
	}
	return 0
}

func (m *GetSuperPlayerInfoResp) GetExpireNotifyHour() int64 {
	if m != nil {
		return m.ExpireNotifyHour
	}
	return 0
}

func (m *GetSuperPlayerInfoResp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetSuperPlayerInfoResp) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GetSuperPlayerInfoResp) GetSuperPlayerContractList() []*SuperPlayerContract {
	if m != nil {
		return m.SuperPlayerContractList
	}
	return nil
}

func (m *GetSuperPlayerInfoResp) GetBindPhone() bool {
	if m != nil {
		return m.BindPhone
	}
	return false
}

func (m *GetSuperPlayerInfoResp) GetYearMemberExpireTs() uint32 {
	if m != nil {
		return m.YearMemberExpireTs
	}
	return 0
}

func (m *GetSuperPlayerInfoResp) GetIsShowManagerContractBtn() bool {
	if m != nil {
		return m.IsShowManagerContractBtn
	}
	return false
}

func (m *GetSuperPlayerInfoResp) GetHasExpired() bool {
	if m != nil {
		return m.HasExpired
	}
	return false
}

func (m *GetSuperPlayerInfoResp) GetSvipBeginTimestamp() int64 {
	if m != nil {
		return m.SvipBeginTimestamp
	}
	return 0
}

func (m *GetSuperPlayerInfoResp) GetSvipExpireTimestamp() int64 {
	if m != nil {
		return m.SvipExpireTimestamp
	}
	return 0
}

func (m *GetSuperPlayerInfoResp) GetSvipYearMemberExpireTimestamp() int64 {
	if m != nil {
		return m.SvipYearMemberExpireTimestamp
	}
	return 0
}

func (m *GetSuperPlayerInfoResp) GetStatus() SuperPlayerStatus {
	if m != nil {
		return m.Status
	}
	return SuperPlayerStatus_ENUM_STATUS_NO_OPEN
}

func (m *GetSuperPlayerInfoResp) GetSvipStatus() SuperPlayerStatus {
	if m != nil {
		return m.SvipStatus
	}
	return SuperPlayerStatus_ENUM_STATUS_NO_OPEN
}

func (m *GetSuperPlayerInfoResp) GetIsSvipFirst() bool {
	if m != nil {
		return m.IsSvipFirst
	}
	return false
}

func (m *GetSuperPlayerInfoResp) GetSuperPlayerLeftDays() uint32 {
	if m != nil {
		return m.SuperPlayerLeftDays
	}
	return 0
}

func (m *GetSuperPlayerInfoResp) GetSvipLeftDays() uint32 {
	if m != nil {
		return m.SvipLeftDays
	}
	return 0
}

type CancelContractReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,"`
	ContractId           string   `protobuf:"bytes,2,opt,name=contract_id,json=contractId,proto3" json:"contract_id,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelContractReq) Reset()         { *m = CancelContractReq{} }
func (m *CancelContractReq) String() string { return proto.CompactTextString(m) }
func (*CancelContractReq) ProtoMessage()    {}
func (*CancelContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{23}
}
func (m *CancelContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelContractReq.Unmarshal(m, b)
}
func (m *CancelContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelContractReq.Marshal(b, m, deterministic)
}
func (dst *CancelContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelContractReq.Merge(dst, src)
}
func (m *CancelContractReq) XXX_Size() int {
	return xxx_messageInfo_CancelContractReq.Size(m)
}
func (m *CancelContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelContractReq proto.InternalMessageInfo

func (m *CancelContractReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CancelContractReq) GetContractId() string {
	if m != nil {
		return m.ContractId
	}
	return ""
}

type CancelContractResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelContractResp) Reset()         { *m = CancelContractResp{} }
func (m *CancelContractResp) String() string { return proto.CompactTextString(m) }
func (*CancelContractResp) ProtoMessage()    {}
func (*CancelContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{24}
}
func (m *CancelContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelContractResp.Unmarshal(m, b)
}
func (m *CancelContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelContractResp.Marshal(b, m, deterministic)
}
func (dst *CancelContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelContractResp.Merge(dst, src)
}
func (m *CancelContractResp) XXX_Size() int {
	return xxx_messageInfo_CancelContractResp.Size(m)
}
func (m *CancelContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelContractResp proto.InternalMessageInfo

// 增加会员值记录
type SuperPlayerValueRecord struct {
	SuperPlayerUid       uint32   `protobuf:"varint,1,opt,name=super_player_uid,json=superPlayerUid,proto3" json:"super_player_uid,"`
	TimeStamp            int64    `protobuf:"varint,2,opt,name=time_stamp,json=timeStamp,proto3" json:"time_stamp,"`
	Reason               string   `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,"`
	IncrValue            int64    `protobuf:"varint,4,opt,name=incr_value,json=incrValue,proto3" json:"incr_value,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SuperPlayerValueRecord) Reset()         { *m = SuperPlayerValueRecord{} }
func (m *SuperPlayerValueRecord) String() string { return proto.CompactTextString(m) }
func (*SuperPlayerValueRecord) ProtoMessage()    {}
func (*SuperPlayerValueRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{25}
}
func (m *SuperPlayerValueRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperPlayerValueRecord.Unmarshal(m, b)
}
func (m *SuperPlayerValueRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperPlayerValueRecord.Marshal(b, m, deterministic)
}
func (dst *SuperPlayerValueRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperPlayerValueRecord.Merge(dst, src)
}
func (m *SuperPlayerValueRecord) XXX_Size() int {
	return xxx_messageInfo_SuperPlayerValueRecord.Size(m)
}
func (m *SuperPlayerValueRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperPlayerValueRecord.DiscardUnknown(m)
}

var xxx_messageInfo_SuperPlayerValueRecord proto.InternalMessageInfo

func (m *SuperPlayerValueRecord) GetSuperPlayerUid() uint32 {
	if m != nil {
		return m.SuperPlayerUid
	}
	return 0
}

func (m *SuperPlayerValueRecord) GetTimeStamp() int64 {
	if m != nil {
		return m.TimeStamp
	}
	return 0
}

func (m *SuperPlayerValueRecord) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *SuperPlayerValueRecord) GetIncrValue() int64 {
	if m != nil {
		return m.IncrValue
	}
	return 0
}

// 设置红点时间
type SetLastSuperPlayerValueChangeTimeReq struct {
	SuperPlayerUid       uint32   `protobuf:"varint,1,opt,name=super_player_uid,json=superPlayerUid,proto3" json:"super_player_uid,"`
	PrevTimestamp        int64    `protobuf:"varint,2,opt,name=prev_timestamp,json=prevTimestamp,proto3" json:"prev_timestamp,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetLastSuperPlayerValueChangeTimeReq) Reset()         { *m = SetLastSuperPlayerValueChangeTimeReq{} }
func (m *SetLastSuperPlayerValueChangeTimeReq) String() string { return proto.CompactTextString(m) }
func (*SetLastSuperPlayerValueChangeTimeReq) ProtoMessage()    {}
func (*SetLastSuperPlayerValueChangeTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{26}
}
func (m *SetLastSuperPlayerValueChangeTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLastSuperPlayerValueChangeTimeReq.Unmarshal(m, b)
}
func (m *SetLastSuperPlayerValueChangeTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLastSuperPlayerValueChangeTimeReq.Marshal(b, m, deterministic)
}
func (dst *SetLastSuperPlayerValueChangeTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLastSuperPlayerValueChangeTimeReq.Merge(dst, src)
}
func (m *SetLastSuperPlayerValueChangeTimeReq) XXX_Size() int {
	return xxx_messageInfo_SetLastSuperPlayerValueChangeTimeReq.Size(m)
}
func (m *SetLastSuperPlayerValueChangeTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLastSuperPlayerValueChangeTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetLastSuperPlayerValueChangeTimeReq proto.InternalMessageInfo

func (m *SetLastSuperPlayerValueChangeTimeReq) GetSuperPlayerUid() uint32 {
	if m != nil {
		return m.SuperPlayerUid
	}
	return 0
}

func (m *SetLastSuperPlayerValueChangeTimeReq) GetPrevTimestamp() int64 {
	if m != nil {
		return m.PrevTimestamp
	}
	return 0
}

type SetLastSuperPlayerValueChangeTimeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetLastSuperPlayerValueChangeTimeResp) Reset()         { *m = SetLastSuperPlayerValueChangeTimeResp{} }
func (m *SetLastSuperPlayerValueChangeTimeResp) String() string { return proto.CompactTextString(m) }
func (*SetLastSuperPlayerValueChangeTimeResp) ProtoMessage()    {}
func (*SetLastSuperPlayerValueChangeTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{27}
}
func (m *SetLastSuperPlayerValueChangeTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLastSuperPlayerValueChangeTimeResp.Unmarshal(m, b)
}
func (m *SetLastSuperPlayerValueChangeTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLastSuperPlayerValueChangeTimeResp.Marshal(b, m, deterministic)
}
func (dst *SetLastSuperPlayerValueChangeTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLastSuperPlayerValueChangeTimeResp.Merge(dst, src)
}
func (m *SetLastSuperPlayerValueChangeTimeResp) XXX_Size() int {
	return xxx_messageInfo_SetLastSuperPlayerValueChangeTimeResp.Size(m)
}
func (m *SetLastSuperPlayerValueChangeTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLastSuperPlayerValueChangeTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetLastSuperPlayerValueChangeTimeResp proto.InternalMessageInfo

// 充值记录
type PayRecord struct {
	TimeStamp            int64    `protobuf:"varint,1,opt,name=time_stamp,json=timeStamp,proto3" json:"time_stamp,"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,"`
	Price                float32  `protobuf:"fixed32,3,opt,name=price,proto3" json:"price,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayRecord) Reset()         { *m = PayRecord{} }
func (m *PayRecord) String() string { return proto.CompactTextString(m) }
func (*PayRecord) ProtoMessage()    {}
func (*PayRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{28}
}
func (m *PayRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayRecord.Unmarshal(m, b)
}
func (m *PayRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayRecord.Marshal(b, m, deterministic)
}
func (dst *PayRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayRecord.Merge(dst, src)
}
func (m *PayRecord) XXX_Size() int {
	return xxx_messageInfo_PayRecord.Size(m)
}
func (m *PayRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_PayRecord.DiscardUnknown(m)
}

var xxx_messageInfo_PayRecord proto.InternalMessageInfo

func (m *PayRecord) GetTimeStamp() int64 {
	if m != nil {
		return m.TimeStamp
	}
	return 0
}

func (m *PayRecord) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *PayRecord) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

type GetSuperPlayerPayRecordReq struct {
	SuperPlayerUid       uint32   `protobuf:"varint,1,opt,name=super_player_uid,json=superPlayerUid,proto3" json:"super_player_uid,"`
	Off                  int64    `protobuf:"varint,2,opt,name=off,proto3" json:"off,"`
	Count                int64    `protobuf:"varint,3,opt,name=count,proto3" json:"count,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSuperPlayerPayRecordReq) Reset()         { *m = GetSuperPlayerPayRecordReq{} }
func (m *GetSuperPlayerPayRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetSuperPlayerPayRecordReq) ProtoMessage()    {}
func (*GetSuperPlayerPayRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{29}
}
func (m *GetSuperPlayerPayRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSuperPlayerPayRecordReq.Unmarshal(m, b)
}
func (m *GetSuperPlayerPayRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSuperPlayerPayRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetSuperPlayerPayRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSuperPlayerPayRecordReq.Merge(dst, src)
}
func (m *GetSuperPlayerPayRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetSuperPlayerPayRecordReq.Size(m)
}
func (m *GetSuperPlayerPayRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSuperPlayerPayRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSuperPlayerPayRecordReq proto.InternalMessageInfo

func (m *GetSuperPlayerPayRecordReq) GetSuperPlayerUid() uint32 {
	if m != nil {
		return m.SuperPlayerUid
	}
	return 0
}

func (m *GetSuperPlayerPayRecordReq) GetOff() int64 {
	if m != nil {
		return m.Off
	}
	return 0
}

func (m *GetSuperPlayerPayRecordReq) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetSuperPlayerPayRecordResp struct {
	PayRecordList        []*PayRecord `protobuf:"bytes,1,rep,name=pay_record_list,json=payRecordList,proto3" json:"pay_record_list,"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSuperPlayerPayRecordResp) Reset()         { *m = GetSuperPlayerPayRecordResp{} }
func (m *GetSuperPlayerPayRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetSuperPlayerPayRecordResp) ProtoMessage()    {}
func (*GetSuperPlayerPayRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{30}
}
func (m *GetSuperPlayerPayRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSuperPlayerPayRecordResp.Unmarshal(m, b)
}
func (m *GetSuperPlayerPayRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSuperPlayerPayRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetSuperPlayerPayRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSuperPlayerPayRecordResp.Merge(dst, src)
}
func (m *GetSuperPlayerPayRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetSuperPlayerPayRecordResp.Size(m)
}
func (m *GetSuperPlayerPayRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSuperPlayerPayRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSuperPlayerPayRecordResp proto.InternalMessageInfo

func (m *GetSuperPlayerPayRecordResp) GetPayRecordList() []*PayRecord {
	if m != nil {
		return m.PayRecordList
	}
	return nil
}

type GetSuperPlayerValueRecordReq struct {
	SuperPlayerUid       uint32   `protobuf:"varint,1,opt,name=super_player_uid,json=superPlayerUid,proto3" json:"super_player_uid,"`
	Off                  int64    `protobuf:"varint,2,opt,name=off,proto3" json:"off,"`
	Count                int64    `protobuf:"varint,3,opt,name=count,proto3" json:"count,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSuperPlayerValueRecordReq) Reset()         { *m = GetSuperPlayerValueRecordReq{} }
func (m *GetSuperPlayerValueRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetSuperPlayerValueRecordReq) ProtoMessage()    {}
func (*GetSuperPlayerValueRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{31}
}
func (m *GetSuperPlayerValueRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSuperPlayerValueRecordReq.Unmarshal(m, b)
}
func (m *GetSuperPlayerValueRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSuperPlayerValueRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetSuperPlayerValueRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSuperPlayerValueRecordReq.Merge(dst, src)
}
func (m *GetSuperPlayerValueRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetSuperPlayerValueRecordReq.Size(m)
}
func (m *GetSuperPlayerValueRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSuperPlayerValueRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSuperPlayerValueRecordReq proto.InternalMessageInfo

func (m *GetSuperPlayerValueRecordReq) GetSuperPlayerUid() uint32 {
	if m != nil {
		return m.SuperPlayerUid
	}
	return 0
}

func (m *GetSuperPlayerValueRecordReq) GetOff() int64 {
	if m != nil {
		return m.Off
	}
	return 0
}

func (m *GetSuperPlayerValueRecordReq) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetSuperPlayerValueRecordResp struct {
	RecordList           []*SuperPlayerValueRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,"`
	TodayIncrValue       int64                     `protobuf:"varint,2,opt,name=today_incr_value,json=todayIncrValue,proto3" json:"today_incr_value,"`
	PrevTimestamp        int64                     `protobuf:"varint,3,opt,name=prev_timestamp,json=prevTimestamp,proto3" json:"prev_timestamp,"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetSuperPlayerValueRecordResp) Reset()         { *m = GetSuperPlayerValueRecordResp{} }
func (m *GetSuperPlayerValueRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetSuperPlayerValueRecordResp) ProtoMessage()    {}
func (*GetSuperPlayerValueRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{32}
}
func (m *GetSuperPlayerValueRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSuperPlayerValueRecordResp.Unmarshal(m, b)
}
func (m *GetSuperPlayerValueRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSuperPlayerValueRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetSuperPlayerValueRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSuperPlayerValueRecordResp.Merge(dst, src)
}
func (m *GetSuperPlayerValueRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetSuperPlayerValueRecordResp.Size(m)
}
func (m *GetSuperPlayerValueRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSuperPlayerValueRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSuperPlayerValueRecordResp proto.InternalMessageInfo

func (m *GetSuperPlayerValueRecordResp) GetRecordList() []*SuperPlayerValueRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetSuperPlayerValueRecordResp) GetTodayIncrValue() int64 {
	if m != nil {
		return m.TodayIncrValue
	}
	return 0
}

func (m *GetSuperPlayerValueRecordResp) GetPrevTimestamp() int64 {
	if m != nil {
		return m.PrevTimestamp
	}
	return 0
}

// 会员任务，获取用户所有会员任务
type GetSuperPlayerMissionReq struct {
	SuperPlayerUid       uint32   `protobuf:"varint,1,opt,name=super_player_uid,json=superPlayerUid,proto3" json:"super_player_uid,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSuperPlayerMissionReq) Reset()         { *m = GetSuperPlayerMissionReq{} }
func (m *GetSuperPlayerMissionReq) String() string { return proto.CompactTextString(m) }
func (*GetSuperPlayerMissionReq) ProtoMessage()    {}
func (*GetSuperPlayerMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{33}
}
func (m *GetSuperPlayerMissionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSuperPlayerMissionReq.Unmarshal(m, b)
}
func (m *GetSuperPlayerMissionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSuperPlayerMissionReq.Marshal(b, m, deterministic)
}
func (dst *GetSuperPlayerMissionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSuperPlayerMissionReq.Merge(dst, src)
}
func (m *GetSuperPlayerMissionReq) XXX_Size() int {
	return xxx_messageInfo_GetSuperPlayerMissionReq.Size(m)
}
func (m *GetSuperPlayerMissionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSuperPlayerMissionReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSuperPlayerMissionReq proto.InternalMessageInfo

func (m *GetSuperPlayerMissionReq) GetSuperPlayerUid() uint32 {
	if m != nil {
		return m.SuperPlayerUid
	}
	return 0
}

type GetSuperPlayerMissionResp struct {
	Mission              []*MissionDetail `protobuf:"bytes,1,rep,name=mission,proto3" json:"mission,"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetSuperPlayerMissionResp) Reset()         { *m = GetSuperPlayerMissionResp{} }
func (m *GetSuperPlayerMissionResp) String() string { return proto.CompactTextString(m) }
func (*GetSuperPlayerMissionResp) ProtoMessage()    {}
func (*GetSuperPlayerMissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{34}
}
func (m *GetSuperPlayerMissionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSuperPlayerMissionResp.Unmarshal(m, b)
}
func (m *GetSuperPlayerMissionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSuperPlayerMissionResp.Marshal(b, m, deterministic)
}
func (dst *GetSuperPlayerMissionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSuperPlayerMissionResp.Merge(dst, src)
}
func (m *GetSuperPlayerMissionResp) XXX_Size() int {
	return xxx_messageInfo_GetSuperPlayerMissionResp.Size(m)
}
func (m *GetSuperPlayerMissionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSuperPlayerMissionResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSuperPlayerMissionResp proto.InternalMessageInfo

func (m *GetSuperPlayerMissionResp) GetMission() []*MissionDetail {
	if m != nil {
		return m.Mission
	}
	return nil
}

type AddSuperPlayerValueReq struct {
	SuperPlayerUid       uint32   `protobuf:"varint,1,opt,name=super_player_uid,json=superPlayerUid,proto3" json:"super_player_uid,"`
	SuperPlayerValue     int64    `protobuf:"varint,2,opt,name=super_player_value,json=superPlayerValue,proto3" json:"super_player_value,"`
	OrderId              string   `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,"`
	Reason               string   `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSuperPlayerValueReq) Reset()         { *m = AddSuperPlayerValueReq{} }
func (m *AddSuperPlayerValueReq) String() string { return proto.CompactTextString(m) }
func (*AddSuperPlayerValueReq) ProtoMessage()    {}
func (*AddSuperPlayerValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{35}
}
func (m *AddSuperPlayerValueReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSuperPlayerValueReq.Unmarshal(m, b)
}
func (m *AddSuperPlayerValueReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSuperPlayerValueReq.Marshal(b, m, deterministic)
}
func (dst *AddSuperPlayerValueReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSuperPlayerValueReq.Merge(dst, src)
}
func (m *AddSuperPlayerValueReq) XXX_Size() int {
	return xxx_messageInfo_AddSuperPlayerValueReq.Size(m)
}
func (m *AddSuperPlayerValueReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSuperPlayerValueReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddSuperPlayerValueReq proto.InternalMessageInfo

func (m *AddSuperPlayerValueReq) GetSuperPlayerUid() uint32 {
	if m != nil {
		return m.SuperPlayerUid
	}
	return 0
}

func (m *AddSuperPlayerValueReq) GetSuperPlayerValue() int64 {
	if m != nil {
		return m.SuperPlayerValue
	}
	return 0
}

func (m *AddSuperPlayerValueReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AddSuperPlayerValueReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type AddSuperPlayerValueResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSuperPlayerValueResp) Reset()         { *m = AddSuperPlayerValueResp{} }
func (m *AddSuperPlayerValueResp) String() string { return proto.CompactTextString(m) }
func (*AddSuperPlayerValueResp) ProtoMessage()    {}
func (*AddSuperPlayerValueResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{36}
}
func (m *AddSuperPlayerValueResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSuperPlayerValueResp.Unmarshal(m, b)
}
func (m *AddSuperPlayerValueResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSuperPlayerValueResp.Marshal(b, m, deterministic)
}
func (dst *AddSuperPlayerValueResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSuperPlayerValueResp.Merge(dst, src)
}
func (m *AddSuperPlayerValueResp) XXX_Size() int {
	return xxx_messageInfo_AddSuperPlayerValueResp.Size(m)
}
func (m *AddSuperPlayerValueResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSuperPlayerValueResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddSuperPlayerValueResp proto.InternalMessageInfo

type GetOrderStatusReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderStatusReq) Reset()         { *m = GetOrderStatusReq{} }
func (m *GetOrderStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderStatusReq) ProtoMessage()    {}
func (*GetOrderStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{37}
}
func (m *GetOrderStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderStatusReq.Unmarshal(m, b)
}
func (m *GetOrderStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetOrderStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderStatusReq.Merge(dst, src)
}
func (m *GetOrderStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetOrderStatusReq.Size(m)
}
func (m *GetOrderStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderStatusReq proto.InternalMessageInfo

func (m *GetOrderStatusReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetOrderStatusResp struct {
	Status               EnumOrderStatus `protobuf:"varint,1,opt,name=status,proto3,enum=super_player_http_logic.EnumOrderStatus" json:"status,"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetOrderStatusResp) Reset()         { *m = GetOrderStatusResp{} }
func (m *GetOrderStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderStatusResp) ProtoMessage()    {}
func (*GetOrderStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{38}
}
func (m *GetOrderStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderStatusResp.Unmarshal(m, b)
}
func (m *GetOrderStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetOrderStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderStatusResp.Merge(dst, src)
}
func (m *GetOrderStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetOrderStatusResp.Size(m)
}
func (m *GetOrderStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderStatusResp proto.InternalMessageInfo

func (m *GetOrderStatusResp) GetStatus() EnumOrderStatus {
	if m != nil {
		return m.Status
	}
	return EnumOrderStatus_ORDER_INIT
}

type MissionDetail struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,"`
	MissionType          uint32   `protobuf:"varint,3,opt,name=mission_type,json=missionType,proto3" json:"mission_type,"`
	AwardValue           uint32   `protobuf:"varint,4,opt,name=award_value,json=awardValue,proto3" json:"award_value,"`
	RequiredCount        uint32   `protobuf:"varint,5,opt,name=required_count,json=requiredCount,proto3" json:"required_count,"`
	FinishCount          uint32   `protobuf:"varint,6,opt,name=finish_count,json=finishCount,proto3" json:"finish_count,"`
	MissionStatus        uint32   `protobuf:"varint,7,opt,name=mission_status,json=missionStatus,proto3" json:"mission_status,"`
	MissionIcon          string   `protobuf:"bytes,8,opt,name=mission_icon,json=missionIcon,proto3" json:"mission_icon,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MissionDetail) Reset()         { *m = MissionDetail{} }
func (m *MissionDetail) String() string { return proto.CompactTextString(m) }
func (*MissionDetail) ProtoMessage()    {}
func (*MissionDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{39}
}
func (m *MissionDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MissionDetail.Unmarshal(m, b)
}
func (m *MissionDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MissionDetail.Marshal(b, m, deterministic)
}
func (dst *MissionDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MissionDetail.Merge(dst, src)
}
func (m *MissionDetail) XXX_Size() int {
	return xxx_messageInfo_MissionDetail.Size(m)
}
func (m *MissionDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_MissionDetail.DiscardUnknown(m)
}

var xxx_messageInfo_MissionDetail proto.InternalMessageInfo

func (m *MissionDetail) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MissionDetail) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MissionDetail) GetMissionType() uint32 {
	if m != nil {
		return m.MissionType
	}
	return 0
}

func (m *MissionDetail) GetAwardValue() uint32 {
	if m != nil {
		return m.AwardValue
	}
	return 0
}

func (m *MissionDetail) GetRequiredCount() uint32 {
	if m != nil {
		return m.RequiredCount
	}
	return 0
}

func (m *MissionDetail) GetFinishCount() uint32 {
	if m != nil {
		return m.FinishCount
	}
	return 0
}

func (m *MissionDetail) GetMissionStatus() uint32 {
	if m != nil {
		return m.MissionStatus
	}
	return 0
}

func (m *MissionDetail) GetMissionIcon() string {
	if m != nil {
		return m.MissionIcon
	}
	return ""
}

// 装扮资源配置
type DressResInfo struct {
	ListUrl              string   `protobuf:"bytes,1,opt,name=list_url,json=listUrl,proto3" json:"list_url,"`
	PreviewUrl           string   `protobuf:"bytes,2,opt,name=preview_url,json=previewUrl,proto3" json:"preview_url,"`
	AudioUrl             string   `protobuf:"bytes,3,opt,name=audio_url,json=audioUrl,proto3" json:"audio_url,"`
	VideoUrl             string   `protobuf:"bytes,4,opt,name=video_url,json=videoUrl,proto3" json:"video_url,"`
	TopUrl               string   `protobuf:"bytes,5,opt,name=top_url,json=topUrl,proto3" json:"top_url,"`
	WebStatic            string   `protobuf:"bytes,6,opt,name=web_static,json=webStatic,proto3" json:"web_static,"`
	ComDesc              string   `protobuf:"bytes,7,opt,name=com_desc,json=comDesc,proto3" json:"com_desc,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DressResInfo) Reset()         { *m = DressResInfo{} }
func (m *DressResInfo) String() string { return proto.CompactTextString(m) }
func (*DressResInfo) ProtoMessage()    {}
func (*DressResInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{40}
}
func (m *DressResInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DressResInfo.Unmarshal(m, b)
}
func (m *DressResInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DressResInfo.Marshal(b, m, deterministic)
}
func (dst *DressResInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DressResInfo.Merge(dst, src)
}
func (m *DressResInfo) XXX_Size() int {
	return xxx_messageInfo_DressResInfo.Size(m)
}
func (m *DressResInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DressResInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DressResInfo proto.InternalMessageInfo

func (m *DressResInfo) GetListUrl() string {
	if m != nil {
		return m.ListUrl
	}
	return ""
}

func (m *DressResInfo) GetPreviewUrl() string {
	if m != nil {
		return m.PreviewUrl
	}
	return ""
}

func (m *DressResInfo) GetAudioUrl() string {
	if m != nil {
		return m.AudioUrl
	}
	return ""
}

func (m *DressResInfo) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *DressResInfo) GetTopUrl() string {
	if m != nil {
		return m.TopUrl
	}
	return ""
}

func (m *DressResInfo) GetWebStatic() string {
	if m != nil {
		return m.WebStatic
	}
	return ""
}

func (m *DressResInfo) GetComDesc() string {
	if m != nil {
		return m.ComDesc
	}
	return ""
}

// 会员装扮配置
type DressInfo struct {
	Id                   uint32        `protobuf:"varint,1,opt,name=id,proto3" json:"id,"`
	Level                uint32        `protobuf:"varint,2,opt,name=level,proto3" json:"level,"`
	IsUse                bool          `protobuf:"varint,3,opt,name=is_use,json=isUse,proto3" json:"is_use,"`
	Name                 string        `protobuf:"bytes,4,opt,name=name,proto3" json:"name,"`
	Desc                 string        `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,"`
	ResInfo              *DressResInfo `protobuf:"bytes,6,opt,name=res_info,json=resInfo,proto3" json:"res_info,"`
	IsExperience         bool          `protobuf:"varint,7,opt,name=is_experience,json=isExperience,proto3" json:"is_experience,"`
	ExperienceEndTime    int64         `protobuf:"varint,8,opt,name=experience_end_time,json=experienceEndTime,proto3" json:"experience_end_time,"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DressInfo) Reset()         { *m = DressInfo{} }
func (m *DressInfo) String() string { return proto.CompactTextString(m) }
func (*DressInfo) ProtoMessage()    {}
func (*DressInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{41}
}
func (m *DressInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DressInfo.Unmarshal(m, b)
}
func (m *DressInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DressInfo.Marshal(b, m, deterministic)
}
func (dst *DressInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DressInfo.Merge(dst, src)
}
func (m *DressInfo) XXX_Size() int {
	return xxx_messageInfo_DressInfo.Size(m)
}
func (m *DressInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DressInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DressInfo proto.InternalMessageInfo

func (m *DressInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DressInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *DressInfo) GetIsUse() bool {
	if m != nil {
		return m.IsUse
	}
	return false
}

func (m *DressInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DressInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *DressInfo) GetResInfo() *DressResInfo {
	if m != nil {
		return m.ResInfo
	}
	return nil
}

func (m *DressInfo) GetIsExperience() bool {
	if m != nil {
		return m.IsExperience
	}
	return false
}

func (m *DressInfo) GetExperienceEndTime() int64 {
	if m != nil {
		return m.ExperienceEndTime
	}
	return 0
}

// 获取用户会员装扮
type GetUserDressListReq struct {
	Uid                  string    `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,"`
	Type                 DressType `protobuf:"varint,2,opt,name=type,proto3,enum=super_player_http_logic.DressType" json:"type,"`
	ToAccount            string    `protobuf:"bytes,3,opt,name=to_account,json=toAccount,proto3" json:"to_account,"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetUserDressListReq) Reset()         { *m = GetUserDressListReq{} }
func (m *GetUserDressListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDressListReq) ProtoMessage()    {}
func (*GetUserDressListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{42}
}
func (m *GetUserDressListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDressListReq.Unmarshal(m, b)
}
func (m *GetUserDressListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDressListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDressListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDressListReq.Merge(dst, src)
}
func (m *GetUserDressListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDressListReq.Size(m)
}
func (m *GetUserDressListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDressListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDressListReq proto.InternalMessageInfo

func (m *GetUserDressListReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetUserDressListReq) GetType() DressType {
	if m != nil {
		return m.Type
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *GetUserDressListReq) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

type GetUserDressListResp struct {
	InfoList             []*DressInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserDressListResp) Reset()         { *m = GetUserDressListResp{} }
func (m *GetUserDressListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDressListResp) ProtoMessage()    {}
func (*GetUserDressListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{43}
}
func (m *GetUserDressListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDressListResp.Unmarshal(m, b)
}
func (m *GetUserDressListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDressListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDressListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDressListResp.Merge(dst, src)
}
func (m *GetUserDressListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDressListResp.Size(m)
}
func (m *GetUserDressListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDressListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDressListResp proto.InternalMessageInfo

func (m *GetUserDressListResp) GetInfoList() []*DressInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 设置使用装扮
type SetDressInUseReq struct {
	Uid                  string    `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,"`
	DressType            DressType `protobuf:"varint,2,opt,name=dress_type,json=dressType,proto3,enum=super_player_http_logic.DressType" json:"dress_type,"`
	DressId              uint32    `protobuf:"varint,3,opt,name=dress_id,json=dressId,proto3" json:"dress_id,"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SetDressInUseReq) Reset()         { *m = SetDressInUseReq{} }
func (m *SetDressInUseReq) String() string { return proto.CompactTextString(m) }
func (*SetDressInUseReq) ProtoMessage()    {}
func (*SetDressInUseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{44}
}
func (m *SetDressInUseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDressInUseReq.Unmarshal(m, b)
}
func (m *SetDressInUseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDressInUseReq.Marshal(b, m, deterministic)
}
func (dst *SetDressInUseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDressInUseReq.Merge(dst, src)
}
func (m *SetDressInUseReq) XXX_Size() int {
	return xxx_messageInfo_SetDressInUseReq.Size(m)
}
func (m *SetDressInUseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDressInUseReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetDressInUseReq proto.InternalMessageInfo

func (m *SetDressInUseReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *SetDressInUseReq) GetDressType() DressType {
	if m != nil {
		return m.DressType
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *SetDressInUseReq) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

type SetDressInUseResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDressInUseResp) Reset()         { *m = SetDressInUseResp{} }
func (m *SetDressInUseResp) String() string { return proto.CompactTextString(m) }
func (*SetDressInUseResp) ProtoMessage()    {}
func (*SetDressInUseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{45}
}
func (m *SetDressInUseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDressInUseResp.Unmarshal(m, b)
}
func (m *SetDressInUseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDressInUseResp.Marshal(b, m, deterministic)
}
func (dst *SetDressInUseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDressInUseResp.Merge(dst, src)
}
func (m *SetDressInUseResp) XXX_Size() int {
	return xxx_messageInfo_SetDressInUseResp.Size(m)
}
func (m *SetDressInUseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDressInUseResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetDressInUseResp proto.InternalMessageInfo

// 设置IM聊天背景特殊装扮
type SetChatSpecialDressInUseReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,"`
	DressId              uint32   `protobuf:"varint,2,opt,name=dress_id,json=dressId,proto3" json:"dress_id,"`
	ToAccount            string   `protobuf:"bytes,3,opt,name=to_account,json=toAccount,proto3" json:"to_account,"`
	ReplaceAccount       string   `protobuf:"bytes,4,opt,name=replace_account,json=replaceAccount,proto3" json:"replace_account,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChatSpecialDressInUseReq) Reset()         { *m = SetChatSpecialDressInUseReq{} }
func (m *SetChatSpecialDressInUseReq) String() string { return proto.CompactTextString(m) }
func (*SetChatSpecialDressInUseReq) ProtoMessage()    {}
func (*SetChatSpecialDressInUseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{46}
}
func (m *SetChatSpecialDressInUseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChatSpecialDressInUseReq.Unmarshal(m, b)
}
func (m *SetChatSpecialDressInUseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChatSpecialDressInUseReq.Marshal(b, m, deterministic)
}
func (dst *SetChatSpecialDressInUseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChatSpecialDressInUseReq.Merge(dst, src)
}
func (m *SetChatSpecialDressInUseReq) XXX_Size() int {
	return xxx_messageInfo_SetChatSpecialDressInUseReq.Size(m)
}
func (m *SetChatSpecialDressInUseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChatSpecialDressInUseReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChatSpecialDressInUseReq proto.InternalMessageInfo

func (m *SetChatSpecialDressInUseReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *SetChatSpecialDressInUseReq) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

func (m *SetChatSpecialDressInUseReq) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

func (m *SetChatSpecialDressInUseReq) GetReplaceAccount() string {
	if m != nil {
		return m.ReplaceAccount
	}
	return ""
}

type SetChatSpecialDressInUseResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChatSpecialDressInUseResp) Reset()         { *m = SetChatSpecialDressInUseResp{} }
func (m *SetChatSpecialDressInUseResp) String() string { return proto.CompactTextString(m) }
func (*SetChatSpecialDressInUseResp) ProtoMessage()    {}
func (*SetChatSpecialDressInUseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{47}
}
func (m *SetChatSpecialDressInUseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChatSpecialDressInUseResp.Unmarshal(m, b)
}
func (m *SetChatSpecialDressInUseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChatSpecialDressInUseResp.Marshal(b, m, deterministic)
}
func (dst *SetChatSpecialDressInUseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChatSpecialDressInUseResp.Merge(dst, src)
}
func (m *SetChatSpecialDressInUseResp) XXX_Size() int {
	return xxx_messageInfo_SetChatSpecialDressInUseResp.Size(m)
}
func (m *SetChatSpecialDressInUseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChatSpecialDressInUseResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChatSpecialDressInUseResp proto.InternalMessageInfo

// 获取用户会员装扮历史记录
type GetUserDressHistoryReq struct {
	Uid                  string    `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,"`
	Type                 DressType `protobuf:"varint,2,opt,name=type,proto3,enum=super_player_http_logic.DressType" json:"type,"`
	Offset               uint32    `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,"`
	Limit                uint32    `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetUserDressHistoryReq) Reset()         { *m = GetUserDressHistoryReq{} }
func (m *GetUserDressHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDressHistoryReq) ProtoMessage()    {}
func (*GetUserDressHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{48}
}
func (m *GetUserDressHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDressHistoryReq.Unmarshal(m, b)
}
func (m *GetUserDressHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDressHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDressHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDressHistoryReq.Merge(dst, src)
}
func (m *GetUserDressHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDressHistoryReq.Size(m)
}
func (m *GetUserDressHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDressHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDressHistoryReq proto.InternalMessageInfo

func (m *GetUserDressHistoryReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetUserDressHistoryReq) GetType() DressType {
	if m != nil {
		return m.Type
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *GetUserDressHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetUserDressHistoryReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

// 装扮历史记录
type UserDressHistory struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,"`
	CreateTime           uint32   `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,"`
	Removed              uint32   `protobuf:"varint,3,opt,name=removed,proto3" json:"removed,"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,"`
	WebList              string   `protobuf:"bytes,5,opt,name=web_list,json=webList,proto3" json:"web_list,"`
	Level                uint32   `protobuf:"varint,6,opt,name=level,proto3" json:"level,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserDressHistory) Reset()         { *m = UserDressHistory{} }
func (m *UserDressHistory) String() string { return proto.CompactTextString(m) }
func (*UserDressHistory) ProtoMessage()    {}
func (*UserDressHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{49}
}
func (m *UserDressHistory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDressHistory.Unmarshal(m, b)
}
func (m *UserDressHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDressHistory.Marshal(b, m, deterministic)
}
func (dst *UserDressHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDressHistory.Merge(dst, src)
}
func (m *UserDressHistory) XXX_Size() int {
	return xxx_messageInfo_UserDressHistory.Size(m)
}
func (m *UserDressHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDressHistory.DiscardUnknown(m)
}

var xxx_messageInfo_UserDressHistory proto.InternalMessageInfo

func (m *UserDressHistory) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserDressHistory) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *UserDressHistory) GetRemoved() uint32 {
	if m != nil {
		return m.Removed
	}
	return 0
}

func (m *UserDressHistory) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UserDressHistory) GetWebList() string {
	if m != nil {
		return m.WebList
	}
	return ""
}

func (m *UserDressHistory) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 获取用户会员装扮历史记录
type GetUserDressHistoryResp struct {
	Uid                  string              `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,"`
	Type                 DressType           `protobuf:"varint,2,opt,name=type,proto3,enum=super_player_http_logic.DressType" json:"type,"`
	Offset               uint32              `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,"`
	Limit                uint32              `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,"`
	HistoryList          []*UserDressHistory `protobuf:"bytes,5,rep,name=history_list,json=historyList,proto3" json:"history_list,"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetUserDressHistoryResp) Reset()         { *m = GetUserDressHistoryResp{} }
func (m *GetUserDressHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDressHistoryResp) ProtoMessage()    {}
func (*GetUserDressHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{50}
}
func (m *GetUserDressHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDressHistoryResp.Unmarshal(m, b)
}
func (m *GetUserDressHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDressHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDressHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDressHistoryResp.Merge(dst, src)
}
func (m *GetUserDressHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDressHistoryResp.Size(m)
}
func (m *GetUserDressHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDressHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDressHistoryResp proto.InternalMessageInfo

func (m *GetUserDressHistoryResp) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetUserDressHistoryResp) GetType() DressType {
	if m != nil {
		return m.Type
	}
	return DressType_DRESS_TYPE_UNSPECIFIC
}

func (m *GetUserDressHistoryResp) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetUserDressHistoryResp) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetUserDressHistoryResp) GetHistoryList() []*UserDressHistory {
	if m != nil {
		return m.HistoryList
	}
	return nil
}

// 会员身份提示弹窗
type SuperPlayerIdentityPopupReq struct {
	ReqType              uint32   `protobuf:"varint,1,opt,name=req_type,json=reqType,proto3" json:"req_type,"`
	Uid                  string   `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SuperPlayerIdentityPopupReq) Reset()         { *m = SuperPlayerIdentityPopupReq{} }
func (m *SuperPlayerIdentityPopupReq) String() string { return proto.CompactTextString(m) }
func (*SuperPlayerIdentityPopupReq) ProtoMessage()    {}
func (*SuperPlayerIdentityPopupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{51}
}
func (m *SuperPlayerIdentityPopupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperPlayerIdentityPopupReq.Unmarshal(m, b)
}
func (m *SuperPlayerIdentityPopupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperPlayerIdentityPopupReq.Marshal(b, m, deterministic)
}
func (dst *SuperPlayerIdentityPopupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperPlayerIdentityPopupReq.Merge(dst, src)
}
func (m *SuperPlayerIdentityPopupReq) XXX_Size() int {
	return xxx_messageInfo_SuperPlayerIdentityPopupReq.Size(m)
}
func (m *SuperPlayerIdentityPopupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperPlayerIdentityPopupReq.DiscardUnknown(m)
}

var xxx_messageInfo_SuperPlayerIdentityPopupReq proto.InternalMessageInfo

func (m *SuperPlayerIdentityPopupReq) GetReqType() uint32 {
	if m != nil {
		return m.ReqType
	}
	return 0
}

func (m *SuperPlayerIdentityPopupReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type SuperPlayerIdentityPopupResp struct {
	IsNeedPush           bool     `protobuf:"varint,1,opt,name=is_need_push,json=isNeedPush,proto3" json:"is_need_push,"`
	BeforeLv             uint32   `protobuf:"varint,2,opt,name=before_lv,json=beforeLv,proto3" json:"before_lv,"`
	AfterLv              uint32   `protobuf:"varint,3,opt,name=after_lv,json=afterLv,proto3" json:"after_lv,"`
	PopUpMsg             string   `protobuf:"bytes,4,opt,name=pop_up_msg,json=popUpMsg,proto3" json:"pop_up_msg,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SuperPlayerIdentityPopupResp) Reset()         { *m = SuperPlayerIdentityPopupResp{} }
func (m *SuperPlayerIdentityPopupResp) String() string { return proto.CompactTextString(m) }
func (*SuperPlayerIdentityPopupResp) ProtoMessage()    {}
func (*SuperPlayerIdentityPopupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{52}
}
func (m *SuperPlayerIdentityPopupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperPlayerIdentityPopupResp.Unmarshal(m, b)
}
func (m *SuperPlayerIdentityPopupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperPlayerIdentityPopupResp.Marshal(b, m, deterministic)
}
func (dst *SuperPlayerIdentityPopupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperPlayerIdentityPopupResp.Merge(dst, src)
}
func (m *SuperPlayerIdentityPopupResp) XXX_Size() int {
	return xxx_messageInfo_SuperPlayerIdentityPopupResp.Size(m)
}
func (m *SuperPlayerIdentityPopupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperPlayerIdentityPopupResp.DiscardUnknown(m)
}

var xxx_messageInfo_SuperPlayerIdentityPopupResp proto.InternalMessageInfo

func (m *SuperPlayerIdentityPopupResp) GetIsNeedPush() bool {
	if m != nil {
		return m.IsNeedPush
	}
	return false
}

func (m *SuperPlayerIdentityPopupResp) GetBeforeLv() uint32 {
	if m != nil {
		return m.BeforeLv
	}
	return 0
}

func (m *SuperPlayerIdentityPopupResp) GetAfterLv() uint32 {
	if m != nil {
		return m.AfterLv
	}
	return 0
}

func (m *SuperPlayerIdentityPopupResp) GetPopUpMsg() string {
	if m != nil {
		return m.PopUpMsg
	}
	return ""
}

type BannerAdvConf struct {
	BannerImg            string   `protobuf:"bytes,1,opt,name=banner_img,json=bannerImg,proto3" json:"banner_img,"`
	JumpUrl              string   `protobuf:"bytes,2,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BannerAdvConf) Reset()         { *m = BannerAdvConf{} }
func (m *BannerAdvConf) String() string { return proto.CompactTextString(m) }
func (*BannerAdvConf) ProtoMessage()    {}
func (*BannerAdvConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{53}
}
func (m *BannerAdvConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BannerAdvConf.Unmarshal(m, b)
}
func (m *BannerAdvConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BannerAdvConf.Marshal(b, m, deterministic)
}
func (dst *BannerAdvConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BannerAdvConf.Merge(dst, src)
}
func (m *BannerAdvConf) XXX_Size() int {
	return xxx_messageInfo_BannerAdvConf.Size(m)
}
func (m *BannerAdvConf) XXX_DiscardUnknown() {
	xxx_messageInfo_BannerAdvConf.DiscardUnknown(m)
}

var xxx_messageInfo_BannerAdvConf proto.InternalMessageInfo

func (m *BannerAdvConf) GetBannerImg() string {
	if m != nil {
		return m.BannerImg
	}
	return ""
}

func (m *BannerAdvConf) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

// 获取会员banner广告位
type GetBannerAdvConfListReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,"`
	PlatformType         uint32   `protobuf:"varint,2,opt,name=platform_type,json=platformType,proto3" json:"platform_type,"`
	SuperPlayerStatus    uint32   `protobuf:"varint,3,opt,name=super_player_status,json=superPlayerStatus,proto3" json:"super_player_status,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBannerAdvConfListReq) Reset()         { *m = GetBannerAdvConfListReq{} }
func (m *GetBannerAdvConfListReq) String() string { return proto.CompactTextString(m) }
func (*GetBannerAdvConfListReq) ProtoMessage()    {}
func (*GetBannerAdvConfListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{54}
}
func (m *GetBannerAdvConfListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannerAdvConfListReq.Unmarshal(m, b)
}
func (m *GetBannerAdvConfListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannerAdvConfListReq.Marshal(b, m, deterministic)
}
func (dst *GetBannerAdvConfListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannerAdvConfListReq.Merge(dst, src)
}
func (m *GetBannerAdvConfListReq) XXX_Size() int {
	return xxx_messageInfo_GetBannerAdvConfListReq.Size(m)
}
func (m *GetBannerAdvConfListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannerAdvConfListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannerAdvConfListReq proto.InternalMessageInfo

func (m *GetBannerAdvConfListReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetBannerAdvConfListReq) GetPlatformType() uint32 {
	if m != nil {
		return m.PlatformType
	}
	return 0
}

func (m *GetBannerAdvConfListReq) GetSuperPlayerStatus() uint32 {
	if m != nil {
		return m.SuperPlayerStatus
	}
	return 0
}

type GetBannerAdvConfListResp struct {
	AdvList              []*BannerAdvConf `protobuf:"bytes,1,rep,name=adv_list,json=advList,proto3" json:"adv_list,"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetBannerAdvConfListResp) Reset()         { *m = GetBannerAdvConfListResp{} }
func (m *GetBannerAdvConfListResp) String() string { return proto.CompactTextString(m) }
func (*GetBannerAdvConfListResp) ProtoMessage()    {}
func (*GetBannerAdvConfListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{55}
}
func (m *GetBannerAdvConfListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannerAdvConfListResp.Unmarshal(m, b)
}
func (m *GetBannerAdvConfListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannerAdvConfListResp.Marshal(b, m, deterministic)
}
func (dst *GetBannerAdvConfListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannerAdvConfListResp.Merge(dst, src)
}
func (m *GetBannerAdvConfListResp) XXX_Size() int {
	return xxx_messageInfo_GetBannerAdvConfListResp.Size(m)
}
func (m *GetBannerAdvConfListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannerAdvConfListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannerAdvConfListResp proto.InternalMessageInfo

func (m *GetBannerAdvConfListResp) GetAdvList() []*BannerAdvConf {
	if m != nil {
		return m.AdvList
	}
	return nil
}

// 会员订单端外退款后回滚
type RevokeOrderReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,"`
	NotifyTime           string   `protobuf:"bytes,2,opt,name=notify_time,json=notifyTime,proto3" json:"notify_time,"`
	Uid                  string   `protobuf:"bytes,3,opt,name=uid,proto3" json:"uid,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeOrderReq) Reset()         { *m = RevokeOrderReq{} }
func (m *RevokeOrderReq) String() string { return proto.CompactTextString(m) }
func (*RevokeOrderReq) ProtoMessage()    {}
func (*RevokeOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{56}
}
func (m *RevokeOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeOrderReq.Unmarshal(m, b)
}
func (m *RevokeOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeOrderReq.Marshal(b, m, deterministic)
}
func (dst *RevokeOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeOrderReq.Merge(dst, src)
}
func (m *RevokeOrderReq) XXX_Size() int {
	return xxx_messageInfo_RevokeOrderReq.Size(m)
}
func (m *RevokeOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeOrderReq proto.InternalMessageInfo

func (m *RevokeOrderReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *RevokeOrderReq) GetNotifyTime() string {
	if m != nil {
		return m.NotifyTime
	}
	return ""
}

func (m *RevokeOrderReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type RevokeOrderResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeOrderResp) Reset()         { *m = RevokeOrderResp{} }
func (m *RevokeOrderResp) String() string { return proto.CompactTextString(m) }
func (*RevokeOrderResp) ProtoMessage()    {}
func (*RevokeOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{57}
}
func (m *RevokeOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeOrderResp.Unmarshal(m, b)
}
func (m *RevokeOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeOrderResp.Marshal(b, m, deterministic)
}
func (dst *RevokeOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeOrderResp.Merge(dst, src)
}
func (m *RevokeOrderResp) XXX_Size() int {
	return xxx_messageInfo_RevokeOrderResp.Size(m)
}
func (m *RevokeOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeOrderResp proto.InternalMessageInfo

// 获取TT用户信息
type GetTTUserInfoReq struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,"`
	Token                string   `protobuf:"bytes,2,opt,name=token,proto3" json:"token,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTTUserInfoReq) Reset()         { *m = GetTTUserInfoReq{} }
func (m *GetTTUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetTTUserInfoReq) ProtoMessage()    {}
func (*GetTTUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{58}
}
func (m *GetTTUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTTUserInfoReq.Unmarshal(m, b)
}
func (m *GetTTUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTTUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetTTUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTTUserInfoReq.Merge(dst, src)
}
func (m *GetTTUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetTTUserInfoReq.Size(m)
}
func (m *GetTTUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTTUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTTUserInfoReq proto.InternalMessageInfo

func (m *GetTTUserInfoReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GetTTUserInfoReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type GetTTUserInfoResp struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,"`
	Age                  uint32   `protobuf:"varint,3,opt,name=age,proto3" json:"age,"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,"`
	Alias                string   `protobuf:"bytes,5,opt,name=alias,proto3" json:"alias,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTTUserInfoResp) Reset()         { *m = GetTTUserInfoResp{} }
func (m *GetTTUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetTTUserInfoResp) ProtoMessage()    {}
func (*GetTTUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{59}
}
func (m *GetTTUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTTUserInfoResp.Unmarshal(m, b)
}
func (m *GetTTUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTTUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetTTUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTTUserInfoResp.Merge(dst, src)
}
func (m *GetTTUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetTTUserInfoResp.Size(m)
}
func (m *GetTTUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTTUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTTUserInfoResp proto.InternalMessageInfo

func (m *GetTTUserInfoResp) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GetTTUserInfoResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTTUserInfoResp) GetAge() uint32 {
	if m != nil {
		return m.Age
	}
	return 0
}

func (m *GetTTUserInfoResp) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GetTTUserInfoResp) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

// 获取套餐列表
type GetSuperPlayerPackageListReq struct {
	PayChannel           string   `protobuf:"bytes,1,opt,name=pay_channel,json=payChannel,proto3" json:"pay_channel,"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,"`
	Token                string   `protobuf:"bytes,3,opt,name=token,proto3" json:"token,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSuperPlayerPackageListReq) Reset()         { *m = GetSuperPlayerPackageListReq{} }
func (m *GetSuperPlayerPackageListReq) String() string { return proto.CompactTextString(m) }
func (*GetSuperPlayerPackageListReq) ProtoMessage()    {}
func (*GetSuperPlayerPackageListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{60}
}
func (m *GetSuperPlayerPackageListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSuperPlayerPackageListReq.Unmarshal(m, b)
}
func (m *GetSuperPlayerPackageListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSuperPlayerPackageListReq.Marshal(b, m, deterministic)
}
func (dst *GetSuperPlayerPackageListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSuperPlayerPackageListReq.Merge(dst, src)
}
func (m *GetSuperPlayerPackageListReq) XXX_Size() int {
	return xxx_messageInfo_GetSuperPlayerPackageListReq.Size(m)
}
func (m *GetSuperPlayerPackageListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSuperPlayerPackageListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSuperPlayerPackageListReq proto.InternalMessageInfo

func (m *GetSuperPlayerPackageListReq) GetPayChannel() string {
	if m != nil {
		return m.PayChannel
	}
	return ""
}

func (m *GetSuperPlayerPackageListReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GetSuperPlayerPackageListReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type GetSuperPlayerPackageListResp struct {
	PackageList          []*Package `protobuf:"bytes,1,rep,name=package_list,json=packageList,proto3" json:"package_list,"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetSuperPlayerPackageListResp) Reset()         { *m = GetSuperPlayerPackageListResp{} }
func (m *GetSuperPlayerPackageListResp) String() string { return proto.CompactTextString(m) }
func (*GetSuperPlayerPackageListResp) ProtoMessage()    {}
func (*GetSuperPlayerPackageListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{61}
}
func (m *GetSuperPlayerPackageListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSuperPlayerPackageListResp.Unmarshal(m, b)
}
func (m *GetSuperPlayerPackageListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSuperPlayerPackageListResp.Marshal(b, m, deterministic)
}
func (dst *GetSuperPlayerPackageListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSuperPlayerPackageListResp.Merge(dst, src)
}
func (m *GetSuperPlayerPackageListResp) XXX_Size() int {
	return xxx_messageInfo_GetSuperPlayerPackageListResp.Size(m)
}
func (m *GetSuperPlayerPackageListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSuperPlayerPackageListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSuperPlayerPackageListResp proto.InternalMessageInfo

func (m *GetSuperPlayerPackageListResp) GetPackageList() []*Package {
	if m != nil {
		return m.PackageList
	}
	return nil
}

// 网页授权接口
type SuperPlayerPayAuthReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SuperPlayerPayAuthReq) Reset()         { *m = SuperPlayerPayAuthReq{} }
func (m *SuperPlayerPayAuthReq) String() string { return proto.CompactTextString(m) }
func (*SuperPlayerPayAuthReq) ProtoMessage()    {}
func (*SuperPlayerPayAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{62}
}
func (m *SuperPlayerPayAuthReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperPlayerPayAuthReq.Unmarshal(m, b)
}
func (m *SuperPlayerPayAuthReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperPlayerPayAuthReq.Marshal(b, m, deterministic)
}
func (dst *SuperPlayerPayAuthReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperPlayerPayAuthReq.Merge(dst, src)
}
func (m *SuperPlayerPayAuthReq) XXX_Size() int {
	return xxx_messageInfo_SuperPlayerPayAuthReq.Size(m)
}
func (m *SuperPlayerPayAuthReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperPlayerPayAuthReq.DiscardUnknown(m)
}

var xxx_messageInfo_SuperPlayerPayAuthReq proto.InternalMessageInfo

func (m *SuperPlayerPayAuthReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SuperPlayerPayAuthResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SuperPlayerPayAuthResp) Reset()         { *m = SuperPlayerPayAuthResp{} }
func (m *SuperPlayerPayAuthResp) String() string { return proto.CompactTextString(m) }
func (*SuperPlayerPayAuthResp) ProtoMessage()    {}
func (*SuperPlayerPayAuthResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{63}
}
func (m *SuperPlayerPayAuthResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperPlayerPayAuthResp.Unmarshal(m, b)
}
func (m *SuperPlayerPayAuthResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperPlayerPayAuthResp.Marshal(b, m, deterministic)
}
func (dst *SuperPlayerPayAuthResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperPlayerPayAuthResp.Merge(dst, src)
}
func (m *SuperPlayerPayAuthResp) XXX_Size() int {
	return xxx_messageInfo_SuperPlayerPayAuthResp.Size(m)
}
func (m *SuperPlayerPayAuthResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperPlayerPayAuthResp.DiscardUnknown(m)
}

var xxx_messageInfo_SuperPlayerPayAuthResp proto.InternalMessageInfo

// 检查是否展示优惠券弹窗
type CheckCouponPopUpReq struct {
	Scene                CouponPopUpScene `protobuf:"varint,1,opt,name=scene,proto3,enum=super_player_http_logic.CouponPopUpScene" json:"scene,"`
	OsType               uint32           `protobuf:"varint,2,opt,name=os_type,json=osType,proto3" json:"os_type,"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *CheckCouponPopUpReq) Reset()         { *m = CheckCouponPopUpReq{} }
func (m *CheckCouponPopUpReq) String() string { return proto.CompactTextString(m) }
func (*CheckCouponPopUpReq) ProtoMessage()    {}
func (*CheckCouponPopUpReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{64}
}
func (m *CheckCouponPopUpReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCouponPopUpReq.Unmarshal(m, b)
}
func (m *CheckCouponPopUpReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCouponPopUpReq.Marshal(b, m, deterministic)
}
func (dst *CheckCouponPopUpReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCouponPopUpReq.Merge(dst, src)
}
func (m *CheckCouponPopUpReq) XXX_Size() int {
	return xxx_messageInfo_CheckCouponPopUpReq.Size(m)
}
func (m *CheckCouponPopUpReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCouponPopUpReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCouponPopUpReq proto.InternalMessageInfo

func (m *CheckCouponPopUpReq) GetScene() CouponPopUpScene {
	if m != nil {
		return m.Scene
	}
	return CouponPopUpScene_ENUM_RENEWAL_POPUP_SCENE_UNSPECIFIED
}

func (m *CheckCouponPopUpReq) GetOsType() uint32 {
	if m != nil {
		return m.OsType
	}
	return 0
}

type CheckCouponPopUpResp struct {
	IsRedPackgeShow      bool     `protobuf:"varint,1,opt,name=is_red_packge_show,json=isRedPackgeShow,proto3" json:"is_red_packge_show,"`
	IsRetainShow         bool     `protobuf:"varint,2,opt,name=is_retain_show,json=isRetainShow,proto3" json:"is_retain_show,"`
	ResourceUrl          string   `protobuf:"bytes,3,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,"`
	ResourceMd5          string   `protobuf:"bytes,4,opt,name=resource_md5,json=resourceMd5,proto3" json:"resource_md5,"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCouponPopUpResp) Reset()         { *m = CheckCouponPopUpResp{} }
func (m *CheckCouponPopUpResp) String() string { return proto.CompactTextString(m) }
func (*CheckCouponPopUpResp) ProtoMessage()    {}
func (*CheckCouponPopUpResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{65}
}
func (m *CheckCouponPopUpResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCouponPopUpResp.Unmarshal(m, b)
}
func (m *CheckCouponPopUpResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCouponPopUpResp.Marshal(b, m, deterministic)
}
func (dst *CheckCouponPopUpResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCouponPopUpResp.Merge(dst, src)
}
func (m *CheckCouponPopUpResp) XXX_Size() int {
	return xxx_messageInfo_CheckCouponPopUpResp.Size(m)
}
func (m *CheckCouponPopUpResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCouponPopUpResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCouponPopUpResp proto.InternalMessageInfo

func (m *CheckCouponPopUpResp) GetIsRedPackgeShow() bool {
	if m != nil {
		return m.IsRedPackgeShow
	}
	return false
}

func (m *CheckCouponPopUpResp) GetIsRetainShow() bool {
	if m != nil {
		return m.IsRetainShow
	}
	return false
}

func (m *CheckCouponPopUpResp) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *CheckCouponPopUpResp) GetResourceMd5() string {
	if m != nil {
		return m.ResourceMd5
	}
	return ""
}

// 获取优惠券挽留弹窗配置
type GetCouponRetainPopUpConfReq struct {
	Scene                CouponPopUpScene `protobuf:"varint,1,opt,name=scene,proto3,enum=super_player_http_logic.CouponPopUpScene" json:"scene,"`
	OsType               uint32           `protobuf:"varint,2,opt,name=os_type,json=osType,proto3" json:"os_type,"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetCouponRetainPopUpConfReq) Reset()         { *m = GetCouponRetainPopUpConfReq{} }
func (m *GetCouponRetainPopUpConfReq) String() string { return proto.CompactTextString(m) }
func (*GetCouponRetainPopUpConfReq) ProtoMessage()    {}
func (*GetCouponRetainPopUpConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{66}
}
func (m *GetCouponRetainPopUpConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponRetainPopUpConfReq.Unmarshal(m, b)
}
func (m *GetCouponRetainPopUpConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponRetainPopUpConfReq.Marshal(b, m, deterministic)
}
func (dst *GetCouponRetainPopUpConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponRetainPopUpConfReq.Merge(dst, src)
}
func (m *GetCouponRetainPopUpConfReq) XXX_Size() int {
	return xxx_messageInfo_GetCouponRetainPopUpConfReq.Size(m)
}
func (m *GetCouponRetainPopUpConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponRetainPopUpConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponRetainPopUpConfReq proto.InternalMessageInfo

func (m *GetCouponRetainPopUpConfReq) GetScene() CouponPopUpScene {
	if m != nil {
		return m.Scene
	}
	return CouponPopUpScene_ENUM_RENEWAL_POPUP_SCENE_UNSPECIFIED
}

func (m *GetCouponRetainPopUpConfReq) GetOsType() uint32 {
	if m != nil {
		return m.OsType
	}
	return 0
}

type GetCouponRetainPopUpConfResp struct {
	Reminder             string           `protobuf:"bytes,1,opt,name=reminder,proto3" json:"reminder,"`
	SaleId               string           `protobuf:"bytes,2,opt,name=sale_id,json=saleId,proto3" json:"sale_id,"`
	PackageType          uint32           `protobuf:"varint,3,opt,name=package_type,json=packageType,proto3" json:"package_type,"`
	ShowTime             uint32           `protobuf:"varint,4,opt,name=show_time,json=showTime,proto3" json:"show_time,"`
	Scene                CouponPopUpScene `protobuf:"varint,5,opt,name=scene,proto3,enum=super_player_http_logic.CouponPopUpScene" json:"scene,"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetCouponRetainPopUpConfResp) Reset()         { *m = GetCouponRetainPopUpConfResp{} }
func (m *GetCouponRetainPopUpConfResp) String() string { return proto.CompactTextString(m) }
func (*GetCouponRetainPopUpConfResp) ProtoMessage()    {}
func (*GetCouponRetainPopUpConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{67}
}
func (m *GetCouponRetainPopUpConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponRetainPopUpConfResp.Unmarshal(m, b)
}
func (m *GetCouponRetainPopUpConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponRetainPopUpConfResp.Marshal(b, m, deterministic)
}
func (dst *GetCouponRetainPopUpConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponRetainPopUpConfResp.Merge(dst, src)
}
func (m *GetCouponRetainPopUpConfResp) XXX_Size() int {
	return xxx_messageInfo_GetCouponRetainPopUpConfResp.Size(m)
}
func (m *GetCouponRetainPopUpConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponRetainPopUpConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponRetainPopUpConfResp proto.InternalMessageInfo

func (m *GetCouponRetainPopUpConfResp) GetReminder() string {
	if m != nil {
		return m.Reminder
	}
	return ""
}

func (m *GetCouponRetainPopUpConfResp) GetSaleId() string {
	if m != nil {
		return m.SaleId
	}
	return ""
}

func (m *GetCouponRetainPopUpConfResp) GetPackageType() uint32 {
	if m != nil {
		return m.PackageType
	}
	return 0
}

func (m *GetCouponRetainPopUpConfResp) GetShowTime() uint32 {
	if m != nil {
		return m.ShowTime
	}
	return 0
}

func (m *GetCouponRetainPopUpConfResp) GetScene() CouponPopUpScene {
	if m != nil {
		return m.Scene
	}
	return CouponPopUpScene_ENUM_RENEWAL_POPUP_SCENE_UNSPECIFIED
}

type CouponPopUpAndPayReq struct {
	Scene                CouponPopUpScene `protobuf:"varint,1,opt,name=scene,proto3,enum=super_player_http_logic.CouponPopUpScene" json:"scene,"`
	IsPay                bool             `protobuf:"varint,2,opt,name=is_pay,json=isPay,proto3" json:"is_pay,"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *CouponPopUpAndPayReq) Reset()         { *m = CouponPopUpAndPayReq{} }
func (m *CouponPopUpAndPayReq) String() string { return proto.CompactTextString(m) }
func (*CouponPopUpAndPayReq) ProtoMessage()    {}
func (*CouponPopUpAndPayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{68}
}
func (m *CouponPopUpAndPayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponPopUpAndPayReq.Unmarshal(m, b)
}
func (m *CouponPopUpAndPayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponPopUpAndPayReq.Marshal(b, m, deterministic)
}
func (dst *CouponPopUpAndPayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponPopUpAndPayReq.Merge(dst, src)
}
func (m *CouponPopUpAndPayReq) XXX_Size() int {
	return xxx_messageInfo_CouponPopUpAndPayReq.Size(m)
}
func (m *CouponPopUpAndPayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponPopUpAndPayReq.DiscardUnknown(m)
}

var xxx_messageInfo_CouponPopUpAndPayReq proto.InternalMessageInfo

func (m *CouponPopUpAndPayReq) GetScene() CouponPopUpScene {
	if m != nil {
		return m.Scene
	}
	return CouponPopUpScene_ENUM_RENEWAL_POPUP_SCENE_UNSPECIFIED
}

func (m *CouponPopUpAndPayReq) GetIsPay() bool {
	if m != nil {
		return m.IsPay
	}
	return false
}

type CouponPopUpAndPayResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponPopUpAndPayResp) Reset()         { *m = CouponPopUpAndPayResp{} }
func (m *CouponPopUpAndPayResp) String() string { return proto.CompactTextString(m) }
func (*CouponPopUpAndPayResp) ProtoMessage()    {}
func (*CouponPopUpAndPayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58, []int{69}
}
func (m *CouponPopUpAndPayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponPopUpAndPayResp.Unmarshal(m, b)
}
func (m *CouponPopUpAndPayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponPopUpAndPayResp.Marshal(b, m, deterministic)
}
func (dst *CouponPopUpAndPayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponPopUpAndPayResp.Merge(dst, src)
}
func (m *CouponPopUpAndPayResp) XXX_Size() int {
	return xxx_messageInfo_CouponPopUpAndPayResp.Size(m)
}
func (m *CouponPopUpAndPayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponPopUpAndPayResp.DiscardUnknown(m)
}

var xxx_messageInfo_CouponPopUpAndPayResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ReplacementOrderReq)(nil), "super_player_http_logic.ReplacementOrderReq")
	proto.RegisterType((*ReplacementOrderResp)(nil), "super_player_http_logic.ReplacementOrderResp")
	proto.RegisterType((*OpenSuperPlayerReq)(nil), "super_player_http_logic.OpenSuperPlayerReq")
	proto.RegisterType((*OpenSuperPlayerResp)(nil), "super_player_http_logic.OpenSuperPlayerResp")
	proto.RegisterType((*Package)(nil), "super_player_http_logic.Package")
	proto.RegisterType((*UpgradePackage)(nil), "super_player_http_logic.UpgradePackage")
	proto.RegisterType((*GetPackageListReq)(nil), "super_player_http_logic.GetPackageListReq")
	proto.RegisterType((*GetPackageListResp)(nil), "super_player_http_logic.GetPackageListResp")
	proto.RegisterType((*GetSvipPackageListReq)(nil), "super_player_http_logic.GetSvipPackageListReq")
	proto.RegisterType((*GetSvipPackageListResp)(nil), "super_player_http_logic.GetSvipPackageListResp")
	proto.RegisterType((*PlaceOrderReq)(nil), "super_player_http_logic.PlaceOrderReq")
	proto.RegisterType((*PlaceOrderResp)(nil), "super_player_http_logic.PlaceOrderResp")
	proto.RegisterType((*CancelOrderReq)(nil), "super_player_http_logic.CancelOrderReq")
	proto.RegisterType((*CancelOrderResp)(nil), "super_player_http_logic.CancelOrderResp")
	proto.RegisterType((*PlaceAutoPayOrderReq)(nil), "super_player_http_logic.PlaceAutoPayOrderReq")
	proto.RegisterType((*PlaceAutoPayOrderResp)(nil), "super_player_http_logic.PlaceAutoPayOrderResp")
	proto.RegisterType((*PayCallBackReq)(nil), "super_player_http_logic.PayCallBackReq")
	proto.RegisterType((*PayCallBackResp)(nil), "super_player_http_logic.PayCallBackResp")
	proto.RegisterType((*NotifyContractReq)(nil), "super_player_http_logic.NotifyContractReq")
	proto.RegisterType((*NotifyContractResp)(nil), "super_player_http_logic.NotifyContractResp")
	proto.RegisterType((*GetSuperPlayerInfoReq)(nil), "super_player_http_logic.GetSuperPlayerInfoReq")
	proto.RegisterType((*SuperPlayerContract)(nil), "super_player_http_logic.SuperPlayerContract")
	proto.RegisterType((*GetSuperPlayerInfoResp)(nil), "super_player_http_logic.GetSuperPlayerInfoResp")
	proto.RegisterType((*CancelContractReq)(nil), "super_player_http_logic.CancelContractReq")
	proto.RegisterType((*CancelContractResp)(nil), "super_player_http_logic.CancelContractResp")
	proto.RegisterType((*SuperPlayerValueRecord)(nil), "super_player_http_logic.SuperPlayerValueRecord")
	proto.RegisterType((*SetLastSuperPlayerValueChangeTimeReq)(nil), "super_player_http_logic.SetLastSuperPlayerValueChangeTimeReq")
	proto.RegisterType((*SetLastSuperPlayerValueChangeTimeResp)(nil), "super_player_http_logic.SetLastSuperPlayerValueChangeTimeResp")
	proto.RegisterType((*PayRecord)(nil), "super_player_http_logic.PayRecord")
	proto.RegisterType((*GetSuperPlayerPayRecordReq)(nil), "super_player_http_logic.GetSuperPlayerPayRecordReq")
	proto.RegisterType((*GetSuperPlayerPayRecordResp)(nil), "super_player_http_logic.GetSuperPlayerPayRecordResp")
	proto.RegisterType((*GetSuperPlayerValueRecordReq)(nil), "super_player_http_logic.GetSuperPlayerValueRecordReq")
	proto.RegisterType((*GetSuperPlayerValueRecordResp)(nil), "super_player_http_logic.GetSuperPlayerValueRecordResp")
	proto.RegisterType((*GetSuperPlayerMissionReq)(nil), "super_player_http_logic.GetSuperPlayerMissionReq")
	proto.RegisterType((*GetSuperPlayerMissionResp)(nil), "super_player_http_logic.GetSuperPlayerMissionResp")
	proto.RegisterType((*AddSuperPlayerValueReq)(nil), "super_player_http_logic.AddSuperPlayerValueReq")
	proto.RegisterType((*AddSuperPlayerValueResp)(nil), "super_player_http_logic.AddSuperPlayerValueResp")
	proto.RegisterType((*GetOrderStatusReq)(nil), "super_player_http_logic.GetOrderStatusReq")
	proto.RegisterType((*GetOrderStatusResp)(nil), "super_player_http_logic.GetOrderStatusResp")
	proto.RegisterType((*MissionDetail)(nil), "super_player_http_logic.MissionDetail")
	proto.RegisterType((*DressResInfo)(nil), "super_player_http_logic.DressResInfo")
	proto.RegisterType((*DressInfo)(nil), "super_player_http_logic.DressInfo")
	proto.RegisterType((*GetUserDressListReq)(nil), "super_player_http_logic.GetUserDressListReq")
	proto.RegisterType((*GetUserDressListResp)(nil), "super_player_http_logic.GetUserDressListResp")
	proto.RegisterType((*SetDressInUseReq)(nil), "super_player_http_logic.SetDressInUseReq")
	proto.RegisterType((*SetDressInUseResp)(nil), "super_player_http_logic.SetDressInUseResp")
	proto.RegisterType((*SetChatSpecialDressInUseReq)(nil), "super_player_http_logic.SetChatSpecialDressInUseReq")
	proto.RegisterType((*SetChatSpecialDressInUseResp)(nil), "super_player_http_logic.SetChatSpecialDressInUseResp")
	proto.RegisterType((*GetUserDressHistoryReq)(nil), "super_player_http_logic.GetUserDressHistoryReq")
	proto.RegisterType((*UserDressHistory)(nil), "super_player_http_logic.UserDressHistory")
	proto.RegisterType((*GetUserDressHistoryResp)(nil), "super_player_http_logic.GetUserDressHistoryResp")
	proto.RegisterType((*SuperPlayerIdentityPopupReq)(nil), "super_player_http_logic.SuperPlayerIdentityPopupReq")
	proto.RegisterType((*SuperPlayerIdentityPopupResp)(nil), "super_player_http_logic.SuperPlayerIdentityPopupResp")
	proto.RegisterType((*BannerAdvConf)(nil), "super_player_http_logic.BannerAdvConf")
	proto.RegisterType((*GetBannerAdvConfListReq)(nil), "super_player_http_logic.GetBannerAdvConfListReq")
	proto.RegisterType((*GetBannerAdvConfListResp)(nil), "super_player_http_logic.GetBannerAdvConfListResp")
	proto.RegisterType((*RevokeOrderReq)(nil), "super_player_http_logic.RevokeOrderReq")
	proto.RegisterType((*RevokeOrderResp)(nil), "super_player_http_logic.RevokeOrderResp")
	proto.RegisterType((*GetTTUserInfoReq)(nil), "super_player_http_logic.GetTTUserInfoReq")
	proto.RegisterType((*GetTTUserInfoResp)(nil), "super_player_http_logic.GetTTUserInfoResp")
	proto.RegisterType((*GetSuperPlayerPackageListReq)(nil), "super_player_http_logic.GetSuperPlayerPackageListReq")
	proto.RegisterType((*GetSuperPlayerPackageListResp)(nil), "super_player_http_logic.GetSuperPlayerPackageListResp")
	proto.RegisterType((*SuperPlayerPayAuthReq)(nil), "super_player_http_logic.SuperPlayerPayAuthReq")
	proto.RegisterType((*SuperPlayerPayAuthResp)(nil), "super_player_http_logic.SuperPlayerPayAuthResp")
	proto.RegisterType((*CheckCouponPopUpReq)(nil), "super_player_http_logic.CheckCouponPopUpReq")
	proto.RegisterType((*CheckCouponPopUpResp)(nil), "super_player_http_logic.CheckCouponPopUpResp")
	proto.RegisterType((*GetCouponRetainPopUpConfReq)(nil), "super_player_http_logic.GetCouponRetainPopUpConfReq")
	proto.RegisterType((*GetCouponRetainPopUpConfResp)(nil), "super_player_http_logic.GetCouponRetainPopUpConfResp")
	proto.RegisterType((*CouponPopUpAndPayReq)(nil), "super_player_http_logic.CouponPopUpAndPayReq")
	proto.RegisterType((*CouponPopUpAndPayResp)(nil), "super_player_http_logic.CouponPopUpAndPayResp")
	proto.RegisterEnum("super_player_http_logic.EnumOrderStatus", EnumOrderStatus_name, EnumOrderStatus_value)
	proto.RegisterEnum("super_player_http_logic.MissionType", MissionType_name, MissionType_value)
	proto.RegisterEnum("super_player_http_logic.MissionStatus", MissionStatus_name, MissionStatus_value)
	proto.RegisterEnum("super_player_http_logic.DressType", DressType_name, DressType_value)
	proto.RegisterEnum("super_player_http_logic.PlatFormType", PlatFormType_name, PlatFormType_value)
	proto.RegisterEnum("super_player_http_logic.SuperPlayerStatus", SuperPlayerStatus_name, SuperPlayerStatus_value)
	proto.RegisterEnum("super_player_http_logic.SuperPlayerType", SuperPlayerType_name, SuperPlayerType_value)
	proto.RegisterEnum("super_player_http_logic.CouponPopUpScene", CouponPopUpScene_name, CouponPopUpScene_value)
	proto.RegisterEnum("super_player_http_logic.SuperPlayerIdentityPopupReq_ReqType", SuperPlayerIdentityPopupReq_ReqType_name, SuperPlayerIdentityPopupReq_ReqType_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/super-player-http-logic/super-player-http-logic.proto", fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58)
}

var fileDescriptor_super_player_http_logic_5e6ebd12b22c6b58 = []byte{
	// 4145 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x7a, 0xcf, 0x6f, 0x1b, 0x49,
	0x76, 0xbf, 0x49, 0x5a, 0x22, 0xf9, 0xf8, 0x43, 0xad, 0x96, 0x64, 0xc9, 0xd6, 0xcc, 0x58, 0xee,
	0xb5, 0x67, 0x64, 0x7d, 0x3d, 0xf6, 0x77, 0xbd, 0x98, 0x20, 0xc1, 0x2e, 0xb2, 0x4b, 0x51, 0x94,
	0xcd, 0x5d, 0x89, 0xe4, 0x36, 0x49, 0x7b, 0x1d, 0x60, 0xb7, 0xd1, 0xea, 0x2e, 0x89, 0xb5, 0x6a,
	0x76, 0xb7, 0xbb, 0x9a, 0xd4, 0xf0, 0x90, 0x20, 0xc8, 0x25, 0x87, 0x20, 0x40, 0x90, 0x43, 0x90,
	0x43, 0x80, 0x00, 0xc9, 0x25, 0xc8, 0x21, 0xb9, 0xe4, 0x90, 0x73, 0xfe, 0x86, 0x04, 0x39, 0xe4,
	0x90, 0x3f, 0x23, 0xc7, 0x04, 0xf5, 0xaa, 0xba, 0xd9, 0xdd, 0x24, 0xfd, 0x63, 0xb2, 0x46, 0x6e,
	0x5d, 0x9f, 0x7a, 0x55, 0xf5, 0xea, 0xfd, 0xaa, 0xf7, 0xaa, 0x1a, 0x4e, 0xc2, 0xf0, 0xd9, 0xdb,
	0x09, 0xb5, 0xae, 0x19, 0x75, 0xa6, 0x24, 0x78, 0xc6, 0x26, 0x3e, 0x09, 0xbe, 0xf6, 0x1d, 0x73,
	0x46, 0x82, 0xaf, 0x47, 0x61, 0xe8, 0x7f, 0xed, 0x78, 0x57, 0xd4, 0x5a, 0x85, 0x3f, 0xf5, 0x03,
	0x2f, 0xf4, 0xd4, 0x5d, 0xec, 0x36, 0x44, 0xb7, 0xc1, 0xbb, 0x0d, 0xec, 0xd6, 0x7e, 0x01, 0x5b,
	0x3a, 0xf1, 0x1d, 0xd3, 0x22, 0x63, 0xe2, 0x86, 0xdd, 0xc0, 0x26, 0x81, 0x4e, 0xde, 0xaa, 0x07,
	0x50, 0xf5, 0x1c, 0xdb, 0xf0, 0x78, 0xdb, 0xa0, 0xf6, 0x5e, 0xee, 0x20, 0x77, 0x58, 0xd6, 0xc1,
	0x73, 0x6c, 0x24, 0x69, 0xdb, 0xea, 0x7d, 0xa8, 0x58, 0x01, 0x31, 0x43, 0x62, 0x84, 0x74, 0x4c,
	0xf6, 0xf2, 0x07, 0xb9, 0xc3, 0x35, 0x1d, 0x04, 0x34, 0xa0, 0x63, 0xa2, 0x7d, 0x1f, 0xb6, 0x17,
	0x67, 0x66, 0xbe, 0x7a, 0x17, 0x4a, 0x99, 0x69, 0x8b, 0x9e, 0x98, 0x53, 0xfb, 0x8f, 0x1c, 0xa8,
	0x5d, 0x9f, 0xb8, 0x7d, 0xce, 0x6c, 0x0f, 0x79, 0xe5, 0xcc, 0x1c, 0x82, 0x92, 0x62, 0x7f, 0x22,
	0x47, 0xd6, 0xf4, 0x3a, 0x9b, 0x53, 0x0e, 0xa9, 0xad, 0xde, 0x81, 0xf5, 0xb1, 0xe7, 0x86, 0x23,
	0x86, 0xfc, 0x14, 0x74, 0xd9, 0x52, 0xb7, 0x61, 0x6d, 0x6a, 0x3a, 0x13, 0xb2, 0x57, 0x40, 0x58,
	0x34, 0xf8, 0x16, 0x18, 0x09, 0xa6, 0x24, 0x10, 0x5b, 0xb8, 0x8d, 0x7d, 0x20, 0x20, 0xbe, 0x85,
	0x14, 0xab, 0x6b, 0x29, 0x56, 0xd5, 0xcf, 0x01, 0x7c, 0xd3, 0xba, 0x36, 0xaf, 0x08, 0xef, 0x5c,
	0xc7, 0xce, 0xb2, 0x44, 0xda, 0xb6, 0xaa, 0xc2, 0xed, 0x31, 0x75, 0xd9, 0x5e, 0x11, 0xe7, 0xc4,
	0x6f, 0x6d, 0x07, 0xb6, 0x16, 0x36, 0xc7, 0x7c, 0xed, 0x4f, 0x0b, 0x50, 0xec, 0x89, 0x81, 0x6a,
	0x1d, 0xf2, 0xb1, 0x54, 0xf2, 0x54, 0xac, 0x12, 0x78, 0xf6, 0xc4, 0x0a, 0xf9, 0x2a, 0x79, 0xb9,
	0x8a, 0x40, 0xc4, 0x2a, 0xae, 0x39, 0x16, 0xbb, 0x2a, 0xeb, 0xf8, 0xcd, 0x31, 0x9b, 0x30, 0x0b,
	0x77, 0x53, 0xd6, 0xf1, 0x9b, 0x6f, 0xdf, 0x31, 0x2f, 0x88, 0x23, 0x37, 0x21, 0x1a, 0xea, 0x23,
	0xa8, 0x7b, 0x01, 0xbd, 0xa2, 0xae, 0xe9, 0x18, 0x7e, 0x40, 0x2d, 0x82, 0xdb, 0xc8, 0xeb, 0xb5,
	0x08, 0xed, 0x71, 0x90, 0x0f, 0x16, 0xbd, 0x45, 0xec, 0x15, 0x8d, 0x84, 0xa4, 0x4b, 0xa8, 0xf9,
	0x05, 0x49, 0x97, 0x11, 0x96, 0x92, 0x56, 0xe1, 0xb6, 0x39, 0x09, 0xbd, 0x3d, 0x38, 0xc8, 0x1d,
	0x96, 0x74, 0xfc, 0xe6, 0x5a, 0xf5, 0xcd, 0x99, 0x61, 0x8d, 0x4c, 0xd7, 0x25, 0x8e, 0xe1, 0x50,
	0x16, 0xee, 0x55, 0x0e, 0x0a, 0x87, 0x65, 0xbd, 0xee, 0x9b, 0xb3, 0xa6, 0x80, 0xcf, 0x28, 0x0b,
	0x39, 0xa3, 0x36, 0x65, 0x96, 0x37, 0x71, 0x43, 0xc9, 0x68, 0x55, 0x30, 0x1a, 0xa1, 0x82, 0xd1,
	0xfb, 0x50, 0xb1, 0x4d, 0xea, 0xcc, 0x24, 0x4d, 0x0d, 0x69, 0x00, 0x21, 0x41, 0xf0, 0x10, 0xea,
	0x94, 0x19, 0x6c, 0xe4, 0xdd, 0x18, 0x96, 0x37, 0xf1, 0x3d, 0x77, 0xaf, 0x8e, 0xfc, 0x54, 0x29,
	0xeb, 0x8f, 0xbc, 0x9b, 0x26, 0x62, 0xda, 0xbf, 0xe5, 0xa1, 0x3e, 0xf4, 0xaf, 0x02, 0xd3, 0x26,
	0x9f, 0x5e, 0x2d, 0x82, 0xd5, 0xb5, 0xa4, 0x64, 0x39, 0xa5, 0x39, 0x63, 0xa8, 0x8c, 0x35, 0x1d,
	0xbf, 0xe7, 0x52, 0x2d, 0x26, 0xa5, 0xba, 0x4c, 0x82, 0xa5, 0x0f, 0x94, 0x60, 0xf9, 0x03, 0x24,
	0x08, 0x0b, 0x12, 0x8c, 0x0d, 0xa9, 0x92, 0x34, 0xa4, 0x45, 0xb9, 0x56, 0x97, 0xc8, 0x35, 0x80,
	0xcd, 0x17, 0x24, 0x94, 0x22, 0xe5, 0x5c, 0x71, 0xd7, 0x56, 0xa0, 0x30, 0x89, 0x45, 0xcb, 0x3f,
	0xd5, 0xa7, 0xb0, 0x95, 0x72, 0x76, 0x16, 0x9a, 0xe1, 0x44, 0xf8, 0x73, 0x4d, 0xdf, 0x4c, 0xf8,
	0x7b, 0x1f, 0x3b, 0xd4, 0x7d, 0x28, 0xdb, 0x64, 0x4a, 0x2d, 0xf4, 0x43, 0x21, 0xf1, 0x92, 0x00,
	0xda, 0xb6, 0xf6, 0x06, 0xd4, 0xec, 0x9a, 0xcc, 0x57, 0x9b, 0x50, 0x8d, 0x7c, 0x17, 0x65, 0x96,
	0x3b, 0x28, 0x1c, 0x56, 0x9e, 0x1f, 0x3c, 0x5d, 0x11, 0x23, 0x9f, 0xca, 0xf1, 0x7a, 0xc5, 0x9f,
	0x4f, 0xa4, 0x3d, 0x86, 0x9d, 0x17, 0x24, 0xec, 0x4f, 0xa9, 0xff, 0xbe, 0x2d, 0x69, 0xff, 0x9c,
	0x83, 0x3b, 0xcb, 0x68, 0x99, 0xaf, 0xbe, 0x81, 0xed, 0x89, 0xb0, 0x35, 0x63, 0x09, 0x4b, 0x5f,
	0xad, 0x64, 0x29, 0x6d, 0xa0, 0xba, 0x3a, 0x49, 0xb5, 0x51, 0xe7, 0xd9, 0x5d, 0xe6, 0xbf, 0xcb,
	0x2e, 0xff, 0x6e, 0x1d, 0x6a, 0x3d, 0x1e, 0xc3, 0xe3, 0x93, 0xe1, 0xc3, 0x83, 0x71, 0x3a, 0x44,
	0xe6, 0xb3, 0x21, 0xf2, 0x3e, 0x54, 0x12, 0xd6, 0x2b, 0x55, 0x07, 0x73, 0xc3, 0xe5, 0xe3, 0x45,
	0xf4, 0x0d, 0x67, 0x3e, 0x91, 0x8e, 0x53, 0x46, 0x64, 0x30, 0xf3, 0x89, 0xba, 0x0b, 0x45, 0x8f,
	0x89, 0x3e, 0x11, 0xd6, 0xd6, 0x3d, 0x86, 0x1d, 0x7b, 0x50, 0x9c, 0x92, 0x80, 0x51, 0xcf, 0x95,
	0x71, 0x39, 0x6a, 0xf2, 0xa0, 0x15, 0x90, 0xb1, 0x19, 0x5c, 0xa3, 0x1f, 0x95, 0x75, 0xd9, 0xe2,
	0x36, 0x74, 0x31, 0x71, 0x6d, 0x07, 0x19, 0x2d, 0x09, 0x1b, 0x12, 0x80, 0xf0, 0x66, 0xcb, 0xb3,
	0x85, 0xc7, 0x94, 0x75, 0xfc, 0xe6, 0x58, 0x18, 0x52, 0x1b, 0x3d, 0xa4, 0xac, 0xe3, 0x37, 0xf7,
	0x8d, 0xd0, 0xbb, 0x26, 0x6e, 0xe4, 0x1b, 0xd8, 0xe0, 0x9e, 0x67, 0x39, 0x94, 0xb8, 0xa1, 0x11,
	0xf1, 0x54, 0x45, 0x61, 0xd5, 0x04, 0xfa, 0x4a, 0x72, 0x96, 0xb2, 0xe2, 0x5a, 0xda, 0x8a, 0xb9,
	0x20, 0x02, 0xf2, 0x76, 0x42, 0x18, 0x86, 0x9b, 0xba, 0x10, 0x84, 0x44, 0xe4, 0x49, 0x2c, 0x96,
	0x40, 0x61, 0x6c, 0xe0, 0xfc, 0x20, 0x20, 0x14, 0xc8, 0x43, 0xa8, 0x9d, 0x9a, 0x16, 0x69, 0x4c,
	0xc2, 0xd1, 0x00, 0x39, 0x54, 0x70, 0x8a, 0x34, 0xa8, 0x7e, 0x03, 0xbb, 0x97, 0xa6, 0x45, 0x0c,
	0x73, 0x12, 0x8e, 0x0c, 0x3f, 0xf0, 0xa6, 0x94, 0xcb, 0x1e, 0xb7, 0xbe, 0x89, 0xf4, 0xdb, 0x97,
	0x92, 0xbe, 0x27, 0x3b, 0x9b, 0x5c, 0x14, 0xc7, 0xf0, 0xc5, 0x92, 0x61, 0x01, 0x61, 0x13, 0x27,
	0x34, 0x6c, 0x33, 0x34, 0xf7, 0x54, 0x1c, 0x7d, 0x2f, 0x3b, 0x5a, 0x47, 0x92, 0x13, 0x33, 0x34,
	0xd5, 0x1f, 0xc0, 0x9d, 0xf9, 0x1c, 0x72, 0xa8, 0x90, 0xe5, 0x16, 0x8e, 0xdd, 0x8a, 0xc6, 0x8a,
	0x31, 0x82, 0xdf, 0x01, 0x6c, 0xa6, 0x0c, 0x11, 0x37, 0xbf, 0x7d, 0x90, 0x3b, 0xac, 0x3f, 0x3f,
	0x5c, 0x69, 0xe4, 0x89, 0xc3, 0x97, 0x8b, 0x46, 0xdf, 0x60, 0x69, 0x40, 0xfd, 0x6d, 0xd8, 0x8b,
	0x0f, 0xc5, 0x30, 0x30, 0x5d, 0x66, 0x5a, 0x21, 0xf5, 0x5c, 0x83, 0xda, 0x6c, 0x6f, 0x07, 0x63,
	0xeb, 0x9d, 0xa8, 0x7f, 0x30, 0xef, 0x6e, 0xdb, 0x0c, 0xcf, 0x8d, 0xa4, 0xab, 0xbc, 0x33, 0xd5,
	0x99, 0x5b, 0x4b, 0x3e, 0x69, 0x2d, 0x07, 0x50, 0xb5, 0x1c, 0x2a, 0xd3, 0x2e, 0xd7, 0x8b, 0x9c,
	0xc2, 0x72, 0x28, 0x4e, 0xda, 0xf1, 0xd4, 0x2f, 0x61, 0x63, 0x4e, 0x11, 0xd2, 0xd0, 0x89, 0x3c,
	0xa3, 0x16, 0x11, 0x0d, 0x38, 0xc8, 0xa3, 0x50, 0xc8, 0xae, 0xa5, 0x67, 0xf0, 0x4f, 0x34, 0x13,
	0x79, 0x52, 0x8c, 0x4d, 0x5f, 0xba, 0x06, 0x48, 0xe8, 0xdc, 0xf4, 0x33, 0x66, 0x56, 0xcc, 0x9a,
	0xd9, 0xe7, 0x00, 0xa8, 0x1f, 0x66, 0x11, 0x97, 0xa0, 0x97, 0xd4, 0xf4, 0x32, 0x47, 0xfa, 0x1c,
	0x48, 0xeb, 0xd0, 0xf2, 0xdc, 0x90, 0x7c, 0x1b, 0x1a, 0xbf, 0x66, 0x9e, 0x2b, 0x1d, 0x27, 0xd6,
	0x61, 0x53, 0xf4, 0xfd, 0x94, 0x79, 0xae, 0xaa, 0x41, 0x8d, 0x32, 0xe3, 0x92, 0x06, 0x2c, 0x34,
	0x3c, 0x9f, 0xb8, 0x32, 0x41, 0xa8, 0x50, 0x76, 0xca, 0x31, 0x9e, 0x2e, 0x69, 0x43, 0xa8, 0x37,
	0x4d, 0xd7, 0x22, 0xce, 0x77, 0x08, 0x41, 0x49, 0x05, 0xe4, 0xd3, 0xb9, 0xe6, 0x26, 0x6c, 0xa4,
	0xa6, 0x65, 0xbe, 0xf6, 0xb7, 0x39, 0xd8, 0x46, 0x0d, 0x36, 0x26, 0xa1, 0xd7, 0x33, 0x67, 0xdf,
	0x31, 0xe6, 0x61, 0x1e, 0x40, 0x16, 0x32, 0x03, 0x19, 0xf3, 0xb8, 0x68, 0x02, 0x53, 0x64, 0x0e,
	0x91, 0x7a, 0x25, 0xd4, 0xb6, 0xd5, 0x07, 0x50, 0x1d, 0x99, 0xcc, 0x88, 0x8e, 0x65, 0xd4, 0x6d,
	0x49, 0xaf, 0x8c, 0x4c, 0x76, 0x22, 0x21, 0x6d, 0x17, 0x76, 0x96, 0x30, 0xc9, 0x7c, 0xed, 0xbf,
	0xb9, 0x01, 0x9a, 0xb3, 0xa6, 0xe9, 0x38, 0xc7, 0xa6, 0x75, 0xcd, 0x19, 0x8f, 0xf7, 0xef, 0x7a,
	0x29, 0x03, 0xec, 0x78, 0xd9, 0xf0, 0x9b, 0x5f, 0x08, 0xbf, 0xfb, 0x50, 0x0e, 0xbd, 0xd0, 0x74,
	0x8c, 0x4b, 0x12, 0xa5, 0x32, 0x25, 0x04, 0x4e, 0x09, 0xe1, 0x7c, 0x8a, 0x89, 0xe5, 0xf1, 0x2c,
	0x6c, 0xb0, 0x82, 0x98, 0x3c, 0x98, 0xb3, 0xb6, 0xbc, 0xb6, 0x60, 0xcb, 0x0f, 0xa1, 0xee, 0x85,
	0x23, 0x12, 0xcc, 0x69, 0x84, 0x51, 0x56, 0x11, 0x8d, 0xa8, 0xf8, 0x52, 0x48, 0x25, 0x97, 0x2a,
	0xca, 0xa5, 0x38, 0x26, 0x97, 0xba, 0x0f, 0x15, 0xd7, 0x0b, 0xe9, 0xe5, 0x4c, 0x24, 0xf2, 0x22,
	0x82, 0x83, 0x80, 0x30, 0x91, 0x97, 0x67, 0x72, 0x79, 0x9e, 0x66, 0xdc, 0x85, 0xd2, 0xc5, 0x64,
	0x26, 0x2c, 0x43, 0x44, 0xf1, 0x22, 0xb6, 0x85, 0xa1, 0x5f, 0x90, 0x2b, 0xea, 0x8a, 0xc9, 0x44,
	0x34, 0x2f, 0x23, 0x12, 0x15, 0x05, 0xc4, 0xb5, 0x45, 0x67, 0x55, 0x8c, 0x24, 0xae, 0x8d, 0x25,
	0xcf, 0x26, 0x6c, 0xa4, 0x14, 0xc0, 0x7c, 0xed, 0xbf, 0xf2, 0xb0, 0xd9, 0x41, 0x46, 0x9a, 0x52,
	0xcb, 0x52, 0x2f, 0xf1, 0xea, 0xb9, 0xf4, 0xea, 0xfc, 0x68, 0xe1, 0x91, 0x2c, 0x2f, 0x8f, 0x16,
	0x1e, 0x94, 0xf6, 0xa1, 0x2c, 0x23, 0xfc, 0x3c, 0xc7, 0x11, 0x80, 0xb0, 0xa9, 0x8b, 0x09, 0xa3,
	0x2e, 0x61, 0x8c, 0x77, 0x0b, 0x4d, 0x40, 0x04, 0xb5, 0x6d, 0x7e, 0x50, 0xfa, 0x8e, 0xe9, 0xce,
	0x8b, 0x98, 0x75, 0xde, 0x6c, 0xdb, 0x32, 0xad, 0x5d, 0x8f, 0xd3, 0xda, 0x8c, 0x75, 0x16, 0x17,
	0xac, 0xf3, 0x31, 0x28, 0x31, 0x41, 0x64, 0x38, 0x42, 0xd8, 0x1b, 0x11, 0x1e, 0x59, 0x4f, 0xd2,
	0xf2, 0xca, 0x69, 0xcb, 0x4b, 0x67, 0xcf, 0x90, 0xcd, 0x9e, 0x35, 0xa8, 0xb9, 0x3c, 0x76, 0x70,
	0xeb, 0x4c, 0x68, 0xa0, 0xc2, 0xc1, 0x9e, 0x29, 0xf4, 0xa9, 0x41, 0x2d, 0x20, 0xa6, 0x63, 0xc4,
	0x42, 0x14, 0x8a, 0xa8, 0x70, 0xf0, 0x58, 0x08, 0x52, 0xdb, 0x06, 0x35, 0x2b, 0x78, 0xe6, 0x6b,
	0xbf, 0x12, 0x69, 0xdb, 0xdc, 0x6b, 0xdb, 0xee, 0xa5, 0xf7, 0x71, 0x3e, 0xbe, 0x0f, 0x65, 0x9e,
	0x35, 0x90, 0x38, 0xf9, 0xaf, 0xe9, 0x25, 0x01, 0xb4, 0x6d, 0xee, 0x84, 0x5b, 0x89, 0xd9, 0xa3,
	0xb5, 0xb3, 0xee, 0x96, 0x5b, 0x70, 0xb7, 0x3b, 0xb0, 0x9e, 0x48, 0x75, 0xcb, 0xba, 0x6c, 0x65,
	0xb2, 0xa8, 0x42, 0x36, 0x8b, 0xda, 0x87, 0x32, 0xf9, 0x36, 0x0c, 0x4c, 0x23, 0x34, 0xaf, 0xa4,
	0xee, 0x4b, 0x08, 0x0c, 0xcc, 0x2b, 0xf5, 0x09, 0xa8, 0x29, 0x51, 0xb2, 0xd0, 0x1c, 0xfb, 0x68,
	0x04, 0x35, 0x5d, 0x49, 0xc8, 0x13, 0xf1, 0xf4, 0xbe, 0xd6, 0xd3, 0xfb, 0x7a, 0xbf, 0x6d, 0xec,
	0x43, 0x99, 0xd1, 0x2b, 0x77, 0xee, 0x81, 0x35, 0xbd, 0xc4, 0x01, 0xd4, 0x57, 0x5c, 0xe9, 0x94,
	0x93, 0x95, 0xce, 0xd2, 0x13, 0x1c, 0xfe, 0x97, 0x27, 0xb8, 0xf6, 0x4f, 0x25, 0x91, 0x6d, 0x67,
	0x55, 0xcc, 0xfc, 0x8f, 0xd0, 0xf1, 0x13, 0x50, 0x53, 0x94, 0xa2, 0xfa, 0x12, 0x97, 0x0a, 0x4a,
	0x82, 0xf6, 0x15, 0x16, 0x62, 0x59, 0x6a, 0x87, 0x4c, 0x65, 0x46, 0x9b, 0xa6, 0x3e, 0xe3, 0xb8,
	0xfa, 0x15, 0x6c, 0xcc, 0xe3, 0x8b, 0x50, 0x89, 0xb8, 0x7a, 0xa8, 0xc7, 0x41, 0x46, 0x28, 0xe4,
	0x31, 0x28, 0xe4, 0x5b, 0x9f, 0x06, 0x24, 0xa3, 0xbc, 0x82, 0xbe, 0x21, 0xf0, 0x39, 0xe9, 0x13,
	0x50, 0x25, 0xa9, 0x0c, 0x84, 0x23, 0x6f, 0x12, 0xa0, 0x12, 0x0b, 0xba, 0x9c, 0x44, 0x38, 0xc3,
	0x4b, 0x6f, 0x12, 0xf0, 0x0c, 0xd9, 0xb4, 0xc4, 0x01, 0x23, 0x14, 0x19, 0x35, 0xd5, 0x7b, 0x50,
	0x72, 0xa9, 0x75, 0x8d, 0xe5, 0xab, 0x4c, 0x84, 0xa3, 0xb6, 0x4a, 0xe1, 0x5e, 0x6a, 0x97, 0xb1,
	0x3d, 0x60, 0x79, 0x51, 0xc6, 0xf2, 0xe2, 0xc9, 0x87, 0xe8, 0x2d, 0x76, 0xc8, 0x5d, 0xb6, 0x08,
	0x62, 0xed, 0xc2, 0x43, 0x30, 0x75, 0x6d, 0xc3, 0x1f, 0x79, 0x2e, 0x91, 0x49, 0x41, 0x99, 0x23,
	0x3d, 0x0e, 0xa8, 0xdf, 0x87, 0x9d, 0x19, 0x31, 0x03, 0x63, 0x4c, 0xc6, 0x17, 0x24, 0x30, 0x22,
	0x21, 0x31, 0x0c, 0x15, 0x35, 0x5d, 0xe5, 0x9d, 0xe7, 0xd8, 0xd7, 0x12, 0x72, 0x62, 0xea, 0xef,
	0xc2, 0x67, 0x51, 0x8d, 0x3a, 0x36, 0x5d, 0xf3, 0x2a, 0xc9, 0xff, 0x45, 0x18, 0x55, 0xac, 0x7b,
	0xa2, 0x62, 0x3d, 0x17, 0x14, 0x11, 0x4b, 0xc7, 0xa1, 0xcb, 0xed, 0x9f, 0x1f, 0xcc, 0x62, 0x29,
	0x91, 0xa2, 0x97, 0x74, 0x18, 0x99, 0x4c, 0xac, 0x60, 0xab, 0xff, 0x1f, 0xb6, 0xd9, 0x94, 0xfa,
	0x46, 0x56, 0xb5, 0x75, 0xd4, 0x81, 0xca, 0xfb, 0x8e, 0xd3, 0xea, 0x7d, 0x0e, 0x3b, 0x38, 0x62,
	0x41, 0xc7, 0x1b, 0x38, 0x64, 0x8b, 0x77, 0xb6, 0x32, 0x7a, 0x7e, 0x09, 0x0f, 0x70, 0xcc, 0xb2,
	0xed, 0xc7, 0xe3, 0x15, 0x1c, 0xff, 0x39, 0x27, 0x7c, 0x93, 0x95, 0x44, 0x3c, 0xd3, 0x71, 0x1c,
	0x6f, 0x36, 0xd1, 0xe3, 0x8e, 0x3e, 0x44, 0x73, 0xe2, 0xbc, 0x8d, 0x63, 0xd3, 0xcf, 0xa0, 0x82,
	0xdc, 0xc8, 0x89, 0xd4, 0x8f, 0x9e, 0x08, 0xf8, 0x70, 0x79, 0x88, 0x8b, 0x5c, 0x10, 0xe7, 0xc3,
	0x84, 0x10, 0x73, 0x7f, 0xcc, 0x05, 0x79, 0xe1, 0x8c, 0xf9, 0x20, 0x4f, 0x32, 0x33, 0x8e, 0x76,
	0xc9, 0x8b, 0x8c, 0x19, 0xc3, 0xc4, 0xbf, 0xa6, 0x6f, 0xa5, 0x9c, 0xed, 0x32, 0x3c, 0x31, 0x67,
	0x8c, 0xa7, 0x19, 0x38, 0xeb, 0x9c, 0x78, 0x07, 0x89, 0xab, 0x1c, 0x8d, 0xa8, 0xb4, 0x53, 0xd8,
	0x14, 0xf9, 0x60, 0xf2, 0x9c, 0x4e, 0xd4, 0xf2, 0x35, 0x91, 0x37, 0x64, 0xe2, 0x60, 0x3e, 0x1b,
	0x07, 0xf9, 0xb1, 0x93, 0x9d, 0x87, 0xf9, 0xda, 0x5f, 0xe6, 0xe0, 0x4e, 0x3f, 0x13, 0x36, 0x74,
	0x62, 0x79, 0x81, 0xfd, 0x71, 0xc9, 0x25, 0x57, 0xb2, 0x21, 0xb4, 0x2c, 0x82, 0x51, 0x99, 0x23,
	0x7d, 0xd4, 0x28, 0x56, 0xb7, 0x26, 0xcf, 0xb8, 0x0b, 0x51, 0x75, 0xcb, 0x5b, 0x7c, 0x18, 0x75,
	0xad, 0x28, 0x86, 0x89, 0x50, 0x53, 0xe6, 0x08, 0x72, 0xa1, 0xdd, 0xc0, 0xc3, 0x3e, 0x09, 0xcf,
	0x4c, 0x16, 0x66, 0x19, 0xe4, 0x47, 0xd3, 0x15, 0x9a, 0xcc, 0xc7, 0x1d, 0x90, 0x8f, 0xa0, 0xee,
	0x07, 0x64, 0x9a, 0xb0, 0x48, 0xc1, 0x6b, 0x8d, 0xa3, 0xb1, 0x05, 0x6a, 0x5f, 0xc1, 0xa3, 0x0f,
	0x58, 0x98, 0xf9, 0xda, 0x00, 0xca, 0x3d, 0x73, 0x26, 0xc5, 0x95, 0x16, 0x42, 0x2e, 0x2b, 0x84,
	0xe8, 0x9e, 0x2d, 0xbf, 0xec, 0x9e, 0xad, 0x90, 0x38, 0x7d, 0x34, 0x17, 0xee, 0xa5, 0x8f, 0x89,
	0x78, 0x8d, 0x8f, 0xdb, 0xad, 0x02, 0x05, 0xef, 0xf2, 0x52, 0x6e, 0x91, 0x7f, 0xf2, 0xf5, 0x44,
	0x70, 0x95, 0xb7, 0xcd, 0x22, 0x6f, 0xa7, 0xb0, 0xbf, 0x72, 0x3d, 0xe6, 0xab, 0x3f, 0x85, 0x0d,
	0x7e, 0x4c, 0x07, 0x88, 0x24, 0x2f, 0x81, 0xb4, 0x77, 0xdc, 0xd8, 0x44, 0x13, 0xd4, 0xfc, 0xe8,
	0x13, 0x6f, 0x6d, 0x7c, 0xf8, 0x2c, 0xbd, 0x54, 0xc2, 0xde, 0x3e, 0xcd, 0xe6, 0xfe, 0x25, 0x07,
	0x9f, 0xbf, 0x63, 0x49, 0xe6, 0xab, 0x3d, 0xa8, 0x2c, 0xee, 0xed, 0xd9, 0x87, 0xc4, 0x8a, 0xe4,
	0x4c, 0x10, 0xc4, 0xbb, 0xe4, 0xbb, 0x08, 0x3d, 0xdb, 0x9c, 0x19, 0x09, 0xeb, 0x16, 0x8c, 0xd6,
	0x11, 0x6f, 0x47, 0x26, 0xbe, 0xc4, 0x20, 0x0b, 0xcb, 0x0c, 0xf2, 0x04, 0xf6, 0xd2, 0x7b, 0x38,
	0xa7, 0x8c, 0x51, 0xcf, 0xfd, 0x28, 0x91, 0x69, 0xbf, 0x84, 0xbb, 0x2b, 0x66, 0x61, 0xbe, 0xfa,
	0x13, 0x28, 0x8e, 0x45, 0x53, 0x4a, 0xe0, 0xcb, 0x95, 0x12, 0x90, 0xc3, 0x4e, 0x48, 0x68, 0x52,
	0x47, 0x8f, 0x86, 0xf1, 0x22, 0xf5, 0x4e, 0xc3, 0xb6, 0x17, 0xe5, 0xf3, 0xf6, 0x93, 0xa5, 0x37,
	0xc9, 0x2a, 0xba, 0x90, 0xbe, 0xc6, 0x98, 0xc7, 0x9c, 0xdb, 0xc9, 0x98, 0xa3, 0xdd, 0x85, 0xdd,
	0xa5, 0x4c, 0x32, 0x5f, 0x7b, 0x8a, 0xf7, 0xc0, 0xdd, 0x79, 0xa5, 0x98, 0x2a, 0x54, 0x17, 0x1f,
	0x85, 0x5e, 0xe1, 0x1d, 0x6e, 0x8a, 0x1e, 0x05, 0x19, 0x1d, 0x5f, 0xb9, 0xf7, 0x24, 0x8c, 0x2d,
	0x77, 0x32, 0x4e, 0x8e, 0x96, 0xe3, 0xb4, 0x3f, 0xc9, 0x43, 0x2d, 0x25, 0xe3, 0xc4, 0x35, 0x7f,
	0x0d, 0xeb, 0xa1, 0xe8, 0x1e, 0x3f, 0x9f, 0xb8, 0xc7, 0x7f, 0x00, 0x55, 0xa9, 0x09, 0x91, 0xae,
	0x16, 0x90, 0xba, 0x22, 0x31, 0xbc, 0x42, 0xba, 0x0f, 0x15, 0xf3, 0xc6, 0x0c, 0xec, 0x44, 0xc0,
	0xad, 0xe9, 0x80, 0x50, 0x6c, 0x8e, 0x01, 0x79, 0x3b, 0xe1, 0x69, 0x83, 0x21, 0x7c, 0x49, 0xa4,
	0xe4, 0xb5, 0x08, 0x6d, 0x62, 0x2e, 0xf6, 0x00, 0xaa, 0x97, 0xd4, 0xa5, 0x6c, 0x24, 0x89, 0x44,
	0x4a, 0x5e, 0x11, 0x98, 0x20, 0x79, 0x04, 0xf5, 0x88, 0x9b, 0x44, 0x75, 0x5c, 0xd3, 0x6b, 0x12,
	0x95, 0x47, 0x6b, 0x82, 0x69, 0x6a, 0x79, 0xae, 0xcc, 0xec, 0x22, 0xa6, 0xdb, 0x96, 0xe7, 0x6a,
	0xff, 0x9e, 0x83, 0xea, 0x49, 0x40, 0x18, 0x97, 0x2e, 0xcf, 0x97, 0xb9, 0x46, 0xb8, 0xa3, 0x1a,
	0x93, 0x20, 0xaa, 0x56, 0x8a, 0xbc, 0x3d, 0x0c, 0x1c, 0xac, 0x65, 0x02, 0x32, 0xa5, 0xe4, 0x06,
	0x7b, 0xa3, 0xab, 0x03, 0x01, 0x71, 0x82, 0x7d, 0x28, 0x9b, 0x13, 0x9b, 0x7a, 0xd8, 0x2d, 0xeb,
	0x55, 0x04, 0x64, 0xe7, 0x94, 0xda, 0x44, 0x74, 0xca, 0x8a, 0x05, 0x01, 0xde, 0xb9, 0x0b, 0xc5,
	0xd0, 0xf3, 0xb1, 0x4b, 0xd6, 0xaa, 0xa1, 0xe7, 0xf3, 0x8e, 0xcf, 0x01, 0x6e, 0xc8, 0x05, 0xee,
	0x92, 0x5a, 0xd1, 0x7b, 0xdb, 0x0d, 0xb9, 0xe8, 0x23, 0xc0, 0xb9, 0xb5, 0xbc, 0xb1, 0x81, 0xa1,
	0x5f, 0xa6, 0xb4, 0x96, 0x37, 0x3e, 0x21, 0xcc, 0xd2, 0xfe, 0x38, 0x0f, 0x65, 0xdc, 0x19, 0x6e,
	0x2b, 0xab, 0xe3, 0x6d, 0x58, 0x13, 0xd9, 0xba, 0x28, 0xe4, 0x44, 0x43, 0xdd, 0x81, 0x75, 0xca,
	0x8c, 0x09, 0x13, 0xfa, 0x2d, 0xe9, 0x6b, 0x94, 0x0d, 0x19, 0x89, 0x0d, 0xe2, 0xf6, 0x92, 0x87,
	0x9d, 0xb5, 0xc4, 0x81, 0xf3, 0x13, 0x28, 0x05, 0x84, 0x19, 0xd4, 0xbd, 0x14, 0x57, 0x1a, 0x95,
	0xe7, 0x8f, 0x56, 0x9a, 0x67, 0x52, 0xe8, 0x7a, 0x31, 0x90, 0xd2, 0xff, 0x1e, 0x26, 0x43, 0xe4,
	0x5b, 0x9f, 0x04, 0x94, 0xb8, 0xf2, 0xf1, 0x0d, 0x5f, 0x54, 0x5a, 0x31, 0xa6, 0x3e, 0x85, 0xad,
	0x39, 0x85, 0x11, 0x5f, 0x4a, 0x94, 0xd0, 0x8d, 0x37, 0xe7, 0x5d, 0x2d, 0x79, 0x3d, 0xf1, 0x07,
	0xb0, 0xf5, 0x82, 0x84, 0x43, 0x46, 0x02, 0x5c, 0x74, 0xf5, 0x1b, 0xcc, 0x6f, 0x25, 0xee, 0x20,
	0xea, 0xef, 0x38, 0x80, 0x70, 0x1a, 0xac, 0xc2, 0xc4, 0x3d, 0x05, 0x3f, 0x9b, 0x3d, 0x23, 0x2a,
	0x2d, 0x64, 0xad, 0x1a, 0x7a, 0x0d, 0x01, 0x68, 0xaf, 0x61, 0x7b, 0x71, 0x7d, 0xe6, 0xab, 0x3f,
	0x86, 0x32, 0x17, 0xd5, 0x87, 0x1d, 0x7a, 0xb1, 0x2a, 0xf5, 0x12, 0x1f, 0x84, 0xe7, 0xdd, 0x1f,
	0xe5, 0x40, 0xe9, 0x93, 0x50, 0x76, 0x0d, 0x19, 0x59, 0xbe, 0xad, 0x06, 0x80, 0xcd, 0x49, 0x8c,
	0x8f, 0xdc, 0x5c, 0xd9, 0x8e, 0x3e, 0xb9, 0x9d, 0x89, 0x29, 0x64, 0x28, 0xac, 0xe9, 0x45, 0x6c,
	0xb7, 0x6d, 0x6d, 0x0b, 0x36, 0x33, 0x3c, 0x30, 0x5f, 0xfb, 0xf3, 0x1c, 0xec, 0xf7, 0x49, 0xd8,
	0x1c, 0x99, 0x61, 0xdf, 0x27, 0x16, 0x35, 0x9d, 0xf7, 0x31, 0x99, 0x5c, 0x21, 0x9f, 0x5a, 0xe1,
	0x3d, 0xe2, 0xe5, 0x75, 0x65, 0x20, 0x1e, 0xdc, 0x63, 0x1a, 0x61, 0xa8, 0x75, 0x09, 0x47, 0x7a,
	0xf8, 0x02, 0x3e, 0x5b, 0xcd, 0x13, 0xf3, 0xb5, 0x3f, 0x13, 0xef, 0x55, 0xb1, 0xa2, 0x5e, 0x52,
	0x16, 0x7a, 0xc1, 0xec, 0x37, 0x6b, 0x2b, 0x77, 0x60, 0xdd, 0xbb, 0xbc, 0x64, 0x24, 0x94, 0x72,
	0x94, 0x2d, 0x74, 0x48, 0x3a, 0xa6, 0xa1, 0x8c, 0x9b, 0xa2, 0xa1, 0xfd, 0x4d, 0x0e, 0x94, 0x2c,
	0x3f, 0x0b, 0xbe, 0xbc, 0xe4, 0x97, 0x84, 0x5a, 0xf2, 0x97, 0x04, 0x5e, 0xf7, 0x06, 0x64, 0xec,
	0x4d, 0x49, 0xac, 0x3c, 0xd9, 0x5c, 0xea, 0xd9, 0x77, 0xa1, 0xc4, 0x43, 0x0e, 0x5a, 0xa5, 0x7c,
	0xfd, 0xbf, 0x21, 0x17, 0x98, 0x7a, 0xc4, 0x51, 0x63, 0x3d, 0x11, 0x35, 0xb4, 0xff, 0xcc, 0xc1,
	0xee, 0x52, 0xb9, 0x31, 0xff, 0xff, 0x4a, 0x70, 0xea, 0x19, 0x54, 0x47, 0x82, 0x8d, 0x68, 0x23,
	0xdc, 0xbd, 0x1e, 0xaf, 0x7e, 0x58, 0xcc, 0x32, 0x5f, 0x91, 0xc3, 0xd1, 0xd1, 0xfe, 0x81, 0x9b,
	0x73, 0xe2, 0x62, 0xc5, 0x26, 0x6e, 0x48, 0xc3, 0x59, 0xcf, 0xf3, 0x27, 0xbe, 0x3c, 0xc6, 0x03,
	0xf2, 0x56, 0xf8, 0x57, 0x2e, 0x92, 0xf0, 0x5b, 0xf4, 0x1c, 0x29, 0x80, 0xfc, 0xfc, 0x59, 0xf4,
	0x02, 0x8a, 0xba, 0xec, 0xac, 0x41, 0x59, 0x6f, 0xfd, 0xdc, 0xf8, 0xf9, 0xb0, 0xa5, 0xbf, 0x51,
	0x6e, 0xa9, 0x0a, 0x54, 0x79, 0xf3, 0xb4, 0xdd, 0x69, 0xf7, 0x5f, 0xb6, 0x4e, 0x94, 0x9c, 0x7a,
	0x17, 0x76, 0x38, 0xf2, 0xaa, 0x71, 0x36, 0x6c, 0x19, 0xcd, 0x97, 0x8d, 0xce, 0x8b, 0x96, 0x31,
	0x68, 0x9f, 0xb7, 0x94, 0xbc, 0xba, 0x0f, 0xbb, 0xbc, 0xeb, 0x75, 0xeb, 0xd8, 0x68, 0x36, 0x3a,
	0xcd, 0xd6, 0x99, 0xd1, 0xec, 0x76, 0x06, 0x7a, 0xa3, 0x39, 0x50, 0x0a, 0xda, 0x5f, 0xe4, 0xe0,
	0xb3, 0xd5, 0x0c, 0x33, 0x5f, 0x3d, 0x80, 0x2a, 0x65, 0x86, 0x4b, 0x88, 0x6d, 0xf8, 0x13, 0x36,
	0x42, 0xae, 0x4b, 0x3a, 0x50, 0xd6, 0x21, 0xc4, 0xee, 0x4d, 0xd8, 0x08, 0x1f, 0x07, 0xc9, 0xa5,
	0x17, 0x10, 0xc3, 0x99, 0x46, 0xd7, 0x7d, 0x02, 0x38, 0x9b, 0xf2, 0x0d, 0x9b, 0x97, 0x21, 0xaf,
	0x44, 0xa7, 0x91, 0x49, 0x61, 0xfb, 0x6c, 0xaa, 0x7e, 0x06, 0xe0, 0xf3, 0xa3, 0xcc, 0x37, 0xc6,
	0x2c, 0xbe, 0x9a, 0xf3, 0x3d, 0x7f, 0xe8, 0x9f, 0xb3, 0x2b, 0xad, 0x0d, 0xb5, 0x63, 0xd3, 0x75,
	0x49, 0xd0, 0xb0, 0xa7, 0x4d, 0xcf, 0xbd, 0xc4, 0x2b, 0x0f, 0x04, 0x0c, 0x3a, 0xbe, 0x92, 0x76,
	0x52, 0x16, 0x48, 0x7b, 0x7c, 0xc5, 0x17, 0xfa, 0xf5, 0x64, 0xec, 0x27, 0x0e, 0xdc, 0x22, 0x6f,
	0x0f, 0x03, 0x47, 0xfb, 0x43, 0x61, 0x76, 0xa9, 0xe9, 0x56, 0xc7, 0xf6, 0xef, 0x41, 0xcd, 0x77,
	0xcc, 0xf0, 0xd2, 0x0b, 0xc6, 0xf3, 0x38, 0x58, 0xd3, 0xab, 0x11, 0x88, 0xfa, 0x58, 0xf1, 0x08,
	0x5f, 0x58, 0xf1, 0x08, 0xaf, 0xfd, 0x12, 0x33, 0xe7, 0x25, 0x1c, 0x30, 0x5f, 0x6d, 0x40, 0xc9,
	0xb4, 0xa7, 0xc9, 0xe0, 0xbe, 0x3a, 0xe7, 0x4d, 0xcd, 0xa0, 0x17, 0x4d, 0x7b, 0x8a, 0x66, 0xf7,
	0x2b, 0xa8, 0xeb, 0x64, 0xea, 0x5d, 0xcf, 0x5f, 0xa1, 0xdf, 0xf1, 0xb2, 0x96, 0x79, 0x0c, 0xc8,
	0xaf, 0x7a, 0x0c, 0x28, 0xcc, 0x2d, 0x71, 0x13, 0x36, 0x52, 0xf3, 0x33, 0x5f, 0xfb, 0x11, 0x28,
	0x2f, 0x48, 0x38, 0x18, 0x70, 0x7f, 0x88, 0xae, 0x88, 0xa3, 0x57, 0xdf, 0xdc, 0xb2, 0x57, 0xdf,
	0xe4, 0x3b, 0x9e, 0xf6, 0xfb, 0x98, 0xe3, 0x26, 0x47, 0x33, 0x7f, 0xe9, 0xf0, 0x84, 0x57, 0xc8,
	0x0b, 0x06, 0x05, 0x0a, 0xe6, 0x55, 0x94, 0x57, 0xf2, 0xcf, 0xd4, 0x9d, 0xdc, 0xed, 0xcc, 0x9d,
	0xdc, 0x36, 0xac, 0x99, 0x0e, 0x35, 0x59, 0xf4, 0x67, 0x0f, 0x36, 0x34, 0x9a, 0xad, 0xff, 0x32,
	0xbf, 0x28, 0xbc, 0xf7, 0x32, 0x3a, 0x62, 0x35, 0xbf, 0x6c, 0xa7, 0x85, 0xe4, 0x4e, 0xed, 0x6c,
	0xdd, 0xf7, 0xa9, 0x7e, 0xb6, 0x48, 0x17, 0xce, 0xe2, 0x31, 0x78, 0xc9, 0x05, 0x8d, 0xb6, 0x97,
	0xba, 0x68, 0x89, 0x49, 0x99, 0xaf, 0x79, 0xb0, 0xd5, 0x1c, 0x11, 0xeb, 0x5a, 0xfc, 0x8f, 0xd2,
	0xe3, 0x9e, 0xc8, 0xa7, 0xf8, 0x31, 0xac, 0x89, 0x27, 0x4d, 0x51, 0x48, 0xac, 0x0e, 0x8d, 0x89,
	0x71, 0xf8, 0xe4, 0xa9, 0x8b, 0x71, 0xc9, 0x1f, 0x11, 0xf2, 0x32, 0x22, 0x63, 0xbc, 0xd6, 0xfe,
	0x31, 0x07, 0xdb, 0x8b, 0x2b, 0x32, 0x5f, 0xfd, 0x7f, 0xa0, 0x52, 0x66, 0xf0, 0xf4, 0x9f, 0x6f,
	0xf2, 0x8a, 0xe0, 0xbd, 0xa4, 0x0c, 0x3d, 0x1b, 0x94, 0xe9, 0xc4, 0xee, 0x21, 0xde, 0x1f, 0x79,
	0x37, 0xf2, 0xef, 0x9a, 0x80, 0x97, 0x28, 0xae, 0x20, 0xcc, 0x47, 0xb9, 0xa0, 0x8e, 0x20, 0x52,
	0x3d, 0x80, 0x6a, 0x40, 0x98, 0x37, 0x09, 0x2c, 0x92, 0xc8, 0xba, 0x2b, 0x11, 0xc6, 0x53, 0xe8,
	0x24, 0xc9, 0xd8, 0xfe, 0x26, 0x7a, 0xb3, 0x8b, 0xb0, 0x73, 0xfb, 0x1b, 0xed, 0x06, 0xef, 0x28,
	0x04, 0xbb, 0x62, 0x72, 0x64, 0x1a, 0xbd, 0xf1, 0x93, 0x8a, 0xea, 0x5f, 0x73, 0x68, 0xb2, 0x2b,
	0x56, 0x66, 0x3e, 0x77, 0x82, 0x80, 0x8c, 0xa9, 0x6b, 0x93, 0x40, 0xda, 0x6b, 0xdc, 0xe6, 0xb3,
	0x32, 0xd3, 0x49, 0xbc, 0xb8, 0xae, 0xf3, 0xa6, 0x78, 0x4d, 0x8d, 0x6c, 0x2f, 0x59, 0xac, 0x49,
	0x6c, 0x20, 0x9f, 0xd6, 0xf0, 0x52, 0x38, 0xfe, 0x03, 0xb0, 0xa6, 0x97, 0x38, 0x80, 0x91, 0x22,
	0xde, 0xef, 0xda, 0x77, 0xdb, 0xaf, 0xe6, 0xc2, 0x76, 0xa2, 0xab, 0xe1, 0xda, 0x78, 0x65, 0xf3,
	0x1b, 0x10, 0xa4, 0x28, 0x50, 0x7c, 0x73, 0x26, 0x8d, 0x61, 0x8d, 0xb2, 0x9e, 0x39, 0xd3, 0x76,
	0x61, 0x67, 0xc9, 0x7a, 0xcc, 0x3f, 0xea, 0xc0, 0x46, 0xa6, 0x0e, 0x56, 0xeb, 0x00, 0x5d, 0xfd,
	0xa4, 0xa5, 0x1b, 0xed, 0x4e, 0x7b, 0xa0, 0xdc, 0x52, 0x77, 0x60, 0x53, 0xb4, 0x7b, 0x8d, 0x37,
	0x46, 0x7f, 0xd8, 0x6c, 0xb6, 0xfa, 0x7d, 0x25, 0xa7, 0x6e, 0x83, 0x32, 0x87, 0x4f, 0x1b, 0xed,
	0xb3, 0xd6, 0x89, 0x92, 0x3f, 0x3a, 0x86, 0xca, 0x79, 0xa2, 0xe4, 0xad, 0x40, 0x71, 0xe8, 0x5e,
	0xbb, 0xde, 0x8d, 0xab, 0xdc, 0x52, 0xcb, 0xb0, 0x76, 0x62, 0x52, 0x67, 0xa6, 0xe4, 0xf8, 0xb9,
	0xce, 0x05, 0x79, 0xc6, 0x53, 0x11, 0x25, 0xaf, 0x56, 0xa1, 0xd4, 0xb0, 0x42, 0x3a, 0xa5, 0xe1,
	0x4c, 0x29, 0x1c, 0xfd, 0x28, 0xae, 0xbf, 0xe7, 0x1c, 0xb5, 0x5d, 0xcb, 0x1b, 0xfb, 0x0e, 0x09,
	0x89, 0x72, 0x8b, 0x8f, 0x6e, 0xca, 0x96, 0xad, 0xe4, 0xf8, 0xe8, 0x53, 0xac, 0x7d, 0x89, 0xad,
	0xe4, 0x8f, 0xfe, 0x2a, 0x27, 0xcb, 0x3a, 0x99, 0x97, 0xef, 0x9c, 0xe8, 0xad, 0x7e, 0xdf, 0x18,
	0xbc, 0xe9, 0xb5, 0x8c, 0x61, 0xa7, 0xdf, 0x6b, 0x35, 0xdb, 0xa7, 0xed, 0xa6, 0x72, 0x4b, 0xdd,
	0x83, 0xed, 0x44, 0x97, 0xde, 0xed, 0x9e, 0x1b, 0xfd, 0x61, 0x7b, 0xa0, 0xe4, 0xd4, 0x2f, 0xe0,
	0x5e, 0xa2, 0x07, 0x87, 0x34, 0x30, 0x7b, 0x68, 0xb6, 0xf4, 0x8e, 0x92, 0xcf, 0xf4, 0x37, 0x5f,
	0x36, 0x06, 0xc6, 0x71, 0xa3, 0xf9, 0xb3, 0x17, 0x7a, 0x77, 0xd8, 0x39, 0x51, 0x0a, 0xea, 0x3d,
	0xb8, 0xb3, 0xd0, 0x3f, 0x3c, 0x3e, 0x3e, 0x6b, 0x29, 0xb7, 0x8f, 0x5e, 0x43, 0xb5, 0xe7, 0x98,
	0xe1, 0x69, 0x74, 0xa2, 0xee, 0xc0, 0x66, 0xab, 0x33, 0x3c, 0x37, 0x7a, 0x67, 0x8d, 0xc1, 0x69,
	0x57, 0x3f, 0x37, 0x1a, 0x67, 0x67, 0xca, 0x2d, 0xce, 0x77, 0x06, 0xee, 0x9c, 0xe8, 0xdd, 0x36,
	0x4f, 0x79, 0x16, 0x46, 0xb4, 0xbb, 0x7d, 0x25, 0x7f, 0xf4, 0xf7, 0x39, 0xd8, 0x5c, 0xb8, 0x48,
	0x57, 0x77, 0x61, 0x0b, 0x89, 0xfb, 0x83, 0xc6, 0x60, 0xd8, 0x37, 0x3a, 0x5d, 0xa3, 0xdb, 0x6b,
	0x75, 0x94, 0x5b, 0xd9, 0x0e, 0x8e, 0xb6, 0x3b, 0x2f, 0x94, 0x1c, 0x4f, 0x9b, 0x92, 0x1d, 0xfd,
	0x6e, 0xb7, 0x63, 0xb4, 0x7e, 0xd1, 0x6b, 0xeb, 0x3c, 0xa7, 0xca, 0x8c, 0x12, 0x38, 0xdf, 0xf2,
	0x36, 0x28, 0xa9, 0x51, 0xed, 0x17, 0x1d, 0xe5, 0x76, 0x96, 0xbc, 0xd7, 0xea, 0x9c, 0xf0, 0x45,
	0xd6, 0x8e, 0x7e, 0x08, 0x1b, 0x99, 0xf7, 0xba, 0x78, 0x06, 0x94, 0x59, 0xa7, 0xab, 0x9f, 0x37,
	0xb8, 0x1c, 0x54, 0xa8, 0xcf, 0xd1, 0xfe, 0xab, 0x76, 0x4f, 0xc9, 0x1d, 0xfd, 0x75, 0x1e, 0x94,
	0xac, 0xfd, 0xab, 0x87, 0xf0, 0x10, 0x09, 0xf5, 0x56, 0xa7, 0xf5, 0xba, 0x71, 0x66, 0xf4, 0xba,
	0xbd, 0x61, 0xcf, 0xe8, 0x37, 0x5b, 0x9d, 0x84, 0xda, 0x5b, 0x27, 0xca, 0x2d, 0xf5, 0x08, 0xbe,
	0x5c, 0x49, 0xd9, 0x6b, 0xe9, 0xfd, 0x6e, 0x87, 0x83, 0x8d, 0x17, 0x2d, 0x25, 0xa7, 0x3e, 0x86,
	0x47, 0x2b, 0x69, 0x5b, 0x9d, 0x81, 0xfe, 0xc6, 0x38, 0x6e, 0x74, 0x3a, 0x2d, 0x5d, 0xc9, 0xab,
	0x4f, 0xe0, 0x70, 0x25, 0x69, 0x7f, 0xd8, 0xe3, 0x8e, 0x72, 0xd6, 0x78, 0x23, 0x26, 0x2e, 0xa8,
	0xcf, 0xe1, 0xe9, 0x87, 0x52, 0x1b, 0x67, 0xad, 0xc6, 0xab, 0x96, 0x72, 0xfb, 0x9d, 0x5b, 0x7c,
	0xfd, 0xb2, 0x6b, 0x9c, 0xb7, 0x3b, 0x27, 0xc6, 0x79, 0x4b, 0x29, 0x1e, 0xff, 0xf0, 0xf7, 0x7e,
	0xe7, 0xca, 0x73, 0x4c, 0xf7, 0xea, 0xe9, 0x37, 0xcf, 0xc3, 0xf0, 0xa9, 0xe5, 0x8d, 0x9f, 0xe1,
	0xef, 0xde, 0x96, 0xe7, 0x3c, 0x63, 0x24, 0x98, 0x52, 0x8b, 0xb0, 0x55, 0x3f, 0x86, 0x5f, 0xac,
	0x23, 0xe9, 0x0f, 0xfe, 0x27, 0x00, 0x00, 0xff, 0xff, 0xf0, 0x8e, 0x47, 0xbe, 0x61, 0x2e, 0x00,
	0x00,
}
