// Code generated by protoc-gen-go. DO NOT EDIT.
// source: src/attachmentsvr/attachment.proto

package Attachment

/*
namespace
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type StImMsgCache struct {
	TargetUid            *uint32  `protobuf:"varint,1,opt,name=target_uid,json=targetUid" json:"target_uid,omitempty"`
	TargetGroup          *uint32  `protobuf:"varint,2,opt,name=target_group,json=targetGroup" json:"target_group,omitempty"`
	SvrMsgId             *uint32  `protobuf:"varint,3,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id,omitempty"`
	Msg                  []byte   `protobuf:"bytes,4,req,name=msg" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StImMsgCache) Reset()         { *m = StImMsgCache{} }
func (m *StImMsgCache) String() string { return proto.CompactTextString(m) }
func (*StImMsgCache) ProtoMessage()    {}
func (*StImMsgCache) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{0}
}
func (m *StImMsgCache) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StImMsgCache.Unmarshal(m, b)
}
func (m *StImMsgCache) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StImMsgCache.Marshal(b, m, deterministic)
}
func (dst *StImMsgCache) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StImMsgCache.Merge(dst, src)
}
func (m *StImMsgCache) XXX_Size() int {
	return xxx_messageInfo_StImMsgCache.Size(m)
}
func (m *StImMsgCache) XXX_DiscardUnknown() {
	xxx_messageInfo_StImMsgCache.DiscardUnknown(m)
}

var xxx_messageInfo_StImMsgCache proto.InternalMessageInfo

func (m *StImMsgCache) GetTargetUid() uint32 {
	if m != nil && m.TargetUid != nil {
		return *m.TargetUid
	}
	return 0
}

func (m *StImMsgCache) GetTargetGroup() uint32 {
	if m != nil && m.TargetGroup != nil {
		return *m.TargetGroup
	}
	return 0
}

func (m *StImMsgCache) GetSvrMsgId() uint32 {
	if m != nil && m.SvrMsgId != nil {
		return *m.SvrMsgId
	}
	return 0
}

func (m *StImMsgCache) GetMsg() []byte {
	if m != nil {
		return m.Msg
	}
	return nil
}

type StFileProperty struct {
	SvrTime              *uint32         `protobuf:"varint,1,opt,name=svr_time,json=svrTime" json:"svr_time,omitempty"`
	ClientProp           []byte          `protobuf:"bytes,2,opt,name=client_prop,json=clientProp" json:"client_prop,omitempty"`
	Status               *uint32         `protobuf:"varint,3,opt,name=status" json:"status,omitempty"`
	CacheMsgList         []*StImMsgCache `protobuf:"bytes,4,rep,name=cache_msg_list,json=cacheMsgList" json:"cache_msg_list,omitempty"`
	Type                 *uint32         `protobuf:"varint,5,req,name=type" json:"type,omitempty"`
	TargetAccount        *string         `protobuf:"bytes,6,req,name=target_account,json=targetAccount" json:"target_account,omitempty"`
	FromAccount          *string         `protobuf:"bytes,7,req,name=from_account,json=fromAccount" json:"from_account,omitempty"`
	NewFileLocation      *MMBufLocationT `protobuf:"bytes,8,opt,name=new_file_location,json=newFileLocation" json:"new_file_location,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *StFileProperty) Reset()         { *m = StFileProperty{} }
func (m *StFileProperty) String() string { return proto.CompactTextString(m) }
func (*StFileProperty) ProtoMessage()    {}
func (*StFileProperty) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{1}
}
func (m *StFileProperty) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StFileProperty.Unmarshal(m, b)
}
func (m *StFileProperty) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StFileProperty.Marshal(b, m, deterministic)
}
func (dst *StFileProperty) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StFileProperty.Merge(dst, src)
}
func (m *StFileProperty) XXX_Size() int {
	return xxx_messageInfo_StFileProperty.Size(m)
}
func (m *StFileProperty) XXX_DiscardUnknown() {
	xxx_messageInfo_StFileProperty.DiscardUnknown(m)
}

var xxx_messageInfo_StFileProperty proto.InternalMessageInfo

func (m *StFileProperty) GetSvrTime() uint32 {
	if m != nil && m.SvrTime != nil {
		return *m.SvrTime
	}
	return 0
}

func (m *StFileProperty) GetClientProp() []byte {
	if m != nil {
		return m.ClientProp
	}
	return nil
}

func (m *StFileProperty) GetStatus() uint32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

func (m *StFileProperty) GetCacheMsgList() []*StImMsgCache {
	if m != nil {
		return m.CacheMsgList
	}
	return nil
}

func (m *StFileProperty) GetType() uint32 {
	if m != nil && m.Type != nil {
		return *m.Type
	}
	return 0
}

func (m *StFileProperty) GetTargetAccount() string {
	if m != nil && m.TargetAccount != nil {
		return *m.TargetAccount
	}
	return ""
}

func (m *StFileProperty) GetFromAccount() string {
	if m != nil && m.FromAccount != nil {
		return *m.FromAccount
	}
	return ""
}

func (m *StFileProperty) GetNewFileLocation() *MMBufLocationT {
	if m != nil {
		return m.NewFileLocation
	}
	return nil
}

type StFile struct {
	Data                 []byte          `protobuf:"bytes,1,req,name=data" json:"data,omitempty"`
	Prop                 *StFileProperty `protobuf:"bytes,2,req,name=prop" json:"prop,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *StFile) Reset()         { *m = StFile{} }
func (m *StFile) String() string { return proto.CompactTextString(m) }
func (*StFile) ProtoMessage()    {}
func (*StFile) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{2}
}
func (m *StFile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StFile.Unmarshal(m, b)
}
func (m *StFile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StFile.Marshal(b, m, deterministic)
}
func (dst *StFile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StFile.Merge(dst, src)
}
func (m *StFile) XXX_Size() int {
	return xxx_messageInfo_StFile.Size(m)
}
func (m *StFile) XXX_DiscardUnknown() {
	xxx_messageInfo_StFile.DiscardUnknown(m)
}

var xxx_messageInfo_StFile proto.InternalMessageInfo

func (m *StFile) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *StFile) GetProp() *StFileProperty {
	if m != nil {
		return m.Prop
	}
	return nil
}

type AddFileReq struct {
	Key                  *string  `protobuf:"bytes,1,req,name=key" json:"key,omitempty"`
	File                 *StFile  `protobuf:"bytes,2,req,name=file" json:"file,omitempty"`
	Ttl                  *uint32  `protobuf:"varint,3,req,name=ttl" json:"ttl,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFileReq) Reset()         { *m = AddFileReq{} }
func (m *AddFileReq) String() string { return proto.CompactTextString(m) }
func (*AddFileReq) ProtoMessage()    {}
func (*AddFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{3}
}
func (m *AddFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFileReq.Unmarshal(m, b)
}
func (m *AddFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFileReq.Marshal(b, m, deterministic)
}
func (dst *AddFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFileReq.Merge(dst, src)
}
func (m *AddFileReq) XXX_Size() int {
	return xxx_messageInfo_AddFileReq.Size(m)
}
func (m *AddFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddFileReq proto.InternalMessageInfo

func (m *AddFileReq) GetKey() string {
	if m != nil && m.Key != nil {
		return *m.Key
	}
	return ""
}

func (m *AddFileReq) GetFile() *StFile {
	if m != nil {
		return m.File
	}
	return nil
}

func (m *AddFileReq) GetTtl() uint32 {
	if m != nil && m.Ttl != nil {
		return *m.Ttl
	}
	return 0
}

type GetFileReq struct {
	Key                  *string  `protobuf:"bytes,1,req,name=key" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFileReq) Reset()         { *m = GetFileReq{} }
func (m *GetFileReq) String() string { return proto.CompactTextString(m) }
func (*GetFileReq) ProtoMessage()    {}
func (*GetFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{4}
}
func (m *GetFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFileReq.Unmarshal(m, b)
}
func (m *GetFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFileReq.Marshal(b, m, deterministic)
}
func (dst *GetFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFileReq.Merge(dst, src)
}
func (m *GetFileReq) XXX_Size() int {
	return xxx_messageInfo_GetFileReq.Size(m)
}
func (m *GetFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFileReq proto.InternalMessageInfo

func (m *GetFileReq) GetKey() string {
	if m != nil && m.Key != nil {
		return *m.Key
	}
	return ""
}

type GetFileResp struct {
	File                 *StFile  `protobuf:"bytes,1,opt,name=file" json:"file,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFileResp) Reset()         { *m = GetFileResp{} }
func (m *GetFileResp) String() string { return proto.CompactTextString(m) }
func (*GetFileResp) ProtoMessage()    {}
func (*GetFileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{5}
}
func (m *GetFileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFileResp.Unmarshal(m, b)
}
func (m *GetFileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFileResp.Marshal(b, m, deterministic)
}
func (dst *GetFileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFileResp.Merge(dst, src)
}
func (m *GetFileResp) XXX_Size() int {
	return xxx_messageInfo_GetFileResp.Size(m)
}
func (m *GetFileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFileResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFileResp proto.InternalMessageInfo

func (m *GetFileResp) GetFile() *StFile {
	if m != nil {
		return m.File
	}
	return nil
}

type MMAttributeT struct {
	IDataLen             *uint32  `protobuf:"varint,1,req,name=iDataLen" json:"iDataLen,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MMAttributeT) Reset()         { *m = MMAttributeT{} }
func (m *MMAttributeT) String() string { return proto.CompactTextString(m) }
func (*MMAttributeT) ProtoMessage()    {}
func (*MMAttributeT) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{6}
}
func (m *MMAttributeT) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MMAttributeT.Unmarshal(m, b)
}
func (m *MMAttributeT) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MMAttributeT.Marshal(b, m, deterministic)
}
func (dst *MMAttributeT) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MMAttributeT.Merge(dst, src)
}
func (m *MMAttributeT) XXX_Size() int {
	return xxx_messageInfo_MMAttributeT.Size(m)
}
func (m *MMAttributeT) XXX_DiscardUnknown() {
	xxx_messageInfo_MMAttributeT.DiscardUnknown(m)
}

var xxx_messageInfo_MMAttributeT proto.InternalMessageInfo

func (m *MMAttributeT) GetIDataLen() uint32 {
	if m != nil && m.IDataLen != nil {
		return *m.IDataLen
	}
	return 0
}

type MMBufDataT struct {
	TAttribute           *MMAttributeT                 `protobuf:"bytes,1,req,name=tAttribute" json:"tAttribute,omitempty"`
	TData                *tlvpickle.SKBuiltinBuffer_PB `protobuf:"bytes,2,req,name=tData" json:"tData,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *MMBufDataT) Reset()         { *m = MMBufDataT{} }
func (m *MMBufDataT) String() string { return proto.CompactTextString(m) }
func (*MMBufDataT) ProtoMessage()    {}
func (*MMBufDataT) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{7}
}
func (m *MMBufDataT) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MMBufDataT.Unmarshal(m, b)
}
func (m *MMBufDataT) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MMBufDataT.Marshal(b, m, deterministic)
}
func (dst *MMBufDataT) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MMBufDataT.Merge(dst, src)
}
func (m *MMBufDataT) XXX_Size() int {
	return xxx_messageInfo_MMBufDataT.Size(m)
}
func (m *MMBufDataT) XXX_DiscardUnknown() {
	xxx_messageInfo_MMBufDataT.DiscardUnknown(m)
}

var xxx_messageInfo_MMBufDataT proto.InternalMessageInfo

func (m *MMBufDataT) GetTAttribute() *MMAttributeT {
	if m != nil {
		return m.TAttribute
	}
	return nil
}

func (m *MMBufDataT) GetTData() *tlvpickle.SKBuiltinBuffer_PB {
	if m != nil {
		return m.TData
	}
	return nil
}

type MMBufLocationT struct {
	IOffset              *uint32  `protobuf:"varint,1,req,name=iOffset" json:"iOffset,omitempty"`
	IFileIndex           *uint32  `protobuf:"varint,2,req,name=iFileIndex" json:"iFileIndex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MMBufLocationT) Reset()         { *m = MMBufLocationT{} }
func (m *MMBufLocationT) String() string { return proto.CompactTextString(m) }
func (*MMBufLocationT) ProtoMessage()    {}
func (*MMBufLocationT) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{8}
}
func (m *MMBufLocationT) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MMBufLocationT.Unmarshal(m, b)
}
func (m *MMBufLocationT) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MMBufLocationT.Marshal(b, m, deterministic)
}
func (dst *MMBufLocationT) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MMBufLocationT.Merge(dst, src)
}
func (m *MMBufLocationT) XXX_Size() int {
	return xxx_messageInfo_MMBufLocationT.Size(m)
}
func (m *MMBufLocationT) XXX_DiscardUnknown() {
	xxx_messageInfo_MMBufLocationT.DiscardUnknown(m)
}

var xxx_messageInfo_MMBufLocationT proto.InternalMessageInfo

func (m *MMBufLocationT) GetIOffset() uint32 {
	if m != nil && m.IOffset != nil {
		return *m.IOffset
	}
	return 0
}

func (m *MMBufLocationT) GetIFileIndex() uint32 {
	if m != nil && m.IFileIndex != nil {
		return *m.IFileIndex
	}
	return 0
}

type LowLevelAddFileReq struct {
	Data                 []byte   `protobuf:"bytes,1,req,name=data" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LowLevelAddFileReq) Reset()         { *m = LowLevelAddFileReq{} }
func (m *LowLevelAddFileReq) String() string { return proto.CompactTextString(m) }
func (*LowLevelAddFileReq) ProtoMessage()    {}
func (*LowLevelAddFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{9}
}
func (m *LowLevelAddFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LowLevelAddFileReq.Unmarshal(m, b)
}
func (m *LowLevelAddFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LowLevelAddFileReq.Marshal(b, m, deterministic)
}
func (dst *LowLevelAddFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LowLevelAddFileReq.Merge(dst, src)
}
func (m *LowLevelAddFileReq) XXX_Size() int {
	return xxx_messageInfo_LowLevelAddFileReq.Size(m)
}
func (m *LowLevelAddFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LowLevelAddFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_LowLevelAddFileReq proto.InternalMessageInfo

func (m *LowLevelAddFileReq) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type LowLevelAddFileResp struct {
	FileLocation         *MMBufLocationT `protobuf:"bytes,1,req,name=file_location,json=fileLocation" json:"file_location,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *LowLevelAddFileResp) Reset()         { *m = LowLevelAddFileResp{} }
func (m *LowLevelAddFileResp) String() string { return proto.CompactTextString(m) }
func (*LowLevelAddFileResp) ProtoMessage()    {}
func (*LowLevelAddFileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{10}
}
func (m *LowLevelAddFileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LowLevelAddFileResp.Unmarshal(m, b)
}
func (m *LowLevelAddFileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LowLevelAddFileResp.Marshal(b, m, deterministic)
}
func (dst *LowLevelAddFileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LowLevelAddFileResp.Merge(dst, src)
}
func (m *LowLevelAddFileResp) XXX_Size() int {
	return xxx_messageInfo_LowLevelAddFileResp.Size(m)
}
func (m *LowLevelAddFileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LowLevelAddFileResp.DiscardUnknown(m)
}

var xxx_messageInfo_LowLevelAddFileResp proto.InternalMessageInfo

func (m *LowLevelAddFileResp) GetFileLocation() *MMBufLocationT {
	if m != nil {
		return m.FileLocation
	}
	return nil
}

type LowLevelGetFileReq struct {
	FileLocation         *MMBufLocationT `protobuf:"bytes,1,req,name=file_location,json=fileLocation" json:"file_location,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *LowLevelGetFileReq) Reset()         { *m = LowLevelGetFileReq{} }
func (m *LowLevelGetFileReq) String() string { return proto.CompactTextString(m) }
func (*LowLevelGetFileReq) ProtoMessage()    {}
func (*LowLevelGetFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{11}
}
func (m *LowLevelGetFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LowLevelGetFileReq.Unmarshal(m, b)
}
func (m *LowLevelGetFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LowLevelGetFileReq.Marshal(b, m, deterministic)
}
func (dst *LowLevelGetFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LowLevelGetFileReq.Merge(dst, src)
}
func (m *LowLevelGetFileReq) XXX_Size() int {
	return xxx_messageInfo_LowLevelGetFileReq.Size(m)
}
func (m *LowLevelGetFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LowLevelGetFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_LowLevelGetFileReq proto.InternalMessageInfo

func (m *LowLevelGetFileReq) GetFileLocation() *MMBufLocationT {
	if m != nil {
		return m.FileLocation
	}
	return nil
}

type LowLevelGetFileResp struct {
	Data                 []byte   `protobuf:"bytes,1,req,name=data" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LowLevelGetFileResp) Reset()         { *m = LowLevelGetFileResp{} }
func (m *LowLevelGetFileResp) String() string { return proto.CompactTextString(m) }
func (*LowLevelGetFileResp) ProtoMessage()    {}
func (*LowLevelGetFileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{12}
}
func (m *LowLevelGetFileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LowLevelGetFileResp.Unmarshal(m, b)
}
func (m *LowLevelGetFileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LowLevelGetFileResp.Marshal(b, m, deterministic)
}
func (dst *LowLevelGetFileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LowLevelGetFileResp.Merge(dst, src)
}
func (m *LowLevelGetFileResp) XXX_Size() int {
	return xxx_messageInfo_LowLevelGetFileResp.Size(m)
}
func (m *LowLevelGetFileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LowLevelGetFileResp.DiscardUnknown(m)
}

var xxx_messageInfo_LowLevelGetFileResp proto.InternalMessageInfo

func (m *LowLevelGetFileResp) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type DelFileReq struct {
	Key                  *string  `protobuf:"bytes,1,req,name=key" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFileReq) Reset()         { *m = DelFileReq{} }
func (m *DelFileReq) String() string { return proto.CompactTextString(m) }
func (*DelFileReq) ProtoMessage()    {}
func (*DelFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{13}
}
func (m *DelFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFileReq.Unmarshal(m, b)
}
func (m *DelFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFileReq.Marshal(b, m, deterministic)
}
func (dst *DelFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFileReq.Merge(dst, src)
}
func (m *DelFileReq) XXX_Size() int {
	return xxx_messageInfo_DelFileReq.Size(m)
}
func (m *DelFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelFileReq proto.InternalMessageInfo

func (m *DelFileReq) GetKey() string {
	if m != nil && m.Key != nil {
		return *m.Key
	}
	return ""
}

type DelFileResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFileResp) Reset()         { *m = DelFileResp{} }
func (m *DelFileResp) String() string { return proto.CompactTextString(m) }
func (*DelFileResp) ProtoMessage()    {}
func (*DelFileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_attachment_68cef8123ab80ee2, []int{14}
}
func (m *DelFileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFileResp.Unmarshal(m, b)
}
func (m *DelFileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFileResp.Marshal(b, m, deterministic)
}
func (dst *DelFileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFileResp.Merge(dst, src)
}
func (m *DelFileResp) XXX_Size() int {
	return xxx_messageInfo_DelFileResp.Size(m)
}
func (m *DelFileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFileResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelFileResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*StImMsgCache)(nil), "Attachment.stImMsgCache")
	proto.RegisterType((*StFileProperty)(nil), "Attachment.stFileProperty")
	proto.RegisterType((*StFile)(nil), "Attachment.stFile")
	proto.RegisterType((*AddFileReq)(nil), "Attachment.AddFileReq")
	proto.RegisterType((*GetFileReq)(nil), "Attachment.GetFileReq")
	proto.RegisterType((*GetFileResp)(nil), "Attachment.GetFileResp")
	proto.RegisterType((*MMAttributeT)(nil), "Attachment.MMAttribute_t")
	proto.RegisterType((*MMBufDataT)(nil), "Attachment.MMBufData_t")
	proto.RegisterType((*MMBufLocationT)(nil), "Attachment.MMBufLocation_t")
	proto.RegisterType((*LowLevelAddFileReq)(nil), "Attachment.LowLevelAddFileReq")
	proto.RegisterType((*LowLevelAddFileResp)(nil), "Attachment.LowLevelAddFileResp")
	proto.RegisterType((*LowLevelGetFileReq)(nil), "Attachment.LowLevelGetFileReq")
	proto.RegisterType((*LowLevelGetFileResp)(nil), "Attachment.LowLevelGetFileResp")
	proto.RegisterType((*DelFileReq)(nil), "Attachment.DelFileReq")
	proto.RegisterType((*DelFileResp)(nil), "Attachment.DelFileResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AttachmentClient is the client API for Attachment service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AttachmentClient interface {
	// 高版本接口的 上传文件
	// 高版本接口负责文件存取 同时 管理索引
	AddFile(ctx context.Context, in *AddFileReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFile(ctx context.Context, in *GetFileReq, opts ...grpc.CallOption) (*GetFileResp, error)
	// 低版本接口的 上传与获取文件
	// 低版本接口只负责文件存取 不管理索引
	LowLevelAddFile(ctx context.Context, in *LowLevelAddFileReq, opts ...grpc.CallOption) (*LowLevelAddFileResp, error)
	LowLevelGetFile(ctx context.Context, in *LowLevelGetFileReq, opts ...grpc.CallOption) (*LowLevelGetFileResp, error)
	DelFile(ctx context.Context, in *DelFileReq, opts ...grpc.CallOption) (*DelFileResp, error)
}

type attachmentClient struct {
	cc *grpc.ClientConn
}

func NewAttachmentClient(cc *grpc.ClientConn) AttachmentClient {
	return &attachmentClient{cc}
}

func (c *attachmentClient) AddFile(ctx context.Context, in *AddFileReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, "/Attachment.Attachment/AddFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *attachmentClient) GetFile(ctx context.Context, in *GetFileReq, opts ...grpc.CallOption) (*GetFileResp, error) {
	out := new(GetFileResp)
	err := c.cc.Invoke(ctx, "/Attachment.Attachment/GetFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *attachmentClient) LowLevelAddFile(ctx context.Context, in *LowLevelAddFileReq, opts ...grpc.CallOption) (*LowLevelAddFileResp, error) {
	out := new(LowLevelAddFileResp)
	err := c.cc.Invoke(ctx, "/Attachment.Attachment/LowLevelAddFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *attachmentClient) LowLevelGetFile(ctx context.Context, in *LowLevelGetFileReq, opts ...grpc.CallOption) (*LowLevelGetFileResp, error) {
	out := new(LowLevelGetFileResp)
	err := c.cc.Invoke(ctx, "/Attachment.Attachment/LowLevelGetFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *attachmentClient) DelFile(ctx context.Context, in *DelFileReq, opts ...grpc.CallOption) (*DelFileResp, error) {
	out := new(DelFileResp)
	err := c.cc.Invoke(ctx, "/Attachment.Attachment/DelFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AttachmentServer is the server API for Attachment service.
type AttachmentServer interface {
	// 高版本接口的 上传文件
	// 高版本接口负责文件存取 同时 管理索引
	AddFile(context.Context, *AddFileReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFile(context.Context, *GetFileReq) (*GetFileResp, error)
	// 低版本接口的 上传与获取文件
	// 低版本接口只负责文件存取 不管理索引
	LowLevelAddFile(context.Context, *LowLevelAddFileReq) (*LowLevelAddFileResp, error)
	LowLevelGetFile(context.Context, *LowLevelGetFileReq) (*LowLevelGetFileResp, error)
	DelFile(context.Context, *DelFileReq) (*DelFileResp, error)
}

func RegisterAttachmentServer(s *grpc.Server, srv AttachmentServer) {
	s.RegisterService(&_Attachment_serviceDesc, srv)
}

func _Attachment_AddFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttachmentServer).AddFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Attachment.Attachment/AddFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttachmentServer).AddFile(ctx, req.(*AddFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Attachment_GetFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttachmentServer).GetFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Attachment.Attachment/GetFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttachmentServer).GetFile(ctx, req.(*GetFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Attachment_LowLevelAddFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LowLevelAddFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttachmentServer).LowLevelAddFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Attachment.Attachment/LowLevelAddFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttachmentServer).LowLevelAddFile(ctx, req.(*LowLevelAddFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Attachment_LowLevelGetFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LowLevelGetFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttachmentServer).LowLevelGetFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Attachment.Attachment/LowLevelGetFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttachmentServer).LowLevelGetFile(ctx, req.(*LowLevelGetFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Attachment_DelFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttachmentServer).DelFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Attachment.Attachment/DelFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttachmentServer).DelFile(ctx, req.(*DelFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Attachment_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Attachment.Attachment",
	HandlerType: (*AttachmentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddFile",
			Handler:    _Attachment_AddFile_Handler,
		},
		{
			MethodName: "GetFile",
			Handler:    _Attachment_GetFile_Handler,
		},
		{
			MethodName: "LowLevelAddFile",
			Handler:    _Attachment_LowLevelAddFile_Handler,
		},
		{
			MethodName: "LowLevelGetFile",
			Handler:    _Attachment_LowLevelGetFile_Handler,
		},
		{
			MethodName: "DelFile",
			Handler:    _Attachment_DelFile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/attachmentsvr/attachment.proto",
}

func init() {
	proto.RegisterFile("src/attachmentsvr/attachment.proto", fileDescriptor_attachment_68cef8123ab80ee2)
}

var fileDescriptor_attachment_68cef8123ab80ee2 = []byte{
	// 882 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x54, 0xdf, 0x6e, 0xe3, 0xc4,
	0x17, 0x56, 0xe2, 0xb4, 0x69, 0x8f, 0xe3, 0xf6, 0xf7, 0x1b, 0xa4, 0xc5, 0x0d, 0xbb, 0xa9, 0xb1,
	0x16, 0x14, 0x84, 0x92, 0x4a, 0x59, 0x71, 0xb1, 0x55, 0x15, 0xd1, 0xb0, 0x50, 0x55, 0x9b, 0x88,
	0xca, 0xb0, 0x80, 0xb8, 0xb1, 0xbc, 0xf6, 0x24, 0x3b, 0x8a, 0xff, 0x31, 0x73, 0x92, 0x12, 0x24,
	0xa4, 0xbd, 0x63, 0xc5, 0x25, 0xcf, 0xd0, 0x17, 0xe1, 0x9a, 0x17, 0xe9, 0x63, 0xa0, 0x19, 0x3b,
	0xc9, 0x64, 0x9b, 0xf6, 0x86, 0xbb, 0x39, 0xdf, 0xf9, 0xf3, 0x9d, 0xf3, 0x9d, 0x63, 0x83, 0x2b,
	0x78, 0x78, 0x12, 0x20, 0x06, 0xe1, 0x9b, 0x84, 0xa6, 0x28, 0xe6, 0x5c, 0xb3, 0xba, 0x39, 0xcf,
	0x30, 0x23, 0x70, 0xbe, 0x42, 0x9a, 0x4f, 0xc3, 0x2c, 0x49, 0xb2, 0xf4, 0x04, 0xe3, 0x79, 0xce,
	0xc2, 0x69, 0x4c, 0x4f, 0xc4, 0xf4, 0xf5, 0x8c, 0xc5, 0xc8, 0x52, 0x5c, 0xe4, 0xb4, 0xc8, 0x70,
	0xdf, 0x56, 0xa0, 0x21, 0xf0, 0x32, 0x19, 0x89, 0xc9, 0x57, 0x41, 0xf8, 0x86, 0x92, 0x27, 0x00,
	0x18, 0xf0, 0x09, 0x45, 0x7f, 0xc6, 0x22, 0xbb, 0xe2, 0x54, 0xda, 0x96, 0xb7, 0x5f, 0x20, 0xaf,
	0x58, 0x44, 0x3e, 0x86, 0x46, 0xe9, 0x9e, 0xf0, 0x6c, 0x96, 0xdb, 0x55, 0x15, 0x60, 0x16, 0xd8,
	0x85, 0x84, 0xc8, 0x63, 0x00, 0x31, 0xe7, 0x7e, 0x22, 0x26, 0x3e, 0x8b, 0x6c, 0xc3, 0xa9, 0xb6,
	0x2d, 0x6f, 0x4f, 0xcc, 0xf9, 0x48, 0x4c, 0x2e, 0x23, 0xf2, 0x3f, 0x30, 0x12, 0x31, 0xb1, 0x6b,
	0x4e, 0xb5, 0xdd, 0xf0, 0xe4, 0xd3, 0xfd, 0xa7, 0x0a, 0x07, 0x02, 0xbf, 0x61, 0x31, 0xbd, 0xe2,
	0x59, 0x4e, 0x39, 0x2e, 0xc8, 0x11, 0xc8, 0x04, 0x1f, 0x59, 0x42, 0xcb, 0x16, 0xea, 0x62, 0xce,
	0xbf, 0x67, 0x09, 0x25, 0xc7, 0x60, 0x86, 0x31, 0xa3, 0x29, 0xfa, 0x39, 0xcf, 0x0a, 0xfe, 0x86,
	0x07, 0x05, 0x24, 0xf3, 0xc9, 0x23, 0xd8, 0x15, 0x18, 0xe0, 0x4c, 0xd8, 0x86, 0xca, 0x2c, 0x2d,
	0xd2, 0x87, 0x83, 0x50, 0x4e, 0xa8, 0x1a, 0x8b, 0x99, 0x40, 0xbb, 0xe6, 0x18, 0x6d, 0xb3, 0x67,
	0x77, 0xd7, 0xa2, 0x75, 0x75, 0x29, 0xbc, 0x86, 0x8a, 0x1f, 0x89, 0xc9, 0x90, 0x09, 0x24, 0x04,
	0x6a, 0x52, 0x37, 0x7b, 0x47, 0x0d, 0xa4, 0xde, 0xe4, 0x13, 0x38, 0x28, 0xd5, 0x08, 0xc2, 0x30,
	0x9b, 0xa5, 0x68, 0xef, 0x3a, 0xd5, 0xf6, 0xbe, 0x67, 0x15, 0xe8, 0x79, 0x01, 0x4a, 0xd1, 0xc6,
	0x3c, 0x4b, 0x56, 0x41, 0x75, 0x15, 0x64, 0x4a, 0x6c, 0x19, 0x72, 0x01, 0xff, 0x4f, 0xe9, 0xb5,
	0x3f, 0x66, 0x31, 0xf5, 0xe3, 0x2c, 0x0c, 0x90, 0x65, 0xa9, 0xbd, 0xe7, 0x54, 0xda, 0x66, 0xef,
	0x23, 0xbd, 0xc1, 0xd1, 0x68, 0x30, 0x1b, 0x0f, 0xcb, 0x00, 0x1f, 0xbd, 0xc3, 0x94, 0x5e, 0x4b,
	0xe9, 0x96, 0x90, 0x3b, 0x94, 0xe3, 0x4b, 0x44, 0x36, 0x1c, 0x05, 0x18, 0xd8, 0x15, 0x25, 0xb5,
	0x7a, 0x93, 0x2e, 0xd4, 0x4a, 0xd9, 0xaa, 0x6d, 0xb3, 0xd7, 0xdc, 0x1c, 0x5d, 0x5f, 0x81, 0xa7,
	0xe2, 0xdc, 0x9f, 0x00, 0xce, 0xa3, 0x48, 0x3a, 0x3c, 0xfa, 0x8b, 0xdc, 0xdd, 0x94, 0x2e, 0x54,
	0xc1, 0x7d, 0x4f, 0x3e, 0xc9, 0xa7, 0x50, 0x93, 0x2d, 0x97, 0xf5, 0xc8, 0xdd, 0x7a, 0x9e, 0xf2,
	0xcb, 0x4c, 0xc4, 0xb8, 0x3c, 0x06, 0xf9, 0x74, 0x5b, 0x00, 0x17, 0x14, 0xef, 0xad, 0xec, 0x7e,
	0x01, 0xe6, 0xca, 0x2f, 0xf2, 0x15, 0x51, 0x45, 0x49, 0x72, 0x2f, 0x91, 0xfb, 0x39, 0x58, 0xa3,
	0xd1, 0x39, 0x22, 0x67, 0xaf, 0x67, 0x48, 0x7d, 0x24, 0x4d, 0xd8, 0x63, 0x2f, 0x02, 0x0c, 0x86,
	0x34, 0x55, 0xe5, 0x2d, 0x6f, 0x65, 0xbb, 0xbf, 0x83, 0xa9, 0xf4, 0x94, 0xb6, 0x8f, 0xe4, 0x39,
	0x00, 0xae, 0x52, 0x55, 0xb0, 0xd9, 0x3b, 0xda, 0x14, 0x5f, 0xab, 0xec, 0x69, 0xc1, 0xe4, 0x19,
	0xec, 0xa0, 0xac, 0x52, 0x0a, 0xf1, 0xa4, 0xbb, 0xfa, 0xea, 0xba, 0xdf, 0xbd, 0x1c, 0x14, 0x5f,
	0xdd, 0x60, 0x36, 0x1e, 0x53, 0xee, 0x5f, 0x0d, 0xbc, 0x22, 0xd6, 0x7d, 0x09, 0x87, 0xef, 0xad,
	0x93, 0xd8, 0x50, 0x67, 0xdf, 0x8e, 0xc7, 0x82, 0x62, 0xd9, 0xec, 0xd2, 0x24, 0x2d, 0x00, 0x26,
	0xe7, 0xbc, 0x4c, 0x23, 0xfa, 0xab, 0xa2, 0xb1, 0x3c, 0x0d, 0x71, 0xdb, 0x40, 0x86, 0xd9, 0xf5,
	0x90, 0xce, 0x69, 0xac, 0x6d, 0x6c, 0xcb, 0x0d, 0xb8, 0x3f, 0xc2, 0x07, 0x77, 0x22, 0x45, 0x4e,
	0xbe, 0x04, 0x6b, 0xf3, 0xfa, 0x0a, 0x01, 0x1e, 0xbc, 0xbe, 0xc6, 0x58, 0x3f, 0xbd, 0x1f, 0xd6,
	0x2d, 0x68, 0xab, 0xfd, 0xef, 0x75, 0x3f, 0x5b, 0x37, 0xac, 0x9f, 0xc4, 0xb6, 0xd9, 0x5a, 0x00,
	0x2f, 0x68, 0x7c, 0xff, 0x55, 0x59, 0x60, 0xae, 0xfc, 0x22, 0xef, 0xfd, 0x5d, 0x03, 0xed, 0x97,
	0x49, 0x7e, 0x83, 0x7a, 0xa9, 0x08, 0x79, 0xa4, 0xb7, 0xb7, 0x16, 0xb4, 0xf9, 0x78, 0xdb, 0x66,
	0xbf, 0x4e, 0x72, 0x5c, 0xf8, 0x57, 0x03, 0xf7, 0xf9, 0xdb, 0x9b, 0x5b, 0xa3, 0xf2, 0xe7, 0xcd,
	0xad, 0xb1, 0x3b, 0x3d, 0x8d, 0x4e, 0xf1, 0xf4, 0xaf, 0x9b, 0x5b, 0xe3, 0x69, 0x67, 0xea, 0x9c,
	0x4d, 0xe9, 0xc2, 0x4f, 0x83, 0x84, 0xf6, 0x9d, 0x4e, 0xe4, 0x9c, 0x29, 0x41, 0x22, 0xc6, 0xfb,
	0x4e, 0x07, 0x9d, 0x33, 0xc4, 0xb8, 0x4f, 0x7e, 0x86, 0x7a, 0x39, 0xdc, 0x26, 0xf7, 0x5a, 0xc9,
	0xe6, 0x87, 0x5b, 0x71, 0x91, 0xbb, 0x2d, 0x49, 0x5b, 0x95, 0xb4, 0xd5, 0xa9, 0xa2, 0xb4, 0x36,
	0x28, 0x09, 0x87, 0xc3, 0xf7, 0x36, 0x4e, 0x5a, 0x7a, 0xad, 0xbb, 0x87, 0xd3, 0x3c, 0x7e, 0xd0,
	0xbf, 0xe4, 0x34, 0x14, 0x67, 0x54, 0x72, 0xea, 0x83, 0x91, 0x3f, 0x2a, 0x6b, 0xd2, 0xe5, 0x60,
	0x5b, 0x49, 0xb5, 0x01, 0x8f, 0x1f, 0xf4, 0x8b, 0xdc, 0x7d, 0x26, 0x49, 0x6b, 0x92, 0xb4, 0xc6,
	0x4e, 0x33, 0x45, 0xeb, 0x74, 0x58, 0x41, 0xeb, 0x30, 0xf9, 0x1d, 0xf4, 0x9d, 0x4e, 0x56, 0xda,
	0x99, 0xfa, 0x70, 0xfa, 0xe4, 0x15, 0xd4, 0xcb, 0x9d, 0x6f, 0x2a, 0xbb, 0x3e, 0x94, 0x4d, 0x65,
	0xb5, 0x03, 0x71, 0x8f, 0x24, 0xe1, 0x8e, 0xa6, 0xec, 0x5e, 0xa9, 0x6c, 0xbf, 0xb9, 0xfb, 0xee,
	0xe6, 0xd6, 0x78, 0xc7, 0xff, 0x0d, 0x00, 0x00, 0xff, 0xff, 0x46, 0xc0, 0x85, 0xd4, 0x98, 0x07,
	0x00, 0x00,
}
