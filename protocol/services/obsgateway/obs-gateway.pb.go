// Code generated by protoc-gen-go. DO NOT EDIT.
// source: obs-gateway/obs-gateway.proto

package obsgateway // import "golang.52tt.com/protocol/services/obsgateway"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TokenType int32

const (
	TokenType_TOKEN_TYPE_ALL TokenType = 0
	TokenType_TOKEN_TYPE_GET TokenType = 1
	TokenType_TOKEN_TYPE_PUT TokenType = 2
	TokenType_TOKEN_TYPE_DEL TokenType = 4
)

var TokenType_name = map[int32]string{
	0: "TOKEN_TYPE_ALL",
	1: "TOKEN_TYPE_GET",
	2: "TOKEN_TYPE_PUT",
	4: "TOKEN_TYPE_DEL",
}
var TokenType_value = map[string]int32{
	"TOKEN_TYPE_ALL": 0,
	"TOKEN_TYPE_GET": 1,
	"TOKEN_TYPE_PUT": 2,
	"TOKEN_TYPE_DEL": 4,
}

func (x TokenType) String() string {
	return proto.EnumName(TokenType_name, int32(x))
}
func (TokenType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{0}
}

type SyncType int32

const (
	SyncType_SYNC_TYPE_NIL SyncType = 0
	SyncType_SYNC_TYPE_PUT SyncType = 1
	SyncType_SYNC_TYPE_DEL SyncType = 2
)

var SyncType_name = map[int32]string{
	0: "SYNC_TYPE_NIL",
	1: "SYNC_TYPE_PUT",
	2: "SYNC_TYPE_DEL",
}
var SyncType_value = map[string]int32{
	"SYNC_TYPE_NIL": 0,
	"SYNC_TYPE_PUT": 1,
	"SYNC_TYPE_DEL": 2,
}

func (x SyncType) String() string {
	return proto.EnumName(SyncType_name, int32(x))
}
func (SyncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{1}
}

type BaseReq struct {
	Appid                string   `protobuf:"bytes,1,opt,name=appid,proto3" json:"appid,omitempty"`
	Scope                string   `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`
	RequestId            string   `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Operator             string   `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	Uid                  uint32   `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	ScopeId              uint32   `protobuf:"varint,7,opt,name=scope_id,json=scopeId,proto3" json:"scope_id,omitempty"`
	Unreview             bool     `protobuf:"varint,8,opt,name=unreview,proto3" json:"unreview,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseReq) Reset()         { *m = BaseReq{} }
func (m *BaseReq) String() string { return proto.CompactTextString(m) }
func (*BaseReq) ProtoMessage()    {}
func (*BaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{0}
}
func (m *BaseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseReq.Unmarshal(m, b)
}
func (m *BaseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseReq.Marshal(b, m, deterministic)
}
func (dst *BaseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseReq.Merge(dst, src)
}
func (m *BaseReq) XXX_Size() int {
	return xxx_messageInfo_BaseReq.Size(m)
}
func (m *BaseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseReq.DiscardUnknown(m)
}

var xxx_messageInfo_BaseReq proto.InternalMessageInfo

func (m *BaseReq) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *BaseReq) GetScope() string {
	if m != nil {
		return m.Scope
	}
	return ""
}

func (m *BaseReq) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *BaseReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *BaseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BaseReq) GetScopeId() uint32 {
	if m != nil {
		return m.ScopeId
	}
	return 0
}

func (m *BaseReq) GetUnreview() bool {
	if m != nil {
		return m.Unreview
	}
	return false
}

type BaseResp struct {
	Appid                string   `protobuf:"bytes,1,opt,name=appid,proto3" json:"appid,omitempty"`
	Scope                string   `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`
	IsPublic             bool     `protobuf:"varint,3,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResp) Reset()         { *m = BaseResp{} }
func (m *BaseResp) String() string { return proto.CompactTextString(m) }
func (*BaseResp) ProtoMessage()    {}
func (*BaseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{1}
}
func (m *BaseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResp.Unmarshal(m, b)
}
func (m *BaseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResp.Marshal(b, m, deterministic)
}
func (dst *BaseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResp.Merge(dst, src)
}
func (m *BaseResp) XXX_Size() int {
	return xxx_messageInfo_BaseResp.Size(m)
}
func (m *BaseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResp.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResp proto.InternalMessageInfo

func (m *BaseResp) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *BaseResp) GetScope() string {
	if m != nil {
		return m.Scope
	}
	return ""
}

func (m *BaseResp) GetIsPublic() bool {
	if m != nil {
		return m.IsPublic
	}
	return false
}

type GenerateTokenReq struct {
	Appid                string    `protobuf:"bytes,1,opt,name=appid,proto3" json:"appid,omitempty"`
	Scope                string    `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`
	Uid                  uint32    `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttl                  uint32    `protobuf:"varint,4,opt,name=ttl,proto3" json:"ttl,omitempty"`
	KeyPrefix            string    `protobuf:"bytes,5,opt,name=key_prefix,json=keyPrefix,proto3" json:"key_prefix,omitempty"`
	Transcoding          string    `protobuf:"bytes,6,opt,name=transcoding,proto3" json:"transcoding,omitempty"`
	TokenType            TokenType `protobuf:"varint,7,opt,name=token_type,json=tokenType,proto3,enum=obsgateway.TokenType" json:"token_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GenerateTokenReq) Reset()         { *m = GenerateTokenReq{} }
func (m *GenerateTokenReq) String() string { return proto.CompactTextString(m) }
func (*GenerateTokenReq) ProtoMessage()    {}
func (*GenerateTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{2}
}
func (m *GenerateTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenerateTokenReq.Unmarshal(m, b)
}
func (m *GenerateTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenerateTokenReq.Marshal(b, m, deterministic)
}
func (dst *GenerateTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenerateTokenReq.Merge(dst, src)
}
func (m *GenerateTokenReq) XXX_Size() int {
	return xxx_messageInfo_GenerateTokenReq.Size(m)
}
func (m *GenerateTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenerateTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenerateTokenReq proto.InternalMessageInfo

func (m *GenerateTokenReq) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *GenerateTokenReq) GetScope() string {
	if m != nil {
		return m.Scope
	}
	return ""
}

func (m *GenerateTokenReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GenerateTokenReq) GetTtl() uint32 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

func (m *GenerateTokenReq) GetKeyPrefix() string {
	if m != nil {
		return m.KeyPrefix
	}
	return ""
}

func (m *GenerateTokenReq) GetTranscoding() string {
	if m != nil {
		return m.Transcoding
	}
	return ""
}

func (m *GenerateTokenReq) GetTokenType() TokenType {
	if m != nil {
		return m.TokenType
	}
	return TokenType_TOKEN_TYPE_ALL
}

type GenerateTokenResp struct {
	Token                string   `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenerateTokenResp) Reset()         { *m = GenerateTokenResp{} }
func (m *GenerateTokenResp) String() string { return proto.CompactTextString(m) }
func (*GenerateTokenResp) ProtoMessage()    {}
func (*GenerateTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{3}
}
func (m *GenerateTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenerateTokenResp.Unmarshal(m, b)
}
func (m *GenerateTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenerateTokenResp.Marshal(b, m, deterministic)
}
func (dst *GenerateTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenerateTokenResp.Merge(dst, src)
}
func (m *GenerateTokenResp) XXX_Size() int {
	return xxx_messageInfo_GenerateTokenResp.Size(m)
}
func (m *GenerateTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GenerateTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_GenerateTokenResp proto.InternalMessageInfo

func (m *GenerateTokenResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type ValidateTokenReq struct {
	Token                string   `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ValidateTokenReq) Reset()         { *m = ValidateTokenReq{} }
func (m *ValidateTokenReq) String() string { return proto.CompactTextString(m) }
func (*ValidateTokenReq) ProtoMessage()    {}
func (*ValidateTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{4}
}
func (m *ValidateTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidateTokenReq.Unmarshal(m, b)
}
func (m *ValidateTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidateTokenReq.Marshal(b, m, deterministic)
}
func (dst *ValidateTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidateTokenReq.Merge(dst, src)
}
func (m *ValidateTokenReq) XXX_Size() int {
	return xxx_messageInfo_ValidateTokenReq.Size(m)
}
func (m *ValidateTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidateTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_ValidateTokenReq proto.InternalMessageInfo

func (m *ValidateTokenReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *ValidateTokenReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type ValidateTokenResp struct {
	Appid                string   `protobuf:"bytes,1,opt,name=appid,proto3" json:"appid,omitempty"`
	Scope                string   `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Transcoding          string   `protobuf:"bytes,4,opt,name=transcoding,proto3" json:"transcoding,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ValidateTokenResp) Reset()         { *m = ValidateTokenResp{} }
func (m *ValidateTokenResp) String() string { return proto.CompactTextString(m) }
func (*ValidateTokenResp) ProtoMessage()    {}
func (*ValidateTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{5}
}
func (m *ValidateTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidateTokenResp.Unmarshal(m, b)
}
func (m *ValidateTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidateTokenResp.Marshal(b, m, deterministic)
}
func (dst *ValidateTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidateTokenResp.Merge(dst, src)
}
func (m *ValidateTokenResp) XXX_Size() int {
	return xxx_messageInfo_ValidateTokenResp.Size(m)
}
func (m *ValidateTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidateTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_ValidateTokenResp proto.InternalMessageInfo

func (m *ValidateTokenResp) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *ValidateTokenResp) GetScope() string {
	if m != nil {
		return m.Scope
	}
	return ""
}

func (m *ValidateTokenResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ValidateTokenResp) GetTranscoding() string {
	if m != nil {
		return m.Transcoding
	}
	return ""
}

// 获取临时链接
type GetTemporaryURLReq struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Ttl                  uint32   `protobuf:"varint,2,opt,name=ttl,proto3" json:"ttl,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTemporaryURLReq) Reset()         { *m = GetTemporaryURLReq{} }
func (m *GetTemporaryURLReq) String() string { return proto.CompactTextString(m) }
func (*GetTemporaryURLReq) ProtoMessage()    {}
func (*GetTemporaryURLReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{6}
}
func (m *GetTemporaryURLReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTemporaryURLReq.Unmarshal(m, b)
}
func (m *GetTemporaryURLReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTemporaryURLReq.Marshal(b, m, deterministic)
}
func (dst *GetTemporaryURLReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTemporaryURLReq.Merge(dst, src)
}
func (m *GetTemporaryURLReq) XXX_Size() int {
	return xxx_messageInfo_GetTemporaryURLReq.Size(m)
}
func (m *GetTemporaryURLReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTemporaryURLReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTemporaryURLReq proto.InternalMessageInfo

func (m *GetTemporaryURLReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GetTemporaryURLReq) GetTtl() uint32 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

type GetTemporaryURLResp struct {
	TempUrl              string   `protobuf:"bytes,1,opt,name=temp_url,json=tempUrl,proto3" json:"temp_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTemporaryURLResp) Reset()         { *m = GetTemporaryURLResp{} }
func (m *GetTemporaryURLResp) String() string { return proto.CompactTextString(m) }
func (*GetTemporaryURLResp) ProtoMessage()    {}
func (*GetTemporaryURLResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{7}
}
func (m *GetTemporaryURLResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTemporaryURLResp.Unmarshal(m, b)
}
func (m *GetTemporaryURLResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTemporaryURLResp.Marshal(b, m, deterministic)
}
func (dst *GetTemporaryURLResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTemporaryURLResp.Merge(dst, src)
}
func (m *GetTemporaryURLResp) XXX_Size() int {
	return xxx_messageInfo_GetTemporaryURLResp.Size(m)
}
func (m *GetTemporaryURLResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTemporaryURLResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTemporaryURLResp proto.InternalMessageInfo

func (m *GetTemporaryURLResp) GetTempUrl() string {
	if m != nil {
		return m.TempUrl
	}
	return ""
}

type GetObjectReq struct {
	BaseReq              *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	RangeStart           int64    `protobuf:"varint,3,opt,name=range_start,json=rangeStart,proto3" json:"range_start,omitempty"`
	RangeEnd             int64    `protobuf:"varint,4,opt,name=range_end,json=rangeEnd,proto3" json:"range_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetObjectReq) Reset()         { *m = GetObjectReq{} }
func (m *GetObjectReq) String() string { return proto.CompactTextString(m) }
func (*GetObjectReq) ProtoMessage()    {}
func (*GetObjectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{8}
}
func (m *GetObjectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetObjectReq.Unmarshal(m, b)
}
func (m *GetObjectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetObjectReq.Marshal(b, m, deterministic)
}
func (dst *GetObjectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetObjectReq.Merge(dst, src)
}
func (m *GetObjectReq) XXX_Size() int {
	return xxx_messageInfo_GetObjectReq.Size(m)
}
func (m *GetObjectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetObjectReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetObjectReq proto.InternalMessageInfo

func (m *GetObjectReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetObjectReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *GetObjectReq) GetRangeStart() int64 {
	if m != nil {
		return m.RangeStart
	}
	return 0
}

func (m *GetObjectReq) GetRangeEnd() int64 {
	if m != nil {
		return m.RangeEnd
	}
	return 0
}

type GetObjectResp struct {
	BaseResp             *BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Body                 []byte    `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	Etag                 string    `protobuf:"bytes,3,opt,name=etag,proto3" json:"etag,omitempty"`
	LastModified         int64     `protobuf:"varint,4,opt,name=last_modified,json=lastModified,proto3" json:"last_modified,omitempty"`
	ContentLength        int64     `protobuf:"varint,5,opt,name=content_length,json=contentLength,proto3" json:"content_length,omitempty"`
	ContentType          string    `protobuf:"bytes,6,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	ContentEncoding      string    `protobuf:"bytes,7,opt,name=content_encoding,json=contentEncoding,proto3" json:"content_encoding,omitempty"`
	ContentLanguage      string    `protobuf:"bytes,8,opt,name=content_language,json=contentLanguage,proto3" json:"content_language,omitempty"`
	ContentDisposition   string    `protobuf:"bytes,9,opt,name=content_disposition,json=contentDisposition,proto3" json:"content_disposition,omitempty"`
	CacheControl         string    `protobuf:"bytes,10,opt,name=cache_control,json=cacheControl,proto3" json:"cache_control,omitempty"`
	Expires              int64     `protobuf:"varint,11,opt,name=expires,proto3" json:"expires,omitempty"`
	ContentRange         string    `protobuf:"bytes,12,opt,name=content_range,json=contentRange,proto3" json:"content_range,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetObjectResp) Reset()         { *m = GetObjectResp{} }
func (m *GetObjectResp) String() string { return proto.CompactTextString(m) }
func (*GetObjectResp) ProtoMessage()    {}
func (*GetObjectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{9}
}
func (m *GetObjectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetObjectResp.Unmarshal(m, b)
}
func (m *GetObjectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetObjectResp.Marshal(b, m, deterministic)
}
func (dst *GetObjectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetObjectResp.Merge(dst, src)
}
func (m *GetObjectResp) XXX_Size() int {
	return xxx_messageInfo_GetObjectResp.Size(m)
}
func (m *GetObjectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetObjectResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetObjectResp proto.InternalMessageInfo

func (m *GetObjectResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetObjectResp) GetBody() []byte {
	if m != nil {
		return m.Body
	}
	return nil
}

func (m *GetObjectResp) GetEtag() string {
	if m != nil {
		return m.Etag
	}
	return ""
}

func (m *GetObjectResp) GetLastModified() int64 {
	if m != nil {
		return m.LastModified
	}
	return 0
}

func (m *GetObjectResp) GetContentLength() int64 {
	if m != nil {
		return m.ContentLength
	}
	return 0
}

func (m *GetObjectResp) GetContentType() string {
	if m != nil {
		return m.ContentType
	}
	return ""
}

func (m *GetObjectResp) GetContentEncoding() string {
	if m != nil {
		return m.ContentEncoding
	}
	return ""
}

func (m *GetObjectResp) GetContentLanguage() string {
	if m != nil {
		return m.ContentLanguage
	}
	return ""
}

func (m *GetObjectResp) GetContentDisposition() string {
	if m != nil {
		return m.ContentDisposition
	}
	return ""
}

func (m *GetObjectResp) GetCacheControl() string {
	if m != nil {
		return m.CacheControl
	}
	return ""
}

func (m *GetObjectResp) GetExpires() int64 {
	if m != nil {
		return m.Expires
	}
	return 0
}

func (m *GetObjectResp) GetContentRange() string {
	if m != nil {
		return m.ContentRange
	}
	return ""
}

type PutObjectReq struct {
	BaseReq *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Key     string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Body    []byte   `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`
	// 以下可选
	ContentLength        int64    `protobuf:"varint,4,opt,name=content_length,json=contentLength,proto3" json:"content_length,omitempty"`
	ContentType          string   `protobuf:"bytes,5,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	ContentMd5           string   `protobuf:"bytes,6,opt,name=content_md5,json=contentMd5,proto3" json:"content_md5,omitempty"`
	ContentEncoding      string   `protobuf:"bytes,7,opt,name=content_encoding,json=contentEncoding,proto3" json:"content_encoding,omitempty"`
	ContentLanguage      string   `protobuf:"bytes,8,opt,name=content_language,json=contentLanguage,proto3" json:"content_language,omitempty"`
	ContentDisposition   string   `protobuf:"bytes,9,opt,name=content_disposition,json=contentDisposition,proto3" json:"content_disposition,omitempty"`
	CacheControl         string   `protobuf:"bytes,10,opt,name=cache_control,json=cacheControl,proto3" json:"cache_control,omitempty"`
	Expires              int64    `protobuf:"varint,11,opt,name=expires,proto3" json:"expires,omitempty"`
	NoReview             bool     `protobuf:"varint,12,opt,name=no_review,json=noReview,proto3" json:"no_review,omitempty"`
	Transcoding          string   `protobuf:"bytes,13,opt,name=transcoding,proto3" json:"transcoding,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PutObjectReq) Reset()         { *m = PutObjectReq{} }
func (m *PutObjectReq) String() string { return proto.CompactTextString(m) }
func (*PutObjectReq) ProtoMessage()    {}
func (*PutObjectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{10}
}
func (m *PutObjectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PutObjectReq.Unmarshal(m, b)
}
func (m *PutObjectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PutObjectReq.Marshal(b, m, deterministic)
}
func (dst *PutObjectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutObjectReq.Merge(dst, src)
}
func (m *PutObjectReq) XXX_Size() int {
	return xxx_messageInfo_PutObjectReq.Size(m)
}
func (m *PutObjectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PutObjectReq.DiscardUnknown(m)
}

var xxx_messageInfo_PutObjectReq proto.InternalMessageInfo

func (m *PutObjectReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PutObjectReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *PutObjectReq) GetBody() []byte {
	if m != nil {
		return m.Body
	}
	return nil
}

func (m *PutObjectReq) GetContentLength() int64 {
	if m != nil {
		return m.ContentLength
	}
	return 0
}

func (m *PutObjectReq) GetContentType() string {
	if m != nil {
		return m.ContentType
	}
	return ""
}

func (m *PutObjectReq) GetContentMd5() string {
	if m != nil {
		return m.ContentMd5
	}
	return ""
}

func (m *PutObjectReq) GetContentEncoding() string {
	if m != nil {
		return m.ContentEncoding
	}
	return ""
}

func (m *PutObjectReq) GetContentLanguage() string {
	if m != nil {
		return m.ContentLanguage
	}
	return ""
}

func (m *PutObjectReq) GetContentDisposition() string {
	if m != nil {
		return m.ContentDisposition
	}
	return ""
}

func (m *PutObjectReq) GetCacheControl() string {
	if m != nil {
		return m.CacheControl
	}
	return ""
}

func (m *PutObjectReq) GetExpires() int64 {
	if m != nil {
		return m.Expires
	}
	return 0
}

func (m *PutObjectReq) GetNoReview() bool {
	if m != nil {
		return m.NoReview
	}
	return false
}

func (m *PutObjectReq) GetTranscoding() string {
	if m != nil {
		return m.Transcoding
	}
	return ""
}

type PutObjectResp struct {
	BaseResp             *BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Key                  string    `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Etag                 string    `protobuf:"bytes,3,opt,name=etag,proto3" json:"etag,omitempty"`
	TranscodingTaskId    string    `protobuf:"bytes,4,opt,name=transcoding_task_id,json=transcodingTaskId,proto3" json:"transcoding_task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *PutObjectResp) Reset()         { *m = PutObjectResp{} }
func (m *PutObjectResp) String() string { return proto.CompactTextString(m) }
func (*PutObjectResp) ProtoMessage()    {}
func (*PutObjectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{11}
}
func (m *PutObjectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PutObjectResp.Unmarshal(m, b)
}
func (m *PutObjectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PutObjectResp.Marshal(b, m, deterministic)
}
func (dst *PutObjectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutObjectResp.Merge(dst, src)
}
func (m *PutObjectResp) XXX_Size() int {
	return xxx_messageInfo_PutObjectResp.Size(m)
}
func (m *PutObjectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PutObjectResp.DiscardUnknown(m)
}

var xxx_messageInfo_PutObjectResp proto.InternalMessageInfo

func (m *PutObjectResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PutObjectResp) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *PutObjectResp) GetEtag() string {
	if m != nil {
		return m.Etag
	}
	return ""
}

func (m *PutObjectResp) GetTranscodingTaskId() string {
	if m != nil {
		return m.TranscodingTaskId
	}
	return ""
}

type HeadObjectReq struct {
	BaseReq              *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HeadObjectReq) Reset()         { *m = HeadObjectReq{} }
func (m *HeadObjectReq) String() string { return proto.CompactTextString(m) }
func (*HeadObjectReq) ProtoMessage()    {}
func (*HeadObjectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{12}
}
func (m *HeadObjectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HeadObjectReq.Unmarshal(m, b)
}
func (m *HeadObjectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HeadObjectReq.Marshal(b, m, deterministic)
}
func (dst *HeadObjectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HeadObjectReq.Merge(dst, src)
}
func (m *HeadObjectReq) XXX_Size() int {
	return xxx_messageInfo_HeadObjectReq.Size(m)
}
func (m *HeadObjectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HeadObjectReq.DiscardUnknown(m)
}

var xxx_messageInfo_HeadObjectReq proto.InternalMessageInfo

func (m *HeadObjectReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *HeadObjectReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type HeadObjectResp struct {
	BaseResp             *BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *HeadObjectResp) Reset()         { *m = HeadObjectResp{} }
func (m *HeadObjectResp) String() string { return proto.CompactTextString(m) }
func (*HeadObjectResp) ProtoMessage()    {}
func (*HeadObjectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{13}
}
func (m *HeadObjectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HeadObjectResp.Unmarshal(m, b)
}
func (m *HeadObjectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HeadObjectResp.Marshal(b, m, deterministic)
}
func (dst *HeadObjectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HeadObjectResp.Merge(dst, src)
}
func (m *HeadObjectResp) XXX_Size() int {
	return xxx_messageInfo_HeadObjectResp.Size(m)
}
func (m *HeadObjectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HeadObjectResp.DiscardUnknown(m)
}

var xxx_messageInfo_HeadObjectResp proto.InternalMessageInfo

func (m *HeadObjectResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type CopyObjectReq struct {
	BaseReq              *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	SrcUrl               string   `protobuf:"bytes,3,opt,name=src_url,json=srcUrl,proto3" json:"src_url,omitempty"`
	UrlTokenTtl          uint32   `protobuf:"varint,4,opt,name=url_token_ttl,json=urlTokenTtl,proto3" json:"url_token_ttl,omitempty"`
	ContentType          string   `protobuf:"bytes,5,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CopyObjectReq) Reset()         { *m = CopyObjectReq{} }
func (m *CopyObjectReq) String() string { return proto.CompactTextString(m) }
func (*CopyObjectReq) ProtoMessage()    {}
func (*CopyObjectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{14}
}
func (m *CopyObjectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CopyObjectReq.Unmarshal(m, b)
}
func (m *CopyObjectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CopyObjectReq.Marshal(b, m, deterministic)
}
func (dst *CopyObjectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CopyObjectReq.Merge(dst, src)
}
func (m *CopyObjectReq) XXX_Size() int {
	return xxx_messageInfo_CopyObjectReq.Size(m)
}
func (m *CopyObjectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CopyObjectReq.DiscardUnknown(m)
}

var xxx_messageInfo_CopyObjectReq proto.InternalMessageInfo

func (m *CopyObjectReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CopyObjectReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CopyObjectReq) GetSrcUrl() string {
	if m != nil {
		return m.SrcUrl
	}
	return ""
}

func (m *CopyObjectReq) GetUrlTokenTtl() uint32 {
	if m != nil {
		return m.UrlTokenTtl
	}
	return 0
}

func (m *CopyObjectReq) GetContentType() string {
	if m != nil {
		return m.ContentType
	}
	return ""
}

type CopyObjectResp struct {
	BaseResp             *BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Key                  string    `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Etag                 string    `protobuf:"bytes,3,opt,name=etag,proto3" json:"etag,omitempty"`
	Url                  string    `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *CopyObjectResp) Reset()         { *m = CopyObjectResp{} }
func (m *CopyObjectResp) String() string { return proto.CompactTextString(m) }
func (*CopyObjectResp) ProtoMessage()    {}
func (*CopyObjectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{15}
}
func (m *CopyObjectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CopyObjectResp.Unmarshal(m, b)
}
func (m *CopyObjectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CopyObjectResp.Marshal(b, m, deterministic)
}
func (dst *CopyObjectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CopyObjectResp.Merge(dst, src)
}
func (m *CopyObjectResp) XXX_Size() int {
	return xxx_messageInfo_CopyObjectResp.Size(m)
}
func (m *CopyObjectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CopyObjectResp.DiscardUnknown(m)
}

var xxx_messageInfo_CopyObjectResp proto.InternalMessageInfo

func (m *CopyObjectResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CopyObjectResp) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CopyObjectResp) GetEtag() string {
	if m != nil {
		return m.Etag
	}
	return ""
}

func (m *CopyObjectResp) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type DeleteObjectReq struct {
	BaseReq              *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteObjectReq) Reset()         { *m = DeleteObjectReq{} }
func (m *DeleteObjectReq) String() string { return proto.CompactTextString(m) }
func (*DeleteObjectReq) ProtoMessage()    {}
func (*DeleteObjectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{16}
}
func (m *DeleteObjectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteObjectReq.Unmarshal(m, b)
}
func (m *DeleteObjectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteObjectReq.Marshal(b, m, deterministic)
}
func (dst *DeleteObjectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteObjectReq.Merge(dst, src)
}
func (m *DeleteObjectReq) XXX_Size() int {
	return xxx_messageInfo_DeleteObjectReq.Size(m)
}
func (m *DeleteObjectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteObjectReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteObjectReq proto.InternalMessageInfo

func (m *DeleteObjectReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DeleteObjectReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type DeleteObjectResp struct {
	BaseResp             *BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *DeleteObjectResp) Reset()         { *m = DeleteObjectResp{} }
func (m *DeleteObjectResp) String() string { return proto.CompactTextString(m) }
func (*DeleteObjectResp) ProtoMessage()    {}
func (*DeleteObjectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{17}
}
func (m *DeleteObjectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteObjectResp.Unmarshal(m, b)
}
func (m *DeleteObjectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteObjectResp.Marshal(b, m, deterministic)
}
func (dst *DeleteObjectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteObjectResp.Merge(dst, src)
}
func (m *DeleteObjectResp) XXX_Size() int {
	return xxx_messageInfo_DeleteObjectResp.Size(m)
}
func (m *DeleteObjectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteObjectResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteObjectResp proto.InternalMessageInfo

func (m *DeleteObjectResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ListObjectsReq struct {
	BaseReq              *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Prefix               string   `protobuf:"bytes,2,opt,name=prefix,proto3" json:"prefix,omitempty"`
	Marker               string   `protobuf:"bytes,3,opt,name=marker,proto3" json:"marker,omitempty"`
	MaxKeys              int32    `protobuf:"varint,4,opt,name=max_keys,json=maxKeys,proto3" json:"max_keys,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListObjectsReq) Reset()         { *m = ListObjectsReq{} }
func (m *ListObjectsReq) String() string { return proto.CompactTextString(m) }
func (*ListObjectsReq) ProtoMessage()    {}
func (*ListObjectsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{18}
}
func (m *ListObjectsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListObjectsReq.Unmarshal(m, b)
}
func (m *ListObjectsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListObjectsReq.Marshal(b, m, deterministic)
}
func (dst *ListObjectsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListObjectsReq.Merge(dst, src)
}
func (m *ListObjectsReq) XXX_Size() int {
	return xxx_messageInfo_ListObjectsReq.Size(m)
}
func (m *ListObjectsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListObjectsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListObjectsReq proto.InternalMessageInfo

func (m *ListObjectsReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListObjectsReq) GetPrefix() string {
	if m != nil {
		return m.Prefix
	}
	return ""
}

func (m *ListObjectsReq) GetMarker() string {
	if m != nil {
		return m.Marker
	}
	return ""
}

func (m *ListObjectsReq) GetMaxKeys() int32 {
	if m != nil {
		return m.MaxKeys
	}
	return 0
}

type ListObjectsResp struct {
	BaseResp             *BaseResp                  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsTruncated          bool                       `protobuf:"varint,2,opt,name=is_truncated,json=isTruncated,proto3" json:"is_truncated,omitempty"`
	NextMarker           string                     `protobuf:"bytes,3,opt,name=next_marker,json=nextMarker,proto3" json:"next_marker,omitempty"`
	ContentList          []*ListObjectsResp_Content `protobuf:"bytes,4,rep,name=content_list,json=contentList,proto3" json:"content_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *ListObjectsResp) Reset()         { *m = ListObjectsResp{} }
func (m *ListObjectsResp) String() string { return proto.CompactTextString(m) }
func (*ListObjectsResp) ProtoMessage()    {}
func (*ListObjectsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{19}
}
func (m *ListObjectsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListObjectsResp.Unmarshal(m, b)
}
func (m *ListObjectsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListObjectsResp.Marshal(b, m, deterministic)
}
func (dst *ListObjectsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListObjectsResp.Merge(dst, src)
}
func (m *ListObjectsResp) XXX_Size() int {
	return xxx_messageInfo_ListObjectsResp.Size(m)
}
func (m *ListObjectsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListObjectsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListObjectsResp proto.InternalMessageInfo

func (m *ListObjectsResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListObjectsResp) GetIsTruncated() bool {
	if m != nil {
		return m.IsTruncated
	}
	return false
}

func (m *ListObjectsResp) GetNextMarker() string {
	if m != nil {
		return m.NextMarker
	}
	return ""
}

func (m *ListObjectsResp) GetContentList() []*ListObjectsResp_Content {
	if m != nil {
		return m.ContentList
	}
	return nil
}

type ListObjectsResp_Content struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Size                 int64    `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	LastModified         int64    `protobuf:"varint,3,opt,name=last_modified,json=lastModified,proto3" json:"last_modified,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListObjectsResp_Content) Reset()         { *m = ListObjectsResp_Content{} }
func (m *ListObjectsResp_Content) String() string { return proto.CompactTextString(m) }
func (*ListObjectsResp_Content) ProtoMessage()    {}
func (*ListObjectsResp_Content) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{19, 0}
}
func (m *ListObjectsResp_Content) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListObjectsResp_Content.Unmarshal(m, b)
}
func (m *ListObjectsResp_Content) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListObjectsResp_Content.Marshal(b, m, deterministic)
}
func (dst *ListObjectsResp_Content) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListObjectsResp_Content.Merge(dst, src)
}
func (m *ListObjectsResp_Content) XXX_Size() int {
	return xxx_messageInfo_ListObjectsResp_Content.Size(m)
}
func (m *ListObjectsResp_Content) XXX_DiscardUnknown() {
	xxx_messageInfo_ListObjectsResp_Content.DiscardUnknown(m)
}

var xxx_messageInfo_ListObjectsResp_Content proto.InternalMessageInfo

func (m *ListObjectsResp_Content) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ListObjectsResp_Content) GetSize() int64 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *ListObjectsResp_Content) GetLastModified() int64 {
	if m != nil {
		return m.LastModified
	}
	return 0
}

type CreateMultipartUploadReq struct {
	BaseReq              *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	ContentType          string   `protobuf:"bytes,3,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateMultipartUploadReq) Reset()         { *m = CreateMultipartUploadReq{} }
func (m *CreateMultipartUploadReq) String() string { return proto.CompactTextString(m) }
func (*CreateMultipartUploadReq) ProtoMessage()    {}
func (*CreateMultipartUploadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{20}
}
func (m *CreateMultipartUploadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateMultipartUploadReq.Unmarshal(m, b)
}
func (m *CreateMultipartUploadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateMultipartUploadReq.Marshal(b, m, deterministic)
}
func (dst *CreateMultipartUploadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateMultipartUploadReq.Merge(dst, src)
}
func (m *CreateMultipartUploadReq) XXX_Size() int {
	return xxx_messageInfo_CreateMultipartUploadReq.Size(m)
}
func (m *CreateMultipartUploadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateMultipartUploadReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateMultipartUploadReq proto.InternalMessageInfo

func (m *CreateMultipartUploadReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CreateMultipartUploadReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CreateMultipartUploadReq) GetContentType() string {
	if m != nil {
		return m.ContentType
	}
	return ""
}

type CreateMultipartUploadResp struct {
	BaseResp             *BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Key                  string    `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	UploadId             string    `protobuf:"bytes,3,opt,name=upload_id,json=uploadId,proto3" json:"upload_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *CreateMultipartUploadResp) Reset()         { *m = CreateMultipartUploadResp{} }
func (m *CreateMultipartUploadResp) String() string { return proto.CompactTextString(m) }
func (*CreateMultipartUploadResp) ProtoMessage()    {}
func (*CreateMultipartUploadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{21}
}
func (m *CreateMultipartUploadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateMultipartUploadResp.Unmarshal(m, b)
}
func (m *CreateMultipartUploadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateMultipartUploadResp.Marshal(b, m, deterministic)
}
func (dst *CreateMultipartUploadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateMultipartUploadResp.Merge(dst, src)
}
func (m *CreateMultipartUploadResp) XXX_Size() int {
	return xxx_messageInfo_CreateMultipartUploadResp.Size(m)
}
func (m *CreateMultipartUploadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateMultipartUploadResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateMultipartUploadResp proto.InternalMessageInfo

func (m *CreateMultipartUploadResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CreateMultipartUploadResp) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CreateMultipartUploadResp) GetUploadId() string {
	if m != nil {
		return m.UploadId
	}
	return ""
}

type UploadPartReq struct {
	BaseReq              *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	UploadId             string   `protobuf:"bytes,3,opt,name=upload_id,json=uploadId,proto3" json:"upload_id,omitempty"`
	PartNumber           int32    `protobuf:"varint,4,opt,name=part_number,json=partNumber,proto3" json:"part_number,omitempty"`
	Body                 []byte   `protobuf:"bytes,5,opt,name=body,proto3" json:"body,omitempty"`
	ContentLength        int64    `protobuf:"varint,6,opt,name=content_length,json=contentLength,proto3" json:"content_length,omitempty"`
	ContentMd5           string   `protobuf:"bytes,7,opt,name=content_md5,json=contentMd5,proto3" json:"content_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UploadPartReq) Reset()         { *m = UploadPartReq{} }
func (m *UploadPartReq) String() string { return proto.CompactTextString(m) }
func (*UploadPartReq) ProtoMessage()    {}
func (*UploadPartReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{22}
}
func (m *UploadPartReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadPartReq.Unmarshal(m, b)
}
func (m *UploadPartReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadPartReq.Marshal(b, m, deterministic)
}
func (dst *UploadPartReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadPartReq.Merge(dst, src)
}
func (m *UploadPartReq) XXX_Size() int {
	return xxx_messageInfo_UploadPartReq.Size(m)
}
func (m *UploadPartReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadPartReq.DiscardUnknown(m)
}

var xxx_messageInfo_UploadPartReq proto.InternalMessageInfo

func (m *UploadPartReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UploadPartReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *UploadPartReq) GetUploadId() string {
	if m != nil {
		return m.UploadId
	}
	return ""
}

func (m *UploadPartReq) GetPartNumber() int32 {
	if m != nil {
		return m.PartNumber
	}
	return 0
}

func (m *UploadPartReq) GetBody() []byte {
	if m != nil {
		return m.Body
	}
	return nil
}

func (m *UploadPartReq) GetContentLength() int64 {
	if m != nil {
		return m.ContentLength
	}
	return 0
}

func (m *UploadPartReq) GetContentMd5() string {
	if m != nil {
		return m.ContentMd5
	}
	return ""
}

type UploadPartResp struct {
	BaseResp             *BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Etag                 string    `protobuf:"bytes,2,opt,name=etag,proto3" json:"etag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *UploadPartResp) Reset()         { *m = UploadPartResp{} }
func (m *UploadPartResp) String() string { return proto.CompactTextString(m) }
func (*UploadPartResp) ProtoMessage()    {}
func (*UploadPartResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{23}
}
func (m *UploadPartResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadPartResp.Unmarshal(m, b)
}
func (m *UploadPartResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadPartResp.Marshal(b, m, deterministic)
}
func (dst *UploadPartResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadPartResp.Merge(dst, src)
}
func (m *UploadPartResp) XXX_Size() int {
	return xxx_messageInfo_UploadPartResp.Size(m)
}
func (m *UploadPartResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadPartResp.DiscardUnknown(m)
}

var xxx_messageInfo_UploadPartResp proto.InternalMessageInfo

func (m *UploadPartResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UploadPartResp) GetEtag() string {
	if m != nil {
		return m.Etag
	}
	return ""
}

type CompleteMultipartUploadReq struct {
	BaseReq              *BaseReq                           `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Key                  string                             `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	UploadId             string                             `protobuf:"bytes,3,opt,name=upload_id,json=uploadId,proto3" json:"upload_id,omitempty"`
	PartList             []*CompleteMultipartUploadReq_Part `protobuf:"bytes,4,rep,name=part_list,json=partList,proto3" json:"part_list,omitempty"`
	MimeType             string                             `protobuf:"bytes,5,opt,name=mimeType,proto3" json:"mimeType,omitempty"`
	Transcoding          string                             `protobuf:"bytes,6,opt,name=transcoding,proto3" json:"transcoding,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *CompleteMultipartUploadReq) Reset()         { *m = CompleteMultipartUploadReq{} }
func (m *CompleteMultipartUploadReq) String() string { return proto.CompactTextString(m) }
func (*CompleteMultipartUploadReq) ProtoMessage()    {}
func (*CompleteMultipartUploadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{24}
}
func (m *CompleteMultipartUploadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompleteMultipartUploadReq.Unmarshal(m, b)
}
func (m *CompleteMultipartUploadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompleteMultipartUploadReq.Marshal(b, m, deterministic)
}
func (dst *CompleteMultipartUploadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompleteMultipartUploadReq.Merge(dst, src)
}
func (m *CompleteMultipartUploadReq) XXX_Size() int {
	return xxx_messageInfo_CompleteMultipartUploadReq.Size(m)
}
func (m *CompleteMultipartUploadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CompleteMultipartUploadReq.DiscardUnknown(m)
}

var xxx_messageInfo_CompleteMultipartUploadReq proto.InternalMessageInfo

func (m *CompleteMultipartUploadReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CompleteMultipartUploadReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CompleteMultipartUploadReq) GetUploadId() string {
	if m != nil {
		return m.UploadId
	}
	return ""
}

func (m *CompleteMultipartUploadReq) GetPartList() []*CompleteMultipartUploadReq_Part {
	if m != nil {
		return m.PartList
	}
	return nil
}

func (m *CompleteMultipartUploadReq) GetMimeType() string {
	if m != nil {
		return m.MimeType
	}
	return ""
}

func (m *CompleteMultipartUploadReq) GetTranscoding() string {
	if m != nil {
		return m.Transcoding
	}
	return ""
}

type CompleteMultipartUploadReq_Part struct {
	Etag                 string   `protobuf:"bytes,1,opt,name=etag,proto3" json:"etag,omitempty"`
	PartNumber           int32    `protobuf:"varint,2,opt,name=part_number,json=partNumber,proto3" json:"part_number,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CompleteMultipartUploadReq_Part) Reset()         { *m = CompleteMultipartUploadReq_Part{} }
func (m *CompleteMultipartUploadReq_Part) String() string { return proto.CompactTextString(m) }
func (*CompleteMultipartUploadReq_Part) ProtoMessage()    {}
func (*CompleteMultipartUploadReq_Part) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{24, 0}
}
func (m *CompleteMultipartUploadReq_Part) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompleteMultipartUploadReq_Part.Unmarshal(m, b)
}
func (m *CompleteMultipartUploadReq_Part) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompleteMultipartUploadReq_Part.Marshal(b, m, deterministic)
}
func (dst *CompleteMultipartUploadReq_Part) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompleteMultipartUploadReq_Part.Merge(dst, src)
}
func (m *CompleteMultipartUploadReq_Part) XXX_Size() int {
	return xxx_messageInfo_CompleteMultipartUploadReq_Part.Size(m)
}
func (m *CompleteMultipartUploadReq_Part) XXX_DiscardUnknown() {
	xxx_messageInfo_CompleteMultipartUploadReq_Part.DiscardUnknown(m)
}

var xxx_messageInfo_CompleteMultipartUploadReq_Part proto.InternalMessageInfo

func (m *CompleteMultipartUploadReq_Part) GetEtag() string {
	if m != nil {
		return m.Etag
	}
	return ""
}

func (m *CompleteMultipartUploadReq_Part) GetPartNumber() int32 {
	if m != nil {
		return m.PartNumber
	}
	return 0
}

type CompleteMultipartUploadResp struct {
	BaseResp             *BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Key                  string    `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Etag                 string    `protobuf:"bytes,3,opt,name=etag,proto3" json:"etag,omitempty"`
	TranscodingTaskId    string    `protobuf:"bytes,4,opt,name=transcoding_task_id,json=transcodingTaskId,proto3" json:"transcoding_task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *CompleteMultipartUploadResp) Reset()         { *m = CompleteMultipartUploadResp{} }
func (m *CompleteMultipartUploadResp) String() string { return proto.CompactTextString(m) }
func (*CompleteMultipartUploadResp) ProtoMessage()    {}
func (*CompleteMultipartUploadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{25}
}
func (m *CompleteMultipartUploadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompleteMultipartUploadResp.Unmarshal(m, b)
}
func (m *CompleteMultipartUploadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompleteMultipartUploadResp.Marshal(b, m, deterministic)
}
func (dst *CompleteMultipartUploadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompleteMultipartUploadResp.Merge(dst, src)
}
func (m *CompleteMultipartUploadResp) XXX_Size() int {
	return xxx_messageInfo_CompleteMultipartUploadResp.Size(m)
}
func (m *CompleteMultipartUploadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CompleteMultipartUploadResp.DiscardUnknown(m)
}

var xxx_messageInfo_CompleteMultipartUploadResp proto.InternalMessageInfo

func (m *CompleteMultipartUploadResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CompleteMultipartUploadResp) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CompleteMultipartUploadResp) GetEtag() string {
	if m != nil {
		return m.Etag
	}
	return ""
}

func (m *CompleteMultipartUploadResp) GetTranscodingTaskId() string {
	if m != nil {
		return m.TranscodingTaskId
	}
	return ""
}

type AbortMultipartUploadReq struct {
	BaseReq              *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	UploadId             string   `protobuf:"bytes,3,opt,name=upload_id,json=uploadId,proto3" json:"upload_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AbortMultipartUploadReq) Reset()         { *m = AbortMultipartUploadReq{} }
func (m *AbortMultipartUploadReq) String() string { return proto.CompactTextString(m) }
func (*AbortMultipartUploadReq) ProtoMessage()    {}
func (*AbortMultipartUploadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{26}
}
func (m *AbortMultipartUploadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AbortMultipartUploadReq.Unmarshal(m, b)
}
func (m *AbortMultipartUploadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AbortMultipartUploadReq.Marshal(b, m, deterministic)
}
func (dst *AbortMultipartUploadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AbortMultipartUploadReq.Merge(dst, src)
}
func (m *AbortMultipartUploadReq) XXX_Size() int {
	return xxx_messageInfo_AbortMultipartUploadReq.Size(m)
}
func (m *AbortMultipartUploadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AbortMultipartUploadReq.DiscardUnknown(m)
}

var xxx_messageInfo_AbortMultipartUploadReq proto.InternalMessageInfo

func (m *AbortMultipartUploadReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AbortMultipartUploadReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *AbortMultipartUploadReq) GetUploadId() string {
	if m != nil {
		return m.UploadId
	}
	return ""
}

type AbortMultipartUploadResp struct {
	BaseResp             *BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *AbortMultipartUploadResp) Reset()         { *m = AbortMultipartUploadResp{} }
func (m *AbortMultipartUploadResp) String() string { return proto.CompactTextString(m) }
func (*AbortMultipartUploadResp) ProtoMessage()    {}
func (*AbortMultipartUploadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{27}
}
func (m *AbortMultipartUploadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AbortMultipartUploadResp.Unmarshal(m, b)
}
func (m *AbortMultipartUploadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AbortMultipartUploadResp.Marshal(b, m, deterministic)
}
func (dst *AbortMultipartUploadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AbortMultipartUploadResp.Merge(dst, src)
}
func (m *AbortMultipartUploadResp) XXX_Size() int {
	return xxx_messageInfo_AbortMultipartUploadResp.Size(m)
}
func (m *AbortMultipartUploadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AbortMultipartUploadResp.DiscardUnknown(m)
}

var xxx_messageInfo_AbortMultipartUploadResp proto.InternalMessageInfo

func (m *AbortMultipartUploadResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ListPartsReq struct {
	BaseReq              *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	UploadId             string   `protobuf:"bytes,3,opt,name=upload_id,json=uploadId,proto3" json:"upload_id,omitempty"`
	MaxParts             int32    `protobuf:"varint,4,opt,name=max_parts,json=maxParts,proto3" json:"max_parts,omitempty"`
	PartNumberMarker     int32    `protobuf:"varint,5,opt,name=part_number_marker,json=partNumberMarker,proto3" json:"part_number_marker,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListPartsReq) Reset()         { *m = ListPartsReq{} }
func (m *ListPartsReq) String() string { return proto.CompactTextString(m) }
func (*ListPartsReq) ProtoMessage()    {}
func (*ListPartsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{28}
}
func (m *ListPartsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListPartsReq.Unmarshal(m, b)
}
func (m *ListPartsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListPartsReq.Marshal(b, m, deterministic)
}
func (dst *ListPartsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListPartsReq.Merge(dst, src)
}
func (m *ListPartsReq) XXX_Size() int {
	return xxx_messageInfo_ListPartsReq.Size(m)
}
func (m *ListPartsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListPartsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListPartsReq proto.InternalMessageInfo

func (m *ListPartsReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListPartsReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ListPartsReq) GetUploadId() string {
	if m != nil {
		return m.UploadId
	}
	return ""
}

func (m *ListPartsReq) GetMaxParts() int32 {
	if m != nil {
		return m.MaxParts
	}
	return 0
}

func (m *ListPartsReq) GetPartNumberMarker() int32 {
	if m != nil {
		return m.PartNumberMarker
	}
	return 0
}

type ListPartsResp struct {
	BaseResp             *BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	NextPartNumberMarker int32                 `protobuf:"varint,2,opt,name=next_part_number_marker,json=nextPartNumberMarker,proto3" json:"next_part_number_marker,omitempty"`
	MaxParts             int32                 `protobuf:"varint,3,opt,name=max_parts,json=maxParts,proto3" json:"max_parts,omitempty"`
	IsTruncated          bool                  `protobuf:"varint,4,opt,name=is_truncated,json=isTruncated,proto3" json:"is_truncated,omitempty"`
	PartList             []*ListPartsResp_Part `protobuf:"bytes,5,rep,name=part_list,json=partList,proto3" json:"part_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ListPartsResp) Reset()         { *m = ListPartsResp{} }
func (m *ListPartsResp) String() string { return proto.CompactTextString(m) }
func (*ListPartsResp) ProtoMessage()    {}
func (*ListPartsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{29}
}
func (m *ListPartsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListPartsResp.Unmarshal(m, b)
}
func (m *ListPartsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListPartsResp.Marshal(b, m, deterministic)
}
func (dst *ListPartsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListPartsResp.Merge(dst, src)
}
func (m *ListPartsResp) XXX_Size() int {
	return xxx_messageInfo_ListPartsResp.Size(m)
}
func (m *ListPartsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListPartsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListPartsResp proto.InternalMessageInfo

func (m *ListPartsResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListPartsResp) GetNextPartNumberMarker() int32 {
	if m != nil {
		return m.NextPartNumberMarker
	}
	return 0
}

func (m *ListPartsResp) GetMaxParts() int32 {
	if m != nil {
		return m.MaxParts
	}
	return 0
}

func (m *ListPartsResp) GetIsTruncated() bool {
	if m != nil {
		return m.IsTruncated
	}
	return false
}

func (m *ListPartsResp) GetPartList() []*ListPartsResp_Part {
	if m != nil {
		return m.PartList
	}
	return nil
}

type ListPartsResp_Part struct {
	Etag                 string   `protobuf:"bytes,1,opt,name=etag,proto3" json:"etag,omitempty"`
	PartNumber           int32    `protobuf:"varint,2,opt,name=part_number,json=partNumber,proto3" json:"part_number,omitempty"`
	LastModified         int64    `protobuf:"varint,3,opt,name=last_modified,json=lastModified,proto3" json:"last_modified,omitempty"`
	Size                 uint64   `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListPartsResp_Part) Reset()         { *m = ListPartsResp_Part{} }
func (m *ListPartsResp_Part) String() string { return proto.CompactTextString(m) }
func (*ListPartsResp_Part) ProtoMessage()    {}
func (*ListPartsResp_Part) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{29, 0}
}
func (m *ListPartsResp_Part) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListPartsResp_Part.Unmarshal(m, b)
}
func (m *ListPartsResp_Part) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListPartsResp_Part.Marshal(b, m, deterministic)
}
func (dst *ListPartsResp_Part) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListPartsResp_Part.Merge(dst, src)
}
func (m *ListPartsResp_Part) XXX_Size() int {
	return xxx_messageInfo_ListPartsResp_Part.Size(m)
}
func (m *ListPartsResp_Part) XXX_DiscardUnknown() {
	xxx_messageInfo_ListPartsResp_Part.DiscardUnknown(m)
}

var xxx_messageInfo_ListPartsResp_Part proto.InternalMessageInfo

func (m *ListPartsResp_Part) GetEtag() string {
	if m != nil {
		return m.Etag
	}
	return ""
}

func (m *ListPartsResp_Part) GetPartNumber() int32 {
	if m != nil {
		return m.PartNumber
	}
	return 0
}

func (m *ListPartsResp_Part) GetLastModified() int64 {
	if m != nil {
		return m.LastModified
	}
	return 0
}

func (m *ListPartsResp_Part) GetSize() uint64 {
	if m != nil {
		return m.Size
	}
	return 0
}

type ListMultipartUploadsReq struct {
	BaseReq              *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListMultipartUploadsReq) Reset()         { *m = ListMultipartUploadsReq{} }
func (m *ListMultipartUploadsReq) String() string { return proto.CompactTextString(m) }
func (*ListMultipartUploadsReq) ProtoMessage()    {}
func (*ListMultipartUploadsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{30}
}
func (m *ListMultipartUploadsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMultipartUploadsReq.Unmarshal(m, b)
}
func (m *ListMultipartUploadsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMultipartUploadsReq.Marshal(b, m, deterministic)
}
func (dst *ListMultipartUploadsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMultipartUploadsReq.Merge(dst, src)
}
func (m *ListMultipartUploadsReq) XXX_Size() int {
	return xxx_messageInfo_ListMultipartUploadsReq.Size(m)
}
func (m *ListMultipartUploadsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMultipartUploadsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListMultipartUploadsReq proto.InternalMessageInfo

func (m *ListMultipartUploadsReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type ListMultipartUploadsResp struct {
	BaseResp             *BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ListMultipartUploadsResp) Reset()         { *m = ListMultipartUploadsResp{} }
func (m *ListMultipartUploadsResp) String() string { return proto.CompactTextString(m) }
func (*ListMultipartUploadsResp) ProtoMessage()    {}
func (*ListMultipartUploadsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{31}
}
func (m *ListMultipartUploadsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMultipartUploadsResp.Unmarshal(m, b)
}
func (m *ListMultipartUploadsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMultipartUploadsResp.Marshal(b, m, deterministic)
}
func (dst *ListMultipartUploadsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMultipartUploadsResp.Merge(dst, src)
}
func (m *ListMultipartUploadsResp) XXX_Size() int {
	return xxx_messageInfo_ListMultipartUploadsResp.Size(m)
}
func (m *ListMultipartUploadsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMultipartUploadsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListMultipartUploadsResp proto.InternalMessageInfo

func (m *ListMultipartUploadsResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type HideObjectReq struct {
	BaseReq              *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HideObjectReq) Reset()         { *m = HideObjectReq{} }
func (m *HideObjectReq) String() string { return proto.CompactTextString(m) }
func (*HideObjectReq) ProtoMessage()    {}
func (*HideObjectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{32}
}
func (m *HideObjectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HideObjectReq.Unmarshal(m, b)
}
func (m *HideObjectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HideObjectReq.Marshal(b, m, deterministic)
}
func (dst *HideObjectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HideObjectReq.Merge(dst, src)
}
func (m *HideObjectReq) XXX_Size() int {
	return xxx_messageInfo_HideObjectReq.Size(m)
}
func (m *HideObjectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HideObjectReq.DiscardUnknown(m)
}

var xxx_messageInfo_HideObjectReq proto.InternalMessageInfo

func (m *HideObjectReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *HideObjectReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type HideObjectResp struct {
	BaseResp             *BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *HideObjectResp) Reset()         { *m = HideObjectResp{} }
func (m *HideObjectResp) String() string { return proto.CompactTextString(m) }
func (*HideObjectResp) ProtoMessage()    {}
func (*HideObjectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{33}
}
func (m *HideObjectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HideObjectResp.Unmarshal(m, b)
}
func (m *HideObjectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HideObjectResp.Marshal(b, m, deterministic)
}
func (dst *HideObjectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HideObjectResp.Merge(dst, src)
}
func (m *HideObjectResp) XXX_Size() int {
	return xxx_messageInfo_HideObjectResp.Size(m)
}
func (m *HideObjectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HideObjectResp.DiscardUnknown(m)
}

var xxx_messageInfo_HideObjectResp proto.InternalMessageInfo

func (m *HideObjectResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ShowObjectReq struct {
	BaseReq              *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	OnlyDelete           bool     `protobuf:"varint,3,opt,name=only_delete,json=onlyDelete,proto3" json:"only_delete,omitempty"`
	OnlyShow             bool     `protobuf:"varint,4,opt,name=only_show,json=onlyShow,proto3" json:"only_show,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowObjectReq) Reset()         { *m = ShowObjectReq{} }
func (m *ShowObjectReq) String() string { return proto.CompactTextString(m) }
func (*ShowObjectReq) ProtoMessage()    {}
func (*ShowObjectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{34}
}
func (m *ShowObjectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowObjectReq.Unmarshal(m, b)
}
func (m *ShowObjectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowObjectReq.Marshal(b, m, deterministic)
}
func (dst *ShowObjectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowObjectReq.Merge(dst, src)
}
func (m *ShowObjectReq) XXX_Size() int {
	return xxx_messageInfo_ShowObjectReq.Size(m)
}
func (m *ShowObjectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowObjectReq.DiscardUnknown(m)
}

var xxx_messageInfo_ShowObjectReq proto.InternalMessageInfo

func (m *ShowObjectReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ShowObjectReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ShowObjectReq) GetOnlyDelete() bool {
	if m != nil {
		return m.OnlyDelete
	}
	return false
}

func (m *ShowObjectReq) GetOnlyShow() bool {
	if m != nil {
		return m.OnlyShow
	}
	return false
}

type ShowObjectResp struct {
	BaseResp             *BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ShowObjectResp) Reset()         { *m = ShowObjectResp{} }
func (m *ShowObjectResp) String() string { return proto.CompactTextString(m) }
func (*ShowObjectResp) ProtoMessage()    {}
func (*ShowObjectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{35}
}
func (m *ShowObjectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowObjectResp.Unmarshal(m, b)
}
func (m *ShowObjectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowObjectResp.Marshal(b, m, deterministic)
}
func (dst *ShowObjectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowObjectResp.Merge(dst, src)
}
func (m *ShowObjectResp) XXX_Size() int {
	return xxx_messageInfo_ShowObjectResp.Size(m)
}
func (m *ShowObjectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowObjectResp.DiscardUnknown(m)
}

var xxx_messageInfo_ShowObjectResp proto.InternalMessageInfo

func (m *ShowObjectResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type SyncObjectReq struct {
	BaseReq              *BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	SyncType             SyncType `protobuf:"varint,3,opt,name=sync_type,json=syncType,proto3,enum=obsgateway.SyncType" json:"sync_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SyncObjectReq) Reset()         { *m = SyncObjectReq{} }
func (m *SyncObjectReq) String() string { return proto.CompactTextString(m) }
func (*SyncObjectReq) ProtoMessage()    {}
func (*SyncObjectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{36}
}
func (m *SyncObjectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncObjectReq.Unmarshal(m, b)
}
func (m *SyncObjectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncObjectReq.Marshal(b, m, deterministic)
}
func (dst *SyncObjectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncObjectReq.Merge(dst, src)
}
func (m *SyncObjectReq) XXX_Size() int {
	return xxx_messageInfo_SyncObjectReq.Size(m)
}
func (m *SyncObjectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncObjectReq.DiscardUnknown(m)
}

var xxx_messageInfo_SyncObjectReq proto.InternalMessageInfo

func (m *SyncObjectReq) GetBaseReq() *BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SyncObjectReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *SyncObjectReq) GetSyncType() SyncType {
	if m != nil {
		return m.SyncType
	}
	return SyncType_SYNC_TYPE_NIL
}

type SyncObjectResp struct {
	BaseResp             *BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SyncObjectResp) Reset()         { *m = SyncObjectResp{} }
func (m *SyncObjectResp) String() string { return proto.CompactTextString(m) }
func (*SyncObjectResp) ProtoMessage()    {}
func (*SyncObjectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_gateway_ae073b91f7e8ca0a, []int{37}
}
func (m *SyncObjectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncObjectResp.Unmarshal(m, b)
}
func (m *SyncObjectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncObjectResp.Marshal(b, m, deterministic)
}
func (dst *SyncObjectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncObjectResp.Merge(dst, src)
}
func (m *SyncObjectResp) XXX_Size() int {
	return xxx_messageInfo_SyncObjectResp.Size(m)
}
func (m *SyncObjectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncObjectResp.DiscardUnknown(m)
}

var xxx_messageInfo_SyncObjectResp proto.InternalMessageInfo

func (m *SyncObjectResp) GetBaseResp() *BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*BaseReq)(nil), "obsgateway.BaseReq")
	proto.RegisterType((*BaseResp)(nil), "obsgateway.BaseResp")
	proto.RegisterType((*GenerateTokenReq)(nil), "obsgateway.GenerateTokenReq")
	proto.RegisterType((*GenerateTokenResp)(nil), "obsgateway.GenerateTokenResp")
	proto.RegisterType((*ValidateTokenReq)(nil), "obsgateway.ValidateTokenReq")
	proto.RegisterType((*ValidateTokenResp)(nil), "obsgateway.ValidateTokenResp")
	proto.RegisterType((*GetTemporaryURLReq)(nil), "obsgateway.GetTemporaryURLReq")
	proto.RegisterType((*GetTemporaryURLResp)(nil), "obsgateway.GetTemporaryURLResp")
	proto.RegisterType((*GetObjectReq)(nil), "obsgateway.GetObjectReq")
	proto.RegisterType((*GetObjectResp)(nil), "obsgateway.GetObjectResp")
	proto.RegisterType((*PutObjectReq)(nil), "obsgateway.PutObjectReq")
	proto.RegisterType((*PutObjectResp)(nil), "obsgateway.PutObjectResp")
	proto.RegisterType((*HeadObjectReq)(nil), "obsgateway.HeadObjectReq")
	proto.RegisterType((*HeadObjectResp)(nil), "obsgateway.HeadObjectResp")
	proto.RegisterType((*CopyObjectReq)(nil), "obsgateway.CopyObjectReq")
	proto.RegisterType((*CopyObjectResp)(nil), "obsgateway.CopyObjectResp")
	proto.RegisterType((*DeleteObjectReq)(nil), "obsgateway.DeleteObjectReq")
	proto.RegisterType((*DeleteObjectResp)(nil), "obsgateway.DeleteObjectResp")
	proto.RegisterType((*ListObjectsReq)(nil), "obsgateway.ListObjectsReq")
	proto.RegisterType((*ListObjectsResp)(nil), "obsgateway.ListObjectsResp")
	proto.RegisterType((*ListObjectsResp_Content)(nil), "obsgateway.ListObjectsResp.Content")
	proto.RegisterType((*CreateMultipartUploadReq)(nil), "obsgateway.CreateMultipartUploadReq")
	proto.RegisterType((*CreateMultipartUploadResp)(nil), "obsgateway.CreateMultipartUploadResp")
	proto.RegisterType((*UploadPartReq)(nil), "obsgateway.UploadPartReq")
	proto.RegisterType((*UploadPartResp)(nil), "obsgateway.UploadPartResp")
	proto.RegisterType((*CompleteMultipartUploadReq)(nil), "obsgateway.CompleteMultipartUploadReq")
	proto.RegisterType((*CompleteMultipartUploadReq_Part)(nil), "obsgateway.CompleteMultipartUploadReq.Part")
	proto.RegisterType((*CompleteMultipartUploadResp)(nil), "obsgateway.CompleteMultipartUploadResp")
	proto.RegisterType((*AbortMultipartUploadReq)(nil), "obsgateway.AbortMultipartUploadReq")
	proto.RegisterType((*AbortMultipartUploadResp)(nil), "obsgateway.AbortMultipartUploadResp")
	proto.RegisterType((*ListPartsReq)(nil), "obsgateway.ListPartsReq")
	proto.RegisterType((*ListPartsResp)(nil), "obsgateway.ListPartsResp")
	proto.RegisterType((*ListPartsResp_Part)(nil), "obsgateway.ListPartsResp.Part")
	proto.RegisterType((*ListMultipartUploadsReq)(nil), "obsgateway.ListMultipartUploadsReq")
	proto.RegisterType((*ListMultipartUploadsResp)(nil), "obsgateway.ListMultipartUploadsResp")
	proto.RegisterType((*HideObjectReq)(nil), "obsgateway.HideObjectReq")
	proto.RegisterType((*HideObjectResp)(nil), "obsgateway.HideObjectResp")
	proto.RegisterType((*ShowObjectReq)(nil), "obsgateway.ShowObjectReq")
	proto.RegisterType((*ShowObjectResp)(nil), "obsgateway.ShowObjectResp")
	proto.RegisterType((*SyncObjectReq)(nil), "obsgateway.SyncObjectReq")
	proto.RegisterType((*SyncObjectResp)(nil), "obsgateway.SyncObjectResp")
	proto.RegisterEnum("obsgateway.TokenType", TokenType_name, TokenType_value)
	proto.RegisterEnum("obsgateway.SyncType", SyncType_name, SyncType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ObsGatewayClient is the client API for ObsGateway service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ObsGatewayClient interface {
	// 权限管理
	GenerateToken(ctx context.Context, in *GenerateTokenReq, opts ...grpc.CallOption) (*GenerateTokenResp, error)
	ValidateToken(ctx context.Context, in *ValidateTokenReq, opts ...grpc.CallOption) (*ValidateTokenResp, error)
	// URL 操作
	GetTemporaryURL(ctx context.Context, in *GetTemporaryURLReq, opts ...grpc.CallOption) (*GetTemporaryURLResp, error)
	// 对象操作
	GetObject(ctx context.Context, in *GetObjectReq, opts ...grpc.CallOption) (*GetObjectResp, error)
	PutObject(ctx context.Context, in *PutObjectReq, opts ...grpc.CallOption) (*PutObjectResp, error)
	HeadObject(ctx context.Context, in *HeadObjectReq, opts ...grpc.CallOption) (*HeadObjectResp, error)
	CopyObject(ctx context.Context, in *CopyObjectReq, opts ...grpc.CallOption) (*CopyObjectResp, error)
	DeleteObject(ctx context.Context, in *DeleteObjectReq, opts ...grpc.CallOption) (*DeleteObjectResp, error)
	// 分片操作
	CreateMultipartUpload(ctx context.Context, in *CreateMultipartUploadReq, opts ...grpc.CallOption) (*CreateMultipartUploadResp, error)
	UploadPart(ctx context.Context, in *UploadPartReq, opts ...grpc.CallOption) (*UploadPartResp, error)
	CompleteMultipartUpload(ctx context.Context, in *CompleteMultipartUploadReq, opts ...grpc.CallOption) (*CompleteMultipartUploadResp, error)
	AbortMultipartUpload(ctx context.Context, in *AbortMultipartUploadReq, opts ...grpc.CallOption) (*AbortMultipartUploadResp, error)
	ListParts(ctx context.Context, in *ListPartsReq, opts ...grpc.CallOption) (*ListPartsResp, error)
	ListMultipartUploads(ctx context.Context, in *ListMultipartUploadsReq, opts ...grpc.CallOption) (*ListMultipartUploadsResp, error)
	// 桶操作
	ListObjects(ctx context.Context, in *ListObjectsReq, opts ...grpc.CallOption) (*ListObjectsResp, error)
	// 扩展功能
	HideObject(ctx context.Context, in *HideObjectReq, opts ...grpc.CallOption) (*HideObjectResp, error)
	ShowObject(ctx context.Context, in *ShowObjectReq, opts ...grpc.CallOption) (*ShowObjectResp, error)
	SyncObject(ctx context.Context, in *SyncObjectReq, opts ...grpc.CallOption) (*SyncObjectResp, error)
}

type obsGatewayClient struct {
	cc *grpc.ClientConn
}

func NewObsGatewayClient(cc *grpc.ClientConn) ObsGatewayClient {
	return &obsGatewayClient{cc}
}

func (c *obsGatewayClient) GenerateToken(ctx context.Context, in *GenerateTokenReq, opts ...grpc.CallOption) (*GenerateTokenResp, error) {
	out := new(GenerateTokenResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/GenerateToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) ValidateToken(ctx context.Context, in *ValidateTokenReq, opts ...grpc.CallOption) (*ValidateTokenResp, error) {
	out := new(ValidateTokenResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/ValidateToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) GetTemporaryURL(ctx context.Context, in *GetTemporaryURLReq, opts ...grpc.CallOption) (*GetTemporaryURLResp, error) {
	out := new(GetTemporaryURLResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/GetTemporaryURL", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) GetObject(ctx context.Context, in *GetObjectReq, opts ...grpc.CallOption) (*GetObjectResp, error) {
	out := new(GetObjectResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/GetObject", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) PutObject(ctx context.Context, in *PutObjectReq, opts ...grpc.CallOption) (*PutObjectResp, error) {
	out := new(PutObjectResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/PutObject", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) HeadObject(ctx context.Context, in *HeadObjectReq, opts ...grpc.CallOption) (*HeadObjectResp, error) {
	out := new(HeadObjectResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/HeadObject", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) CopyObject(ctx context.Context, in *CopyObjectReq, opts ...grpc.CallOption) (*CopyObjectResp, error) {
	out := new(CopyObjectResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/CopyObject", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) DeleteObject(ctx context.Context, in *DeleteObjectReq, opts ...grpc.CallOption) (*DeleteObjectResp, error) {
	out := new(DeleteObjectResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/DeleteObject", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) CreateMultipartUpload(ctx context.Context, in *CreateMultipartUploadReq, opts ...grpc.CallOption) (*CreateMultipartUploadResp, error) {
	out := new(CreateMultipartUploadResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/CreateMultipartUpload", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) UploadPart(ctx context.Context, in *UploadPartReq, opts ...grpc.CallOption) (*UploadPartResp, error) {
	out := new(UploadPartResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/UploadPart", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) CompleteMultipartUpload(ctx context.Context, in *CompleteMultipartUploadReq, opts ...grpc.CallOption) (*CompleteMultipartUploadResp, error) {
	out := new(CompleteMultipartUploadResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/CompleteMultipartUpload", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) AbortMultipartUpload(ctx context.Context, in *AbortMultipartUploadReq, opts ...grpc.CallOption) (*AbortMultipartUploadResp, error) {
	out := new(AbortMultipartUploadResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/AbortMultipartUpload", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) ListParts(ctx context.Context, in *ListPartsReq, opts ...grpc.CallOption) (*ListPartsResp, error) {
	out := new(ListPartsResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/ListParts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) ListMultipartUploads(ctx context.Context, in *ListMultipartUploadsReq, opts ...grpc.CallOption) (*ListMultipartUploadsResp, error) {
	out := new(ListMultipartUploadsResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/ListMultipartUploads", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) ListObjects(ctx context.Context, in *ListObjectsReq, opts ...grpc.CallOption) (*ListObjectsResp, error) {
	out := new(ListObjectsResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/ListObjects", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) HideObject(ctx context.Context, in *HideObjectReq, opts ...grpc.CallOption) (*HideObjectResp, error) {
	out := new(HideObjectResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/HideObject", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) ShowObject(ctx context.Context, in *ShowObjectReq, opts ...grpc.CallOption) (*ShowObjectResp, error) {
	out := new(ShowObjectResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/ShowObject", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obsGatewayClient) SyncObject(ctx context.Context, in *SyncObjectReq, opts ...grpc.CallOption) (*SyncObjectResp, error) {
	out := new(SyncObjectResp)
	err := c.cc.Invoke(ctx, "/obsgateway.ObsGateway/SyncObject", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ObsGatewayServer is the server API for ObsGateway service.
type ObsGatewayServer interface {
	// 权限管理
	GenerateToken(context.Context, *GenerateTokenReq) (*GenerateTokenResp, error)
	ValidateToken(context.Context, *ValidateTokenReq) (*ValidateTokenResp, error)
	// URL 操作
	GetTemporaryURL(context.Context, *GetTemporaryURLReq) (*GetTemporaryURLResp, error)
	// 对象操作
	GetObject(context.Context, *GetObjectReq) (*GetObjectResp, error)
	PutObject(context.Context, *PutObjectReq) (*PutObjectResp, error)
	HeadObject(context.Context, *HeadObjectReq) (*HeadObjectResp, error)
	CopyObject(context.Context, *CopyObjectReq) (*CopyObjectResp, error)
	DeleteObject(context.Context, *DeleteObjectReq) (*DeleteObjectResp, error)
	// 分片操作
	CreateMultipartUpload(context.Context, *CreateMultipartUploadReq) (*CreateMultipartUploadResp, error)
	UploadPart(context.Context, *UploadPartReq) (*UploadPartResp, error)
	CompleteMultipartUpload(context.Context, *CompleteMultipartUploadReq) (*CompleteMultipartUploadResp, error)
	AbortMultipartUpload(context.Context, *AbortMultipartUploadReq) (*AbortMultipartUploadResp, error)
	ListParts(context.Context, *ListPartsReq) (*ListPartsResp, error)
	ListMultipartUploads(context.Context, *ListMultipartUploadsReq) (*ListMultipartUploadsResp, error)
	// 桶操作
	ListObjects(context.Context, *ListObjectsReq) (*ListObjectsResp, error)
	// 扩展功能
	HideObject(context.Context, *HideObjectReq) (*HideObjectResp, error)
	ShowObject(context.Context, *ShowObjectReq) (*ShowObjectResp, error)
	SyncObject(context.Context, *SyncObjectReq) (*SyncObjectResp, error)
}

func RegisterObsGatewayServer(s *grpc.Server, srv ObsGatewayServer) {
	s.RegisterService(&_ObsGateway_serviceDesc, srv)
}

func _ObsGateway_GenerateToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).GenerateToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/GenerateToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).GenerateToken(ctx, req.(*GenerateTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_ValidateToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).ValidateToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/ValidateToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).ValidateToken(ctx, req.(*ValidateTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_GetTemporaryURL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTemporaryURLReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).GetTemporaryURL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/GetTemporaryURL",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).GetTemporaryURL(ctx, req.(*GetTemporaryURLReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_GetObject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetObjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).GetObject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/GetObject",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).GetObject(ctx, req.(*GetObjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_PutObject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutObjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).PutObject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/PutObject",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).PutObject(ctx, req.(*PutObjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_HeadObject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HeadObjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).HeadObject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/HeadObject",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).HeadObject(ctx, req.(*HeadObjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_CopyObject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyObjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).CopyObject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/CopyObject",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).CopyObject(ctx, req.(*CopyObjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_DeleteObject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteObjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).DeleteObject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/DeleteObject",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).DeleteObject(ctx, req.(*DeleteObjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_CreateMultipartUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMultipartUploadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).CreateMultipartUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/CreateMultipartUpload",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).CreateMultipartUpload(ctx, req.(*CreateMultipartUploadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_UploadPart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadPartReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).UploadPart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/UploadPart",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).UploadPart(ctx, req.(*UploadPartReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_CompleteMultipartUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompleteMultipartUploadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).CompleteMultipartUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/CompleteMultipartUpload",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).CompleteMultipartUpload(ctx, req.(*CompleteMultipartUploadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_AbortMultipartUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AbortMultipartUploadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).AbortMultipartUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/AbortMultipartUpload",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).AbortMultipartUpload(ctx, req.(*AbortMultipartUploadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_ListParts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPartsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).ListParts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/ListParts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).ListParts(ctx, req.(*ListPartsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_ListMultipartUploads_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMultipartUploadsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).ListMultipartUploads(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/ListMultipartUploads",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).ListMultipartUploads(ctx, req.(*ListMultipartUploadsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_ListObjects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListObjectsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).ListObjects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/ListObjects",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).ListObjects(ctx, req.(*ListObjectsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_HideObject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HideObjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).HideObject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/HideObject",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).HideObject(ctx, req.(*HideObjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_ShowObject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShowObjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).ShowObject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/ShowObject",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).ShowObject(ctx, req.(*ShowObjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObsGateway_SyncObject_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncObjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObsGatewayServer).SyncObject(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/obsgateway.ObsGateway/SyncObject",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObsGatewayServer).SyncObject(ctx, req.(*SyncObjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ObsGateway_serviceDesc = grpc.ServiceDesc{
	ServiceName: "obsgateway.ObsGateway",
	HandlerType: (*ObsGatewayServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateToken",
			Handler:    _ObsGateway_GenerateToken_Handler,
		},
		{
			MethodName: "ValidateToken",
			Handler:    _ObsGateway_ValidateToken_Handler,
		},
		{
			MethodName: "GetTemporaryURL",
			Handler:    _ObsGateway_GetTemporaryURL_Handler,
		},
		{
			MethodName: "GetObject",
			Handler:    _ObsGateway_GetObject_Handler,
		},
		{
			MethodName: "PutObject",
			Handler:    _ObsGateway_PutObject_Handler,
		},
		{
			MethodName: "HeadObject",
			Handler:    _ObsGateway_HeadObject_Handler,
		},
		{
			MethodName: "CopyObject",
			Handler:    _ObsGateway_CopyObject_Handler,
		},
		{
			MethodName: "DeleteObject",
			Handler:    _ObsGateway_DeleteObject_Handler,
		},
		{
			MethodName: "CreateMultipartUpload",
			Handler:    _ObsGateway_CreateMultipartUpload_Handler,
		},
		{
			MethodName: "UploadPart",
			Handler:    _ObsGateway_UploadPart_Handler,
		},
		{
			MethodName: "CompleteMultipartUpload",
			Handler:    _ObsGateway_CompleteMultipartUpload_Handler,
		},
		{
			MethodName: "AbortMultipartUpload",
			Handler:    _ObsGateway_AbortMultipartUpload_Handler,
		},
		{
			MethodName: "ListParts",
			Handler:    _ObsGateway_ListParts_Handler,
		},
		{
			MethodName: "ListMultipartUploads",
			Handler:    _ObsGateway_ListMultipartUploads_Handler,
		},
		{
			MethodName: "ListObjects",
			Handler:    _ObsGateway_ListObjects_Handler,
		},
		{
			MethodName: "HideObject",
			Handler:    _ObsGateway_HideObject_Handler,
		},
		{
			MethodName: "ShowObject",
			Handler:    _ObsGateway_ShowObject_Handler,
		},
		{
			MethodName: "SyncObject",
			Handler:    _ObsGateway_SyncObject_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "obs-gateway/obs-gateway.proto",
}

func init() {
	proto.RegisterFile("obs-gateway/obs-gateway.proto", fileDescriptor_obs_gateway_ae073b91f7e8ca0a)
}

var fileDescriptor_obs_gateway_ae073b91f7e8ca0a = []byte{
	// 1878 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x59, 0xcf, 0x72, 0x1b, 0x49,
	0x19, 0xf7, 0x48, 0xb2, 0x25, 0x7d, 0x92, 0x1c, 0xa5, 0x93, 0xc5, 0xca, 0x78, 0xb3, 0xf1, 0xf6,
	0xee, 0x42, 0x76, 0x59, 0x64, 0xd6, 0x90, 0x2a, 0x8a, 0x3d, 0x6d, 0x1c, 0xe3, 0xb8, 0x62, 0x3b,
	0x66, 0x2c, 0x43, 0x2d, 0x1c, 0xa6, 0x46, 0x33, 0x1d, 0x65, 0xd0, 0x68, 0x66, 0x32, 0xdd, 0x5a,
	0x5b, 0x50, 0x14, 0x17, 0x6e, 0x54, 0xc1, 0x95, 0x27, 0xd8, 0x2b, 0xc5, 0x0d, 0x6e, 0x1c, 0x78,
	0x04, 0x2e, 0x3c, 0x03, 0x3c, 0x04, 0xd5, 0x3d, 0x3d, 0x7f, 0x7a, 0x46, 0xf2, 0x3a, 0x42, 0x9b,
	0xa2, 0xb8, 0x4d, 0xff, 0xfa, 0xd3, 0xd7, 0xdf, 0xdf, 0xfe, 0x75, 0xb7, 0xe0, 0x7e, 0x30, 0xa4,
	0xdf, 0x19, 0x59, 0x8c, 0x5c, 0x5a, 0xb3, 0xdd, 0xdc, 0x77, 0x3f, 0x8c, 0x02, 0x16, 0x20, 0x08,
	0x86, 0x54, 0x22, 0xf8, 0xaf, 0x1a, 0xd4, 0x1f, 0x5b, 0x94, 0x18, 0xe4, 0x15, 0xba, 0x0b, 0xeb,
	0x56, 0x18, 0xba, 0x4e, 0x4f, 0xdb, 0xd1, 0x1e, 0x36, 0x8d, 0x78, 0xc0, 0x51, 0x6a, 0x07, 0x21,
	0xe9, 0x55, 0x62, 0x54, 0x0c, 0xd0, 0x7d, 0x80, 0x88, 0xbc, 0x9a, 0x12, 0xca, 0x4c, 0xd7, 0xe9,
	0x55, 0xc5, 0x54, 0x53, 0x22, 0x47, 0x0e, 0xd2, 0xa1, 0x11, 0x84, 0x24, 0xb2, 0x58, 0x10, 0xf5,
	0x6a, 0x62, 0x32, 0x1d, 0xa3, 0x2e, 0x54, 0xa7, 0xae, 0xd3, 0xdb, 0xd8, 0xd1, 0x1e, 0x76, 0x0c,
	0xfe, 0x89, 0xee, 0x41, 0x43, 0x68, 0xe5, 0xaa, 0xea, 0x02, 0xae, 0x8b, 0x71, 0xac, 0x68, 0xea,
	0x47, 0xe4, 0x0b, 0x97, 0x5c, 0xf6, 0x1a, 0x3b, 0xda, 0xc3, 0x86, 0x91, 0x8e, 0xf1, 0x39, 0x34,
	0x62, 0xd3, 0x69, 0xf8, 0x5a, 0xb6, 0x6f, 0x43, 0xd3, 0xa5, 0x66, 0x38, 0x1d, 0x7a, 0xae, 0x2d,
	0x4c, 0x6f, 0x18, 0x0d, 0x97, 0x9e, 0x89, 0x31, 0xfe, 0xa7, 0x06, 0xdd, 0x43, 0xe2, 0x73, 0x5b,
	0xc9, 0x20, 0x18, 0x13, 0xff, 0x75, 0x23, 0x23, 0xdd, 0xab, 0x66, 0xee, 0x75, 0xa1, 0xca, 0x98,
	0x27, 0xe2, 0xd0, 0x31, 0xf8, 0x27, 0x8f, 0xde, 0x98, 0xcc, 0xcc, 0x30, 0x22, 0x2f, 0xdc, 0xab,
	0xde, 0x7a, 0x1c, 0xbd, 0x31, 0x99, 0x9d, 0x09, 0x00, 0xed, 0x40, 0x8b, 0x45, 0x96, 0x4f, 0xed,
	0xc0, 0x71, 0xfd, 0x91, 0x88, 0x54, 0xd3, 0xc8, 0x43, 0xe8, 0xfb, 0x00, 0x8c, 0x1b, 0x67, 0xb2,
	0x59, 0x48, 0x44, 0xcc, 0x36, 0xf7, 0xde, 0xea, 0x67, 0x79, 0xed, 0x0b, 0xd3, 0x07, 0xb3, 0x90,
	0x18, 0x4d, 0x96, 0x7c, 0xe2, 0x0f, 0xe1, 0x76, 0xc1, 0xb5, 0x38, 0x72, 0x42, 0x22, 0xf1, 0x4d,
	0x0c, 0xf0, 0x0f, 0xa1, 0xfb, 0x13, 0xcb, 0x73, 0x9d, 0x42, 0x14, 0xca, 0x92, 0xdc, 0xbb, 0x31,
	0x99, 0xc9, 0x18, 0xf0, 0x4f, 0x4c, 0xe1, 0x76, 0xe1, 0xb7, 0xaf, 0x99, 0xa0, 0x72, 0x08, 0x0b,
	0x11, 0xa9, 0x95, 0x22, 0x82, 0x7f, 0x00, 0xe8, 0x90, 0xb0, 0x01, 0x99, 0x84, 0x41, 0x64, 0x45,
	0xb3, 0x0b, 0xe3, 0x98, 0x9b, 0xcc, 0x35, 0x45, 0x9e, 0x5c, 0x93, 0x7f, 0x26, 0xc9, 0xa8, 0xa4,
	0xc9, 0xc0, 0xdf, 0x85, 0x3b, 0xa5, 0x5f, 0xd2, 0x90, 0x17, 0x25, 0x23, 0x93, 0xd0, 0xcc, 0x7e,
	0x5f, 0xe7, 0xe3, 0x8b, 0xc8, 0xc3, 0xbf, 0xd7, 0xa0, 0x7d, 0x48, 0xd8, 0xf3, 0xe1, 0x2f, 0x88,
	0xcd, 0xf8, 0x32, 0x7d, 0x68, 0x0c, 0x2d, 0x4a, 0xcc, 0x88, 0xbc, 0x12, 0xb2, 0xad, 0xbd, 0x3b,
	0xf9, 0x64, 0xc8, 0x06, 0x33, 0xea, 0x43, 0xd9, 0x69, 0xa5, 0x98, 0xa1, 0x07, 0xd0, 0x8a, 0x2c,
	0x7f, 0x44, 0x4c, 0xca, 0xac, 0x88, 0x09, 0xd7, 0xab, 0x06, 0x08, 0xe8, 0x9c, 0x23, 0xbc, 0x68,
	0x63, 0x01, 0xe2, 0x3b, 0xc2, 0xff, 0xaa, 0xd1, 0x10, 0xc0, 0x81, 0xef, 0xe0, 0xbf, 0x57, 0xa1,
	0x93, 0x33, 0x88, 0x86, 0xe8, 0x13, 0x68, 0x4a, 0x8b, 0x68, 0x28, 0x4d, 0xba, 0x5b, 0x36, 0x89,
	0x86, 0x46, 0x63, 0x98, 0xb4, 0x10, 0x82, 0xda, 0x30, 0x70, 0x62, 0xab, 0xda, 0x86, 0xf8, 0xe6,
	0x18, 0x61, 0xd6, 0x48, 0x36, 0xb8, 0xf8, 0x46, 0xef, 0x41, 0xc7, 0xb3, 0x28, 0x33, 0x27, 0x81,
	0xe3, 0xbe, 0x70, 0x49, 0x62, 0x4d, 0x9b, 0x83, 0x27, 0x12, 0x43, 0x1f, 0xc0, 0xa6, 0x1d, 0xf8,
	0x8c, 0xf8, 0xcc, 0xf4, 0x88, 0x3f, 0x62, 0x2f, 0x45, 0x95, 0x57, 0x8d, 0x8e, 0x44, 0x8f, 0x05,
	0x88, 0xde, 0x85, 0x76, 0x22, 0x26, 0x2a, 0x59, 0x96, 0xba, 0xc4, 0x78, 0xd1, 0xa2, 0x0f, 0xa1,
	0x9b, 0x88, 0x10, 0x5f, 0xe6, 0xbf, 0x2e, 0xc4, 0x6e, 0x49, 0xfc, 0x40, 0xc2, 0x79, 0x51, 0xcf,
	0xf2, 0x47, 0x53, 0x6b, 0x44, 0xc4, 0xa6, 0x91, 0x89, 0x1e, 0x4b, 0x18, 0xed, 0xc2, 0x9d, 0x44,
	0xd4, 0x71, 0x69, 0x18, 0x50, 0x97, 0xb9, 0x81, 0xdf, 0x6b, 0x0a, 0x69, 0x24, 0xa7, 0x9e, 0x64,
	0x33, 0xdc, 0x6b, 0xdb, 0xb2, 0x5f, 0x12, 0x93, 0xcf, 0x45, 0x81, 0xd7, 0x03, 0x21, 0xda, 0x16,
	0xe0, 0x7e, 0x8c, 0xa1, 0x1e, 0xd4, 0xc9, 0x55, 0xe8, 0x46, 0x84, 0xf6, 0x5a, 0xc2, 0xdd, 0x64,
	0x28, 0x7e, 0x2e, 0xd7, 0x13, 0x59, 0xeb, 0xb5, 0xe5, 0xcf, 0x63, 0xd0, 0xe0, 0x18, 0xfe, 0x47,
	0x15, 0xda, 0x67, 0xd3, 0x95, 0xd6, 0x55, 0x92, 0xd4, 0x6a, 0x2e, 0xa9, 0xe5, 0xdc, 0xd4, 0x6e,
	0x92, 0x9b, 0xf5, 0x72, 0x6e, 0x1e, 0x40, 0x32, 0x34, 0x27, 0xce, 0x23, 0x99, 0x3d, 0x90, 0xd0,
	0x89, 0xf3, 0xe8, 0xff, 0x24, 0x79, 0xdb, 0xd0, 0xf4, 0x03, 0x53, 0xb2, 0x50, 0x3b, 0x26, 0x0c,
	0x3f, 0x30, 0xc4, 0xb8, 0xb8, 0x35, 0x75, 0xca, 0x5b, 0xd3, 0x1f, 0x35, 0xe8, 0xe4, 0xd2, 0xba,
	0x5c, 0x77, 0xce, 0x4d, 0x6d, 0xa9, 0x37, 0xfb, 0x70, 0x27, 0xb7, 0xb2, 0xc9, 0x2c, 0x3a, 0xe6,
	0xa4, 0x1a, 0xef, 0x97, 0xb7, 0x73, 0x53, 0x03, 0x8b, 0x8e, 0x8f, 0x1c, 0xfc, 0x63, 0xe8, 0x3c,
	0x25, 0x96, 0xb3, 0xc2, 0x8a, 0xc3, 0xfb, 0xb0, 0x99, 0x57, 0xb9, 0x94, 0xb7, 0xf8, 0xcf, 0x1a,
	0x74, 0xf6, 0x83, 0x70, 0xb6, 0xca, 0x56, 0xd8, 0x82, 0x3a, 0x8d, 0x6c, 0xb1, 0x9f, 0xc7, 0x21,
	0xdb, 0xa0, 0x91, 0x7d, 0x11, 0x79, 0x08, 0x43, 0x67, 0x1a, 0x79, 0xa6, 0x24, 0xd4, 0x94, 0xa9,
	0x5b, 0xd3, 0xc8, 0x8b, 0x69, 0x94, 0x79, 0x37, 0x68, 0x06, 0xfc, 0x2b, 0xd8, 0xcc, 0x9b, 0xfc,
	0x75, 0xa6, 0x59, 0xd2, 0x5a, 0x2d, 0xa5, 0x35, 0x7c, 0x0e, 0xb7, 0x9e, 0x10, 0x8f, 0x30, 0xb2,
	0xca, 0x54, 0x1e, 0x40, 0x57, 0x55, 0xba, 0x5c, 0x32, 0x7f, 0xa7, 0xc1, 0xe6, 0xb1, 0x4b, 0x65,
	0x03, 0xd0, 0x65, 0x6c, 0xfb, 0x06, 0x6c, 0xc8, 0xc3, 0x52, 0x6c, 0x9e, 0x1c, 0x71, 0x7c, 0x62,
	0x45, 0x63, 0x12, 0x25, 0x29, 0x8d, 0x47, 0x9c, 0xbc, 0x27, 0xd6, 0x95, 0x39, 0x26, 0x33, 0x2a,
	0xa2, 0xb4, 0x6e, 0xd4, 0x27, 0xd6, 0xd5, 0x33, 0x32, 0xa3, 0xf8, 0xcb, 0x0a, 0xdc, 0x52, 0xac,
	0x59, 0x2e, 0x51, 0xef, 0x42, 0xdb, 0xa5, 0x26, 0x8b, 0xa6, 0xbe, 0x6d, 0x31, 0xe2, 0x08, 0xbb,
	0x1a, 0x46, 0xcb, 0xa5, 0x83, 0x04, 0xe2, 0xbb, 0xa3, 0x4f, 0xae, 0x98, 0xa9, 0x58, 0x08, 0x1c,
	0x3a, 0x89, 0xad, 0xfc, 0x51, 0x56, 0x54, 0x9e, 0x4b, 0x59, 0xaf, 0xb6, 0x53, 0x7d, 0xd8, 0xda,
	0x7b, 0x2f, 0xbf, 0x72, 0xc1, 0xd2, 0xfe, 0xbe, 0xe4, 0x8b, 0xa4, 0xf2, 0xf8, 0xbc, 0x3e, 0x80,
	0xba, 0xc4, 0x93, 0x24, 0x6a, 0x4a, 0xfd, 0x50, 0xf7, 0x97, 0xf1, 0x09, 0xab, 0x6a, 0x88, 0xef,
	0x32, 0x85, 0x57, 0xcb, 0x14, 0x8e, 0x7f, 0x03, 0xbd, 0xfd, 0x88, 0x58, 0x8c, 0x9c, 0x4c, 0x3d,
	0xe6, 0x86, 0x56, 0xc4, 0x2e, 0x42, 0x2f, 0xb0, 0x9c, 0xd5, 0x74, 0x63, 0xb1, 0xa1, 0xaa, 0xe5,
	0x86, 0xfa, 0x35, 0xdc, 0x5b, 0x60, 0xc0, 0xaa, 0x7a, 0x6b, 0x1b, 0x9a, 0x53, 0xa1, 0x32, 0xbb,
	0xc4, 0x34, 0x62, 0xe0, 0xc8, 0xc1, 0xff, 0xd2, 0xa0, 0x13, 0x2f, 0x78, 0x66, 0x45, 0x2b, 0xda,
	0x83, 0xae, 0x5b, 0x90, 0xd7, 0x0b, 0x77, 0xd2, 0xf4, 0xa7, 0x93, 0x21, 0x89, 0x64, 0xdd, 0x02,
	0x87, 0x4e, 0x05, 0x92, 0x92, 0xf9, 0xfa, 0xb5, 0x64, 0xbe, 0x31, 0x8f, 0xcc, 0x0b, 0x4c, 0x5d,
	0x2f, 0x32, 0x35, 0xfe, 0x29, 0x6c, 0xe6, 0x9d, 0x5d, 0xfa, 0x08, 0x29, 0xf6, 0xaa, 0x4a, 0xb6,
	0x57, 0xe1, 0xbf, 0x55, 0x40, 0xdf, 0x0f, 0x26, 0x21, 0xdf, 0x47, 0xbe, 0x96, 0x4a, 0xba, 0x36,
	0xa6, 0x4f, 0xa1, 0x29, 0x62, 0x9a, 0xeb, 0xaf, 0x6f, 0xe7, 0xf5, 0x2f, 0xb6, 0xac, 0x2f, 0x02,
	0xd1, 0xe0, 0x18, 0x6f, 0x32, 0x7e, 0x13, 0x9d, 0xb8, 0x13, 0x32, 0xc8, 0x76, 0xff, 0x74, 0xfc,
	0xd5, 0x17, 0x36, 0xfd, 0x53, 0xa8, 0x71, 0x7d, 0x69, 0x84, 0xb4, 0xdc, 0x6e, 0x5e, 0xc8, 0x7b,
	0xa5, 0x98, 0x77, 0xfc, 0xa5, 0x06, 0xdb, 0x0b, 0x0d, 0xfd, 0x5f, 0x3a, 0x4e, 0x5c, 0xc1, 0xd6,
	0x67, 0xc3, 0x20, 0x62, 0x6f, 0x3c, 0xcf, 0xf8, 0x04, 0x7a, 0xf3, 0x57, 0x5e, 0x8e, 0xb2, 0xfe,
	0xa2, 0x41, 0x9b, 0x67, 0x9d, 0xe7, 0x8c, 0xbe, 0x81, 0x32, 0xdd, 0x86, 0x26, 0xe7, 0x2b, 0x6e,
	0x78, 0x42, 0x58, 0x9c, 0xc0, 0xc4, 0xf2, 0xe8, 0x63, 0x40, 0xb9, 0xfa, 0x48, 0xe8, 0x64, 0x5d,
	0x48, 0x75, 0xb3, 0x32, 0x89, 0x49, 0x05, 0xff, 0xbb, 0x02, 0x9d, 0x9c, 0xe9, 0xcb, 0x95, 0xc7,
	0x23, 0xd8, 0x12, 0xd4, 0x35, 0x67, 0xdd, 0xb8, 0x3c, 0xef, 0xf2, 0xe9, 0xb3, 0xc2, 0xda, 0xaa,
	0x1b, 0xd5, 0x82, 0x1b, 0x45, 0xc6, 0xac, 0x95, 0x19, 0xf3, 0xd3, 0x7c, 0xb7, 0xae, 0x8b, 0x6e,
	0x7d, 0xa7, 0xc8, 0x86, 0xa9, 0x5f, 0x85, 0x06, 0xd5, 0xd9, 0x7f, 0xd1, 0x62, 0x37, 0x62, 0xc4,
	0x94, 0x4a, 0xb9, 0xe5, 0xb5, 0x98, 0x4a, 0xf1, 0x11, 0x6c, 0xf1, 0xd5, 0x0b, 0x75, 0xb7, 0x4c,
	0xcd, 0xf0, 0x1a, 0x9e, 0xaf, 0x6a, 0xb9, 0x1a, 0xe6, 0x67, 0x7b, 0xd7, 0x21, 0xab, 0x3e, 0xdb,
	0xe7, 0x54, 0x2e, 0x67, 0xd7, 0x1f, 0x34, 0xe8, 0x9c, 0xbf, 0x0c, 0x2e, 0x57, 0xfc, 0x7c, 0x12,
	0xf8, 0xde, 0xcc, 0x74, 0xc4, 0x71, 0x55, 0x3e, 0xea, 0x01, 0x87, 0xe2, 0x03, 0x2c, 0xaf, 0x4c,
	0x21, 0x40, 0x5f, 0x06, 0x97, 0xb2, 0xf2, 0x1a, 0x1c, 0xe0, 0x86, 0x70, 0xb7, 0xf2, 0x06, 0x2d,
	0xe7, 0xd6, 0x6f, 0xb9, 0x5b, 0x33, 0xdf, 0x5e, 0xa5, 0x5b, 0x9f, 0x40, 0x93, 0xce, 0x7c, 0x3b,
	0x3b, 0x21, 0x6d, 0xaa, 0x66, 0xf0, 0xf5, 0xc4, 0x23, 0x5f, 0x83, 0xca, 0x2f, 0xe1, 0x4b, 0xce,
	0x8a, 0xa5, 0x7c, 0xf9, 0xe8, 0xe7, 0xd0, 0x4c, 0x1f, 0x10, 0x11, 0x82, 0xcd, 0xc1, 0xf3, 0x67,
	0x07, 0xa7, 0xe6, 0xe0, 0xf3, 0xb3, 0x03, 0xf3, 0xb3, 0xe3, 0xe3, 0xee, 0x5a, 0x01, 0x3b, 0x3c,
	0x18, 0x74, 0xb5, 0x02, 0x76, 0x76, 0x31, 0xe8, 0x56, 0x0a, 0xd8, 0x93, 0x83, 0xe3, 0x6e, 0xed,
	0xa3, 0x7d, 0x68, 0x24, 0x76, 0xa3, 0xdb, 0xd0, 0x39, 0xff, 0xfc, 0x74, 0x3f, 0x9e, 0x3e, 0x3d,
	0xe2, 0xaa, 0x15, 0x88, 0x6b, 0xd1, 0x54, 0x88, 0x2b, 0xa9, 0xec, 0xfd, 0xa9, 0x05, 0xf0, 0x7c,
	0x48, 0x0f, 0x63, 0x1f, 0xd0, 0x29, 0x74, 0x94, 0x97, 0x4d, 0xf4, 0x76, 0xde, 0xc3, 0xe2, 0x7b,
	0xae, 0x7e, 0xff, 0x9a, 0x59, 0x1a, 0xe2, 0x35, 0xae, 0x4f, 0x79, 0xc2, 0x54, 0xf5, 0x15, 0x5f,
	0x46, 0x55, 0x7d, 0xa5, 0xb7, 0x4f, 0xbc, 0x86, 0x06, 0x70, 0xab, 0xf0, 0xc6, 0x88, 0xde, 0x51,
	0x6d, 0x28, 0x3e, 0x5d, 0xea, 0x0f, 0xae, 0x9d, 0x17, 0x5a, 0x1f, 0x43, 0x33, 0x7d, 0xf5, 0x43,
	0xbd, 0x82, 0x7c, 0x5a, 0x87, 0xfa, 0xbd, 0x05, 0x33, 0x89, 0x8e, 0xf4, 0x6d, 0x42, 0xd5, 0x91,
	0x7f, 0x89, 0x52, 0x75, 0x28, 0x8f, 0x19, 0x78, 0x0d, 0x1d, 0x00, 0x64, 0x57, 0x7e, 0xa4, 0x88,
	0x2a, 0xaf, 0x0b, 0xba, 0xbe, 0x68, 0x2a, 0x51, 0x93, 0x5d, 0xa0, 0x55, 0x35, 0xca, 0x5b, 0x80,
	0xaa, 0x46, 0xbd, 0x73, 0xe3, 0x35, 0xf4, 0x0c, 0xda, 0xf9, 0x5b, 0x2b, 0xda, 0xce, 0x4b, 0x17,
	0x2e, 0xc9, 0xfa, 0xdb, 0x8b, 0x27, 0x85, 0xb2, 0x17, 0xf0, 0xd6, 0xdc, 0x3b, 0x08, 0x7a, 0x5f,
	0xb1, 0x61, 0xc1, 0x3d, 0x49, 0xff, 0xe0, 0x06, 0x52, 0x89, 0xef, 0xd9, 0xf1, 0x5b, 0xf5, 0x5d,
	0xb9, 0x83, 0xa8, 0xbe, 0xab, 0x27, 0x76, 0xbc, 0x86, 0x3c, 0xd8, 0x5a, 0x70, 0x50, 0x44, 0xdf,
	0xbc, 0xd9, 0xb1, 0x57, 0xff, 0xd6, 0x8d, 0xe4, 0xc4, 0x6a, 0x36, 0xdc, 0x9d, 0x77, 0xe8, 0x42,
	0xca, 0x0d, 0x76, 0xc1, 0x81, 0x50, 0x7f, 0xff, 0xab, 0x85, 0x92, 0x02, 0x4d, 0x69, 0x5f, 0x2d,
	0xd0, 0xfc, 0x01, 0x4d, 0x2d, 0x50, 0xe5, 0x9c, 0x10, 0x1b, 0x3a, 0x8f, 0x59, 0x51, 0xe9, 0xaa,
	0x3d, 0x87, 0xc6, 0x55, 0x43, 0x17, 0x11, 0x34, 0x5e, 0x43, 0x4f, 0xa1, 0x95, 0xbb, 0xad, 0x23,
	0x7d, 0xe1, 0x35, 0xfe, 0x95, 0xbe, 0x7d, 0xcd, 0x15, 0x5f, 0xf6, 0x53, 0x4a, 0xb3, 0x85, 0x7e,
	0xca, 0x33, 0x7a, 0xa1, 0x9f, 0x14, 0x66, 0x8e, 0xd5, 0x64, 0xb4, 0xa6, 0xaa, 0x51, 0xf8, 0x57,
	0x55, 0xa3, 0x32, 0xa1, 0x54, 0x93, 0x32, 0x4a, 0x41, 0x4d, 0x9e, 0xef, 0x0a, 0x6a, 0x14, 0x12,
	0xc2, 0x6b, 0x8f, 0xfb, 0x3f, 0xfb, 0x78, 0x14, 0x78, 0x96, 0x3f, 0xea, 0x3f, 0xda, 0x63, 0xac,
	0x6f, 0x07, 0x93, 0x5d, 0xf1, 0x77, 0xa4, 0x1d, 0x78, 0xbb, 0x94, 0x44, 0x5f, 0xb8, 0x36, 0xa1,
	0xbb, 0x99, 0x82, 0xe1, 0x86, 0x98, 0xfd, 0xde, 0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0xc2, 0x8c,
	0xd6, 0x46, 0xcd, 0x1c, 0x00, 0x00,
}
