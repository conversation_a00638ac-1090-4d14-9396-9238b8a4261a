// Code generated by protoc-gen-go. DO NOT EDIT.
// source: present-illustration/present-illustration.proto

package present_illustration // import "golang.52tt.com/protocol/services/present-illustration"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 图鉴状态
type IllustrationStatus int32

const (
	IllustrationStatus_ILLUSTRATION_STATUS_UNSPECIFIED IllustrationStatus = 0
	IllustrationStatus_ILLUSTRATION_STATUS_IN_PROGRESS IllustrationStatus = 1
	IllustrationStatus_ILLUSTRATION_STATUS_NOT_STARTED IllustrationStatus = 2
	IllustrationStatus_ILLUSTRATION_STATUS_ENDED       IllustrationStatus = 3
)

var IllustrationStatus_name = map[int32]string{
	0: "ILLUSTRATION_STATUS_UNSPECIFIED",
	1: "ILLUSTRATION_STATUS_IN_PROGRESS",
	2: "ILLUSTRATION_STATUS_NOT_STARTED",
	3: "ILLUSTRATION_STATUS_ENDED",
}
var IllustrationStatus_value = map[string]int32{
	"ILLUSTRATION_STATUS_UNSPECIFIED": 0,
	"ILLUSTRATION_STATUS_IN_PROGRESS": 1,
	"ILLUSTRATION_STATUS_NOT_STARTED": 2,
	"ILLUSTRATION_STATUS_ENDED":       3,
}

func (x IllustrationStatus) String() string {
	return proto.EnumName(IllustrationStatus_name, int32(x))
}
func (IllustrationStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{0}
}

// 活动状态
type IllustrationSearchType int32

const (
	IllustrationSearchType_ILLUSTRATION_SEARCH_TYPE_UNSPECIFIED IllustrationSearchType = 0
	IllustrationSearchType_ILLUSTRATION_SEARCH_TYPE_ID          IllustrationSearchType = 1
	IllustrationSearchType_ILLUSTRATION_SEARCH_TYPE_NAME        IllustrationSearchType = 2
	IllustrationSearchType_ILLUSTRATION_SEARCH_TYPE_REMARK      IllustrationSearchType = 3
)

var IllustrationSearchType_name = map[int32]string{
	0: "ILLUSTRATION_SEARCH_TYPE_UNSPECIFIED",
	1: "ILLUSTRATION_SEARCH_TYPE_ID",
	2: "ILLUSTRATION_SEARCH_TYPE_NAME",
	3: "ILLUSTRATION_SEARCH_TYPE_REMARK",
}
var IllustrationSearchType_value = map[string]int32{
	"ILLUSTRATION_SEARCH_TYPE_UNSPECIFIED": 0,
	"ILLUSTRATION_SEARCH_TYPE_ID":          1,
	"ILLUSTRATION_SEARCH_TYPE_NAME":        2,
	"ILLUSTRATION_SEARCH_TYPE_REMARK":      3,
}

func (x IllustrationSearchType) String() string {
	return proto.EnumName(IllustrationSearchType_name, int32(x))
}
func (IllustrationSearchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{1}
}

type IllustrationInfoRedType int32

const (
	IllustrationInfoRedType_Unknow    IllustrationInfoRedType = 0
	IllustrationInfoRedType_Collected IllustrationInfoRedType = 1
	IllustrationInfoRedType_Upgrade   IllustrationInfoRedType = 2
)

var IllustrationInfoRedType_name = map[int32]string{
	0: "Unknow",
	1: "Collected",
	2: "Upgrade",
}
var IllustrationInfoRedType_value = map[string]int32{
	"Unknow":    0,
	"Collected": 1,
	"Upgrade":   2,
}

func (x IllustrationInfoRedType) String() string {
	return proto.EnumName(IllustrationInfoRedType_name, int32(x))
}
func (IllustrationInfoRedType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{2}
}

type GetIllustrationConfigListReq_SortType int32

const (
	GetIllustrationConfigListReq_SORT_TYPE_UNSPECIFIED GetIllustrationConfigListReq_SortType = 0
	GetIllustrationConfigListReq_SORT_TYPE_TOTAL_PRICE GetIllustrationConfigListReq_SortType = 1
	GetIllustrationConfigListReq_SORT_TYPE_START_TIME  GetIllustrationConfigListReq_SortType = 2
	GetIllustrationConfigListReq_SORT_TYPE_END_TIME    GetIllustrationConfigListReq_SortType = 3
	GetIllustrationConfigListReq_SORT_TYPE_ID          GetIllustrationConfigListReq_SortType = 4
)

var GetIllustrationConfigListReq_SortType_name = map[int32]string{
	0: "SORT_TYPE_UNSPECIFIED",
	1: "SORT_TYPE_TOTAL_PRICE",
	2: "SORT_TYPE_START_TIME",
	3: "SORT_TYPE_END_TIME",
	4: "SORT_TYPE_ID",
}
var GetIllustrationConfigListReq_SortType_value = map[string]int32{
	"SORT_TYPE_UNSPECIFIED": 0,
	"SORT_TYPE_TOTAL_PRICE": 1,
	"SORT_TYPE_START_TIME":  2,
	"SORT_TYPE_END_TIME":    3,
	"SORT_TYPE_ID":          4,
}

func (x GetIllustrationConfigListReq_SortType) String() string {
	return proto.EnumName(GetIllustrationConfigListReq_SortType_name, int32(x))
}
func (GetIllustrationConfigListReq_SortType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{3, 0}
}

type GetIllustrationConfigListReq_SortOrder int32

const (
	GetIllustrationConfigListReq_SORT_ORDER_UNSPECIFIED GetIllustrationConfigListReq_SortOrder = 0
	GetIllustrationConfigListReq_SORT_ORDER_ASC         GetIllustrationConfigListReq_SortOrder = 1
	GetIllustrationConfigListReq_SORT_ORDER_DESC        GetIllustrationConfigListReq_SortOrder = 2
)

var GetIllustrationConfigListReq_SortOrder_name = map[int32]string{
	0: "SORT_ORDER_UNSPECIFIED",
	1: "SORT_ORDER_ASC",
	2: "SORT_ORDER_DESC",
}
var GetIllustrationConfigListReq_SortOrder_value = map[string]int32{
	"SORT_ORDER_UNSPECIFIED": 0,
	"SORT_ORDER_ASC":         1,
	"SORT_ORDER_DESC":        2,
}

func (x GetIllustrationConfigListReq_SortOrder) String() string {
	return proto.EnumName(GetIllustrationConfigListReq_SortOrder_name, int32(x))
}
func (GetIllustrationConfigListReq_SortOrder) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{3, 1}
}

// 马甲包跳转配置
type PackageJumpConfig struct {
	App                  uint32   `protobuf:"varint,1,opt,name=app,proto3" json:"app,omitempty"`
	Platform             uint32   `protobuf:"varint,2,opt,name=platform,proto3" json:"platform,omitempty"`
	Link                 string   `protobuf:"bytes,3,opt,name=link,proto3" json:"link,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PackageJumpConfig) Reset()         { *m = PackageJumpConfig{} }
func (m *PackageJumpConfig) String() string { return proto.CompactTextString(m) }
func (*PackageJumpConfig) ProtoMessage()    {}
func (*PackageJumpConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{0}
}
func (m *PackageJumpConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PackageJumpConfig.Unmarshal(m, b)
}
func (m *PackageJumpConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PackageJumpConfig.Marshal(b, m, deterministic)
}
func (dst *PackageJumpConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PackageJumpConfig.Merge(dst, src)
}
func (m *PackageJumpConfig) XXX_Size() int {
	return xxx_messageInfo_PackageJumpConfig.Size(m)
}
func (m *PackageJumpConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PackageJumpConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PackageJumpConfig proto.InternalMessageInfo

func (m *PackageJumpConfig) GetApp() uint32 {
	if m != nil {
		return m.App
	}
	return 0
}

func (m *PackageJumpConfig) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *PackageJumpConfig) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

// 图鉴配置
type IllustrationConfig struct {
	Id                    uint32                           `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                  string                           `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Icon                  string                           `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	TotalPrice            uint64                           `protobuf:"varint,4,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	TotalPriceCny         string                           `protobuf:"bytes,5,opt,name=total_price_cny,json=totalPriceCny,proto3" json:"total_price_cny,omitempty"`
	StartTime             uint64                           `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime               uint64                           `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Remark                string                           `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark,omitempty"`
	PresentList           []*IllustrationPresentConfig     `protobuf:"bytes,9,rep,name=present_list,json=presentList,proto3" json:"present_list,omitempty"`
	Sort                  uint32                           `protobuf:"varint,10,opt,name=sort,proto3" json:"sort,omitempty"`
	EncoreStartTime       uint64                           `protobuf:"varint,11,opt,name=encore_start_time,json=encoreStartTime,proto3" json:"encore_start_time,omitempty"`
	EncoreEndTime         uint64                           `protobuf:"varint,12,opt,name=encore_end_time,json=encoreEndTime,proto3" json:"encore_end_time,omitempty"`
	Operator              string                           `protobuf:"bytes,13,opt,name=operator,proto3" json:"operator,omitempty"`
	HistoryEncoreTimeList []*IllustrationConfig_EncoreTime `protobuf:"bytes,14,rep,name=history_encore_time_list,json=historyEncoreTimeList,proto3" json:"history_encore_time_list,omitempty"`
	SourceNote            string                           `protobuf:"bytes,15,opt,name=source_note,json=sourceNote,proto3" json:"source_note,omitempty"`
	SourceJumpConfig      []*PackageJumpConfig             `protobuf:"bytes,16,rep,name=source_jump_config,json=sourceJumpConfig,proto3" json:"source_jump_config,omitempty"`
	OriginStartTime       uint64                           `protobuf:"varint,17,opt,name=origin_start_time,json=originStartTime,proto3" json:"origin_start_time,omitempty"`
	OriginEndTime         uint64                           `protobuf:"varint,18,opt,name=origin_end_time,json=originEndTime,proto3" json:"origin_end_time,omitempty"`
	IsEncore              bool                             `protobuf:"varint,19,opt,name=is_encore,json=isEncore,proto3" json:"is_encore,omitempty"`
	CreateTime            uint64                           `protobuf:"varint,20,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Status                IllustrationStatus               `protobuf:"varint,21,opt,name=status,proto3,enum=present_illustration.IllustrationStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                         `json:"-"`
	XXX_unrecognized      []byte                           `json:"-"`
	XXX_sizecache         int32                            `json:"-"`
}

func (m *IllustrationConfig) Reset()         { *m = IllustrationConfig{} }
func (m *IllustrationConfig) String() string { return proto.CompactTextString(m) }
func (*IllustrationConfig) ProtoMessage()    {}
func (*IllustrationConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{1}
}
func (m *IllustrationConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IllustrationConfig.Unmarshal(m, b)
}
func (m *IllustrationConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IllustrationConfig.Marshal(b, m, deterministic)
}
func (dst *IllustrationConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IllustrationConfig.Merge(dst, src)
}
func (m *IllustrationConfig) XXX_Size() int {
	return xxx_messageInfo_IllustrationConfig.Size(m)
}
func (m *IllustrationConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_IllustrationConfig.DiscardUnknown(m)
}

var xxx_messageInfo_IllustrationConfig proto.InternalMessageInfo

func (m *IllustrationConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *IllustrationConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *IllustrationConfig) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *IllustrationConfig) GetTotalPrice() uint64 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *IllustrationConfig) GetTotalPriceCny() string {
	if m != nil {
		return m.TotalPriceCny
	}
	return ""
}

func (m *IllustrationConfig) GetStartTime() uint64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *IllustrationConfig) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *IllustrationConfig) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *IllustrationConfig) GetPresentList() []*IllustrationPresentConfig {
	if m != nil {
		return m.PresentList
	}
	return nil
}

func (m *IllustrationConfig) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *IllustrationConfig) GetEncoreStartTime() uint64 {
	if m != nil {
		return m.EncoreStartTime
	}
	return 0
}

func (m *IllustrationConfig) GetEncoreEndTime() uint64 {
	if m != nil {
		return m.EncoreEndTime
	}
	return 0
}

func (m *IllustrationConfig) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *IllustrationConfig) GetHistoryEncoreTimeList() []*IllustrationConfig_EncoreTime {
	if m != nil {
		return m.HistoryEncoreTimeList
	}
	return nil
}

func (m *IllustrationConfig) GetSourceNote() string {
	if m != nil {
		return m.SourceNote
	}
	return ""
}

func (m *IllustrationConfig) GetSourceJumpConfig() []*PackageJumpConfig {
	if m != nil {
		return m.SourceJumpConfig
	}
	return nil
}

func (m *IllustrationConfig) GetOriginStartTime() uint64 {
	if m != nil {
		return m.OriginStartTime
	}
	return 0
}

func (m *IllustrationConfig) GetOriginEndTime() uint64 {
	if m != nil {
		return m.OriginEndTime
	}
	return 0
}

func (m *IllustrationConfig) GetIsEncore() bool {
	if m != nil {
		return m.IsEncore
	}
	return false
}

func (m *IllustrationConfig) GetCreateTime() uint64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *IllustrationConfig) GetStatus() IllustrationStatus {
	if m != nil {
		return m.Status
	}
	return IllustrationStatus_ILLUSTRATION_STATUS_UNSPECIFIED
}

// 返场时间
type IllustrationConfig_EncoreTime struct {
	StartTime            uint64   `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint64   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IllustrationConfig_EncoreTime) Reset()         { *m = IllustrationConfig_EncoreTime{} }
func (m *IllustrationConfig_EncoreTime) String() string { return proto.CompactTextString(m) }
func (*IllustrationConfig_EncoreTime) ProtoMessage()    {}
func (*IllustrationConfig_EncoreTime) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{1, 0}
}
func (m *IllustrationConfig_EncoreTime) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IllustrationConfig_EncoreTime.Unmarshal(m, b)
}
func (m *IllustrationConfig_EncoreTime) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IllustrationConfig_EncoreTime.Marshal(b, m, deterministic)
}
func (dst *IllustrationConfig_EncoreTime) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IllustrationConfig_EncoreTime.Merge(dst, src)
}
func (m *IllustrationConfig_EncoreTime) XXX_Size() int {
	return xxx_messageInfo_IllustrationConfig_EncoreTime.Size(m)
}
func (m *IllustrationConfig_EncoreTime) XXX_DiscardUnknown() {
	xxx_messageInfo_IllustrationConfig_EncoreTime.DiscardUnknown(m)
}

var xxx_messageInfo_IllustrationConfig_EncoreTime proto.InternalMessageInfo

func (m *IllustrationConfig_EncoreTime) GetStartTime() uint64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *IllustrationConfig_EncoreTime) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// 图鉴礼物配置
type IllustrationPresentConfig struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	PriceCny             uint32   `protobuf:"varint,5,opt,name=price_cny,json=priceCny,proto3" json:"price_cny,omitempty"`
	Sort                 uint32   `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IllustrationPresentConfig) Reset()         { *m = IllustrationPresentConfig{} }
func (m *IllustrationPresentConfig) String() string { return proto.CompactTextString(m) }
func (*IllustrationPresentConfig) ProtoMessage()    {}
func (*IllustrationPresentConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{2}
}
func (m *IllustrationPresentConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IllustrationPresentConfig.Unmarshal(m, b)
}
func (m *IllustrationPresentConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IllustrationPresentConfig.Marshal(b, m, deterministic)
}
func (dst *IllustrationPresentConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IllustrationPresentConfig.Merge(dst, src)
}
func (m *IllustrationPresentConfig) XXX_Size() int {
	return xxx_messageInfo_IllustrationPresentConfig.Size(m)
}
func (m *IllustrationPresentConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_IllustrationPresentConfig.DiscardUnknown(m)
}

var xxx_messageInfo_IllustrationPresentConfig proto.InternalMessageInfo

func (m *IllustrationPresentConfig) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *IllustrationPresentConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *IllustrationPresentConfig) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *IllustrationPresentConfig) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *IllustrationPresentConfig) GetPriceCny() uint32 {
	if m != nil {
		return m.PriceCny
	}
	return 0
}

func (m *IllustrationPresentConfig) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

type GetIllustrationConfigListReq struct {
	Offset               uint32                                 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32                                 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Id                   string                                 `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Remark               string                                 `protobuf:"bytes,5,opt,name=remark,proto3" json:"remark,omitempty"`
	Status               IllustrationStatus                     `protobuf:"varint,6,opt,name=status,proto3,enum=present_illustration.IllustrationStatus" json:"status,omitempty"`
	Sort                 GetIllustrationConfigListReq_SortType  `protobuf:"varint,7,opt,name=sort,proto3,enum=present_illustration.GetIllustrationConfigListReq_SortType" json:"sort,omitempty"`
	Order                GetIllustrationConfigListReq_SortOrder `protobuf:"varint,8,opt,name=order,proto3,enum=present_illustration.GetIllustrationConfigListReq_SortOrder" json:"order,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *GetIllustrationConfigListReq) Reset()         { *m = GetIllustrationConfigListReq{} }
func (m *GetIllustrationConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetIllustrationConfigListReq) ProtoMessage()    {}
func (*GetIllustrationConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{3}
}
func (m *GetIllustrationConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIllustrationConfigListReq.Unmarshal(m, b)
}
func (m *GetIllustrationConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIllustrationConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetIllustrationConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIllustrationConfigListReq.Merge(dst, src)
}
func (m *GetIllustrationConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetIllustrationConfigListReq.Size(m)
}
func (m *GetIllustrationConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIllustrationConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetIllustrationConfigListReq proto.InternalMessageInfo

func (m *GetIllustrationConfigListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetIllustrationConfigListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetIllustrationConfigListReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetIllustrationConfigListReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetIllustrationConfigListReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *GetIllustrationConfigListReq) GetStatus() IllustrationStatus {
	if m != nil {
		return m.Status
	}
	return IllustrationStatus_ILLUSTRATION_STATUS_UNSPECIFIED
}

func (m *GetIllustrationConfigListReq) GetSort() GetIllustrationConfigListReq_SortType {
	if m != nil {
		return m.Sort
	}
	return GetIllustrationConfigListReq_SORT_TYPE_UNSPECIFIED
}

func (m *GetIllustrationConfigListReq) GetOrder() GetIllustrationConfigListReq_SortOrder {
	if m != nil {
		return m.Order
	}
	return GetIllustrationConfigListReq_SORT_ORDER_UNSPECIFIED
}

type GetIllustrationConfigListResp struct {
	List                 []*IllustrationConfig `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetIllustrationConfigListResp) Reset()         { *m = GetIllustrationConfigListResp{} }
func (m *GetIllustrationConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetIllustrationConfigListResp) ProtoMessage()    {}
func (*GetIllustrationConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{4}
}
func (m *GetIllustrationConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIllustrationConfigListResp.Unmarshal(m, b)
}
func (m *GetIllustrationConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIllustrationConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetIllustrationConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIllustrationConfigListResp.Merge(dst, src)
}
func (m *GetIllustrationConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetIllustrationConfigListResp.Size(m)
}
func (m *GetIllustrationConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIllustrationConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetIllustrationConfigListResp proto.InternalMessageInfo

func (m *GetIllustrationConfigListResp) GetList() []*IllustrationConfig {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetIllustrationConfigListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetIllustrationConfigReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIllustrationConfigReq) Reset()         { *m = GetIllustrationConfigReq{} }
func (m *GetIllustrationConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetIllustrationConfigReq) ProtoMessage()    {}
func (*GetIllustrationConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{5}
}
func (m *GetIllustrationConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIllustrationConfigReq.Unmarshal(m, b)
}
func (m *GetIllustrationConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIllustrationConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetIllustrationConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIllustrationConfigReq.Merge(dst, src)
}
func (m *GetIllustrationConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetIllustrationConfigReq.Size(m)
}
func (m *GetIllustrationConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIllustrationConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetIllustrationConfigReq proto.InternalMessageInfo

func (m *GetIllustrationConfigReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetIllustrationConfigResp struct {
	Config               *IllustrationConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetIllustrationConfigResp) Reset()         { *m = GetIllustrationConfigResp{} }
func (m *GetIllustrationConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetIllustrationConfigResp) ProtoMessage()    {}
func (*GetIllustrationConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{6}
}
func (m *GetIllustrationConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIllustrationConfigResp.Unmarshal(m, b)
}
func (m *GetIllustrationConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIllustrationConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetIllustrationConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIllustrationConfigResp.Merge(dst, src)
}
func (m *GetIllustrationConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetIllustrationConfigResp.Size(m)
}
func (m *GetIllustrationConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIllustrationConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetIllustrationConfigResp proto.InternalMessageInfo

func (m *GetIllustrationConfigResp) GetConfig() *IllustrationConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type CreateIllustrationConfigReq struct {
	Config               *IllustrationConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *CreateIllustrationConfigReq) Reset()         { *m = CreateIllustrationConfigReq{} }
func (m *CreateIllustrationConfigReq) String() string { return proto.CompactTextString(m) }
func (*CreateIllustrationConfigReq) ProtoMessage()    {}
func (*CreateIllustrationConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{7}
}
func (m *CreateIllustrationConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateIllustrationConfigReq.Unmarshal(m, b)
}
func (m *CreateIllustrationConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateIllustrationConfigReq.Marshal(b, m, deterministic)
}
func (dst *CreateIllustrationConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateIllustrationConfigReq.Merge(dst, src)
}
func (m *CreateIllustrationConfigReq) XXX_Size() int {
	return xxx_messageInfo_CreateIllustrationConfigReq.Size(m)
}
func (m *CreateIllustrationConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateIllustrationConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateIllustrationConfigReq proto.InternalMessageInfo

func (m *CreateIllustrationConfigReq) GetConfig() *IllustrationConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type CreateIllustrationConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateIllustrationConfigResp) Reset()         { *m = CreateIllustrationConfigResp{} }
func (m *CreateIllustrationConfigResp) String() string { return proto.CompactTextString(m) }
func (*CreateIllustrationConfigResp) ProtoMessage()    {}
func (*CreateIllustrationConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{8}
}
func (m *CreateIllustrationConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateIllustrationConfigResp.Unmarshal(m, b)
}
func (m *CreateIllustrationConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateIllustrationConfigResp.Marshal(b, m, deterministic)
}
func (dst *CreateIllustrationConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateIllustrationConfigResp.Merge(dst, src)
}
func (m *CreateIllustrationConfigResp) XXX_Size() int {
	return xxx_messageInfo_CreateIllustrationConfigResp.Size(m)
}
func (m *CreateIllustrationConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateIllustrationConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateIllustrationConfigResp proto.InternalMessageInfo

type DeleteIllustrationConfigReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteIllustrationConfigReq) Reset()         { *m = DeleteIllustrationConfigReq{} }
func (m *DeleteIllustrationConfigReq) String() string { return proto.CompactTextString(m) }
func (*DeleteIllustrationConfigReq) ProtoMessage()    {}
func (*DeleteIllustrationConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{9}
}
func (m *DeleteIllustrationConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteIllustrationConfigReq.Unmarshal(m, b)
}
func (m *DeleteIllustrationConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteIllustrationConfigReq.Marshal(b, m, deterministic)
}
func (dst *DeleteIllustrationConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteIllustrationConfigReq.Merge(dst, src)
}
func (m *DeleteIllustrationConfigReq) XXX_Size() int {
	return xxx_messageInfo_DeleteIllustrationConfigReq.Size(m)
}
func (m *DeleteIllustrationConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteIllustrationConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteIllustrationConfigReq proto.InternalMessageInfo

func (m *DeleteIllustrationConfigReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteIllustrationConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteIllustrationConfigResp) Reset()         { *m = DeleteIllustrationConfigResp{} }
func (m *DeleteIllustrationConfigResp) String() string { return proto.CompactTextString(m) }
func (*DeleteIllustrationConfigResp) ProtoMessage()    {}
func (*DeleteIllustrationConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{10}
}
func (m *DeleteIllustrationConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteIllustrationConfigResp.Unmarshal(m, b)
}
func (m *DeleteIllustrationConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteIllustrationConfigResp.Marshal(b, m, deterministic)
}
func (dst *DeleteIllustrationConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteIllustrationConfigResp.Merge(dst, src)
}
func (m *DeleteIllustrationConfigResp) XXX_Size() int {
	return xxx_messageInfo_DeleteIllustrationConfigResp.Size(m)
}
func (m *DeleteIllustrationConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteIllustrationConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteIllustrationConfigResp proto.InternalMessageInfo

type CheckIllustrationConfigSortReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Sort                 uint32   `protobuf:"varint,2,opt,name=sort,proto3" json:"sort,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIllustrationConfigSortReq) Reset()         { *m = CheckIllustrationConfigSortReq{} }
func (m *CheckIllustrationConfigSortReq) String() string { return proto.CompactTextString(m) }
func (*CheckIllustrationConfigSortReq) ProtoMessage()    {}
func (*CheckIllustrationConfigSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{11}
}
func (m *CheckIllustrationConfigSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIllustrationConfigSortReq.Unmarshal(m, b)
}
func (m *CheckIllustrationConfigSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIllustrationConfigSortReq.Marshal(b, m, deterministic)
}
func (dst *CheckIllustrationConfigSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIllustrationConfigSortReq.Merge(dst, src)
}
func (m *CheckIllustrationConfigSortReq) XXX_Size() int {
	return xxx_messageInfo_CheckIllustrationConfigSortReq.Size(m)
}
func (m *CheckIllustrationConfigSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIllustrationConfigSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIllustrationConfigSortReq proto.InternalMessageInfo

func (m *CheckIllustrationConfigSortReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CheckIllustrationConfigSortReq) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

type CheckIllustrationConfigSortResp struct {
	IsRepeat             bool                `protobuf:"varint,1,opt,name=is_repeat,json=isRepeat,proto3" json:"is_repeat,omitempty"`
	Config               *IllustrationConfig `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *CheckIllustrationConfigSortResp) Reset()         { *m = CheckIllustrationConfigSortResp{} }
func (m *CheckIllustrationConfigSortResp) String() string { return proto.CompactTextString(m) }
func (*CheckIllustrationConfigSortResp) ProtoMessage()    {}
func (*CheckIllustrationConfigSortResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{12}
}
func (m *CheckIllustrationConfigSortResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIllustrationConfigSortResp.Unmarshal(m, b)
}
func (m *CheckIllustrationConfigSortResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIllustrationConfigSortResp.Marshal(b, m, deterministic)
}
func (dst *CheckIllustrationConfigSortResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIllustrationConfigSortResp.Merge(dst, src)
}
func (m *CheckIllustrationConfigSortResp) XXX_Size() int {
	return xxx_messageInfo_CheckIllustrationConfigSortResp.Size(m)
}
func (m *CheckIllustrationConfigSortResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIllustrationConfigSortResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIllustrationConfigSortResp proto.InternalMessageInfo

func (m *CheckIllustrationConfigSortResp) GetIsRepeat() bool {
	if m != nil {
		return m.IsRepeat
	}
	return false
}

func (m *CheckIllustrationConfigSortResp) GetConfig() *IllustrationConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

// 图鉴摘要信息
type SummaryTopInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SummaryTopInfo) Reset()         { *m = SummaryTopInfo{} }
func (m *SummaryTopInfo) String() string { return proto.CompactTextString(m) }
func (*SummaryTopInfo) ProtoMessage()    {}
func (*SummaryTopInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{13}
}
func (m *SummaryTopInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SummaryTopInfo.Unmarshal(m, b)
}
func (m *SummaryTopInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SummaryTopInfo.Marshal(b, m, deterministic)
}
func (dst *SummaryTopInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SummaryTopInfo.Merge(dst, src)
}
func (m *SummaryTopInfo) XXX_Size() int {
	return xxx_messageInfo_SummaryTopInfo.Size(m)
}
func (m *SummaryTopInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SummaryTopInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SummaryTopInfo proto.InternalMessageInfo

func (m *SummaryTopInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SummaryTopInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 获取礼物图鉴摘要
type GetIllustrationSummaryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIllustrationSummaryReq) Reset()         { *m = GetIllustrationSummaryReq{} }
func (m *GetIllustrationSummaryReq) String() string { return proto.CompactTextString(m) }
func (*GetIllustrationSummaryReq) ProtoMessage()    {}
func (*GetIllustrationSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{14}
}
func (m *GetIllustrationSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIllustrationSummaryReq.Unmarshal(m, b)
}
func (m *GetIllustrationSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIllustrationSummaryReq.Marshal(b, m, deterministic)
}
func (dst *GetIllustrationSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIllustrationSummaryReq.Merge(dst, src)
}
func (m *GetIllustrationSummaryReq) XXX_Size() int {
	return xxx_messageInfo_GetIllustrationSummaryReq.Size(m)
}
func (m *GetIllustrationSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIllustrationSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetIllustrationSummaryReq proto.InternalMessageInfo

func (m *GetIllustrationSummaryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetIllustrationSummaryRsp struct {
	CollectedTotal       uint32            `protobuf:"varint,1,opt,name=collected_total,json=collectedTotal,proto3" json:"collected_total,omitempty"`
	NotCollectTotal      uint32            `protobuf:"varint,2,opt,name=not_collect_total,json=notCollectTotal,proto3" json:"not_collect_total,omitempty"`
	TopList              []*SummaryTopInfo `protobuf:"bytes,3,rep,name=top_list,json=topList,proto3" json:"top_list,omitempty"`
	HasNew               bool              `protobuf:"varint,4,opt,name=has_new,json=hasNew,proto3" json:"has_new,omitempty"`
	RedDot               bool              `protobuf:"varint,5,opt,name=red_dot,json=redDot,proto3" json:"red_dot,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetIllustrationSummaryRsp) Reset()         { *m = GetIllustrationSummaryRsp{} }
func (m *GetIllustrationSummaryRsp) String() string { return proto.CompactTextString(m) }
func (*GetIllustrationSummaryRsp) ProtoMessage()    {}
func (*GetIllustrationSummaryRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{15}
}
func (m *GetIllustrationSummaryRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIllustrationSummaryRsp.Unmarshal(m, b)
}
func (m *GetIllustrationSummaryRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIllustrationSummaryRsp.Marshal(b, m, deterministic)
}
func (dst *GetIllustrationSummaryRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIllustrationSummaryRsp.Merge(dst, src)
}
func (m *GetIllustrationSummaryRsp) XXX_Size() int {
	return xxx_messageInfo_GetIllustrationSummaryRsp.Size(m)
}
func (m *GetIllustrationSummaryRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIllustrationSummaryRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetIllustrationSummaryRsp proto.InternalMessageInfo

func (m *GetIllustrationSummaryRsp) GetCollectedTotal() uint32 {
	if m != nil {
		return m.CollectedTotal
	}
	return 0
}

func (m *GetIllustrationSummaryRsp) GetNotCollectTotal() uint32 {
	if m != nil {
		return m.NotCollectTotal
	}
	return 0
}

func (m *GetIllustrationSummaryRsp) GetTopList() []*SummaryTopInfo {
	if m != nil {
		return m.TopList
	}
	return nil
}

func (m *GetIllustrationSummaryRsp) GetHasNew() bool {
	if m != nil {
		return m.HasNew
	}
	return false
}

func (m *GetIllustrationSummaryRsp) GetRedDot() bool {
	if m != nil {
		return m.RedDot
	}
	return false
}

// 图鉴信息
type IllustrationInfo struct {
	Id                   uint32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Level                uint32                  `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	Status               uint32                  `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	Progress             uint32                  `protobuf:"varint,4,opt,name=progress,proto3" json:"progress,omitempty"`
	Value                uint64                  `protobuf:"varint,5,opt,name=value,proto3" json:"value,omitempty"`
	HasNew               bool                    `protobuf:"varint,6,opt,name=has_new,json=hasNew,proto3" json:"has_new,omitempty"`
	RedDot               IllustrationInfoRedType `protobuf:"varint,7,opt,name=red_dot,json=redDot,proto3,enum=present_illustration.IllustrationInfoRedType" json:"red_dot,omitempty"`
	TotalGiftId          uint32                  `protobuf:"varint,8,opt,name=total_gift_id,json=totalGiftId,proto3" json:"total_gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *IllustrationInfo) Reset()         { *m = IllustrationInfo{} }
func (m *IllustrationInfo) String() string { return proto.CompactTextString(m) }
func (*IllustrationInfo) ProtoMessage()    {}
func (*IllustrationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{16}
}
func (m *IllustrationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IllustrationInfo.Unmarshal(m, b)
}
func (m *IllustrationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IllustrationInfo.Marshal(b, m, deterministic)
}
func (dst *IllustrationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IllustrationInfo.Merge(dst, src)
}
func (m *IllustrationInfo) XXX_Size() int {
	return xxx_messageInfo_IllustrationInfo.Size(m)
}
func (m *IllustrationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_IllustrationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_IllustrationInfo proto.InternalMessageInfo

func (m *IllustrationInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *IllustrationInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *IllustrationInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *IllustrationInfo) GetProgress() uint32 {
	if m != nil {
		return m.Progress
	}
	return 0
}

func (m *IllustrationInfo) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *IllustrationInfo) GetHasNew() bool {
	if m != nil {
		return m.HasNew
	}
	return false
}

func (m *IllustrationInfo) GetRedDot() IllustrationInfoRedType {
	if m != nil {
		return m.RedDot
	}
	return IllustrationInfoRedType_Unknow
}

func (m *IllustrationInfo) GetTotalGiftId() uint32 {
	if m != nil {
		return m.TotalGiftId
	}
	return 0
}

// 获取用户所有图鉴信息
type GetIllustrationListAllReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ClearRed             bool     `protobuf:"varint,3,opt,name=clear_red,json=clearRed,proto3" json:"clear_red,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIllustrationListAllReq) Reset()         { *m = GetIllustrationListAllReq{} }
func (m *GetIllustrationListAllReq) String() string { return proto.CompactTextString(m) }
func (*GetIllustrationListAllReq) ProtoMessage()    {}
func (*GetIllustrationListAllReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{17}
}
func (m *GetIllustrationListAllReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIllustrationListAllReq.Unmarshal(m, b)
}
func (m *GetIllustrationListAllReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIllustrationListAllReq.Marshal(b, m, deterministic)
}
func (dst *GetIllustrationListAllReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIllustrationListAllReq.Merge(dst, src)
}
func (m *GetIllustrationListAllReq) XXX_Size() int {
	return xxx_messageInfo_GetIllustrationListAllReq.Size(m)
}
func (m *GetIllustrationListAllReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIllustrationListAllReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetIllustrationListAllReq proto.InternalMessageInfo

func (m *GetIllustrationListAllReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetIllustrationListAllReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetIllustrationListAllReq) GetClearRed() bool {
	if m != nil {
		return m.ClearRed
	}
	return false
}

type GetIllustrationListAllRsp struct {
	List                 []*IllustrationInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetIllustrationListAllRsp) Reset()         { *m = GetIllustrationListAllRsp{} }
func (m *GetIllustrationListAllRsp) String() string { return proto.CompactTextString(m) }
func (*GetIllustrationListAllRsp) ProtoMessage()    {}
func (*GetIllustrationListAllRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{18}
}
func (m *GetIllustrationListAllRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIllustrationListAllRsp.Unmarshal(m, b)
}
func (m *GetIllustrationListAllRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIllustrationListAllRsp.Marshal(b, m, deterministic)
}
func (dst *GetIllustrationListAllRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIllustrationListAllRsp.Merge(dst, src)
}
func (m *GetIllustrationListAllRsp) XXX_Size() int {
	return xxx_messageInfo_GetIllustrationListAllRsp.Size(m)
}
func (m *GetIllustrationListAllRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIllustrationListAllRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetIllustrationListAllRsp proto.InternalMessageInfo

func (m *GetIllustrationListAllRsp) GetList() []*IllustrationInfo {
	if m != nil {
		return m.List
	}
	return nil
}

// 图鉴礼物信息
type IllustrationGiftInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	ReceivedNum          uint32   `protobuf:"varint,2,opt,name=received_num,json=receivedNum,proto3" json:"received_num,omitempty"`
	ReceivedPrice        uint64   `protobuf:"varint,3,opt,name=received_price,json=receivedPrice,proto3" json:"received_price,omitempty"`
	PriceType            uint32   `protobuf:"varint,4,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IllustrationGiftInfo) Reset()         { *m = IllustrationGiftInfo{} }
func (m *IllustrationGiftInfo) String() string { return proto.CompactTextString(m) }
func (*IllustrationGiftInfo) ProtoMessage()    {}
func (*IllustrationGiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{19}
}
func (m *IllustrationGiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IllustrationGiftInfo.Unmarshal(m, b)
}
func (m *IllustrationGiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IllustrationGiftInfo.Marshal(b, m, deterministic)
}
func (dst *IllustrationGiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IllustrationGiftInfo.Merge(dst, src)
}
func (m *IllustrationGiftInfo) XXX_Size() int {
	return xxx_messageInfo_IllustrationGiftInfo.Size(m)
}
func (m *IllustrationGiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_IllustrationGiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_IllustrationGiftInfo proto.InternalMessageInfo

func (m *IllustrationGiftInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *IllustrationGiftInfo) GetReceivedNum() uint32 {
	if m != nil {
		return m.ReceivedNum
	}
	return 0
}

func (m *IllustrationGiftInfo) GetReceivedPrice() uint64 {
	if m != nil {
		return m.ReceivedPrice
	}
	return 0
}

func (m *IllustrationGiftInfo) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

// 获取图鉴详情信息
type GetIllustrationDetailReq struct {
	IllustrationId       uint32   `protobuf:"varint,1,opt,name=illustration_id,json=illustrationId,proto3" json:"illustration_id,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIllustrationDetailReq) Reset()         { *m = GetIllustrationDetailReq{} }
func (m *GetIllustrationDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetIllustrationDetailReq) ProtoMessage()    {}
func (*GetIllustrationDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{20}
}
func (m *GetIllustrationDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIllustrationDetailReq.Unmarshal(m, b)
}
func (m *GetIllustrationDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIllustrationDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetIllustrationDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIllustrationDetailReq.Merge(dst, src)
}
func (m *GetIllustrationDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetIllustrationDetailReq.Size(m)
}
func (m *GetIllustrationDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIllustrationDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetIllustrationDetailReq proto.InternalMessageInfo

func (m *GetIllustrationDetailReq) GetIllustrationId() uint32 {
	if m != nil {
		return m.IllustrationId
	}
	return 0
}

func (m *GetIllustrationDetailReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetIllustrationDetailRsp struct {
	BaseInfo             *IllustrationInfo       `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	GiftList             []*IllustrationGiftInfo `protobuf:"bytes,2,rep,name=gift_list,json=giftList,proto3" json:"gift_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetIllustrationDetailRsp) Reset()         { *m = GetIllustrationDetailRsp{} }
func (m *GetIllustrationDetailRsp) String() string { return proto.CompactTextString(m) }
func (*GetIllustrationDetailRsp) ProtoMessage()    {}
func (*GetIllustrationDetailRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{21}
}
func (m *GetIllustrationDetailRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIllustrationDetailRsp.Unmarshal(m, b)
}
func (m *GetIllustrationDetailRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIllustrationDetailRsp.Marshal(b, m, deterministic)
}
func (dst *GetIllustrationDetailRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIllustrationDetailRsp.Merge(dst, src)
}
func (m *GetIllustrationDetailRsp) XXX_Size() int {
	return xxx_messageInfo_GetIllustrationDetailRsp.Size(m)
}
func (m *GetIllustrationDetailRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIllustrationDetailRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetIllustrationDetailRsp proto.InternalMessageInfo

func (m *GetIllustrationDetailRsp) GetBaseInfo() *IllustrationInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *GetIllustrationDetailRsp) GetGiftList() []*IllustrationGiftInfo {
	if m != nil {
		return m.GiftList
	}
	return nil
}

// 获取用户开关
type GetIllustrationSwitchReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIllustrationSwitchReq) Reset()         { *m = GetIllustrationSwitchReq{} }
func (m *GetIllustrationSwitchReq) String() string { return proto.CompactTextString(m) }
func (*GetIllustrationSwitchReq) ProtoMessage()    {}
func (*GetIllustrationSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{22}
}
func (m *GetIllustrationSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIllustrationSwitchReq.Unmarshal(m, b)
}
func (m *GetIllustrationSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIllustrationSwitchReq.Marshal(b, m, deterministic)
}
func (dst *GetIllustrationSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIllustrationSwitchReq.Merge(dst, src)
}
func (m *GetIllustrationSwitchReq) XXX_Size() int {
	return xxx_messageInfo_GetIllustrationSwitchReq.Size(m)
}
func (m *GetIllustrationSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIllustrationSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetIllustrationSwitchReq proto.InternalMessageInfo

func (m *GetIllustrationSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetIllustrationSwitchRsp struct {
	Switch               bool     `protobuf:"varint,1,opt,name=switch,proto3" json:"switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIllustrationSwitchRsp) Reset()         { *m = GetIllustrationSwitchRsp{} }
func (m *GetIllustrationSwitchRsp) String() string { return proto.CompactTextString(m) }
func (*GetIllustrationSwitchRsp) ProtoMessage()    {}
func (*GetIllustrationSwitchRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{23}
}
func (m *GetIllustrationSwitchRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIllustrationSwitchRsp.Unmarshal(m, b)
}
func (m *GetIllustrationSwitchRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIllustrationSwitchRsp.Marshal(b, m, deterministic)
}
func (dst *GetIllustrationSwitchRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIllustrationSwitchRsp.Merge(dst, src)
}
func (m *GetIllustrationSwitchRsp) XXX_Size() int {
	return xxx_messageInfo_GetIllustrationSwitchRsp.Size(m)
}
func (m *GetIllustrationSwitchRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIllustrationSwitchRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetIllustrationSwitchRsp proto.InternalMessageInfo

func (m *GetIllustrationSwitchRsp) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

type FixPresentIllustrationEventReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	SendTime             uint32   `protobuf:"varint,3,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	GiftId               uint32   `protobuf:"varint,4,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftNum              uint32   `protobuf:"varint,5,opt,name=gift_num,json=giftNum,proto3" json:"gift_num,omitempty"`
	PriceType            uint32   `protobuf:"varint,6,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	Price                uint32   `protobuf:"varint,7,opt,name=price,proto3" json:"price,omitempty"`
	OrderId              string   `protobuf:"bytes,8,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FixPresentIllustrationEventReq) Reset()         { *m = FixPresentIllustrationEventReq{} }
func (m *FixPresentIllustrationEventReq) String() string { return proto.CompactTextString(m) }
func (*FixPresentIllustrationEventReq) ProtoMessage()    {}
func (*FixPresentIllustrationEventReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{24}
}
func (m *FixPresentIllustrationEventReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FixPresentIllustrationEventReq.Unmarshal(m, b)
}
func (m *FixPresentIllustrationEventReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FixPresentIllustrationEventReq.Marshal(b, m, deterministic)
}
func (dst *FixPresentIllustrationEventReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FixPresentIllustrationEventReq.Merge(dst, src)
}
func (m *FixPresentIllustrationEventReq) XXX_Size() int {
	return xxx_messageInfo_FixPresentIllustrationEventReq.Size(m)
}
func (m *FixPresentIllustrationEventReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FixPresentIllustrationEventReq.DiscardUnknown(m)
}

var xxx_messageInfo_FixPresentIllustrationEventReq proto.InternalMessageInfo

func (m *FixPresentIllustrationEventReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FixPresentIllustrationEventReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *FixPresentIllustrationEventReq) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *FixPresentIllustrationEventReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *FixPresentIllustrationEventReq) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

func (m *FixPresentIllustrationEventReq) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *FixPresentIllustrationEventReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *FixPresentIllustrationEventReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type FixPresentIllustrationEventRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FixPresentIllustrationEventRsp) Reset()         { *m = FixPresentIllustrationEventRsp{} }
func (m *FixPresentIllustrationEventRsp) String() string { return proto.CompactTextString(m) }
func (*FixPresentIllustrationEventRsp) ProtoMessage()    {}
func (*FixPresentIllustrationEventRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{25}
}
func (m *FixPresentIllustrationEventRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FixPresentIllustrationEventRsp.Unmarshal(m, b)
}
func (m *FixPresentIllustrationEventRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FixPresentIllustrationEventRsp.Marshal(b, m, deterministic)
}
func (dst *FixPresentIllustrationEventRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FixPresentIllustrationEventRsp.Merge(dst, src)
}
func (m *FixPresentIllustrationEventRsp) XXX_Size() int {
	return xxx_messageInfo_FixPresentIllustrationEventRsp.Size(m)
}
func (m *FixPresentIllustrationEventRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_FixPresentIllustrationEventRsp.DiscardUnknown(m)
}

var xxx_messageInfo_FixPresentIllustrationEventRsp proto.InternalMessageInfo

type CleanUserIllustrationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CleanUserIllustrationReq) Reset()         { *m = CleanUserIllustrationReq{} }
func (m *CleanUserIllustrationReq) String() string { return proto.CompactTextString(m) }
func (*CleanUserIllustrationReq) ProtoMessage()    {}
func (*CleanUserIllustrationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{26}
}
func (m *CleanUserIllustrationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CleanUserIllustrationReq.Unmarshal(m, b)
}
func (m *CleanUserIllustrationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CleanUserIllustrationReq.Marshal(b, m, deterministic)
}
func (dst *CleanUserIllustrationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CleanUserIllustrationReq.Merge(dst, src)
}
func (m *CleanUserIllustrationReq) XXX_Size() int {
	return xxx_messageInfo_CleanUserIllustrationReq.Size(m)
}
func (m *CleanUserIllustrationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CleanUserIllustrationReq.DiscardUnknown(m)
}

var xxx_messageInfo_CleanUserIllustrationReq proto.InternalMessageInfo

func (m *CleanUserIllustrationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CleanUserIllustrationRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CleanUserIllustrationRsp) Reset()         { *m = CleanUserIllustrationRsp{} }
func (m *CleanUserIllustrationRsp) String() string { return proto.CompactTextString(m) }
func (*CleanUserIllustrationRsp) ProtoMessage()    {}
func (*CleanUserIllustrationRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{27}
}
func (m *CleanUserIllustrationRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CleanUserIllustrationRsp.Unmarshal(m, b)
}
func (m *CleanUserIllustrationRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CleanUserIllustrationRsp.Marshal(b, m, deterministic)
}
func (dst *CleanUserIllustrationRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CleanUserIllustrationRsp.Merge(dst, src)
}
func (m *CleanUserIllustrationRsp) XXX_Size() int {
	return xxx_messageInfo_CleanUserIllustrationRsp.Size(m)
}
func (m *CleanUserIllustrationRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CleanUserIllustrationRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CleanUserIllustrationRsp proto.InternalMessageInfo

type GetUserSampleRedpointReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSampleRedpointReq) Reset()         { *m = GetUserSampleRedpointReq{} }
func (m *GetUserSampleRedpointReq) String() string { return proto.CompactTextString(m) }
func (*GetUserSampleRedpointReq) ProtoMessage()    {}
func (*GetUserSampleRedpointReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{28}
}
func (m *GetUserSampleRedpointReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSampleRedpointReq.Unmarshal(m, b)
}
func (m *GetUserSampleRedpointReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSampleRedpointReq.Marshal(b, m, deterministic)
}
func (dst *GetUserSampleRedpointReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSampleRedpointReq.Merge(dst, src)
}
func (m *GetUserSampleRedpointReq) XXX_Size() int {
	return xxx_messageInfo_GetUserSampleRedpointReq.Size(m)
}
func (m *GetUserSampleRedpointReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSampleRedpointReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSampleRedpointReq proto.InternalMessageInfo

func (m *GetUserSampleRedpointReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserSampleRedpointRsp struct {
	Redpoint             bool     `protobuf:"varint,2,opt,name=redpoint,proto3" json:"redpoint,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSampleRedpointRsp) Reset()         { *m = GetUserSampleRedpointRsp{} }
func (m *GetUserSampleRedpointRsp) String() string { return proto.CompactTextString(m) }
func (*GetUserSampleRedpointRsp) ProtoMessage()    {}
func (*GetUserSampleRedpointRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_illustration_0b85e00117326e78, []int{29}
}
func (m *GetUserSampleRedpointRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSampleRedpointRsp.Unmarshal(m, b)
}
func (m *GetUserSampleRedpointRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSampleRedpointRsp.Marshal(b, m, deterministic)
}
func (dst *GetUserSampleRedpointRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSampleRedpointRsp.Merge(dst, src)
}
func (m *GetUserSampleRedpointRsp) XXX_Size() int {
	return xxx_messageInfo_GetUserSampleRedpointRsp.Size(m)
}
func (m *GetUserSampleRedpointRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSampleRedpointRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSampleRedpointRsp proto.InternalMessageInfo

func (m *GetUserSampleRedpointRsp) GetRedpoint() bool {
	if m != nil {
		return m.Redpoint
	}
	return false
}

func init() {
	proto.RegisterType((*PackageJumpConfig)(nil), "present_illustration.PackageJumpConfig")
	proto.RegisterType((*IllustrationConfig)(nil), "present_illustration.IllustrationConfig")
	proto.RegisterType((*IllustrationConfig_EncoreTime)(nil), "present_illustration.IllustrationConfig.EncoreTime")
	proto.RegisterType((*IllustrationPresentConfig)(nil), "present_illustration.IllustrationPresentConfig")
	proto.RegisterType((*GetIllustrationConfigListReq)(nil), "present_illustration.GetIllustrationConfigListReq")
	proto.RegisterType((*GetIllustrationConfigListResp)(nil), "present_illustration.GetIllustrationConfigListResp")
	proto.RegisterType((*GetIllustrationConfigReq)(nil), "present_illustration.GetIllustrationConfigReq")
	proto.RegisterType((*GetIllustrationConfigResp)(nil), "present_illustration.GetIllustrationConfigResp")
	proto.RegisterType((*CreateIllustrationConfigReq)(nil), "present_illustration.CreateIllustrationConfigReq")
	proto.RegisterType((*CreateIllustrationConfigResp)(nil), "present_illustration.CreateIllustrationConfigResp")
	proto.RegisterType((*DeleteIllustrationConfigReq)(nil), "present_illustration.DeleteIllustrationConfigReq")
	proto.RegisterType((*DeleteIllustrationConfigResp)(nil), "present_illustration.DeleteIllustrationConfigResp")
	proto.RegisterType((*CheckIllustrationConfigSortReq)(nil), "present_illustration.CheckIllustrationConfigSortReq")
	proto.RegisterType((*CheckIllustrationConfigSortResp)(nil), "present_illustration.CheckIllustrationConfigSortResp")
	proto.RegisterType((*SummaryTopInfo)(nil), "present_illustration.SummaryTopInfo")
	proto.RegisterType((*GetIllustrationSummaryReq)(nil), "present_illustration.GetIllustrationSummaryReq")
	proto.RegisterType((*GetIllustrationSummaryRsp)(nil), "present_illustration.GetIllustrationSummaryRsp")
	proto.RegisterType((*IllustrationInfo)(nil), "present_illustration.IllustrationInfo")
	proto.RegisterType((*GetIllustrationListAllReq)(nil), "present_illustration.GetIllustrationListAllReq")
	proto.RegisterType((*GetIllustrationListAllRsp)(nil), "present_illustration.GetIllustrationListAllRsp")
	proto.RegisterType((*IllustrationGiftInfo)(nil), "present_illustration.IllustrationGiftInfo")
	proto.RegisterType((*GetIllustrationDetailReq)(nil), "present_illustration.GetIllustrationDetailReq")
	proto.RegisterType((*GetIllustrationDetailRsp)(nil), "present_illustration.GetIllustrationDetailRsp")
	proto.RegisterType((*GetIllustrationSwitchReq)(nil), "present_illustration.GetIllustrationSwitchReq")
	proto.RegisterType((*GetIllustrationSwitchRsp)(nil), "present_illustration.GetIllustrationSwitchRsp")
	proto.RegisterType((*FixPresentIllustrationEventReq)(nil), "present_illustration.FixPresentIllustrationEventReq")
	proto.RegisterType((*FixPresentIllustrationEventRsp)(nil), "present_illustration.FixPresentIllustrationEventRsp")
	proto.RegisterType((*CleanUserIllustrationReq)(nil), "present_illustration.CleanUserIllustrationReq")
	proto.RegisterType((*CleanUserIllustrationRsp)(nil), "present_illustration.CleanUserIllustrationRsp")
	proto.RegisterType((*GetUserSampleRedpointReq)(nil), "present_illustration.GetUserSampleRedpointReq")
	proto.RegisterType((*GetUserSampleRedpointRsp)(nil), "present_illustration.GetUserSampleRedpointRsp")
	proto.RegisterEnum("present_illustration.IllustrationStatus", IllustrationStatus_name, IllustrationStatus_value)
	proto.RegisterEnum("present_illustration.IllustrationSearchType", IllustrationSearchType_name, IllustrationSearchType_value)
	proto.RegisterEnum("present_illustration.IllustrationInfoRedType", IllustrationInfoRedType_name, IllustrationInfoRedType_value)
	proto.RegisterEnum("present_illustration.GetIllustrationConfigListReq_SortType", GetIllustrationConfigListReq_SortType_name, GetIllustrationConfigListReq_SortType_value)
	proto.RegisterEnum("present_illustration.GetIllustrationConfigListReq_SortOrder", GetIllustrationConfigListReq_SortOrder_name, GetIllustrationConfigListReq_SortOrder_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PresentIllustrationClient is the client API for PresentIllustration service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PresentIllustrationClient interface {
	// 获取图鉴配置列表
	GetIllustrationConfigList(ctx context.Context, in *GetIllustrationConfigListReq, opts ...grpc.CallOption) (*GetIllustrationConfigListResp, error)
	// 获取图鉴配置详情
	GetIllustrationConfig(ctx context.Context, in *GetIllustrationConfigReq, opts ...grpc.CallOption) (*GetIllustrationConfigResp, error)
	// 创建图鉴配置
	CreateIllustrationConfig(ctx context.Context, in *CreateIllustrationConfigReq, opts ...grpc.CallOption) (*CreateIllustrationConfigResp, error)
	// 删除图鉴配置
	DeleteIllustrationConfig(ctx context.Context, in *DeleteIllustrationConfigReq, opts ...grpc.CallOption) (*DeleteIllustrationConfigResp, error)
	// 检查排序重复
	CheckIllustrationConfigSort(ctx context.Context, in *CheckIllustrationConfigSortReq, opts ...grpc.CallOption) (*CheckIllustrationConfigSortResp, error)
	// 获取礼物图鉴摘要
	GetIllustrationSummary(ctx context.Context, in *GetIllustrationSummaryReq, opts ...grpc.CallOption) (*GetIllustrationSummaryRsp, error)
	// 获取用户所有图鉴列表
	GetIllustrationListAll(ctx context.Context, in *GetIllustrationListAllReq, opts ...grpc.CallOption) (*GetIllustrationListAllRsp, error)
	// 获取图鉴详情信息
	GetIllustrationDetail(ctx context.Context, in *GetIllustrationDetailReq, opts ...grpc.CallOption) (*GetIllustrationDetailRsp, error)
	// 开关
	GetIllustrationSwitch(ctx context.Context, in *GetIllustrationSwitchReq, opts ...grpc.CallOption) (*GetIllustrationSwitchRsp, error)
	// 定时获取红点的接口
	GetUserSampleRedpoint(ctx context.Context, in *GetUserSampleRedpointReq, opts ...grpc.CallOption) (*GetUserSampleRedpointRsp, error)
	FixPresentIllustrationEvent(ctx context.Context, in *FixPresentIllustrationEventReq, opts ...grpc.CallOption) (*FixPresentIllustrationEventRsp, error)
	CleanUserIllustration(ctx context.Context, in *CleanUserIllustrationReq, opts ...grpc.CallOption) (*CleanUserIllustrationRsp, error)
	// 礼物->图鉴对账
	TimeRangeCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	TimeRangeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 补单图鉴
	FixPresentIllustrationOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
}

type presentIllustrationClient struct {
	cc *grpc.ClientConn
}

func NewPresentIllustrationClient(cc *grpc.ClientConn) PresentIllustrationClient {
	return &presentIllustrationClient{cc}
}

func (c *presentIllustrationClient) GetIllustrationConfigList(ctx context.Context, in *GetIllustrationConfigListReq, opts ...grpc.CallOption) (*GetIllustrationConfigListResp, error) {
	out := new(GetIllustrationConfigListResp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/GetIllustrationConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationClient) GetIllustrationConfig(ctx context.Context, in *GetIllustrationConfigReq, opts ...grpc.CallOption) (*GetIllustrationConfigResp, error) {
	out := new(GetIllustrationConfigResp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/GetIllustrationConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationClient) CreateIllustrationConfig(ctx context.Context, in *CreateIllustrationConfigReq, opts ...grpc.CallOption) (*CreateIllustrationConfigResp, error) {
	out := new(CreateIllustrationConfigResp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/CreateIllustrationConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationClient) DeleteIllustrationConfig(ctx context.Context, in *DeleteIllustrationConfigReq, opts ...grpc.CallOption) (*DeleteIllustrationConfigResp, error) {
	out := new(DeleteIllustrationConfigResp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/DeleteIllustrationConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationClient) CheckIllustrationConfigSort(ctx context.Context, in *CheckIllustrationConfigSortReq, opts ...grpc.CallOption) (*CheckIllustrationConfigSortResp, error) {
	out := new(CheckIllustrationConfigSortResp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/CheckIllustrationConfigSort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationClient) GetIllustrationSummary(ctx context.Context, in *GetIllustrationSummaryReq, opts ...grpc.CallOption) (*GetIllustrationSummaryRsp, error) {
	out := new(GetIllustrationSummaryRsp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/GetIllustrationSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationClient) GetIllustrationListAll(ctx context.Context, in *GetIllustrationListAllReq, opts ...grpc.CallOption) (*GetIllustrationListAllRsp, error) {
	out := new(GetIllustrationListAllRsp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/GetIllustrationListAll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationClient) GetIllustrationDetail(ctx context.Context, in *GetIllustrationDetailReq, opts ...grpc.CallOption) (*GetIllustrationDetailRsp, error) {
	out := new(GetIllustrationDetailRsp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/GetIllustrationDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationClient) GetIllustrationSwitch(ctx context.Context, in *GetIllustrationSwitchReq, opts ...grpc.CallOption) (*GetIllustrationSwitchRsp, error) {
	out := new(GetIllustrationSwitchRsp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/GetIllustrationSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationClient) GetUserSampleRedpoint(ctx context.Context, in *GetUserSampleRedpointReq, opts ...grpc.CallOption) (*GetUserSampleRedpointRsp, error) {
	out := new(GetUserSampleRedpointRsp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/GetUserSampleRedpoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationClient) FixPresentIllustrationEvent(ctx context.Context, in *FixPresentIllustrationEventReq, opts ...grpc.CallOption) (*FixPresentIllustrationEventRsp, error) {
	out := new(FixPresentIllustrationEventRsp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/FixPresentIllustrationEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationClient) CleanUserIllustration(ctx context.Context, in *CleanUserIllustrationReq, opts ...grpc.CallOption) (*CleanUserIllustrationRsp, error) {
	out := new(CleanUserIllustrationRsp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/CleanUserIllustration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationClient) TimeRangeCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/TimeRangeCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationClient) TimeRangeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/TimeRangeOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationClient) FixPresentIllustrationOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/present_illustration.PresentIllustration/FixPresentIllustrationOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PresentIllustrationServer is the server API for PresentIllustration service.
type PresentIllustrationServer interface {
	// 获取图鉴配置列表
	GetIllustrationConfigList(context.Context, *GetIllustrationConfigListReq) (*GetIllustrationConfigListResp, error)
	// 获取图鉴配置详情
	GetIllustrationConfig(context.Context, *GetIllustrationConfigReq) (*GetIllustrationConfigResp, error)
	// 创建图鉴配置
	CreateIllustrationConfig(context.Context, *CreateIllustrationConfigReq) (*CreateIllustrationConfigResp, error)
	// 删除图鉴配置
	DeleteIllustrationConfig(context.Context, *DeleteIllustrationConfigReq) (*DeleteIllustrationConfigResp, error)
	// 检查排序重复
	CheckIllustrationConfigSort(context.Context, *CheckIllustrationConfigSortReq) (*CheckIllustrationConfigSortResp, error)
	// 获取礼物图鉴摘要
	GetIllustrationSummary(context.Context, *GetIllustrationSummaryReq) (*GetIllustrationSummaryRsp, error)
	// 获取用户所有图鉴列表
	GetIllustrationListAll(context.Context, *GetIllustrationListAllReq) (*GetIllustrationListAllRsp, error)
	// 获取图鉴详情信息
	GetIllustrationDetail(context.Context, *GetIllustrationDetailReq) (*GetIllustrationDetailRsp, error)
	// 开关
	GetIllustrationSwitch(context.Context, *GetIllustrationSwitchReq) (*GetIllustrationSwitchRsp, error)
	// 定时获取红点的接口
	GetUserSampleRedpoint(context.Context, *GetUserSampleRedpointReq) (*GetUserSampleRedpointRsp, error)
	FixPresentIllustrationEvent(context.Context, *FixPresentIllustrationEventReq) (*FixPresentIllustrationEventRsp, error)
	CleanUserIllustration(context.Context, *CleanUserIllustrationReq) (*CleanUserIllustrationRsp, error)
	// 礼物->图鉴对账
	TimeRangeCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	TimeRangeOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 补单图鉴
	FixPresentIllustrationOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
}

func RegisterPresentIllustrationServer(s *grpc.Server, srv PresentIllustrationServer) {
	s.RegisterService(&_PresentIllustration_serviceDesc, srv)
}

func _PresentIllustration_GetIllustrationConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIllustrationConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).GetIllustrationConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/GetIllustrationConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).GetIllustrationConfigList(ctx, req.(*GetIllustrationConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustration_GetIllustrationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIllustrationConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).GetIllustrationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/GetIllustrationConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).GetIllustrationConfig(ctx, req.(*GetIllustrationConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustration_CreateIllustrationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIllustrationConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).CreateIllustrationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/CreateIllustrationConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).CreateIllustrationConfig(ctx, req.(*CreateIllustrationConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustration_DeleteIllustrationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIllustrationConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).DeleteIllustrationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/DeleteIllustrationConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).DeleteIllustrationConfig(ctx, req.(*DeleteIllustrationConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustration_CheckIllustrationConfigSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIllustrationConfigSortReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).CheckIllustrationConfigSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/CheckIllustrationConfigSort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).CheckIllustrationConfigSort(ctx, req.(*CheckIllustrationConfigSortReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustration_GetIllustrationSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIllustrationSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).GetIllustrationSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/GetIllustrationSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).GetIllustrationSummary(ctx, req.(*GetIllustrationSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustration_GetIllustrationListAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIllustrationListAllReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).GetIllustrationListAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/GetIllustrationListAll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).GetIllustrationListAll(ctx, req.(*GetIllustrationListAllReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustration_GetIllustrationDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIllustrationDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).GetIllustrationDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/GetIllustrationDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).GetIllustrationDetail(ctx, req.(*GetIllustrationDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustration_GetIllustrationSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIllustrationSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).GetIllustrationSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/GetIllustrationSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).GetIllustrationSwitch(ctx, req.(*GetIllustrationSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustration_GetUserSampleRedpoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSampleRedpointReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).GetUserSampleRedpoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/GetUserSampleRedpoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).GetUserSampleRedpoint(ctx, req.(*GetUserSampleRedpointReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustration_FixPresentIllustrationEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FixPresentIllustrationEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).FixPresentIllustrationEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/FixPresentIllustrationEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).FixPresentIllustrationEvent(ctx, req.(*FixPresentIllustrationEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustration_CleanUserIllustration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanUserIllustrationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).CleanUserIllustration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/CleanUserIllustration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).CleanUserIllustration(ctx, req.(*CleanUserIllustrationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustration_TimeRangeCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).TimeRangeCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/TimeRangeCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).TimeRangeCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustration_TimeRangeOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).TimeRangeOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/TimeRangeOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).TimeRangeOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustration_FixPresentIllustrationOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationServer).FixPresentIllustrationOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_illustration.PresentIllustration/FixPresentIllustrationOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationServer).FixPresentIllustrationOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PresentIllustration_serviceDesc = grpc.ServiceDesc{
	ServiceName: "present_illustration.PresentIllustration",
	HandlerType: (*PresentIllustrationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetIllustrationConfigList",
			Handler:    _PresentIllustration_GetIllustrationConfigList_Handler,
		},
		{
			MethodName: "GetIllustrationConfig",
			Handler:    _PresentIllustration_GetIllustrationConfig_Handler,
		},
		{
			MethodName: "CreateIllustrationConfig",
			Handler:    _PresentIllustration_CreateIllustrationConfig_Handler,
		},
		{
			MethodName: "DeleteIllustrationConfig",
			Handler:    _PresentIllustration_DeleteIllustrationConfig_Handler,
		},
		{
			MethodName: "CheckIllustrationConfigSort",
			Handler:    _PresentIllustration_CheckIllustrationConfigSort_Handler,
		},
		{
			MethodName: "GetIllustrationSummary",
			Handler:    _PresentIllustration_GetIllustrationSummary_Handler,
		},
		{
			MethodName: "GetIllustrationListAll",
			Handler:    _PresentIllustration_GetIllustrationListAll_Handler,
		},
		{
			MethodName: "GetIllustrationDetail",
			Handler:    _PresentIllustration_GetIllustrationDetail_Handler,
		},
		{
			MethodName: "GetIllustrationSwitch",
			Handler:    _PresentIllustration_GetIllustrationSwitch_Handler,
		},
		{
			MethodName: "GetUserSampleRedpoint",
			Handler:    _PresentIllustration_GetUserSampleRedpoint_Handler,
		},
		{
			MethodName: "FixPresentIllustrationEvent",
			Handler:    _PresentIllustration_FixPresentIllustrationEvent_Handler,
		},
		{
			MethodName: "CleanUserIllustration",
			Handler:    _PresentIllustration_CleanUserIllustration_Handler,
		},
		{
			MethodName: "TimeRangeCount",
			Handler:    _PresentIllustration_TimeRangeCount_Handler,
		},
		{
			MethodName: "TimeRangeOrderIds",
			Handler:    _PresentIllustration_TimeRangeOrderIds_Handler,
		},
		{
			MethodName: "FixPresentIllustrationOrder",
			Handler:    _PresentIllustration_FixPresentIllustrationOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "present-illustration/present-illustration.proto",
}

func init() {
	proto.RegisterFile("present-illustration/present-illustration.proto", fileDescriptor_present_illustration_0b85e00117326e78)
}

var fileDescriptor_present_illustration_0b85e00117326e78 = []byte{
	// 2015 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x59, 0x6d, 0x6f, 0xe3, 0x4a,
	0xf5, 0x5f, 0x27, 0xdd, 0x3c, 0x9c, 0x6e, 0x53, 0x77, 0xb6, 0xdb, 0x75, 0xd3, 0xed, 0xb6, 0xd7,
	0xff, 0xfb, 0xdf, 0xad, 0x2a, 0x9a, 0x8a, 0x2c, 0xf7, 0x0a, 0xc1, 0x4a, 0x50, 0x12, 0x6f, 0x09,
	0x77, 0x6f, 0x5a, 0x39, 0x09, 0x08, 0x24, 0x64, 0x79, 0x9d, 0x69, 0x6a, 0xea, 0x78, 0x8c, 0x67,
	0xd2, 0x52, 0x5e, 0x21, 0x81, 0x78, 0x90, 0xf8, 0x00, 0xbc, 0x86, 0x37, 0x48, 0x7c, 0x0a, 0xde,
	0xf1, 0x3d, 0x10, 0xdf, 0x03, 0xcd, 0x8c, 0xed, 0x38, 0x8d, 0x9d, 0x36, 0xe5, 0x9d, 0xe7, 0x3c,
	0xcd, 0x39, 0xbf, 0x39, 0x73, 0xe6, 0x97, 0x16, 0x8e, 0x83, 0x10, 0x53, 0xec, 0xb3, 0x23, 0xd7,
	0xf3, 0x26, 0x94, 0x85, 0x36, 0x73, 0x89, 0x9f, 0x29, 0x6c, 0x04, 0x21, 0x61, 0x04, 0x6d, 0x46,
	0x3a, 0x2b, 0xad, 0xab, 0xef, 0x85, 0xd8, 0x21, 0xbe, 0xe3, 0x7a, 0xf8, 0xe8, 0xba, 0x79, 0x9c,
	0x5e, 0x48, 0x37, 0x7d, 0x00, 0x1b, 0xe7, 0xb6, 0x73, 0x65, 0x8f, 0xf0, 0x8f, 0x26, 0xe3, 0xa0,
	0x45, 0xfc, 0x0b, 0x77, 0x84, 0x54, 0x28, 0xda, 0x41, 0xa0, 0x29, 0xfb, 0xca, 0xc1, 0x9a, 0xc9,
	0x3f, 0x51, 0x1d, 0x2a, 0x81, 0x67, 0xb3, 0x0b, 0x12, 0x8e, 0xb5, 0x82, 0x10, 0x27, 0x6b, 0x84,
	0x60, 0xc5, 0x73, 0xfd, 0x2b, 0xad, 0xb8, 0xaf, 0x1c, 0x54, 0x4d, 0xf1, 0xad, 0xff, 0xb3, 0x0c,
	0xa8, 0x93, 0x4a, 0x24, 0x0a, 0x5c, 0x83, 0x82, 0x3b, 0x8c, 0xe2, 0x16, 0xdc, 0x21, 0x77, 0xf5,
	0xed, 0x31, 0x16, 0x21, 0xab, 0xa6, 0xf8, 0xe6, 0x32, 0xd7, 0x21, 0x7e, 0x1c, 0x8e, 0x7f, 0xa3,
	0x3d, 0x58, 0x65, 0x84, 0xd9, 0x9e, 0x15, 0x84, 0xae, 0x83, 0xb5, 0x95, 0x7d, 0xe5, 0x60, 0xc5,
	0x04, 0x21, 0x3a, 0xe7, 0x12, 0xf4, 0x06, 0xd6, 0x53, 0x06, 0x96, 0xe3, 0xdf, 0x6a, 0x4f, 0x85,
	0xff, 0xda, 0xd4, 0xa8, 0xe5, 0xdf, 0xa2, 0x5d, 0x00, 0xca, 0xec, 0x90, 0x59, 0xcc, 0x1d, 0x63,
	0xad, 0x24, 0xe2, 0x54, 0x85, 0xa4, 0xef, 0x8e, 0x31, 0xda, 0x86, 0x0a, 0xf6, 0x87, 0x52, 0x59,
	0x16, 0xca, 0x32, 0xf6, 0x87, 0x42, 0xb5, 0x05, 0xa5, 0x10, 0x8f, 0xed, 0xf0, 0x4a, 0xab, 0x88,
	0xc0, 0xd1, 0x0a, 0x99, 0xf0, 0x2c, 0x46, 0xde, 0x73, 0x29, 0xd3, 0xaa, 0xfb, 0xc5, 0x83, 0xd5,
	0xe6, 0x71, 0x23, 0xeb, 0x38, 0x1a, 0x69, 0x48, 0xce, 0xa5, 0x81, 0x44, 0xc6, 0x5c, 0x8d, 0xec,
	0x3f, 0xba, 0x94, 0x71, 0x08, 0x28, 0x09, 0x99, 0x06, 0x02, 0x28, 0xf1, 0x8d, 0x0e, 0x61, 0x03,
	0xfb, 0x0e, 0x09, 0xb1, 0x95, 0x2a, 0x60, 0x55, 0xe4, 0xb8, 0x2e, 0x15, 0xbd, 0xa4, 0x8c, 0x37,
	0x10, 0x89, 0xac, 0xa4, 0x9a, 0x67, 0xc2, 0x72, 0x4d, 0x8a, 0x8d, 0xa8, 0xa6, 0x3a, 0x54, 0x48,
	0x80, 0x43, 0x9b, 0x91, 0x50, 0x5b, 0x13, 0x55, 0x25, 0x6b, 0xe4, 0x81, 0x76, 0xe9, 0x52, 0x46,
	0xc2, 0x5b, 0x2b, 0x8a, 0xc5, 0xe3, 0xc8, 0x1a, 0x6b, 0xa2, 0xc6, 0x77, 0xf7, 0xd7, 0x28, 0x8b,
	0x6b, 0x18, 0x22, 0x00, 0xdf, 0xd2, 0x7c, 0x11, 0x05, 0x9d, 0x8a, 0x44, 0xc5, 0x7b, 0xb0, 0x4a,
	0xc9, 0x24, 0x74, 0xb0, 0xe5, 0x13, 0x86, 0xb5, 0x75, 0x91, 0x0c, 0x48, 0x51, 0x97, 0x30, 0x8c,
	0x06, 0x80, 0x22, 0x83, 0x5f, 0x4c, 0xc6, 0x81, 0xe5, 0x88, 0xc0, 0x9a, 0x2a, 0x12, 0x79, 0x9b,
	0x9d, 0xc8, 0x5c, 0x5f, 0x9b, 0xaa, 0x0c, 0x91, 0xea, 0xf4, 0x43, 0xd8, 0x20, 0xa1, 0x3b, 0x72,
	0xfd, 0x34, 0xaa, 0x1b, 0x12, 0x55, 0xa9, 0x98, 0x41, 0x35, 0xb2, 0x4d, 0x50, 0x45, 0x12, 0x55,
	0x29, 0x8e, 0x51, 0xdd, 0x81, 0xaa, 0x4b, 0x23, 0xd0, 0xb4, 0xe7, 0xfb, 0xca, 0x41, 0xc5, 0xac,
	0xb8, 0x54, 0x16, 0xcc, 0x0b, 0x75, 0x42, 0x6c, 0x33, 0x09, 0xa7, 0xb6, 0x29, 0x3b, 0x59, 0x8a,
	0x84, 0xf7, 0xf7, 0xa1, 0x44, 0x99, 0xcd, 0x26, 0x54, 0x7b, 0xb1, 0xaf, 0x1c, 0xd4, 0x9a, 0x07,
	0xf7, 0xa3, 0xdc, 0x13, 0xf6, 0x66, 0xe4, 0x57, 0xff, 0x00, 0x30, 0x45, 0xf7, 0x4e, 0xc7, 0x2b,
	0x8b, 0x3a, 0xbe, 0x30, 0xd3, 0xf1, 0xfa, 0xdf, 0x14, 0xd8, 0xce, 0x6d, 0x58, 0xf4, 0x12, 0xca,
	0x23, 0xf7, 0x82, 0x59, 0xc9, 0x7d, 0x2e, 0xf1, 0x65, 0xe7, 0xe1, 0x77, 0x7a, 0x13, 0x9e, 0x4e,
	0x6f, 0xf3, 0x9a, 0x29, 0x17, 0x1c, 0xbc, 0xd9, 0x2b, 0xcc, 0x27, 0x4d, 0x7c, 0x7b, 0xe3, 0x7b,
	0x51, 0x9a, 0xde, 0x0b, 0xfd, 0x5f, 0x2b, 0xf0, 0xea, 0x14, 0xb3, 0xf9, 0xae, 0xe3, 0x7d, 0x65,
	0xe2, 0x5f, 0xf2, 0x8b, 0x4b, 0x2e, 0x2e, 0x28, 0x66, 0x71, 0x9e, 0x72, 0xc5, 0xf7, 0xf7, 0xdc,
	0xb1, 0xcb, 0xa2, 0x79, 0x26, 0x17, 0xd1, 0x84, 0x92, 0x79, 0xa6, 0x27, 0xd4, 0x4a, 0xaa, 0x9a,
	0xe9, 0x28, 0x78, 0x3a, 0x33, 0x0a, 0xa6, 0x47, 0x57, 0x7a, 0xdc, 0xd1, 0xa1, 0xb3, 0xa8, 0xc0,
	0xb2, 0xf0, 0xff, 0x6e, 0xb6, 0xff, 0xa2, 0x6a, 0x1b, 0x3d, 0x12, 0xb2, 0xfe, 0x6d, 0x80, 0xa3,
	0xa9, 0x61, 0xc2, 0x53, 0x12, 0x0e, 0x71, 0x28, 0x86, 0x56, 0xad, 0xf9, 0xfe, 0x91, 0x11, 0xcf,
	0x78, 0x0c, 0x53, 0x86, 0xd2, 0x7f, 0xa7, 0x40, 0x25, 0xde, 0x06, 0x6d, 0xc3, 0x8b, 0xde, 0x99,
	0xd9, 0xb7, 0xfa, 0x3f, 0x3d, 0x37, 0xac, 0x41, 0xb7, 0x77, 0x6e, 0xb4, 0x3a, 0x1f, 0x3a, 0x46,
	0x5b, 0x7d, 0x32, 0xab, 0xea, 0x9f, 0xf5, 0x4f, 0x3e, 0x5a, 0xe7, 0x66, 0xa7, 0x65, 0xa8, 0x0a,
	0xd2, 0x60, 0x73, 0xaa, 0xea, 0xf5, 0x4f, 0xf8, 0x67, 0xe7, 0x6b, 0x43, 0x2d, 0xa0, 0x2d, 0x40,
	0x53, 0x8d, 0xd1, 0x6d, 0x4b, 0x79, 0x11, 0xa9, 0xf0, 0x6c, 0x2a, 0xef, 0xb4, 0xd5, 0x15, 0xfd,
	0x1c, 0xaa, 0x49, 0x6a, 0xa8, 0x0e, 0x5b, 0x42, 0x7d, 0x66, 0xb6, 0x0d, 0xf3, 0x4e, 0x1e, 0x08,
	0x6a, 0x29, 0xdd, 0x49, 0xaf, 0xa5, 0x2a, 0xe8, 0x39, 0xac, 0xa7, 0x64, 0x6d, 0xa3, 0xd7, 0x52,
	0x0b, 0x3a, 0x85, 0xdd, 0x05, 0x48, 0xd0, 0x00, 0xbd, 0xe7, 0x2f, 0x1d, 0xe5, 0x8d, 0xc4, 0xc7,
	0xce, 0xc1, 0x43, 0xe7, 0x9f, 0x29, 0xbc, 0x78, 0xc3, 0x89, 0xc7, 0x28, 0x6e, 0x38, 0xb1, 0xd0,
	0x0f, 0x41, 0xcb, 0xdc, 0x94, 0xb7, 0xee, 0x9d, 0xe7, 0x52, 0xff, 0x39, 0x6c, 0xe7, 0xd8, 0xd2,
	0x80, 0x77, 0x5f, 0x34, 0x15, 0xb9, 0xc3, 0x32, 0xe9, 0x45, 0x7e, 0xba, 0x05, 0x3b, 0x2d, 0x31,
	0x88, 0xb2, 0xb3, 0xf9, 0xdf, 0x37, 0x78, 0x0d, 0xaf, 0xf2, 0x37, 0xa0, 0x81, 0x7e, 0x04, 0x3b,
	0x6d, 0xec, 0xe1, 0xbc, 0x04, 0xee, 0xc2, 0xf1, 0x1a, 0x5e, 0xe5, 0x9b, 0xd3, 0x40, 0x6f, 0xc3,
	0xeb, 0xd6, 0x25, 0x76, 0xae, 0xe6, 0xd5, 0xbc, 0x71, 0x32, 0x22, 0x26, 0x03, 0xa6, 0x90, 0x1a,
	0x30, 0xbf, 0x51, 0x60, 0x6f, 0x61, 0x18, 0x1a, 0x44, 0x23, 0x3f, 0xc4, 0x01, 0xb6, 0xe5, 0x98,
	0x11, 0x23, 0xdf, 0x14, 0xeb, 0x14, 0x6e, 0x85, 0x47, 0xe2, 0xf6, 0x1e, 0x6a, 0xbd, 0xc9, 0x78,
	0x6c, 0x87, 0xb7, 0x7d, 0x12, 0x74, 0xfc, 0x0b, 0x22, 0x86, 0x17, 0xbe, 0xc6, 0x5e, 0x94, 0xbb,
	0x5c, 0x70, 0xa9, 0x43, 0x26, 0x7e, 0x32, 0xd2, 0xc4, 0x42, 0x3f, 0x9a, 0xeb, 0x9a, 0x28, 0x18,
	0x47, 0x40, 0x85, 0xe2, 0x24, 0x81, 0x80, 0x7f, 0xea, 0xff, 0x56, 0x72, 0xed, 0x69, 0x80, 0xde,
	0xc2, 0xba, 0x43, 0x3c, 0x0f, 0x3b, 0x0c, 0x0f, 0x2d, 0xd9, 0xce, 0xd2, 0xb7, 0x96, 0x88, 0xfb,
	0x5c, 0xca, 0x5f, 0x56, 0x9f, 0x30, 0x2b, 0x92, 0x5a, 0xe9, 0xce, 0x5f, 0xf7, 0x09, 0x6b, 0x49,
	0xb9, 0xb4, 0xfd, 0x1e, 0x54, 0x18, 0x09, 0x24, 0xb7, 0x28, 0x8a, 0xbb, 0xf5, 0x79, 0x36, 0x46,
	0xb3, 0x28, 0x98, 0x65, 0x46, 0x02, 0x41, 0x1f, 0x5e, 0x42, 0xf9, 0xd2, 0xa6, 0x96, 0x8f, 0x6f,
	0xc4, 0xa0, 0xae, 0x98, 0xa5, 0x4b, 0x9b, 0x76, 0xf1, 0x0d, 0x57, 0x84, 0x78, 0x68, 0x0d, 0x09,
	0x13, 0xb3, 0xba, 0xc2, 0x67, 0xf5, 0xb0, 0x4d, 0x98, 0xfe, 0x87, 0x02, 0xa8, 0xe9, 0x12, 0x05,
	0xaa, 0x77, 0xdb, 0x21, 0x41, 0xb9, 0x90, 0x46, 0x79, 0x2b, 0x19, 0xf3, 0x45, 0xf9, 0xa0, 0x44,
	0xc3, 0x9b, 0x73, 0xe4, 0x90, 0x8c, 0x42, 0x4c, 0x69, 0xf4, 0xa6, 0x25, 0x6b, 0x1e, 0xe9, 0xda,
	0xf6, 0x26, 0x58, 0x64, 0xb1, 0x62, 0xca, 0x45, 0x3a, 0xed, 0xd2, 0x4c, 0xda, 0x1f, 0xa6, 0x69,
	0xcb, 0xa7, 0xe0, 0xe8, 0xfe, 0x9e, 0x11, 0x88, 0xe0, 0xa1, 0x18, 0xfe, 0x51, 0x95, 0x48, 0x07,
	0xc9, 0x7f, 0xad, 0xf8, 0xa9, 0xae, 0x88, 0xbc, 0x24, 0x99, 0x3e, 0x15, 0xef, 0xb5, 0xee, 0xce,
	0x1d, 0x37, 0x87, 0xf4, 0xc4, 0xf3, 0x32, 0xdb, 0x83, 0xf3, 0x09, 0x66, 0x87, 0x23, 0xcc, 0x2c,
	0xae, 0x90, 0xc0, 0x54, 0xa5, 0x64, 0xe0, 0x0e, 0xf9, 0x4d, 0x70, 0x3c, 0x6c, 0x87, 0x56, 0x88,
	0xe5, 0x33, 0x5a, 0x31, 0x2b, 0x42, 0x60, 0xe2, 0xa1, 0xfe, 0x93, 0xdc, 0xad, 0x68, 0x80, 0xbe,
	0x33, 0x33, 0x5c, 0xdf, 0x3c, 0xb0, 0x60, 0xe1, 0xa3, 0xff, 0x45, 0x81, 0xcd, 0xb4, 0x4a, 0x94,
	0xc6, 0x4f, 0x34, 0x97, 0xa5, 0x7c, 0x06, 0xcf, 0x42, 0xec, 0x60, 0xf7, 0x1a, 0x0f, 0x2d, 0x7f,
	0x12, 0xff, 0xa8, 0x59, 0x8d, 0x65, 0xdd, 0xc9, 0x18, 0xfd, 0x3f, 0xd4, 0x12, 0x13, 0xc9, 0x54,
	0x8a, 0x92, 0xee, 0xc5, 0x52, 0xf9, 0xd3, 0x63, 0x17, 0x40, 0x32, 0x16, 0x76, 0x1b, 0xc4, 0x64,
	0x46, 0x72, 0x18, 0x7e, 0x14, 0xfa, 0xa7, 0xb9, 0xf9, 0xde, 0xc6, 0xcc, 0x76, 0x05, 0xba, 0x6f,
	0x61, 0x3d, 0x5d, 0xdd, 0x34, 0xcb, 0x5a, 0x5a, 0xdc, 0xb9, 0x0f, 0x74, 0xfd, 0xef, 0x4a, 0xde,
	0x26, 0x34, 0x40, 0x2d, 0xa8, 0x7e, 0xb2, 0x29, 0xb6, 0x5c, 0xff, 0x82, 0x44, 0x93, 0xfb, 0xa1,
	0xe0, 0x56, 0xb8, 0xa3, 0xc0, 0xf1, 0x14, 0xaa, 0x02, 0x47, 0x71, 0x42, 0x05, 0x71, 0x42, 0x87,
	0xf7, 0x07, 0x89, 0x8f, 0xc1, 0xac, 0x70, 0x67, 0x7e, 0xd6, 0xfa, 0x37, 0xe6, 0x32, 0xed, 0xdd,
	0xb8, 0xcc, 0xb9, 0xcc, 0x9e, 0x45, 0xcd, 0x3c, 0x6b, 0x1a, 0x88, 0x6b, 0x28, 0x16, 0xd1, 0xc0,
	0x8d, 0x56, 0xfa, 0x7f, 0x14, 0x78, 0xfd, 0xc1, 0xfd, 0x55, 0xc4, 0x56, 0xd3, 0xbe, 0xc6, 0x35,
	0xf6, 0x59, 0x76, 0x57, 0xab, 0x50, 0x74, 0x12, 0x64, 0xf9, 0x27, 0x6f, 0x64, 0x9a, 0x30, 0x63,
	0x79, 0xd1, 0x2b, 0x34, 0xfe, 0x31, 0x98, 0x6a, 0xab, 0x95, 0x99, 0xb6, 0xda, 0x06, 0x51, 0xaa,
	0x68, 0x29, 0xc9, 0x5e, 0x85, 0x21, 0x6f, 0xa7, 0xd9, 0x3e, 0x29, 0xdd, 0xe9, 0x93, 0x29, 0x1d,
	0x2e, 0xa7, 0xe9, 0xf0, 0x36, 0x54, 0x04, 0xe9, 0x8a, 0xef, 0x6e, 0xd5, 0x2c, 0x8b, 0x75, 0x67,
	0xa8, 0xef, 0x2f, 0x2e, 0x93, 0x06, 0x1c, 0xeb, 0x96, 0x87, 0x6d, 0x7f, 0x40, 0x71, 0x98, 0x36,
	0xc8, 0xc6, 0xba, 0x9e, 0x67, 0x2d, 0x23, 0x9d, 0x62, 0xc6, 0x35, 0x3d, 0x7b, 0x1c, 0x78, 0xd8,
	0xc4, 0xc3, 0x80, 0xb8, 0x39, 0x60, 0xea, 0x5f, 0xe6, 0x59, 0x53, 0xf1, 0x87, 0x84, 0x30, 0x5a,
	0x0a, 0xb4, 0x2b, 0x66, 0xb2, 0x3e, 0xfc, 0xab, 0x32, 0xfb, 0x47, 0x03, 0x49, 0x8e, 0xd1, 0xff,
	0xc1, 0x5e, 0xe7, 0xe3, 0xc7, 0x41, 0xaf, 0x6f, 0x9e, 0xf4, 0x3b, 0x67, 0x5d, 0xce, 0x17, 0xfb,
	0x83, 0xde, 0x1d, 0x92, 0x97, 0x63, 0xd4, 0xe9, 0x5a, 0xe7, 0xe6, 0xd9, 0xa9, 0x69, 0xf4, 0x7a,
	0xaa, 0x92, 0x67, 0xd4, 0x3d, 0xeb, 0x4b, 0x12, 0x6a, 0xb4, 0xd5, 0x02, 0xda, 0x85, 0xed, 0x2c,
	0x23, 0xa3, 0xdb, 0x36, 0xda, 0x6a, 0xf1, 0xf0, 0x1f, 0x0a, 0x6c, 0xcd, 0x24, 0x89, 0xed, 0xd0,
	0xb9, 0x14, 0x47, 0x78, 0x00, 0x9f, 0xcf, 0x7a, 0x1a, 0x27, 0x66, 0xeb, 0x87, 0x59, 0xd4, 0x78,
	0x0f, 0x76, 0x72, 0x2d, 0x3b, 0x6d, 0x55, 0x41, 0x9f, 0xc1, 0x6e, 0xae, 0x41, 0xf7, 0x44, 0x30,
	0xe5, 0xb9, 0x62, 0x52, 0x26, 0xa6, 0xf1, 0xf5, 0x89, 0xf9, 0x95, 0x5a, 0x3c, 0x3c, 0x81, 0x97,
	0x39, 0x6f, 0x04, 0x02, 0x28, 0x0d, 0xfc, 0x2b, 0x9f, 0xdc, 0xa8, 0x4f, 0xd0, 0x1a, 0x54, 0x5b,
	0xf1, 0xf3, 0xad, 0x2a, 0x68, 0x15, 0xca, 0x83, 0x60, 0x14, 0xda, 0x43, 0xac, 0x16, 0x9a, 0x7f,
	0xac, 0xc1, 0xf3, 0x8c, 0x2e, 0x43, 0xbf, 0x57, 0x72, 0xd8, 0xa8, 0x78, 0x91, 0x9b, 0xcb, 0xff,
	0xd2, 0xa8, 0xbf, 0x5b, 0xda, 0x87, 0x06, 0xfa, 0x13, 0xf4, 0x6b, 0x78, 0x91, 0x69, 0x82, 0x1a,
	0x4b, 0xc4, 0xe3, 0xfb, 0x1f, 0x2f, 0x65, 0x2f, 0xf6, 0xfe, 0xad, 0x02, 0x5a, 0x1e, 0xa5, 0x45,
	0xdf, 0xcc, 0x8e, 0xb7, 0x80, 0x63, 0xd7, 0x9b, 0xcb, 0xba, 0x24, 0x59, 0xe4, 0x31, 0xe1, 0xbc,
	0x2c, 0x16, 0x10, 0xed, 0xbc, 0x2c, 0x16, 0x92, 0xed, 0x27, 0xe8, 0xcf, 0x0a, 0xec, 0x2c, 0x20,
	0xca, 0xe8, 0x5b, 0x39, 0xb5, 0x2d, 0xa4, 0xe8, 0xf5, 0x2f, 0x1e, 0xe1, 0x15, 0xb5, 0xc5, 0x56,
	0x36, 0x8d, 0x45, 0x0f, 0x3b, 0xe7, 0x29, 0x49, 0xae, 0x2f, 0xe7, 0x40, 0x83, 0x8c, 0xbd, 0x23,
	0xa2, 0xf3, 0xc0, 0xbd, 0xa7, 0x0c, 0xac, 0xbe, 0x9c, 0x03, 0x0d, 0xd0, 0xcd, 0xdc, 0x75, 0x90,
	0x5c, 0xe0, 0x81, 0xd7, 0x21, 0x61, 0x27, 0xf5, 0xa5, 0xec, 0x33, 0x37, 0x96, 0x8f, 0xf5, 0x03,
	0x37, 0x4e, 0x78, 0x40, 0x7d, 0x29, 0xfb, 0x64, 0xe3, 0xf9, 0xf7, 0x66, 0xc1, 0xc6, 0x99, 0x4f,
	0x59, 0x7d, 0x29, 0x7b, 0x1a, 0xa0, 0x3f, 0x29, 0xb0, 0xb3, 0xe0, 0x0d, 0xce, 0xeb, 0xf8, 0xc5,
	0xec, 0xa4, 0xfe, 0x08, 0x2f, 0x09, 0x42, 0xe6, 0xf3, 0x9d, 0x07, 0x42, 0x1e, 0x33, 0xa8, 0x2f,
	0x65, 0x4f, 0x03, 0x64, 0x40, 0x4d, 0xfc, 0x65, 0xd7, 0xf6, 0x47, 0xb8, 0xc5, 0x7f, 0x70, 0xa2,
	0xed, 0x86, 0x19, 0xff, 0xa3, 0xe1, 0xc7, 0xcd, 0x46, 0xa2, 0xe4, 0xc1, 0xb7, 0x66, 0x54, 0xc2,
	0x3c, 0xba, 0xae, 0x5f, 0xc1, 0x46, 0x62, 0x79, 0x26, 0x29, 0x0e, 0x5d, 0x14, 0x69, 0x56, 0x15,
	0x7b, 0x44, 0xc1, 0x06, 0x79, 0xe7, 0x22, 0xff, 0x5a, 0xf4, 0x6a, 0xc6, 0xd7, 0xc4, 0x81, 0x67,
	0x3b, 0x72, 0xd3, 0xf9, 0x1c, 0x8d, 0x71, 0xc0, 0x6e, 0x65, 0xd8, 0x1f, 0x7c, 0xfb, 0x67, 0x5f,
	0x8e, 0x88, 0x67, 0xfb, 0xa3, 0xc6, 0x17, 0x4d, 0xc6, 0x1a, 0x0e, 0x19, 0x1f, 0x8b, 0xff, 0xa2,
	0x38, 0xc4, 0x3b, 0xa6, 0x38, 0xbc, 0x76, 0x1d, 0x4c, 0x33, 0xff, 0x47, 0xf3, 0xa9, 0x24, 0xec,
	0xde, 0xfd, 0x37, 0x00, 0x00, 0xff, 0xff, 0xa2, 0x88, 0x1b, 0x3a, 0xd7, 0x19, 0x00, 0x00,
}
