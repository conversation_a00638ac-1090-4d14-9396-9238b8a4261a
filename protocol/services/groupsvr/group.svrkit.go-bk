package Group

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: services/group/group.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for Group service
const GroupMagic = uint16(15290)

// Client API for Group service

type GroupClientInterface interface {
	AddBulletin(ctx context.Context, uin uint32, in *AddBulletinReq, opts ...svrkit.CallOption) (*AddBulletinResp, error)
	DeleteBulletin(ctx context.Context, uin uint32, in *DeleteBulletinReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetBulletin(ctx context.Context, uin uint32, in *GetBulletinReq, opts ...svrkit.CallOption) (*GetBulletinResp, error)
	UpdateBulletinSeq(ctx context.Context, uin uint32, in *UpdateBulletinSeqReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetBulletinSeq(ctx context.Context, uin uint32, in *GetBulletinSeqReq, opts ...svrkit.CallOption) (*GetBulletinSeqResp, error)
	RecordUserKicked(ctx context.Context, uin uint32, in *RecordUserKickedReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetUserKickedRecords(ctx context.Context, uin uint32, in *GetUserKickedRecordsReq, opts ...svrkit.CallOption) (*GetUserKickedRecordsResp, error)
	UpdateGroupAdminChangeTimeline(ctx context.Context, uin uint32, in *UpdateGroupAdminChangeTimelineReq, opts ...svrkit.CallOption) (*UpdateGroupAdminChangeTimelineResp, error)
	GetGroupAdminChangeTimeline(ctx context.Context, uin uint32, in *GetGroupAdminChangeTimelineReq, opts ...svrkit.CallOption) (*GetGroupAdminChangeTimelineResp, error)
	BatchDelBulletin(ctx context.Context, uin uint32, in *BatchDelBulletinReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
}

type GroupClient struct {
	cc *svrkit.ClientConn
}

func NewGroupClient(cc *svrkit.ClientConn) GroupClientInterface {
	return &GroupClient{cc}
}

const (
	commandGroupGetSelfSvnInfo                 = 9995
	commandGroupEcho                           = 9999
	commandGroupAddBulletin                    = 1
	commandGroupDeleteBulletin                 = 2
	commandGroupGetBulletin                    = 3
	commandGroupUpdateBulletinSeq              = 4
	commandGroupGetBulletinSeq                 = 5
	commandGroupRecordUserKicked               = 10
	commandGroupGetUserKickedRecords           = 11
	commandGroupUpdateGroupAdminChangeTimeline = 12
	commandGroupGetGroupAdminChangeTimeline    = 13
	commandGroupBatchDelBulletin               = 14
)

func (c *GroupClient) AddBulletin(ctx context.Context, uin uint32, in *AddBulletinReq, opts ...svrkit.CallOption) (*AddBulletinResp, error) {
	out := new(AddBulletinResp)
	err := c.cc.Invoke(ctx, uin, commandGroupAddBulletin, "/Group.Group/AddBulletin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GroupClient) DeleteBulletin(ctx context.Context, uin uint32, in *DeleteBulletinReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGroupDeleteBulletin, "/Group.Group/DeleteBulletin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GroupClient) GetBulletin(ctx context.Context, uin uint32, in *GetBulletinReq, opts ...svrkit.CallOption) (*GetBulletinResp, error) {
	out := new(GetBulletinResp)
	err := c.cc.Invoke(ctx, uin, commandGroupGetBulletin, "/Group.Group/GetBulletin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GroupClient) UpdateBulletinSeq(ctx context.Context, uin uint32, in *UpdateBulletinSeqReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGroupUpdateBulletinSeq, "/Group.Group/UpdateBulletinSeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GroupClient) GetBulletinSeq(ctx context.Context, uin uint32, in *GetBulletinSeqReq, opts ...svrkit.CallOption) (*GetBulletinSeqResp, error) {
	out := new(GetBulletinSeqResp)
	err := c.cc.Invoke(ctx, uin, commandGroupGetBulletinSeq, "/Group.Group/GetBulletinSeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GroupClient) RecordUserKicked(ctx context.Context, uin uint32, in *RecordUserKickedReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGroupRecordUserKicked, "/Group.Group/RecordUserKicked", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GroupClient) GetUserKickedRecords(ctx context.Context, uin uint32, in *GetUserKickedRecordsReq, opts ...svrkit.CallOption) (*GetUserKickedRecordsResp, error) {
	out := new(GetUserKickedRecordsResp)
	err := c.cc.Invoke(ctx, uin, commandGroupGetUserKickedRecords, "/Group.Group/GetUserKickedRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GroupClient) UpdateGroupAdminChangeTimeline(ctx context.Context, uin uint32, in *UpdateGroupAdminChangeTimelineReq, opts ...svrkit.CallOption) (*UpdateGroupAdminChangeTimelineResp, error) {
	out := new(UpdateGroupAdminChangeTimelineResp)
	err := c.cc.Invoke(ctx, uin, commandGroupUpdateGroupAdminChangeTimeline, "/Group.Group/UpdateGroupAdminChangeTimeline", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GroupClient) GetGroupAdminChangeTimeline(ctx context.Context, uin uint32, in *GetGroupAdminChangeTimelineReq, opts ...svrkit.CallOption) (*GetGroupAdminChangeTimelineResp, error) {
	out := new(GetGroupAdminChangeTimelineResp)
	err := c.cc.Invoke(ctx, uin, commandGroupGetGroupAdminChangeTimeline, "/Group.Group/GetGroupAdminChangeTimeline", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GroupClient) BatchDelBulletin(ctx context.Context, uin uint32, in *BatchDelBulletinReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGroupBatchDelBulletin, "/Group.Group/BatchDelBulletin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
