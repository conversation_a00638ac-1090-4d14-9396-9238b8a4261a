// Code generated by protoc-gen-go. DO NOT EDIT.
// source: muse-channel-heat/muse-channel-heat.proto

package muse_channel_heat // import "golang.52tt.com/protocol/services/muse-channel-heat"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type MainSwitchStatus int32

const (
	MainSwitchStatus_UnknownStatus MainSwitchStatus = 0
	MainSwitchStatus_TurnOn        MainSwitchStatus = 1
	MainSwitchStatus_TurnOff       MainSwitchStatus = 2
)

var MainSwitchStatus_name = map[int32]string{
	0: "UnknownStatus",
	1: "TurnOn",
	2: "TurnOff",
}
var MainSwitchStatus_value = map[string]int32{
	"UnknownStatus": 0,
	"TurnOn":        1,
	"TurnOff":       2,
}

func (x MainSwitchStatus) String() string {
	return proto.EnumName(MainSwitchStatus_name, int32(x))
}
func (MainSwitchStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_channel_heat_be1ae519f71845f1, []int{0}
}

type SetMainSwitchStatusReq struct {
	MainSwitchStatus     uint32   `protobuf:"varint,1,opt,name=main_switch_status,json=mainSwitchStatus,proto3" json:"main_switch_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMainSwitchStatusReq) Reset()         { *m = SetMainSwitchStatusReq{} }
func (m *SetMainSwitchStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetMainSwitchStatusReq) ProtoMessage()    {}
func (*SetMainSwitchStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_channel_heat_be1ae519f71845f1, []int{0}
}
func (m *SetMainSwitchStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMainSwitchStatusReq.Unmarshal(m, b)
}
func (m *SetMainSwitchStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMainSwitchStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetMainSwitchStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMainSwitchStatusReq.Merge(dst, src)
}
func (m *SetMainSwitchStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetMainSwitchStatusReq.Size(m)
}
func (m *SetMainSwitchStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMainSwitchStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMainSwitchStatusReq proto.InternalMessageInfo

func (m *SetMainSwitchStatusReq) GetMainSwitchStatus() uint32 {
	if m != nil {
		return m.MainSwitchStatus
	}
	return 0
}

type SetMainSwitchStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMainSwitchStatusResp) Reset()         { *m = SetMainSwitchStatusResp{} }
func (m *SetMainSwitchStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetMainSwitchStatusResp) ProtoMessage()    {}
func (*SetMainSwitchStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_channel_heat_be1ae519f71845f1, []int{1}
}
func (m *SetMainSwitchStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMainSwitchStatusResp.Unmarshal(m, b)
}
func (m *SetMainSwitchStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMainSwitchStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetMainSwitchStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMainSwitchStatusResp.Merge(dst, src)
}
func (m *SetMainSwitchStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetMainSwitchStatusResp.Size(m)
}
func (m *SetMainSwitchStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMainSwitchStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMainSwitchStatusResp proto.InternalMessageInfo

type StartHeatChannelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartHeatChannelReq) Reset()         { *m = StartHeatChannelReq{} }
func (m *StartHeatChannelReq) String() string { return proto.CompactTextString(m) }
func (*StartHeatChannelReq) ProtoMessage()    {}
func (*StartHeatChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_channel_heat_be1ae519f71845f1, []int{2}
}
func (m *StartHeatChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartHeatChannelReq.Unmarshal(m, b)
}
func (m *StartHeatChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartHeatChannelReq.Marshal(b, m, deterministic)
}
func (dst *StartHeatChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartHeatChannelReq.Merge(dst, src)
}
func (m *StartHeatChannelReq) XXX_Size() int {
	return xxx_messageInfo_StartHeatChannelReq.Size(m)
}
func (m *StartHeatChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartHeatChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartHeatChannelReq proto.InternalMessageInfo

func (m *StartHeatChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartHeatChannelReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type StartHeatChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartHeatChannelResp) Reset()         { *m = StartHeatChannelResp{} }
func (m *StartHeatChannelResp) String() string { return proto.CompactTextString(m) }
func (*StartHeatChannelResp) ProtoMessage()    {}
func (*StartHeatChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_channel_heat_be1ae519f71845f1, []int{3}
}
func (m *StartHeatChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartHeatChannelResp.Unmarshal(m, b)
}
func (m *StartHeatChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartHeatChannelResp.Marshal(b, m, deterministic)
}
func (dst *StartHeatChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartHeatChannelResp.Merge(dst, src)
}
func (m *StartHeatChannelResp) XXX_Size() int {
	return xxx_messageInfo_StartHeatChannelResp.Size(m)
}
func (m *StartHeatChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartHeatChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartHeatChannelResp proto.InternalMessageInfo

type RefusalToHeatReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefusalToHeatReq) Reset()         { *m = RefusalToHeatReq{} }
func (m *RefusalToHeatReq) String() string { return proto.CompactTextString(m) }
func (*RefusalToHeatReq) ProtoMessage()    {}
func (*RefusalToHeatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_channel_heat_be1ae519f71845f1, []int{4}
}
func (m *RefusalToHeatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefusalToHeatReq.Unmarshal(m, b)
}
func (m *RefusalToHeatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefusalToHeatReq.Marshal(b, m, deterministic)
}
func (dst *RefusalToHeatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefusalToHeatReq.Merge(dst, src)
}
func (m *RefusalToHeatReq) XXX_Size() int {
	return xxx_messageInfo_RefusalToHeatReq.Size(m)
}
func (m *RefusalToHeatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RefusalToHeatReq.DiscardUnknown(m)
}

var xxx_messageInfo_RefusalToHeatReq proto.InternalMessageInfo

func (m *RefusalToHeatReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RefusalToHeatReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type RefusalToHeatResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefusalToHeatResp) Reset()         { *m = RefusalToHeatResp{} }
func (m *RefusalToHeatResp) String() string { return proto.CompactTextString(m) }
func (*RefusalToHeatResp) ProtoMessage()    {}
func (*RefusalToHeatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_channel_heat_be1ae519f71845f1, []int{5}
}
func (m *RefusalToHeatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefusalToHeatResp.Unmarshal(m, b)
}
func (m *RefusalToHeatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefusalToHeatResp.Marshal(b, m, deterministic)
}
func (dst *RefusalToHeatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefusalToHeatResp.Merge(dst, src)
}
func (m *RefusalToHeatResp) XXX_Size() int {
	return xxx_messageInfo_RefusalToHeatResp.Size(m)
}
func (m *RefusalToHeatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RefusalToHeatResp.DiscardUnknown(m)
}

var xxx_messageInfo_RefusalToHeatResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*SetMainSwitchStatusReq)(nil), "muse_channel_heat.SetMainSwitchStatusReq")
	proto.RegisterType((*SetMainSwitchStatusResp)(nil), "muse_channel_heat.SetMainSwitchStatusResp")
	proto.RegisterType((*StartHeatChannelReq)(nil), "muse_channel_heat.StartHeatChannelReq")
	proto.RegisterType((*StartHeatChannelResp)(nil), "muse_channel_heat.StartHeatChannelResp")
	proto.RegisterType((*RefusalToHeatReq)(nil), "muse_channel_heat.RefusalToHeatReq")
	proto.RegisterType((*RefusalToHeatResp)(nil), "muse_channel_heat.RefusalToHeatResp")
	proto.RegisterEnum("muse_channel_heat.MainSwitchStatus", MainSwitchStatus_name, MainSwitchStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelHeatClient is the client API for ChannelHeat service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelHeatClient interface {
	SetMainSwitchStatus(ctx context.Context, in *SetMainSwitchStatusReq, opts ...grpc.CallOption) (*SetMainSwitchStatusResp, error)
	StartHeatChannel(ctx context.Context, in *StartHeatChannelReq, opts ...grpc.CallOption) (*StartHeatChannelResp, error)
	RefusalToHeat(ctx context.Context, in *RefusalToHeatReq, opts ...grpc.CallOption) (*RefusalToHeatResp, error)
}

type channelHeatClient struct {
	cc *grpc.ClientConn
}

func NewChannelHeatClient(cc *grpc.ClientConn) ChannelHeatClient {
	return &channelHeatClient{cc}
}

func (c *channelHeatClient) SetMainSwitchStatus(ctx context.Context, in *SetMainSwitchStatusReq, opts ...grpc.CallOption) (*SetMainSwitchStatusResp, error) {
	out := new(SetMainSwitchStatusResp)
	err := c.cc.Invoke(ctx, "/muse_channel_heat.channelHeat/SetMainSwitchStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelHeatClient) StartHeatChannel(ctx context.Context, in *StartHeatChannelReq, opts ...grpc.CallOption) (*StartHeatChannelResp, error) {
	out := new(StartHeatChannelResp)
	err := c.cc.Invoke(ctx, "/muse_channel_heat.channelHeat/StartHeatChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelHeatClient) RefusalToHeat(ctx context.Context, in *RefusalToHeatReq, opts ...grpc.CallOption) (*RefusalToHeatResp, error) {
	out := new(RefusalToHeatResp)
	err := c.cc.Invoke(ctx, "/muse_channel_heat.channelHeat/RefusalToHeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelHeatServer is the server API for ChannelHeat service.
type ChannelHeatServer interface {
	SetMainSwitchStatus(context.Context, *SetMainSwitchStatusReq) (*SetMainSwitchStatusResp, error)
	StartHeatChannel(context.Context, *StartHeatChannelReq) (*StartHeatChannelResp, error)
	RefusalToHeat(context.Context, *RefusalToHeatReq) (*RefusalToHeatResp, error)
}

func RegisterChannelHeatServer(s *grpc.Server, srv ChannelHeatServer) {
	s.RegisterService(&_ChannelHeat_serviceDesc, srv)
}

func _ChannelHeat_SetMainSwitchStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMainSwitchStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelHeatServer).SetMainSwitchStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_channel_heat.channelHeat/SetMainSwitchStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelHeatServer).SetMainSwitchStatus(ctx, req.(*SetMainSwitchStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelHeat_StartHeatChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartHeatChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelHeatServer).StartHeatChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_channel_heat.channelHeat/StartHeatChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelHeatServer).StartHeatChannel(ctx, req.(*StartHeatChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelHeat_RefusalToHeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefusalToHeatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelHeatServer).RefusalToHeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_channel_heat.channelHeat/RefusalToHeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelHeatServer).RefusalToHeat(ctx, req.(*RefusalToHeatReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelHeat_serviceDesc = grpc.ServiceDesc{
	ServiceName: "muse_channel_heat.channelHeat",
	HandlerType: (*ChannelHeatServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetMainSwitchStatus",
			Handler:    _ChannelHeat_SetMainSwitchStatus_Handler,
		},
		{
			MethodName: "StartHeatChannel",
			Handler:    _ChannelHeat_StartHeatChannel_Handler,
		},
		{
			MethodName: "RefusalToHeat",
			Handler:    _ChannelHeat_RefusalToHeat_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "muse-channel-heat/muse-channel-heat.proto",
}

func init() {
	proto.RegisterFile("muse-channel-heat/muse-channel-heat.proto", fileDescriptor_muse_channel_heat_be1ae519f71845f1)
}

var fileDescriptor_muse_channel_heat_be1ae519f71845f1 = []byte{
	// 347 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x92, 0xe1, 0x4b, 0xc2, 0x40,
	0x18, 0xc6, 0x55, 0xc8, 0xe8, 0x15, 0xe1, 0x3c, 0xcb, 0x4c, 0xfa, 0x10, 0x2b, 0x2a, 0x25, 0x27,
	0x28, 0x7e, 0xed, 0x43, 0x41, 0x65, 0x10, 0x81, 0xb3, 0x2f, 0x11, 0x8c, 0x73, 0x9e, 0x6e, 0xb4,
	0xdd, 0xad, 0xdd, 0xbb, 0xfc, 0xff, 0xfa, 0xcb, 0x62, 0xe7, 0x20, 0xda, 0x06, 0x49, 0xdf, 0xc6,
	0xfb, 0x3c, 0xf7, 0x7b, 0x76, 0xef, 0x73, 0xd0, 0x0d, 0x62, 0xc5, 0xfb, 0x8e, 0xcb, 0x84, 0xe0,
	0x7e, 0xdf, 0xe5, 0x0c, 0x07, 0xb9, 0x89, 0x19, 0x46, 0x12, 0x25, 0x6d, 0x24, 0x82, 0x9d, 0x0a,
	0x76, 0x22, 0x18, 0x77, 0xd0, 0xb2, 0x38, 0x3e, 0x31, 0x4f, 0x58, 0x6b, 0x0f, 0x1d, 0xd7, 0x42,
	0x86, 0xb1, 0x9a, 0xf2, 0x0f, 0x7a, 0x05, 0x34, 0x60, 0x9e, 0xb0, 0x95, 0x9e, 0xdb, 0x4a, 0x0b,
	0xed, 0xf2, 0x49, 0xf9, 0xb2, 0x3e, 0x25, 0x41, 0xe6, 0x80, 0x71, 0x04, 0x87, 0x85, 0x1c, 0x15,
	0x1a, 0x8f, 0xd0, 0xb4, 0x90, 0x45, 0xf8, 0xc0, 0x19, 0xde, 0x6e, 0xb2, 0x13, 0xfe, 0x31, 0xec,
	0xa5, 0x7f, 0x32, 0x59, 0xa4, 0xd8, 0x9f, 0x01, 0x3d, 0x80, 0x2a, 0xb2, 0xb9, 0xed, 0x2d, 0xda,
	0x15, 0x2d, 0xed, 0x20, 0x9b, 0x4f, 0x16, 0x46, 0x0b, 0xf6, 0xf3, 0x2c, 0x15, 0x1a, 0xf7, 0x40,
	0xa6, 0x7c, 0x19, 0x2b, 0xe6, 0xcf, 0x64, 0xa2, 0xfd, 0x3b, 0xa0, 0x09, 0x8d, 0x0c, 0x48, 0x85,
	0xbd, 0x6b, 0x20, 0xd9, 0x9b, 0xd1, 0x06, 0xd4, 0x5f, 0xc4, 0xbb, 0x90, 0x6b, 0xb1, 0x19, 0x90,
	0x12, 0x05, 0xa8, 0xce, 0xe2, 0x48, 0x3c, 0x0b, 0x52, 0xa6, 0x35, 0xd8, 0xd5, 0xdf, 0xcb, 0x25,
	0xa9, 0x0c, 0xbf, 0x2a, 0x50, 0x4b, 0x93, 0x13, 0x26, 0x15, 0xd0, 0x2c, 0x58, 0x16, 0xed, 0x9a,
	0xb9, 0x7e, 0xcc, 0xe2, 0x72, 0x3a, 0xbd, 0x6d, 0xad, 0x2a, 0x34, 0x4a, 0x94, 0x03, 0xc9, 0x6e,
	0x8d, 0x9e, 0x17, 0x11, 0xf2, 0x35, 0x75, 0x2e, 0xb6, 0xf2, 0xe9, 0x98, 0x37, 0xa8, 0xff, 0xda,
	0x1d, 0x3d, 0x2d, 0x38, 0x9b, 0xad, 0xa9, 0x73, 0xf6, 0xb7, 0x29, 0xa1, 0xdf, 0x8c, 0x5f, 0x47,
	0x2b, 0xe9, 0x33, 0xb1, 0x32, 0xc7, 0x43, 0x44, 0xd3, 0x91, 0xc1, 0x40, 0xbf, 0x6a, 0x47, 0xfa,
	0x03, 0xc5, 0xa3, 0x4f, 0xcf, 0xe1, 0x2a, 0xff, 0xf2, 0xe7, 0x55, 0x6d, 0x1a, 0x7d, 0x07, 0x00,
	0x00, 0xff, 0xff, 0xc3, 0x3a, 0xe1, 0x5a, 0x27, 0x03, 0x00, 0x00,
}
