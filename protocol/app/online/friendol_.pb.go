// Code generated by protoc-gen-gogo.
// source: friendol_.proto
// DO NOT EDIT!

/*
	Package online is a generated protocol buffer package.

	It is generated from these files:
		friendol_.proto

	It has these top-level messages:
		ReportPlayingGameReq
		ReportPlayingGameResp
		FriendsDetail
		Nobles
		GetOfflineFriendsReq
		GetOfflineFriendsResp
		GetOnlineFriendsReq
		GetOnlineFriendsResp
		GetGroupOnlineCountReq
		GetGroupOnlineCountResp
		OnlineEventPushMsg
		UpdateFollowChannelAuthReq
		UpdateFollowChannelAuthResp
		GetFollowChannelAuthReq
		GetFollowChannelAuthResp
		CheckShowAddInviteCodeReq
		CheckShowAddInviteCodeResp
		CheckInviteFriendCodeReq
		CheckInviteFriendCodeResp
		OnlyTestxxxx
		FollowChannelInfo
		GetUserFollowChannelInfoReq
		GetUserFollowChannelInfoResp
		FollowLabelUpdate
*/
package online

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type EFollowChannelAuthSwitchType int32

const (
	EFollowChannelAuthSwitchType_ENUM_FollowChannelAuth_CLOSE             EFollowChannelAuthSwitchType = 0
	EFollowChannelAuthSwitchType_ENUM_FollowChannelAuth_ALL_ALLOW         EFollowChannelAuthSwitchType = 1
	EFollowChannelAuthSwitchType_ENUM_FollowChannelAuth_ONLY_FRIEND_ALLOW EFollowChannelAuthSwitchType = 2
	EFollowChannelAuthSwitchType_ENUM_FollowChannelAuth_ONLY_FANS_ALLOW   EFollowChannelAuthSwitchType = 3
)

var EFollowChannelAuthSwitchType_name = map[int32]string{
	0: "ENUM_FollowChannelAuth_CLOSE",
	1: "ENUM_FollowChannelAuth_ALL_ALLOW",
	2: "ENUM_FollowChannelAuth_ONLY_FRIEND_ALLOW",
	3: "ENUM_FollowChannelAuth_ONLY_FANS_ALLOW",
}
var EFollowChannelAuthSwitchType_value = map[string]int32{
	"ENUM_FollowChannelAuth_CLOSE":             0,
	"ENUM_FollowChannelAuth_ALL_ALLOW":         1,
	"ENUM_FollowChannelAuth_ONLY_FRIEND_ALLOW": 2,
	"ENUM_FollowChannelAuth_ONLY_FANS_ALLOW":   3,
}

func (x EFollowChannelAuthSwitchType) Enum() *EFollowChannelAuthSwitchType {
	p := new(EFollowChannelAuthSwitchType)
	*p = x
	return p
}
func (x EFollowChannelAuthSwitchType) String() string {
	return proto.EnumName(EFollowChannelAuthSwitchType_name, int32(x))
}
func (x *EFollowChannelAuthSwitchType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EFollowChannelAuthSwitchType_value, data, "EFollowChannelAuthSwitchType")
	if err != nil {
		return err
	}
	*x = EFollowChannelAuthSwitchType(value)
	return nil
}
func (EFollowChannelAuthSwitchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorFriendol_, []int{0}
}

type OnlineEventPushMsg_OnlinePushType int32

const (
	OnlineEventPushMsg_OnlinePushTypeDefault  OnlineEventPushMsg_OnlinePushType = 0
	OnlineEventPushMsg_OnlinePushTypeUnfollow OnlineEventPushMsg_OnlinePushType = 1
)

var OnlineEventPushMsg_OnlinePushType_name = map[int32]string{
	0: "OnlinePushTypeDefault",
	1: "OnlinePushTypeUnfollow",
}
var OnlineEventPushMsg_OnlinePushType_value = map[string]int32{
	"OnlinePushTypeDefault":  0,
	"OnlinePushTypeUnfollow": 1,
}

func (x OnlineEventPushMsg_OnlinePushType) Enum() *OnlineEventPushMsg_OnlinePushType {
	p := new(OnlineEventPushMsg_OnlinePushType)
	*p = x
	return p
}
func (x OnlineEventPushMsg_OnlinePushType) String() string {
	return proto.EnumName(OnlineEventPushMsg_OnlinePushType_name, int32(x))
}
func (x *OnlineEventPushMsg_OnlinePushType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(OnlineEventPushMsg_OnlinePushType_value, data, "OnlineEventPushMsg_OnlinePushType")
	if err != nil {
		return err
	}
	*x = OnlineEventPushMsg_OnlinePushType(value)
	return nil
}
func (OnlineEventPushMsg_OnlinePushType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorFriendol_, []int{10, 0}
}

// 上报在线用户正在玩的游戏
type ReportPlayingGameReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	PkgName   string      `protobuf:"bytes,2,req,name=pkg_name,json=pkgName" json:"pkg_name"`
	GameState uint32      `protobuf:"varint,3,opt,name=game_state,json=gameState" json:"game_state"`
}

func (m *ReportPlayingGameReq) Reset()                    { *m = ReportPlayingGameReq{} }
func (m *ReportPlayingGameReq) String() string            { return proto.CompactTextString(m) }
func (*ReportPlayingGameReq) ProtoMessage()               {}
func (*ReportPlayingGameReq) Descriptor() ([]byte, []int) { return fileDescriptorFriendol_, []int{0} }

func (m *ReportPlayingGameReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportPlayingGameReq) GetPkgName() string {
	if m != nil {
		return m.PkgName
	}
	return ""
}

func (m *ReportPlayingGameReq) GetGameState() uint32 {
	if m != nil {
		return m.GameState
	}
	return 0
}

type ReportPlayingGameResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *ReportPlayingGameResp) Reset()                    { *m = ReportPlayingGameResp{} }
func (m *ReportPlayingGameResp) String() string            { return proto.CompactTextString(m) }
func (*ReportPlayingGameResp) ProtoMessage()               {}
func (*ReportPlayingGameResp) Descriptor() ([]byte, []int) { return fileDescriptorFriendol_, []int{1} }

func (m *ReportPlayingGameResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type FriendsDetail struct {
	Uid                 uint32  `protobuf:"varint,1,req,name=uid" json:"uid"`
	OlStatus            uint32  `protobuf:"varint,2,req,name=ol_status,json=olStatus" json:"ol_status"`
	LastOlTime          uint32  `protobuf:"varint,3,req,name=last_ol_time,json=lastOlTime" json:"last_ol_time"`
	Account             string  `protobuf:"bytes,4,req,name=account" json:"account"`
	RoomId              uint32  `protobuf:"varint,5,opt,name=room_id,json=roomId" json:"room_id"`
	GameName            string  `protobuf:"bytes,6,opt,name=game_name,json=gameName" json:"game_name"`
	RoomType            uint32  `protobuf:"varint,7,opt,name=room_type,json=roomType" json:"room_type"`
	RoomIsPwd           bool    `protobuf:"varint,8,opt,name=room_is_pwd,json=roomIsPwd" json:"room_is_pwd"`
	NickName            string  `protobuf:"bytes,9,opt,name=nick_name,json=nickName" json:"nick_name"`
	FollowLabelImg      string  `protobuf:"bytes,19,opt,name=follow_label_img,json=followLabelImg" json:"follow_label_img"`
	FollowLabelText     string  `protobuf:"bytes,20,opt,name=follow_label_text,json=followLabelText" json:"follow_label_text"`
	FindPlayingText     string  `protobuf:"bytes,21,opt,name=find_playing_text,json=findPlayingText" json:"find_playing_text"`
	FindPlayingImg      string  `protobuf:"bytes,22,opt,name=find_playing_img,json=findPlayingImg" json:"find_playing_img"`
	NobleInfo           *Nobles `protobuf:"bytes,30,opt,name=noble_info,json=nobleInfo" json:"noble_info,omitempty"`
	IsLive              bool    `protobuf:"varint,31,opt,name=is_live,json=isLive" json:"is_live"`
	LastLiveTime        uint32  `protobuf:"varint,32,opt,name=last_live_time,json=lastLiveTime" json:"last_live_time"`
	IsPking             bool    `protobuf:"varint,33,opt,name=is_pking,json=isPking" json:"is_pking"`
	IsPerforming        bool    `protobuf:"varint,34,opt,name=is_performing,json=isPerforming" json:"is_performing"`
	RoomLevel           uint32  `protobuf:"varint,35,opt,name=room_level,json=roomLevel" json:"room_level"`
	FollowCnt           uint32  `protobuf:"varint,36,opt,name=follow_cnt,json=followCnt" json:"follow_cnt"`
	IsMusicNextDirector bool    `protobuf:"varint,37,opt,name=is_music_next_director,json=isMusicNextDirector" json:"is_music_next_director"`
	// 个人认证标识
	CertType        uint32   `protobuf:"varint,38,opt,name=cert_type,json=certType" json:"cert_type"`
	Icon            string   `protobuf:"bytes,39,opt,name=icon" json:"icon"`
	Text            string   `protobuf:"bytes,40,opt,name=text" json:"text"`
	Color           []string `protobuf:"bytes,41,rep,name=color" json:"color,omitempty"`
	TextShadowColor string   `protobuf:"bytes,42,opt,name=text_shadow_color,json=textShadowColor" json:"text_shadow_color"`
	RingColor       []string `protobuf:"bytes,43,rep,name=ring_color,json=ringColor" json:"ring_color,omitempty"`
	RingColorExtra  []string `protobuf:"bytes,44,rep,name=ring_color_extra,json=ringColorExtra" json:"ring_color_extra,omitempty"`
	// 个人铭牌
	NameplateDetailInfos []*ga.NameplateDetailInfo `protobuf:"bytes,45,rep,name=nameplate_detail_infos,json=nameplateDetailInfos" json:"nameplate_detail_infos,omitempty"`
	// 房间是否发布
	IsPublishing bool   `protobuf:"varint,46,opt,name=is_publishing,json=isPublishing" json:"is_publishing"`
	Sex          uint32 `protobuf:"varint,47,opt,name=sex" json:"sex"`
	RecentIntro  string `protobuf:"bytes,48,opt,name=recent_intro,json=recentIntro" json:"recent_intro"`
}

func (m *FriendsDetail) Reset()                    { *m = FriendsDetail{} }
func (m *FriendsDetail) String() string            { return proto.CompactTextString(m) }
func (*FriendsDetail) ProtoMessage()               {}
func (*FriendsDetail) Descriptor() ([]byte, []int) { return fileDescriptorFriendol_, []int{2} }

func (m *FriendsDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FriendsDetail) GetOlStatus() uint32 {
	if m != nil {
		return m.OlStatus
	}
	return 0
}

func (m *FriendsDetail) GetLastOlTime() uint32 {
	if m != nil {
		return m.LastOlTime
	}
	return 0
}

func (m *FriendsDetail) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *FriendsDetail) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

func (m *FriendsDetail) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *FriendsDetail) GetRoomType() uint32 {
	if m != nil {
		return m.RoomType
	}
	return 0
}

func (m *FriendsDetail) GetRoomIsPwd() bool {
	if m != nil {
		return m.RoomIsPwd
	}
	return false
}

func (m *FriendsDetail) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *FriendsDetail) GetFollowLabelImg() string {
	if m != nil {
		return m.FollowLabelImg
	}
	return ""
}

func (m *FriendsDetail) GetFollowLabelText() string {
	if m != nil {
		return m.FollowLabelText
	}
	return ""
}

func (m *FriendsDetail) GetFindPlayingText() string {
	if m != nil {
		return m.FindPlayingText
	}
	return ""
}

func (m *FriendsDetail) GetFindPlayingImg() string {
	if m != nil {
		return m.FindPlayingImg
	}
	return ""
}

func (m *FriendsDetail) GetNobleInfo() *Nobles {
	if m != nil {
		return m.NobleInfo
	}
	return nil
}

func (m *FriendsDetail) GetIsLive() bool {
	if m != nil {
		return m.IsLive
	}
	return false
}

func (m *FriendsDetail) GetLastLiveTime() uint32 {
	if m != nil {
		return m.LastLiveTime
	}
	return 0
}

func (m *FriendsDetail) GetIsPking() bool {
	if m != nil {
		return m.IsPking
	}
	return false
}

func (m *FriendsDetail) GetIsPerforming() bool {
	if m != nil {
		return m.IsPerforming
	}
	return false
}

func (m *FriendsDetail) GetRoomLevel() uint32 {
	if m != nil {
		return m.RoomLevel
	}
	return 0
}

func (m *FriendsDetail) GetFollowCnt() uint32 {
	if m != nil {
		return m.FollowCnt
	}
	return 0
}

func (m *FriendsDetail) GetIsMusicNextDirector() bool {
	if m != nil {
		return m.IsMusicNextDirector
	}
	return false
}

func (m *FriendsDetail) GetCertType() uint32 {
	if m != nil {
		return m.CertType
	}
	return 0
}

func (m *FriendsDetail) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *FriendsDetail) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *FriendsDetail) GetColor() []string {
	if m != nil {
		return m.Color
	}
	return nil
}

func (m *FriendsDetail) GetTextShadowColor() string {
	if m != nil {
		return m.TextShadowColor
	}
	return ""
}

func (m *FriendsDetail) GetRingColor() []string {
	if m != nil {
		return m.RingColor
	}
	return nil
}

func (m *FriendsDetail) GetRingColorExtra() []string {
	if m != nil {
		return m.RingColorExtra
	}
	return nil
}

func (m *FriendsDetail) GetNameplateDetailInfos() []*ga.NameplateDetailInfo {
	if m != nil {
		return m.NameplateDetailInfos
	}
	return nil
}

func (m *FriendsDetail) GetIsPublishing() bool {
	if m != nil {
		return m.IsPublishing
	}
	return false
}

func (m *FriendsDetail) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *FriendsDetail) GetRecentIntro() string {
	if m != nil {
		return m.RecentIntro
	}
	return ""
}

// 贵族相关字段
type Nobles struct {
	Value     uint64 `protobuf:"varint,1,opt,name=value" json:"value"`
	KeepValue uint64 `protobuf:"varint,2,opt,name=keep_value,json=keepValue" json:"keep_value"`
	Level     uint32 `protobuf:"varint,3,opt,name=level" json:"level"`
	CycleTs   uint32 `protobuf:"varint,4,opt,name=cycle_ts,json=cycleTs" json:"cycle_ts"`
}

func (m *Nobles) Reset()                    { *m = Nobles{} }
func (m *Nobles) String() string            { return proto.CompactTextString(m) }
func (*Nobles) ProtoMessage()               {}
func (*Nobles) Descriptor() ([]byte, []int) { return fileDescriptorFriendol_, []int{3} }

func (m *Nobles) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *Nobles) GetKeepValue() uint64 {
	if m != nil {
		return m.KeepValue
	}
	return 0
}

func (m *Nobles) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *Nobles) GetCycleTs() uint32 {
	if m != nil {
		return m.CycleTs
	}
	return 0
}

// 获取用户的离线好友列表
type GetOfflineFriendsReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetOfflineFriendsReq) Reset()                    { *m = GetOfflineFriendsReq{} }
func (m *GetOfflineFriendsReq) String() string            { return proto.CompactTextString(m) }
func (*GetOfflineFriendsReq) ProtoMessage()               {}
func (*GetOfflineFriendsReq) Descriptor() ([]byte, []int) { return fileDescriptorFriendol_, []int{4} }

func (m *GetOfflineFriendsReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetOfflineFriendsResp struct {
	BaseResp    *ga.BaseResp     `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	OfflineList []*FriendsDetail `protobuf:"bytes,2,rep,name=offline_list,json=offlineList" json:"offline_list,omitempty"`
}

func (m *GetOfflineFriendsResp) Reset()                    { *m = GetOfflineFriendsResp{} }
func (m *GetOfflineFriendsResp) String() string            { return proto.CompactTextString(m) }
func (*GetOfflineFriendsResp) ProtoMessage()               {}
func (*GetOfflineFriendsResp) Descriptor() ([]byte, []int) { return fileDescriptorFriendol_, []int{5} }

func (m *GetOfflineFriendsResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetOfflineFriendsResp) GetOfflineList() []*FriendsDetail {
	if m != nil {
		return m.OfflineList
	}
	return nil
}

// 获取用户的在线好友列表
type GetOnlineFriendsReq struct {
	BaseReq               *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	IncludeFollowing      bool        `protobuf:"varint,2,opt,name=include_following,json=includeFollowing" json:"include_following"`
	ExtCheckOnlineUidlist []uint32    `protobuf:"varint,3,rep,name=ext_check_online_uidlist,json=extCheckOnlineUidlist" json:"ext_check_online_uidlist,omitempty"`
	EnterChannelCnt       bool        `protobuf:"varint,4,opt,name=enter_channel_cnt,json=enterChannelCnt" json:"enter_channel_cnt"`
	OnlyUseRecentInvite   bool        `protobuf:"varint,5,opt,name=only_use_recent_invite,json=onlyUseRecentInvite" json:"only_use_recent_invite"`
	IsMainPage            bool        `protobuf:"varint,6,opt,name=is_main_page,json=isMainPage" json:"is_main_page"`
}

func (m *GetOnlineFriendsReq) Reset()                    { *m = GetOnlineFriendsReq{} }
func (m *GetOnlineFriendsReq) String() string            { return proto.CompactTextString(m) }
func (*GetOnlineFriendsReq) ProtoMessage()               {}
func (*GetOnlineFriendsReq) Descriptor() ([]byte, []int) { return fileDescriptorFriendol_, []int{6} }

func (m *GetOnlineFriendsReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetOnlineFriendsReq) GetIncludeFollowing() bool {
	if m != nil {
		return m.IncludeFollowing
	}
	return false
}

func (m *GetOnlineFriendsReq) GetExtCheckOnlineUidlist() []uint32 {
	if m != nil {
		return m.ExtCheckOnlineUidlist
	}
	return nil
}

func (m *GetOnlineFriendsReq) GetEnterChannelCnt() bool {
	if m != nil {
		return m.EnterChannelCnt
	}
	return false
}

func (m *GetOnlineFriendsReq) GetOnlyUseRecentInvite() bool {
	if m != nil {
		return m.OnlyUseRecentInvite
	}
	return false
}

func (m *GetOnlineFriendsReq) GetIsMainPage() bool {
	if m != nil {
		return m.IsMainPage
	}
	return false
}

type GetOnlineFriendsResp struct {
	BaseResp            *ga.BaseResp     `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	OnlineList          []*FriendsDetail `protobuf:"bytes,2,rep,name=online_list,json=onlineList" json:"online_list,omitempty"`
	ExtCheckOfflineList []*FriendsDetail `protobuf:"bytes,3,rep,name=ext_check_offline_list,json=extCheckOfflineList" json:"ext_check_offline_list,omitempty"`
}

func (m *GetOnlineFriendsResp) Reset()                    { *m = GetOnlineFriendsResp{} }
func (m *GetOnlineFriendsResp) String() string            { return proto.CompactTextString(m) }
func (*GetOnlineFriendsResp) ProtoMessage()               {}
func (*GetOnlineFriendsResp) Descriptor() ([]byte, []int) { return fileDescriptorFriendol_, []int{7} }

func (m *GetOnlineFriendsResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetOnlineFriendsResp) GetOnlineList() []*FriendsDetail {
	if m != nil {
		return m.OnlineList
	}
	return nil
}

func (m *GetOnlineFriendsResp) GetExtCheckOfflineList() []*FriendsDetail {
	if m != nil {
		return m.ExtCheckOfflineList
	}
	return nil
}

// 获取指定群的在线人数信息
type GetGroupOnlineCountReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GroupId uint32      `protobuf:"varint,2,req,name=group_id,json=groupId" json:"group_id"`
}

func (m *GetGroupOnlineCountReq) Reset()                    { *m = GetGroupOnlineCountReq{} }
func (m *GetGroupOnlineCountReq) String() string            { return proto.CompactTextString(m) }
func (*GetGroupOnlineCountReq) ProtoMessage()               {}
func (*GetGroupOnlineCountReq) Descriptor() ([]byte, []int) { return fileDescriptorFriendol_, []int{8} }

func (m *GetGroupOnlineCountReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGroupOnlineCountReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetGroupOnlineCountResp struct {
	BaseResp   *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GroupId    uint32       `protobuf:"varint,2,req,name=group_id,json=groupId" json:"group_id"`
	OnlineCnt  uint32       `protobuf:"varint,3,opt,name=online_cnt,json=onlineCnt" json:"online_cnt"`
	OfflineCnt uint32       `protobuf:"varint,4,opt,name=offline_cnt,json=offlineCnt" json:"offline_cnt"`
}

func (m *GetGroupOnlineCountResp) Reset()                    { *m = GetGroupOnlineCountResp{} }
func (m *GetGroupOnlineCountResp) String() string            { return proto.CompactTextString(m) }
func (*GetGroupOnlineCountResp) ProtoMessage()               {}
func (*GetGroupOnlineCountResp) Descriptor() ([]byte, []int) { return fileDescriptorFriendol_, []int{9} }

func (m *GetGroupOnlineCountResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGroupOnlineCountResp) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GetGroupOnlineCountResp) GetOnlineCnt() uint32 {
	if m != nil {
		return m.OnlineCnt
	}
	return 0
}

func (m *GetGroupOnlineCountResp) GetOfflineCnt() uint32 {
	if m != nil {
		return m.OfflineCnt
	}
	return 0
}

// 好友在线事件的推送消息
type OnlineEventPushMsg struct {
	Uid                 uint32                            `protobuf:"varint,1,req,name=uid" json:"uid"`
	OlStatus            uint32                            `protobuf:"varint,2,req,name=ol_status,json=olStatus" json:"ol_status"`
	ChannelId           uint32                            `protobuf:"varint,3,opt,name=channel_id,json=channelId" json:"channel_id"`
	GameName            string                            `protobuf:"bytes,4,opt,name=game_name,json=gameName" json:"game_name"`
	RoomType            uint32                            `protobuf:"varint,5,opt,name=room_type,json=roomType" json:"room_type"`
	ChannelIdV2         uint32                            `protobuf:"varint,8,opt,name=channel_id_v2,json=channelIdV2" json:"channel_id_v2"`
	RoomTypeV2          uint32                            `protobuf:"varint,9,opt,name=room_type_v2,json=roomTypeV2" json:"room_type_v2"`
	ChannelIsPwd        bool                              `protobuf:"varint,10,opt,name=channel_is_pwd,json=channelIsPwd" json:"channel_is_pwd"`
	Account             string                            `protobuf:"bytes,11,opt,name=account" json:"account"`
	NickName            string                            `protobuf:"bytes,12,opt,name=nick_name,json=nickName" json:"nick_name"`
	PushType            OnlineEventPushMsg_OnlinePushType `protobuf:"varint,13,opt,name=push_type,json=pushType,enum=ga.OnlineEventPushMsg_OnlinePushType" json:"push_type"`
	FindPlayingText     string                            `protobuf:"bytes,14,opt,name=find_playing_text,json=findPlayingText" json:"find_playing_text"`
	FindPlayingImg      string                            `protobuf:"bytes,15,opt,name=find_playing_img,json=findPlayingImg" json:"find_playing_img"`
	NobleInfo           *Nobles                           `protobuf:"bytes,30,opt,name=noble_info,json=nobleInfo" json:"noble_info,omitempty"`
	IsLive              bool                              `protobuf:"varint,31,opt,name=is_live,json=isLive" json:"is_live"`
	LastLiveTime        uint32                            `protobuf:"varint,32,opt,name=last_live_time,json=lastLiveTime" json:"last_live_time"`
	IsPking             bool                              `protobuf:"varint,33,opt,name=is_pking,json=isPking" json:"is_pking"`
	IsPerforming        bool                              `protobuf:"varint,34,opt,name=is_performing,json=isPerforming" json:"is_performing"`
	RoomLevel           uint32                            `protobuf:"varint,35,opt,name=room_level,json=roomLevel" json:"room_level"`
	IsMusicNextDirector bool                              `protobuf:"varint,36,opt,name=is_music_next_director,json=isMusicNextDirector" json:"is_music_next_director"`
	// 个人认证标识
	CertType        uint32   `protobuf:"varint,37,opt,name=cert_type,json=certType" json:"cert_type"`
	Icon            string   `protobuf:"bytes,38,opt,name=icon" json:"icon"`
	Text            string   `protobuf:"bytes,39,opt,name=text" json:"text"`
	Color           []string `protobuf:"bytes,40,rep,name=color" json:"color,omitempty"`
	TextShadowColor string   `protobuf:"bytes,41,opt,name=text_shadow_color,json=textShadowColor" json:"text_shadow_color"`
	RingColor       []string `protobuf:"bytes,42,rep,name=ring_color,json=ringColor" json:"ring_color,omitempty"`
	RingColorExtra  []string `protobuf:"bytes,43,rep,name=ring_color_extra,json=ringColorExtra" json:"ring_color_extra,omitempty"`
	// 个人铭牌
	NameplateDetailInfos []*ga.NameplateDetailInfo `protobuf:"bytes,44,rep,name=nameplate_detail_infos,json=nameplateDetailInfos" json:"nameplate_detail_infos,omitempty"`
	// 房间是否发布
	IsPublishing bool   `protobuf:"varint,45,opt,name=is_publishing,json=isPublishing" json:"is_publishing"`
	Sex          uint32 `protobuf:"varint,46,opt,name=sex" json:"sex"`
}

func (m *OnlineEventPushMsg) Reset()                    { *m = OnlineEventPushMsg{} }
func (m *OnlineEventPushMsg) String() string            { return proto.CompactTextString(m) }
func (*OnlineEventPushMsg) ProtoMessage()               {}
func (*OnlineEventPushMsg) Descriptor() ([]byte, []int) { return fileDescriptorFriendol_, []int{10} }

func (m *OnlineEventPushMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OnlineEventPushMsg) GetOlStatus() uint32 {
	if m != nil {
		return m.OlStatus
	}
	return 0
}

func (m *OnlineEventPushMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OnlineEventPushMsg) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *OnlineEventPushMsg) GetRoomType() uint32 {
	if m != nil {
		return m.RoomType
	}
	return 0
}

func (m *OnlineEventPushMsg) GetChannelIdV2() uint32 {
	if m != nil {
		return m.ChannelIdV2
	}
	return 0
}

func (m *OnlineEventPushMsg) GetRoomTypeV2() uint32 {
	if m != nil {
		return m.RoomTypeV2
	}
	return 0
}

func (m *OnlineEventPushMsg) GetChannelIsPwd() bool {
	if m != nil {
		return m.ChannelIsPwd
	}
	return false
}

func (m *OnlineEventPushMsg) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *OnlineEventPushMsg) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *OnlineEventPushMsg) GetPushType() OnlineEventPushMsg_OnlinePushType {
	if m != nil {
		return m.PushType
	}
	return OnlineEventPushMsg_OnlinePushTypeDefault
}

func (m *OnlineEventPushMsg) GetFindPlayingText() string {
	if m != nil {
		return m.FindPlayingText
	}
	return ""
}

func (m *OnlineEventPushMsg) GetFindPlayingImg() string {
	if m != nil {
		return m.FindPlayingImg
	}
	return ""
}

func (m *OnlineEventPushMsg) GetNobleInfo() *Nobles {
	if m != nil {
		return m.NobleInfo
	}
	return nil
}

func (m *OnlineEventPushMsg) GetIsLive() bool {
	if m != nil {
		return m.IsLive
	}
	return false
}

func (m *OnlineEventPushMsg) GetLastLiveTime() uint32 {
	if m != nil {
		return m.LastLiveTime
	}
	return 0
}

func (m *OnlineEventPushMsg) GetIsPking() bool {
	if m != nil {
		return m.IsPking
	}
	return false
}

func (m *OnlineEventPushMsg) GetIsPerforming() bool {
	if m != nil {
		return m.IsPerforming
	}
	return false
}

func (m *OnlineEventPushMsg) GetRoomLevel() uint32 {
	if m != nil {
		return m.RoomLevel
	}
	return 0
}

func (m *OnlineEventPushMsg) GetIsMusicNextDirector() bool {
	if m != nil {
		return m.IsMusicNextDirector
	}
	return false
}

func (m *OnlineEventPushMsg) GetCertType() uint32 {
	if m != nil {
		return m.CertType
	}
	return 0
}

func (m *OnlineEventPushMsg) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *OnlineEventPushMsg) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *OnlineEventPushMsg) GetColor() []string {
	if m != nil {
		return m.Color
	}
	return nil
}

func (m *OnlineEventPushMsg) GetTextShadowColor() string {
	if m != nil {
		return m.TextShadowColor
	}
	return ""
}

func (m *OnlineEventPushMsg) GetRingColor() []string {
	if m != nil {
		return m.RingColor
	}
	return nil
}

func (m *OnlineEventPushMsg) GetRingColorExtra() []string {
	if m != nil {
		return m.RingColorExtra
	}
	return nil
}

func (m *OnlineEventPushMsg) GetNameplateDetailInfos() []*ga.NameplateDetailInfo {
	if m != nil {
		return m.NameplateDetailInfos
	}
	return nil
}

func (m *OnlineEventPushMsg) GetIsPublishing() bool {
	if m != nil {
		return m.IsPublishing
	}
	return false
}

func (m *OnlineEventPushMsg) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

// 是否打开 跟随进房开关
type UpdateFollowChannelAuthReq struct {
	BaseReq          *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uid              uint32      `protobuf:"varint,2,req,name=uid" json:"uid"`
	FollowAuth       bool        `protobuf:"varint,3,req,name=follow_auth,json=followAuth" json:"follow_auth"`
	FollowAuthSwitch uint32      `protobuf:"varint,4,opt,name=follow_auth_switch,json=followAuthSwitch" json:"follow_auth_switch"`
}

func (m *UpdateFollowChannelAuthReq) Reset()         { *m = UpdateFollowChannelAuthReq{} }
func (m *UpdateFollowChannelAuthReq) String() string { return proto.CompactTextString(m) }
func (*UpdateFollowChannelAuthReq) ProtoMessage()    {}
func (*UpdateFollowChannelAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendol_, []int{11}
}

func (m *UpdateFollowChannelAuthReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpdateFollowChannelAuthReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateFollowChannelAuthReq) GetFollowAuth() bool {
	if m != nil {
		return m.FollowAuth
	}
	return false
}

func (m *UpdateFollowChannelAuthReq) GetFollowAuthSwitch() uint32 {
	if m != nil {
		return m.FollowAuthSwitch
	}
	return 0
}

type UpdateFollowChannelAuthResp struct {
	BaseResp         *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	FollowAuthSwitch uint32       `protobuf:"varint,2,opt,name=follow_auth_switch,json=followAuthSwitch" json:"follow_auth_switch"`
}

func (m *UpdateFollowChannelAuthResp) Reset()         { *m = UpdateFollowChannelAuthResp{} }
func (m *UpdateFollowChannelAuthResp) String() string { return proto.CompactTextString(m) }
func (*UpdateFollowChannelAuthResp) ProtoMessage()    {}
func (*UpdateFollowChannelAuthResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendol_, []int{12}
}

func (m *UpdateFollowChannelAuthResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UpdateFollowChannelAuthResp) GetFollowAuthSwitch() uint32 {
	if m != nil {
		return m.FollowAuthSwitch
	}
	return 0
}

type GetFollowChannelAuthReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uid     uint32      `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *GetFollowChannelAuthReq) Reset()         { *m = GetFollowChannelAuthReq{} }
func (m *GetFollowChannelAuthReq) String() string { return proto.CompactTextString(m) }
func (*GetFollowChannelAuthReq) ProtoMessage()    {}
func (*GetFollowChannelAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendol_, []int{13}
}

func (m *GetFollowChannelAuthReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFollowChannelAuthReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetFollowChannelAuthResp struct {
	BaseResp         *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	FollowAuth       bool         `protobuf:"varint,2,req,name=follow_auth,json=followAuth" json:"follow_auth"`
	FollowAuthSwitch uint32       `protobuf:"varint,3,opt,name=follow_auth_switch,json=followAuthSwitch" json:"follow_auth_switch"`
}

func (m *GetFollowChannelAuthResp) Reset()         { *m = GetFollowChannelAuthResp{} }
func (m *GetFollowChannelAuthResp) String() string { return proto.CompactTextString(m) }
func (*GetFollowChannelAuthResp) ProtoMessage()    {}
func (*GetFollowChannelAuthResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendol_, []int{14}
}

func (m *GetFollowChannelAuthResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFollowChannelAuthResp) GetFollowAuth() bool {
	if m != nil {
		return m.FollowAuth
	}
	return false
}

func (m *GetFollowChannelAuthResp) GetFollowAuthSwitch() uint32 {
	if m != nil {
		return m.FollowAuthSwitch
	}
	return 0
}

// 是否显示 输入邀请码
type CheckShowAddInviteCodeReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uid     uint32      `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *CheckShowAddInviteCodeReq) Reset()         { *m = CheckShowAddInviteCodeReq{} }
func (m *CheckShowAddInviteCodeReq) String() string { return proto.CompactTextString(m) }
func (*CheckShowAddInviteCodeReq) ProtoMessage()    {}
func (*CheckShowAddInviteCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendol_, []int{15}
}

func (m *CheckShowAddInviteCodeReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckShowAddInviteCodeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckShowAddInviteCodeResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	IsShow   bool         `protobuf:"varint,2,req,name=is_show,json=isShow" json:"is_show"`
}

func (m *CheckShowAddInviteCodeResp) Reset()         { *m = CheckShowAddInviteCodeResp{} }
func (m *CheckShowAddInviteCodeResp) String() string { return proto.CompactTextString(m) }
func (*CheckShowAddInviteCodeResp) ProtoMessage()    {}
func (*CheckShowAddInviteCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendol_, []int{16}
}

func (m *CheckShowAddInviteCodeResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckShowAddInviteCodeResp) GetIsShow() bool {
	if m != nil {
		return m.IsShow
	}
	return false
}

// 邀请好友 邀请码验证
type CheckInviteFriendCodeReq struct {
	BaseReq        *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uid            uint32      `protobuf:"varint,2,req,name=uid" json:"uid"`
	PackageType    uint32      `protobuf:"varint,3,opt,name=package_type,json=packageType" json:"package_type"`
	InviteCode     string      `protobuf:"bytes,4,req,name=invite_code,json=inviteCode" json:"invite_code"`
	OriginChannel  string      `protobuf:"bytes,5,opt,name=origin_channel,json=originChannel" json:"origin_channel"`
	CurrentChannel string      `protobuf:"bytes,6,opt,name=current_channel,json=currentChannel" json:"current_channel"`
}

func (m *CheckInviteFriendCodeReq) Reset()         { *m = CheckInviteFriendCodeReq{} }
func (m *CheckInviteFriendCodeReq) String() string { return proto.CompactTextString(m) }
func (*CheckInviteFriendCodeReq) ProtoMessage()    {}
func (*CheckInviteFriendCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendol_, []int{17}
}

func (m *CheckInviteFriendCodeReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckInviteFriendCodeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckInviteFriendCodeReq) GetPackageType() uint32 {
	if m != nil {
		return m.PackageType
	}
	return 0
}

func (m *CheckInviteFriendCodeReq) GetInviteCode() string {
	if m != nil {
		return m.InviteCode
	}
	return ""
}

func (m *CheckInviteFriendCodeReq) GetOriginChannel() string {
	if m != nil {
		return m.OriginChannel
	}
	return ""
}

func (m *CheckInviteFriendCodeReq) GetCurrentChannel() string {
	if m != nil {
		return m.CurrentChannel
	}
	return ""
}

type CheckInviteFriendCodeResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	IsOk     bool         `protobuf:"varint,2,req,name=is_ok,json=isOk" json:"is_ok"`
	Lives    uint32       `protobuf:"varint,3,req,name=lives" json:"lives"`
}

func (m *CheckInviteFriendCodeResp) Reset()         { *m = CheckInviteFriendCodeResp{} }
func (m *CheckInviteFriendCodeResp) String() string { return proto.CompactTextString(m) }
func (*CheckInviteFriendCodeResp) ProtoMessage()    {}
func (*CheckInviteFriendCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendol_, []int{18}
}

func (m *CheckInviteFriendCodeResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckInviteFriendCodeResp) GetIsOk() bool {
	if m != nil {
		return m.IsOk
	}
	return false
}

func (m *CheckInviteFriendCodeResp) GetLives() uint32 {
	if m != nil {
		return m.Lives
	}
	return 0
}

type OnlyTestxxxx struct {
	A uint32 `protobuf:"varint,1,opt,name=a" json:"a"`
	B uint32 `protobuf:"varint,2,opt,name=b" json:"b"`
}

func (m *OnlyTestxxxx) Reset()                    { *m = OnlyTestxxxx{} }
func (m *OnlyTestxxxx) String() string            { return proto.CompactTextString(m) }
func (*OnlyTestxxxx) ProtoMessage()               {}
func (*OnlyTestxxxx) Descriptor() ([]byte, []int) { return fileDescriptorFriendol_, []int{19} }

func (m *OnlyTestxxxx) GetA() uint32 {
	if m != nil {
		return m.A
	}
	return 0
}

func (m *OnlyTestxxxx) GetB() uint32 {
	if m != nil {
		return m.B
	}
	return 0
}

// 用户当前的支持跟随的房间信息
type FollowChannelInfo struct {
	ChannelId           uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId" json:"channel_id"`
	ChannelType         uint32 `protobuf:"varint,2,opt,name=channel_type,json=channelType" json:"channel_type"`
	IsLock              bool   `protobuf:"varint,3,opt,name=is_lock,json=isLock" json:"is_lock"`
	FindPlayingText     string `protobuf:"bytes,4,opt,name=find_playing_text,json=findPlayingText" json:"find_playing_text"`
	FindPlayingImg      string `protobuf:"bytes,5,opt,name=find_playing_img,json=findPlayingImg" json:"find_playing_img"`
	FindPlayingLongText string `protobuf:"bytes,6,opt,name=find_playing_long_text,json=findPlayingLongText" json:"find_playing_long_text"`
	ChannelLevel        uint32 `protobuf:"varint,7,opt,name=channel_level,json=channelLevel" json:"channel_level"`
}

func (m *FollowChannelInfo) Reset()                    { *m = FollowChannelInfo{} }
func (m *FollowChannelInfo) String() string            { return proto.CompactTextString(m) }
func (*FollowChannelInfo) ProtoMessage()               {}
func (*FollowChannelInfo) Descriptor() ([]byte, []int) { return fileDescriptorFriendol_, []int{20} }

func (m *FollowChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *FollowChannelInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *FollowChannelInfo) GetIsLock() bool {
	if m != nil {
		return m.IsLock
	}
	return false
}

func (m *FollowChannelInfo) GetFindPlayingText() string {
	if m != nil {
		return m.FindPlayingText
	}
	return ""
}

func (m *FollowChannelInfo) GetFindPlayingImg() string {
	if m != nil {
		return m.FindPlayingImg
	}
	return ""
}

func (m *FollowChannelInfo) GetFindPlayingLongText() string {
	if m != nil {
		return m.FindPlayingLongText
	}
	return ""
}

func (m *FollowChannelInfo) GetChannelLevel() uint32 {
	if m != nil {
		return m.ChannelLevel
	}
	return 0
}

// 获取当前用户对于指定用户的房间跟随信息
// 用于查看其他人详情页时 显示对方的房间跟随信息
type GetUserFollowChannelInfoReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetUid uint32      `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
}

func (m *GetUserFollowChannelInfoReq) Reset()         { *m = GetUserFollowChannelInfoReq{} }
func (m *GetUserFollowChannelInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserFollowChannelInfoReq) ProtoMessage()    {}
func (*GetUserFollowChannelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendol_, []int{21}
}

func (m *GetUserFollowChannelInfoReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserFollowChannelInfoReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetUserFollowChannelInfoResp struct {
	BaseResp      *ga.BaseResp       `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	FollowChannel *FollowChannelInfo `protobuf:"bytes,2,opt,name=follow_channel,json=followChannel" json:"follow_channel,omitempty"`
}

func (m *GetUserFollowChannelInfoResp) Reset()         { *m = GetUserFollowChannelInfoResp{} }
func (m *GetUserFollowChannelInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserFollowChannelInfoResp) ProtoMessage()    {}
func (*GetUserFollowChannelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFriendol_, []int{22}
}

func (m *GetUserFollowChannelInfoResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserFollowChannelInfoResp) GetFollowChannel() *FollowChannelInfo {
	if m != nil {
		return m.FollowChannel
	}
	return nil
}

type FollowLabelUpdate struct {
	Uid             uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account         string `protobuf:"bytes,2,req,name=account" json:"account"`
	Timestamp       uint32 `protobuf:"varint,3,req,name=timestamp" json:"timestamp"`
	FollowLabelImg  string `protobuf:"bytes,4,opt,name=follow_label_img,json=followLabelImg" json:"follow_label_img"`
	FollowLabelText string `protobuf:"bytes,5,opt,name=follow_label_text,json=followLabelText" json:"follow_label_text"`
	GameName        string `protobuf:"bytes,6,opt,name=game_name,json=gameName" json:"game_name"`
}

func (m *FollowLabelUpdate) Reset()                    { *m = FollowLabelUpdate{} }
func (m *FollowLabelUpdate) String() string            { return proto.CompactTextString(m) }
func (*FollowLabelUpdate) ProtoMessage()               {}
func (*FollowLabelUpdate) Descriptor() ([]byte, []int) { return fileDescriptorFriendol_, []int{23} }

func (m *FollowLabelUpdate) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FollowLabelUpdate) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *FollowLabelUpdate) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *FollowLabelUpdate) GetFollowLabelImg() string {
	if m != nil {
		return m.FollowLabelImg
	}
	return ""
}

func (m *FollowLabelUpdate) GetFollowLabelText() string {
	if m != nil {
		return m.FollowLabelText
	}
	return ""
}

func (m *FollowLabelUpdate) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func init() {
	proto.RegisterType((*ReportPlayingGameReq)(nil), "ga.ReportPlayingGameReq")
	proto.RegisterType((*ReportPlayingGameResp)(nil), "ga.ReportPlayingGameResp")
	proto.RegisterType((*FriendsDetail)(nil), "ga.FriendsDetail")
	proto.RegisterType((*Nobles)(nil), "ga.Nobles")
	proto.RegisterType((*GetOfflineFriendsReq)(nil), "ga.GetOfflineFriendsReq")
	proto.RegisterType((*GetOfflineFriendsResp)(nil), "ga.GetOfflineFriendsResp")
	proto.RegisterType((*GetOnlineFriendsReq)(nil), "ga.GetOnlineFriendsReq")
	proto.RegisterType((*GetOnlineFriendsResp)(nil), "ga.GetOnlineFriendsResp")
	proto.RegisterType((*GetGroupOnlineCountReq)(nil), "ga.GetGroupOnlineCountReq")
	proto.RegisterType((*GetGroupOnlineCountResp)(nil), "ga.GetGroupOnlineCountResp")
	proto.RegisterType((*OnlineEventPushMsg)(nil), "ga.OnlineEventPushMsg")
	proto.RegisterType((*UpdateFollowChannelAuthReq)(nil), "ga.UpdateFollowChannelAuthReq")
	proto.RegisterType((*UpdateFollowChannelAuthResp)(nil), "ga.UpdateFollowChannelAuthResp")
	proto.RegisterType((*GetFollowChannelAuthReq)(nil), "ga.GetFollowChannelAuthReq")
	proto.RegisterType((*GetFollowChannelAuthResp)(nil), "ga.GetFollowChannelAuthResp")
	proto.RegisterType((*CheckShowAddInviteCodeReq)(nil), "ga.CheckShowAddInviteCodeReq")
	proto.RegisterType((*CheckShowAddInviteCodeResp)(nil), "ga.CheckShowAddInviteCodeResp")
	proto.RegisterType((*CheckInviteFriendCodeReq)(nil), "ga.CheckInviteFriendCodeReq")
	proto.RegisterType((*CheckInviteFriendCodeResp)(nil), "ga.CheckInviteFriendCodeResp")
	proto.RegisterType((*OnlyTestxxxx)(nil), "ga.OnlyTestxxxx")
	proto.RegisterType((*FollowChannelInfo)(nil), "ga.FollowChannelInfo")
	proto.RegisterType((*GetUserFollowChannelInfoReq)(nil), "ga.GetUserFollowChannelInfoReq")
	proto.RegisterType((*GetUserFollowChannelInfoResp)(nil), "ga.GetUserFollowChannelInfoResp")
	proto.RegisterType((*FollowLabelUpdate)(nil), "ga.FollowLabelUpdate")
	proto.RegisterEnum("ga.EFollowChannelAuthSwitchType", EFollowChannelAuthSwitchType_name, EFollowChannelAuthSwitchType_value)
	proto.RegisterEnum("ga.OnlineEventPushMsg_OnlinePushType", OnlineEventPushMsg_OnlinePushType_name, OnlineEventPushMsg_OnlinePushType_value)
}
func (m *ReportPlayingGameReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportPlayingGameReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.PkgName)))
	i += copy(dAtA[i:], m.PkgName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.GameState))
	return i, nil
}

func (m *ReportPlayingGameResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportPlayingGameResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *FriendsDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FriendsDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.OlStatus))
	dAtA[i] = 0x18
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.LastOlTime))
	dAtA[i] = 0x22
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x28
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.RoomId))
	dAtA[i] = 0x32
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	dAtA[i] = 0x38
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.RoomType))
	dAtA[i] = 0x40
	i++
	if m.RoomIsPwd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x4a
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.NickName)))
	i += copy(dAtA[i:], m.NickName)
	dAtA[i] = 0x9a
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.FollowLabelImg)))
	i += copy(dAtA[i:], m.FollowLabelImg)
	dAtA[i] = 0xa2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.FollowLabelText)))
	i += copy(dAtA[i:], m.FollowLabelText)
	dAtA[i] = 0xaa
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.FindPlayingText)))
	i += copy(dAtA[i:], m.FindPlayingText)
	dAtA[i] = 0xb2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.FindPlayingImg)))
	i += copy(dAtA[i:], m.FindPlayingImg)
	if m.NobleInfo != nil {
		dAtA[i] = 0xf2
		i++
		dAtA[i] = 0x1
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.NobleInfo.Size()))
		n3, err := m.NobleInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0xf8
	i++
	dAtA[i] = 0x1
	i++
	if m.IsLive {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.LastLiveTime))
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x2
	i++
	if m.IsPking {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x90
	i++
	dAtA[i] = 0x2
	i++
	if m.IsPerforming {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x98
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.RoomLevel))
	dAtA[i] = 0xa0
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.FollowCnt))
	dAtA[i] = 0xa8
	i++
	dAtA[i] = 0x2
	i++
	if m.IsMusicNextDirector {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0xb0
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.CertType))
	dAtA[i] = 0xba
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.Icon)))
	i += copy(dAtA[i:], m.Icon)
	dAtA[i] = 0xc2
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.Text)))
	i += copy(dAtA[i:], m.Text)
	if len(m.Color) > 0 {
		for _, s := range m.Color {
			dAtA[i] = 0xca
			i++
			dAtA[i] = 0x2
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0xd2
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.TextShadowColor)))
	i += copy(dAtA[i:], m.TextShadowColor)
	if len(m.RingColor) > 0 {
		for _, s := range m.RingColor {
			dAtA[i] = 0xda
			i++
			dAtA[i] = 0x2
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.RingColorExtra) > 0 {
		for _, s := range m.RingColorExtra {
			dAtA[i] = 0xe2
			i++
			dAtA[i] = 0x2
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.NameplateDetailInfos) > 0 {
		for _, msg := range m.NameplateDetailInfos {
			dAtA[i] = 0xea
			i++
			dAtA[i] = 0x2
			i++
			i = encodeVarintFriendol_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0xf0
	i++
	dAtA[i] = 0x2
	i++
	if m.IsPublishing {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0xf8
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x82
	i++
	dAtA[i] = 0x3
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.RecentIntro)))
	i += copy(dAtA[i:], m.RecentIntro)
	return i, nil
}

func (m *Nobles) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Nobles) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.Value))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.KeepValue))
	dAtA[i] = 0x18
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.Level))
	dAtA[i] = 0x20
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.CycleTs))
	return i, nil
}

func (m *GetOfflineFriendsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOfflineFriendsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseReq.Size()))
		n4, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *GetOfflineFriendsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOfflineFriendsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseResp.Size()))
		n5, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if len(m.OfflineList) > 0 {
		for _, msg := range m.OfflineList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintFriendol_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetOnlineFriendsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOnlineFriendsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseReq.Size()))
		n6, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x10
	i++
	if m.IncludeFollowing {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if len(m.ExtCheckOnlineUidlist) > 0 {
		for _, num := range m.ExtCheckOnlineUidlist {
			dAtA[i] = 0x18
			i++
			i = encodeVarintFriendol_(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x20
	i++
	if m.EnterChannelCnt {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	if m.OnlyUseRecentInvite {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x30
	i++
	if m.IsMainPage {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetOnlineFriendsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOnlineFriendsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseResp.Size()))
		n7, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if len(m.OnlineList) > 0 {
		for _, msg := range m.OnlineList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintFriendol_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.ExtCheckOfflineList) > 0 {
		for _, msg := range m.ExtCheckOfflineList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintFriendol_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGroupOnlineCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGroupOnlineCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseReq.Size()))
		n8, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.GroupId))
	return i, nil
}

func (m *GetGroupOnlineCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGroupOnlineCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseResp.Size()))
		n9, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.OnlineCnt))
	dAtA[i] = 0x20
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.OfflineCnt))
	return i, nil
}

func (m *OnlineEventPushMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OnlineEventPushMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.OlStatus))
	dAtA[i] = 0x18
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	dAtA[i] = 0x28
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.RoomType))
	dAtA[i] = 0x40
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.ChannelIdV2))
	dAtA[i] = 0x48
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.RoomTypeV2))
	dAtA[i] = 0x50
	i++
	if m.ChannelIsPwd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x5a
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x62
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.NickName)))
	i += copy(dAtA[i:], m.NickName)
	dAtA[i] = 0x68
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.PushType))
	dAtA[i] = 0x72
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.FindPlayingText)))
	i += copy(dAtA[i:], m.FindPlayingText)
	dAtA[i] = 0x7a
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.FindPlayingImg)))
	i += copy(dAtA[i:], m.FindPlayingImg)
	if m.NobleInfo != nil {
		dAtA[i] = 0xf2
		i++
		dAtA[i] = 0x1
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.NobleInfo.Size()))
		n10, err := m.NobleInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0xf8
	i++
	dAtA[i] = 0x1
	i++
	if m.IsLive {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.LastLiveTime))
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x2
	i++
	if m.IsPking {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x90
	i++
	dAtA[i] = 0x2
	i++
	if m.IsPerforming {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x98
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.RoomLevel))
	dAtA[i] = 0xa0
	i++
	dAtA[i] = 0x2
	i++
	if m.IsMusicNextDirector {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0xa8
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.CertType))
	dAtA[i] = 0xb2
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.Icon)))
	i += copy(dAtA[i:], m.Icon)
	dAtA[i] = 0xba
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.Text)))
	i += copy(dAtA[i:], m.Text)
	if len(m.Color) > 0 {
		for _, s := range m.Color {
			dAtA[i] = 0xc2
			i++
			dAtA[i] = 0x2
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0xca
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.TextShadowColor)))
	i += copy(dAtA[i:], m.TextShadowColor)
	if len(m.RingColor) > 0 {
		for _, s := range m.RingColor {
			dAtA[i] = 0xd2
			i++
			dAtA[i] = 0x2
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.RingColorExtra) > 0 {
		for _, s := range m.RingColorExtra {
			dAtA[i] = 0xda
			i++
			dAtA[i] = 0x2
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.NameplateDetailInfos) > 0 {
		for _, msg := range m.NameplateDetailInfos {
			dAtA[i] = 0xe2
			i++
			dAtA[i] = 0x2
			i++
			i = encodeVarintFriendol_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0xe8
	i++
	dAtA[i] = 0x2
	i++
	if m.IsPublishing {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0xf0
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.Sex))
	return i, nil
}

func (m *UpdateFollowChannelAuthReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateFollowChannelAuthReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseReq.Size()))
		n11, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	if m.FollowAuth {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.FollowAuthSwitch))
	return i, nil
}

func (m *UpdateFollowChannelAuthResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateFollowChannelAuthResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseResp.Size()))
		n12, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.FollowAuthSwitch))
	return i, nil
}

func (m *GetFollowChannelAuthReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFollowChannelAuthReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseReq.Size()))
		n13, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetFollowChannelAuthResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFollowChannelAuthResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseResp.Size()))
		n14, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	dAtA[i] = 0x10
	i++
	if m.FollowAuth {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.FollowAuthSwitch))
	return i, nil
}

func (m *CheckShowAddInviteCodeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckShowAddInviteCodeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseReq.Size()))
		n15, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *CheckShowAddInviteCodeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckShowAddInviteCodeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseResp.Size()))
		n16, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	dAtA[i] = 0x10
	i++
	if m.IsShow {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CheckInviteFriendCodeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckInviteFriendCodeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseReq.Size()))
		n17, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.PackageType))
	dAtA[i] = 0x22
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.InviteCode)))
	i += copy(dAtA[i:], m.InviteCode)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.OriginChannel)))
	i += copy(dAtA[i:], m.OriginChannel)
	dAtA[i] = 0x32
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.CurrentChannel)))
	i += copy(dAtA[i:], m.CurrentChannel)
	return i, nil
}

func (m *CheckInviteFriendCodeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckInviteFriendCodeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseResp.Size()))
		n18, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	dAtA[i] = 0x10
	i++
	if m.IsOk {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.Lives))
	return i, nil
}

func (m *OnlyTestxxxx) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OnlyTestxxxx) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.A))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.B))
	return i, nil
}

func (m *FollowChannelInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FollowChannelInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x18
	i++
	if m.IsLock {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x22
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.FindPlayingText)))
	i += copy(dAtA[i:], m.FindPlayingText)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.FindPlayingImg)))
	i += copy(dAtA[i:], m.FindPlayingImg)
	dAtA[i] = 0x32
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.FindPlayingLongText)))
	i += copy(dAtA[i:], m.FindPlayingLongText)
	dAtA[i] = 0x38
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.ChannelLevel))
	return i, nil
}

func (m *GetUserFollowChannelInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserFollowChannelInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseReq.Size()))
		n19, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.TargetUid))
	return i, nil
}

func (m *GetUserFollowChannelInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserFollowChannelInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.BaseResp.Size()))
		n20, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	if m.FollowChannel != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintFriendol_(dAtA, i, uint64(m.FollowChannel.Size()))
		n21, err := m.FollowChannel.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	return i, nil
}

func (m *FollowLabelUpdate) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FollowLabelUpdate) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x18
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x22
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.FollowLabelImg)))
	i += copy(dAtA[i:], m.FollowLabelImg)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.FollowLabelText)))
	i += copy(dAtA[i:], m.FollowLabelText)
	dAtA[i] = 0x32
	i++
	i = encodeVarintFriendol_(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	return i, nil
}

func encodeFixed64Friendol_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Friendol_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintFriendol_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ReportPlayingGameReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	l = len(m.PkgName)
	n += 1 + l + sovFriendol_(uint64(l))
	n += 1 + sovFriendol_(uint64(m.GameState))
	return n
}

func (m *ReportPlayingGameResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	return n
}

func (m *FriendsDetail) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendol_(uint64(m.Uid))
	n += 1 + sovFriendol_(uint64(m.OlStatus))
	n += 1 + sovFriendol_(uint64(m.LastOlTime))
	l = len(m.Account)
	n += 1 + l + sovFriendol_(uint64(l))
	n += 1 + sovFriendol_(uint64(m.RoomId))
	l = len(m.GameName)
	n += 1 + l + sovFriendol_(uint64(l))
	n += 1 + sovFriendol_(uint64(m.RoomType))
	n += 2
	l = len(m.NickName)
	n += 1 + l + sovFriendol_(uint64(l))
	l = len(m.FollowLabelImg)
	n += 2 + l + sovFriendol_(uint64(l))
	l = len(m.FollowLabelText)
	n += 2 + l + sovFriendol_(uint64(l))
	l = len(m.FindPlayingText)
	n += 2 + l + sovFriendol_(uint64(l))
	l = len(m.FindPlayingImg)
	n += 2 + l + sovFriendol_(uint64(l))
	if m.NobleInfo != nil {
		l = m.NobleInfo.Size()
		n += 2 + l + sovFriendol_(uint64(l))
	}
	n += 3
	n += 2 + sovFriendol_(uint64(m.LastLiveTime))
	n += 3
	n += 3
	n += 2 + sovFriendol_(uint64(m.RoomLevel))
	n += 2 + sovFriendol_(uint64(m.FollowCnt))
	n += 3
	n += 2 + sovFriendol_(uint64(m.CertType))
	l = len(m.Icon)
	n += 2 + l + sovFriendol_(uint64(l))
	l = len(m.Text)
	n += 2 + l + sovFriendol_(uint64(l))
	if len(m.Color) > 0 {
		for _, s := range m.Color {
			l = len(s)
			n += 2 + l + sovFriendol_(uint64(l))
		}
	}
	l = len(m.TextShadowColor)
	n += 2 + l + sovFriendol_(uint64(l))
	if len(m.RingColor) > 0 {
		for _, s := range m.RingColor {
			l = len(s)
			n += 2 + l + sovFriendol_(uint64(l))
		}
	}
	if len(m.RingColorExtra) > 0 {
		for _, s := range m.RingColorExtra {
			l = len(s)
			n += 2 + l + sovFriendol_(uint64(l))
		}
	}
	if len(m.NameplateDetailInfos) > 0 {
		for _, e := range m.NameplateDetailInfos {
			l = e.Size()
			n += 2 + l + sovFriendol_(uint64(l))
		}
	}
	n += 3
	n += 2 + sovFriendol_(uint64(m.Sex))
	l = len(m.RecentIntro)
	n += 2 + l + sovFriendol_(uint64(l))
	return n
}

func (m *Nobles) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendol_(uint64(m.Value))
	n += 1 + sovFriendol_(uint64(m.KeepValue))
	n += 1 + sovFriendol_(uint64(m.Level))
	n += 1 + sovFriendol_(uint64(m.CycleTs))
	return n
}

func (m *GetOfflineFriendsReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	return n
}

func (m *GetOfflineFriendsResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	if len(m.OfflineList) > 0 {
		for _, e := range m.OfflineList {
			l = e.Size()
			n += 1 + l + sovFriendol_(uint64(l))
		}
	}
	return n
}

func (m *GetOnlineFriendsReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	n += 2
	if len(m.ExtCheckOnlineUidlist) > 0 {
		for _, e := range m.ExtCheckOnlineUidlist {
			n += 1 + sovFriendol_(uint64(e))
		}
	}
	n += 2
	n += 2
	n += 2
	return n
}

func (m *GetOnlineFriendsResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	if len(m.OnlineList) > 0 {
		for _, e := range m.OnlineList {
			l = e.Size()
			n += 1 + l + sovFriendol_(uint64(l))
		}
	}
	if len(m.ExtCheckOfflineList) > 0 {
		for _, e := range m.ExtCheckOfflineList {
			l = e.Size()
			n += 1 + l + sovFriendol_(uint64(l))
		}
	}
	return n
}

func (m *GetGroupOnlineCountReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	n += 1 + sovFriendol_(uint64(m.GroupId))
	return n
}

func (m *GetGroupOnlineCountResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	n += 1 + sovFriendol_(uint64(m.GroupId))
	n += 1 + sovFriendol_(uint64(m.OnlineCnt))
	n += 1 + sovFriendol_(uint64(m.OfflineCnt))
	return n
}

func (m *OnlineEventPushMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendol_(uint64(m.Uid))
	n += 1 + sovFriendol_(uint64(m.OlStatus))
	n += 1 + sovFriendol_(uint64(m.ChannelId))
	l = len(m.GameName)
	n += 1 + l + sovFriendol_(uint64(l))
	n += 1 + sovFriendol_(uint64(m.RoomType))
	n += 1 + sovFriendol_(uint64(m.ChannelIdV2))
	n += 1 + sovFriendol_(uint64(m.RoomTypeV2))
	n += 2
	l = len(m.Account)
	n += 1 + l + sovFriendol_(uint64(l))
	l = len(m.NickName)
	n += 1 + l + sovFriendol_(uint64(l))
	n += 1 + sovFriendol_(uint64(m.PushType))
	l = len(m.FindPlayingText)
	n += 1 + l + sovFriendol_(uint64(l))
	l = len(m.FindPlayingImg)
	n += 1 + l + sovFriendol_(uint64(l))
	if m.NobleInfo != nil {
		l = m.NobleInfo.Size()
		n += 2 + l + sovFriendol_(uint64(l))
	}
	n += 3
	n += 2 + sovFriendol_(uint64(m.LastLiveTime))
	n += 3
	n += 3
	n += 2 + sovFriendol_(uint64(m.RoomLevel))
	n += 3
	n += 2 + sovFriendol_(uint64(m.CertType))
	l = len(m.Icon)
	n += 2 + l + sovFriendol_(uint64(l))
	l = len(m.Text)
	n += 2 + l + sovFriendol_(uint64(l))
	if len(m.Color) > 0 {
		for _, s := range m.Color {
			l = len(s)
			n += 2 + l + sovFriendol_(uint64(l))
		}
	}
	l = len(m.TextShadowColor)
	n += 2 + l + sovFriendol_(uint64(l))
	if len(m.RingColor) > 0 {
		for _, s := range m.RingColor {
			l = len(s)
			n += 2 + l + sovFriendol_(uint64(l))
		}
	}
	if len(m.RingColorExtra) > 0 {
		for _, s := range m.RingColorExtra {
			l = len(s)
			n += 2 + l + sovFriendol_(uint64(l))
		}
	}
	if len(m.NameplateDetailInfos) > 0 {
		for _, e := range m.NameplateDetailInfos {
			l = e.Size()
			n += 2 + l + sovFriendol_(uint64(l))
		}
	}
	n += 3
	n += 2 + sovFriendol_(uint64(m.Sex))
	return n
}

func (m *UpdateFollowChannelAuthReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	n += 1 + sovFriendol_(uint64(m.Uid))
	n += 2
	n += 1 + sovFriendol_(uint64(m.FollowAuthSwitch))
	return n
}

func (m *UpdateFollowChannelAuthResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	n += 1 + sovFriendol_(uint64(m.FollowAuthSwitch))
	return n
}

func (m *GetFollowChannelAuthReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	n += 1 + sovFriendol_(uint64(m.Uid))
	return n
}

func (m *GetFollowChannelAuthResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	n += 2
	n += 1 + sovFriendol_(uint64(m.FollowAuthSwitch))
	return n
}

func (m *CheckShowAddInviteCodeReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	n += 1 + sovFriendol_(uint64(m.Uid))
	return n
}

func (m *CheckShowAddInviteCodeResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	n += 2
	return n
}

func (m *CheckInviteFriendCodeReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	n += 1 + sovFriendol_(uint64(m.Uid))
	n += 1 + sovFriendol_(uint64(m.PackageType))
	l = len(m.InviteCode)
	n += 1 + l + sovFriendol_(uint64(l))
	l = len(m.OriginChannel)
	n += 1 + l + sovFriendol_(uint64(l))
	l = len(m.CurrentChannel)
	n += 1 + l + sovFriendol_(uint64(l))
	return n
}

func (m *CheckInviteFriendCodeResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	n += 2
	n += 1 + sovFriendol_(uint64(m.Lives))
	return n
}

func (m *OnlyTestxxxx) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendol_(uint64(m.A))
	n += 1 + sovFriendol_(uint64(m.B))
	return n
}

func (m *FollowChannelInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendol_(uint64(m.ChannelId))
	n += 1 + sovFriendol_(uint64(m.ChannelType))
	n += 2
	l = len(m.FindPlayingText)
	n += 1 + l + sovFriendol_(uint64(l))
	l = len(m.FindPlayingImg)
	n += 1 + l + sovFriendol_(uint64(l))
	l = len(m.FindPlayingLongText)
	n += 1 + l + sovFriendol_(uint64(l))
	n += 1 + sovFriendol_(uint64(m.ChannelLevel))
	return n
}

func (m *GetUserFollowChannelInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	n += 1 + sovFriendol_(uint64(m.TargetUid))
	return n
}

func (m *GetUserFollowChannelInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	if m.FollowChannel != nil {
		l = m.FollowChannel.Size()
		n += 1 + l + sovFriendol_(uint64(l))
	}
	return n
}

func (m *FollowLabelUpdate) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriendol_(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovFriendol_(uint64(l))
	n += 1 + sovFriendol_(uint64(m.Timestamp))
	l = len(m.FollowLabelImg)
	n += 1 + l + sovFriendol_(uint64(l))
	l = len(m.FollowLabelText)
	n += 1 + l + sovFriendol_(uint64(l))
	l = len(m.GameName)
	n += 1 + l + sovFriendol_(uint64(l))
	return n
}

func sovFriendol_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozFriendol_(x uint64) (n int) {
	return sovFriendol_(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ReportPlayingGameReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportPlayingGameReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportPlayingGameReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PkgName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PkgName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameState", wireType)
			}
			m.GameState = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameState |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("pkg_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportPlayingGameResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportPlayingGameResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportPlayingGameResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FriendsDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FriendsDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FriendsDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OlStatus", wireType)
			}
			m.OlStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OlStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastOlTime", wireType)
			}
			m.LastOlTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastOlTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomId", wireType)
			}
			m.RoomId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomType", wireType)
			}
			m.RoomType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomIsPwd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.RoomIsPwd = bool(v != 0)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NickName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NickName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowLabelImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FollowLabelImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowLabelText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FollowLabelText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FindPlayingText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FindPlayingText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 22:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FindPlayingImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FindPlayingImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 30:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NobleInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.NobleInfo == nil {
				m.NobleInfo = &Nobles{}
			}
			if err := m.NobleInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 31:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsLive", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsLive = bool(v != 0)
		case 32:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastLiveTime", wireType)
			}
			m.LastLiveTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastLiveTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 33:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPking", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPking = bool(v != 0)
		case 34:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPerforming", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPerforming = bool(v != 0)
		case 35:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomLevel", wireType)
			}
			m.RoomLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 36:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowCnt", wireType)
			}
			m.FollowCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FollowCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 37:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsMusicNextDirector", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMusicNextDirector = bool(v != 0)
		case 38:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CertType", wireType)
			}
			m.CertType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CertType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 39:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Icon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Icon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 40:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 41:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Color", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Color = append(m.Color, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 42:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TextShadowColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TextShadowColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 43:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RingColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RingColor = append(m.RingColor, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 44:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RingColorExtra", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RingColorExtra = append(m.RingColorExtra, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 45:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NameplateDetailInfos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NameplateDetailInfos = append(m.NameplateDetailInfos, &ga.NameplateDetailInfo{})
			if err := m.NameplateDetailInfos[len(m.NameplateDetailInfos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 46:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPublishing", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPublishing = bool(v != 0)
		case 47:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 48:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecentIntro", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecentIntro = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("ol_status")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("last_ol_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Nobles) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Nobles: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Nobles: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KeepValue", wireType)
			}
			m.KeepValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KeepValue |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CycleTs", wireType)
			}
			m.CycleTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CycleTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOfflineFriendsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOfflineFriendsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOfflineFriendsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOfflineFriendsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOfflineFriendsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOfflineFriendsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfflineList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OfflineList = append(m.OfflineList, &FriendsDetail{})
			if err := m.OfflineList[len(m.OfflineList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOnlineFriendsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOnlineFriendsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOnlineFriendsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IncludeFollowing", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IncludeFollowing = bool(v != 0)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFriendol_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ExtCheckOnlineUidlist = append(m.ExtCheckOnlineUidlist, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFriendol_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFriendol_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFriendol_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ExtCheckOnlineUidlist = append(m.ExtCheckOnlineUidlist, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtCheckOnlineUidlist", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EnterChannelCnt", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.EnterChannelCnt = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlyUseRecentInvite", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.OnlyUseRecentInvite = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsMainPage", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMainPage = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOnlineFriendsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOnlineFriendsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOnlineFriendsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlineList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OnlineList = append(m.OnlineList, &FriendsDetail{})
			if err := m.OnlineList[len(m.OnlineList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtCheckOfflineList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtCheckOfflineList = append(m.ExtCheckOfflineList, &FriendsDetail{})
			if err := m.ExtCheckOfflineList[len(m.ExtCheckOfflineList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGroupOnlineCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGroupOnlineCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGroupOnlineCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGroupOnlineCountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGroupOnlineCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGroupOnlineCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlineCnt", wireType)
			}
			m.OnlineCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OnlineCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfflineCnt", wireType)
			}
			m.OfflineCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfflineCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OnlineEventPushMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OnlineEventPushMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OnlineEventPushMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OlStatus", wireType)
			}
			m.OlStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OlStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomType", wireType)
			}
			m.RoomType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdV2", wireType)
			}
			m.ChannelIdV2 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelIdV2 |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomTypeV2", wireType)
			}
			m.RoomTypeV2 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomTypeV2 |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIsPwd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ChannelIsPwd = bool(v != 0)
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NickName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NickName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushType", wireType)
			}
			m.PushType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushType |= (OnlineEventPushMsg_OnlinePushType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FindPlayingText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FindPlayingText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FindPlayingImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FindPlayingImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 30:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NobleInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.NobleInfo == nil {
				m.NobleInfo = &Nobles{}
			}
			if err := m.NobleInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 31:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsLive", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsLive = bool(v != 0)
		case 32:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastLiveTime", wireType)
			}
			m.LastLiveTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastLiveTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 33:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPking", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPking = bool(v != 0)
		case 34:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPerforming", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPerforming = bool(v != 0)
		case 35:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomLevel", wireType)
			}
			m.RoomLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 36:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsMusicNextDirector", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMusicNextDirector = bool(v != 0)
		case 37:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CertType", wireType)
			}
			m.CertType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CertType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 38:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Icon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Icon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 39:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 40:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Color", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Color = append(m.Color, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 41:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TextShadowColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TextShadowColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 42:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RingColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RingColor = append(m.RingColor, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 43:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RingColorExtra", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RingColorExtra = append(m.RingColorExtra, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 44:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NameplateDetailInfos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NameplateDetailInfos = append(m.NameplateDetailInfos, &ga.NameplateDetailInfo{})
			if err := m.NameplateDetailInfos[len(m.NameplateDetailInfos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 45:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPublishing", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPublishing = bool(v != 0)
		case 46:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("ol_status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateFollowChannelAuthReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateFollowChannelAuthReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateFollowChannelAuthReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowAuth", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.FollowAuth = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowAuthSwitch", wireType)
			}
			m.FollowAuthSwitch = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FollowAuthSwitch |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("follow_auth")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateFollowChannelAuthResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateFollowChannelAuthResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateFollowChannelAuthResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowAuthSwitch", wireType)
			}
			m.FollowAuthSwitch = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FollowAuthSwitch |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFollowChannelAuthReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFollowChannelAuthReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFollowChannelAuthReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFollowChannelAuthResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFollowChannelAuthResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFollowChannelAuthResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowAuth", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.FollowAuth = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowAuthSwitch", wireType)
			}
			m.FollowAuthSwitch = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FollowAuthSwitch |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("follow_auth")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckShowAddInviteCodeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckShowAddInviteCodeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckShowAddInviteCodeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckShowAddInviteCodeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckShowAddInviteCodeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckShowAddInviteCodeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsShow", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsShow = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_show")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckInviteFriendCodeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckInviteFriendCodeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckInviteFriendCodeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PackageType", wireType)
			}
			m.PackageType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PackageType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InviteCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InviteCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OriginChannel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OriginChannel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentChannel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CurrentChannel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("invite_code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckInviteFriendCodeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckInviteFriendCodeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckInviteFriendCodeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsOk", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsOk = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Lives", wireType)
			}
			m.Lives = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Lives |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_ok")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("lives")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OnlyTestxxxx) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OnlyTestxxxx: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OnlyTestxxxx: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field A", wireType)
			}
			m.A = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.A |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field B", wireType)
			}
			m.B = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.B |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FollowChannelInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FollowChannelInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FollowChannelInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsLock", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsLock = bool(v != 0)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FindPlayingText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FindPlayingText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FindPlayingImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FindPlayingImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FindPlayingLongText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FindPlayingLongText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelLevel", wireType)
			}
			m.ChannelLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserFollowChannelInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserFollowChannelInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserFollowChannelInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserFollowChannelInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserFollowChannelInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserFollowChannelInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowChannel", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FollowChannel == nil {
				m.FollowChannel = &FollowChannelInfo{}
			}
			if err := m.FollowChannel.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FollowLabelUpdate) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FollowLabelUpdate: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FollowLabelUpdate: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowLabelImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FollowLabelImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowLabelText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FollowLabelText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriendol_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriendol_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriendol_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipFriendol_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowFriendol_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFriendol_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthFriendol_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowFriendol_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipFriendol_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthFriendol_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowFriendol_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("friendol_.proto", fileDescriptorFriendol_) }

var fileDescriptorFriendol_ = []byte{
	// 1988 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x59, 0x4b, 0x6f, 0xdb, 0xca,
	0x15, 0x0e, 0x65, 0xf9, 0xa1, 0x23, 0xbf, 0x42, 0x3f, 0x2e, 0xe3, 0x9b, 0xeb, 0x28, 0x74, 0xec,
	0xc8, 0x4e, 0xe2, 0xdc, 0x0a, 0x7d, 0xe0, 0x02, 0x45, 0x81, 0xc4, 0x71, 0x5c, 0x03, 0x7e, 0x41,
	0xb6, 0x53, 0xdc, 0x76, 0x41, 0xd0, 0xe4, 0x88, 0x9a, 0x8a, 0xe2, 0xf0, 0x72, 0x46, 0xb6, 0x0c,
	0xb4, 0xbb, 0xe2, 0x76, 0xd3, 0x45, 0x7f, 0x42, 0xff, 0x42, 0x57, 0x5d, 0xb5, 0xe8, 0x32, 0xcb,
	0xfe, 0x82, 0xa2, 0x48, 0xf7, 0x5d, 0xf5, 0x07, 0x14, 0x67, 0x86, 0x94, 0x87, 0xb6, 0x64, 0x5f,
	0x35, 0xe9, 0xae, 0x8b, 0x00, 0xd6, 0x39, 0xdf, 0xcc, 0x99, 0x39, 0xe7, 0x9b, 0x39, 0x1f, 0x27,
	0x30, 0xd3, 0x48, 0x28, 0x89, 0x7c, 0x16, 0x3a, 0x9b, 0x71, 0xc2, 0x04, 0x33, 0x0b, 0x81, 0xbb,
	0x34, 0x15, 0xb8, 0xce, 0x99, 0xcb, 0x89, 0x32, 0xd9, 0xbf, 0x31, 0x60, 0xbe, 0x4e, 0x62, 0x96,
	0x88, 0xa3, 0xd0, 0xbd, 0xa4, 0x51, 0xb0, 0xe3, 0xb6, 0x49, 0x9d, 0x7c, 0x63, 0xae, 0xc1, 0x04,
	0xc2, 0x9c, 0x84, 0x7c, 0x63, 0x19, 0x95, 0x42, 0xb5, 0x5c, 0x2b, 0x6f, 0x06, 0xee, 0xe6, 0x6b,
	0x97, 0xa3, 0xbb, 0x3e, 0x7e, 0xa6, 0xfe, 0x30, 0x1f, 0xc1, 0x44, 0xdc, 0x0a, 0x9c, 0xc8, 0x6d,
	0x13, 0xab, 0x50, 0x29, 0x54, 0x4b, 0xaf, 0x8b, 0xef, 0xff, 0xfe, 0xe8, 0x5e, 0x7d, 0x3c, 0x6e,
	0x05, 0x07, 0x6e, 0x9b, 0x98, 0x2b, 0x00, 0x81, 0xdb, 0x26, 0x0e, 0x17, 0xae, 0x20, 0xd6, 0x48,
	0xc5, 0xa8, 0x4e, 0xa5, 0x90, 0x12, 0xda, 0x8f, 0xd1, 0x6c, 0xbf, 0x86, 0x85, 0x3e, 0xab, 0xe0,
	0xb1, 0xb9, 0x0e, 0xa5, 0x74, 0x19, 0x3c, 0x4e, 0xd7, 0x31, 0x79, 0xb5, 0x0e, 0x1e, 0xd7, 0x27,
	0xce, 0xd2, 0xbf, 0xec, 0x7f, 0x95, 0x60, 0xea, 0xad, 0xdc, 0x31, 0x7f, 0x43, 0x84, 0x4b, 0x43,
	0x73, 0x11, 0x46, 0x3a, 0xd4, 0x97, 0xc3, 0xb2, 0x98, 0x68, 0x30, 0x1f, 0x43, 0x89, 0x85, 0x72,
	0x41, 0x1d, 0x2e, 0x17, 0x9d, 0x79, 0x27, 0x58, 0x78, 0x2c, 0xad, 0xe6, 0x1a, 0x4c, 0x86, 0x2e,
	0x17, 0x0e, 0x0b, 0x1d, 0x41, 0xdb, 0xb8, 0xee, 0x2b, 0x14, 0xa0, 0xe7, 0x30, 0x3c, 0xa1, 0x6d,
	0x62, 0x2e, 0xc3, 0xb8, 0xeb, 0x79, 0xac, 0x13, 0x09, 0xab, 0xa8, 0xef, 0x3e, 0x35, 0x9a, 0x5f,
	0xc0, 0x78, 0xc2, 0x58, 0xdb, 0xa1, 0xbe, 0x35, 0xaa, 0x6d, 0x7d, 0x0c, 0x8d, 0xbb, 0x72, 0x25,
	0x32, 0x39, 0x32, 0x7d, 0x63, 0x15, 0xa3, 0x37, 0xc1, 0x04, 0x9a, 0x65, 0xfe, 0x1e, 0x43, 0x49,
	0xce, 0x20, 0x2e, 0x63, 0x62, 0x8d, 0x6b, 0x73, 0x4c, 0xa0, 0xf9, 0xe4, 0x32, 0x26, 0xe6, 0x13,
	0x28, 0xab, 0x20, 0xdc, 0x89, 0x2f, 0x7c, 0x6b, 0xa2, 0x62, 0x54, 0x27, 0xb2, 0x1c, 0xcb, 0x40,
	0xfc, 0xe8, 0x42, 0xc6, 0x8a, 0xa8, 0xd7, 0x52, 0xb1, 0x4a, 0x7a, 0x2c, 0x34, 0xcb, 0x58, 0x9b,
	0x30, 0xdb, 0x60, 0x61, 0xc8, 0x2e, 0x9c, 0xd0, 0x3d, 0x23, 0xa1, 0x43, 0xdb, 0x81, 0x35, 0xa7,
	0x21, 0xa7, 0x95, 0x77, 0x0f, 0x9d, 0xbb, 0xed, 0xc0, 0xfc, 0x12, 0xee, 0xe7, 0xf0, 0x82, 0x74,
	0x85, 0x35, 0xaf, 0x0d, 0x98, 0xd1, 0x06, 0x9c, 0x90, 0xae, 0x90, 0x23, 0x68, 0xe4, 0x3b, 0xb1,
	0xaa, 0xb3, 0x1a, 0xb1, 0x90, 0x1b, 0x41, 0x23, 0x3f, 0x65, 0x81, 0x1c, 0x81, 0x6b, 0xd2, 0x47,
	0xe0, 0x9a, 0x16, 0x73, 0x6b, 0xba, 0x1a, 0x80, 0x6b, 0x5a, 0x07, 0x88, 0xd8, 0x59, 0x48, 0x1c,
	0x1a, 0x35, 0x98, 0xb5, 0x5c, 0x31, 0xaa, 0xe5, 0x1a, 0x20, 0x65, 0x0e, 0xd0, 0xca, 0xeb, 0x25,
	0xe9, 0xdd, 0x8d, 0x1a, 0x0c, 0x8b, 0x43, 0xb9, 0x13, 0xd2, 0x73, 0x62, 0x3d, 0xd2, 0x72, 0x36,
	0x46, 0xf9, 0x1e, 0x3d, 0x27, 0xe6, 0x06, 0x4c, 0x4b, 0x0e, 0x20, 0x40, 0xb1, 0xa0, 0xa2, 0xa5,
	0x5f, 0xf2, 0x03, 0x71, 0x92, 0x07, 0x8f, 0x60, 0x02, 0xb3, 0xdf, 0xa2, 0x51, 0x60, 0x3d, 0xd6,
	0xe6, 0x1a, 0xa7, 0xfc, 0x08, 0x8d, 0xe6, 0x3a, 0x4c, 0x21, 0x80, 0x24, 0x0d, 0x96, 0xb4, 0x11,
	0x65, 0x6b, 0xa8, 0x49, 0xca, 0x8f, 0x7a, 0x1e, 0x3c, 0x31, 0xb2, 0x9c, 0x21, 0x39, 0x27, 0xa1,
	0xb5, 0xa2, 0x9f, 0x18, 0xb4, 0xef, 0xa1, 0x19, 0x41, 0x69, 0xea, 0xbd, 0x48, 0x58, 0x4f, 0x74,
	0x90, 0xb2, 0x6f, 0x45, 0xc2, 0xfc, 0x0a, 0x16, 0x29, 0x77, 0xda, 0x1d, 0x4e, 0x3d, 0x27, 0x22,
	0x5d, 0xe1, 0xf8, 0x34, 0x21, 0x9e, 0x60, 0x89, 0xb5, 0xaa, 0x45, 0x9f, 0xa3, 0x7c, 0x1f, 0x21,
	0x07, 0xa4, 0x2b, 0xde, 0xa4, 0x00, 0x64, 0x8b, 0x47, 0x12, 0xa1, 0x68, 0xb7, 0xa6, 0xd3, 0x0e,
	0xcd, 0x92, 0x76, 0x16, 0x14, 0xa9, 0xc7, 0x22, 0xeb, 0xa9, 0x56, 0x0d, 0x69, 0x41, 0x8f, 0x2c,
	0x6c, 0x55, 0xf7, 0xa0, 0xc5, 0x9c, 0x87, 0x51, 0x8f, 0x85, 0x2c, 0xb1, 0xd6, 0x2b, 0x23, 0xd5,
	0x52, 0x5d, 0xfd, 0x40, 0x56, 0xa0, 0xd7, 0xe1, 0x4d, 0xd7, 0xc7, 0x1d, 0x49, 0xc4, 0x86, 0xce,
	0x0a, 0x74, 0x1f, 0x4b, 0xef, 0x96, 0x1c, 0xf1, 0x05, 0x40, 0x82, 0x6c, 0x50, 0xd0, 0x67, 0x72,
	0xb2, 0x12, 0x5a, 0x94, 0xbb, 0x0a, 0xb3, 0x57, 0x6e, 0x87, 0x74, 0x45, 0xe2, 0x5a, 0xcf, 0x25,
	0x68, 0xba, 0x07, 0xda, 0x46, 0xab, 0xb9, 0x0f, 0x8b, 0x78, 0x20, 0xe2, 0xd0, 0x15, 0xc4, 0xf1,
	0xe5, 0xbd, 0x21, 0x99, 0xc3, 0xad, 0x17, 0x95, 0x91, 0x6a, 0xb9, 0xf6, 0x99, 0xa4, 0x4e, 0x86,
	0x50, 0x17, 0x0b, 0x92, 0xa7, 0x3e, 0x1f, 0xdd, 0x34, 0xf2, 0xac, 0xcc, 0x9d, 0xb3, 0x90, 0xf2,
	0x26, 0x96, 0x79, 0xf3, 0x5a, 0x99, 0x7b, 0x1e, 0xbc, 0x9d, 0x38, 0xe9, 0x5a, 0x2f, 0xb5, 0xdc,
	0xa2, 0xc1, 0x7c, 0x0a, 0x93, 0x09, 0xf1, 0x48, 0x24, 0x1c, 0x1a, 0x89, 0x84, 0x59, 0x5f, 0x6a,
	0x79, 0x28, 0x2b, 0xcf, 0x2e, 0x3a, 0xec, 0x6f, 0x0d, 0x18, 0x53, 0xa4, 0x36, 0x97, 0x60, 0xf4,
	0xdc, 0x0d, 0x3b, 0xc4, 0x32, 0x2a, 0x46, 0xb5, 0x98, 0x82, 0x95, 0x09, 0x99, 0xd2, 0x22, 0x24,
	0x76, 0x14, 0xa0, 0xa0, 0x01, 0x4a, 0x68, 0x7f, 0x27, 0x41, 0x4b, 0x30, 0xaa, 0xe8, 0xa6, 0x5f,
	0xd0, 0xca, 0x84, 0xdc, 0xf6, 0x2e, 0xbd, 0x90, 0x38, 0x82, 0x5b, 0x45, 0xcd, 0x3d, 0x2e, 0xad,
	0x27, 0xdc, 0xfe, 0x09, 0xcc, 0xef, 0x10, 0x71, 0xd8, 0x68, 0x84, 0x34, 0x22, 0xe9, 0x15, 0x3c,
	0x44, 0x0f, 0xb1, 0xbb, 0xb0, 0xd0, 0x67, 0xfc, 0x50, 0xb7, 0xbf, 0xf9, 0x7d, 0x98, 0x64, 0x6a,
	0x02, 0x27, 0xa4, 0x5c, 0x58, 0x05, 0x59, 0xbd, 0xfb, 0x88, 0xce, 0x35, 0x85, 0x7a, 0x39, 0x85,
	0xed, 0x51, 0x2e, 0xec, 0xbf, 0x14, 0x60, 0x0e, 0x43, 0x47, 0xff, 0xdd, 0xca, 0xcd, 0xef, 0xc1,
	0x7d, 0x1a, 0x79, 0x61, 0xc7, 0x27, 0x8e, 0x3a, 0x75, 0x58, 0xf2, 0x82, 0x56, 0xf2, 0xd9, 0xd4,
	0xfd, 0x36, 0xf3, 0x9a, 0x3f, 0x02, 0x0b, 0xa9, 0xee, 0x35, 0x89, 0xd7, 0x72, 0x98, 0x0c, 0xec,
	0x74, 0xa8, 0x2f, 0x17, 0x3d, 0x52, 0x19, 0xa9, 0x4e, 0xd5, 0x17, 0x48, 0x57, 0x6c, 0xa1, 0x5b,
	0x2d, 0xeb, 0x54, 0x39, 0xf1, 0x90, 0x90, 0x48, 0x90, 0xc4, 0xf1, 0x9a, 0x6e, 0x14, 0x91, 0x50,
	0x1e, 0xfc, 0xa2, 0x16, 0x6b, 0x46, 0xba, 0xb7, 0x94, 0x37, 0x3d, 0xfe, 0x2c, 0x0a, 0x2f, 0x9d,
	0x8e, 0xdc, 0x49, 0x4a, 0xa9, 0x73, 0x2a, 0x88, 0xec, 0x45, 0xbd, 0xe3, 0x8f, 0x98, 0x53, 0xdc,
	0x8f, 0xa2, 0x16, 0x02, 0xb0, 0xff, 0xe1, 0xcd, 0xe1, 0xd2, 0xc8, 0x89, 0xdd, 0x40, 0xf5, 0xa6,
	0x6c, 0x00, 0x50, 0xbe, 0xef, 0xd2, 0xe8, 0xc8, 0x0d, 0x88, 0xfd, 0x67, 0x43, 0xd5, 0x3e, 0xfa,
	0x88, 0xd2, 0xd5, 0xa0, 0x9c, 0xe6, 0xe1, 0xf6, 0xca, 0x81, 0x42, 0x61, 0xe1, 0xcc, 0xb7, 0xb0,
	0xa8, 0x65, 0x51, 0x2f, 0xfc, 0xc8, 0xa0, 0xe1, 0x73, 0xbd, 0xb4, 0x6a, 0x04, 0x70, 0x61, 0x71,
	0x87, 0x88, 0x9d, 0x84, 0x75, 0x62, 0xb5, 0x87, 0x2d, 0x6c, 0xdb, 0x43, 0x0a, 0xa0, 0x00, 0x87,
	0x63, 0x8b, 0xd7, 0xb5, 0xc4, 0xb8, 0xb4, 0xee, 0xfa, 0xf6, 0x1f, 0x0d, 0xf8, 0xac, 0x6f, 0x8c,
	0xe1, 0xb2, 0x74, 0x57, 0x1c, 0x3c, 0xe7, 0x69, 0x1a, 0x91, 0x18, 0x39, 0xa1, 0xa5, 0xec, 0x48,
	0x89, 0x55, 0xc8, 0xf8, 0xdf, 0xa3, 0x4f, 0x4f, 0xd6, 0xa4, 0x8e, 0xad, 0x48, 0xd8, 0xbf, 0x03,
	0x30, 0xd5, 0x5a, 0xb7, 0xcf, 0x49, 0x24, 0x8e, 0x3a, 0xbc, 0xb9, 0xcf, 0x83, 0x8f, 0x11, 0x54,
	0x2b, 0x00, 0x19, 0x6f, 0xa9, 0x9f, 0x5f, 0x5d, 0x6a, 0xbf, 0x2e, 0x87, 0x8a, 0x77, 0xcb, 0xa1,
	0xd1, 0xbe, 0x72, 0xa8, 0x0a, 0x53, 0x57, 0xa1, 0x9c, 0xf3, 0x9a, 0x14, 0x44, 0x19, 0xac, 0xdc,
	0x8b, 0xf6, 0xae, 0x86, 0x2c, 0xef, 0x4d, 0x86, 0xc0, 0x92, 0x9e, 0x8e, 0x6c, 0xbe, 0x77, 0x35,
	0x54, 0x02, 0xbd, 0x19, 0x95, 0xc6, 0x02, 0xfd, 0x5a, 0xcf, 0xa6, 0x94, 0x32, 0x4b, 0x53, 0x84,
	0x65, 0x6d, 0x07, 0x3d, 0x45, 0x98, 0x93, 0x61, 0x93, 0x7d, 0x65, 0xd8, 0x4f, 0xa1, 0x14, 0x77,
	0x78, 0x53, 0xed, 0x71, 0xaa, 0x62, 0x54, 0xa7, 0x6b, 0xab, 0xc8, 0x8a, 0x9b, 0x15, 0x49, 0x4d,
	0xf8, 0x0b, 0x97, 0x9a, 0xcd, 0x14, 0xa7, 0xbf, 0xfb, 0xcb, 0xad, 0xe9, 0x61, 0xe5, 0xd6, 0xcc,
	0xff, 0xe5, 0xd6, 0x60, 0x25, 0xf5, 0x64, 0x28, 0x25, 0xb5, 0x7a, 0xab, 0x92, 0x5a, 0x1b, 0xa8,
	0xa4, 0x9e, 0x0e, 0x56, 0x52, 0xd5, 0x3b, 0x95, 0xd4, 0xfa, 0x77, 0x57, 0x52, 0x1b, 0xdf, 0x45,
	0x49, 0x3d, 0x1b, 0x52, 0x49, 0x3d, 0xff, 0x24, 0x4a, 0xea, 0xc5, 0x5d, 0x4a, 0x6a, 0xf3, 0x9a,
	0x92, 0xb2, 0x77, 0x60, 0x3a, 0x7f, 0x3e, 0xcc, 0x07, 0xb0, 0x90, 0xb7, 0xbc, 0x21, 0x0d, 0xb7,
	0x13, 0x8a, 0xd9, 0x7b, 0xe6, 0x12, 0x2c, 0xe6, 0x5d, 0xa7, 0x91, 0x6a, 0xe9, 0xb3, 0x86, 0xfd,
	0x27, 0x03, 0x96, 0x4e, 0x63, 0xdf, 0x15, 0x69, 0x1f, 0x4f, 0x7b, 0xec, 0xab, 0x8e, 0x68, 0x0e,
	0xd3, 0x2a, 0xd2, 0xeb, 0xb3, 0x70, 0xfd, 0xfa, 0x5c, 0x85, 0x72, 0xaa, 0xe5, 0xdd, 0x8e, 0x68,
	0xca, 0x6f, 0xcd, 0x5e, 0xaf, 0x55, 0x0e, 0x8c, 0x64, 0xd6, 0xc0, 0xd4, 0x60, 0x0e, 0xbf, 0xa0,
	0xc2, 0x6b, 0xe6, 0xae, 0xf0, 0xd9, 0x2b, 0xf4, 0xb1, 0xf4, 0xda, 0xbf, 0x82, 0xcf, 0x07, 0x2e,
	0x7c, 0xd8, 0x2e, 0xdd, 0x2f, 0x7a, 0xe1, 0xd6, 0xe8, 0x5f, 0xcb, 0xce, 0xf7, 0xbf, 0xc8, 0x99,
	0xfd, 0x07, 0x03, 0xac, 0xfe, 0x73, 0x0f, 0xb7, 0xad, 0x6b, 0xb9, 0x2f, 0x0c, 0x95, 0xfb, 0x91,
	0x5b, 0x77, 0xff, 0x0b, 0x78, 0x20, 0xf5, 0xc6, 0x71, 0x93, 0x5d, 0xbc, 0xf2, 0x7d, 0xa5, 0xac,
	0xb6, 0x98, 0x4f, 0x3e, 0xc5, 0xfe, 0x1b, 0xb0, 0x34, 0x68, 0xf2, 0xe1, 0x12, 0xa0, 0x6e, 0x65,
	0xde, 0x64, 0x17, 0xb9, 0xcd, 0x8f, 0x51, 0x8e, 0x53, 0xdb, 0xdf, 0x16, 0xc0, 0x92, 0x81, 0x54,
	0x04, 0x25, 0xa9, 0x3e, 0xd1, 0x26, 0xf0, 0x53, 0x27, 0x76, 0xbd, 0x96, 0x1b, 0x10, 0x75, 0x3b,
	0xea, 0xf9, 0x2c, 0xa7, 0x1e, 0x79, 0x6e, 0x57, 0xa1, 0xac, 0x94, 0xab, 0xe3, 0x31, 0x9f, 0xe4,
	0x9e, 0x5a, 0x80, 0xf6, 0xb6, 0x6e, 0x3e, 0x83, 0x69, 0x96, 0xd0, 0x80, 0x46, 0x99, 0x46, 0x96,
	0x0a, 0x21, 0x43, 0x4e, 0x29, 0x5f, 0x4a, 0x16, 0xf3, 0x05, 0xcc, 0x78, 0x9d, 0x24, 0x41, 0x55,
	0x9c, 0xa1, 0xf5, 0x17, 0x98, 0xe9, 0xd4, 0x99, 0xc2, 0xed, 0x5f, 0xa7, 0xd5, 0xbc, 0x99, 0x87,
	0xe1, 0xf2, 0xfd, 0x00, 0x46, 0x29, 0x77, 0x58, 0x2b, 0x97, 0xed, 0x22, 0xe5, 0x87, 0x2d, 0xf9,
	0x11, 0x46, 0xcf, 0x09, 0xcf, 0xbd, 0x36, 0x29, 0x93, 0xfd, 0x43, 0x98, 0x3c, 0x8c, 0xc2, 0xcb,
	0x13, 0xc2, 0x45, 0xb7, 0xdb, 0xed, 0x9a, 0x26, 0x18, 0xae, 0xfc, 0xda, 0xcb, 0x70, 0x86, 0x8b,
	0xb6, 0xb3, 0xdc, 0x89, 0x34, 0xce, 0xec, 0xf7, 0x05, 0xb8, 0x9f, 0x3b, 0x24, 0xb2, 0x15, 0xe7,
	0xd5, 0x98, 0xd1, 0x5f, 0x8d, 0x3d, 0x85, 0x4c, 0xd9, 0xa8, 0xea, 0x14, 0xfa, 0xc8, 0x28, 0x59,
	0x9d, 0xb4, 0xb1, 0x33, 0xaf, 0x25, 0x2b, 0xa8, 0x37, 0x76, 0xe6, 0xb5, 0xfa, 0x8b, 0x90, 0xe2,
	0xb0, 0x22, 0x64, 0xf4, 0x16, 0x11, 0xf2, 0x15, 0x2c, 0xe6, 0xf0, 0x21, 0xcb, 0xc2, 0xe8, 0x15,
	0x9d, 0xd3, 0x46, 0xed, 0xb1, 0x34, 0xd4, 0xfa, 0x95, 0x58, 0x54, 0x02, 0x40, 0x7f, 0x62, 0xcb,
	0xf6, 0x2f, 0x35, 0x80, 0xfd, 0x4b, 0xf8, 0x7c, 0x87, 0x88, 0x53, 0x4e, 0x92, 0x1b, 0x09, 0x1d,
	0xe6, 0x30, 0xac, 0x00, 0x08, 0x37, 0x09, 0x88, 0x70, 0xae, 0x9f, 0x89, 0x92, 0xb2, 0x9f, 0x52,
	0xdf, 0xfe, 0xad, 0x01, 0x0f, 0x07, 0x07, 0x1b, 0x8e, 0x71, 0x3f, 0x86, 0xe9, 0xec, 0xa9, 0x28,
	0xe5, 0x79, 0x41, 0xca, 0xb4, 0x05, 0xf9, 0x8d, 0x74, 0x63, 0xf6, 0xa9, 0x86, 0x6e, 0xb2, 0xff,
	0x6d, 0x64, 0x04, 0x92, 0xaf, 0x78, 0xaa, 0x9b, 0x0c, 0xfc, 0x12, 0xd0, 0xd4, 0x6f, 0xa1, 0xdf,
	0x7b, 0xa8, 0x0d, 0x25, 0x94, 0x76, 0x5c, 0xb8, 0xed, 0x38, 0x47, 0xf3, 0x2b, 0x73, 0xdf, 0x57,
	0xc8, 0xe2, 0xb0, 0xaf, 0x90, 0xa3, 0xb7, 0xbd, 0x42, 0xde, 0xfd, 0xec, 0xba, 0xf1, 0x57, 0x03,
	0x1e, 0x6e, 0xdf, 0xe8, 0x2e, 0xea, 0x66, 0x97, 0xa4, 0xaf, 0xc0, 0xc3, 0xed, 0x83, 0xd3, 0x7d,
	0xe7, 0x06, 0xc6, 0xd9, 0xda, 0x3b, 0x3c, 0xde, 0x9e, 0xbd, 0x67, 0x3e, 0x81, 0xca, 0x00, 0xc4,
	0xab, 0xbd, 0x3d, 0xfc, 0x77, 0xf8, 0xb3, 0x59, 0xc3, 0x7c, 0x0e, 0xd5, 0x01, 0xa8, 0xc3, 0x83,
	0xbd, 0xaf, 0x9d, 0xb7, 0xf5, 0xdd, 0xed, 0x83, 0x37, 0x29, 0xba, 0x60, 0x6e, 0xc0, 0xda, 0xad,
	0xe8, 0x57, 0x07, 0xc7, 0x29, 0x76, 0xe4, 0xf5, 0xe1, 0xfb, 0x0f, 0xcb, 0xc6, 0xdf, 0x3e, 0x2c,
	0x1b, 0xff, 0xf8, 0xb0, 0x6c, 0xfc, 0xfe, 0x9f, 0xcb, 0xf7, 0xc0, 0xf2, 0x58, 0x7b, 0xf3, 0x92,
	0x5e, 0xb2, 0x0e, 0x96, 0xbe, 0xcd, 0x7c, 0x12, 0xaa, 0xff, 0x07, 0xf8, 0xf9, 0x4a, 0xc0, 0x42,
	0x37, 0x0a, 0x36, 0x7f, 0x50, 0x13, 0x62, 0xd3, 0x63, 0xed, 0x97, 0xd2, 0xec, 0xb1, 0xf0, 0xa5,
	0x1b, 0xc7, 0x2f, 0xd5, 0xf7, 0xe3, 0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0x4c, 0x03, 0x2e, 0x84,
	0x51, 0x18, 0x00, 0x00,
}
