// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/richer_birthday/grpc_richer_birthday.proto

package richer_birthday // import "golang.52tt.com/protocol/app/api/richer_birthday"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import richer_birthday_logic "golang.52tt.com/protocol/app/richer_birthday_logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RicherBirthdayLogicClient is the client API for RicherBirthdayLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RicherBirthdayLogicClient interface {
	// 获取生日信息
	GetRicherBirthdayInfo(ctx context.Context, in *richer_birthday_logic.GetRicherBirthdayInfoRequest, opts ...grpc.CallOption) (*richer_birthday_logic.GetRicherBirthdayInfoResponse, error)
	// 批量获取用户生日信息
	BathGetRicherBirthdayInfo(ctx context.Context, in *richer_birthday_logic.BathGetRicherBirthdayInfoRequest, opts ...grpc.CallOption) (*richer_birthday_logic.BathGetRicherBirthdayInfoResponse, error)
	// 生日隐藏开关
	HideRicherBirthdaySwitch(ctx context.Context, in *richer_birthday_logic.HideRicherBirthdaySwitchRequest, opts ...grpc.CallOption) (*richer_birthday_logic.HideRicherBirthdaySwitchResponse, error)
	// 发送生日系统消息
	SendRicherBirthdaySystemMessage(ctx context.Context, in *richer_birthday_logic.SendRicherBirthdaySystemMessageRequest, opts ...grpc.CallOption) (*richer_birthday_logic.SendRicherBirthdaySystemMessageResponse, error)
	// 获取生日礼物配置
	GetRicherBirthdayGiftCfgInRoom(ctx context.Context, in *richer_birthday_logic.GetRicherBirthdayGiftCfgInRoomRequest, opts ...grpc.CallOption) (*richer_birthday_logic.GetRicherBirthdayGiftCfgInRoomResponse, error)
	// 获取生日隐藏开关
	GetHideRicherBirthdaySwitchRequest(ctx context.Context, in *richer_birthday_logic.GetHideRicherBirthdaySwitchRequest, opts ...grpc.CallOption) (*richer_birthday_logic.GetHideRicherBirthdaySwitchResponse, error)
}

type richerBirthdayLogicClient struct {
	cc *grpc.ClientConn
}

func NewRicherBirthdayLogicClient(cc *grpc.ClientConn) RicherBirthdayLogicClient {
	return &richerBirthdayLogicClient{cc}
}

func (c *richerBirthdayLogicClient) GetRicherBirthdayInfo(ctx context.Context, in *richer_birthday_logic.GetRicherBirthdayInfoRequest, opts ...grpc.CallOption) (*richer_birthday_logic.GetRicherBirthdayInfoResponse, error) {
	out := new(richer_birthday_logic.GetRicherBirthdayInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.richer_birthday.RicherBirthdayLogic/GetRicherBirthdayInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayLogicClient) BathGetRicherBirthdayInfo(ctx context.Context, in *richer_birthday_logic.BathGetRicherBirthdayInfoRequest, opts ...grpc.CallOption) (*richer_birthday_logic.BathGetRicherBirthdayInfoResponse, error) {
	out := new(richer_birthday_logic.BathGetRicherBirthdayInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.richer_birthday.RicherBirthdayLogic/BathGetRicherBirthdayInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayLogicClient) HideRicherBirthdaySwitch(ctx context.Context, in *richer_birthday_logic.HideRicherBirthdaySwitchRequest, opts ...grpc.CallOption) (*richer_birthday_logic.HideRicherBirthdaySwitchResponse, error) {
	out := new(richer_birthday_logic.HideRicherBirthdaySwitchResponse)
	err := c.cc.Invoke(ctx, "/ga.api.richer_birthday.RicherBirthdayLogic/HideRicherBirthdaySwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayLogicClient) SendRicherBirthdaySystemMessage(ctx context.Context, in *richer_birthday_logic.SendRicherBirthdaySystemMessageRequest, opts ...grpc.CallOption) (*richer_birthday_logic.SendRicherBirthdaySystemMessageResponse, error) {
	out := new(richer_birthday_logic.SendRicherBirthdaySystemMessageResponse)
	err := c.cc.Invoke(ctx, "/ga.api.richer_birthday.RicherBirthdayLogic/SendRicherBirthdaySystemMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayLogicClient) GetRicherBirthdayGiftCfgInRoom(ctx context.Context, in *richer_birthday_logic.GetRicherBirthdayGiftCfgInRoomRequest, opts ...grpc.CallOption) (*richer_birthday_logic.GetRicherBirthdayGiftCfgInRoomResponse, error) {
	out := new(richer_birthday_logic.GetRicherBirthdayGiftCfgInRoomResponse)
	err := c.cc.Invoke(ctx, "/ga.api.richer_birthday.RicherBirthdayLogic/GetRicherBirthdayGiftCfgInRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *richerBirthdayLogicClient) GetHideRicherBirthdaySwitchRequest(ctx context.Context, in *richer_birthday_logic.GetHideRicherBirthdaySwitchRequest, opts ...grpc.CallOption) (*richer_birthday_logic.GetHideRicherBirthdaySwitchResponse, error) {
	out := new(richer_birthday_logic.GetHideRicherBirthdaySwitchResponse)
	err := c.cc.Invoke(ctx, "/ga.api.richer_birthday.RicherBirthdayLogic/GetHideRicherBirthdaySwitchRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RicherBirthdayLogicServer is the server API for RicherBirthdayLogic service.
type RicherBirthdayLogicServer interface {
	// 获取生日信息
	GetRicherBirthdayInfo(context.Context, *richer_birthday_logic.GetRicherBirthdayInfoRequest) (*richer_birthday_logic.GetRicherBirthdayInfoResponse, error)
	// 批量获取用户生日信息
	BathGetRicherBirthdayInfo(context.Context, *richer_birthday_logic.BathGetRicherBirthdayInfoRequest) (*richer_birthday_logic.BathGetRicherBirthdayInfoResponse, error)
	// 生日隐藏开关
	HideRicherBirthdaySwitch(context.Context, *richer_birthday_logic.HideRicherBirthdaySwitchRequest) (*richer_birthday_logic.HideRicherBirthdaySwitchResponse, error)
	// 发送生日系统消息
	SendRicherBirthdaySystemMessage(context.Context, *richer_birthday_logic.SendRicherBirthdaySystemMessageRequest) (*richer_birthday_logic.SendRicherBirthdaySystemMessageResponse, error)
	// 获取生日礼物配置
	GetRicherBirthdayGiftCfgInRoom(context.Context, *richer_birthday_logic.GetRicherBirthdayGiftCfgInRoomRequest) (*richer_birthday_logic.GetRicherBirthdayGiftCfgInRoomResponse, error)
	// 获取生日隐藏开关
	GetHideRicherBirthdaySwitchRequest(context.Context, *richer_birthday_logic.GetHideRicherBirthdaySwitchRequest) (*richer_birthday_logic.GetHideRicherBirthdaySwitchResponse, error)
}

func RegisterRicherBirthdayLogicServer(s *grpc.Server, srv RicherBirthdayLogicServer) {
	s.RegisterService(&_RicherBirthdayLogic_serviceDesc, srv)
}

func _RicherBirthdayLogic_GetRicherBirthdayInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(richer_birthday_logic.GetRicherBirthdayInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayLogicServer).GetRicherBirthdayInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.richer_birthday.RicherBirthdayLogic/GetRicherBirthdayInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayLogicServer).GetRicherBirthdayInfo(ctx, req.(*richer_birthday_logic.GetRicherBirthdayInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayLogic_BathGetRicherBirthdayInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(richer_birthday_logic.BathGetRicherBirthdayInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayLogicServer).BathGetRicherBirthdayInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.richer_birthday.RicherBirthdayLogic/BathGetRicherBirthdayInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayLogicServer).BathGetRicherBirthdayInfo(ctx, req.(*richer_birthday_logic.BathGetRicherBirthdayInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayLogic_HideRicherBirthdaySwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(richer_birthday_logic.HideRicherBirthdaySwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayLogicServer).HideRicherBirthdaySwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.richer_birthday.RicherBirthdayLogic/HideRicherBirthdaySwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayLogicServer).HideRicherBirthdaySwitch(ctx, req.(*richer_birthday_logic.HideRicherBirthdaySwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayLogic_SendRicherBirthdaySystemMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(richer_birthday_logic.SendRicherBirthdaySystemMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayLogicServer).SendRicherBirthdaySystemMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.richer_birthday.RicherBirthdayLogic/SendRicherBirthdaySystemMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayLogicServer).SendRicherBirthdaySystemMessage(ctx, req.(*richer_birthday_logic.SendRicherBirthdaySystemMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayLogic_GetRicherBirthdayGiftCfgInRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(richer_birthday_logic.GetRicherBirthdayGiftCfgInRoomRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayLogicServer).GetRicherBirthdayGiftCfgInRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.richer_birthday.RicherBirthdayLogic/GetRicherBirthdayGiftCfgInRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayLogicServer).GetRicherBirthdayGiftCfgInRoom(ctx, req.(*richer_birthday_logic.GetRicherBirthdayGiftCfgInRoomRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RicherBirthdayLogic_GetHideRicherBirthdaySwitchRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(richer_birthday_logic.GetHideRicherBirthdaySwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RicherBirthdayLogicServer).GetHideRicherBirthdaySwitchRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.richer_birthday.RicherBirthdayLogic/GetHideRicherBirthdaySwitchRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RicherBirthdayLogicServer).GetHideRicherBirthdaySwitchRequest(ctx, req.(*richer_birthday_logic.GetHideRicherBirthdaySwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _RicherBirthdayLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.richer_birthday.RicherBirthdayLogic",
	HandlerType: (*RicherBirthdayLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRicherBirthdayInfo",
			Handler:    _RicherBirthdayLogic_GetRicherBirthdayInfo_Handler,
		},
		{
			MethodName: "BathGetRicherBirthdayInfo",
			Handler:    _RicherBirthdayLogic_BathGetRicherBirthdayInfo_Handler,
		},
		{
			MethodName: "HideRicherBirthdaySwitch",
			Handler:    _RicherBirthdayLogic_HideRicherBirthdaySwitch_Handler,
		},
		{
			MethodName: "SendRicherBirthdaySystemMessage",
			Handler:    _RicherBirthdayLogic_SendRicherBirthdaySystemMessage_Handler,
		},
		{
			MethodName: "GetRicherBirthdayGiftCfgInRoom",
			Handler:    _RicherBirthdayLogic_GetRicherBirthdayGiftCfgInRoom_Handler,
		},
		{
			MethodName: "GetHideRicherBirthdaySwitchRequest",
			Handler:    _RicherBirthdayLogic_GetHideRicherBirthdaySwitchRequest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/richer_birthday/grpc_richer_birthday.proto",
}

func init() {
	proto.RegisterFile("api/richer_birthday/grpc_richer_birthday.proto", fileDescriptor_grpc_richer_birthday_b2d2dc7ae7b663ea)
}

var fileDescriptor_grpc_richer_birthday_b2d2dc7ae7b663ea = []byte{
	// 415 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x94, 0x4f, 0x8b, 0xda, 0x40,
	0x1c, 0x86, 0x89, 0x29, 0x45, 0xe6, 0x38, 0x45, 0x69, 0x03, 0xad, 0xe2, 0xdd, 0x09, 0xb5, 0xb4,
	0xa5, 0xda, 0x52, 0xab, 0x07, 0x2b, 0xb4, 0x50, 0xe2, 0xad, 0x17, 0x3b, 0xc6, 0x71, 0x32, 0x60,
	0x66, 0x62, 0x32, 0x62, 0xbd, 0x89, 0xc7, 0x5e, 0x02, 0xbd, 0xda, 0xda, 0x3f, 0xd7, 0x52, 0xd8,
	0x8f, 0xb8, 0x24, 0xd9, 0xb0, 0x4c, 0x36, 0x59, 0x31, 0x7b, 0x4b, 0xe6, 0x9d, 0xe7, 0xfd, 0x3d,
	0x24, 0xc3, 0x00, 0x84, 0x3d, 0x66, 0xfa, 0xcc, 0x76, 0x88, 0x3f, 0x9d, 0x31, 0x5f, 0x3a, 0x73,
	0xbc, 0x35, 0xa9, 0xef, 0xd9, 0xd3, 0xcc, 0x22, 0xf2, 0x7c, 0x21, 0x05, 0xac, 0x53, 0x1c, 0x21,
	0x28, 0x93, 0x1a, 0x8f, 0xa3, 0x1e, 0xf2, 0x55, 0x12, 0x1e, 0x30, 0xc1, 0xaf, 0x9f, 0x12, 0xcc,
	0x78, 0x9a, 0xd9, 0x3f, 0x5d, 0x0a, 0xca, 0x6c, 0x33, 0x77, 0x35, 0x41, 0x3a, 0x87, 0x2a, 0x78,
	0x60, 0xc5, 0xf9, 0xe0, 0x2a, 0xfe, 0x10, 0xa5, 0xf0, 0xbb, 0x06, 0x6a, 0x23, 0x22, 0xd5, 0x68,
	0xcc, 0x17, 0x02, 0xbe, 0x40, 0x14, 0xa3, 0xfc, 0xca, 0x5c, 0xc0, 0x22, 0xab, 0x35, 0x09, 0xa4,
	0xf1, 0xf2, 0x6c, 0x2e, 0xf0, 0x04, 0x0f, 0x48, 0xab, 0xba, 0xdf, 0x35, 0xef, 0x55, 0x7f, 0x84,
	0x3a, 0x3c, 0x6a, 0xe0, 0xd1, 0x00, 0x4b, 0x27, 0x5f, 0xac, 0x5b, 0x3c, 0xa0, 0x10, 0x4a, 0xe5,
	0x7a, 0xa5, 0x58, 0x45, 0xf0, 0x67, 0xa8, 0xc3, 0x83, 0x06, 0x1e, 0xbe, 0x67, 0x73, 0xa2, 0x6e,
	0x9e, 0x6c, 0x98, 0xb4, 0x1d, 0xf8, 0xaa, 0x78, 0x46, 0x11, 0x93, 0xea, 0x75, 0xcb, 0xa0, 0x8a,
	0xdd, 0x31, 0xd4, 0xe1, 0x85, 0x06, 0x1a, 0x13, 0xc2, 0xe7, 0x99, 0xed, 0xdb, 0x40, 0x12, 0xf7,
	0x23, 0x09, 0x02, 0x4c, 0x09, 0xec, 0x17, 0x4f, 0x3a, 0x81, 0xa6, 0xae, 0xef, 0xee, 0xd0, 0xa0,
	0x28, 0xff, 0x0a, 0x75, 0xf8, 0x5f, 0x03, 0x4f, 0x6e, 0x7c, 0xfc, 0x11, 0x5b, 0xc8, 0xe1, 0x82,
	0x8e, 0xb9, 0x25, 0x84, 0x0b, 0xdf, 0x9e, 0x71, 0xae, 0x14, 0x32, 0x15, 0xee, 0x97, 0x2f, 0x50,
	0x7c, 0x7f, 0x87, 0x3a, 0xfc, 0xa7, 0x81, 0xd6, 0x88, 0xc8, 0x13, 0xff, 0x13, 0xbe, 0xbe, 0x75,
	0xe4, 0xa9, 0xd3, 0xf0, 0xa6, 0x24, 0xad, 0xd8, 0xfe, 0x09, 0x75, 0xa3, 0xb1, 0xdf, 0x35, 0x6b,
	0x49, 0x51, 0x3b, 0x2d, 0x6a, 0xc7, 0x45, 0xdf, 0x76, 0xcd, 0x0a, 0x15, 0x83, 0x2f, 0xa0, 0x6e,
	0x0b, 0x17, 0xad, 0xd6, 0x1b, 0xcc, 0x91, 0x94, 0xc9, 0x9d, 0x11, 0xdd, 0x4c, 0x9f, 0xfb, 0x54,
	0x2c, 0x31, 0xa7, 0xe8, 0x79, 0x47, 0x4a, 0x64, 0x0b, 0xd7, 0x8c, 0x23, 0x5b, 0x2c, 0x4d, 0xec,
	0x79, 0x66, 0xce, 0x75, 0xd7, 0xcb, 0xbc, 0xff, 0xad, 0xe8, 0xd6, 0xa7, 0xe1, 0xec, 0x7e, 0xcc,
	0x3d, 0xbb, 0x0c, 0x00, 0x00, 0xff, 0xff, 0xc0, 0x56, 0x86, 0xd9, 0x22, 0x05, 0x00, 0x00,
}
