// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/super_player/grpc_super_player.proto

package super_player // import "golang.52tt.com/protocol/app/api/super_player"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import superplayerlogic "golang.52tt.com/protocol/app/superplayerlogic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SuperPlayerLogicClient is the client API for SuperPlayerLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SuperPlayerLogicClient interface {
	GetSuperPlayerConf(ctx context.Context, in *superplayerlogic.GetSuperPlayerConfReq, opts ...grpc.CallOption) (*superplayerlogic.GetSuperPlayerConfResp, error)
	GetSuperPlayerInfo(ctx context.Context, in *superplayerlogic.GetSuperPlayerInfoReq, opts ...grpc.CallOption) (*superplayerlogic.GetSuperPlayerInfoResp, error)
	BatchGetSuperPlayerInfo(ctx context.Context, in *superplayerlogic.BatchGetSuperPlayerInfoReq, opts ...grpc.CallOption) (*superplayerlogic.BatchGetSuperPlayerInfoResp, error)
	GetUserSpecialConcern(ctx context.Context, in *superplayerlogic.GetUserSpecialConcernReq, opts ...grpc.CallOption) (*superplayerlogic.GetUserSpecialConcernResp, error)
	AddUserSpecialConcern(ctx context.Context, in *superplayerlogic.AddUserSpecialConcernReq, opts ...grpc.CallOption) (*superplayerlogic.AddUserSpecialConcernResp, error)
	DelUserSpecialConcern(ctx context.Context, in *superplayerlogic.DelUserSpecialConcernReq, opts ...grpc.CallOption) (*superplayerlogic.DelUserSpecialConcernResp, error)
	GetIMPrivilegeCount(ctx context.Context, in *superplayerlogic.GetIMPrivilegeCountReq, opts ...grpc.CallOption) (*superplayerlogic.GetIMPrivilegeCountResp, error)
	UseIMPrivilege(ctx context.Context, in *superplayerlogic.UseIMPrivilegeReq, opts ...grpc.CallOption) (*superplayerlogic.UseIMPrivilegeResp, error)
	GetUserSVIPPrivilegeProfile(ctx context.Context, in *superplayerlogic.GetUserSVIPPrivilegeProfileReq, opts ...grpc.CallOption) (*superplayerlogic.GetUserSVIPPrivilegeProfileResp, error)
	SetUserSVIPPrivilegeProfile(ctx context.Context, in *superplayerlogic.SetUserSVIPPrivilegeProfileReq, opts ...grpc.CallOption) (*superplayerlogic.SetUserSVIPPrivilegeProfileResp, error)
	UseSVIPStealthAhead(ctx context.Context, in *superplayerlogic.UseSVIPStealthAheadReq, opts ...grpc.CallOption) (*superplayerlogic.UseSVIPStealthAheadResp, error)
	GetRenewalReminder(ctx context.Context, in *superplayerlogic.GetRenewalReminderReq, opts ...grpc.CallOption) (*superplayerlogic.GetRenewalReminderResp, error)
	ReportSneakilyRead(ctx context.Context, in *superplayerlogic.ReportSneakilyReadReq, opts ...grpc.CallOption) (*superplayerlogic.ReportSneakilyReadResp, error)
	CheckCouponPopUp(ctx context.Context, in *superplayerlogic.CheckCouponPopUpReq, opts ...grpc.CallOption) (*superplayerlogic.CheckCouponPopUpResp, error)
	GetCouponPopUpLimit(ctx context.Context, in *superplayerlogic.GetCouponPopUpLimitReq, opts ...grpc.CallOption) (*superplayerlogic.GetCouponPopUpLimitResp, error)
}

type superPlayerLogicClient struct {
	cc *grpc.ClientConn
}

func NewSuperPlayerLogicClient(cc *grpc.ClientConn) SuperPlayerLogicClient {
	return &superPlayerLogicClient{cc}
}

func (c *superPlayerLogicClient) GetSuperPlayerConf(ctx context.Context, in *superplayerlogic.GetSuperPlayerConfReq, opts ...grpc.CallOption) (*superplayerlogic.GetSuperPlayerConfResp, error) {
	out := new(superplayerlogic.GetSuperPlayerConfResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/GetSuperPlayerConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerLogicClient) GetSuperPlayerInfo(ctx context.Context, in *superplayerlogic.GetSuperPlayerInfoReq, opts ...grpc.CallOption) (*superplayerlogic.GetSuperPlayerInfoResp, error) {
	out := new(superplayerlogic.GetSuperPlayerInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/GetSuperPlayerInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerLogicClient) BatchGetSuperPlayerInfo(ctx context.Context, in *superplayerlogic.BatchGetSuperPlayerInfoReq, opts ...grpc.CallOption) (*superplayerlogic.BatchGetSuperPlayerInfoResp, error) {
	out := new(superplayerlogic.BatchGetSuperPlayerInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/BatchGetSuperPlayerInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerLogicClient) GetUserSpecialConcern(ctx context.Context, in *superplayerlogic.GetUserSpecialConcernReq, opts ...grpc.CallOption) (*superplayerlogic.GetUserSpecialConcernResp, error) {
	out := new(superplayerlogic.GetUserSpecialConcernResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/GetUserSpecialConcern", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerLogicClient) AddUserSpecialConcern(ctx context.Context, in *superplayerlogic.AddUserSpecialConcernReq, opts ...grpc.CallOption) (*superplayerlogic.AddUserSpecialConcernResp, error) {
	out := new(superplayerlogic.AddUserSpecialConcernResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/AddUserSpecialConcern", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerLogicClient) DelUserSpecialConcern(ctx context.Context, in *superplayerlogic.DelUserSpecialConcernReq, opts ...grpc.CallOption) (*superplayerlogic.DelUserSpecialConcernResp, error) {
	out := new(superplayerlogic.DelUserSpecialConcernResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/DelUserSpecialConcern", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerLogicClient) GetIMPrivilegeCount(ctx context.Context, in *superplayerlogic.GetIMPrivilegeCountReq, opts ...grpc.CallOption) (*superplayerlogic.GetIMPrivilegeCountResp, error) {
	out := new(superplayerlogic.GetIMPrivilegeCountResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/GetIMPrivilegeCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerLogicClient) UseIMPrivilege(ctx context.Context, in *superplayerlogic.UseIMPrivilegeReq, opts ...grpc.CallOption) (*superplayerlogic.UseIMPrivilegeResp, error) {
	out := new(superplayerlogic.UseIMPrivilegeResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/UseIMPrivilege", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerLogicClient) GetUserSVIPPrivilegeProfile(ctx context.Context, in *superplayerlogic.GetUserSVIPPrivilegeProfileReq, opts ...grpc.CallOption) (*superplayerlogic.GetUserSVIPPrivilegeProfileResp, error) {
	out := new(superplayerlogic.GetUserSVIPPrivilegeProfileResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/GetUserSVIPPrivilegeProfile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerLogicClient) SetUserSVIPPrivilegeProfile(ctx context.Context, in *superplayerlogic.SetUserSVIPPrivilegeProfileReq, opts ...grpc.CallOption) (*superplayerlogic.SetUserSVIPPrivilegeProfileResp, error) {
	out := new(superplayerlogic.SetUserSVIPPrivilegeProfileResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/SetUserSVIPPrivilegeProfile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerLogicClient) UseSVIPStealthAhead(ctx context.Context, in *superplayerlogic.UseSVIPStealthAheadReq, opts ...grpc.CallOption) (*superplayerlogic.UseSVIPStealthAheadResp, error) {
	out := new(superplayerlogic.UseSVIPStealthAheadResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/UseSVIPStealthAhead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerLogicClient) GetRenewalReminder(ctx context.Context, in *superplayerlogic.GetRenewalReminderReq, opts ...grpc.CallOption) (*superplayerlogic.GetRenewalReminderResp, error) {
	out := new(superplayerlogic.GetRenewalReminderResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/GetRenewalReminder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerLogicClient) ReportSneakilyRead(ctx context.Context, in *superplayerlogic.ReportSneakilyReadReq, opts ...grpc.CallOption) (*superplayerlogic.ReportSneakilyReadResp, error) {
	out := new(superplayerlogic.ReportSneakilyReadResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/ReportSneakilyRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerLogicClient) CheckCouponPopUp(ctx context.Context, in *superplayerlogic.CheckCouponPopUpReq, opts ...grpc.CallOption) (*superplayerlogic.CheckCouponPopUpResp, error) {
	out := new(superplayerlogic.CheckCouponPopUpResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/CheckCouponPopUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerLogicClient) GetCouponPopUpLimit(ctx context.Context, in *superplayerlogic.GetCouponPopUpLimitReq, opts ...grpc.CallOption) (*superplayerlogic.GetCouponPopUpLimitResp, error) {
	out := new(superplayerlogic.GetCouponPopUpLimitResp)
	err := c.cc.Invoke(ctx, "/ga.api.super_player.SuperPlayerLogic/GetCouponPopUpLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SuperPlayerLogicServer is the server API for SuperPlayerLogic service.
type SuperPlayerLogicServer interface {
	GetSuperPlayerConf(context.Context, *superplayerlogic.GetSuperPlayerConfReq) (*superplayerlogic.GetSuperPlayerConfResp, error)
	GetSuperPlayerInfo(context.Context, *superplayerlogic.GetSuperPlayerInfoReq) (*superplayerlogic.GetSuperPlayerInfoResp, error)
	BatchGetSuperPlayerInfo(context.Context, *superplayerlogic.BatchGetSuperPlayerInfoReq) (*superplayerlogic.BatchGetSuperPlayerInfoResp, error)
	GetUserSpecialConcern(context.Context, *superplayerlogic.GetUserSpecialConcernReq) (*superplayerlogic.GetUserSpecialConcernResp, error)
	AddUserSpecialConcern(context.Context, *superplayerlogic.AddUserSpecialConcernReq) (*superplayerlogic.AddUserSpecialConcernResp, error)
	DelUserSpecialConcern(context.Context, *superplayerlogic.DelUserSpecialConcernReq) (*superplayerlogic.DelUserSpecialConcernResp, error)
	GetIMPrivilegeCount(context.Context, *superplayerlogic.GetIMPrivilegeCountReq) (*superplayerlogic.GetIMPrivilegeCountResp, error)
	UseIMPrivilege(context.Context, *superplayerlogic.UseIMPrivilegeReq) (*superplayerlogic.UseIMPrivilegeResp, error)
	GetUserSVIPPrivilegeProfile(context.Context, *superplayerlogic.GetUserSVIPPrivilegeProfileReq) (*superplayerlogic.GetUserSVIPPrivilegeProfileResp, error)
	SetUserSVIPPrivilegeProfile(context.Context, *superplayerlogic.SetUserSVIPPrivilegeProfileReq) (*superplayerlogic.SetUserSVIPPrivilegeProfileResp, error)
	UseSVIPStealthAhead(context.Context, *superplayerlogic.UseSVIPStealthAheadReq) (*superplayerlogic.UseSVIPStealthAheadResp, error)
	GetRenewalReminder(context.Context, *superplayerlogic.GetRenewalReminderReq) (*superplayerlogic.GetRenewalReminderResp, error)
	ReportSneakilyRead(context.Context, *superplayerlogic.ReportSneakilyReadReq) (*superplayerlogic.ReportSneakilyReadResp, error)
	CheckCouponPopUp(context.Context, *superplayerlogic.CheckCouponPopUpReq) (*superplayerlogic.CheckCouponPopUpResp, error)
	GetCouponPopUpLimit(context.Context, *superplayerlogic.GetCouponPopUpLimitReq) (*superplayerlogic.GetCouponPopUpLimitResp, error)
}

func RegisterSuperPlayerLogicServer(s *grpc.Server, srv SuperPlayerLogicServer) {
	s.RegisterService(&_SuperPlayerLogic_serviceDesc, srv)
}

func _SuperPlayerLogic_GetSuperPlayerConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.GetSuperPlayerConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).GetSuperPlayerConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/GetSuperPlayerConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).GetSuperPlayerConf(ctx, req.(*superplayerlogic.GetSuperPlayerConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerLogic_GetSuperPlayerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.GetSuperPlayerInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).GetSuperPlayerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/GetSuperPlayerInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).GetSuperPlayerInfo(ctx, req.(*superplayerlogic.GetSuperPlayerInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerLogic_BatchGetSuperPlayerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.BatchGetSuperPlayerInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).BatchGetSuperPlayerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/BatchGetSuperPlayerInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).BatchGetSuperPlayerInfo(ctx, req.(*superplayerlogic.BatchGetSuperPlayerInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerLogic_GetUserSpecialConcern_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.GetUserSpecialConcernReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).GetUserSpecialConcern(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/GetUserSpecialConcern",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).GetUserSpecialConcern(ctx, req.(*superplayerlogic.GetUserSpecialConcernReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerLogic_AddUserSpecialConcern_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.AddUserSpecialConcernReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).AddUserSpecialConcern(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/AddUserSpecialConcern",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).AddUserSpecialConcern(ctx, req.(*superplayerlogic.AddUserSpecialConcernReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerLogic_DelUserSpecialConcern_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.DelUserSpecialConcernReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).DelUserSpecialConcern(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/DelUserSpecialConcern",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).DelUserSpecialConcern(ctx, req.(*superplayerlogic.DelUserSpecialConcernReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerLogic_GetIMPrivilegeCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.GetIMPrivilegeCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).GetIMPrivilegeCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/GetIMPrivilegeCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).GetIMPrivilegeCount(ctx, req.(*superplayerlogic.GetIMPrivilegeCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerLogic_UseIMPrivilege_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.UseIMPrivilegeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).UseIMPrivilege(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/UseIMPrivilege",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).UseIMPrivilege(ctx, req.(*superplayerlogic.UseIMPrivilegeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerLogic_GetUserSVIPPrivilegeProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.GetUserSVIPPrivilegeProfileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).GetUserSVIPPrivilegeProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/GetUserSVIPPrivilegeProfile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).GetUserSVIPPrivilegeProfile(ctx, req.(*superplayerlogic.GetUserSVIPPrivilegeProfileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerLogic_SetUserSVIPPrivilegeProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.SetUserSVIPPrivilegeProfileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).SetUserSVIPPrivilegeProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/SetUserSVIPPrivilegeProfile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).SetUserSVIPPrivilegeProfile(ctx, req.(*superplayerlogic.SetUserSVIPPrivilegeProfileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerLogic_UseSVIPStealthAhead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.UseSVIPStealthAheadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).UseSVIPStealthAhead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/UseSVIPStealthAhead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).UseSVIPStealthAhead(ctx, req.(*superplayerlogic.UseSVIPStealthAheadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerLogic_GetRenewalReminder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.GetRenewalReminderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).GetRenewalReminder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/GetRenewalReminder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).GetRenewalReminder(ctx, req.(*superplayerlogic.GetRenewalReminderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerLogic_ReportSneakilyRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.ReportSneakilyReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).ReportSneakilyRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/ReportSneakilyRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).ReportSneakilyRead(ctx, req.(*superplayerlogic.ReportSneakilyReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerLogic_CheckCouponPopUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.CheckCouponPopUpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).CheckCouponPopUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/CheckCouponPopUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).CheckCouponPopUp(ctx, req.(*superplayerlogic.CheckCouponPopUpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerLogic_GetCouponPopUpLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(superplayerlogic.GetCouponPopUpLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerLogicServer).GetCouponPopUpLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.super_player.SuperPlayerLogic/GetCouponPopUpLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerLogicServer).GetCouponPopUpLimit(ctx, req.(*superplayerlogic.GetCouponPopUpLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SuperPlayerLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.super_player.SuperPlayerLogic",
	HandlerType: (*SuperPlayerLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSuperPlayerConf",
			Handler:    _SuperPlayerLogic_GetSuperPlayerConf_Handler,
		},
		{
			MethodName: "GetSuperPlayerInfo",
			Handler:    _SuperPlayerLogic_GetSuperPlayerInfo_Handler,
		},
		{
			MethodName: "BatchGetSuperPlayerInfo",
			Handler:    _SuperPlayerLogic_BatchGetSuperPlayerInfo_Handler,
		},
		{
			MethodName: "GetUserSpecialConcern",
			Handler:    _SuperPlayerLogic_GetUserSpecialConcern_Handler,
		},
		{
			MethodName: "AddUserSpecialConcern",
			Handler:    _SuperPlayerLogic_AddUserSpecialConcern_Handler,
		},
		{
			MethodName: "DelUserSpecialConcern",
			Handler:    _SuperPlayerLogic_DelUserSpecialConcern_Handler,
		},
		{
			MethodName: "GetIMPrivilegeCount",
			Handler:    _SuperPlayerLogic_GetIMPrivilegeCount_Handler,
		},
		{
			MethodName: "UseIMPrivilege",
			Handler:    _SuperPlayerLogic_UseIMPrivilege_Handler,
		},
		{
			MethodName: "GetUserSVIPPrivilegeProfile",
			Handler:    _SuperPlayerLogic_GetUserSVIPPrivilegeProfile_Handler,
		},
		{
			MethodName: "SetUserSVIPPrivilegeProfile",
			Handler:    _SuperPlayerLogic_SetUserSVIPPrivilegeProfile_Handler,
		},
		{
			MethodName: "UseSVIPStealthAhead",
			Handler:    _SuperPlayerLogic_UseSVIPStealthAhead_Handler,
		},
		{
			MethodName: "GetRenewalReminder",
			Handler:    _SuperPlayerLogic_GetRenewalReminder_Handler,
		},
		{
			MethodName: "ReportSneakilyRead",
			Handler:    _SuperPlayerLogic_ReportSneakilyRead_Handler,
		},
		{
			MethodName: "CheckCouponPopUp",
			Handler:    _SuperPlayerLogic_CheckCouponPopUp_Handler,
		},
		{
			MethodName: "GetCouponPopUpLimit",
			Handler:    _SuperPlayerLogic_GetCouponPopUpLimit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/super_player/grpc_super_player.proto",
}

func init() {
	proto.RegisterFile("api/super_player/grpc_super_player.proto", fileDescriptor_grpc_super_player_e12e59431c51756e)
}

var fileDescriptor_grpc_super_player_e12e59431c51756e = []byte{
	// 606 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x96, 0x4d, 0x6f, 0xd3, 0x30,
	0x18, 0xc7, 0xb5, 0x4d, 0x02, 0x94, 0x03, 0x4c, 0x9e, 0x78, 0x2b, 0xa9, 0x54, 0x71, 0x80, 0xf1,
	0xb2, 0x04, 0x6d, 0x20, 0x21, 0x38, 0x6d, 0x41, 0xaa, 0x2a, 0x0d, 0x29, 0x4a, 0x54, 0x0e, 0xbb,
	0x54, 0x26, 0x7d, 0x9a, 0x9a, 0xa5, 0xb6, 0x97, 0xb8, 0x1b, 0x3d, 0x51, 0x8a, 0xb8, 0x70, 0x83,
	0x8f, 0xc0, 0x07, 0xe0, 0xd3, 0xf1, 0x8e, 0x78, 0x91, 0x93, 0xcd, 0x84, 0x24, 0xae, 0x52, 0x6e,
	0x71, 0xfc, 0x7b, 0xfc, 0xfb, 0x47, 0x7a, 0x6c, 0xc7, 0x58, 0xc7, 0x9c, 0xd8, 0xc9, 0x98, 0x43,
	0xdc, 0xe3, 0x11, 0x9e, 0x40, 0x6c, 0x87, 0x31, 0x0f, 0x7a, 0xf9, 0x37, 0x16, 0x8f, 0x99, 0x60,
	0x68, 0x2d, 0xc4, 0x16, 0xe6, 0xc4, 0xca, 0x4f, 0x35, 0x2e, 0xa7, 0xa3, 0x8d, 0x6c, 0xb4, 0x11,
	0xb1, 0x90, 0x04, 0xbd, 0x8c, 0x6f, 0x34, 0xe5, 0xca, 0xf0, 0x5c, 0x00, 0x4d, 0x08, 0xa3, 0x7f,
	0x9f, 0xb2, 0xe9, 0xcd, 0x0f, 0xe7, 0x8c, 0x55, 0x5f, 0x16, 0xbb, 0x69, 0xed, 0xae, 0x2c, 0x45,
	0x87, 0x06, 0x6a, 0x83, 0xc8, 0xbd, 0x76, 0x18, 0x1d, 0xa0, 0x9b, 0x56, 0x88, 0x33, 0x6d, 0xe6,
	0x49, 0x35, 0x56, 0x19, 0xf4, 0xe0, 0xa0, 0x71, 0xab, 0x36, 0x9b, 0xf0, 0xab, 0xa7, 0x67, 0xd3,
	0xd6, 0xca, 0x99, 0x8f, 0x66, 0xd9, 0xdb, 0xa1, 0x03, 0x56, 0xcb, 0x2b, 0xc1, 0xba, 0xde, 0x8c,
	0x55, 0xde, 0x4f, 0x26, 0x7a, 0xbd, 0x64, 0x5c, 0xdc, 0xc1, 0x22, 0x18, 0x56, 0xd8, 0xed, 0xca,
	0x15, 0x35, 0xb4, 0x8c, 0x70, 0x67, 0xb1, 0x02, 0x95, 0xe3, 0xb3, 0x89, 0x5e, 0x18, 0xe7, 0xdb,
	0x20, 0xba, 0x09, 0xc4, 0x3e, 0x87, 0x80, 0xe0, 0xc8, 0x61, 0x34, 0x80, 0x98, 0xa2, 0x0d, 0xdd,
	0x67, 0x95, 0x59, 0x19, 0xc1, 0x5a, 0x04, 0x57, 0x01, 0xbe, 0xa4, 0x01, 0xb6, 0xfb, 0xfd, 0xda,
	0x01, 0x2a, 0x59, 0x7d, 0x00, 0x0d, 0xae, 0x02, 0x7c, 0x4d, 0x03, 0x3c, 0x82, 0xa8, 0x76, 0x80,
	0x4a, 0x56, 0x1f, 0x40, 0x83, 0xab, 0x00, 0xdf, 0x4c, 0x34, 0x31, 0xd6, 0xda, 0x20, 0x3a, 0x8f,
	0xdd, 0x98, 0x1c, 0x92, 0x08, 0x42, 0x70, 0xd8, 0x98, 0x0a, 0xa4, 0xed, 0xab, 0x22, 0x29, 0xe5,
	0xb7, 0xeb, 0xc3, 0x4a, 0xfd, 0xdd, 0x44, 0xcf, 0x8c, 0xb3, 0xdd, 0x04, 0x72, 0x0c, 0xba, 0x56,
	0xb9, 0xd0, 0xbf, 0x90, 0x14, 0x5e, 0xaf, 0xc5, 0x29, 0xd7, 0x0f, 0x13, 0xbd, 0x5d, 0x32, 0xae,
	0x9c, 0xf4, 0xc3, 0x93, 0x8e, 0xab, 0x28, 0x37, 0x66, 0x03, 0x12, 0x01, 0xda, 0x9a, 0xdb, 0x41,
	0x15, 0x15, 0x32, 0xc6, 0xdd, 0xc5, 0x8b, 0x54, 0xa6, 0x9f, 0x59, 0x26, 0x7f, 0xe1, 0x4c, 0xfe,
	0xff, 0x64, 0xf2, 0x6b, 0x65, 0xfa, 0x95, 0xb6, 0x43, 0x37, 0x01, 0xc9, 0xf9, 0x02, 0x70, 0x24,
	0x86, 0xdb, 0x43, 0xc0, 0x7d, 0x4d, 0x3b, 0x54, 0x90, 0xfa, 0x76, 0xa8, 0x84, 0x95, 0xfa, 0xf7,
	0xc9, 0x61, 0xe8, 0x01, 0x85, 0x23, 0x1c, 0x79, 0x30, 0x22, 0xb4, 0x0f, 0xb1, 0xfe, 0x30, 0x2c,
	0x80, 0x73, 0x0f, 0xc3, 0x12, 0xab, 0xbc, 0xd3, 0xa6, 0xf4, 0x7a, 0xc0, 0x59, 0x2c, 0x7c, 0x0a,
	0x78, 0x9f, 0x44, 0x13, 0x4f, 0x7e, 0x71, 0xb5, 0xb7, 0x0c, 0xea, 0xbd, 0x55, 0xac, 0xf2, 0xbe,
	0x6c, 0x22, 0x6e, 0xac, 0x3a, 0x43, 0x08, 0xf6, 0x1d, 0x36, 0xe6, 0x8c, 0xba, 0x8c, 0x77, 0x39,
	0x5a, 0xaf, 0x5c, 0xa9, 0x88, 0x49, 0xe7, 0x8d, 0x9a, 0xa4, 0x32, 0xce, 0x9a, 0xc7, 0x7b, 0x3d,
	0x37, 0xbd, 0x4b, 0x46, 0x64, 0xce, 0x5e, 0x2f, 0x92, 0x73, 0xf7, 0x7a, 0x19, 0x56, 0xea, 0x57,
	0xcd, 0xc6, 0xfd, 0xd9, 0xb4, 0x85, 0xca, 0x97, 0xf6, 0x9b, 0x69, 0x6b, 0x39, 0x64, 0xef, 0xa6,
	0xad, 0x4b, 0xf6, 0x71, 0xa3, 0x16, 0xae, 0x66, 0x7b, 0x67, 0xcf, 0xb8, 0x10, 0xb0, 0x91, 0x75,
	0x30, 0x3e, 0xc2, 0xd4, 0x12, 0x22, 0xbb, 0xc6, 0xe5, 0x1f, 0xc1, 0xde, 0x83, 0x90, 0x45, 0x98,
	0x86, 0xd6, 0xbd, 0x4d, 0x21, 0xac, 0x80, 0x8d, 0xec, 0x74, 0x2a, 0x60, 0x91, 0x8d, 0x39, 0xb7,
	0x8b, 0x3f, 0x18, 0x0f, 0xf3, 0x83, 0xf7, 0xcb, 0x2b, 0x9e, 0xeb, 0x3c, 0x3d, 0x95, 0x56, 0x6c,
	0xfd, 0x09, 0x00, 0x00, 0xff, 0xff, 0x10, 0xb5, 0x1c, 0x50, 0x8e, 0x08, 0x00, 0x00,
}
