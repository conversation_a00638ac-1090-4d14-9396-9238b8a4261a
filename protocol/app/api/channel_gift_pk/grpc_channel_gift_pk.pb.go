// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/channel_gift_pk/grpc_channel_gift_pk.proto

package channel_gift_pk // import "golang.52tt.com/protocol/app/api/channel_gift_pk"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import channel_gift_pk "golang.52tt.com/protocol/app/channel_gift_pk"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelGiftPkLogicClient is the client API for ChannelGiftPkLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelGiftPkLogicClient interface {
	// 获取入口状态
	CheckChannelGiftPkEntry(ctx context.Context, in *channel_gift_pk.CheckChannelGiftPkEntryRequest, opts ...grpc.CallOption) (*channel_gift_pk.CheckChannelGiftPkEntryResponse, error)
	// 获取对决信息
	GetChannelGiftPkInfo(ctx context.Context, in *channel_gift_pk.GetChannelGiftPkInfoRequest, opts ...grpc.CallOption) (*channel_gift_pk.GetChannelGiftPkInfoResponse, error)
	// 发起对决
	SponsorChannelGiftPk(ctx context.Context, in *channel_gift_pk.SponsorChannelGiftPkRequest, opts ...grpc.CallOption) (*channel_gift_pk.SponsorChannelGiftPkResponse, error)
	// 取消对决匹配
	CancelChannelGiftPkMatch(ctx context.Context, in *channel_gift_pk.CancelChannelGiftPkMatchRequest, opts ...grpc.CallOption) (*channel_gift_pk.CancelChannelGiftPkMatchResponse, error)
	// 选择对决精灵
	ChooseChannelGiftPkSprite(ctx context.Context, in *channel_gift_pk.ChooseChannelGiftPkSpriteRequest, opts ...grpc.CallOption) (*channel_gift_pk.ChooseChannelGiftPkSpriteResponse, error)
	// 获取最新对决记录播报
	GetRecentlyChannelGiftPkLog(ctx context.Context, in *channel_gift_pk.GetRecentlyChannelGiftPkLogRequest, opts ...grpc.CallOption) (*channel_gift_pk.GetRecentlyChannelGiftPkLogResponse, error)
	// 获取对决记录
	GetChannelGiftPkRecord(ctx context.Context, in *channel_gift_pk.GetChannelGiftPkRecordRequest, opts ...grpc.CallOption) (*channel_gift_pk.GetChannelGiftPkRecordResponse, error)
}

type channelGiftPkLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelGiftPkLogicClient(cc *grpc.ClientConn) ChannelGiftPkLogicClient {
	return &channelGiftPkLogicClient{cc}
}

func (c *channelGiftPkLogicClient) CheckChannelGiftPkEntry(ctx context.Context, in *channel_gift_pk.CheckChannelGiftPkEntryRequest, opts ...grpc.CallOption) (*channel_gift_pk.CheckChannelGiftPkEntryResponse, error) {
	out := new(channel_gift_pk.CheckChannelGiftPkEntryResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_gift_pk.ChannelGiftPkLogic/CheckChannelGiftPkEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGiftPkLogicClient) GetChannelGiftPkInfo(ctx context.Context, in *channel_gift_pk.GetChannelGiftPkInfoRequest, opts ...grpc.CallOption) (*channel_gift_pk.GetChannelGiftPkInfoResponse, error) {
	out := new(channel_gift_pk.GetChannelGiftPkInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_gift_pk.ChannelGiftPkLogic/GetChannelGiftPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGiftPkLogicClient) SponsorChannelGiftPk(ctx context.Context, in *channel_gift_pk.SponsorChannelGiftPkRequest, opts ...grpc.CallOption) (*channel_gift_pk.SponsorChannelGiftPkResponse, error) {
	out := new(channel_gift_pk.SponsorChannelGiftPkResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_gift_pk.ChannelGiftPkLogic/SponsorChannelGiftPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGiftPkLogicClient) CancelChannelGiftPkMatch(ctx context.Context, in *channel_gift_pk.CancelChannelGiftPkMatchRequest, opts ...grpc.CallOption) (*channel_gift_pk.CancelChannelGiftPkMatchResponse, error) {
	out := new(channel_gift_pk.CancelChannelGiftPkMatchResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_gift_pk.ChannelGiftPkLogic/CancelChannelGiftPkMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGiftPkLogicClient) ChooseChannelGiftPkSprite(ctx context.Context, in *channel_gift_pk.ChooseChannelGiftPkSpriteRequest, opts ...grpc.CallOption) (*channel_gift_pk.ChooseChannelGiftPkSpriteResponse, error) {
	out := new(channel_gift_pk.ChooseChannelGiftPkSpriteResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_gift_pk.ChannelGiftPkLogic/ChooseChannelGiftPkSprite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGiftPkLogicClient) GetRecentlyChannelGiftPkLog(ctx context.Context, in *channel_gift_pk.GetRecentlyChannelGiftPkLogRequest, opts ...grpc.CallOption) (*channel_gift_pk.GetRecentlyChannelGiftPkLogResponse, error) {
	out := new(channel_gift_pk.GetRecentlyChannelGiftPkLogResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_gift_pk.ChannelGiftPkLogic/GetRecentlyChannelGiftPkLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGiftPkLogicClient) GetChannelGiftPkRecord(ctx context.Context, in *channel_gift_pk.GetChannelGiftPkRecordRequest, opts ...grpc.CallOption) (*channel_gift_pk.GetChannelGiftPkRecordResponse, error) {
	out := new(channel_gift_pk.GetChannelGiftPkRecordResponse)
	err := c.cc.Invoke(ctx, "/ga.api.channel_gift_pk.ChannelGiftPkLogic/GetChannelGiftPkRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelGiftPkLogicServer is the server API for ChannelGiftPkLogic service.
type ChannelGiftPkLogicServer interface {
	// 获取入口状态
	CheckChannelGiftPkEntry(context.Context, *channel_gift_pk.CheckChannelGiftPkEntryRequest) (*channel_gift_pk.CheckChannelGiftPkEntryResponse, error)
	// 获取对决信息
	GetChannelGiftPkInfo(context.Context, *channel_gift_pk.GetChannelGiftPkInfoRequest) (*channel_gift_pk.GetChannelGiftPkInfoResponse, error)
	// 发起对决
	SponsorChannelGiftPk(context.Context, *channel_gift_pk.SponsorChannelGiftPkRequest) (*channel_gift_pk.SponsorChannelGiftPkResponse, error)
	// 取消对决匹配
	CancelChannelGiftPkMatch(context.Context, *channel_gift_pk.CancelChannelGiftPkMatchRequest) (*channel_gift_pk.CancelChannelGiftPkMatchResponse, error)
	// 选择对决精灵
	ChooseChannelGiftPkSprite(context.Context, *channel_gift_pk.ChooseChannelGiftPkSpriteRequest) (*channel_gift_pk.ChooseChannelGiftPkSpriteResponse, error)
	// 获取最新对决记录播报
	GetRecentlyChannelGiftPkLog(context.Context, *channel_gift_pk.GetRecentlyChannelGiftPkLogRequest) (*channel_gift_pk.GetRecentlyChannelGiftPkLogResponse, error)
	// 获取对决记录
	GetChannelGiftPkRecord(context.Context, *channel_gift_pk.GetChannelGiftPkRecordRequest) (*channel_gift_pk.GetChannelGiftPkRecordResponse, error)
}

func RegisterChannelGiftPkLogicServer(s *grpc.Server, srv ChannelGiftPkLogicServer) {
	s.RegisterService(&_ChannelGiftPkLogic_serviceDesc, srv)
}

func _ChannelGiftPkLogic_CheckChannelGiftPkEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_gift_pk.CheckChannelGiftPkEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGiftPkLogicServer).CheckChannelGiftPkEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_gift_pk.ChannelGiftPkLogic/CheckChannelGiftPkEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGiftPkLogicServer).CheckChannelGiftPkEntry(ctx, req.(*channel_gift_pk.CheckChannelGiftPkEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGiftPkLogic_GetChannelGiftPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_gift_pk.GetChannelGiftPkInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGiftPkLogicServer).GetChannelGiftPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_gift_pk.ChannelGiftPkLogic/GetChannelGiftPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGiftPkLogicServer).GetChannelGiftPkInfo(ctx, req.(*channel_gift_pk.GetChannelGiftPkInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGiftPkLogic_SponsorChannelGiftPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_gift_pk.SponsorChannelGiftPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGiftPkLogicServer).SponsorChannelGiftPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_gift_pk.ChannelGiftPkLogic/SponsorChannelGiftPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGiftPkLogicServer).SponsorChannelGiftPk(ctx, req.(*channel_gift_pk.SponsorChannelGiftPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGiftPkLogic_CancelChannelGiftPkMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_gift_pk.CancelChannelGiftPkMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGiftPkLogicServer).CancelChannelGiftPkMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_gift_pk.ChannelGiftPkLogic/CancelChannelGiftPkMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGiftPkLogicServer).CancelChannelGiftPkMatch(ctx, req.(*channel_gift_pk.CancelChannelGiftPkMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGiftPkLogic_ChooseChannelGiftPkSprite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_gift_pk.ChooseChannelGiftPkSpriteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGiftPkLogicServer).ChooseChannelGiftPkSprite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_gift_pk.ChannelGiftPkLogic/ChooseChannelGiftPkSprite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGiftPkLogicServer).ChooseChannelGiftPkSprite(ctx, req.(*channel_gift_pk.ChooseChannelGiftPkSpriteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGiftPkLogic_GetRecentlyChannelGiftPkLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_gift_pk.GetRecentlyChannelGiftPkLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGiftPkLogicServer).GetRecentlyChannelGiftPkLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_gift_pk.ChannelGiftPkLogic/GetRecentlyChannelGiftPkLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGiftPkLogicServer).GetRecentlyChannelGiftPkLog(ctx, req.(*channel_gift_pk.GetRecentlyChannelGiftPkLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGiftPkLogic_GetChannelGiftPkRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_gift_pk.GetChannelGiftPkRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGiftPkLogicServer).GetChannelGiftPkRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_gift_pk.ChannelGiftPkLogic/GetChannelGiftPkRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGiftPkLogicServer).GetChannelGiftPkRecord(ctx, req.(*channel_gift_pk.GetChannelGiftPkRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelGiftPkLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.channel_gift_pk.ChannelGiftPkLogic",
	HandlerType: (*ChannelGiftPkLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckChannelGiftPkEntry",
			Handler:    _ChannelGiftPkLogic_CheckChannelGiftPkEntry_Handler,
		},
		{
			MethodName: "GetChannelGiftPkInfo",
			Handler:    _ChannelGiftPkLogic_GetChannelGiftPkInfo_Handler,
		},
		{
			MethodName: "SponsorChannelGiftPk",
			Handler:    _ChannelGiftPkLogic_SponsorChannelGiftPk_Handler,
		},
		{
			MethodName: "CancelChannelGiftPkMatch",
			Handler:    _ChannelGiftPkLogic_CancelChannelGiftPkMatch_Handler,
		},
		{
			MethodName: "ChooseChannelGiftPkSprite",
			Handler:    _ChannelGiftPkLogic_ChooseChannelGiftPkSprite_Handler,
		},
		{
			MethodName: "GetRecentlyChannelGiftPkLog",
			Handler:    _ChannelGiftPkLogic_GetRecentlyChannelGiftPkLog_Handler,
		},
		{
			MethodName: "GetChannelGiftPkRecord",
			Handler:    _ChannelGiftPkLogic_GetChannelGiftPkRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/channel_gift_pk/grpc_channel_gift_pk.proto",
}

func init() {
	proto.RegisterFile("api/channel_gift_pk/grpc_channel_gift_pk.proto", fileDescriptor_grpc_channel_gift_pk_5cd7eb3459d21036)
}

var fileDescriptor_grpc_channel_gift_pk_5cd7eb3459d21036 = []byte{
	// 434 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x94, 0xcf, 0x8b, 0xd3, 0x40,
	0x14, 0xc7, 0xc9, 0x28, 0x52, 0xe6, 0x38, 0x68, 0xd5, 0x88, 0x58, 0x04, 0x8f, 0x9d, 0x68, 0x6a,
	0xf5, 0xe0, 0x45, 0x0c, 0x52, 0x04, 0x85, 0x92, 0xde, 0xbc, 0xd4, 0x71, 0x9c, 0x4e, 0x42, 0xd2,
	0x99, 0x69, 0x32, 0x45, 0x7b, 0x2b, 0xf5, 0xa4, 0x08, 0x82, 0x27, 0x4f, 0x1e, 0xfc, 0x17, 0x72,
	0xf0, 0xc7, 0xee, 0xfe, 0x6f, 0x4b, 0x9a, 0x86, 0x65, 0xc2, 0x64, 0x69, 0x6e, 0xc9, 0x7b, 0xdf,
	0xcf, 0xcb, 0x87, 0xf0, 0x78, 0x10, 0x13, 0x15, 0x7b, 0x34, 0x22, 0x42, 0xb0, 0x74, 0xce, 0xe3,
	0x85, 0x9e, 0xab, 0xc4, 0xe3, 0x99, 0xa2, 0xf3, 0x46, 0x11, 0xab, 0x4c, 0x6a, 0x89, 0xfa, 0x9c,
	0x94, 0x08, 0x6e, 0x74, 0xdd, 0xbb, 0xe5, 0x1c, 0xf6, 0x49, 0x33, 0x91, 0xc7, 0x52, 0x5c, 0x3c,
	0x55, 0x98, 0xfb, 0xa0, 0xf9, 0x09, 0xeb, 0x74, 0xff, 0x57, 0x0f, 0xa2, 0xa0, 0xea, 0x4c, 0xe2,
	0x85, 0x9e, 0x26, 0xaf, 0x25, 0x8f, 0x29, 0xfa, 0xe6, 0xc0, 0x9b, 0x41, 0xc4, 0x68, 0x62, 0xf4,
	0x5e, 0x0a, 0x9d, 0x6d, 0x90, 0x8f, 0x39, 0x69, 0xda, 0xe0, 0x96, 0x70, 0xc8, 0x56, 0x6b, 0x96,
	0x6b, 0x77, 0xd4, 0x89, 0xc9, 0x95, 0x14, 0x39, 0xbb, 0xdf, 0xdb, 0x6d, 0x07, 0x57, 0x7b, 0x7f,
	0x0a, 0x80, 0x3e, 0x3b, 0xf0, 0xfa, 0x84, 0x69, 0x23, 0xfb, 0x4a, 0x2c, 0x24, 0xf2, 0x6c, 0x73,
	0x6d, 0xc9, 0x5a, 0xe4, 0xe1, 0xf1, 0x80, 0x61, 0xf1, 0xf7, 0x60, 0x31, 0x2b, 0x8b, 0x32, 0x33,
	0xe2, 0x76, 0x0b, 0x5b, 0xf2, 0x52, 0x0b, 0x3b, 0x60, 0x58, 0xfc, 0x2b, 0x00, 0xfa, 0xee, 0xc0,
	0x5b, 0x01, 0x11, 0x94, 0xa5, 0x46, 0xf2, 0x0d, 0xd1, 0x34, 0x42, 0xf6, 0xff, 0xdc, 0x92, 0xae,
	0x6d, 0x1e, 0x77, 0x83, 0x0c, 0xa3, 0xff, 0x05, 0x40, 0x3f, 0x1c, 0x78, 0x3b, 0x88, 0xa4, 0xcc,
	0x99, 0x11, 0x9f, 0xa9, 0x2c, 0xd6, 0x0c, 0xd9, 0xa7, 0xb7, 0xc5, 0x6b, 0xa7, 0x71, 0x47, 0xca,
	0x90, 0x3a, 0x29, 0x00, 0xfa, 0xe9, 0xc0, 0x3b, 0x13, 0xa6, 0x43, 0x46, 0x99, 0xd0, 0xe9, 0xa6,
	0xb9, 0xe3, 0xe8, 0x49, 0xcb, 0x22, 0xb4, 0x01, 0xb5, 0xd8, 0xd3, 0xce, 0x9c, 0xa1, 0x76, 0x5a,
	0x00, 0xf4, 0xc5, 0x81, 0xfd, 0xe6, 0xca, 0x85, 0x8c, 0xca, 0xec, 0x03, 0x7a, 0x74, 0xcc, 0x7a,
	0x56, 0xd9, 0x5a, 0xc8, 0xef, 0x82, 0x18, 0x2e, 0x67, 0x05, 0x70, 0xef, 0xed, 0xb6, 0x83, 0x1b,
	0x07, 0x7a, 0x58, 0xd2, 0x43, 0x95, 0x0c, 0xd3, 0xf2, 0x06, 0x7c, 0xdd, 0x0e, 0x00, 0x97, 0x2f,
	0xde, 0xc1, 0x3e, 0x95, 0x4b, 0xbc, 0x5a, 0x7f, 0x24, 0x02, 0x6b, 0x5d, 0x9d, 0x8d, 0xf2, 0x20,
	0xbd, 0x7d, 0xce, 0x65, 0x4a, 0x04, 0xc7, 0x63, 0x5f, 0x6b, 0x4c, 0xe5, 0xd2, 0xdb, 0xb7, 0xa8,
	0x4c, 0x3d, 0xa2, 0x94, 0x67, 0xb9, 0x72, 0xcf, 0x1a, 0xef, 0xbf, 0xc1, 0x95, 0x70, 0x1a, 0xbc,
	0xbf, 0xb6, 0xe7, 0x46, 0xe7, 0x01, 0x00, 0x00, 0xff, 0xff, 0x5f, 0x58, 0x69, 0x93, 0x19, 0x05,
	0x00, 0x00,
}
