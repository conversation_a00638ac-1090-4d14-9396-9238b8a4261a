// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/mijing_rcmd/grpc_mijing_rcmd.proto

package mijing_rcmd // import "golang.52tt.com/protocol/app/api/mijing_rcmd"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import mijing_rcmd_logic "golang.52tt.com/protocol/app/mijing-rcmd-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MijingRcmdLogicClient is the client API for MijingRcmdLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MijingRcmdLogicClient interface {
	// 获取主页推荐本
	MijingGetRecommendedScenarioForHomePage(ctx context.Context, in *mijing_rcmd_logic.MijingGetRecommendedScenarioForHomePageRequest, opts ...grpc.CallOption) (*mijing_rcmd_logic.MijingGetRecommendedScenarioForHomePageResponse, error)
	// 获取剧本评价
	GetMijingScenarioCommentList(ctx context.Context, in *mijing_rcmd_logic.GetMijingScenarioCommentListRequest, opts ...grpc.CallOption) (*mijing_rcmd_logic.GetMijingScenarioCommentListResponse, error)
	// 获取总评价
	GetMijingCommentList(ctx context.Context, in *mijing_rcmd_logic.GetMijingCommentListRequest, opts ...grpc.CallOption) (*mijing_rcmd_logic.GetMijingCommentListResponse, error)
}

type mijingRcmdLogicClient struct {
	cc *grpc.ClientConn
}

func NewMijingRcmdLogicClient(cc *grpc.ClientConn) MijingRcmdLogicClient {
	return &mijingRcmdLogicClient{cc}
}

func (c *mijingRcmdLogicClient) MijingGetRecommendedScenarioForHomePage(ctx context.Context, in *mijing_rcmd_logic.MijingGetRecommendedScenarioForHomePageRequest, opts ...grpc.CallOption) (*mijing_rcmd_logic.MijingGetRecommendedScenarioForHomePageResponse, error) {
	out := new(mijing_rcmd_logic.MijingGetRecommendedScenarioForHomePageResponse)
	err := c.cc.Invoke(ctx, "/ga.api.mijing_rcmd.MijingRcmdLogic/MijingGetRecommendedScenarioForHomePage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingRcmdLogicClient) GetMijingScenarioCommentList(ctx context.Context, in *mijing_rcmd_logic.GetMijingScenarioCommentListRequest, opts ...grpc.CallOption) (*mijing_rcmd_logic.GetMijingScenarioCommentListResponse, error) {
	out := new(mijing_rcmd_logic.GetMijingScenarioCommentListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.mijing_rcmd.MijingRcmdLogic/GetMijingScenarioCommentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingRcmdLogicClient) GetMijingCommentList(ctx context.Context, in *mijing_rcmd_logic.GetMijingCommentListRequest, opts ...grpc.CallOption) (*mijing_rcmd_logic.GetMijingCommentListResponse, error) {
	out := new(mijing_rcmd_logic.GetMijingCommentListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.mijing_rcmd.MijingRcmdLogic/GetMijingCommentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MijingRcmdLogicServer is the server API for MijingRcmdLogic service.
type MijingRcmdLogicServer interface {
	// 获取主页推荐本
	MijingGetRecommendedScenarioForHomePage(context.Context, *mijing_rcmd_logic.MijingGetRecommendedScenarioForHomePageRequest) (*mijing_rcmd_logic.MijingGetRecommendedScenarioForHomePageResponse, error)
	// 获取剧本评价
	GetMijingScenarioCommentList(context.Context, *mijing_rcmd_logic.GetMijingScenarioCommentListRequest) (*mijing_rcmd_logic.GetMijingScenarioCommentListResponse, error)
	// 获取总评价
	GetMijingCommentList(context.Context, *mijing_rcmd_logic.GetMijingCommentListRequest) (*mijing_rcmd_logic.GetMijingCommentListResponse, error)
}

func RegisterMijingRcmdLogicServer(s *grpc.Server, srv MijingRcmdLogicServer) {
	s.RegisterService(&_MijingRcmdLogic_serviceDesc, srv)
}

func _MijingRcmdLogic_MijingGetRecommendedScenarioForHomePage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mijing_rcmd_logic.MijingGetRecommendedScenarioForHomePageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingRcmdLogicServer).MijingGetRecommendedScenarioForHomePage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mijing_rcmd.MijingRcmdLogic/MijingGetRecommendedScenarioForHomePage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingRcmdLogicServer).MijingGetRecommendedScenarioForHomePage(ctx, req.(*mijing_rcmd_logic.MijingGetRecommendedScenarioForHomePageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingRcmdLogic_GetMijingScenarioCommentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mijing_rcmd_logic.GetMijingScenarioCommentListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingRcmdLogicServer).GetMijingScenarioCommentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mijing_rcmd.MijingRcmdLogic/GetMijingScenarioCommentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingRcmdLogicServer).GetMijingScenarioCommentList(ctx, req.(*mijing_rcmd_logic.GetMijingScenarioCommentListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingRcmdLogic_GetMijingCommentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mijing_rcmd_logic.GetMijingCommentListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingRcmdLogicServer).GetMijingCommentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mijing_rcmd.MijingRcmdLogic/GetMijingCommentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingRcmdLogicServer).GetMijingCommentList(ctx, req.(*mijing_rcmd_logic.GetMijingCommentListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MijingRcmdLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.mijing_rcmd.MijingRcmdLogic",
	HandlerType: (*MijingRcmdLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MijingGetRecommendedScenarioForHomePage",
			Handler:    _MijingRcmdLogic_MijingGetRecommendedScenarioForHomePage_Handler,
		},
		{
			MethodName: "GetMijingScenarioCommentList",
			Handler:    _MijingRcmdLogic_GetMijingScenarioCommentList_Handler,
		},
		{
			MethodName: "GetMijingCommentList",
			Handler:    _MijingRcmdLogic_GetMijingCommentList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/mijing_rcmd/grpc_mijing_rcmd.proto",
}

func init() {
	proto.RegisterFile("api/mijing_rcmd/grpc_mijing_rcmd.proto", fileDescriptor_grpc_mijing_rcmd_d8ca4ad4eaa7cd91)
}

var fileDescriptor_grpc_mijing_rcmd_d8ca4ad4eaa7cd91 = []byte{
	// 337 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x92, 0xbd, 0x4a, 0x03, 0x41,
	0x14, 0x85, 0xc9, 0x1a, 0x24, 0x6c, 0x23, 0x0e, 0x62, 0xb1, 0x2a, 0x04, 0x0b, 0xc5, 0x22, 0xb3,
	0x18, 0x11, 0xfc, 0xe9, 0x8c, 0x1a, 0x8b, 0x08, 0x61, 0x6d, 0xc4, 0x26, 0x8c, 0xb3, 0x97, 0x61,
	0x24, 0x33, 0x77, 0xb2, 0x3b, 0x41, 0xcb, 0x90, 0x46, 0xf0, 0x0d, 0xb6, 0x10, 0xc1, 0x27, 0xf1,
	0x91, 0xd4, 0x27, 0x90, 0xdd, 0x25, 0xba, 0x31, 0xc1, 0xdf, 0x6e, 0xb8, 0xe7, 0x7c, 0x67, 0x4e,
	0x71, 0xdc, 0x35, 0x66, 0xa4, 0xaf, 0xe4, 0x95, 0xd4, 0xa2, 0x13, 0x71, 0x15, 0xfa, 0x22, 0x32,
	0xbc, 0x53, 0x38, 0x50, 0x13, 0xa1, 0x45, 0x42, 0x04, 0xa3, 0xcc, 0x48, 0x5a, 0x50, 0xbc, 0x95,
	0x94, 0x85, 0x1b, 0x0b, 0x3a, 0x96, 0xa8, 0x3f, 0x5e, 0x39, 0xe2, 0x6d, 0x14, 0xbc, 0x9d, 0x2e,
	0x0a, 0xc9, 0xfd, 0x89, 0x4b, 0x6e, 0xad, 0xdf, 0x97, 0xdd, 0xb9, 0xd3, 0x4c, 0x0b, 0xb8, 0x0a,
	0x5b, 0xa9, 0x42, 0x9e, 0x4a, 0xee, 0x7a, 0x7e, 0x6b, 0x82, 0x0d, 0x80, 0xa3, 0x52, 0xa0, 0x43,
	0x08, 0xcf, 0x38, 0x68, 0x16, 0x49, 0x3c, 0xc6, 0xe8, 0x04, 0x15, 0xb4, 0x99, 0x00, 0x72, 0x48,
	0x05, 0xa3, 0x93, 0xe1, 0x3f, 0xc4, 0x03, 0xe8, 0xf5, 0x21, 0xb6, 0xde, 0xd1, 0x3f, 0x53, 0x62,
	0x83, 0x3a, 0x86, 0xd5, 0xca, 0x70, 0x50, 0x2d, 0x57, 0x9e, 0x13, 0x87, 0x3c, 0x94, 0xdc, 0xe5,
	0x26, 0xd8, 0x3c, 0x60, 0x84, 0x34, 0xb2, 0x0c, 0xdb, 0x92, 0xb1, 0x25, 0xbb, 0xd3, 0x7f, 0xfc,
	0x8a, 0x19, 0x95, 0xdd, 0xfb, 0x0b, 0x3a, 0xd6, 0xf0, 0x25, 0x71, 0xc8, 0x6d, 0xc9, 0x5d, 0x78,
	0x47, 0x8a, 0xcd, 0x36, 0xbf, 0x89, 0x9f, 0xd2, 0xa8, 0xfe, 0x1b, 0x64, 0xac, 0xc9, 0x6b, 0xe2,
	0x78, 0x4b, 0xc3, 0x41, 0x75, 0x3e, 0xa7, 0x6b, 0x29, 0x5d, 0xcb, 0xe8, 0xbb, 0x41, 0xd5, 0x11,
	0x78, 0x70, 0xee, 0x2e, 0x72, 0x54, 0xb4, 0xd7, 0xbf, 0x66, 0x9a, 0x5a, 0x9b, 0xaf, 0x26, 0xdd,
	0xe3, 0xc5, 0x8e, 0xc0, 0x2e, 0xd3, 0x82, 0x6e, 0xd7, 0xad, 0xa5, 0x1c, 0x95, 0x9f, 0x49, 0x1c,
	0xbb, 0x3e, 0x33, 0xc6, 0xff, 0x34, 0xee, 0xfd, 0xc2, 0xfb, 0xd1, 0x99, 0x09, 0xda, 0x8d, 0xcb,
	0xd9, 0xcc, 0xbf, 0xf5, 0x16, 0x00, 0x00, 0xff, 0xff, 0x49, 0x61, 0xa1, 0x95, 0x08, 0x03, 0x00,
	0x00,
}
