// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/virtual_avatar/grpc_virtual_avatar_logic.proto

package virtual_avatar // import "golang.52tt.com/protocol/app/api/virtual_avatar"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import virtual_avatar_logic "golang.52tt.com/protocol/app/virtual_avatar_logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// VirtualAvatarLogicClient is the client API for VirtualAvatarLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VirtualAvatarLogicClient interface {
	// 获取虚拟形象入口状态
	GetVirtualAvatarEntry(ctx context.Context, in *virtual_avatar_logic.GetVirtualAvatarEntryRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.GetVirtualAvatarEntryResponse, error)
	// 获取用户虚拟形象列表
	GetUserVirtualAvatarList(ctx context.Context, in *virtual_avatar_logic.GetUserVirtualAvatarListRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.GetUserVirtualAvatarListResponse, error)
	// 用户佩戴虚拟形象
	SetUserVirtualAvatarInUse(ctx context.Context, in *virtual_avatar_logic.SetUserVirtualAvatarInUseRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.SetUserVirtualAvatarInUseResponse, error)
	// 获取用户使用中的虚拟形象信息
	GetUserVirtualAvatarInUse(ctx context.Context, in *virtual_avatar_logic.GetUserVirtualAvatarInUseRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.GetUserVirtualAvatarInUseResponse, error)
	// 设置用户虚拟形象使用范围(废弃)
	SetUserVirtualAvatarUseScope(ctx context.Context, in *virtual_avatar_logic.SetUserVirtualAvatarInUseRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.SetUserVirtualAvatarInUseResponse, error)
	// 获取用户虚拟形象使用范围(废弃)
	GetUserVirtualAvatarUseScope(ctx context.Context, in *virtual_avatar_logic.GetUserVirtualAvatarInUseRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.GetUserVirtualAvatarInUseResponse, error)
	// 主态 获取麦位形象类型
	GetUserCurrMicAvatarType(ctx context.Context, in *virtual_avatar_logic.GetUserCurrMicAvatarTypeRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.GetUserCurrMicAvatarTypeResponse, error)
	// 主态 设置麦位形象类型
	SetUserCurrMicAvatarType(ctx context.Context, in *virtual_avatar_logic.SetUserCurrMicAvatarTypeRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.SetUserCurrMicAvatarTypeResponse, error)
	// 设置用户虚拟形象使用范围
	SetUserVirtualAvatarUseScopeV2(ctx context.Context, in *virtual_avatar_logic.SetVirtualAvatarUseScopeRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.SetVirtualAvatarUseScopeResponse, error)
	// 获取用户虚拟形象使用范围
	GetUserVirtualAvatarUseScopeV2(ctx context.Context, in *virtual_avatar_logic.GetVirtualAvatarUseScopeRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.GetVirtualAvatarUseScopeResponse, error)
}

type virtualAvatarLogicClient struct {
	cc *grpc.ClientConn
}

func NewVirtualAvatarLogicClient(cc *grpc.ClientConn) VirtualAvatarLogicClient {
	return &virtualAvatarLogicClient{cc}
}

func (c *virtualAvatarLogicClient) GetVirtualAvatarEntry(ctx context.Context, in *virtual_avatar_logic.GetVirtualAvatarEntryRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.GetVirtualAvatarEntryResponse, error) {
	out := new(virtual_avatar_logic.GetVirtualAvatarEntryResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_avatar.VirtualAvatarLogic/GetVirtualAvatarEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualAvatarLogicClient) GetUserVirtualAvatarList(ctx context.Context, in *virtual_avatar_logic.GetUserVirtualAvatarListRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.GetUserVirtualAvatarListResponse, error) {
	out := new(virtual_avatar_logic.GetUserVirtualAvatarListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_avatar.VirtualAvatarLogic/GetUserVirtualAvatarList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualAvatarLogicClient) SetUserVirtualAvatarInUse(ctx context.Context, in *virtual_avatar_logic.SetUserVirtualAvatarInUseRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.SetUserVirtualAvatarInUseResponse, error) {
	out := new(virtual_avatar_logic.SetUserVirtualAvatarInUseResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_avatar.VirtualAvatarLogic/SetUserVirtualAvatarInUse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualAvatarLogicClient) GetUserVirtualAvatarInUse(ctx context.Context, in *virtual_avatar_logic.GetUserVirtualAvatarInUseRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.GetUserVirtualAvatarInUseResponse, error) {
	out := new(virtual_avatar_logic.GetUserVirtualAvatarInUseResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_avatar.VirtualAvatarLogic/GetUserVirtualAvatarInUse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualAvatarLogicClient) SetUserVirtualAvatarUseScope(ctx context.Context, in *virtual_avatar_logic.SetUserVirtualAvatarInUseRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.SetUserVirtualAvatarInUseResponse, error) {
	out := new(virtual_avatar_logic.SetUserVirtualAvatarInUseResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_avatar.VirtualAvatarLogic/SetUserVirtualAvatarUseScope", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualAvatarLogicClient) GetUserVirtualAvatarUseScope(ctx context.Context, in *virtual_avatar_logic.GetUserVirtualAvatarInUseRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.GetUserVirtualAvatarInUseResponse, error) {
	out := new(virtual_avatar_logic.GetUserVirtualAvatarInUseResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_avatar.VirtualAvatarLogic/GetUserVirtualAvatarUseScope", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualAvatarLogicClient) GetUserCurrMicAvatarType(ctx context.Context, in *virtual_avatar_logic.GetUserCurrMicAvatarTypeRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.GetUserCurrMicAvatarTypeResponse, error) {
	out := new(virtual_avatar_logic.GetUserCurrMicAvatarTypeResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_avatar.VirtualAvatarLogic/GetUserCurrMicAvatarType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualAvatarLogicClient) SetUserCurrMicAvatarType(ctx context.Context, in *virtual_avatar_logic.SetUserCurrMicAvatarTypeRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.SetUserCurrMicAvatarTypeResponse, error) {
	out := new(virtual_avatar_logic.SetUserCurrMicAvatarTypeResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_avatar.VirtualAvatarLogic/SetUserCurrMicAvatarType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualAvatarLogicClient) SetUserVirtualAvatarUseScopeV2(ctx context.Context, in *virtual_avatar_logic.SetVirtualAvatarUseScopeRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.SetVirtualAvatarUseScopeResponse, error) {
	out := new(virtual_avatar_logic.SetVirtualAvatarUseScopeResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_avatar.VirtualAvatarLogic/SetUserVirtualAvatarUseScopeV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualAvatarLogicClient) GetUserVirtualAvatarUseScopeV2(ctx context.Context, in *virtual_avatar_logic.GetVirtualAvatarUseScopeRequest, opts ...grpc.CallOption) (*virtual_avatar_logic.GetVirtualAvatarUseScopeResponse, error) {
	out := new(virtual_avatar_logic.GetVirtualAvatarUseScopeResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_avatar.VirtualAvatarLogic/GetUserVirtualAvatarUseScopeV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VirtualAvatarLogicServer is the server API for VirtualAvatarLogic service.
type VirtualAvatarLogicServer interface {
	// 获取虚拟形象入口状态
	GetVirtualAvatarEntry(context.Context, *virtual_avatar_logic.GetVirtualAvatarEntryRequest) (*virtual_avatar_logic.GetVirtualAvatarEntryResponse, error)
	// 获取用户虚拟形象列表
	GetUserVirtualAvatarList(context.Context, *virtual_avatar_logic.GetUserVirtualAvatarListRequest) (*virtual_avatar_logic.GetUserVirtualAvatarListResponse, error)
	// 用户佩戴虚拟形象
	SetUserVirtualAvatarInUse(context.Context, *virtual_avatar_logic.SetUserVirtualAvatarInUseRequest) (*virtual_avatar_logic.SetUserVirtualAvatarInUseResponse, error)
	// 获取用户使用中的虚拟形象信息
	GetUserVirtualAvatarInUse(context.Context, *virtual_avatar_logic.GetUserVirtualAvatarInUseRequest) (*virtual_avatar_logic.GetUserVirtualAvatarInUseResponse, error)
	// 设置用户虚拟形象使用范围(废弃)
	SetUserVirtualAvatarUseScope(context.Context, *virtual_avatar_logic.SetUserVirtualAvatarInUseRequest) (*virtual_avatar_logic.SetUserVirtualAvatarInUseResponse, error)
	// 获取用户虚拟形象使用范围(废弃)
	GetUserVirtualAvatarUseScope(context.Context, *virtual_avatar_logic.GetUserVirtualAvatarInUseRequest) (*virtual_avatar_logic.GetUserVirtualAvatarInUseResponse, error)
	// 主态 获取麦位形象类型
	GetUserCurrMicAvatarType(context.Context, *virtual_avatar_logic.GetUserCurrMicAvatarTypeRequest) (*virtual_avatar_logic.GetUserCurrMicAvatarTypeResponse, error)
	// 主态 设置麦位形象类型
	SetUserCurrMicAvatarType(context.Context, *virtual_avatar_logic.SetUserCurrMicAvatarTypeRequest) (*virtual_avatar_logic.SetUserCurrMicAvatarTypeResponse, error)
	// 设置用户虚拟形象使用范围
	SetUserVirtualAvatarUseScopeV2(context.Context, *virtual_avatar_logic.SetVirtualAvatarUseScopeRequest) (*virtual_avatar_logic.SetVirtualAvatarUseScopeResponse, error)
	// 获取用户虚拟形象使用范围
	GetUserVirtualAvatarUseScopeV2(context.Context, *virtual_avatar_logic.GetVirtualAvatarUseScopeRequest) (*virtual_avatar_logic.GetVirtualAvatarUseScopeResponse, error)
}

func RegisterVirtualAvatarLogicServer(s *grpc.Server, srv VirtualAvatarLogicServer) {
	s.RegisterService(&_VirtualAvatarLogic_serviceDesc, srv)
}

func _VirtualAvatarLogic_GetVirtualAvatarEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_avatar_logic.GetVirtualAvatarEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualAvatarLogicServer).GetVirtualAvatarEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_avatar.VirtualAvatarLogic/GetVirtualAvatarEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualAvatarLogicServer).GetVirtualAvatarEntry(ctx, req.(*virtual_avatar_logic.GetVirtualAvatarEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualAvatarLogic_GetUserVirtualAvatarList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_avatar_logic.GetUserVirtualAvatarListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualAvatarLogicServer).GetUserVirtualAvatarList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_avatar.VirtualAvatarLogic/GetUserVirtualAvatarList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualAvatarLogicServer).GetUserVirtualAvatarList(ctx, req.(*virtual_avatar_logic.GetUserVirtualAvatarListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualAvatarLogic_SetUserVirtualAvatarInUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_avatar_logic.SetUserVirtualAvatarInUseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualAvatarLogicServer).SetUserVirtualAvatarInUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_avatar.VirtualAvatarLogic/SetUserVirtualAvatarInUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualAvatarLogicServer).SetUserVirtualAvatarInUse(ctx, req.(*virtual_avatar_logic.SetUserVirtualAvatarInUseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualAvatarLogic_GetUserVirtualAvatarInUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_avatar_logic.GetUserVirtualAvatarInUseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualAvatarLogicServer).GetUserVirtualAvatarInUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_avatar.VirtualAvatarLogic/GetUserVirtualAvatarInUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualAvatarLogicServer).GetUserVirtualAvatarInUse(ctx, req.(*virtual_avatar_logic.GetUserVirtualAvatarInUseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualAvatarLogic_SetUserVirtualAvatarUseScope_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_avatar_logic.SetUserVirtualAvatarInUseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualAvatarLogicServer).SetUserVirtualAvatarUseScope(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_avatar.VirtualAvatarLogic/SetUserVirtualAvatarUseScope",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualAvatarLogicServer).SetUserVirtualAvatarUseScope(ctx, req.(*virtual_avatar_logic.SetUserVirtualAvatarInUseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualAvatarLogic_GetUserVirtualAvatarUseScope_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_avatar_logic.GetUserVirtualAvatarInUseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualAvatarLogicServer).GetUserVirtualAvatarUseScope(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_avatar.VirtualAvatarLogic/GetUserVirtualAvatarUseScope",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualAvatarLogicServer).GetUserVirtualAvatarUseScope(ctx, req.(*virtual_avatar_logic.GetUserVirtualAvatarInUseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualAvatarLogic_GetUserCurrMicAvatarType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_avatar_logic.GetUserCurrMicAvatarTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualAvatarLogicServer).GetUserCurrMicAvatarType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_avatar.VirtualAvatarLogic/GetUserCurrMicAvatarType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualAvatarLogicServer).GetUserCurrMicAvatarType(ctx, req.(*virtual_avatar_logic.GetUserCurrMicAvatarTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualAvatarLogic_SetUserCurrMicAvatarType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_avatar_logic.SetUserCurrMicAvatarTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualAvatarLogicServer).SetUserCurrMicAvatarType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_avatar.VirtualAvatarLogic/SetUserCurrMicAvatarType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualAvatarLogicServer).SetUserCurrMicAvatarType(ctx, req.(*virtual_avatar_logic.SetUserCurrMicAvatarTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualAvatarLogic_SetUserVirtualAvatarUseScopeV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_avatar_logic.SetVirtualAvatarUseScopeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualAvatarLogicServer).SetUserVirtualAvatarUseScopeV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_avatar.VirtualAvatarLogic/SetUserVirtualAvatarUseScopeV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualAvatarLogicServer).SetUserVirtualAvatarUseScopeV2(ctx, req.(*virtual_avatar_logic.SetVirtualAvatarUseScopeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualAvatarLogic_GetUserVirtualAvatarUseScopeV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_avatar_logic.GetVirtualAvatarUseScopeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualAvatarLogicServer).GetUserVirtualAvatarUseScopeV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_avatar.VirtualAvatarLogic/GetUserVirtualAvatarUseScopeV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualAvatarLogicServer).GetUserVirtualAvatarUseScopeV2(ctx, req.(*virtual_avatar_logic.GetVirtualAvatarUseScopeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _VirtualAvatarLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.virtual_avatar.VirtualAvatarLogic",
	HandlerType: (*VirtualAvatarLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetVirtualAvatarEntry",
			Handler:    _VirtualAvatarLogic_GetVirtualAvatarEntry_Handler,
		},
		{
			MethodName: "GetUserVirtualAvatarList",
			Handler:    _VirtualAvatarLogic_GetUserVirtualAvatarList_Handler,
		},
		{
			MethodName: "SetUserVirtualAvatarInUse",
			Handler:    _VirtualAvatarLogic_SetUserVirtualAvatarInUse_Handler,
		},
		{
			MethodName: "GetUserVirtualAvatarInUse",
			Handler:    _VirtualAvatarLogic_GetUserVirtualAvatarInUse_Handler,
		},
		{
			MethodName: "SetUserVirtualAvatarUseScope",
			Handler:    _VirtualAvatarLogic_SetUserVirtualAvatarUseScope_Handler,
		},
		{
			MethodName: "GetUserVirtualAvatarUseScope",
			Handler:    _VirtualAvatarLogic_GetUserVirtualAvatarUseScope_Handler,
		},
		{
			MethodName: "GetUserCurrMicAvatarType",
			Handler:    _VirtualAvatarLogic_GetUserCurrMicAvatarType_Handler,
		},
		{
			MethodName: "SetUserCurrMicAvatarType",
			Handler:    _VirtualAvatarLogic_SetUserCurrMicAvatarType_Handler,
		},
		{
			MethodName: "SetUserVirtualAvatarUseScopeV2",
			Handler:    _VirtualAvatarLogic_SetUserVirtualAvatarUseScopeV2_Handler,
		},
		{
			MethodName: "GetUserVirtualAvatarUseScopeV2",
			Handler:    _VirtualAvatarLogic_GetUserVirtualAvatarUseScopeV2_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/virtual_avatar/grpc_virtual_avatar_logic.proto",
}

func init() {
	proto.RegisterFile("api/virtual_avatar/grpc_virtual_avatar_logic.proto", fileDescriptor_grpc_virtual_avatar_logic_34b50adf7ec2081b)
}

var fileDescriptor_grpc_virtual_avatar_logic_34b50adf7ec2081b = []byte{
	// 453 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x95, 0x4d, 0x8b, 0xd4, 0x30,
	0x18, 0xc7, 0x69, 0x94, 0x65, 0x08, 0x9e, 0x82, 0x2b, 0x6b, 0xd1, 0xa5, 0x78, 0xdf, 0x14, 0x2a,
	0x2b, 0xbe, 0x80, 0xa0, 0x8b, 0x04, 0x41, 0x41, 0xb6, 0xce, 0x1e, 0x3c, 0x38, 0xc4, 0x12, 0x4a,
	0xa1, 0xdb, 0x64, 0x93, 0x74, 0x74, 0x6e, 0x65, 0x8e, 0x9e, 0xbc, 0x0a, 0x52, 0xd1, 0x2f, 0xe2,
	0xfb, 0xcb, 0x17, 0xf1, 0x7b, 0xc8, 0xb4, 0x1d, 0x34, 0x4c, 0x3a, 0x76, 0x02, 0xb2, 0xb7, 0xb6,
	0x4f, 0x7e, 0xff, 0xfc, 0x9a, 0xf6, 0xe1, 0x81, 0x11, 0x15, 0x59, 0x38, 0xcd, 0xa4, 0x2e, 0x69,
	0x3e, 0xa1, 0x53, 0xaa, 0xa9, 0x0c, 0x53, 0x29, 0x92, 0x89, 0xf9, 0x6c, 0x92, 0xf3, 0x34, 0x4b,
	0xb0, 0x90, 0x5c, 0x73, 0xb4, 0x9d, 0x52, 0x4c, 0x45, 0x86, 0xcd, 0x25, 0xfe, 0xe5, 0x45, 0x14,
	0x7b, 0xa1, 0x59, 0xa1, 0x32, 0x5e, 0xfc, 0xb9, 0x6a, 0x29, 0x3f, 0xb4, 0x25, 0x86, 0xfd, 0xdb,
	0x44, 0xbf, 0xce, 0x41, 0x74, 0xd4, 0x96, 0xef, 0x34, 0xd5, 0x07, 0x8b, 0x22, 0x7a, 0xe5, 0xc1,
	0x6d, 0xc2, 0xb4, 0x51, 0xb9, 0x57, 0x68, 0x39, 0x43, 0xfb, 0x38, 0xa5, 0xd8, 0x1a, 0x68, 0x5d,
	0x7f, 0xc8, 0x4e, 0x4a, 0xa6, 0xb4, 0x7f, 0x6d, 0x53, 0x4c, 0x09, 0x5e, 0x28, 0x76, 0x65, 0x34,
	0xaf, 0x82, 0xb3, 0xa3, 0x0f, 0x35, 0x40, 0xaf, 0x3d, 0xb8, 0x43, 0x98, 0x1e, 0x2b, 0x26, 0x4d,
	0xe1, 0x4c, 0x69, 0x74, 0x7d, 0x5d, 0xbc, 0x15, 0x59, 0x8a, 0xdd, 0x70, 0x20, 0x0d, 0xb7, 0x8f,
	0x35, 0x40, 0x6f, 0x3c, 0x78, 0x31, 0xb6, 0x2c, 0xbf, 0x5f, 0x8c, 0x15, 0x43, 0xfd, 0x5b, 0xf4,
	0x32, 0x4b, 0xbb, 0x9b, 0x2e, 0xa8, 0xa1, 0xf7, 0xa9, 0xd3, 0x23, 0x0e, 0x7a, 0xc4, 0x5d, 0x8f,
	0x0c, 0xd4, 0xfb, 0x5c, 0x03, 0xf4, 0xce, 0x83, 0x97, 0x6c, 0xaf, 0x33, 0x56, 0x2c, 0x4e, 0xb8,
	0x38, 0xb5, 0x03, 0x84, 0xf3, 0x2a, 0xd8, 0x1a, 0x7d, 0xa9, 0xc1, 0x8e, 0xd7, 0x38, 0x12, 0x37,
	0xc7, 0xff, 0x7a, 0x8a, 0xad, 0xe3, 0xd7, 0xc6, 0xf1, 0xaf, 0x0e, 0x39, 0x28, 0xa5, 0x7c, 0x98,
	0x25, 0x2d, 0xf1, 0x78, 0x26, 0xd8, 0xbf, 0x3b, 0x64, 0x05, 0x19, 0xdc, 0x21, 0x16, 0xd2, 0xf8,
	0xc6, 0xdf, 0xba, 0xee, 0x8d, 0x37, 0x77, 0x8b, 0x9d, 0xdd, 0xe2, 0x61, 0x6e, 0xdf, 0x6b, 0x80,
	0xde, 0x7a, 0x70, 0x77, 0xdd, 0xff, 0x77, 0x14, 0xad, 0x37, 0xb4, 0x42, 0x83, 0x0c, 0x7b, 0x48,
	0xc3, 0xf0, 0x47, 0x67, 0x48, 0x5c, 0x0d, 0x89, 0xb3, 0x21, 0x19, 0x66, 0xf8, 0xb3, 0x06, 0xfe,
	0xee, 0xbc, 0x0a, 0xce, 0x77, 0x21, 0x7b, 0x6d, 0xc8, 0x5e, 0x13, 0xf2, 0xb2, 0x0a, 0x40, 0xca,
	0xef, 0x3e, 0x85, 0x17, 0x12, 0x7e, 0x8c, 0x4f, 0xca, 0xe7, 0xb4, 0xc0, 0x5a, 0xb7, 0xd3, 0x67,
	0x31, 0xe0, 0x9e, 0xdc, 0x4e, 0x79, 0x4e, 0x8b, 0x14, 0xef, 0x47, 0x5a, 0xe3, 0x84, 0x1f, 0x87,
	0x4d, 0x29, 0xe1, 0x79, 0x48, 0x85, 0x08, 0x57, 0x27, 0xe7, 0x2d, 0xf3, 0xf6, 0x3d, 0x38, 0x73,
	0xf8, 0xe8, 0xe0, 0xd9, 0x56, 0x43, 0x5d, 0xfd, 0x1d, 0x00, 0x00, 0xff, 0xff, 0x17, 0x44, 0x9b,
	0xcc, 0x6b, 0x07, 0x00, 0x00,
}
