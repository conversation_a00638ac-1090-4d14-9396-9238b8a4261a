// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/grpc_transport_cfg/grpc_grpc_transport_cfg.proto

package grpc_transport_cfg // import "golang.52tt.com/protocol/app/api/grpc-transport-cfg"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import grpc_transport_cfg "golang.52tt.com/protocol/app/grpc-transport-cfg"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GrpcTransportCfgLogicClient is the client API for GrpcTransportCfgLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GrpcTransportCfgLogicClient interface {
	RefreshTransportConfig(ctx context.Context, in *grpc_transport_cfg.RefreshTransportConfigRequest, opts ...grpc.CallOption) (*grpc_transport_cfg.RefreshTransportConfigResponse, error)
}

type grpcTransportCfgLogicClient struct {
	cc *grpc.ClientConn
}

func NewGrpcTransportCfgLogicClient(cc *grpc.ClientConn) GrpcTransportCfgLogicClient {
	return &grpcTransportCfgLogicClient{cc}
}

func (c *grpcTransportCfgLogicClient) RefreshTransportConfig(ctx context.Context, in *grpc_transport_cfg.RefreshTransportConfigRequest, opts ...grpc.CallOption) (*grpc_transport_cfg.RefreshTransportConfigResponse, error) {
	out := new(grpc_transport_cfg.RefreshTransportConfigResponse)
	err := c.cc.Invoke(ctx, "/ga.api.grpc_transport_cfg.GrpcTransportCfgLogic/RefreshTransportConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GrpcTransportCfgLogicServer is the server API for GrpcTransportCfgLogic service.
type GrpcTransportCfgLogicServer interface {
	RefreshTransportConfig(context.Context, *grpc_transport_cfg.RefreshTransportConfigRequest) (*grpc_transport_cfg.RefreshTransportConfigResponse, error)
}

func RegisterGrpcTransportCfgLogicServer(s *grpc.Server, srv GrpcTransportCfgLogicServer) {
	s.RegisterService(&_GrpcTransportCfgLogic_serviceDesc, srv)
}

func _GrpcTransportCfgLogic_RefreshTransportConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(grpc_transport_cfg.RefreshTransportConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GrpcTransportCfgLogicServer).RefreshTransportConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.grpc_transport_cfg.GrpcTransportCfgLogic/RefreshTransportConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GrpcTransportCfgLogicServer).RefreshTransportConfig(ctx, req.(*grpc_transport_cfg.RefreshTransportConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _GrpcTransportCfgLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.grpc_transport_cfg.GrpcTransportCfgLogic",
	HandlerType: (*GrpcTransportCfgLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RefreshTransportConfig",
			Handler:    _GrpcTransportCfgLogic_RefreshTransportConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/grpc_transport_cfg/grpc_grpc_transport_cfg.proto",
}

func init() {
	proto.RegisterFile("api/grpc_transport_cfg/grpc_grpc_transport_cfg.proto", fileDescriptor_grpc_grpc_transport_cfg_be7f71a308d6bd72)
}

var fileDescriptor_grpc_grpc_transport_cfg_be7f71a308d6bd72 = []byte{
	// 263 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x32, 0x49, 0x2c, 0xc8, 0xd4,
	0x4f, 0x2f, 0x2a, 0x48, 0x8e, 0x2f, 0x29, 0x4a, 0xcc, 0x2b, 0x2e, 0xc8, 0x2f, 0x2a, 0x89, 0x4f,
	0x4e, 0x4b, 0x87, 0x08, 0x61, 0x8a, 0xeb, 0x15, 0x14, 0xe5, 0x97, 0xe4, 0x0b, 0x49, 0xa6, 0x27,
	0xea, 0x25, 0x16, 0x64, 0xea, 0x61, 0x2a, 0x90, 0x92, 0x05, 0x19, 0x98, 0x5a, 0x51, 0x92, 0x9a,
	0x57, 0x9c, 0x99, 0x9f, 0x87, 0x60, 0x41, 0x74, 0x4a, 0x69, 0xe3, 0xb2, 0x0b, 0x8b, 0x35, 0x46,
	0x67, 0x19, 0xb9, 0x44, 0xdd, 0x8b, 0x0a, 0x92, 0x43, 0x60, 0x72, 0xce, 0x69, 0xe9, 0x3e, 0xf9,
	0xe9, 0x99, 0xc9, 0x42, 0xfd, 0x8c, 0x5c, 0x62, 0x41, 0xa9, 0x69, 0x45, 0xa9, 0xc5, 0x19, 0x08,
	0xc9, 0xfc, 0xbc, 0xb4, 0xcc, 0x74, 0x21, 0x13, 0xbd, 0xf4, 0x44, 0x2c, 0x0e, 0xd3, 0xc3, 0xae,
	0x3c, 0x28, 0xb5, 0xb0, 0x34, 0xb5, 0xb8, 0x44, 0xca, 0x94, 0x44, 0x5d, 0xc5, 0x05, 0xf9, 0x79,
	0xc5, 0xa9, 0x4a, 0x1c, 0x4d, 0x0d, 0x0a, 0x2c, 0x1c, 0x9f, 0x1a, 0x99, 0xa4, 0x94, 0x9a, 0x1a,
	0x14, 0x24, 0x40, 0x06, 0xe8, 0xc2, 0x0d, 0xd0, 0x4d, 0x4e, 0x4b, 0xd7, 0xcd, 0x01, 0xb9, 0xb7,
	0xab, 0x41, 0x81, 0x29, 0x3d, 0xdf, 0x29, 0x8d, 0x4b, 0x2c, 0x39, 0x3f, 0x57, 0xaf, 0xb0, 0xb4,
	0x3c, 0x31, 0x4f, 0xaf, 0xa4, 0x04, 0xe2, 0x4b, 0x50, 0x40, 0x46, 0xb9, 0xa5, 0xe7, 0xe7, 0x24,
	0xe6, 0xa5, 0xeb, 0x99, 0x1a, 0x95, 0x94, 0xe8, 0x25, 0xe7, 0xe7, 0xea, 0x83, 0xa5, 0x92, 0xf3,
	0x73, 0xf4, 0x13, 0x0b, 0x0a, 0xf4, 0x61, 0x71, 0x84, 0x6a, 0xb4, 0x35, 0xa6, 0xd0, 0x22, 0x26,
	0xe6, 0xa0, 0x00, 0xe7, 0x24, 0x36, 0xb0, 0x6e, 0x63, 0x40, 0x00, 0x00, 0x00, 0xff, 0xff, 0x41,
	0x9f, 0xb5, 0xf4, 0xdd, 0x01, 0x00, 0x00,
}
