// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/account/grpc_account.proto

package account // import "golang.52tt.com/protocol/app/api/account"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import auth "golang.52tt.com/protocol/app/auth"
import contact "golang.52tt.com/protocol/app/contact"
import face "golang.52tt.com/protocol/app/face"
import game "golang.52tt.com/protocol/app/game"
import myinfo "golang.52tt.com/protocol/app/myinfo"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AccountLogicClient is the client API for AccountLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AccountLogicClient interface {
	AgreeUserContract(ctx context.Context, in *myinfo.AgreeUserContractReq, opts ...grpc.CallOption) (*myinfo.AgreeUserContractResp, error)
	BatGetUserGrowInfo(ctx context.Context, in *myinfo.BatGetUserGrowInfoReq, opts ...grpc.CallOption) (*myinfo.BatGetUserGrowInfoResp, error)
	BatchGetSmallFaceUrl(ctx context.Context, in *face.BatchGetSmallFaceUrlReq, opts ...grpc.CallOption) (*face.BatchGetSmallFaceUrlResp, error)
	BindPhone(ctx context.Context, in *auth.BindPhoneReq, opts ...grpc.CallOption) (*auth.BindPhoneResp, error)
	CheckPhoneExist(ctx context.Context, in *auth.CheckPhoneExistReq, opts ...grpc.CallOption) (*auth.CheckPhoneExistResp, error)
	CheckUpgrade(ctx context.Context, in *auth.CheckUpgradeReq, opts ...grpc.CallOption) (*auth.CheckUpgradeResp, error)
	CheckVerifyCode(ctx context.Context, in *auth.CheckVerifyCodeReq, opts ...grpc.CallOption) (*auth.CheckVerifyCodeResp, error)
	ChinaMobileUidBind(ctx context.Context, in *auth.ChinaMobileUidBindReq, opts ...grpc.CallOption) (*auth.ChinaMobileUidBindResp, error)
	GeneralCheckVerifyCode(ctx context.Context, in *auth.GeneralCheckVerifyCodeReq, opts ...grpc.CallOption) (*auth.GeneralCheckVerifyCodeResp, error)
	GeneralSendVerifyCode(ctx context.Context, in *auth.GeneralSendVerifyCodeReq, opts ...grpc.CallOption) (*auth.GeneralSendVerifyCodeResp, error)
	GetBigFace(ctx context.Context, in *face.GetBigFaceReq, opts ...grpc.CallOption) (*face.GetBigFaceResp, error)
	GetCAPTCHA(ctx context.Context, in *auth.GetCAPTCHAReq, opts ...grpc.CallOption) (*auth.GetCAPTCHAResp, error)
	GetCover(ctx context.Context, in *contact.GetCoverReq, opts ...grpc.CallOption) (*contact.GetCoverResp, error)
	GetIosNeedUploadConfGameList(ctx context.Context, in *game.GetIosNeedUploadConfGameListReq, opts ...grpc.CallOption) (*game.GetIosNeedUploadConfGameListResp, error)
	GetPhotoAlbum(ctx context.Context, in *myinfo.GetPhotoAlbumReq, opts ...grpc.CallOption) (*myinfo.GetPhotoAlbumResp, error)
	GetSmallFace(ctx context.Context, in *face.GetSmallFaceReq, opts ...grpc.CallOption) (*face.GetSmallFaceResp, error)
	GetUnregApplyAuditStatus(ctx context.Context, in *auth.GetUnregApplyAuditStatusReq, opts ...grpc.CallOption) (*auth.GetUnregApplyAuditStatusResp, error)
	GetUserCertificationList(ctx context.Context, in *myinfo.GetUserCertifyListReq, opts ...grpc.CallOption) (*myinfo.GetUserCertifyListResp, error)
	GetUserCertification(ctx context.Context, in *myinfo.GetUserCertificationReq, opts ...grpc.CallOption) (*myinfo.GetUserCertificationResp, error)
	GetUserContractInfo(ctx context.Context, in *myinfo.GetUserContractInfoReq, opts ...grpc.CallOption) (*myinfo.GetUserContractInfoResp, error)
	GetUserDetail(ctx context.Context, in *contact.GetUserDetailReq, opts ...grpc.CallOption) (*contact.GetUserDetailResp, error)
	GetUserHeadwear(ctx context.Context, in *myinfo.GetUserHeadwearReq, opts ...grpc.CallOption) (*myinfo.GetUserHeadwearResp, error)
	GetUserOnlineTerminalList(ctx context.Context, in *contact.GetUserOnlineTerminalListReq, opts ...grpc.CallOption) (*contact.GetUserOnlineTerminalListResp, error)
	GetUserStatus(ctx context.Context, in *contact.GetUserStatusReq, opts ...grpc.CallOption) (*contact.GetUserStatusResp, error)
	ModifyNickname(ctx context.Context, in *myinfo.ModifyNicknameReq, opts ...grpc.CallOption) (*myinfo.ModifyNicknameResp, error)
	ModifyPWD(ctx context.Context, in *auth.ModifyPWDReq, opts ...grpc.CallOption) (*auth.ModifyPWDResp, error)
	ModifySex(ctx context.Context, in *auth.ModifySexReq, opts ...grpc.CallOption) (*auth.ModifySexResp, error)
	ModifySignature(ctx context.Context, in *myinfo.ModifySignatureReq, opts ...grpc.CallOption) (*myinfo.ModifySignatureResp, error)
	ModifyUserName(ctx context.Context, in *myinfo.ModifyUserNameReq, opts ...grpc.CallOption) (*myinfo.ModifyUserNameResp, error)
	ModifyVerify(ctx context.Context, in *auth.ModifyVerifyReq, opts ...grpc.CallOption) (*auth.ModifyVerifyResp, error)
	RebindPhone(ctx context.Context, in *auth.RebindPhoneReq, opts ...grpc.CallOption) (*auth.RebindPhoneResp, error)
	RegResetAccount(ctx context.Context, in *auth.RegResetAccountReq, opts ...grpc.CallOption) (*auth.RegResetAccountResp, error)
	RemoveUserHeadwearInUse(ctx context.Context, in *myinfo.RemoveUserHeadwearReq, opts ...grpc.CallOption) (*myinfo.RemoveUserHeadwearResp, error)
	ReportCrash(ctx context.Context, in *auth.ReportCrashReq, opts ...grpc.CallOption) (*auth.ReportCrashResp, error)
	ResetPWD(ctx context.Context, in *auth.ResetPWDReq, opts ...grpc.CallOption) (*auth.ResetPWDResp, error)
	SetLocalAccountClearSwitch(ctx context.Context, in *auth.SetLocalAccountClearSwitchReq, opts ...grpc.CallOption) (*auth.SetLocalAccountClearSwitchResp, error)
	SetMedalTaillight(ctx context.Context, in *myinfo.SetMyMedalTaillightReq, opts ...grpc.CallOption) (*myinfo.SetMyMedalTaillightResp, error)
	SetUserHeadwearInUse(ctx context.Context, in *myinfo.SetUserHeadwearReq, opts ...grpc.CallOption) (*myinfo.SetUserHeadwearResp, error)
	SetUserWearCertification(ctx context.Context, in *myinfo.SetUserWearCertificationReq, opts ...grpc.CallOption) (*myinfo.SetUserWearCertificationResp, error)
	UpdatePhotoAlbum(ctx context.Context, in *myinfo.UpdatePhotoAlbumReq, opts ...grpc.CallOption) (*myinfo.UpdatePhotoAlbumResp, error)
	UploadIosUserGames(ctx context.Context, in *game.UploadIosUserGamesReq, opts ...grpc.CallOption) (*game.UploadIosUserGamesResp, error)
	UploadUserGames(ctx context.Context, in *game.UploadUserGamesReq, opts ...grpc.CallOption) (*game.UploadUserGamesResp, error)
	UserKickTerminal(ctx context.Context, in *contact.UserKickTerminalReq, opts ...grpc.CallOption) (*contact.UserKickTerminalResp, error)
	CheckRelatedLoginAccount(ctx context.Context, in *contact.CheckRelatedLoginAccountReq, opts ...grpc.CallOption) (*contact.CheckRelatedLoginAccountResp, error)
	GetRelatedLoginAccount(ctx context.Context, in *contact.GetRelatedLoginAccountReq, opts ...grpc.CallOption) (*contact.GetRelatedLoginAccountResp, error)
	UserRegComplete(ctx context.Context, in *auth.UserRegCompleteReq, opts ...grpc.CallOption) (*auth.UserRegCompleteResp, error)
	UserUploadFace(ctx context.Context, in *face.UserUploadFaceReq, opts ...grpc.CallOption) (*face.UserUploadFaceResp, error)
	VerifyCAPTCHA(ctx context.Context, in *auth.VerifyCAPTCHAReq, opts ...grpc.CallOption) (*auth.VerifyCAPTCHAResp, error)
}

type accountLogicClient struct {
	cc *grpc.ClientConn
}

func NewAccountLogicClient(cc *grpc.ClientConn) AccountLogicClient {
	return &accountLogicClient{cc}
}

func (c *accountLogicClient) AgreeUserContract(ctx context.Context, in *myinfo.AgreeUserContractReq, opts ...grpc.CallOption) (*myinfo.AgreeUserContractResp, error) {
	out := new(myinfo.AgreeUserContractResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/AgreeUserContract", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) BatGetUserGrowInfo(ctx context.Context, in *myinfo.BatGetUserGrowInfoReq, opts ...grpc.CallOption) (*myinfo.BatGetUserGrowInfoResp, error) {
	out := new(myinfo.BatGetUserGrowInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/BatGetUserGrowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) BatchGetSmallFaceUrl(ctx context.Context, in *face.BatchGetSmallFaceUrlReq, opts ...grpc.CallOption) (*face.BatchGetSmallFaceUrlResp, error) {
	out := new(face.BatchGetSmallFaceUrlResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/BatchGetSmallFaceUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) BindPhone(ctx context.Context, in *auth.BindPhoneReq, opts ...grpc.CallOption) (*auth.BindPhoneResp, error) {
	out := new(auth.BindPhoneResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/BindPhone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) CheckPhoneExist(ctx context.Context, in *auth.CheckPhoneExistReq, opts ...grpc.CallOption) (*auth.CheckPhoneExistResp, error) {
	out := new(auth.CheckPhoneExistResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/CheckPhoneExist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) CheckUpgrade(ctx context.Context, in *auth.CheckUpgradeReq, opts ...grpc.CallOption) (*auth.CheckUpgradeResp, error) {
	out := new(auth.CheckUpgradeResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/CheckUpgrade", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) CheckVerifyCode(ctx context.Context, in *auth.CheckVerifyCodeReq, opts ...grpc.CallOption) (*auth.CheckVerifyCodeResp, error) {
	out := new(auth.CheckVerifyCodeResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/CheckVerifyCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) ChinaMobileUidBind(ctx context.Context, in *auth.ChinaMobileUidBindReq, opts ...grpc.CallOption) (*auth.ChinaMobileUidBindResp, error) {
	out := new(auth.ChinaMobileUidBindResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/ChinaMobileUidBind", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GeneralCheckVerifyCode(ctx context.Context, in *auth.GeneralCheckVerifyCodeReq, opts ...grpc.CallOption) (*auth.GeneralCheckVerifyCodeResp, error) {
	out := new(auth.GeneralCheckVerifyCodeResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GeneralCheckVerifyCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GeneralSendVerifyCode(ctx context.Context, in *auth.GeneralSendVerifyCodeReq, opts ...grpc.CallOption) (*auth.GeneralSendVerifyCodeResp, error) {
	out := new(auth.GeneralSendVerifyCodeResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GeneralSendVerifyCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetBigFace(ctx context.Context, in *face.GetBigFaceReq, opts ...grpc.CallOption) (*face.GetBigFaceResp, error) {
	out := new(face.GetBigFaceResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetBigFace", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetCAPTCHA(ctx context.Context, in *auth.GetCAPTCHAReq, opts ...grpc.CallOption) (*auth.GetCAPTCHAResp, error) {
	out := new(auth.GetCAPTCHAResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetCAPTCHA", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetCover(ctx context.Context, in *contact.GetCoverReq, opts ...grpc.CallOption) (*contact.GetCoverResp, error) {
	out := new(contact.GetCoverResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetCover", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetIosNeedUploadConfGameList(ctx context.Context, in *game.GetIosNeedUploadConfGameListReq, opts ...grpc.CallOption) (*game.GetIosNeedUploadConfGameListResp, error) {
	out := new(game.GetIosNeedUploadConfGameListResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetIosNeedUploadConfGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetPhotoAlbum(ctx context.Context, in *myinfo.GetPhotoAlbumReq, opts ...grpc.CallOption) (*myinfo.GetPhotoAlbumResp, error) {
	out := new(myinfo.GetPhotoAlbumResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetPhotoAlbum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetSmallFace(ctx context.Context, in *face.GetSmallFaceReq, opts ...grpc.CallOption) (*face.GetSmallFaceResp, error) {
	out := new(face.GetSmallFaceResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetSmallFace", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetUnregApplyAuditStatus(ctx context.Context, in *auth.GetUnregApplyAuditStatusReq, opts ...grpc.CallOption) (*auth.GetUnregApplyAuditStatusResp, error) {
	out := new(auth.GetUnregApplyAuditStatusResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetUnregApplyAuditStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetUserCertificationList(ctx context.Context, in *myinfo.GetUserCertifyListReq, opts ...grpc.CallOption) (*myinfo.GetUserCertifyListResp, error) {
	out := new(myinfo.GetUserCertifyListResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetUserCertificationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetUserCertification(ctx context.Context, in *myinfo.GetUserCertificationReq, opts ...grpc.CallOption) (*myinfo.GetUserCertificationResp, error) {
	out := new(myinfo.GetUserCertificationResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetUserCertification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetUserContractInfo(ctx context.Context, in *myinfo.GetUserContractInfoReq, opts ...grpc.CallOption) (*myinfo.GetUserContractInfoResp, error) {
	out := new(myinfo.GetUserContractInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetUserContractInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetUserDetail(ctx context.Context, in *contact.GetUserDetailReq, opts ...grpc.CallOption) (*contact.GetUserDetailResp, error) {
	out := new(contact.GetUserDetailResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetUserDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetUserHeadwear(ctx context.Context, in *myinfo.GetUserHeadwearReq, opts ...grpc.CallOption) (*myinfo.GetUserHeadwearResp, error) {
	out := new(myinfo.GetUserHeadwearResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetUserHeadwear", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetUserOnlineTerminalList(ctx context.Context, in *contact.GetUserOnlineTerminalListReq, opts ...grpc.CallOption) (*contact.GetUserOnlineTerminalListResp, error) {
	out := new(contact.GetUserOnlineTerminalListResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetUserOnlineTerminalList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetUserStatus(ctx context.Context, in *contact.GetUserStatusReq, opts ...grpc.CallOption) (*contact.GetUserStatusResp, error) {
	out := new(contact.GetUserStatusResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetUserStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) ModifyNickname(ctx context.Context, in *myinfo.ModifyNicknameReq, opts ...grpc.CallOption) (*myinfo.ModifyNicknameResp, error) {
	out := new(myinfo.ModifyNicknameResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/ModifyNickname", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) ModifyPWD(ctx context.Context, in *auth.ModifyPWDReq, opts ...grpc.CallOption) (*auth.ModifyPWDResp, error) {
	out := new(auth.ModifyPWDResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/ModifyPWD", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) ModifySex(ctx context.Context, in *auth.ModifySexReq, opts ...grpc.CallOption) (*auth.ModifySexResp, error) {
	out := new(auth.ModifySexResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/ModifySex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) ModifySignature(ctx context.Context, in *myinfo.ModifySignatureReq, opts ...grpc.CallOption) (*myinfo.ModifySignatureResp, error) {
	out := new(myinfo.ModifySignatureResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/ModifySignature", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) ModifyUserName(ctx context.Context, in *myinfo.ModifyUserNameReq, opts ...grpc.CallOption) (*myinfo.ModifyUserNameResp, error) {
	out := new(myinfo.ModifyUserNameResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/ModifyUserName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) ModifyVerify(ctx context.Context, in *auth.ModifyVerifyReq, opts ...grpc.CallOption) (*auth.ModifyVerifyResp, error) {
	out := new(auth.ModifyVerifyResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/ModifyVerify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) RebindPhone(ctx context.Context, in *auth.RebindPhoneReq, opts ...grpc.CallOption) (*auth.RebindPhoneResp, error) {
	out := new(auth.RebindPhoneResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/RebindPhone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) RegResetAccount(ctx context.Context, in *auth.RegResetAccountReq, opts ...grpc.CallOption) (*auth.RegResetAccountResp, error) {
	out := new(auth.RegResetAccountResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/RegResetAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) RemoveUserHeadwearInUse(ctx context.Context, in *myinfo.RemoveUserHeadwearReq, opts ...grpc.CallOption) (*myinfo.RemoveUserHeadwearResp, error) {
	out := new(myinfo.RemoveUserHeadwearResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/RemoveUserHeadwearInUse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) ReportCrash(ctx context.Context, in *auth.ReportCrashReq, opts ...grpc.CallOption) (*auth.ReportCrashResp, error) {
	out := new(auth.ReportCrashResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/ReportCrash", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) ResetPWD(ctx context.Context, in *auth.ResetPWDReq, opts ...grpc.CallOption) (*auth.ResetPWDResp, error) {
	out := new(auth.ResetPWDResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/ResetPWD", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) SetLocalAccountClearSwitch(ctx context.Context, in *auth.SetLocalAccountClearSwitchReq, opts ...grpc.CallOption) (*auth.SetLocalAccountClearSwitchResp, error) {
	out := new(auth.SetLocalAccountClearSwitchResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/SetLocalAccountClearSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) SetMedalTaillight(ctx context.Context, in *myinfo.SetMyMedalTaillightReq, opts ...grpc.CallOption) (*myinfo.SetMyMedalTaillightResp, error) {
	out := new(myinfo.SetMyMedalTaillightResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/SetMedalTaillight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) SetUserHeadwearInUse(ctx context.Context, in *myinfo.SetUserHeadwearReq, opts ...grpc.CallOption) (*myinfo.SetUserHeadwearResp, error) {
	out := new(myinfo.SetUserHeadwearResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/SetUserHeadwearInUse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) SetUserWearCertification(ctx context.Context, in *myinfo.SetUserWearCertificationReq, opts ...grpc.CallOption) (*myinfo.SetUserWearCertificationResp, error) {
	out := new(myinfo.SetUserWearCertificationResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/SetUserWearCertification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) UpdatePhotoAlbum(ctx context.Context, in *myinfo.UpdatePhotoAlbumReq, opts ...grpc.CallOption) (*myinfo.UpdatePhotoAlbumResp, error) {
	out := new(myinfo.UpdatePhotoAlbumResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/UpdatePhotoAlbum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) UploadIosUserGames(ctx context.Context, in *game.UploadIosUserGamesReq, opts ...grpc.CallOption) (*game.UploadIosUserGamesResp, error) {
	out := new(game.UploadIosUserGamesResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/UploadIosUserGames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) UploadUserGames(ctx context.Context, in *game.UploadUserGamesReq, opts ...grpc.CallOption) (*game.UploadUserGamesResp, error) {
	out := new(game.UploadUserGamesResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/UploadUserGames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) UserKickTerminal(ctx context.Context, in *contact.UserKickTerminalReq, opts ...grpc.CallOption) (*contact.UserKickTerminalResp, error) {
	out := new(contact.UserKickTerminalResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/UserKickTerminal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) CheckRelatedLoginAccount(ctx context.Context, in *contact.CheckRelatedLoginAccountReq, opts ...grpc.CallOption) (*contact.CheckRelatedLoginAccountResp, error) {
	out := new(contact.CheckRelatedLoginAccountResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/CheckRelatedLoginAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) GetRelatedLoginAccount(ctx context.Context, in *contact.GetRelatedLoginAccountReq, opts ...grpc.CallOption) (*contact.GetRelatedLoginAccountResp, error) {
	out := new(contact.GetRelatedLoginAccountResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/GetRelatedLoginAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) UserRegComplete(ctx context.Context, in *auth.UserRegCompleteReq, opts ...grpc.CallOption) (*auth.UserRegCompleteResp, error) {
	out := new(auth.UserRegCompleteResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/UserRegComplete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) UserUploadFace(ctx context.Context, in *face.UserUploadFaceReq, opts ...grpc.CallOption) (*face.UserUploadFaceResp, error) {
	out := new(face.UserUploadFaceResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/UserUploadFace", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountLogicClient) VerifyCAPTCHA(ctx context.Context, in *auth.VerifyCAPTCHAReq, opts ...grpc.CallOption) (*auth.VerifyCAPTCHAResp, error) {
	out := new(auth.VerifyCAPTCHAResp)
	err := c.cc.Invoke(ctx, "/ga.api.account.AccountLogic/VerifyCAPTCHA", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountLogicServer is the server API for AccountLogic service.
type AccountLogicServer interface {
	AgreeUserContract(context.Context, *myinfo.AgreeUserContractReq) (*myinfo.AgreeUserContractResp, error)
	BatGetUserGrowInfo(context.Context, *myinfo.BatGetUserGrowInfoReq) (*myinfo.BatGetUserGrowInfoResp, error)
	BatchGetSmallFaceUrl(context.Context, *face.BatchGetSmallFaceUrlReq) (*face.BatchGetSmallFaceUrlResp, error)
	BindPhone(context.Context, *auth.BindPhoneReq) (*auth.BindPhoneResp, error)
	CheckPhoneExist(context.Context, *auth.CheckPhoneExistReq) (*auth.CheckPhoneExistResp, error)
	CheckUpgrade(context.Context, *auth.CheckUpgradeReq) (*auth.CheckUpgradeResp, error)
	CheckVerifyCode(context.Context, *auth.CheckVerifyCodeReq) (*auth.CheckVerifyCodeResp, error)
	ChinaMobileUidBind(context.Context, *auth.ChinaMobileUidBindReq) (*auth.ChinaMobileUidBindResp, error)
	GeneralCheckVerifyCode(context.Context, *auth.GeneralCheckVerifyCodeReq) (*auth.GeneralCheckVerifyCodeResp, error)
	GeneralSendVerifyCode(context.Context, *auth.GeneralSendVerifyCodeReq) (*auth.GeneralSendVerifyCodeResp, error)
	GetBigFace(context.Context, *face.GetBigFaceReq) (*face.GetBigFaceResp, error)
	GetCAPTCHA(context.Context, *auth.GetCAPTCHAReq) (*auth.GetCAPTCHAResp, error)
	GetCover(context.Context, *contact.GetCoverReq) (*contact.GetCoverResp, error)
	GetIosNeedUploadConfGameList(context.Context, *game.GetIosNeedUploadConfGameListReq) (*game.GetIosNeedUploadConfGameListResp, error)
	GetPhotoAlbum(context.Context, *myinfo.GetPhotoAlbumReq) (*myinfo.GetPhotoAlbumResp, error)
	GetSmallFace(context.Context, *face.GetSmallFaceReq) (*face.GetSmallFaceResp, error)
	GetUnregApplyAuditStatus(context.Context, *auth.GetUnregApplyAuditStatusReq) (*auth.GetUnregApplyAuditStatusResp, error)
	GetUserCertificationList(context.Context, *myinfo.GetUserCertifyListReq) (*myinfo.GetUserCertifyListResp, error)
	GetUserCertification(context.Context, *myinfo.GetUserCertificationReq) (*myinfo.GetUserCertificationResp, error)
	GetUserContractInfo(context.Context, *myinfo.GetUserContractInfoReq) (*myinfo.GetUserContractInfoResp, error)
	GetUserDetail(context.Context, *contact.GetUserDetailReq) (*contact.GetUserDetailResp, error)
	GetUserHeadwear(context.Context, *myinfo.GetUserHeadwearReq) (*myinfo.GetUserHeadwearResp, error)
	GetUserOnlineTerminalList(context.Context, *contact.GetUserOnlineTerminalListReq) (*contact.GetUserOnlineTerminalListResp, error)
	GetUserStatus(context.Context, *contact.GetUserStatusReq) (*contact.GetUserStatusResp, error)
	ModifyNickname(context.Context, *myinfo.ModifyNicknameReq) (*myinfo.ModifyNicknameResp, error)
	ModifyPWD(context.Context, *auth.ModifyPWDReq) (*auth.ModifyPWDResp, error)
	ModifySex(context.Context, *auth.ModifySexReq) (*auth.ModifySexResp, error)
	ModifySignature(context.Context, *myinfo.ModifySignatureReq) (*myinfo.ModifySignatureResp, error)
	ModifyUserName(context.Context, *myinfo.ModifyUserNameReq) (*myinfo.ModifyUserNameResp, error)
	ModifyVerify(context.Context, *auth.ModifyVerifyReq) (*auth.ModifyVerifyResp, error)
	RebindPhone(context.Context, *auth.RebindPhoneReq) (*auth.RebindPhoneResp, error)
	RegResetAccount(context.Context, *auth.RegResetAccountReq) (*auth.RegResetAccountResp, error)
	RemoveUserHeadwearInUse(context.Context, *myinfo.RemoveUserHeadwearReq) (*myinfo.RemoveUserHeadwearResp, error)
	ReportCrash(context.Context, *auth.ReportCrashReq) (*auth.ReportCrashResp, error)
	ResetPWD(context.Context, *auth.ResetPWDReq) (*auth.ResetPWDResp, error)
	SetLocalAccountClearSwitch(context.Context, *auth.SetLocalAccountClearSwitchReq) (*auth.SetLocalAccountClearSwitchResp, error)
	SetMedalTaillight(context.Context, *myinfo.SetMyMedalTaillightReq) (*myinfo.SetMyMedalTaillightResp, error)
	SetUserHeadwearInUse(context.Context, *myinfo.SetUserHeadwearReq) (*myinfo.SetUserHeadwearResp, error)
	SetUserWearCertification(context.Context, *myinfo.SetUserWearCertificationReq) (*myinfo.SetUserWearCertificationResp, error)
	UpdatePhotoAlbum(context.Context, *myinfo.UpdatePhotoAlbumReq) (*myinfo.UpdatePhotoAlbumResp, error)
	UploadIosUserGames(context.Context, *game.UploadIosUserGamesReq) (*game.UploadIosUserGamesResp, error)
	UploadUserGames(context.Context, *game.UploadUserGamesReq) (*game.UploadUserGamesResp, error)
	UserKickTerminal(context.Context, *contact.UserKickTerminalReq) (*contact.UserKickTerminalResp, error)
	CheckRelatedLoginAccount(context.Context, *contact.CheckRelatedLoginAccountReq) (*contact.CheckRelatedLoginAccountResp, error)
	GetRelatedLoginAccount(context.Context, *contact.GetRelatedLoginAccountReq) (*contact.GetRelatedLoginAccountResp, error)
	UserRegComplete(context.Context, *auth.UserRegCompleteReq) (*auth.UserRegCompleteResp, error)
	UserUploadFace(context.Context, *face.UserUploadFaceReq) (*face.UserUploadFaceResp, error)
	VerifyCAPTCHA(context.Context, *auth.VerifyCAPTCHAReq) (*auth.VerifyCAPTCHAResp, error)
}

func RegisterAccountLogicServer(s *grpc.Server, srv AccountLogicServer) {
	s.RegisterService(&_AccountLogic_serviceDesc, srv)
}

func _AccountLogic_AgreeUserContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.AgreeUserContractReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).AgreeUserContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/AgreeUserContract",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).AgreeUserContract(ctx, req.(*myinfo.AgreeUserContractReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_BatGetUserGrowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.BatGetUserGrowInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).BatGetUserGrowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/BatGetUserGrowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).BatGetUserGrowInfo(ctx, req.(*myinfo.BatGetUserGrowInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_BatchGetSmallFaceUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(face.BatchGetSmallFaceUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).BatchGetSmallFaceUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/BatchGetSmallFaceUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).BatchGetSmallFaceUrl(ctx, req.(*face.BatchGetSmallFaceUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_BindPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.BindPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).BindPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/BindPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).BindPhone(ctx, req.(*auth.BindPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_CheckPhoneExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.CheckPhoneExistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).CheckPhoneExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/CheckPhoneExist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).CheckPhoneExist(ctx, req.(*auth.CheckPhoneExistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_CheckUpgrade_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.CheckUpgradeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).CheckUpgrade(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/CheckUpgrade",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).CheckUpgrade(ctx, req.(*auth.CheckUpgradeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_CheckVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.CheckVerifyCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).CheckVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/CheckVerifyCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).CheckVerifyCode(ctx, req.(*auth.CheckVerifyCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_ChinaMobileUidBind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.ChinaMobileUidBindReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).ChinaMobileUidBind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/ChinaMobileUidBind",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).ChinaMobileUidBind(ctx, req.(*auth.ChinaMobileUidBindReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GeneralCheckVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.GeneralCheckVerifyCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GeneralCheckVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GeneralCheckVerifyCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GeneralCheckVerifyCode(ctx, req.(*auth.GeneralCheckVerifyCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GeneralSendVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.GeneralSendVerifyCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GeneralSendVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GeneralSendVerifyCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GeneralSendVerifyCode(ctx, req.(*auth.GeneralSendVerifyCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetBigFace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(face.GetBigFaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetBigFace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetBigFace",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetBigFace(ctx, req.(*face.GetBigFaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetCAPTCHA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.GetCAPTCHAReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetCAPTCHA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetCAPTCHA",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetCAPTCHA(ctx, req.(*auth.GetCAPTCHAReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetCover_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(contact.GetCoverReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetCover(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetCover",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetCover(ctx, req.(*contact.GetCoverReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetIosNeedUploadConfGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game.GetIosNeedUploadConfGameListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetIosNeedUploadConfGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetIosNeedUploadConfGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetIosNeedUploadConfGameList(ctx, req.(*game.GetIosNeedUploadConfGameListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetPhotoAlbum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.GetPhotoAlbumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetPhotoAlbum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetPhotoAlbum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetPhotoAlbum(ctx, req.(*myinfo.GetPhotoAlbumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetSmallFace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(face.GetSmallFaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetSmallFace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetSmallFace",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetSmallFace(ctx, req.(*face.GetSmallFaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetUnregApplyAuditStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.GetUnregApplyAuditStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetUnregApplyAuditStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetUnregApplyAuditStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetUnregApplyAuditStatus(ctx, req.(*auth.GetUnregApplyAuditStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetUserCertificationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.GetUserCertifyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetUserCertificationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetUserCertificationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetUserCertificationList(ctx, req.(*myinfo.GetUserCertifyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetUserCertification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.GetUserCertificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetUserCertification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetUserCertification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetUserCertification(ctx, req.(*myinfo.GetUserCertificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetUserContractInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.GetUserContractInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetUserContractInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetUserContractInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetUserContractInfo(ctx, req.(*myinfo.GetUserContractInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetUserDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(contact.GetUserDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetUserDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetUserDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetUserDetail(ctx, req.(*contact.GetUserDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetUserHeadwear_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.GetUserHeadwearReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetUserHeadwear(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetUserHeadwear",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetUserHeadwear(ctx, req.(*myinfo.GetUserHeadwearReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetUserOnlineTerminalList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(contact.GetUserOnlineTerminalListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetUserOnlineTerminalList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetUserOnlineTerminalList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetUserOnlineTerminalList(ctx, req.(*contact.GetUserOnlineTerminalListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetUserStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(contact.GetUserStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetUserStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetUserStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetUserStatus(ctx, req.(*contact.GetUserStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_ModifyNickname_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.ModifyNicknameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).ModifyNickname(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/ModifyNickname",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).ModifyNickname(ctx, req.(*myinfo.ModifyNicknameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_ModifyPWD_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.ModifyPWDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).ModifyPWD(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/ModifyPWD",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).ModifyPWD(ctx, req.(*auth.ModifyPWDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_ModifySex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.ModifySexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).ModifySex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/ModifySex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).ModifySex(ctx, req.(*auth.ModifySexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_ModifySignature_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.ModifySignatureReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).ModifySignature(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/ModifySignature",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).ModifySignature(ctx, req.(*myinfo.ModifySignatureReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_ModifyUserName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.ModifyUserNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).ModifyUserName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/ModifyUserName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).ModifyUserName(ctx, req.(*myinfo.ModifyUserNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_ModifyVerify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.ModifyVerifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).ModifyVerify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/ModifyVerify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).ModifyVerify(ctx, req.(*auth.ModifyVerifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_RebindPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.RebindPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).RebindPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/RebindPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).RebindPhone(ctx, req.(*auth.RebindPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_RegResetAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.RegResetAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).RegResetAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/RegResetAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).RegResetAccount(ctx, req.(*auth.RegResetAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_RemoveUserHeadwearInUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.RemoveUserHeadwearReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).RemoveUserHeadwearInUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/RemoveUserHeadwearInUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).RemoveUserHeadwearInUse(ctx, req.(*myinfo.RemoveUserHeadwearReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_ReportCrash_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.ReportCrashReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).ReportCrash(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/ReportCrash",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).ReportCrash(ctx, req.(*auth.ReportCrashReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_ResetPWD_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.ResetPWDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).ResetPWD(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/ResetPWD",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).ResetPWD(ctx, req.(*auth.ResetPWDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_SetLocalAccountClearSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.SetLocalAccountClearSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).SetLocalAccountClearSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/SetLocalAccountClearSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).SetLocalAccountClearSwitch(ctx, req.(*auth.SetLocalAccountClearSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_SetMedalTaillight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.SetMyMedalTaillightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).SetMedalTaillight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/SetMedalTaillight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).SetMedalTaillight(ctx, req.(*myinfo.SetMyMedalTaillightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_SetUserHeadwearInUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.SetUserHeadwearReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).SetUserHeadwearInUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/SetUserHeadwearInUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).SetUserHeadwearInUse(ctx, req.(*myinfo.SetUserHeadwearReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_SetUserWearCertification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.SetUserWearCertificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).SetUserWearCertification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/SetUserWearCertification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).SetUserWearCertification(ctx, req.(*myinfo.SetUserWearCertificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_UpdatePhotoAlbum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.UpdatePhotoAlbumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).UpdatePhotoAlbum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/UpdatePhotoAlbum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).UpdatePhotoAlbum(ctx, req.(*myinfo.UpdatePhotoAlbumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_UploadIosUserGames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game.UploadIosUserGamesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).UploadIosUserGames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/UploadIosUserGames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).UploadIosUserGames(ctx, req.(*game.UploadIosUserGamesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_UploadUserGames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game.UploadUserGamesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).UploadUserGames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/UploadUserGames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).UploadUserGames(ctx, req.(*game.UploadUserGamesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_UserKickTerminal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(contact.UserKickTerminalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).UserKickTerminal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/UserKickTerminal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).UserKickTerminal(ctx, req.(*contact.UserKickTerminalReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_CheckRelatedLoginAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(contact.CheckRelatedLoginAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).CheckRelatedLoginAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/CheckRelatedLoginAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).CheckRelatedLoginAccount(ctx, req.(*contact.CheckRelatedLoginAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_GetRelatedLoginAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(contact.GetRelatedLoginAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).GetRelatedLoginAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/GetRelatedLoginAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).GetRelatedLoginAccount(ctx, req.(*contact.GetRelatedLoginAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_UserRegComplete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.UserRegCompleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).UserRegComplete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/UserRegComplete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).UserRegComplete(ctx, req.(*auth.UserRegCompleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_UserUploadFace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(face.UserUploadFaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).UserUploadFace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/UserUploadFace",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).UserUploadFace(ctx, req.(*face.UserUploadFaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountLogic_VerifyCAPTCHA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.VerifyCAPTCHAReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountLogicServer).VerifyCAPTCHA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account.AccountLogic/VerifyCAPTCHA",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountLogicServer).VerifyCAPTCHA(ctx, req.(*auth.VerifyCAPTCHAReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AccountLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.account.AccountLogic",
	HandlerType: (*AccountLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AgreeUserContract",
			Handler:    _AccountLogic_AgreeUserContract_Handler,
		},
		{
			MethodName: "BatGetUserGrowInfo",
			Handler:    _AccountLogic_BatGetUserGrowInfo_Handler,
		},
		{
			MethodName: "BatchGetSmallFaceUrl",
			Handler:    _AccountLogic_BatchGetSmallFaceUrl_Handler,
		},
		{
			MethodName: "BindPhone",
			Handler:    _AccountLogic_BindPhone_Handler,
		},
		{
			MethodName: "CheckPhoneExist",
			Handler:    _AccountLogic_CheckPhoneExist_Handler,
		},
		{
			MethodName: "CheckUpgrade",
			Handler:    _AccountLogic_CheckUpgrade_Handler,
		},
		{
			MethodName: "CheckVerifyCode",
			Handler:    _AccountLogic_CheckVerifyCode_Handler,
		},
		{
			MethodName: "ChinaMobileUidBind",
			Handler:    _AccountLogic_ChinaMobileUidBind_Handler,
		},
		{
			MethodName: "GeneralCheckVerifyCode",
			Handler:    _AccountLogic_GeneralCheckVerifyCode_Handler,
		},
		{
			MethodName: "GeneralSendVerifyCode",
			Handler:    _AccountLogic_GeneralSendVerifyCode_Handler,
		},
		{
			MethodName: "GetBigFace",
			Handler:    _AccountLogic_GetBigFace_Handler,
		},
		{
			MethodName: "GetCAPTCHA",
			Handler:    _AccountLogic_GetCAPTCHA_Handler,
		},
		{
			MethodName: "GetCover",
			Handler:    _AccountLogic_GetCover_Handler,
		},
		{
			MethodName: "GetIosNeedUploadConfGameList",
			Handler:    _AccountLogic_GetIosNeedUploadConfGameList_Handler,
		},
		{
			MethodName: "GetPhotoAlbum",
			Handler:    _AccountLogic_GetPhotoAlbum_Handler,
		},
		{
			MethodName: "GetSmallFace",
			Handler:    _AccountLogic_GetSmallFace_Handler,
		},
		{
			MethodName: "GetUnregApplyAuditStatus",
			Handler:    _AccountLogic_GetUnregApplyAuditStatus_Handler,
		},
		{
			MethodName: "GetUserCertificationList",
			Handler:    _AccountLogic_GetUserCertificationList_Handler,
		},
		{
			MethodName: "GetUserCertification",
			Handler:    _AccountLogic_GetUserCertification_Handler,
		},
		{
			MethodName: "GetUserContractInfo",
			Handler:    _AccountLogic_GetUserContractInfo_Handler,
		},
		{
			MethodName: "GetUserDetail",
			Handler:    _AccountLogic_GetUserDetail_Handler,
		},
		{
			MethodName: "GetUserHeadwear",
			Handler:    _AccountLogic_GetUserHeadwear_Handler,
		},
		{
			MethodName: "GetUserOnlineTerminalList",
			Handler:    _AccountLogic_GetUserOnlineTerminalList_Handler,
		},
		{
			MethodName: "GetUserStatus",
			Handler:    _AccountLogic_GetUserStatus_Handler,
		},
		{
			MethodName: "ModifyNickname",
			Handler:    _AccountLogic_ModifyNickname_Handler,
		},
		{
			MethodName: "ModifyPWD",
			Handler:    _AccountLogic_ModifyPWD_Handler,
		},
		{
			MethodName: "ModifySex",
			Handler:    _AccountLogic_ModifySex_Handler,
		},
		{
			MethodName: "ModifySignature",
			Handler:    _AccountLogic_ModifySignature_Handler,
		},
		{
			MethodName: "ModifyUserName",
			Handler:    _AccountLogic_ModifyUserName_Handler,
		},
		{
			MethodName: "ModifyVerify",
			Handler:    _AccountLogic_ModifyVerify_Handler,
		},
		{
			MethodName: "RebindPhone",
			Handler:    _AccountLogic_RebindPhone_Handler,
		},
		{
			MethodName: "RegResetAccount",
			Handler:    _AccountLogic_RegResetAccount_Handler,
		},
		{
			MethodName: "RemoveUserHeadwearInUse",
			Handler:    _AccountLogic_RemoveUserHeadwearInUse_Handler,
		},
		{
			MethodName: "ReportCrash",
			Handler:    _AccountLogic_ReportCrash_Handler,
		},
		{
			MethodName: "ResetPWD",
			Handler:    _AccountLogic_ResetPWD_Handler,
		},
		{
			MethodName: "SetLocalAccountClearSwitch",
			Handler:    _AccountLogic_SetLocalAccountClearSwitch_Handler,
		},
		{
			MethodName: "SetMedalTaillight",
			Handler:    _AccountLogic_SetMedalTaillight_Handler,
		},
		{
			MethodName: "SetUserHeadwearInUse",
			Handler:    _AccountLogic_SetUserHeadwearInUse_Handler,
		},
		{
			MethodName: "SetUserWearCertification",
			Handler:    _AccountLogic_SetUserWearCertification_Handler,
		},
		{
			MethodName: "UpdatePhotoAlbum",
			Handler:    _AccountLogic_UpdatePhotoAlbum_Handler,
		},
		{
			MethodName: "UploadIosUserGames",
			Handler:    _AccountLogic_UploadIosUserGames_Handler,
		},
		{
			MethodName: "UploadUserGames",
			Handler:    _AccountLogic_UploadUserGames_Handler,
		},
		{
			MethodName: "UserKickTerminal",
			Handler:    _AccountLogic_UserKickTerminal_Handler,
		},
		{
			MethodName: "CheckRelatedLoginAccount",
			Handler:    _AccountLogic_CheckRelatedLoginAccount_Handler,
		},
		{
			MethodName: "GetRelatedLoginAccount",
			Handler:    _AccountLogic_GetRelatedLoginAccount_Handler,
		},
		{
			MethodName: "UserRegComplete",
			Handler:    _AccountLogic_UserRegComplete_Handler,
		},
		{
			MethodName: "UserUploadFace",
			Handler:    _AccountLogic_UserUploadFace_Handler,
		},
		{
			MethodName: "VerifyCAPTCHA",
			Handler:    _AccountLogic_VerifyCAPTCHA_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/account/grpc_account.proto",
}

func init() {
	proto.RegisterFile("api/account/grpc_account.proto", fileDescriptor_grpc_account_e45e63b5a35f9d3f)
}

var fileDescriptor_grpc_account_e45e63b5a35f9d3f = []byte{
	// 1393 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x57, 0xcf, 0x6f, 0x1b, 0x45,
	0x14, 0x96, 0xeb, 0x28, 0x24, 0xd3, 0xb4, 0x49, 0x26, 0x89, 0xd3, 0xba, 0x69, 0x6a, 0x02, 0x5c,
	0x1d, 0x54, 0x54, 0x09, 0x54, 0x44, 0x71, 0xdc, 0xe2, 0x86, 0xb6, 0xa9, 0xb1, 0x63, 0x2a, 0x2a,
	0xa1, 0x32, 0xd9, 0x7d, 0x5e, 0x8f, 0xba, 0xde, 0xd9, 0xee, 0x8e, 0x9b, 0xe4, 0x16, 0xe5, 0x84,
	0x38, 0xc2, 0x09, 0x0e, 0xfc, 0x92, 0xb8, 0x22, 0xf8, 0x0b, 0x10, 0x70, 0x05, 0x09, 0x6e, 0x80,
	0xf8, 0x71, 0x44, 0xc0, 0x3f, 0x81, 0x66, 0x67, 0xf6, 0xed, 0x7a, 0x77, 0x9d, 0x9e, 0xec, 0xf9,
	0xbe, 0xf7, 0xbe, 0x79, 0xf3, 0x66, 0xf6, 0xcd, 0x1b, 0xb2, 0xce, 0x7c, 0xbe, 0xc9, 0x2c, 0x4b,
	0x8c, 0x3c, 0xb9, 0xe9, 0x04, 0xbe, 0xf5, 0xc0, 0x0c, 0xea, 0x7e, 0x20, 0xa4, 0xa0, 0x67, 0x1d,
	0x56, 0x67, 0x3e, 0xaf, 0x1b, 0xb4, 0x7a, 0x51, 0xd9, 0xc3, 0x81, 0x04, 0x2f, 0xe4, 0xc2, 0x4b,
	0xfe, 0x69, 0xf3, 0x2a, 0x61, 0x23, 0x39, 0x30, 0xff, 0xcf, 0x58, 0xc2, 0x93, 0xcc, 0x92, 0x31,
	0xd5, 0x67, 0x16, 0x98, 0xff, 0xa7, 0x1d, 0x36, 0x84, 0x07, 0xb1, 0xdd, 0xf0, 0xf0, 0x01, 0xf7,
	0xfa, 0x42, 0x0f, 0x2f, 0x7f, 0xb1, 0x4e, 0xe6, 0x1a, 0x7a, 0xb6, 0xdb, 0xc2, 0xe1, 0x16, 0x7d,
	0x83, 0x2c, 0x36, 0x9c, 0x00, 0xa0, 0x17, 0x42, 0xd0, 0x14, 0x9e, 0x0c, 0x98, 0x25, 0xe9, 0xb9,
	0xba, 0xc3, 0xea, 0x39, 0xb8, 0x03, 0x8f, 0xaa, 0xe7, 0x27, 0x30, 0xa1, 0xbf, 0xf1, 0xd4, 0xf1,
	0x51, 0xad, 0x3c, 0xf3, 0xd7, 0x12, 0xdd, 0x25, 0x74, 0x8b, 0xc9, 0x16, 0x48, 0x65, 0xd2, 0x0a,
	0xc4, 0xfe, 0xb6, 0xd7, 0x17, 0x34, 0xf2, 0xcc, 0xe3, 0x4a, 0xb4, 0x3a, 0x89, 0x42, 0xd5, 0x2f,
	0x4b, 0xf4, 0x3e, 0x59, 0xde, 0x62, 0xd2, 0x1a, 0xb4, 0x40, 0x76, 0x87, 0xcc, 0x75, 0x5f, 0x63,
	0x16, 0xf4, 0x02, 0x97, 0x5e, 0x30, 0xce, 0x39, 0x46, 0x29, 0xaf, 0x4d, 0x26, 0x51, 0xfb, 0x97,
	0x29, 0xfa, 0x12, 0x99, 0xdd, 0xe2, 0x9e, 0xdd, 0x1e, 0x08, 0x0f, 0xe8, 0x42, 0xe4, 0x13, 0x0f,
	0x95, 0xca, 0x62, 0x06, 0x41, 0xd7, 0xf7, 0xcb, 0xf4, 0x75, 0x32, 0xdf, 0x1c, 0x80, 0xf5, 0x30,
	0xa2, 0x6e, 0x1c, 0xf0, 0x50, 0xd2, 0x8a, 0x32, 0xcf, 0x80, 0x4a, 0x66, 0xb5, 0x10, 0x0f, 0xfd,
	0x8d, 0xe9, 0xe3, 0xa3, 0xda, 0xa9, 0x19, 0x4a, 0xb7, 0xc8, 0x5c, 0x44, 0xf7, 0x7c, 0x27, 0x60,
	0x36, 0xd0, 0x25, 0x74, 0x30, 0x88, 0x52, 0x59, 0xce, 0x83, 0x18, 0xcf, 0x67, 0x25, 0x8c, 0xe7,
	0x4d, 0x08, 0x78, 0xff, 0xb0, 0x29, 0x6c, 0x48, 0xc5, 0x93, 0x80, 0xe3, 0xf1, 0xa4, 0x71, 0x8c,
	0x67, 0x9b, 0xf6, 0x08, 0x6d, 0x0e, 0xb8, 0xc7, 0xee, 0x88, 0x3d, 0xee, 0x42, 0x8f, 0xdb, 0x2a,
	0x07, 0x7a, 0x23, 0xf3, 0x38, 0x6e, 0x64, 0x11, 0x15, 0xfa, 0x1b, 0x33, 0xc7, 0x47, 0xb5, 0xa9,
	0x99, 0x77, 0xff, 0x2b, 0xd1, 0x77, 0x48, 0xa5, 0x05, 0x1e, 0x04, 0xcc, 0xcd, 0x46, 0x7a, 0x51,
	0xf9, 0x17, 0x73, 0x4a, 0x7e, 0xfd, 0x24, 0x1a, 0x93, 0xf0, 0xf3, 0x34, 0x7d, 0x9b, 0xac, 0x18,
	0xb3, 0x2e, 0x78, 0x76, 0x6a, 0x82, 0xb5, 0x94, 0xc2, 0x38, 0xa5, 0xf4, 0x2f, 0x9e, 0xc0, 0xa2,
	0xfc, 0x4f, 0xd3, 0xf4, 0x2a, 0x21, 0x2d, 0x90, 0x5b, 0xdc, 0x51, 0x87, 0x89, 0x2e, 0x6a, 0xaf,
	0x78, 0xac, 0x84, 0x68, 0x16, 0xc2, 0xa4, 0x56, 0xe8, 0xcb, 0x91, 0x73, 0xb3, 0xd1, 0xde, 0x6d,
	0xde, 0x6c, 0xa0, 0xb3, 0x19, 0xa7, 0x9d, 0x11, 0xc2, 0xa9, 0x7f, 0x28, 0xd3, 0x2b, 0x64, 0x46,
	0x51, 0xe2, 0x31, 0x04, 0x74, 0x3e, 0x36, 0x54, 0x23, 0xe5, 0xb9, 0x30, 0x0e, 0xe0, 0xa4, 0x6b,
	0x34, 0x20, 0x6b, 0x2d, 0x90, 0xdb, 0x22, 0xdc, 0x01, 0xb0, 0x7b, 0xbe, 0x2b, 0x98, 0xdd, 0x14,
	0x5e, 0xbf, 0xc5, 0x86, 0x70, 0x5b, 0x1d, 0xd9, 0x67, 0x8c, 0xe7, 0x44, 0x0b, 0x25, 0xff, 0xec,
	0x93, 0x8d, 0x30, 0xd4, 0x8f, 0x16, 0xe9, 0x0d, 0x72, 0xa6, 0x05, 0xb2, 0x3d, 0x10, 0x52, 0x34,
	0xdc, 0xbd, 0xd1, 0x90, 0x2e, 0x1b, 0xff, 0x04, 0x52, 0xaa, 0x2b, 0x05, 0x28, 0xca, 0x7c, 0x3d,
	0x4b, 0x1b, 0x64, 0x2e, 0xfd, 0xed, 0xea, 0x8f, 0x22, 0x8d, 0xe0, 0x47, 0x31, 0x0e, 0xe2, 0xea,
	0x57, 0x68, 0x9f, 0x9c, 0x53, 0xa5, 0xc5, 0x0b, 0xc0, 0x69, 0xf8, 0xbe, 0x7b, 0xd8, 0x18, 0xd9,
	0x5c, 0x76, 0x25, 0x93, 0xa3, 0x90, 0x5e, 0x32, 0x9e, 0x85, 0xac, 0x92, 0xae, 0x9d, 0x6c, 0x80,
	0xa1, 0x7e, 0x52, 0xa6, 0xf7, 0xf5, 0x3c, 0xaa, 0x30, 0x42, 0x20, 0x79, 0x9f, 0x5b, 0x4c, 0x72,
	0xe1, 0x45, 0x19, 0x3e, 0x1f, 0xcb, 0x20, 0x7b, 0x18, 0xe7, 0xb5, 0x3a, 0x89, 0x42, 0xed, 0x7f,
	0xa2, 0xf2, 0x57, 0xa4, 0xad, 0xcb, 0x5f, 0x11, 0x83, 0xe5, 0xaf, 0x98, 0x44, 0xed, 0xbf, 0x4b,
	0xf4, 0x1e, 0x59, 0x8a, 0x8d, 0x4c, 0x41, 0x8f, 0x2a, 0xf6, 0x58, 0x5c, 0x29, 0x42, 0x29, 0x5f,
	0x98, 0xc8, 0xa1, 0xf0, 0x9f, 0x4b, 0xf4, 0x7a, 0x74, 0x04, 0x94, 0xcd, 0x75, 0x90, 0x8c, 0xbb,
	0x78, 0x04, 0x12, 0x28, 0x7d, 0x04, 0xd2, 0x28, 0x6e, 0xdf, 0x05, 0x7a, 0x8b, 0xcc, 0x1b, 0xf2,
	0x26, 0x30, 0x7b, 0x1f, 0x58, 0xa0, 0x4b, 0x5a, 0x06, 0xc4, 0x92, 0x96, 0xc3, 0x31, 0xa4, 0xef,
	0x4a, 0x94, 0x93, 0xf3, 0x86, 0xbf, 0xeb, 0xb9, 0xdc, 0x83, 0x5d, 0x08, 0x86, 0xdc, 0x63, 0x6e,
	0xb4, 0x49, 0xb5, 0x94, 0x7b, 0x9e, 0x56, 0x13, 0x3c, 0xfd, 0x04, 0x0b, 0x9c, 0xea, 0xd7, 0x29,
	0xf3, 0x01, 0x28, 0x4b, 0x73, 0xd6, 0xd2, 0xab, 0x4f, 0x0e, 0xd8, 0x4a, 0x01, 0x8a, 0x32, 0x9f,
	0x96, 0x69, 0x8b, 0x9c, 0xbd, 0x23, 0x6c, 0xde, 0x3f, 0xdc, 0xe1, 0xd6, 0x43, 0x8f, 0x0d, 0x81,
	0x46, 0x1e, 0xe3, 0x98, 0x12, 0xaa, 0x14, 0xc1, 0x98, 0xc7, 0x57, 0xe8, 0x8b, 0x64, 0x56, 0xb3,
	0xed, 0x7b, 0xd7, 0xf5, 0x2d, 0x87, 0x43, 0xbc, 0xe5, 0x52, 0x08, 0x7a, 0x2e, 0x24, 0x9e, 0x5d,
	0x38, 0x48, 0x7b, 0x76, 0xe1, 0x20, 0xe3, 0x19, 0x21, 0xe8, 0xd9, 0x56, 0xd7, 0x91, 0x21, 0xb8,
	0xe3, 0x31, 0x39, 0x0a, 0xcc, 0x75, 0x94, 0x01, 0x71, 0xef, 0x72, 0x38, 0x6a, 0x5d, 0x4b, 0x12,
	0xa1, 0x32, 0xb5, 0x93, 0x49, 0x44, 0x8c, 0x65, 0x12, 0x91, 0xc0, 0x28, 0xb4, 0xa8, 0x4a, 0x8a,
	0x66, 0x75, 0x81, 0xd7, 0x25, 0x25, 0x8d, 0x60, 0x49, 0x19, 0x07, 0x51, 0xe2, 0x55, 0x7a, 0x8d,
	0x9c, 0xee, 0xc0, 0x1e, 0xf6, 0x0c, 0x51, 0xcd, 0x4e, 0x01, 0x4a, 0x60, 0x29, 0x87, 0xe1, 0xae,
	0x7e, 0x10, 0xf5, 0x0d, 0x1d, 0x70, 0x3a, 0x10, 0x82, 0x34, 0xfd, 0x98, 0x4e, 0x4c, 0x06, 0xc4,
	0xc4, 0xe4, 0x70, 0x0c, 0xe6, 0x39, 0xfa, 0x16, 0x59, 0xed, 0xc0, 0x50, 0x3c, 0x86, 0xf4, 0xb1,
	0xdf, 0xf6, 0x7a, 0x21, 0xe8, 0xb2, 0x93, 0x27, 0xb1, 0xec, 0x14, 0x51, 0x18, 0xe6, 0xf7, 0x25,
	0xbd, 0x4e, 0x5f, 0x04, 0xb2, 0x19, 0xb0, 0x70, 0x10, 0xaf, 0x13, 0x81, 0xd4, 0x3a, 0x53, 0x18,
	0x0a, 0x7c, 0x5c, 0x52, 0x17, 0x56, 0x14, 0xb7, 0x3a, 0x73, 0xf3, 0xda, 0x52, 0x8f, 0xf0, 0xc2,
	0x4a, 0x00, 0x5c, 0xd2, 0x3c, 0x75, 0x49, 0xb5, 0x0b, 0xf2, 0xb6, 0xb0, 0x98, 0x6b, 0x56, 0xdc,
	0x74, 0x81, 0x05, 0xdd, 0x7d, 0x2e, 0xad, 0x01, 0x8d, 0xbe, 0xc2, 0xc9, 0xbc, 0x92, 0xde, 0x78,
	0x92, 0x49, 0x12, 0x64, 0x99, 0xf6, 0xc8, 0x62, 0x17, 0xe4, 0x1d, 0xb0, 0x99, 0xbb, 0xcb, 0xb8,
	0xeb, 0x72, 0x67, 0x20, 0x75, 0xf9, 0x53, 0xf0, 0xe1, 0x38, 0x81, 0xe5, 0xaf, 0x90, 0x43, 0xd9,
	0x6f, 0x4a, 0xb4, 0x4d, 0x96, 0xbb, 0xe3, 0xb5, 0x48, 0x6f, 0x4a, 0xc5, 0x78, 0x17, 0x56, 0xaf,
	0xee, 0xc4, 0xea, 0xf5, 0x6d, 0x49, 0xdd, 0x64, 0x86, 0xbf, 0x07, 0x2c, 0x73, 0x13, 0x5c, 0x4a,
	0x79, 0xe7, 0x58, 0xbc, 0xc9, 0x26, 0x1b, 0xe0, 0x3c, 0xff, 0x96, 0xe8, 0x0e, 0x59, 0xe8, 0xf9,
	0x36, 0x93, 0x90, 0xba, 0xbe, 0xa3, 0xe8, 0xb2, 0xa8, 0xd2, 0x3d, 0x57, 0x4c, 0xa0, 0xde, 0x57,
	0xb3, 0xea, 0x49, 0xa0, 0xdb, 0x85, 0x6d, 0x11, 0x46, 0x2d, 0x3e, 0x1b, 0x42, 0xa8, 0x0f, 0x67,
	0x1e, 0xc7, 0xc3, 0x59, 0x44, 0xa1, 0xea, 0x87, 0x8b, 0xea, 0x1b, 0xd2, 0x26, 0x89, 0x64, 0x25,
	0xf1, 0x1b, 0xd3, 0x5b, 0x2d, 0xc4, 0xf1, 0xc0, 0xdd, 0x8d, 0x56, 0x1c, 0x42, 0x70, 0x8b, 0x5b,
	0x0f, 0xe3, 0x8a, 0x6e, 0x56, 0x9c, 0x41, 0x93, 0x15, 0xe7, 0x08, 0x8c, 0xed, 0xb7, 0x29, 0xb5,
	0x53, 0x51, 0x8b, 0xda, 0x01, 0x97, 0x49, 0xb0, 0xd5, 0x63, 0xcb, 0x8b, 0x3f, 0xf4, 0x4b, 0xd8,
	0x78, 0x17, 0xb0, 0xb8, 0x53, 0x93, 0x0d, 0x70, 0x9e, 0xdf, 0xa7, 0x74, 0x33, 0x2d, 0x8b, 0x66,
	0x31, 0xdd, 0xac, 0x9c, 0x30, 0xc7, 0xfa, 0x49, 0x34, 0xce, 0xf0, 0xc7, 0x54, 0x94, 0xe5, 0x50,
	0xf5, 0x93, 0x4e, 0x53, 0x0c, 0x7d, 0x17, 0xa4, 0x39, 0xc0, 0x19, 0x30, 0xc9, 0x72, 0x16, 0xc7,
	0x2c, 0x9f, 0x51, 0x25, 0x5c, 0xd1, 0x7a, 0x23, 0xa2, 0x76, 0x6e, 0x25, 0x76, 0x49, 0x30, 0x2c,
	0xe1, 0x59, 0x18, 0x85, 0x96, 0xd5, 0xdd, 0x6a, 0xba, 0x73, 0xd3, 0x48, 0x47, 0xe5, 0x7a, 0x0c,
	0xc2, 0xbb, 0x35, 0x83, 0xe2, 0xda, 0x7e, 0x2c, 0x57, 0x57, 0x8f, 0x8f, 0x6a, 0x73, 0xe6, 0xf5,
	0xed, 0xaa, 0xf7, 0xf0, 0x7b, 0x47, 0xb5, 0xb2, 0xe5, 0xfb, 0x5b, 0x6d, 0x52, 0xb1, 0xc4, 0xb0,
	0xfe, 0x68, 0xb4, 0xcf, 0xbc, 0xba, 0x34, 0xaf, 0x6c, 0xf5, 0x56, 0xbf, 0xff, 0xbc, 0x23, 0x5c,
	0xe6, 0x39, 0xf5, 0x2b, 0x97, 0xa5, 0xac, 0x5b, 0x62, 0xb8, 0x19, 0x51, 0x96, 0x70, 0x37, 0x99,
	0xef, 0x6f, 0xa6, 0x1e, 0xfc, 0x57, 0xcd, 0xef, 0xe7, 0xa7, 0xca, 0x9d, 0x76, 0x73, 0x6f, 0x3a,
	0xb2, 0x7b, 0xe1, 0xff, 0x00, 0x00, 0x00, 0xff, 0xff, 0xb5, 0xa8, 0xc1, 0x08, 0x14, 0x10, 0x00,
	0x00,
}
