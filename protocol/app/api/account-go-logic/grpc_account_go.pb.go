// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/account_go/grpc_account_go.proto

package account_go_logic // import "golang.52tt.com/protocol/app/api/account-go-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import auth "golang.52tt.com/protocol/app/auth"
import contact "golang.52tt.com/protocol/app/contact"
import myinfo "golang.52tt.com/protocol/app/myinfo"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AccountGoLogicClient is the client API for AccountGoLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AccountGoLogicClient interface {
	GetUnregApplyAuditStatus(ctx context.Context, in *auth.GetUnregApplyAuditStatusReq, opts ...grpc.CallOption) (*auth.GetUnregApplyAuditStatusResp, error)
	GetUserDetail(ctx context.Context, in *contact.GetUserDetailReq, opts ...grpc.CallOption) (*contact.GetUserDetailResp, error)
	GetPhotoAlbum(ctx context.Context, in *myinfo.GetPhotoAlbumReq, opts ...grpc.CallOption) (*myinfo.GetPhotoAlbumResp, error)
	CheckRelatedLoginAccount(ctx context.Context, in *contact.CheckRelatedLoginAccountReq, opts ...grpc.CallOption) (*contact.CheckRelatedLoginAccountResp, error)
	GetUserStatus(ctx context.Context, in *contact.GetUserStatusReq, opts ...grpc.CallOption) (*contact.GetUserStatusResp, error)
	GetUserOnlineTerminalList(ctx context.Context, in *contact.GetUserOnlineTerminalListReq, opts ...grpc.CallOption) (*contact.GetUserOnlineTerminalListResp, error)
	ModifyNickname(ctx context.Context, in *myinfo.ModifyNicknameReq, opts ...grpc.CallOption) (*myinfo.ModifyNicknameResp, error)
	ModifySignature(ctx context.Context, in *myinfo.ModifySignatureReq, opts ...grpc.CallOption) (*myinfo.ModifySignatureResp, error)
	ModifySex(ctx context.Context, in *auth.ModifySexReq, opts ...grpc.CallOption) (*auth.ModifySexResp, error)
}

type accountGoLogicClient struct {
	cc *grpc.ClientConn
}

func NewAccountGoLogicClient(cc *grpc.ClientConn) AccountGoLogicClient {
	return &accountGoLogicClient{cc}
}

func (c *accountGoLogicClient) GetUnregApplyAuditStatus(ctx context.Context, in *auth.GetUnregApplyAuditStatusReq, opts ...grpc.CallOption) (*auth.GetUnregApplyAuditStatusResp, error) {
	out := new(auth.GetUnregApplyAuditStatusResp)
	err := c.cc.Invoke(ctx, "/ga.api.account_go.AccountGoLogic/GetUnregApplyAuditStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountGoLogicClient) GetUserDetail(ctx context.Context, in *contact.GetUserDetailReq, opts ...grpc.CallOption) (*contact.GetUserDetailResp, error) {
	out := new(contact.GetUserDetailResp)
	err := c.cc.Invoke(ctx, "/ga.api.account_go.AccountGoLogic/GetUserDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountGoLogicClient) GetPhotoAlbum(ctx context.Context, in *myinfo.GetPhotoAlbumReq, opts ...grpc.CallOption) (*myinfo.GetPhotoAlbumResp, error) {
	out := new(myinfo.GetPhotoAlbumResp)
	err := c.cc.Invoke(ctx, "/ga.api.account_go.AccountGoLogic/GetPhotoAlbum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountGoLogicClient) CheckRelatedLoginAccount(ctx context.Context, in *contact.CheckRelatedLoginAccountReq, opts ...grpc.CallOption) (*contact.CheckRelatedLoginAccountResp, error) {
	out := new(contact.CheckRelatedLoginAccountResp)
	err := c.cc.Invoke(ctx, "/ga.api.account_go.AccountGoLogic/CheckRelatedLoginAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountGoLogicClient) GetUserStatus(ctx context.Context, in *contact.GetUserStatusReq, opts ...grpc.CallOption) (*contact.GetUserStatusResp, error) {
	out := new(contact.GetUserStatusResp)
	err := c.cc.Invoke(ctx, "/ga.api.account_go.AccountGoLogic/GetUserStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountGoLogicClient) GetUserOnlineTerminalList(ctx context.Context, in *contact.GetUserOnlineTerminalListReq, opts ...grpc.CallOption) (*contact.GetUserOnlineTerminalListResp, error) {
	out := new(contact.GetUserOnlineTerminalListResp)
	err := c.cc.Invoke(ctx, "/ga.api.account_go.AccountGoLogic/GetUserOnlineTerminalList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountGoLogicClient) ModifyNickname(ctx context.Context, in *myinfo.ModifyNicknameReq, opts ...grpc.CallOption) (*myinfo.ModifyNicknameResp, error) {
	out := new(myinfo.ModifyNicknameResp)
	err := c.cc.Invoke(ctx, "/ga.api.account_go.AccountGoLogic/ModifyNickname", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountGoLogicClient) ModifySignature(ctx context.Context, in *myinfo.ModifySignatureReq, opts ...grpc.CallOption) (*myinfo.ModifySignatureResp, error) {
	out := new(myinfo.ModifySignatureResp)
	err := c.cc.Invoke(ctx, "/ga.api.account_go.AccountGoLogic/ModifySignature", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountGoLogicClient) ModifySex(ctx context.Context, in *auth.ModifySexReq, opts ...grpc.CallOption) (*auth.ModifySexResp, error) {
	out := new(auth.ModifySexResp)
	err := c.cc.Invoke(ctx, "/ga.api.account_go.AccountGoLogic/ModifySex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountGoLogicServer is the server API for AccountGoLogic service.
type AccountGoLogicServer interface {
	GetUnregApplyAuditStatus(context.Context, *auth.GetUnregApplyAuditStatusReq) (*auth.GetUnregApplyAuditStatusResp, error)
	GetUserDetail(context.Context, *contact.GetUserDetailReq) (*contact.GetUserDetailResp, error)
	GetPhotoAlbum(context.Context, *myinfo.GetPhotoAlbumReq) (*myinfo.GetPhotoAlbumResp, error)
	CheckRelatedLoginAccount(context.Context, *contact.CheckRelatedLoginAccountReq) (*contact.CheckRelatedLoginAccountResp, error)
	GetUserStatus(context.Context, *contact.GetUserStatusReq) (*contact.GetUserStatusResp, error)
	GetUserOnlineTerminalList(context.Context, *contact.GetUserOnlineTerminalListReq) (*contact.GetUserOnlineTerminalListResp, error)
	ModifyNickname(context.Context, *myinfo.ModifyNicknameReq) (*myinfo.ModifyNicknameResp, error)
	ModifySignature(context.Context, *myinfo.ModifySignatureReq) (*myinfo.ModifySignatureResp, error)
	ModifySex(context.Context, *auth.ModifySexReq) (*auth.ModifySexResp, error)
}

func RegisterAccountGoLogicServer(s *grpc.Server, srv AccountGoLogicServer) {
	s.RegisterService(&_AccountGoLogic_serviceDesc, srv)
}

func _AccountGoLogic_GetUnregApplyAuditStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.GetUnregApplyAuditStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountGoLogicServer).GetUnregApplyAuditStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account_go.AccountGoLogic/GetUnregApplyAuditStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountGoLogicServer).GetUnregApplyAuditStatus(ctx, req.(*auth.GetUnregApplyAuditStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountGoLogic_GetUserDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(contact.GetUserDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountGoLogicServer).GetUserDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account_go.AccountGoLogic/GetUserDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountGoLogicServer).GetUserDetail(ctx, req.(*contact.GetUserDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountGoLogic_GetPhotoAlbum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.GetPhotoAlbumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountGoLogicServer).GetPhotoAlbum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account_go.AccountGoLogic/GetPhotoAlbum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountGoLogicServer).GetPhotoAlbum(ctx, req.(*myinfo.GetPhotoAlbumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountGoLogic_CheckRelatedLoginAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(contact.CheckRelatedLoginAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountGoLogicServer).CheckRelatedLoginAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account_go.AccountGoLogic/CheckRelatedLoginAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountGoLogicServer).CheckRelatedLoginAccount(ctx, req.(*contact.CheckRelatedLoginAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountGoLogic_GetUserStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(contact.GetUserStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountGoLogicServer).GetUserStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account_go.AccountGoLogic/GetUserStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountGoLogicServer).GetUserStatus(ctx, req.(*contact.GetUserStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountGoLogic_GetUserOnlineTerminalList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(contact.GetUserOnlineTerminalListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountGoLogicServer).GetUserOnlineTerminalList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account_go.AccountGoLogic/GetUserOnlineTerminalList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountGoLogicServer).GetUserOnlineTerminalList(ctx, req.(*contact.GetUserOnlineTerminalListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountGoLogic_ModifyNickname_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.ModifyNicknameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountGoLogicServer).ModifyNickname(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account_go.AccountGoLogic/ModifyNickname",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountGoLogicServer).ModifyNickname(ctx, req.(*myinfo.ModifyNicknameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountGoLogic_ModifySignature_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(myinfo.ModifySignatureReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountGoLogicServer).ModifySignature(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account_go.AccountGoLogic/ModifySignature",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountGoLogicServer).ModifySignature(ctx, req.(*myinfo.ModifySignatureReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountGoLogic_ModifySex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.ModifySexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountGoLogicServer).ModifySex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.account_go.AccountGoLogic/ModifySex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountGoLogicServer).ModifySex(ctx, req.(*auth.ModifySexReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AccountGoLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.account_go.AccountGoLogic",
	HandlerType: (*AccountGoLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUnregApplyAuditStatus",
			Handler:    _AccountGoLogic_GetUnregApplyAuditStatus_Handler,
		},
		{
			MethodName: "GetUserDetail",
			Handler:    _AccountGoLogic_GetUserDetail_Handler,
		},
		{
			MethodName: "GetPhotoAlbum",
			Handler:    _AccountGoLogic_GetPhotoAlbum_Handler,
		},
		{
			MethodName: "CheckRelatedLoginAccount",
			Handler:    _AccountGoLogic_CheckRelatedLoginAccount_Handler,
		},
		{
			MethodName: "GetUserStatus",
			Handler:    _AccountGoLogic_GetUserStatus_Handler,
		},
		{
			MethodName: "GetUserOnlineTerminalList",
			Handler:    _AccountGoLogic_GetUserOnlineTerminalList_Handler,
		},
		{
			MethodName: "ModifyNickname",
			Handler:    _AccountGoLogic_ModifyNickname_Handler,
		},
		{
			MethodName: "ModifySignature",
			Handler:    _AccountGoLogic_ModifySignature_Handler,
		},
		{
			MethodName: "ModifySex",
			Handler:    _AccountGoLogic_ModifySex_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/account_go/grpc_account_go.proto",
}

func init() {
	proto.RegisterFile("api/account_go/grpc_account_go.proto", fileDescriptor_grpc_account_go_d211e129b8b293ca)
}

var fileDescriptor_grpc_account_go_d211e129b8b293ca = []byte{
	// 496 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x53, 0xcd, 0x6e, 0xd3, 0x30,
	0x1c, 0x57, 0x3f, 0x54, 0x3a, 0x4b, 0x1b, 0x9b, 0xc5, 0x4a, 0x09, 0x42, 0x84, 0x8a, 0xeb, 0x1c,
	0x69, 0xc0, 0x05, 0x24, 0x50, 0xb7, 0xa1, 0x09, 0xa9, 0x40, 0xd5, 0xc1, 0x85, 0x4b, 0xe5, 0xba,
	0xae, 0x6b, 0xcd, 0xb1, 0xbd, 0xc4, 0x11, 0xed, 0x2d, 0xea, 0x91, 0x23, 0x2f, 0x00, 0xe2, 0xc8,
	0x8d, 0xc7, 0x41, 0xe2, 0x61, 0x90, 0x93, 0xcc, 0xc9, 0x46, 0xcb, 0x6e, 0xf1, 0xef, 0xeb, 0xff,
	0xa1, 0xfc, 0xc1, 0x63, 0xac, 0x79, 0x80, 0x09, 0x51, 0x89, 0x34, 0x63, 0xa6, 0x02, 0x16, 0x69,
	0x32, 0x2e, 0xdf, 0x48, 0x47, 0xca, 0x28, 0xb8, 0xc7, 0x30, 0xc2, 0x9a, 0xa3, 0x92, 0xf0, 0x00,
	0x4e, 0xcc, 0x3c, 0xa7, 0xbd, 0x6d, 0xa2, 0xa4, 0xc1, 0xc4, 0x5c, 0x3e, 0xc3, 0xe5, 0x98, 0xcb,
	0x59, 0x61, 0xf6, 0x1e, 0xd8, 0x12, 0x74, 0x61, 0xa8, 0x8c, 0xb9, 0x92, 0xe5, 0x57, 0x4e, 0x1f,
	0xfe, 0x6c, 0x81, 0x9d, 0x7e, 0x9e, 0x7b, 0xaa, 0x06, 0x8a, 0x71, 0x02, 0x67, 0xa0, 0x7b, 0x4a,
	0xcd, 0x47, 0x19, 0x51, 0xd6, 0xd7, 0x5a, 0x2c, 0xfb, 0xc9, 0x94, 0x9b, 0x33, 0x83, 0x4d, 0x12,
	0xc3, 0x87, 0x88, 0x61, 0xb4, 0x89, 0x1d, 0xd1, 0x0b, 0xcf, 0xff, 0xbf, 0x20, 0xd6, 0xbd, 0x5b,
	0xab, 0xd4, 0x6f, 0xb4, 0xbf, 0x35, 0xe0, 0x09, 0xd8, 0xb6, 0xc2, 0x98, 0x46, 0x27, 0xd4, 0x60,
	0x2e, 0xe0, 0x9d, 0x4b, 0xaf, 0x83, 0x6c, 0xe2, 0xfe, 0x1a, 0x34, 0xd6, 0xbd, 0xd6, 0x2a, 0xf5,
	0xeb, 0xed, 0xfb, 0xf0, 0x75, 0x96, 0x32, 0x9c, 0x2b, 0xa3, 0xfa, 0x62, 0x92, 0x84, 0x2e, 0xa5,
	0x84, 0xaa, 0x29, 0x55, 0xd4, 0x35, 0xf3, 0x6b, 0xcb, 0x0e, 0x7d, 0x3c, 0xa7, 0xe4, 0x7c, 0x44,
	0x05, 0x36, 0x74, 0x6a, 0x37, 0x21, 0x8b, 0xbd, 0xe4, 0x43, 0x6f, 0x62, 0xdd, 0xd0, 0x9b, 0x05,
	0xae, 0xce, 0x9f, 0x66, 0xd1, 0xae, 0x9d, 0xa5, 0xd8, 0x68, 0x75, 0xe8, 0x72, 0x8d, 0xfb, 0x6b,
	0x50, 0x17, 0xf3, 0xbd, 0x01, 0x39, 0xb8, 0x57, 0xb0, 0xef, 0xa5, 0xe0, 0x92, 0x7e, 0xa0, 0x51,
	0xc8, 0x25, 0x16, 0x03, 0x1e, 0x1b, 0xe8, 0x57, 0xcc, 0xff, 0xd2, 0x36, 0xfe, 0xd1, 0x0d, 0x0a,
	0x57, 0xea, 0x77, 0x13, 0xbe, 0x01, 0x3b, 0x6f, 0xd5, 0x94, 0xcf, 0x96, 0xef, 0x38, 0x39, 0x97,
	0x38, 0xa4, 0x30, 0x6b, 0xee, 0x2a, 0x66, 0x43, 0x3b, 0xeb, 0xe0, 0x58, 0xf7, 0xda, 0xab, 0xd4,
	0x6f, 0xb6, 0x5f, 0x76, 0x6b, 0x70, 0x00, 0x6e, 0xe7, 0xfc, 0x19, 0x67, 0x12, 0x9b, 0x24, 0xa2,
	0xb0, 0x62, 0x72, 0xa0, 0x0d, 0xbb, 0xbb, 0x16, 0x77, 0x69, 0xaf, 0xba, 0x35, 0xf8, 0x1c, 0x6c,
	0x15, 0x02, 0xba, 0x80, 0xbb, 0x15, 0x3d, 0x5d, 0xd8, 0x84, 0xbd, 0x6b, 0x88, 0xf3, 0x0e, 0xbb,
	0x35, 0xef, 0xe9, 0x2a, 0xf5, 0x77, 0x8b, 0x7b, 0x3a, 0x60, 0xea, 0x40, 0xd8, 0x3f, 0xff, 0x4b,
	0xea, 0xd7, 0x99, 0xfa, 0x9a, 0xfa, 0x9d, 0x20, 0x7b, 0xa3, 0xab, 0x87, 0x11, 0x1c, 0x4d, 0x40,
	0x87, 0xa8, 0x10, 0x5d, 0x24, 0x9f, 0xb1, 0x44, 0xa6, 0x38, 0x38, 0x7b, 0x9a, 0x9f, 0x8e, 0x98,
	0x12, 0x58, 0x32, 0xf4, 0xec, 0xd0, 0x18, 0x44, 0x54, 0x18, 0x64, 0x14, 0x51, 0x22, 0xc0, 0x5a,
	0x07, 0x95, 0x2b, 0x77, 0xc5, 0x5e, 0x5c, 0x07, 0x7e, 0xd4, 0x1b, 0xa3, 0xe1, 0xf1, 0xa4, 0x95,
	0x39, 0x9f, 0xfc, 0x0d, 0x00, 0x00, 0xff, 0xff, 0x0a, 0x17, 0x97, 0x60, 0x1b, 0x04, 0x00, 0x00,
}
