// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/mijing_trifle/grpc_mijing_trifle.proto

package mijing_trifle // import "golang.52tt.com/protocol/app/api/mijing_trifle"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import mijing_trifle_logic "golang.52tt.com/protocol/app/mijing-trifle-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MijingTrifleLogicClient is the client API for MijingTrifleLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MijingTrifleLogicClient interface {
	GetHomepageTrifleInfo(ctx context.Context, in *mijing_trifle_logic.GetHomepageTrifleInfoRequest, opts ...grpc.CallOption) (*mijing_trifle_logic.GetHomepageTrifleInfoResponse, error)
	GetTrifleRecordList(ctx context.Context, in *mijing_trifle_logic.GetTrifleRecordListRequest, opts ...grpc.CallOption) (*mijing_trifle_logic.GetTrifleRecordListResponse, error)
	MarkTrifleRead(ctx context.Context, in *mijing_trifle_logic.MarkTrifleReadRequest, opts ...grpc.CallOption) (*mijing_trifle_logic.MarkTrifleReadResponse, error)
	GetActiveTrifleVisualStyle(ctx context.Context, in *mijing_trifle_logic.GetActiveTrifleVisualStyleRequest, opts ...grpc.CallOption) (*mijing_trifle_logic.GetActiveTrifleVisualStyleResponse, error)
}

type mijingTrifleLogicClient struct {
	cc *grpc.ClientConn
}

func NewMijingTrifleLogicClient(cc *grpc.ClientConn) MijingTrifleLogicClient {
	return &mijingTrifleLogicClient{cc}
}

func (c *mijingTrifleLogicClient) GetHomepageTrifleInfo(ctx context.Context, in *mijing_trifle_logic.GetHomepageTrifleInfoRequest, opts ...grpc.CallOption) (*mijing_trifle_logic.GetHomepageTrifleInfoResponse, error) {
	out := new(mijing_trifle_logic.GetHomepageTrifleInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.mijing_trifle.MijingTrifleLogic/GetHomepageTrifleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingTrifleLogicClient) GetTrifleRecordList(ctx context.Context, in *mijing_trifle_logic.GetTrifleRecordListRequest, opts ...grpc.CallOption) (*mijing_trifle_logic.GetTrifleRecordListResponse, error) {
	out := new(mijing_trifle_logic.GetTrifleRecordListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.mijing_trifle.MijingTrifleLogic/GetTrifleRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingTrifleLogicClient) MarkTrifleRead(ctx context.Context, in *mijing_trifle_logic.MarkTrifleReadRequest, opts ...grpc.CallOption) (*mijing_trifle_logic.MarkTrifleReadResponse, error) {
	out := new(mijing_trifle_logic.MarkTrifleReadResponse)
	err := c.cc.Invoke(ctx, "/ga.api.mijing_trifle.MijingTrifleLogic/MarkTrifleRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingTrifleLogicClient) GetActiveTrifleVisualStyle(ctx context.Context, in *mijing_trifle_logic.GetActiveTrifleVisualStyleRequest, opts ...grpc.CallOption) (*mijing_trifle_logic.GetActiveTrifleVisualStyleResponse, error) {
	out := new(mijing_trifle_logic.GetActiveTrifleVisualStyleResponse)
	err := c.cc.Invoke(ctx, "/ga.api.mijing_trifle.MijingTrifleLogic/GetActiveTrifleVisualStyle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MijingTrifleLogicServer is the server API for MijingTrifleLogic service.
type MijingTrifleLogicServer interface {
	GetHomepageTrifleInfo(context.Context, *mijing_trifle_logic.GetHomepageTrifleInfoRequest) (*mijing_trifle_logic.GetHomepageTrifleInfoResponse, error)
	GetTrifleRecordList(context.Context, *mijing_trifle_logic.GetTrifleRecordListRequest) (*mijing_trifle_logic.GetTrifleRecordListResponse, error)
	MarkTrifleRead(context.Context, *mijing_trifle_logic.MarkTrifleReadRequest) (*mijing_trifle_logic.MarkTrifleReadResponse, error)
	GetActiveTrifleVisualStyle(context.Context, *mijing_trifle_logic.GetActiveTrifleVisualStyleRequest) (*mijing_trifle_logic.GetActiveTrifleVisualStyleResponse, error)
}

func RegisterMijingTrifleLogicServer(s *grpc.Server, srv MijingTrifleLogicServer) {
	s.RegisterService(&_MijingTrifleLogic_serviceDesc, srv)
}

func _MijingTrifleLogic_GetHomepageTrifleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mijing_trifle_logic.GetHomepageTrifleInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingTrifleLogicServer).GetHomepageTrifleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mijing_trifle.MijingTrifleLogic/GetHomepageTrifleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingTrifleLogicServer).GetHomepageTrifleInfo(ctx, req.(*mijing_trifle_logic.GetHomepageTrifleInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingTrifleLogic_GetTrifleRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mijing_trifle_logic.GetTrifleRecordListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingTrifleLogicServer).GetTrifleRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mijing_trifle.MijingTrifleLogic/GetTrifleRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingTrifleLogicServer).GetTrifleRecordList(ctx, req.(*mijing_trifle_logic.GetTrifleRecordListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingTrifleLogic_MarkTrifleRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mijing_trifle_logic.MarkTrifleReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingTrifleLogicServer).MarkTrifleRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mijing_trifle.MijingTrifleLogic/MarkTrifleRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingTrifleLogicServer).MarkTrifleRead(ctx, req.(*mijing_trifle_logic.MarkTrifleReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingTrifleLogic_GetActiveTrifleVisualStyle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mijing_trifle_logic.GetActiveTrifleVisualStyleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingTrifleLogicServer).GetActiveTrifleVisualStyle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mijing_trifle.MijingTrifleLogic/GetActiveTrifleVisualStyle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingTrifleLogicServer).GetActiveTrifleVisualStyle(ctx, req.(*mijing_trifle_logic.GetActiveTrifleVisualStyleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MijingTrifleLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.mijing_trifle.MijingTrifleLogic",
	HandlerType: (*MijingTrifleLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetHomepageTrifleInfo",
			Handler:    _MijingTrifleLogic_GetHomepageTrifleInfo_Handler,
		},
		{
			MethodName: "GetTrifleRecordList",
			Handler:    _MijingTrifleLogic_GetTrifleRecordList_Handler,
		},
		{
			MethodName: "MarkTrifleRead",
			Handler:    _MijingTrifleLogic_MarkTrifleRead_Handler,
		},
		{
			MethodName: "GetActiveTrifleVisualStyle",
			Handler:    _MijingTrifleLogic_GetActiveTrifleVisualStyle_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/mijing_trifle/grpc_mijing_trifle.proto",
}

func init() {
	proto.RegisterFile("api/mijing_trifle/grpc_mijing_trifle.proto", fileDescriptor_grpc_mijing_trifle_acd00c1d57699360)
}

var fileDescriptor_grpc_mijing_trifle_acd00c1d57699360 = []byte{
	// 381 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x93, 0x4f, 0x4b, 0xc3, 0x30,
	0x18, 0xc6, 0x59, 0xfd, 0xc3, 0xe8, 0x41, 0x30, 0x53, 0xc1, 0x82, 0x50, 0x3c, 0x0a, 0x4d, 0x60,
	0x73, 0x07, 0xa7, 0x17, 0xf5, 0x30, 0x85, 0x0d, 0x64, 0x8a, 0x07, 0x11, 0x46, 0xec, 0xb2, 0x10,
	0x6d, 0x9b, 0xac, 0xcd, 0xd4, 0xdd, 0xca, 0x0e, 0x22, 0x1e, 0x3c, 0x78, 0xeb, 0xc1, 0x8b, 0x9f,
	0x43, 0xfd, 0x06, 0x7e, 0x27, 0x59, 0xb3, 0x22, 0x99, 0xdb, 0x70, 0xde, 0x9a, 0xbc, 0xcf, 0xef,
	0x79, 0x9f, 0xb7, 0xbc, 0x31, 0xb7, 0xb0, 0x60, 0xc8, 0x67, 0xd7, 0x2c, 0xa0, 0x4d, 0x19, 0xb2,
	0xb6, 0x47, 0x10, 0x0d, 0x85, 0xdb, 0xd4, 0xae, 0xa0, 0x08, 0xb9, 0xe4, 0x60, 0x85, 0x62, 0x88,
	0x05, 0x83, 0x5a, 0xcd, 0x72, 0xb4, 0x63, 0xd3, 0xe3, 0x94, 0xb9, 0x68, 0xcc, 0x9d, 0x32, 0xb1,
	0x36, 0x06, 0x0d, 0xc9, 0xbd, 0x24, 0x41, 0xc4, 0x78, 0xf0, 0xf3, 0xa5, 0xca, 0xc5, 0x87, 0x05,
	0x73, 0xb9, 0x9e, 0xc2, 0x67, 0x29, 0x5b, 0x1b, 0xa0, 0xe0, 0x39, 0x67, 0xae, 0x56, 0x89, 0x3c,
	0xe2, 0x3e, 0x11, 0x98, 0x12, 0x55, 0x3a, 0x0e, 0xda, 0x1c, 0x6c, 0x43, 0x8a, 0xe1, 0xb8, 0x6e,
	0x63, 0xe5, 0x0d, 0xd2, 0xe9, 0x92, 0x48, 0x5a, 0xe5, 0x19, 0xa9, 0x48, 0xf0, 0x20, 0x22, 0x9b,
	0xf9, 0x7e, 0x6c, 0xcf, 0xe7, 0xdf, 0x13, 0x03, 0x3c, 0xe6, 0xcc, 0x42, 0x95, 0x48, 0xa5, 0x69,
	0x10, 0x97, 0x87, 0xad, 0x1a, 0x8b, 0x24, 0x28, 0x4e, 0x31, 0x1e, 0x15, 0x67, 0x61, 0x4a, 0x33,
	0x31, 0x5a, 0x94, 0x8f, 0xc4, 0x00, 0x3d, 0x73, 0xa9, 0x8e, 0xc3, 0x9b, 0x4c, 0x89, 0x5b, 0xc0,
	0x99, 0x64, 0xa8, 0xeb, 0xb2, 0xfe, 0xf0, 0xaf, 0x72, 0xad, 0xf5, 0x67, 0x62, 0x80, 0xd7, 0x9c,
	0x69, 0x55, 0x89, 0xdc, 0x77, 0x25, 0xbb, 0x1d, 0xfe, 0xaf, 0x73, 0x16, 0x75, 0xb1, 0x77, 0x2a,
	0x7b, 0x1e, 0x01, 0x3b, 0x53, 0x06, 0x9b, 0xc0, 0x64, 0x99, 0x2a, 0xff, 0x41, 0xb5, 0x7c, 0x5f,
	0x89, 0x61, 0x55, 0xfa, 0xb1, 0x5d, 0x50, 0x2e, 0x8e, 0x72, 0x71, 0x52, 0x97, 0xa7, 0xd8, 0x36,
	0x28, 0x7f, 0x89, 0xed, 0x75, 0x34, 0x9c, 0x74, 0x74, 0xe3, 0xd0, 0xc1, 0xa5, 0xb9, 0xe6, 0x72,
	0x1f, 0x76, 0xba, 0x77, 0x38, 0x80, 0x52, 0xaa, 0xf5, 0x1c, 0xac, 0xff, 0xc5, 0x1e, 0xe5, 0x1e,
	0x0e, 0x28, 0x2c, 0x17, 0xa5, 0x84, 0x2e, 0xf7, 0x51, 0x5a, 0x72, 0xb9, 0x87, 0xb0, 0x10, 0xe8,
	0xd7, 0x7b, 0xda, 0xd5, 0x4e, 0x6f, 0xc6, 0x5c, 0xe3, 0xe4, 0xf0, 0x6a, 0x31, 0x65, 0x4a, 0xdf,
	0x01, 0x00, 0x00, 0xff, 0xff, 0xe5, 0xbf, 0x32, 0x8b, 0x7f, 0x03, 0x00, 0x00,
}
