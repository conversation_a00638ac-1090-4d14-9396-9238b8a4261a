// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/sync/grpc_sync.proto

package sync // import "golang.52tt.com/protocol/app/api/sync"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import sync "golang.52tt.com/protocol/app/sync"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SyncGoLogicClient is the client API for SyncGoLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SyncGoLogicClient interface {
	// checksynckey
	CheckSyncKey(ctx context.Context, in *sync.CheckSyncKeyReq, opts ...grpc.CallOption) (*sync.CheckSyncKeyResp, error)
}

type syncGoLogicClient struct {
	cc *grpc.ClientConn
}

func NewSyncGoLogicClient(cc *grpc.ClientConn) SyncGoLogicClient {
	return &syncGoLogicClient{cc}
}

func (c *syncGoLogicClient) CheckSyncKey(ctx context.Context, in *sync.CheckSyncKeyReq, opts ...grpc.CallOption) (*sync.CheckSyncKeyResp, error) {
	out := new(sync.CheckSyncKeyResp)
	err := c.cc.Invoke(ctx, "/ga.api.sync.SyncGoLogic/CheckSyncKey", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SyncGoLogicServer is the server API for SyncGoLogic service.
type SyncGoLogicServer interface {
	// checksynckey
	CheckSyncKey(context.Context, *sync.CheckSyncKeyReq) (*sync.CheckSyncKeyResp, error)
}

func RegisterSyncGoLogicServer(s *grpc.Server, srv SyncGoLogicServer) {
	s.RegisterService(&_SyncGoLogic_serviceDesc, srv)
}

func _SyncGoLogic_CheckSyncKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(sync.CheckSyncKeyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncGoLogicServer).CheckSyncKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.sync.SyncGoLogic/CheckSyncKey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncGoLogicServer).CheckSyncKey(ctx, req.(*sync.CheckSyncKeyReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SyncGoLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.sync.SyncGoLogic",
	HandlerType: (*SyncGoLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckSyncKey",
			Handler:    _SyncGoLogic_CheckSyncKey_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/sync/grpc_sync.proto",
}

func init() { proto.RegisterFile("api/sync/grpc_sync.proto", fileDescriptor_grpc_sync_b2238613d48d0c69) }

var fileDescriptor_grpc_sync_b2238613d48d0c69 = []byte{
	// 215 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x92, 0x48, 0x2c, 0xc8, 0xd4,
	0x2f, 0xae, 0xcc, 0x4b, 0xd6, 0x4f, 0x2f, 0x2a, 0x48, 0x8e, 0x07, 0xb1, 0xf4, 0x0a, 0x8a, 0xf2,
	0x4b, 0xf2, 0x85, 0xb8, 0xd3, 0x13, 0xf5, 0x12, 0x0b, 0x32, 0xf5, 0x40, 0x42, 0x52, 0xb2, 0x20,
	0x65, 0xa9, 0x15, 0x25, 0xa9, 0x79, 0xc5, 0x99, 0xf9, 0x79, 0x08, 0x16, 0x44, 0xad, 0x14, 0x17,
	0x42, 0x9f, 0x51, 0x1a, 0x17, 0x77, 0x70, 0x65, 0x5e, 0xb2, 0x7b, 0xbe, 0x4f, 0x7e, 0x7a, 0x66,
	0xb2, 0x90, 0x23, 0x17, 0x8f, 0x73, 0x46, 0x6a, 0x72, 0x36, 0x48, 0xcc, 0x3b, 0xb5, 0x52, 0x48,
	0x58, 0x2f, 0x3d, 0x51, 0x0f, 0x59, 0x24, 0x28, 0xb5, 0x50, 0x4a, 0x04, 0x53, 0xb0, 0xb8, 0x40,
	0x89, 0xad, 0xa9, 0x41, 0x81, 0x89, 0xc3, 0x4b, 0x4a, 0xa4, 0xa9, 0x41, 0x01, 0x6c, 0x83, 0x6e,
	0x0e, 0xc8, 0xc8, 0xae, 0x06, 0x05, 0xa6, 0xf4, 0x7c, 0x27, 0x2f, 0x2e, 0xb1, 0xe4, 0xfc, 0x5c,
	0xbd, 0xc2, 0xd2, 0xf2, 0xc4, 0x3c, 0xbd, 0x92, 0x12, 0x88, 0xed, 0x20, 0x17, 0x47, 0x69, 0xa5,
	0xe7, 0xe7, 0x24, 0xe6, 0xa5, 0xeb, 0x99, 0x1a, 0x95, 0x94, 0xe8, 0x25, 0xe7, 0xe7, 0xea, 0x83,
	0xa5, 0x92, 0xf3, 0x73, 0xf4, 0x13, 0x0b, 0x0a, 0xf4, 0x61, 0x1e, 0xb6, 0x06, 0x11, 0x8b, 0x98,
	0x98, 0x83, 0x02, 0x9c, 0x93, 0xd8, 0xc0, 0x2a, 0x8c, 0x01, 0x01, 0x00, 0x00, 0xff, 0xff, 0x81,
	0xcb, 0x2e, 0x20, 0x0e, 0x01, 0x00, 0x00,
}
