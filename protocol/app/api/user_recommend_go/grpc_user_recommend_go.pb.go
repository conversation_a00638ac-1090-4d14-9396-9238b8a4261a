// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/user_recommend_go/grpc_user_recommend_go.proto

package user_recommend_go // import "golang.52tt.com/protocol/app/api/user_recommend_go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import auth "golang.52tt.com/protocol/app/auth"
import find_friends "golang.52tt.com/protocol/app/find_friends"
import userrecommend "golang.52tt.com/protocol/app/userrecommend"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserRecommendLogicGoClient is the client API for UserRecommendLogicGo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserRecommendLogicGoClient interface {
	GetContractInfo(ctx context.Context, in *auth.GetContractInfoReq, opts ...grpc.CallOption) (*auth.GetContractInfoResp, error)
	ChangeRecommendStatus(ctx context.Context, in *userrecommend.ChangeRecommendStatusReq, opts ...grpc.CallOption) (*userrecommend.ChangeRecommendStatusResp, error)
	GetRecommendStatus(ctx context.Context, in *userrecommend.GetRecommendStatusReq, opts ...grpc.CallOption) (*userrecommend.GetRecommendStatusResp, error)
	CheckUserFinishFindFriendExam(ctx context.Context, in *find_friends.CheckUserFinishFindFriendExamReq, opts ...grpc.CallOption) (*find_friends.CheckUserFinishFindFriendExamResp, error)
	AddOrUpdateContacts(ctx context.Context, in *userrecommend.AddOrUpdateContactsReq, opts ...grpc.CallOption) (*userrecommend.AddOrUpdateContactsResp, error)
	GetRecommendFromContacts(ctx context.Context, in *userrecommend.GetRecommendFromContactsReq, opts ...grpc.CallOption) (*userrecommend.GetRecommendFromContactsResp, error)
	GetUserRecommend(ctx context.Context, in *userrecommend.GetUserRecommendReq, opts ...grpc.CallOption) (*userrecommend.GetUserRecommendResp, error)
}

type userRecommendLogicGoClient struct {
	cc *grpc.ClientConn
}

func NewUserRecommendLogicGoClient(cc *grpc.ClientConn) UserRecommendLogicGoClient {
	return &userRecommendLogicGoClient{cc}
}

func (c *userRecommendLogicGoClient) GetContractInfo(ctx context.Context, in *auth.GetContractInfoReq, opts ...grpc.CallOption) (*auth.GetContractInfoResp, error) {
	out := new(auth.GetContractInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.user_recommend_go.UserRecommendLogicGo/GetContractInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecommendLogicGoClient) ChangeRecommendStatus(ctx context.Context, in *userrecommend.ChangeRecommendStatusReq, opts ...grpc.CallOption) (*userrecommend.ChangeRecommendStatusResp, error) {
	out := new(userrecommend.ChangeRecommendStatusResp)
	err := c.cc.Invoke(ctx, "/ga.api.user_recommend_go.UserRecommendLogicGo/ChangeRecommendStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecommendLogicGoClient) GetRecommendStatus(ctx context.Context, in *userrecommend.GetRecommendStatusReq, opts ...grpc.CallOption) (*userrecommend.GetRecommendStatusResp, error) {
	out := new(userrecommend.GetRecommendStatusResp)
	err := c.cc.Invoke(ctx, "/ga.api.user_recommend_go.UserRecommendLogicGo/GetRecommendStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecommendLogicGoClient) CheckUserFinishFindFriendExam(ctx context.Context, in *find_friends.CheckUserFinishFindFriendExamReq, opts ...grpc.CallOption) (*find_friends.CheckUserFinishFindFriendExamResp, error) {
	out := new(find_friends.CheckUserFinishFindFriendExamResp)
	err := c.cc.Invoke(ctx, "/ga.api.user_recommend_go.UserRecommendLogicGo/CheckUserFinishFindFriendExam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecommendLogicGoClient) AddOrUpdateContacts(ctx context.Context, in *userrecommend.AddOrUpdateContactsReq, opts ...grpc.CallOption) (*userrecommend.AddOrUpdateContactsResp, error) {
	out := new(userrecommend.AddOrUpdateContactsResp)
	err := c.cc.Invoke(ctx, "/ga.api.user_recommend_go.UserRecommendLogicGo/AddOrUpdateContacts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecommendLogicGoClient) GetRecommendFromContacts(ctx context.Context, in *userrecommend.GetRecommendFromContactsReq, opts ...grpc.CallOption) (*userrecommend.GetRecommendFromContactsResp, error) {
	out := new(userrecommend.GetRecommendFromContactsResp)
	err := c.cc.Invoke(ctx, "/ga.api.user_recommend_go.UserRecommendLogicGo/GetRecommendFromContacts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecommendLogicGoClient) GetUserRecommend(ctx context.Context, in *userrecommend.GetUserRecommendReq, opts ...grpc.CallOption) (*userrecommend.GetUserRecommendResp, error) {
	out := new(userrecommend.GetUserRecommendResp)
	err := c.cc.Invoke(ctx, "/ga.api.user_recommend_go.UserRecommendLogicGo/GetUserRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserRecommendLogicGoServer is the server API for UserRecommendLogicGo service.
type UserRecommendLogicGoServer interface {
	GetContractInfo(context.Context, *auth.GetContractInfoReq) (*auth.GetContractInfoResp, error)
	ChangeRecommendStatus(context.Context, *userrecommend.ChangeRecommendStatusReq) (*userrecommend.ChangeRecommendStatusResp, error)
	GetRecommendStatus(context.Context, *userrecommend.GetRecommendStatusReq) (*userrecommend.GetRecommendStatusResp, error)
	CheckUserFinishFindFriendExam(context.Context, *find_friends.CheckUserFinishFindFriendExamReq) (*find_friends.CheckUserFinishFindFriendExamResp, error)
	AddOrUpdateContacts(context.Context, *userrecommend.AddOrUpdateContactsReq) (*userrecommend.AddOrUpdateContactsResp, error)
	GetRecommendFromContacts(context.Context, *userrecommend.GetRecommendFromContactsReq) (*userrecommend.GetRecommendFromContactsResp, error)
	GetUserRecommend(context.Context, *userrecommend.GetUserRecommendReq) (*userrecommend.GetUserRecommendResp, error)
}

func RegisterUserRecommendLogicGoServer(s *grpc.Server, srv UserRecommendLogicGoServer) {
	s.RegisterService(&_UserRecommendLogicGo_serviceDesc, srv)
}

func _UserRecommendLogicGo_GetContractInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.GetContractInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecommendLogicGoServer).GetContractInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.user_recommend_go.UserRecommendLogicGo/GetContractInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecommendLogicGoServer).GetContractInfo(ctx, req.(*auth.GetContractInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecommendLogicGo_ChangeRecommendStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(userrecommend.ChangeRecommendStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecommendLogicGoServer).ChangeRecommendStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.user_recommend_go.UserRecommendLogicGo/ChangeRecommendStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecommendLogicGoServer).ChangeRecommendStatus(ctx, req.(*userrecommend.ChangeRecommendStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecommendLogicGo_GetRecommendStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(userrecommend.GetRecommendStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecommendLogicGoServer).GetRecommendStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.user_recommend_go.UserRecommendLogicGo/GetRecommendStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecommendLogicGoServer).GetRecommendStatus(ctx, req.(*userrecommend.GetRecommendStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecommendLogicGo_CheckUserFinishFindFriendExam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(find_friends.CheckUserFinishFindFriendExamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecommendLogicGoServer).CheckUserFinishFindFriendExam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.user_recommend_go.UserRecommendLogicGo/CheckUserFinishFindFriendExam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecommendLogicGoServer).CheckUserFinishFindFriendExam(ctx, req.(*find_friends.CheckUserFinishFindFriendExamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecommendLogicGo_AddOrUpdateContacts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(userrecommend.AddOrUpdateContactsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecommendLogicGoServer).AddOrUpdateContacts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.user_recommend_go.UserRecommendLogicGo/AddOrUpdateContacts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecommendLogicGoServer).AddOrUpdateContacts(ctx, req.(*userrecommend.AddOrUpdateContactsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecommendLogicGo_GetRecommendFromContacts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(userrecommend.GetRecommendFromContactsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecommendLogicGoServer).GetRecommendFromContacts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.user_recommend_go.UserRecommendLogicGo/GetRecommendFromContacts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecommendLogicGoServer).GetRecommendFromContacts(ctx, req.(*userrecommend.GetRecommendFromContactsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecommendLogicGo_GetUserRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(userrecommend.GetUserRecommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecommendLogicGoServer).GetUserRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.user_recommend_go.UserRecommendLogicGo/GetUserRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecommendLogicGoServer).GetUserRecommend(ctx, req.(*userrecommend.GetUserRecommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserRecommendLogicGo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.user_recommend_go.UserRecommendLogicGo",
	HandlerType: (*UserRecommendLogicGoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetContractInfo",
			Handler:    _UserRecommendLogicGo_GetContractInfo_Handler,
		},
		{
			MethodName: "ChangeRecommendStatus",
			Handler:    _UserRecommendLogicGo_ChangeRecommendStatus_Handler,
		},
		{
			MethodName: "GetRecommendStatus",
			Handler:    _UserRecommendLogicGo_GetRecommendStatus_Handler,
		},
		{
			MethodName: "CheckUserFinishFindFriendExam",
			Handler:    _UserRecommendLogicGo_CheckUserFinishFindFriendExam_Handler,
		},
		{
			MethodName: "AddOrUpdateContacts",
			Handler:    _UserRecommendLogicGo_AddOrUpdateContacts_Handler,
		},
		{
			MethodName: "GetRecommendFromContacts",
			Handler:    _UserRecommendLogicGo_GetRecommendFromContacts_Handler,
		},
		{
			MethodName: "GetUserRecommend",
			Handler:    _UserRecommendLogicGo_GetUserRecommend_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/user_recommend_go/grpc_user_recommend_go.proto",
}

func init() {
	proto.RegisterFile("api/user_recommend_go/grpc_user_recommend_go.proto", fileDescriptor_grpc_user_recommend_go_a6841b05e8c498e3)
}

var fileDescriptor_grpc_user_recommend_go_a6841b05e8c498e3 = []byte{
	// 481 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x94, 0x4f, 0x8b, 0x13, 0x31,
	0x14, 0xc0, 0xd9, 0x28, 0xcb, 0x92, 0xcb, 0x4a, 0x74, 0x65, 0xa9, 0x2e, 0x96, 0x05, 0x5d, 0xff,
	0x60, 0x06, 0x2b, 0x9e, 0x3c, 0x69, 0xb5, 0x45, 0x10, 0x94, 0xca, 0x7a, 0xf0, 0x32, 0xc4, 0x4c,
	0x9a, 0x09, 0xdb, 0x49, 0xa6, 0x99, 0x57, 0x5c, 0x3c, 0x95, 0xf5, 0xe6, 0xc9, 0xcf, 0xe0, 0x37,
	0x98, 0xbb, 0xff, 0x3f, 0x96, 0x27, 0x79, 0x6d, 0x8d, 0x9d, 0x9d, 0x71, 0x69, 0x2f, 0x65, 0x92,
	0xf7, 0x7b, 0xef, 0xf7, 0x92, 0x34, 0xa1, 0x1d, 0x91, 0x9b, 0x68, 0x52, 0x28, 0x1f, 0x7b, 0x25,
	0x5d, 0x96, 0x29, 0x9b, 0xc4, 0xda, 0x45, 0xda, 0xe7, 0x32, 0xae, 0x4d, 0xf3, 0xdc, 0x3b, 0x70,
	0x6c, 0x57, 0x0b, 0x2e, 0x72, 0xc3, 0x6b, 0xf1, 0xd6, 0x1e, 0x56, 0x53, 0xc7, 0xa0, 0x6c, 0x61,
	0x9c, 0xfd, 0xf7, 0x35, 0x4f, 0x6c, 0x6d, 0x8b, 0x09, 0xa4, 0x11, 0xfe, 0x2c, 0x26, 0x0e, 0x86,
	0xc6, 0x26, 0xf1, 0xd0, 0x1b, 0x65, 0x93, 0x22, 0xc2, 0xc1, 0xfc, 0x3b, 0xce, 0x04, 0xc8, 0xd4,
	0x58, 0x1d, 0x2f, 0xc0, 0x7d, 0x74, 0x05, 0x55, 0x54, 0x19, 0x2d, 0x98, 0xce, 0xef, 0x4d, 0x7a,
	0xe9, 0xb0, 0x50, 0x7e, 0xf0, 0x37, 0xf0, 0xdc, 0x69, 0x23, 0xfb, 0x8e, 0xbd, 0xa6, 0xdb, 0x7d,
	0x05, 0x5d, 0x67, 0xc1, 0x0b, 0x09, 0xcf, 0xec, 0xd0, 0xb1, 0x2b, 0x1c, 0xd7, 0x80, 0x8d, 0x9c,
	0x8a, 0x0c, 0xd4, 0xb8, 0x75, 0xf5, 0xff, 0xc1, 0x22, 0xdf, 0xdf, 0x3a, 0x99, 0xb6, 0xcf, 0x6f,
	0x7d, 0x29, 0x09, 0x7b, 0x4f, 0x77, 0xba, 0xa9, 0xb0, 0x5a, 0x05, 0xe3, 0x2b, 0x10, 0x30, 0x29,
	0xd8, 0x6d, 0x2c, 0x50, 0xe9, 0x91, 0x37, 0x82, 0x28, 0xbb, 0xb3, 0x32, 0x1b, 0xdc, 0x5f, 0x4b,
	0xc2, 0xc6, 0x94, 0xf5, 0x15, 0x9c, 0x16, 0x1f, 0xd4, 0x8b, 0xd5, 0x29, 0xb4, 0xde, 0x5c, 0x0d,
	0x0c, 0xca, 0x6f, 0x25, 0x61, 0x9f, 0x36, 0xe8, 0x5e, 0x37, 0x55, 0xf2, 0x08, 0x37, 0xb9, 0x67,
	0xac, 0x29, 0xd2, 0x9e, 0xb1, 0x49, 0x6f, 0x76, 0x62, 0x4f, 0x8f, 0x45, 0xc6, 0xee, 0x61, 0xd5,
	0xe5, 0x23, 0xe5, 0x67, 0xf2, 0xd8, 0x48, 0x67, 0xdd, 0x94, 0xd0, 0xd2, 0xf7, 0x92, 0x30, 0xa0,
	0x17, 0x1f, 0x25, 0xc9, 0x0b, 0x7f, 0x98, 0x27, 0x02, 0x14, 0x1e, 0x95, 0x90, 0x50, 0xb0, 0x86,
	0xd5, 0x35, 0x60, 0xa8, 0xbf, 0xb5, 0x22, 0x19, 0xac, 0x3f, 0x4a, 0xc2, 0x3e, 0x6c, 0xd0, 0xdd,
	0xe5, 0xdd, 0xea, 0x79, 0x97, 0x05, 0xf7, 0xdd, 0xb3, 0x77, 0x76, 0x99, 0xc5, 0x06, 0xf8, 0x3a,
	0x78, 0xe8, 0xe2, 0x67, 0x49, 0xd8, 0x11, 0xbd, 0xd0, 0x57, 0x50, 0xf9, 0xc3, 0xb3, 0xeb, 0x8d,
	0xd5, 0x2a, 0x0c, 0x4a, 0x6f, 0xac, 0x82, 0x05, 0xd9, 0xaf, 0x92, 0xb4, 0xae, 0x9d, 0x4c, 0xdb,
	0x3b, 0x95, 0x8c, 0x11, 0x5e, 0xae, 0x58, 0xbb, 0x8f, 0xd3, 0x36, 0xd1, 0xee, 0x71, 0x42, 0x2f,
	0x4b, 0x97, 0xf1, 0xf1, 0xe4, 0x9d, 0xb0, 0x1c, 0x60, 0x7e, 0x25, 0xf1, 0x95, 0x78, 0xf3, 0x44,
	0xbb, 0x91, 0xb0, 0x9a, 0x3f, 0xe8, 0x00, 0x70, 0xe9, 0xb2, 0x68, 0x16, 0x92, 0x6e, 0x14, 0x89,
	0x3c, 0x8f, 0x1a, 0x9f, 0x9f, 0x87, 0xb5, 0x99, 0xcf, 0xe4, 0xdc, 0xe0, 0x65, 0xf7, 0xed, 0xe6,
	0x2c, 0xf7, 0xfe, 0x9f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x00, 0x51, 0x6a, 0xb6, 0x04, 0x00,
	0x00,
}
