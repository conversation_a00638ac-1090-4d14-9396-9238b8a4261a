// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/virtual_image_logic/grpc_virtual_image.proto

package virtual_image_logic // import "golang.52tt.com/protocol/app/api/virtual_image_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import virtual_image_logic "golang.52tt.com/protocol/app/virtual_image_logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// VirtualImageLogicClient is the client API for VirtualImageLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VirtualImageLogicClient interface {
	GetResourceList(ctx context.Context, in *virtual_image_logic.GetResourceListRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetResourceListResponse, error)
	GetVirtualImageResourceCategory(ctx context.Context, in *virtual_image_logic.GetVirtualImageResourceCategoryRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetVirtualImageResourceCategoryResponse, error)
	GetCommodityDataList(ctx context.Context, in *virtual_image_logic.GetCommodityDataListRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetCommodityDataListResponse, error)
	BuyCommodityData(ctx context.Context, in *virtual_image_logic.BuyCommodityDataRequest, opts ...grpc.CallOption) (*virtual_image_logic.BuyCommodityDataResponse, error)
	ComputeCommodityPrice(ctx context.Context, in *virtual_image_logic.ComputeCommodityPriceRequest, opts ...grpc.CallOption) (*virtual_image_logic.ComputeCommodityPriceResponse, error)
	GetRecommendCommodityDataList(ctx context.Context, in *virtual_image_logic.GetRecommendCommodityDataListRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetRecommendCommodityDataListResponse, error)
	GetRedDotAlertStatus(ctx context.Context, in *virtual_image_logic.GetRedDotAlertStatusRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetRedDotAlertStatusResponse, error)
	RedDotAlertReaded(ctx context.Context, in *virtual_image_logic.RedDotAlertReadedRequest, opts ...grpc.CallOption) (*virtual_image_logic.RedDotAlertReadedResponse, error)
	GetDefaultResourceList(ctx context.Context, in *virtual_image_logic.GetDefaultResourceListRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetDefaultResourceListResponse, error)
	GetCommodityDataListById(ctx context.Context, in *virtual_image_logic.GetCommodityDataListByIdRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetCommodityDataListByIdResponse, error)
	// 获取用户虚拟形象
	GetUserVirtualImage(ctx context.Context, in *virtual_image_logic.GetUserVirtualImageRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetUserVirtualImageResponse, error)
	// 设置用户虚拟形象使用
	SetUserVirtualImageInuse(ctx context.Context, in *virtual_image_logic.SetUserVirtualImageInuseRequest, opts ...grpc.CallOption) (*virtual_image_logic.SetUserVirtualImageInuseResponse, error)
	// 获取用户虚拟形象外显配置
	GetVirtualImageDisplayCfg(ctx context.Context, in *virtual_image_logic.GetVirtualImageDisplayCfgRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetVirtualImageDisplayCfgResponse, error)
	// 设置用户外显开关
	SetVirtualImageDisplaySwitch(ctx context.Context, in *virtual_image_logic.SetVirtualImageDisplaySwitchRequest, opts ...grpc.CallOption) (*virtual_image_logic.SetVirtualImageDisplaySwitchResponse, error)
	// 获取可绑定的邀请列表
	GetBindInvitableList(ctx context.Context, in *virtual_image_logic.GetBindInvitableListRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetBindInvitableListResponse, error)
	// 发起/取消 关系绑定邀请
	SetBindInvite(ctx context.Context, in *virtual_image_logic.SetBindInviteRequest, opts ...grpc.CallOption) (*virtual_image_logic.SetBindInviteResponse, error)
	// 确认/拒绝绑定
	BindConfirmAction(ctx context.Context, in *virtual_image_logic.BindConfirmActionRequest, opts ...grpc.CallOption) (*virtual_image_logic.BindConfirmActionResponse, error)
	// 获取关系绑定邀请状态
	GetBindInviteStatus(ctx context.Context, in *virtual_image_logic.GetBindInviteStatusRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetBindInviteStatusResponse, error)
	// 解除绑定
	UnbindVirtualImage(ctx context.Context, in *virtual_image_logic.UnbindVirtualImageRequest, opts ...grpc.CallOption) (*virtual_image_logic.UnbindVirtualImageResponse, error)
	// 指定使用双人关系
	SetVirtualBindInUse(ctx context.Context, in *virtual_image_logic.SetVirtualBindInUseRequest, opts ...grpc.CallOption) (*virtual_image_logic.SetVirtualBindInUseResponse, error)
	// 获取用户待处理的关系绑定邀请列表
	GetBindBeInvitedList(ctx context.Context, in *virtual_image_logic.GetBindBeInvitedListRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetBindBeInvitedListResponse, error)
	// 获取用户入口配置
	CheckUserVirtualImageEntrance(ctx context.Context, in *virtual_image_logic.CheckUserVirtualImageEntranceRequest, opts ...grpc.CallOption) (*virtual_image_logic.CheckUserVirtualImageEntranceResponse, error)
	// 批量获取用户使用中的虚拟形象
	BatchGetUserVirtualImageInuse(ctx context.Context, in *virtual_image_logic.BatchGetUserVirtualImageInuseRequest, opts ...grpc.CallOption) (*virtual_image_logic.BatchGetUserVirtualImageInuseResponse, error)
	// 获取用户正在使用的虚拟形象
	GetUserVirtualImageDisplay(ctx context.Context, in *virtual_image_logic.GetUserVirtualImageDisplayRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetUserVirtualImageDisplayResponse, error)
	// 设置用户使用的虚拟形象朝向
	SetUserVirtualImageOrientation(ctx context.Context, in *virtual_image_logic.SetUserVirtualImageOrientationRequest, opts ...grpc.CallOption) (*virtual_image_logic.SetUserVirtualImageOrientationResponse, error)
	GetVirtualImageCommodityRedDot(ctx context.Context, in *virtual_image_logic.GetVirtualImageCommodityRedDotRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetVirtualImageCommodityRedDotResponse, error)
	// 获取用户的虚拟形象姿势组件
	GetUserVirtualImagePose(ctx context.Context, in *virtual_image_logic.GetUserVirtualImagePoseRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetUserVirtualImagePoseResponse, error)
	GetVirtualImageCommodityRedDotDetail(ctx context.Context, in *virtual_image_logic.GetVirtualImageCommodityRedDotDetailRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetVirtualImageCommodityRedDotDetailResponse, error)
	// 获取虚拟形象无限卡通用配置
	GetVirtualImageCardCommonCfg(ctx context.Context, in *virtual_image_logic.GetVirtualImageCardCommonCfgRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetVirtualImageCardCommonCfgResponse, error)
	// 获取虚拟形象无限卡入口状态
	GetVirtualImageCardEntryStatus(ctx context.Context, in *virtual_image_logic.GetVirtualImageCardEntryStatusRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetVirtualImageCardEntryStatusResponse, error)
	// 设置虚拟形象姿势组件
	SetVirtualImagePoseType(ctx context.Context, in *virtual_image_logic.SetVirtualImagePoseTypeRequest, opts ...grpc.CallOption) (*virtual_image_logic.SetVirtualImagePoseTypeResponse, error)
	// 获取新手引导
	GetVirtualImageBeginnerGuide(ctx context.Context, in *virtual_image_logic.GetVirtualImageBeginnerGuideRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetVirtualImageBeginnerGuideResponse, error)
	// 标记新手引导完成
	MarkVirtualImageBeginnerGuideDone(ctx context.Context, in *virtual_image_logic.MarkVirtualImageBeginnerGuideDoneRequest, opts ...grpc.CallOption) (*virtual_image_logic.MarkVirtualImageBeginnerGuideDoneResponse, error)
}

type virtualImageLogicClient struct {
	cc *grpc.ClientConn
}

func NewVirtualImageLogicClient(cc *grpc.ClientConn) VirtualImageLogicClient {
	return &virtualImageLogicClient{cc}
}

func (c *virtualImageLogicClient) GetResourceList(ctx context.Context, in *virtual_image_logic.GetResourceListRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetResourceListResponse, error) {
	out := new(virtual_image_logic.GetResourceListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetResourceList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetVirtualImageResourceCategory(ctx context.Context, in *virtual_image_logic.GetVirtualImageResourceCategoryRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetVirtualImageResourceCategoryResponse, error) {
	out := new(virtual_image_logic.GetVirtualImageResourceCategoryResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetVirtualImageResourceCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetCommodityDataList(ctx context.Context, in *virtual_image_logic.GetCommodityDataListRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetCommodityDataListResponse, error) {
	out := new(virtual_image_logic.GetCommodityDataListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetCommodityDataList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) BuyCommodityData(ctx context.Context, in *virtual_image_logic.BuyCommodityDataRequest, opts ...grpc.CallOption) (*virtual_image_logic.BuyCommodityDataResponse, error) {
	out := new(virtual_image_logic.BuyCommodityDataResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/BuyCommodityData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) ComputeCommodityPrice(ctx context.Context, in *virtual_image_logic.ComputeCommodityPriceRequest, opts ...grpc.CallOption) (*virtual_image_logic.ComputeCommodityPriceResponse, error) {
	out := new(virtual_image_logic.ComputeCommodityPriceResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/ComputeCommodityPrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetRecommendCommodityDataList(ctx context.Context, in *virtual_image_logic.GetRecommendCommodityDataListRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetRecommendCommodityDataListResponse, error) {
	out := new(virtual_image_logic.GetRecommendCommodityDataListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetRecommendCommodityDataList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetRedDotAlertStatus(ctx context.Context, in *virtual_image_logic.GetRedDotAlertStatusRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetRedDotAlertStatusResponse, error) {
	out := new(virtual_image_logic.GetRedDotAlertStatusResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetRedDotAlertStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) RedDotAlertReaded(ctx context.Context, in *virtual_image_logic.RedDotAlertReadedRequest, opts ...grpc.CallOption) (*virtual_image_logic.RedDotAlertReadedResponse, error) {
	out := new(virtual_image_logic.RedDotAlertReadedResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/RedDotAlertReaded", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetDefaultResourceList(ctx context.Context, in *virtual_image_logic.GetDefaultResourceListRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetDefaultResourceListResponse, error) {
	out := new(virtual_image_logic.GetDefaultResourceListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetDefaultResourceList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetCommodityDataListById(ctx context.Context, in *virtual_image_logic.GetCommodityDataListByIdRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetCommodityDataListByIdResponse, error) {
	out := new(virtual_image_logic.GetCommodityDataListByIdResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetCommodityDataListById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetUserVirtualImage(ctx context.Context, in *virtual_image_logic.GetUserVirtualImageRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetUserVirtualImageResponse, error) {
	out := new(virtual_image_logic.GetUserVirtualImageResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetUserVirtualImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) SetUserVirtualImageInuse(ctx context.Context, in *virtual_image_logic.SetUserVirtualImageInuseRequest, opts ...grpc.CallOption) (*virtual_image_logic.SetUserVirtualImageInuseResponse, error) {
	out := new(virtual_image_logic.SetUserVirtualImageInuseResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/SetUserVirtualImageInuse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetVirtualImageDisplayCfg(ctx context.Context, in *virtual_image_logic.GetVirtualImageDisplayCfgRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetVirtualImageDisplayCfgResponse, error) {
	out := new(virtual_image_logic.GetVirtualImageDisplayCfgResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetVirtualImageDisplayCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) SetVirtualImageDisplaySwitch(ctx context.Context, in *virtual_image_logic.SetVirtualImageDisplaySwitchRequest, opts ...grpc.CallOption) (*virtual_image_logic.SetVirtualImageDisplaySwitchResponse, error) {
	out := new(virtual_image_logic.SetVirtualImageDisplaySwitchResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/SetVirtualImageDisplaySwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetBindInvitableList(ctx context.Context, in *virtual_image_logic.GetBindInvitableListRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetBindInvitableListResponse, error) {
	out := new(virtual_image_logic.GetBindInvitableListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetBindInvitableList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) SetBindInvite(ctx context.Context, in *virtual_image_logic.SetBindInviteRequest, opts ...grpc.CallOption) (*virtual_image_logic.SetBindInviteResponse, error) {
	out := new(virtual_image_logic.SetBindInviteResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/SetBindInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) BindConfirmAction(ctx context.Context, in *virtual_image_logic.BindConfirmActionRequest, opts ...grpc.CallOption) (*virtual_image_logic.BindConfirmActionResponse, error) {
	out := new(virtual_image_logic.BindConfirmActionResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/BindConfirmAction", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetBindInviteStatus(ctx context.Context, in *virtual_image_logic.GetBindInviteStatusRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetBindInviteStatusResponse, error) {
	out := new(virtual_image_logic.GetBindInviteStatusResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetBindInviteStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) UnbindVirtualImage(ctx context.Context, in *virtual_image_logic.UnbindVirtualImageRequest, opts ...grpc.CallOption) (*virtual_image_logic.UnbindVirtualImageResponse, error) {
	out := new(virtual_image_logic.UnbindVirtualImageResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/UnbindVirtualImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) SetVirtualBindInUse(ctx context.Context, in *virtual_image_logic.SetVirtualBindInUseRequest, opts ...grpc.CallOption) (*virtual_image_logic.SetVirtualBindInUseResponse, error) {
	out := new(virtual_image_logic.SetVirtualBindInUseResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/SetVirtualBindInUse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetBindBeInvitedList(ctx context.Context, in *virtual_image_logic.GetBindBeInvitedListRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetBindBeInvitedListResponse, error) {
	out := new(virtual_image_logic.GetBindBeInvitedListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetBindBeInvitedList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) CheckUserVirtualImageEntrance(ctx context.Context, in *virtual_image_logic.CheckUserVirtualImageEntranceRequest, opts ...grpc.CallOption) (*virtual_image_logic.CheckUserVirtualImageEntranceResponse, error) {
	out := new(virtual_image_logic.CheckUserVirtualImageEntranceResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/CheckUserVirtualImageEntrance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) BatchGetUserVirtualImageInuse(ctx context.Context, in *virtual_image_logic.BatchGetUserVirtualImageInuseRequest, opts ...grpc.CallOption) (*virtual_image_logic.BatchGetUserVirtualImageInuseResponse, error) {
	out := new(virtual_image_logic.BatchGetUserVirtualImageInuseResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/BatchGetUserVirtualImageInuse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetUserVirtualImageDisplay(ctx context.Context, in *virtual_image_logic.GetUserVirtualImageDisplayRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetUserVirtualImageDisplayResponse, error) {
	out := new(virtual_image_logic.GetUserVirtualImageDisplayResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetUserVirtualImageDisplay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) SetUserVirtualImageOrientation(ctx context.Context, in *virtual_image_logic.SetUserVirtualImageOrientationRequest, opts ...grpc.CallOption) (*virtual_image_logic.SetUserVirtualImageOrientationResponse, error) {
	out := new(virtual_image_logic.SetUserVirtualImageOrientationResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/SetUserVirtualImageOrientation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetVirtualImageCommodityRedDot(ctx context.Context, in *virtual_image_logic.GetVirtualImageCommodityRedDotRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetVirtualImageCommodityRedDotResponse, error) {
	out := new(virtual_image_logic.GetVirtualImageCommodityRedDotResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetVirtualImageCommodityRedDot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetUserVirtualImagePose(ctx context.Context, in *virtual_image_logic.GetUserVirtualImagePoseRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetUserVirtualImagePoseResponse, error) {
	out := new(virtual_image_logic.GetUserVirtualImagePoseResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetUserVirtualImagePose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetVirtualImageCommodityRedDotDetail(ctx context.Context, in *virtual_image_logic.GetVirtualImageCommodityRedDotDetailRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetVirtualImageCommodityRedDotDetailResponse, error) {
	out := new(virtual_image_logic.GetVirtualImageCommodityRedDotDetailResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetVirtualImageCommodityRedDotDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetVirtualImageCardCommonCfg(ctx context.Context, in *virtual_image_logic.GetVirtualImageCardCommonCfgRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetVirtualImageCardCommonCfgResponse, error) {
	out := new(virtual_image_logic.GetVirtualImageCardCommonCfgResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetVirtualImageCardCommonCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetVirtualImageCardEntryStatus(ctx context.Context, in *virtual_image_logic.GetVirtualImageCardEntryStatusRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetVirtualImageCardEntryStatusResponse, error) {
	out := new(virtual_image_logic.GetVirtualImageCardEntryStatusResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetVirtualImageCardEntryStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) SetVirtualImagePoseType(ctx context.Context, in *virtual_image_logic.SetVirtualImagePoseTypeRequest, opts ...grpc.CallOption) (*virtual_image_logic.SetVirtualImagePoseTypeResponse, error) {
	out := new(virtual_image_logic.SetVirtualImagePoseTypeResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/SetVirtualImagePoseType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) GetVirtualImageBeginnerGuide(ctx context.Context, in *virtual_image_logic.GetVirtualImageBeginnerGuideRequest, opts ...grpc.CallOption) (*virtual_image_logic.GetVirtualImageBeginnerGuideResponse, error) {
	out := new(virtual_image_logic.GetVirtualImageBeginnerGuideResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/GetVirtualImageBeginnerGuide", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageLogicClient) MarkVirtualImageBeginnerGuideDone(ctx context.Context, in *virtual_image_logic.MarkVirtualImageBeginnerGuideDoneRequest, opts ...grpc.CallOption) (*virtual_image_logic.MarkVirtualImageBeginnerGuideDoneResponse, error) {
	out := new(virtual_image_logic.MarkVirtualImageBeginnerGuideDoneResponse)
	err := c.cc.Invoke(ctx, "/ga.api.virtual_image_logic.VirtualImageLogic/MarkVirtualImageBeginnerGuideDone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VirtualImageLogicServer is the server API for VirtualImageLogic service.
type VirtualImageLogicServer interface {
	GetResourceList(context.Context, *virtual_image_logic.GetResourceListRequest) (*virtual_image_logic.GetResourceListResponse, error)
	GetVirtualImageResourceCategory(context.Context, *virtual_image_logic.GetVirtualImageResourceCategoryRequest) (*virtual_image_logic.GetVirtualImageResourceCategoryResponse, error)
	GetCommodityDataList(context.Context, *virtual_image_logic.GetCommodityDataListRequest) (*virtual_image_logic.GetCommodityDataListResponse, error)
	BuyCommodityData(context.Context, *virtual_image_logic.BuyCommodityDataRequest) (*virtual_image_logic.BuyCommodityDataResponse, error)
	ComputeCommodityPrice(context.Context, *virtual_image_logic.ComputeCommodityPriceRequest) (*virtual_image_logic.ComputeCommodityPriceResponse, error)
	GetRecommendCommodityDataList(context.Context, *virtual_image_logic.GetRecommendCommodityDataListRequest) (*virtual_image_logic.GetRecommendCommodityDataListResponse, error)
	GetRedDotAlertStatus(context.Context, *virtual_image_logic.GetRedDotAlertStatusRequest) (*virtual_image_logic.GetRedDotAlertStatusResponse, error)
	RedDotAlertReaded(context.Context, *virtual_image_logic.RedDotAlertReadedRequest) (*virtual_image_logic.RedDotAlertReadedResponse, error)
	GetDefaultResourceList(context.Context, *virtual_image_logic.GetDefaultResourceListRequest) (*virtual_image_logic.GetDefaultResourceListResponse, error)
	GetCommodityDataListById(context.Context, *virtual_image_logic.GetCommodityDataListByIdRequest) (*virtual_image_logic.GetCommodityDataListByIdResponse, error)
	// 获取用户虚拟形象
	GetUserVirtualImage(context.Context, *virtual_image_logic.GetUserVirtualImageRequest) (*virtual_image_logic.GetUserVirtualImageResponse, error)
	// 设置用户虚拟形象使用
	SetUserVirtualImageInuse(context.Context, *virtual_image_logic.SetUserVirtualImageInuseRequest) (*virtual_image_logic.SetUserVirtualImageInuseResponse, error)
	// 获取用户虚拟形象外显配置
	GetVirtualImageDisplayCfg(context.Context, *virtual_image_logic.GetVirtualImageDisplayCfgRequest) (*virtual_image_logic.GetVirtualImageDisplayCfgResponse, error)
	// 设置用户外显开关
	SetVirtualImageDisplaySwitch(context.Context, *virtual_image_logic.SetVirtualImageDisplaySwitchRequest) (*virtual_image_logic.SetVirtualImageDisplaySwitchResponse, error)
	// 获取可绑定的邀请列表
	GetBindInvitableList(context.Context, *virtual_image_logic.GetBindInvitableListRequest) (*virtual_image_logic.GetBindInvitableListResponse, error)
	// 发起/取消 关系绑定邀请
	SetBindInvite(context.Context, *virtual_image_logic.SetBindInviteRequest) (*virtual_image_logic.SetBindInviteResponse, error)
	// 确认/拒绝绑定
	BindConfirmAction(context.Context, *virtual_image_logic.BindConfirmActionRequest) (*virtual_image_logic.BindConfirmActionResponse, error)
	// 获取关系绑定邀请状态
	GetBindInviteStatus(context.Context, *virtual_image_logic.GetBindInviteStatusRequest) (*virtual_image_logic.GetBindInviteStatusResponse, error)
	// 解除绑定
	UnbindVirtualImage(context.Context, *virtual_image_logic.UnbindVirtualImageRequest) (*virtual_image_logic.UnbindVirtualImageResponse, error)
	// 指定使用双人关系
	SetVirtualBindInUse(context.Context, *virtual_image_logic.SetVirtualBindInUseRequest) (*virtual_image_logic.SetVirtualBindInUseResponse, error)
	// 获取用户待处理的关系绑定邀请列表
	GetBindBeInvitedList(context.Context, *virtual_image_logic.GetBindBeInvitedListRequest) (*virtual_image_logic.GetBindBeInvitedListResponse, error)
	// 获取用户入口配置
	CheckUserVirtualImageEntrance(context.Context, *virtual_image_logic.CheckUserVirtualImageEntranceRequest) (*virtual_image_logic.CheckUserVirtualImageEntranceResponse, error)
	// 批量获取用户使用中的虚拟形象
	BatchGetUserVirtualImageInuse(context.Context, *virtual_image_logic.BatchGetUserVirtualImageInuseRequest) (*virtual_image_logic.BatchGetUserVirtualImageInuseResponse, error)
	// 获取用户正在使用的虚拟形象
	GetUserVirtualImageDisplay(context.Context, *virtual_image_logic.GetUserVirtualImageDisplayRequest) (*virtual_image_logic.GetUserVirtualImageDisplayResponse, error)
	// 设置用户使用的虚拟形象朝向
	SetUserVirtualImageOrientation(context.Context, *virtual_image_logic.SetUserVirtualImageOrientationRequest) (*virtual_image_logic.SetUserVirtualImageOrientationResponse, error)
	GetVirtualImageCommodityRedDot(context.Context, *virtual_image_logic.GetVirtualImageCommodityRedDotRequest) (*virtual_image_logic.GetVirtualImageCommodityRedDotResponse, error)
	// 获取用户的虚拟形象姿势组件
	GetUserVirtualImagePose(context.Context, *virtual_image_logic.GetUserVirtualImagePoseRequest) (*virtual_image_logic.GetUserVirtualImagePoseResponse, error)
	GetVirtualImageCommodityRedDotDetail(context.Context, *virtual_image_logic.GetVirtualImageCommodityRedDotDetailRequest) (*virtual_image_logic.GetVirtualImageCommodityRedDotDetailResponse, error)
	// 获取虚拟形象无限卡通用配置
	GetVirtualImageCardCommonCfg(context.Context, *virtual_image_logic.GetVirtualImageCardCommonCfgRequest) (*virtual_image_logic.GetVirtualImageCardCommonCfgResponse, error)
	// 获取虚拟形象无限卡入口状态
	GetVirtualImageCardEntryStatus(context.Context, *virtual_image_logic.GetVirtualImageCardEntryStatusRequest) (*virtual_image_logic.GetVirtualImageCardEntryStatusResponse, error)
	// 设置虚拟形象姿势组件
	SetVirtualImagePoseType(context.Context, *virtual_image_logic.SetVirtualImagePoseTypeRequest) (*virtual_image_logic.SetVirtualImagePoseTypeResponse, error)
	// 获取新手引导
	GetVirtualImageBeginnerGuide(context.Context, *virtual_image_logic.GetVirtualImageBeginnerGuideRequest) (*virtual_image_logic.GetVirtualImageBeginnerGuideResponse, error)
	// 标记新手引导完成
	MarkVirtualImageBeginnerGuideDone(context.Context, *virtual_image_logic.MarkVirtualImageBeginnerGuideDoneRequest) (*virtual_image_logic.MarkVirtualImageBeginnerGuideDoneResponse, error)
}

func RegisterVirtualImageLogicServer(s *grpc.Server, srv VirtualImageLogicServer) {
	s.RegisterService(&_VirtualImageLogic_serviceDesc, srv)
}

func _VirtualImageLogic_GetResourceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetResourceListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetResourceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetResourceList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetResourceList(ctx, req.(*virtual_image_logic.GetResourceListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetVirtualImageResourceCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetVirtualImageResourceCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetVirtualImageResourceCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetVirtualImageResourceCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetVirtualImageResourceCategory(ctx, req.(*virtual_image_logic.GetVirtualImageResourceCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetCommodityDataList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetCommodityDataListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetCommodityDataList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetCommodityDataList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetCommodityDataList(ctx, req.(*virtual_image_logic.GetCommodityDataListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_BuyCommodityData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.BuyCommodityDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).BuyCommodityData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/BuyCommodityData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).BuyCommodityData(ctx, req.(*virtual_image_logic.BuyCommodityDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_ComputeCommodityPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.ComputeCommodityPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).ComputeCommodityPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/ComputeCommodityPrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).ComputeCommodityPrice(ctx, req.(*virtual_image_logic.ComputeCommodityPriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetRecommendCommodityDataList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetRecommendCommodityDataListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetRecommendCommodityDataList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetRecommendCommodityDataList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetRecommendCommodityDataList(ctx, req.(*virtual_image_logic.GetRecommendCommodityDataListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetRedDotAlertStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetRedDotAlertStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetRedDotAlertStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetRedDotAlertStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetRedDotAlertStatus(ctx, req.(*virtual_image_logic.GetRedDotAlertStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_RedDotAlertReaded_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.RedDotAlertReadedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).RedDotAlertReaded(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/RedDotAlertReaded",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).RedDotAlertReaded(ctx, req.(*virtual_image_logic.RedDotAlertReadedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetDefaultResourceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetDefaultResourceListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetDefaultResourceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetDefaultResourceList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetDefaultResourceList(ctx, req.(*virtual_image_logic.GetDefaultResourceListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetCommodityDataListById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetCommodityDataListByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetCommodityDataListById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetCommodityDataListById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetCommodityDataListById(ctx, req.(*virtual_image_logic.GetCommodityDataListByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetUserVirtualImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetUserVirtualImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetUserVirtualImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetUserVirtualImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetUserVirtualImage(ctx, req.(*virtual_image_logic.GetUserVirtualImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_SetUserVirtualImageInuse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.SetUserVirtualImageInuseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).SetUserVirtualImageInuse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/SetUserVirtualImageInuse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).SetUserVirtualImageInuse(ctx, req.(*virtual_image_logic.SetUserVirtualImageInuseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetVirtualImageDisplayCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetVirtualImageDisplayCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetVirtualImageDisplayCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetVirtualImageDisplayCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetVirtualImageDisplayCfg(ctx, req.(*virtual_image_logic.GetVirtualImageDisplayCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_SetVirtualImageDisplaySwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.SetVirtualImageDisplaySwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).SetVirtualImageDisplaySwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/SetVirtualImageDisplaySwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).SetVirtualImageDisplaySwitch(ctx, req.(*virtual_image_logic.SetVirtualImageDisplaySwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetBindInvitableList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetBindInvitableListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetBindInvitableList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetBindInvitableList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetBindInvitableList(ctx, req.(*virtual_image_logic.GetBindInvitableListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_SetBindInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.SetBindInviteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).SetBindInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/SetBindInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).SetBindInvite(ctx, req.(*virtual_image_logic.SetBindInviteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_BindConfirmAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.BindConfirmActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).BindConfirmAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/BindConfirmAction",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).BindConfirmAction(ctx, req.(*virtual_image_logic.BindConfirmActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetBindInviteStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetBindInviteStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetBindInviteStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetBindInviteStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetBindInviteStatus(ctx, req.(*virtual_image_logic.GetBindInviteStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_UnbindVirtualImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.UnbindVirtualImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).UnbindVirtualImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/UnbindVirtualImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).UnbindVirtualImage(ctx, req.(*virtual_image_logic.UnbindVirtualImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_SetVirtualBindInUse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.SetVirtualBindInUseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).SetVirtualBindInUse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/SetVirtualBindInUse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).SetVirtualBindInUse(ctx, req.(*virtual_image_logic.SetVirtualBindInUseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetBindBeInvitedList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetBindBeInvitedListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetBindBeInvitedList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetBindBeInvitedList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetBindBeInvitedList(ctx, req.(*virtual_image_logic.GetBindBeInvitedListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_CheckUserVirtualImageEntrance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.CheckUserVirtualImageEntranceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).CheckUserVirtualImageEntrance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/CheckUserVirtualImageEntrance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).CheckUserVirtualImageEntrance(ctx, req.(*virtual_image_logic.CheckUserVirtualImageEntranceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_BatchGetUserVirtualImageInuse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.BatchGetUserVirtualImageInuseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).BatchGetUserVirtualImageInuse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/BatchGetUserVirtualImageInuse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).BatchGetUserVirtualImageInuse(ctx, req.(*virtual_image_logic.BatchGetUserVirtualImageInuseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetUserVirtualImageDisplay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetUserVirtualImageDisplayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetUserVirtualImageDisplay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetUserVirtualImageDisplay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetUserVirtualImageDisplay(ctx, req.(*virtual_image_logic.GetUserVirtualImageDisplayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_SetUserVirtualImageOrientation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.SetUserVirtualImageOrientationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).SetUserVirtualImageOrientation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/SetUserVirtualImageOrientation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).SetUserVirtualImageOrientation(ctx, req.(*virtual_image_logic.SetUserVirtualImageOrientationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetVirtualImageCommodityRedDot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetVirtualImageCommodityRedDotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetVirtualImageCommodityRedDot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetVirtualImageCommodityRedDot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetVirtualImageCommodityRedDot(ctx, req.(*virtual_image_logic.GetVirtualImageCommodityRedDotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetUserVirtualImagePose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetUserVirtualImagePoseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetUserVirtualImagePose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetUserVirtualImagePose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetUserVirtualImagePose(ctx, req.(*virtual_image_logic.GetUserVirtualImagePoseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetVirtualImageCommodityRedDotDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetVirtualImageCommodityRedDotDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetVirtualImageCommodityRedDotDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetVirtualImageCommodityRedDotDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetVirtualImageCommodityRedDotDetail(ctx, req.(*virtual_image_logic.GetVirtualImageCommodityRedDotDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetVirtualImageCardCommonCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetVirtualImageCardCommonCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetVirtualImageCardCommonCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetVirtualImageCardCommonCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetVirtualImageCardCommonCfg(ctx, req.(*virtual_image_logic.GetVirtualImageCardCommonCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetVirtualImageCardEntryStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetVirtualImageCardEntryStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetVirtualImageCardEntryStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetVirtualImageCardEntryStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetVirtualImageCardEntryStatus(ctx, req.(*virtual_image_logic.GetVirtualImageCardEntryStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_SetVirtualImagePoseType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.SetVirtualImagePoseTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).SetVirtualImagePoseType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/SetVirtualImagePoseType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).SetVirtualImagePoseType(ctx, req.(*virtual_image_logic.SetVirtualImagePoseTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_GetVirtualImageBeginnerGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.GetVirtualImageBeginnerGuideRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).GetVirtualImageBeginnerGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/GetVirtualImageBeginnerGuide",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).GetVirtualImageBeginnerGuide(ctx, req.(*virtual_image_logic.GetVirtualImageBeginnerGuideRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageLogic_MarkVirtualImageBeginnerGuideDone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(virtual_image_logic.MarkVirtualImageBeginnerGuideDoneRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageLogicServer).MarkVirtualImageBeginnerGuideDone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.virtual_image_logic.VirtualImageLogic/MarkVirtualImageBeginnerGuideDone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageLogicServer).MarkVirtualImageBeginnerGuideDone(ctx, req.(*virtual_image_logic.MarkVirtualImageBeginnerGuideDoneRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _VirtualImageLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.virtual_image_logic.VirtualImageLogic",
	HandlerType: (*VirtualImageLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetResourceList",
			Handler:    _VirtualImageLogic_GetResourceList_Handler,
		},
		{
			MethodName: "GetVirtualImageResourceCategory",
			Handler:    _VirtualImageLogic_GetVirtualImageResourceCategory_Handler,
		},
		{
			MethodName: "GetCommodityDataList",
			Handler:    _VirtualImageLogic_GetCommodityDataList_Handler,
		},
		{
			MethodName: "BuyCommodityData",
			Handler:    _VirtualImageLogic_BuyCommodityData_Handler,
		},
		{
			MethodName: "ComputeCommodityPrice",
			Handler:    _VirtualImageLogic_ComputeCommodityPrice_Handler,
		},
		{
			MethodName: "GetRecommendCommodityDataList",
			Handler:    _VirtualImageLogic_GetRecommendCommodityDataList_Handler,
		},
		{
			MethodName: "GetRedDotAlertStatus",
			Handler:    _VirtualImageLogic_GetRedDotAlertStatus_Handler,
		},
		{
			MethodName: "RedDotAlertReaded",
			Handler:    _VirtualImageLogic_RedDotAlertReaded_Handler,
		},
		{
			MethodName: "GetDefaultResourceList",
			Handler:    _VirtualImageLogic_GetDefaultResourceList_Handler,
		},
		{
			MethodName: "GetCommodityDataListById",
			Handler:    _VirtualImageLogic_GetCommodityDataListById_Handler,
		},
		{
			MethodName: "GetUserVirtualImage",
			Handler:    _VirtualImageLogic_GetUserVirtualImage_Handler,
		},
		{
			MethodName: "SetUserVirtualImageInuse",
			Handler:    _VirtualImageLogic_SetUserVirtualImageInuse_Handler,
		},
		{
			MethodName: "GetVirtualImageDisplayCfg",
			Handler:    _VirtualImageLogic_GetVirtualImageDisplayCfg_Handler,
		},
		{
			MethodName: "SetVirtualImageDisplaySwitch",
			Handler:    _VirtualImageLogic_SetVirtualImageDisplaySwitch_Handler,
		},
		{
			MethodName: "GetBindInvitableList",
			Handler:    _VirtualImageLogic_GetBindInvitableList_Handler,
		},
		{
			MethodName: "SetBindInvite",
			Handler:    _VirtualImageLogic_SetBindInvite_Handler,
		},
		{
			MethodName: "BindConfirmAction",
			Handler:    _VirtualImageLogic_BindConfirmAction_Handler,
		},
		{
			MethodName: "GetBindInviteStatus",
			Handler:    _VirtualImageLogic_GetBindInviteStatus_Handler,
		},
		{
			MethodName: "UnbindVirtualImage",
			Handler:    _VirtualImageLogic_UnbindVirtualImage_Handler,
		},
		{
			MethodName: "SetVirtualBindInUse",
			Handler:    _VirtualImageLogic_SetVirtualBindInUse_Handler,
		},
		{
			MethodName: "GetBindBeInvitedList",
			Handler:    _VirtualImageLogic_GetBindBeInvitedList_Handler,
		},
		{
			MethodName: "CheckUserVirtualImageEntrance",
			Handler:    _VirtualImageLogic_CheckUserVirtualImageEntrance_Handler,
		},
		{
			MethodName: "BatchGetUserVirtualImageInuse",
			Handler:    _VirtualImageLogic_BatchGetUserVirtualImageInuse_Handler,
		},
		{
			MethodName: "GetUserVirtualImageDisplay",
			Handler:    _VirtualImageLogic_GetUserVirtualImageDisplay_Handler,
		},
		{
			MethodName: "SetUserVirtualImageOrientation",
			Handler:    _VirtualImageLogic_SetUserVirtualImageOrientation_Handler,
		},
		{
			MethodName: "GetVirtualImageCommodityRedDot",
			Handler:    _VirtualImageLogic_GetVirtualImageCommodityRedDot_Handler,
		},
		{
			MethodName: "GetUserVirtualImagePose",
			Handler:    _VirtualImageLogic_GetUserVirtualImagePose_Handler,
		},
		{
			MethodName: "GetVirtualImageCommodityRedDotDetail",
			Handler:    _VirtualImageLogic_GetVirtualImageCommodityRedDotDetail_Handler,
		},
		{
			MethodName: "GetVirtualImageCardCommonCfg",
			Handler:    _VirtualImageLogic_GetVirtualImageCardCommonCfg_Handler,
		},
		{
			MethodName: "GetVirtualImageCardEntryStatus",
			Handler:    _VirtualImageLogic_GetVirtualImageCardEntryStatus_Handler,
		},
		{
			MethodName: "SetVirtualImagePoseType",
			Handler:    _VirtualImageLogic_SetVirtualImagePoseType_Handler,
		},
		{
			MethodName: "GetVirtualImageBeginnerGuide",
			Handler:    _VirtualImageLogic_GetVirtualImageBeginnerGuide_Handler,
		},
		{
			MethodName: "MarkVirtualImageBeginnerGuideDone",
			Handler:    _VirtualImageLogic_MarkVirtualImageBeginnerGuideDone_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/virtual_image_logic/grpc_virtual_image.proto",
}

func init() {
	proto.RegisterFile("api/virtual_image_logic/grpc_virtual_image.proto", fileDescriptor_grpc_virtual_image_00586ff6626c7659)
}

var fileDescriptor_grpc_virtual_image_00586ff6626c7659 = []byte{
	// 1131 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x98, 0xcb, 0x6f, 0x1b, 0x45,
	0x1c, 0xc7, 0xb5, 0x1d, 0x84, 0xaa, 0x91, 0x10, 0x74, 0x0a, 0x85, 0x5a, 0x14, 0x02, 0xe2, 0xc0,
	0x81, 0xac, 0x4b, 0x42, 0x9f, 0x69, 0x0a, 0xb1, 0x8d, 0x4c, 0xa4, 0x22, 0xa2, 0x98, 0x70, 0xe0,
	0x12, 0x4d, 0x76, 0x27, 0x9b, 0x51, 0xed, 0x99, 0xed, 0xee, 0x6c, 0x8a, 0x25, 0x24, 0x42, 0xa0,
	0x10, 0x5a, 0xc4, 0x1a, 0x84, 0x84, 0x84, 0xc4, 0x05, 0x24, 0x24, 0x24, 0x4e, 0x5c, 0xf9, 0x1f,
	0x78, 0xbf, 0xdf, 0xf0, 0x97, 0x20, 0x7b, 0x77, 0x6b, 0xff, 0xd6, 0x3b, 0xfb, 0x70, 0x6f, 0x91,
	0xfd, 0xfd, 0xcc, 0x7c, 0x76, 0xe6, 0xb7, 0xbf, 0x99, 0x18, 0x9f, 0xa4, 0x2e, 0xaf, 0xef, 0x72,
	0x4f, 0x05, 0xb4, 0xbb, 0xc9, 0x7b, 0xd4, 0x61, 0x9b, 0x5d, 0xe9, 0x70, 0xab, 0xee, 0x78, 0xae,
	0xb5, 0x09, 0xbe, 0x30, 0x5d, 0x4f, 0x2a, 0x49, 0x6a, 0x0e, 0x35, 0xa9, 0xcb, 0xcd, 0x0c, 0xa8,
	0x36, 0x9f, 0x35, 0x52, 0xc6, 0x67, 0xd1, 0x50, 0xb5, 0x13, 0xc3, 0xc9, 0xd9, 0x4b, 0x8a, 0x09,
	0x9f, 0x4b, 0x31, 0xfe, 0x2b, 0xfa, 0x7a, 0xe1, 0xe0, 0x51, 0x7c, 0xe4, 0x85, 0x08, 0x5e, 0x1d,
	0xb2, 0x97, 0x86, 0x28, 0x79, 0x19, 0xdf, 0xd9, 0x66, 0x6a, 0x9d, 0xf9, 0x32, 0xf0, 0x2c, 0x76,
	0x89, 0xfb, 0x8a, 0x98, 0xa6, 0x43, 0xb3, 0x7c, 0xcc, 0x54, 0x70, 0x9d, 0x5d, 0x09, 0x98, 0xaf,
	0x6a, 0xf5, 0xd2, 0x79, 0xdf, 0x95, 0xc2, 0x67, 0x0f, 0x1f, 0xde, 0xdf, 0x9b, 0xbb, 0xed, 0xf0,
	0x57, 0x21, 0x22, 0x9f, 0x1b, 0xf8, 0xc1, 0x36, 0x53, 0x93, 0x5a, 0x09, 0xd1, 0xa4, 0x8a, 0x39,
	0xd2, 0xeb, 0x93, 0x8b, 0x39, 0xc3, 0xe7, 0x81, 0x89, 0xde, 0x93, 0x33, 0xf3, 0x40, 0xf7, 0xeb,
	0x10, 0x91, 0x1b, 0x06, 0xbe, 0xbb, 0xcd, 0x54, 0x53, 0xf6, 0x7a, 0xd2, 0xe6, 0xaa, 0xdf, 0xa2,
	0x8a, 0x8e, 0x96, 0x6c, 0x31, 0x67, 0x8e, 0xa9, 0x74, 0x22, 0xf6, 0x44, 0x35, 0x08, 0xd8, 0x7c,
	0x13, 0x22, 0xf2, 0x0a, 0xbe, 0xab, 0x11, 0xf4, 0x41, 0x92, 0x68, 0xf7, 0x22, 0x9d, 0x4c, 0x24,
	0x4e, 0x96, 0x07, 0x80, 0xc0, 0xb7, 0x21, 0x22, 0xef, 0x18, 0xf8, 0x9e, 0xa6, 0xec, 0xb9, 0x81,
	0x62, 0x37, 0xa3, 0x6b, 0x1e, 0xb7, 0x18, 0xd1, 0x3e, 0x5a, 0x66, 0x3c, 0x71, 0x39, 0x55, 0x91,
	0x02, 0x42, 0xdf, 0x85, 0x88, 0x7c, 0x6a, 0xe0, 0x13, 0xa3, 0xa2, 0xb3, 0x64, 0xaf, 0xc7, 0x84,
	0x3d, 0xbd, 0x51, 0x17, 0x72, 0x6b, 0x55, 0x87, 0x25, 0x82, 0xcb, 0x33, 0xd2, 0x40, 0xf4, 0xfb,
	0x71, 0x21, 0xad, 0x33, 0xbb, 0x25, 0xd5, 0x4a, 0x97, 0x79, 0xaa, 0xa3, 0xa8, 0x0a, 0xfc, 0xdc,
	0x42, 0x9a, 0x4a, 0x97, 0x29, 0xa4, 0x0c, 0x08, 0xd8, 0xfc, 0x10, 0x22, 0xb2, 0x6f, 0xe0, 0x23,
	0x13, 0xb9, 0x75, 0x46, 0x6d, 0x66, 0x13, 0x6d, 0x65, 0x4c, 0x45, 0x13, 0x8f, 0xc7, 0x2b, 0x10,
	0x40, 0xe2, 0xc7, 0x10, 0x91, 0x77, 0x0d, 0x7c, 0xac, 0xcd, 0x54, 0x8b, 0x6d, 0xd3, 0xa0, 0x0b,
	0x1b, 0xd2, 0xa9, 0x9c, 0xe7, 0xcb, 0xc8, 0x27, 0x3a, 0xa7, 0xab, 0x62, 0xc0, 0xe9, 0xa7, 0x10,
	0x91, 0x0f, 0x0c, 0x7c, 0x5f, 0xd6, 0xcb, 0xd8, 0xe8, 0xaf, 0xda, 0xe4, 0x4c, 0x95, 0xd7, 0x77,
	0x48, 0x24, 0x5e, 0x67, 0xab, 0x83, 0xc0, 0xec, 0xe7, 0x10, 0x91, 0x03, 0x03, 0x1f, 0x6d, 0x33,
	0xb5, 0xe1, 0x33, 0x6f, 0xb2, 0x87, 0x91, 0x85, 0x9c, 0xb1, 0xd3, 0xe1, 0xc4, 0x67, 0xb1, 0x12,
	0x03, 0x54, 0x7e, 0x89, 0x17, 0xa9, 0x33, 0x9d, 0x5c, 0x15, 0x81, 0xcf, 0xf4, 0x8b, 0xa4, 0x23,
	0x0a, 0x17, 0x49, 0x0f, 0x02, 0xb3, 0x5f, 0x43, 0x44, 0x3e, 0x34, 0xf0, 0xf1, 0x54, 0x93, 0x6f,
	0x71, 0xdf, 0xed, 0xd2, 0x7e, 0x73, 0xdb, 0x21, 0x67, 0x4b, 0x9e, 0x0b, 0x63, 0x24, 0x71, 0x3b,
	0x37, 0x03, 0x09, 0xe4, 0x7e, 0x0b, 0x11, 0xf9, 0xc4, 0xc0, 0xf7, 0x77, 0x32, 0xf3, 0x9d, 0xab,
	0x5c, 0x59, 0x3b, 0x64, 0x29, 0x67, 0x05, 0xb4, 0x54, 0xa2, 0x78, 0x61, 0x36, 0x18, 0x58, 0xfe,
	0x3e, 0x6e, 0x54, 0x0d, 0x2e, 0xec, 0x55, 0xb1, 0xcb, 0x15, 0xdd, 0xea, 0xb2, 0xc2, 0x13, 0x6f,
	0x2a, 0x5d, 0xa6, 0x51, 0x65, 0x40, 0xc0, 0xe6, 0x8f, 0x10, 0x91, 0x5d, 0x7c, 0x47, 0x67, 0x22,
	0xc9, 0xc8, 0x63, 0x39, 0x8f, 0x39, 0x8e, 0x25, 0xd3, 0xcf, 0x97, 0x4c, 0x83, 0x79, 0xff, 0x8c,
	0x1b, 0xe4, 0x30, 0xd0, 0x94, 0x62, 0x9b, 0x7b, 0xbd, 0x15, 0x4b, 0x71, 0x29, 0xf4, 0x0d, 0x72,
	0x2a, 0x5a, 0xd8, 0x20, 0x33, 0x08, 0x20, 0xf1, 0xd7, 0xf8, 0x95, 0x1f, 0x8b, 0xc6, 0x47, 0xc6,
	0x42, 0x99, 0x45, 0x65, 0xf0, 0xc4, 0x58, 0xac, 0xc4, 0x00, 0x95, 0xbf, 0x43, 0x44, 0xae, 0x19,
	0x98, 0x6c, 0x88, 0x2d, 0x2e, 0x6c, 0xd0, 0x7c, 0xb4, 0x8f, 0x37, 0x9d, 0x4d, 0x44, 0x16, 0xaa,
	0x20, 0xc0, 0xe3, 0x9f, 0x78, 0x49, 0xc6, 0x05, 0x1d, 0x89, 0x6f, 0xf8, 0x39, 0x5d, 0x30, 0x23,
	0x5c, 0xb8, 0x24, 0x99, 0x0c, 0x50, 0xf9, 0x17, 0xbe, 0x28, 0x0d, 0x16, 0x2d, 0x9f, 0x5d, 0xea,
	0x45, 0x01, 0xe9, 0xb2, 0x2f, 0x4a, 0x0a, 0x02, 0x36, 0xff, 0xc5, 0x17, 0xa1, 0xe6, 0x0e, 0xb3,
	0x2e, 0xa7, 0x5b, 0xe5, 0xd3, 0x42, 0x79, 0x54, 0x58, 0x4c, 0x7f, 0x11, 0xca, 0xc5, 0x0a, 0x2f,
	0x42, 0x05, 0x34, 0x10, 0x7d, 0x75, 0x10, 0x89, 0x36, 0xa8, 0xb2, 0x76, 0xda, 0xba, 0x13, 0x44,
	0x2b, 0x9a, 0x8b, 0x15, 0x8a, 0x16, 0xd0, 0x40, 0x74, 0x7f, 0x80, 0xc8, 0x47, 0x06, 0xae, 0x65,
	0xc4, 0xe3, 0xfe, 0x49, 0xce, 0x55, 0x38, 0x43, 0x63, 0x26, 0x51, 0x3c, 0x3f, 0x0b, 0x0a, 0xfc,
	0x5e, 0x1b, 0x20, 0xf2, 0x99, 0x81, 0x1f, 0xc8, 0x38, 0x1a, 0x9f, 0xf3, 0x38, 0x13, 0x8a, 0x8e,
	0xfa, 0xd5, 0x72, 0x85, 0x23, 0x75, 0x82, 0x4b, 0x3c, 0x2f, 0xce, 0x8a, 0x03, 0xd7, 0xd7, 0x63,
	0xd7, 0xd4, 0x51, 0x79, 0xf3, 0xde, 0x13, 0x5d, 0x15, 0xc9, 0x72, 0xc9, 0x23, 0x36, 0xc5, 0x15,
	0xba, 0x16, 0xe1, 0xc0, 0xf5, 0xda, 0x00, 0x91, 0xf7, 0x0d, 0x7c, 0x6f, 0xc6, 0x46, 0xac, 0x49,
	0x9f, 0x91, 0xd3, 0x15, 0x76, 0x6e, 0x08, 0x24, 0x76, 0x67, 0x2a, 0x73, 0x40, 0xeb, 0x8d, 0x01,
	0x22, 0x5f, 0x1a, 0xf8, 0x91, 0xfc, 0x67, 0x69, 0x31, 0x45, 0x79, 0x97, 0x34, 0x67, 0x5b, 0x89,
	0x88, 0x4e, 0x84, 0x5b, 0xb7, 0x36, 0x08, 0xb0, 0x7f, 0x73, 0x10, 0xdd, 0x7d, 0xd2, 0x28, 0xf5,
	0xa2, 0xff, 0x9c, 0xc4, 0xf0, 0x6e, 0xb6, 0x54, 0x76, 0xc2, 0x49, 0xaa, 0xf0, 0xee, 0x93, 0x0f,
	0x03, 0xcb, 0x03, 0x4d, 0x99, 0x52, 0xcf, 0x1e, 0x76, 0xb3, 0x7e, 0x7c, 0xf6, 0x2e, 0x57, 0x98,
	0x6a, 0x82, 0xab, 0x5c, 0xa6, 0x69, 0x1c, 0xb8, 0xbe, 0x15, 0x97, 0x69, 0xea, 0x6a, 0x37, 0xac,
	0x9c, 0xe7, 0xfb, 0x6e, 0x4e, 0x99, 0x6a, 0x80, 0xc2, 0x32, 0xd5, 0x72, 0x40, 0xeb, 0x7a, 0xf6,
	0x46, 0x37, 0x98, 0xc3, 0x85, 0x60, 0x5e, 0x3b, 0xe0, 0x36, 0x2b, 0xbd, 0xd1, 0x80, 0xaa, 0xba,
	0xd1, 0x29, 0x18, 0x58, 0xde, 0x18, 0x20, 0xf2, 0x85, 0x81, 0x1f, 0x7a, 0x96, 0x7a, 0x97, 0xb5,
	0x4c, 0x4b, 0x0a, 0x46, 0x9e, 0xd2, 0xcd, 0x56, 0x88, 0x26, 0xbe, 0x2b, 0xb7, 0x30, 0x02, 0x90,
	0x7e, 0x7b, 0x80, 0x6a, 0xe7, 0xf7, 0xf7, 0xe6, 0x8e, 0xc6, 0x83, 0xcd, 0x8f, 0x06, 0x9b, 0x1f,
	0x0d, 0x76, 0x7d, 0x6f, 0xee, 0x90, 0x23, 0xdf, 0xdb, 0x9b, 0x3b, 0x5e, 0x8f, 0x06, 0x9f, 0xfa,
	0xcd, 0xaf, 0xde, 0xd8, 0xc1, 0xc7, 0x2c, 0xd9, 0x33, 0xaf, 0x04, 0x57, 0xa9, 0x30, 0x95, 0x8a,
	0x7e, 0x20, 0x34, 0xa9, 0xcb, 0x5f, 0x7c, 0xc6, 0x91, 0x5d, 0x2a, 0x1c, 0xf3, 0xd4, 0x82, 0x52,
	0xa6, 0x25, 0x7b, 0xf5, 0xd1, 0x57, 0x96, 0xec, 0xd6, 0xa9, 0xeb, 0xd6, 0x35, 0xbf, 0x6e, 0x2e,
	0x65, 0x7c, 0xf6, 0xf1, 0x21, 0xb4, 0xbe, 0xd6, 0xdc, 0xba, 0x7d, 0xc4, 0x2f, 0xfe, 0x1f, 0x00,
	0x00, 0xff, 0xff, 0x07, 0xdc, 0x12, 0x9b, 0x19, 0x15, 0x00, 0x00,
}
