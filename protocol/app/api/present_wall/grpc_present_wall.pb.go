// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/present_wall/grpc_present_wall.proto

package present_wall // import "golang.52tt.com/protocol/app/api/present_wall"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import present_wall_logic "golang.52tt.com/protocol/app/present_wall_logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PresentWallLogicClient is the client API for PresentWallLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PresentWallLogicClient interface {
	GetUserPresentWall(ctx context.Context, in *present_wall_logic.GetUserPresentWallRequest, opts ...grpc.CallOption) (*present_wall_logic.GetUserPresentWallResponse, error)
	GetUserPresentWallMissingItems(ctx context.Context, in *present_wall_logic.GetUserPresentWallMissingItemsRequest, opts ...grpc.CallOption) (*present_wall_logic.GetUserPresentWallMissingItemsResponse, error)
	GetPresentNamingInfo(ctx context.Context, in *present_wall_logic.GetPresentNamingInfoRequest, opts ...grpc.CallOption) (*present_wall_logic.GetPresentNamingInfoResponse, error)
	GetPresentWallSwitch(ctx context.Context, in *present_wall_logic.GetPresentWallSwitchRequest, opts ...grpc.CallOption) (*present_wall_logic.GetPresentWallSwitchResponse, error)
	SetUserNamingSwitch(ctx context.Context, in *present_wall_logic.SetUserNamingSwitchRequest, opts ...grpc.CallOption) (*present_wall_logic.SetUserNamingSwitchResponse, error)
	GetUserNamingSwitch(ctx context.Context, in *present_wall_logic.GetUserNamingSwitchRequest, opts ...grpc.CallOption) (*present_wall_logic.GetUserNamingSwitchResponse, error)
}

type presentWallLogicClient struct {
	cc *grpc.ClientConn
}

func NewPresentWallLogicClient(cc *grpc.ClientConn) PresentWallLogicClient {
	return &presentWallLogicClient{cc}
}

func (c *presentWallLogicClient) GetUserPresentWall(ctx context.Context, in *present_wall_logic.GetUserPresentWallRequest, opts ...grpc.CallOption) (*present_wall_logic.GetUserPresentWallResponse, error) {
	out := new(present_wall_logic.GetUserPresentWallResponse)
	err := c.cc.Invoke(ctx, "/ga.api.present_wall.PresentWallLogic/GetUserPresentWall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWallLogicClient) GetUserPresentWallMissingItems(ctx context.Context, in *present_wall_logic.GetUserPresentWallMissingItemsRequest, opts ...grpc.CallOption) (*present_wall_logic.GetUserPresentWallMissingItemsResponse, error) {
	out := new(present_wall_logic.GetUserPresentWallMissingItemsResponse)
	err := c.cc.Invoke(ctx, "/ga.api.present_wall.PresentWallLogic/GetUserPresentWallMissingItems", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWallLogicClient) GetPresentNamingInfo(ctx context.Context, in *present_wall_logic.GetPresentNamingInfoRequest, opts ...grpc.CallOption) (*present_wall_logic.GetPresentNamingInfoResponse, error) {
	out := new(present_wall_logic.GetPresentNamingInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.present_wall.PresentWallLogic/GetPresentNamingInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWallLogicClient) GetPresentWallSwitch(ctx context.Context, in *present_wall_logic.GetPresentWallSwitchRequest, opts ...grpc.CallOption) (*present_wall_logic.GetPresentWallSwitchResponse, error) {
	out := new(present_wall_logic.GetPresentWallSwitchResponse)
	err := c.cc.Invoke(ctx, "/ga.api.present_wall.PresentWallLogic/GetPresentWallSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWallLogicClient) SetUserNamingSwitch(ctx context.Context, in *present_wall_logic.SetUserNamingSwitchRequest, opts ...grpc.CallOption) (*present_wall_logic.SetUserNamingSwitchResponse, error) {
	out := new(present_wall_logic.SetUserNamingSwitchResponse)
	err := c.cc.Invoke(ctx, "/ga.api.present_wall.PresentWallLogic/SetUserNamingSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentWallLogicClient) GetUserNamingSwitch(ctx context.Context, in *present_wall_logic.GetUserNamingSwitchRequest, opts ...grpc.CallOption) (*present_wall_logic.GetUserNamingSwitchResponse, error) {
	out := new(present_wall_logic.GetUserNamingSwitchResponse)
	err := c.cc.Invoke(ctx, "/ga.api.present_wall.PresentWallLogic/GetUserNamingSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PresentWallLogicServer is the server API for PresentWallLogic service.
type PresentWallLogicServer interface {
	GetUserPresentWall(context.Context, *present_wall_logic.GetUserPresentWallRequest) (*present_wall_logic.GetUserPresentWallResponse, error)
	GetUserPresentWallMissingItems(context.Context, *present_wall_logic.GetUserPresentWallMissingItemsRequest) (*present_wall_logic.GetUserPresentWallMissingItemsResponse, error)
	GetPresentNamingInfo(context.Context, *present_wall_logic.GetPresentNamingInfoRequest) (*present_wall_logic.GetPresentNamingInfoResponse, error)
	GetPresentWallSwitch(context.Context, *present_wall_logic.GetPresentWallSwitchRequest) (*present_wall_logic.GetPresentWallSwitchResponse, error)
	SetUserNamingSwitch(context.Context, *present_wall_logic.SetUserNamingSwitchRequest) (*present_wall_logic.SetUserNamingSwitchResponse, error)
	GetUserNamingSwitch(context.Context, *present_wall_logic.GetUserNamingSwitchRequest) (*present_wall_logic.GetUserNamingSwitchResponse, error)
}

func RegisterPresentWallLogicServer(s *grpc.Server, srv PresentWallLogicServer) {
	s.RegisterService(&_PresentWallLogic_serviceDesc, srv)
}

func _PresentWallLogic_GetUserPresentWall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(present_wall_logic.GetUserPresentWallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWallLogicServer).GetUserPresentWall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.present_wall.PresentWallLogic/GetUserPresentWall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWallLogicServer).GetUserPresentWall(ctx, req.(*present_wall_logic.GetUserPresentWallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWallLogic_GetUserPresentWallMissingItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(present_wall_logic.GetUserPresentWallMissingItemsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWallLogicServer).GetUserPresentWallMissingItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.present_wall.PresentWallLogic/GetUserPresentWallMissingItems",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWallLogicServer).GetUserPresentWallMissingItems(ctx, req.(*present_wall_logic.GetUserPresentWallMissingItemsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWallLogic_GetPresentNamingInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(present_wall_logic.GetPresentNamingInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWallLogicServer).GetPresentNamingInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.present_wall.PresentWallLogic/GetPresentNamingInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWallLogicServer).GetPresentNamingInfo(ctx, req.(*present_wall_logic.GetPresentNamingInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWallLogic_GetPresentWallSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(present_wall_logic.GetPresentWallSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWallLogicServer).GetPresentWallSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.present_wall.PresentWallLogic/GetPresentWallSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWallLogicServer).GetPresentWallSwitch(ctx, req.(*present_wall_logic.GetPresentWallSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWallLogic_SetUserNamingSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(present_wall_logic.SetUserNamingSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWallLogicServer).SetUserNamingSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.present_wall.PresentWallLogic/SetUserNamingSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWallLogicServer).SetUserNamingSwitch(ctx, req.(*present_wall_logic.SetUserNamingSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentWallLogic_GetUserNamingSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(present_wall_logic.GetUserNamingSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentWallLogicServer).GetUserNamingSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.present_wall.PresentWallLogic/GetUserNamingSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentWallLogicServer).GetUserNamingSwitch(ctx, req.(*present_wall_logic.GetUserNamingSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PresentWallLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.present_wall.PresentWallLogic",
	HandlerType: (*PresentWallLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserPresentWall",
			Handler:    _PresentWallLogic_GetUserPresentWall_Handler,
		},
		{
			MethodName: "GetUserPresentWallMissingItems",
			Handler:    _PresentWallLogic_GetUserPresentWallMissingItems_Handler,
		},
		{
			MethodName: "GetPresentNamingInfo",
			Handler:    _PresentWallLogic_GetPresentNamingInfo_Handler,
		},
		{
			MethodName: "GetPresentWallSwitch",
			Handler:    _PresentWallLogic_GetPresentWallSwitch_Handler,
		},
		{
			MethodName: "SetUserNamingSwitch",
			Handler:    _PresentWallLogic_SetUserNamingSwitch_Handler,
		},
		{
			MethodName: "GetUserNamingSwitch",
			Handler:    _PresentWallLogic_GetUserNamingSwitch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/present_wall/grpc_present_wall.proto",
}

func init() {
	proto.RegisterFile("api/present_wall/grpc_present_wall.proto", fileDescriptor_grpc_present_wall_86634d9b3af02eb0)
}

var fileDescriptor_grpc_present_wall_86634d9b3af02eb0 = []byte{
	// 387 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x94, 0x4f, 0x4b, 0xf3, 0x30,
	0x1c, 0xc7, 0x69, 0x9f, 0x87, 0xe7, 0x19, 0x3d, 0x3d, 0x64, 0x0f, 0x1e, 0x8a, 0xca, 0xf0, 0x24,
	0xc8, 0x52, 0xed, 0xf0, 0xe2, 0x9f, 0x8b, 0x1e, 0x8a, 0xa0, 0x32, 0x36, 0x44, 0xd8, 0x65, 0xc4,
	0x12, 0x63, 0x21, 0x4d, 0xb2, 0x26, 0x63, 0x1e, 0xc7, 0x04, 0xc1, 0xbd, 0x0c, 0x2f, 0x5e, 0xfb,
	0x76, 0xfc, 0xfb, 0x5a, 0xa4, 0x6b, 0xc7, 0x52, 0xbb, 0xe1, 0xea, 0xad, 0xc9, 0xf7, 0xfb, 0xc9,
	0xef, 0x43, 0x69, 0x6a, 0x6d, 0x22, 0x11, 0x38, 0x22, 0xc2, 0x12, 0x33, 0xd5, 0x1d, 0x20, 0x4a,
	0x1d, 0x12, 0x09, 0xbf, 0xab, 0xef, 0x40, 0x11, 0x71, 0xc5, 0x41, 0x95, 0x20, 0x88, 0x44, 0x00,
	0xf5, 0xc8, 0xde, 0xd2, 0x57, 0x5d, 0xca, 0x49, 0xe0, 0x3b, 0xc5, 0xad, 0xf4, 0x04, 0x7b, 0x2d,
	0x99, 0x85, 0x6f, 0x15, 0x66, 0x32, 0xe0, 0x6c, 0xf6, 0x94, 0xc6, 0xee, 0xf8, 0xaf, 0xf5, 0xaf,
	0x99, 0xb2, 0x97, 0x88, 0xd2, 0xd3, 0x84, 0x04, 0x77, 0x86, 0x05, 0x3c, 0xac, 0x2e, 0x24, 0x8e,
	0xb4, 0x0c, 0x6c, 0x43, 0x82, 0xe0, 0x9c, 0x41, 0xc5, 0x6a, 0x0b, 0xf7, 0xfa, 0x58, 0x2a, 0x7b,
	0xa7, 0x04, 0x21, 0x05, 0x67, 0x12, 0x6f, 0x54, 0x46, 0xc3, 0xda, 0xef, 0xca, 0x73, 0x6c, 0x82,
	0x27, 0xc3, 0x5a, 0x2f, 0x16, 0xcf, 0x02, 0x29, 0x03, 0x46, 0x4e, 0x14, 0x0e, 0x25, 0x38, 0x58,
	0xfa, 0x7c, 0x1d, 0x9b, 0xda, 0x1d, 0xfe, 0x90, 0xce, 0x99, 0xbe, 0xc4, 0x26, 0x78, 0x30, 0xac,
	0xff, 0x1e, 0x56, 0x19, 0x70, 0x8e, 0xc2, 0xa4, 0xcb, 0xae, 0x39, 0x70, 0x17, 0x4f, 0x28, 0x94,
	0xa7, 0x56, 0x8d, 0x52, 0x4c, 0xce, 0xe5, 0xb5, 0xe0, 0x92, 0xc8, 0xb7, 0x07, 0x81, 0xf2, 0x6f,
	0x96, 0x70, 0x99, 0x95, 0x97, 0x77, 0xd1, 0x99, 0x9c, 0xcb, 0x5b, 0x6c, 0x82, 0x7b, 0xc3, 0xaa,
	0xb6, 0xd3, 0x97, 0x99, 0x3a, 0x67, 0x2a, 0x8b, 0x3e, 0x8b, 0x39, 0xdd, 0xa9, 0x89, 0x5b, 0x06,
	0xc9, 0x89, 0xbc, 0x67, 0x22, 0x5e, 0x09, 0x11, 0xaf, 0xbc, 0x88, 0xf7, 0xad, 0xc8, 0x47, 0x6c,
	0xda, 0xab, 0xa3, 0x61, 0x0d, 0x64, 0x74, 0x3d, 0xa1, 0xeb, 0x13, 0x7a, 0x3c, 0xac, 0x99, 0x84,
	0x1f, 0x75, 0xac, 0x15, 0x9f, 0x87, 0xb0, 0xd7, 0x1f, 0x20, 0x06, 0x95, 0x4a, 0xaf, 0x68, 0x72,
	0xff, 0x3b, 0x7b, 0x84, 0x53, 0xc4, 0x08, 0xdc, 0x75, 0x95, 0x82, 0x3e, 0x0f, 0x9d, 0x49, 0xe4,
	0x73, 0xea, 0x20, 0x21, 0x9c, 0xaf, 0xbf, 0x93, 0x7d, 0x7d, 0xf1, 0x68, 0xfe, 0x6a, 0x35, 0x8f,
	0xaf, 0xfe, 0x4c, 0x88, 0xc6, 0x67, 0x00, 0x00, 0x00, 0xff, 0xff, 0xd6, 0xd2, 0x4d, 0xe5, 0x7c,
	0x04, 0x00, 0x00,
}
