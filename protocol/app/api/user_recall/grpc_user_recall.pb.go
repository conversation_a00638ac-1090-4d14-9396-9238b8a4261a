// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/user_recall/grpc_user_recall.proto

package user_recall // import "golang.52tt.com/protocol/app/api/user_recall"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import user_recall "golang.52tt.com/protocol/app/user_recall"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserRecallLogicClient is the client API for UserRecallLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserRecallLogicClient interface {
	// 检查是否是召回用户
	CheckIsRecall(ctx context.Context, in *user_recall.CheckIsRecallRequest, opts ...grpc.CallOption) (*user_recall.CheckIsRecallResponse, error)
	// 召回用户领奖
	UserRecallGetPrize(ctx context.Context, in *user_recall.UserRecallGetPrizeRequest, opts ...grpc.CallOption) (*user_recall.UserRecallGetPrizeResponse, error)
	GetUserRecallList(ctx context.Context, in *user_recall.GetUserRecallListRequest, opts ...grpc.CallOption) (*user_recall.GetUserRecallListResponse, error)
	SendRecall(ctx context.Context, in *user_recall.SendRecallRequest, opts ...grpc.CallOption) (*user_recall.SendRecallResponse, error)
}

type userRecallLogicClient struct {
	cc *grpc.ClientConn
}

func NewUserRecallLogicClient(cc *grpc.ClientConn) UserRecallLogicClient {
	return &userRecallLogicClient{cc}
}

func (c *userRecallLogicClient) CheckIsRecall(ctx context.Context, in *user_recall.CheckIsRecallRequest, opts ...grpc.CallOption) (*user_recall.CheckIsRecallResponse, error) {
	out := new(user_recall.CheckIsRecallResponse)
	err := c.cc.Invoke(ctx, "/ga.api.user_recall.UserRecallLogic/CheckIsRecall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallLogicClient) UserRecallGetPrize(ctx context.Context, in *user_recall.UserRecallGetPrizeRequest, opts ...grpc.CallOption) (*user_recall.UserRecallGetPrizeResponse, error) {
	out := new(user_recall.UserRecallGetPrizeResponse)
	err := c.cc.Invoke(ctx, "/ga.api.user_recall.UserRecallLogic/UserRecallGetPrize", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallLogicClient) GetUserRecallList(ctx context.Context, in *user_recall.GetUserRecallListRequest, opts ...grpc.CallOption) (*user_recall.GetUserRecallListResponse, error) {
	out := new(user_recall.GetUserRecallListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.user_recall.UserRecallLogic/GetUserRecallList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallLogicClient) SendRecall(ctx context.Context, in *user_recall.SendRecallRequest, opts ...grpc.CallOption) (*user_recall.SendRecallResponse, error) {
	out := new(user_recall.SendRecallResponse)
	err := c.cc.Invoke(ctx, "/ga.api.user_recall.UserRecallLogic/SendRecall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserRecallLogicServer is the server API for UserRecallLogic service.
type UserRecallLogicServer interface {
	// 检查是否是召回用户
	CheckIsRecall(context.Context, *user_recall.CheckIsRecallRequest) (*user_recall.CheckIsRecallResponse, error)
	// 召回用户领奖
	UserRecallGetPrize(context.Context, *user_recall.UserRecallGetPrizeRequest) (*user_recall.UserRecallGetPrizeResponse, error)
	GetUserRecallList(context.Context, *user_recall.GetUserRecallListRequest) (*user_recall.GetUserRecallListResponse, error)
	SendRecall(context.Context, *user_recall.SendRecallRequest) (*user_recall.SendRecallResponse, error)
}

func RegisterUserRecallLogicServer(s *grpc.Server, srv UserRecallLogicServer) {
	s.RegisterService(&_UserRecallLogic_serviceDesc, srv)
}

func _UserRecallLogic_CheckIsRecall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(user_recall.CheckIsRecallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallLogicServer).CheckIsRecall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.user_recall.UserRecallLogic/CheckIsRecall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallLogicServer).CheckIsRecall(ctx, req.(*user_recall.CheckIsRecallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallLogic_UserRecallGetPrize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(user_recall.UserRecallGetPrizeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallLogicServer).UserRecallGetPrize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.user_recall.UserRecallLogic/UserRecallGetPrize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallLogicServer).UserRecallGetPrize(ctx, req.(*user_recall.UserRecallGetPrizeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallLogic_GetUserRecallList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(user_recall.GetUserRecallListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallLogicServer).GetUserRecallList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.user_recall.UserRecallLogic/GetUserRecallList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallLogicServer).GetUserRecallList(ctx, req.(*user_recall.GetUserRecallListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallLogic_SendRecall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(user_recall.SendRecallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallLogicServer).SendRecall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.user_recall.UserRecallLogic/SendRecall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallLogicServer).SendRecall(ctx, req.(*user_recall.SendRecallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserRecallLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.user_recall.UserRecallLogic",
	HandlerType: (*UserRecallLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckIsRecall",
			Handler:    _UserRecallLogic_CheckIsRecall_Handler,
		},
		{
			MethodName: "UserRecallGetPrize",
			Handler:    _UserRecallLogic_UserRecallGetPrize_Handler,
		},
		{
			MethodName: "GetUserRecallList",
			Handler:    _UserRecallLogic_GetUserRecallList_Handler,
		},
		{
			MethodName: "SendRecall",
			Handler:    _UserRecallLogic_SendRecall_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/user_recall/grpc_user_recall.proto",
}

func init() {
	proto.RegisterFile("api/user_recall/grpc_user_recall.proto", fileDescriptor_grpc_user_recall_a1fa39435f93eee0)
}

var fileDescriptor_grpc_user_recall_a1fa39435f93eee0 = []byte{
	// 331 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x52, 0x4b, 0x2c, 0xc8, 0xd4,
	0x2f, 0x2d, 0x4e, 0x2d, 0x8a, 0x2f, 0x4a, 0x4d, 0x4e, 0xcc, 0xc9, 0xd1, 0x4f, 0x2f, 0x2a, 0x48,
	0x8e, 0x47, 0x12, 0xd0, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x12, 0x4a, 0x4f, 0xd4, 0x4b, 0x2c,
	0xc8, 0xd4, 0x43, 0x92, 0x91, 0x92, 0x45, 0xd6, 0x87, 0xa1, 0x45, 0x4a, 0x16, 0x64, 0x74, 0x6a,
	0x45, 0x49, 0x6a, 0x5e, 0x71, 0x66, 0x7e, 0x1e, 0x82, 0x05, 0x91, 0x36, 0x7a, 0xc0, 0xcc, 0xc5,
	0x1f, 0x5a, 0x9c, 0x5a, 0x14, 0x04, 0xd6, 0xe3, 0x93, 0x9f, 0x9e, 0x99, 0x2c, 0x94, 0xc6, 0xc5,
	0xeb, 0x9c, 0x91, 0x9a, 0x9c, 0xed, 0x59, 0x0c, 0x11, 0x15, 0x52, 0xd1, 0x4b, 0x4f, 0x44, 0xb6,
	0x53, 0x0f, 0x45, 0x3a, 0x28, 0xb5, 0xb0, 0x34, 0xb5, 0xb8, 0x44, 0x4a, 0x95, 0x80, 0xaa, 0xe2,
	0x82, 0xfc, 0xbc, 0xe2, 0x54, 0x25, 0x8e, 0xa6, 0x06, 0x05, 0x16, 0x8e, 0xd9, 0x7d, 0xcc, 0x42,
	0xa5, 0x5c, 0x42, 0x08, 0xab, 0xdd, 0x53, 0x4b, 0x02, 0x8a, 0x32, 0xab, 0x52, 0x85, 0x34, 0xd1,
	0x8d, 0xc1, 0x54, 0x03, 0xb3, 0x51, 0x8b, 0x18, 0xa5, 0x28, 0xd6, 0xce, 0xe9, 0x63, 0x16, 0x2a,
	0xe2, 0x12, 0x74, 0x4f, 0x2d, 0x41, 0xf2, 0x74, 0x66, 0x71, 0x89, 0x90, 0x06, 0xba, 0x51, 0x18,
	0x4a, 0x60, 0x96, 0x6a, 0x12, 0xa1, 0x12, 0xc5, 0xce, 0x79, 0x7d, 0xcc, 0x42, 0xb1, 0x5c, 0x5c,
	0xc1, 0xa9, 0x79, 0x29, 0xd0, 0xf0, 0x54, 0x44, 0x37, 0x02, 0x21, 0x07, 0xb3, 0x45, 0x09, 0x9f,
	0x12, 0x14, 0xe3, 0xe7, 0xf7, 0x31, 0x4b, 0x49, 0x37, 0x35, 0x28, 0x08, 0x82, 0x54, 0xeb, 0x42,
	0x54, 0xeb, 0xe6, 0x80, 0x22, 0xb2, 0xab, 0x41, 0x81, 0x29, 0x3d, 0xdf, 0x29, 0x82, 0x4b, 0x2c,
	0x39, 0x3f, 0x57, 0xaf, 0xb0, 0xb4, 0x3c, 0x31, 0x4f, 0xaf, 0xa4, 0x04, 0x12, 0xf1, 0xa0, 0x64,
	0x14, 0x65, 0x91, 0x9e, 0x9f, 0x93, 0x98, 0x97, 0xae, 0x67, 0x6a, 0x54, 0x52, 0xa2, 0x97, 0x9c,
	0x9f, 0xab, 0x0f, 0x96, 0x4a, 0xce, 0xcf, 0xd1, 0x4f, 0x2c, 0x28, 0xd0, 0x47, 0x4b, 0x93, 0xd6,
	0x48, 0xec, 0x45, 0x4c, 0xcc, 0x41, 0x01, 0xce, 0x49, 0x6c, 0x60, 0xf5, 0xc6, 0x80, 0x00, 0x00,
	0x00, 0xff, 0xff, 0xcd, 0x4a, 0x32, 0x2c, 0xbf, 0x02, 0x00, 0x00,
}
