// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/mijing_label/grpc_mijing_label.proto

package mijing_label // import "golang.52tt.com/protocol/app/api/mijing_label"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import mijing_label_logic "golang.52tt.com/protocol/app/mijing-label-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MijingLabelLogicClient is the client API for MijingLabelLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MijingLabelLogicClient interface {
	// 获取密逃用户标签
	MijingGetEscapeUserLabelList(ctx context.Context, in *mijing_label_logic.MijingGetEscapeUserLabelListRequest, opts ...grpc.CallOption) (*mijing_label_logic.MijingGetEscapeUserLabelListResponse, error)
}

type mijingLabelLogicClient struct {
	cc *grpc.ClientConn
}

func NewMijingLabelLogicClient(cc *grpc.ClientConn) MijingLabelLogicClient {
	return &mijingLabelLogicClient{cc}
}

func (c *mijingLabelLogicClient) MijingGetEscapeUserLabelList(ctx context.Context, in *mijing_label_logic.MijingGetEscapeUserLabelListRequest, opts ...grpc.CallOption) (*mijing_label_logic.MijingGetEscapeUserLabelListResponse, error) {
	out := new(mijing_label_logic.MijingGetEscapeUserLabelListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.mijing_label.MijingLabelLogic/MijingGetEscapeUserLabelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MijingLabelLogicServer is the server API for MijingLabelLogic service.
type MijingLabelLogicServer interface {
	// 获取密逃用户标签
	MijingGetEscapeUserLabelList(context.Context, *mijing_label_logic.MijingGetEscapeUserLabelListRequest) (*mijing_label_logic.MijingGetEscapeUserLabelListResponse, error)
}

func RegisterMijingLabelLogicServer(s *grpc.Server, srv MijingLabelLogicServer) {
	s.RegisterService(&_MijingLabelLogic_serviceDesc, srv)
}

func _MijingLabelLogic_MijingGetEscapeUserLabelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mijing_label_logic.MijingGetEscapeUserLabelListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingLabelLogicServer).MijingGetEscapeUserLabelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mijing_label.MijingLabelLogic/MijingGetEscapeUserLabelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingLabelLogicServer).MijingGetEscapeUserLabelList(ctx, req.(*mijing_label_logic.MijingGetEscapeUserLabelListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MijingLabelLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.mijing_label.MijingLabelLogic",
	HandlerType: (*MijingLabelLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MijingGetEscapeUserLabelList",
			Handler:    _MijingLabelLogic_MijingGetEscapeUserLabelList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/mijing_label/grpc_mijing_label.proto",
}

func init() {
	proto.RegisterFile("api/mijing_label/grpc_mijing_label.proto", fileDescriptor_grpc_mijing_label_7ca60b3cbf2c5eb1)
}

var fileDescriptor_grpc_mijing_label_7ca60b3cbf2c5eb1 = []byte{
	// 261 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xd2, 0x48, 0x2c, 0xc8, 0xd4,
	0xcf, 0xcd, 0xcc, 0xca, 0xcc, 0x4b, 0x8f, 0xcf, 0x49, 0x4c, 0x4a, 0xcd, 0xd1, 0x4f, 0x2f, 0x2a,
	0x48, 0x8e, 0x47, 0x16, 0xd1, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x12, 0x4e, 0x4f, 0xd4, 0x4b,
	0x2c, 0xc8, 0xd4, 0x43, 0x96, 0x92, 0x92, 0x05, 0x69, 0x4f, 0xad, 0x28, 0x49, 0xcd, 0x2b, 0xce,
	0xcc, 0xcf, 0x43, 0xb0, 0x20, 0x7a, 0xa4, 0xb4, 0x91, 0x15, 0xc7, 0xe7, 0xe4, 0xa7, 0x67, 0x26,
	0xeb, 0x63, 0x0a, 0x41, 0x14, 0x1b, 0x5d, 0x61, 0xe4, 0x12, 0xf0, 0x05, 0x4b, 0xfa, 0x80, 0xe4,
	0x7c, 0x40, 0x52, 0x42, 0x0b, 0x19, 0xb9, 0x64, 0x20, 0x82, 0xee, 0xa9, 0x25, 0xae, 0xc5, 0xc9,
	0x89, 0x05, 0xa9, 0xa1, 0xc5, 0xa9, 0x45, 0x10, 0x15, 0x99, 0xc5, 0x25, 0x42, 0x56, 0x7a, 0xe9,
	0x89, 0x7a, 0x58, 0xcc, 0xc4, 0xa7, 0x29, 0x28, 0xb5, 0xb0, 0x34, 0xb5, 0xb8, 0x44, 0xca, 0x9a,
	0x2c, 0xbd, 0xc5, 0x05, 0xf9, 0x79, 0xc5, 0xa9, 0x4a, 0x1c, 0x4d, 0x0d, 0x0a, 0x2c, 0x1c, 0x07,
	0xe6, 0x33, 0x49, 0xc9, 0x34, 0x35, 0x28, 0x08, 0x41, 0x8c, 0xd1, 0x05, 0x1b, 0xa3, 0x0b, 0x36,
	0xa6, 0xab, 0x41, 0x81, 0x29, 0x3d, 0xdf, 0x29, 0x8a, 0x4b, 0x2c, 0x39, 0x3f, 0x57, 0xaf, 0xb0,
	0xb4, 0x3c, 0x31, 0x4f, 0xaf, 0xa4, 0x04, 0xe2, 0x59, 0x50, 0x48, 0x46, 0x59, 0xa5, 0xe7, 0xe7,
	0x24, 0xe6, 0xa5, 0xeb, 0x99, 0x1a, 0x95, 0x94, 0xe8, 0x25, 0xe7, 0xe7, 0xea, 0x83, 0xa5, 0x92,
	0xf3, 0x73, 0xf4, 0x13, 0x0b, 0x0a, 0xf4, 0xd1, 0x23, 0xc6, 0x1a, 0x99, 0xb3, 0x88, 0x89, 0x39,
	0x28, 0xc0, 0x39, 0x89, 0x0d, 0xac, 0xc3, 0x18, 0x10, 0x00, 0x00, 0xff, 0xff, 0x28, 0x45, 0x47,
	0xa7, 0xc6, 0x01, 0x00, 0x00,
}
