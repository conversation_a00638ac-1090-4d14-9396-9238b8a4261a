// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/game_pal/grpc_game_pal.proto

package game_pal // import "golang.52tt.com/protocol/app/api/game_pal"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import game_pal_logic "golang.52tt.com/protocol/app/game-pal-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GamePalLogicClient is the client API for GamePalLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GamePalLogicClient interface {
	// 获取游戏搭子的切换游戏玩法列表
	CheckPublishCondition(ctx context.Context, in *game_pal_logic.CheckPublishConditionRequest, opts ...grpc.CallOption) (*game_pal_logic.CheckPublishConditionResponse, error)
	// 获取游戏搭子卡属性
	GetGamePalCardProps(ctx context.Context, in *game_pal_logic.GetGamePalCardPropsRequest, opts ...grpc.CallOption) (*game_pal_logic.GetGamePalCardPropsResponse, error)
	// 发布游戏搭子卡
	PublishGamePalCard(ctx context.Context, in *game_pal_logic.PublishGamePalCardRequest, opts ...grpc.CallOption) (*game_pal_logic.PublishGamePalCardResponse, error)
	// 点亮游戏搭子卡
	LightenGamePalCard(ctx context.Context, in *game_pal_logic.LightenGamePalCardRequest, opts ...grpc.CallOption) (*game_pal_logic.LightenGamePalCardResponse, error)
	// 获取用户所有游戏主题搭子卡列表
	GetGamePalCardList(ctx context.Context, in *game_pal_logic.GetGamePalCardListRequest, opts ...grpc.CallOption) (*game_pal_logic.GetGamePalCardListResponse, error)
	// 获取用户单个游戏主题搭子卡
	GetGamePalCard(ctx context.Context, in *game_pal_logic.GetGamePalCardRequest, opts ...grpc.CallOption) (*game_pal_logic.GetGamePalCardResponse, error)
	// 删除游戏搭子卡
	DeleteGamePalCard(ctx context.Context, in *game_pal_logic.DeleteGamePalCardRequest, opts ...grpc.CallOption) (*game_pal_logic.DeleteGamePalCardResponse, error)
	// 获取搭子卡筛选项
	GetGamePalFilterByTabId(ctx context.Context, in *game_pal_logic.GetGamePalFilterByTabIdRequest, opts ...grpc.CallOption) (*game_pal_logic.GetGamePalFilterByTabIdResponse, error)
	// 获取搭子卡筛选项
	GetGamePalList(ctx context.Context, in *game_pal_logic.GetGamePalListReq, opts ...grpc.CallOption) (*game_pal_logic.GetGamePalListResp, error)
}

type gamePalLogicClient struct {
	cc *grpc.ClientConn
}

func NewGamePalLogicClient(cc *grpc.ClientConn) GamePalLogicClient {
	return &gamePalLogicClient{cc}
}

func (c *gamePalLogicClient) CheckPublishCondition(ctx context.Context, in *game_pal_logic.CheckPublishConditionRequest, opts ...grpc.CallOption) (*game_pal_logic.CheckPublishConditionResponse, error) {
	out := new(game_pal_logic.CheckPublishConditionResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_pal.GamePalLogic/CheckPublishCondition", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalLogicClient) GetGamePalCardProps(ctx context.Context, in *game_pal_logic.GetGamePalCardPropsRequest, opts ...grpc.CallOption) (*game_pal_logic.GetGamePalCardPropsResponse, error) {
	out := new(game_pal_logic.GetGamePalCardPropsResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_pal.GamePalLogic/GetGamePalCardProps", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalLogicClient) PublishGamePalCard(ctx context.Context, in *game_pal_logic.PublishGamePalCardRequest, opts ...grpc.CallOption) (*game_pal_logic.PublishGamePalCardResponse, error) {
	out := new(game_pal_logic.PublishGamePalCardResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_pal.GamePalLogic/PublishGamePalCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalLogicClient) LightenGamePalCard(ctx context.Context, in *game_pal_logic.LightenGamePalCardRequest, opts ...grpc.CallOption) (*game_pal_logic.LightenGamePalCardResponse, error) {
	out := new(game_pal_logic.LightenGamePalCardResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_pal.GamePalLogic/LightenGamePalCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalLogicClient) GetGamePalCardList(ctx context.Context, in *game_pal_logic.GetGamePalCardListRequest, opts ...grpc.CallOption) (*game_pal_logic.GetGamePalCardListResponse, error) {
	out := new(game_pal_logic.GetGamePalCardListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_pal.GamePalLogic/GetGamePalCardList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalLogicClient) GetGamePalCard(ctx context.Context, in *game_pal_logic.GetGamePalCardRequest, opts ...grpc.CallOption) (*game_pal_logic.GetGamePalCardResponse, error) {
	out := new(game_pal_logic.GetGamePalCardResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_pal.GamePalLogic/GetGamePalCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalLogicClient) DeleteGamePalCard(ctx context.Context, in *game_pal_logic.DeleteGamePalCardRequest, opts ...grpc.CallOption) (*game_pal_logic.DeleteGamePalCardResponse, error) {
	out := new(game_pal_logic.DeleteGamePalCardResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_pal.GamePalLogic/DeleteGamePalCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalLogicClient) GetGamePalFilterByTabId(ctx context.Context, in *game_pal_logic.GetGamePalFilterByTabIdRequest, opts ...grpc.CallOption) (*game_pal_logic.GetGamePalFilterByTabIdResponse, error) {
	out := new(game_pal_logic.GetGamePalFilterByTabIdResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_pal.GamePalLogic/GetGamePalFilterByTabId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePalLogicClient) GetGamePalList(ctx context.Context, in *game_pal_logic.GetGamePalListReq, opts ...grpc.CallOption) (*game_pal_logic.GetGamePalListResp, error) {
	out := new(game_pal_logic.GetGamePalListResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_pal.GamePalLogic/GetGamePalList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GamePalLogicServer is the server API for GamePalLogic service.
type GamePalLogicServer interface {
	// 获取游戏搭子的切换游戏玩法列表
	CheckPublishCondition(context.Context, *game_pal_logic.CheckPublishConditionRequest) (*game_pal_logic.CheckPublishConditionResponse, error)
	// 获取游戏搭子卡属性
	GetGamePalCardProps(context.Context, *game_pal_logic.GetGamePalCardPropsRequest) (*game_pal_logic.GetGamePalCardPropsResponse, error)
	// 发布游戏搭子卡
	PublishGamePalCard(context.Context, *game_pal_logic.PublishGamePalCardRequest) (*game_pal_logic.PublishGamePalCardResponse, error)
	// 点亮游戏搭子卡
	LightenGamePalCard(context.Context, *game_pal_logic.LightenGamePalCardRequest) (*game_pal_logic.LightenGamePalCardResponse, error)
	// 获取用户所有游戏主题搭子卡列表
	GetGamePalCardList(context.Context, *game_pal_logic.GetGamePalCardListRequest) (*game_pal_logic.GetGamePalCardListResponse, error)
	// 获取用户单个游戏主题搭子卡
	GetGamePalCard(context.Context, *game_pal_logic.GetGamePalCardRequest) (*game_pal_logic.GetGamePalCardResponse, error)
	// 删除游戏搭子卡
	DeleteGamePalCard(context.Context, *game_pal_logic.DeleteGamePalCardRequest) (*game_pal_logic.DeleteGamePalCardResponse, error)
	// 获取搭子卡筛选项
	GetGamePalFilterByTabId(context.Context, *game_pal_logic.GetGamePalFilterByTabIdRequest) (*game_pal_logic.GetGamePalFilterByTabIdResponse, error)
	// 获取搭子卡筛选项
	GetGamePalList(context.Context, *game_pal_logic.GetGamePalListReq) (*game_pal_logic.GetGamePalListResp, error)
}

func RegisterGamePalLogicServer(s *grpc.Server, srv GamePalLogicServer) {
	s.RegisterService(&_GamePalLogic_serviceDesc, srv)
}

func _GamePalLogic_CheckPublishCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_pal_logic.CheckPublishConditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalLogicServer).CheckPublishCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_pal.GamePalLogic/CheckPublishCondition",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalLogicServer).CheckPublishCondition(ctx, req.(*game_pal_logic.CheckPublishConditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePalLogic_GetGamePalCardProps_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_pal_logic.GetGamePalCardPropsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalLogicServer).GetGamePalCardProps(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_pal.GamePalLogic/GetGamePalCardProps",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalLogicServer).GetGamePalCardProps(ctx, req.(*game_pal_logic.GetGamePalCardPropsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePalLogic_PublishGamePalCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_pal_logic.PublishGamePalCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalLogicServer).PublishGamePalCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_pal.GamePalLogic/PublishGamePalCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalLogicServer).PublishGamePalCard(ctx, req.(*game_pal_logic.PublishGamePalCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePalLogic_LightenGamePalCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_pal_logic.LightenGamePalCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalLogicServer).LightenGamePalCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_pal.GamePalLogic/LightenGamePalCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalLogicServer).LightenGamePalCard(ctx, req.(*game_pal_logic.LightenGamePalCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePalLogic_GetGamePalCardList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_pal_logic.GetGamePalCardListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalLogicServer).GetGamePalCardList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_pal.GamePalLogic/GetGamePalCardList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalLogicServer).GetGamePalCardList(ctx, req.(*game_pal_logic.GetGamePalCardListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePalLogic_GetGamePalCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_pal_logic.GetGamePalCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalLogicServer).GetGamePalCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_pal.GamePalLogic/GetGamePalCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalLogicServer).GetGamePalCard(ctx, req.(*game_pal_logic.GetGamePalCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePalLogic_DeleteGamePalCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_pal_logic.DeleteGamePalCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalLogicServer).DeleteGamePalCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_pal.GamePalLogic/DeleteGamePalCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalLogicServer).DeleteGamePalCard(ctx, req.(*game_pal_logic.DeleteGamePalCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePalLogic_GetGamePalFilterByTabId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_pal_logic.GetGamePalFilterByTabIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalLogicServer).GetGamePalFilterByTabId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_pal.GamePalLogic/GetGamePalFilterByTabId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalLogicServer).GetGamePalFilterByTabId(ctx, req.(*game_pal_logic.GetGamePalFilterByTabIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePalLogic_GetGamePalList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_pal_logic.GetGamePalListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePalLogicServer).GetGamePalList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_pal.GamePalLogic/GetGamePalList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePalLogicServer).GetGamePalList(ctx, req.(*game_pal_logic.GetGamePalListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GamePalLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.game_pal.GamePalLogic",
	HandlerType: (*GamePalLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckPublishCondition",
			Handler:    _GamePalLogic_CheckPublishCondition_Handler,
		},
		{
			MethodName: "GetGamePalCardProps",
			Handler:    _GamePalLogic_GetGamePalCardProps_Handler,
		},
		{
			MethodName: "PublishGamePalCard",
			Handler:    _GamePalLogic_PublishGamePalCard_Handler,
		},
		{
			MethodName: "LightenGamePalCard",
			Handler:    _GamePalLogic_LightenGamePalCard_Handler,
		},
		{
			MethodName: "GetGamePalCardList",
			Handler:    _GamePalLogic_GetGamePalCardList_Handler,
		},
		{
			MethodName: "GetGamePalCard",
			Handler:    _GamePalLogic_GetGamePalCard_Handler,
		},
		{
			MethodName: "DeleteGamePalCard",
			Handler:    _GamePalLogic_DeleteGamePalCard_Handler,
		},
		{
			MethodName: "GetGamePalFilterByTabId",
			Handler:    _GamePalLogic_GetGamePalFilterByTabId_Handler,
		},
		{
			MethodName: "GetGamePalList",
			Handler:    _GamePalLogic_GetGamePalList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/game_pal/grpc_game_pal.proto",
}

func init() {
	proto.RegisterFile("api/game_pal/grpc_game_pal.proto", fileDescriptor_grpc_game_pal_7e2ffe89b711d661)
}

var fileDescriptor_grpc_game_pal_7e2ffe89b711d661 = []byte{
	// 451 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x94, 0x4d, 0x8b, 0xd4, 0x30,
	0x18, 0xc7, 0xd9, 0x5d, 0xd0, 0x25, 0xc8, 0x8a, 0x11, 0x5f, 0x28, 0x48, 0x07, 0x5f, 0x40, 0x71,
	0x9b, 0x6a, 0xc5, 0x93, 0xb7, 0xad, 0xb8, 0x08, 0x73, 0x28, 0x83, 0x27, 0x2f, 0x43, 0xa6, 0x13,
	0x33, 0xc1, 0x34, 0xc9, 0x36, 0x19, 0x56, 0x05, 0x61, 0x58, 0x4f, 0xfa, 0x31, 0xfc, 0x4a, 0xbe,
	0xe0, 0xdb, 0x67, 0x11, 0x69, 0xa7, 0x61, 0x1b, 0x1b, 0x3a, 0xdd, 0x5b, 0xd2, 0xfc, 0x9e, 0xe7,
	0xf7, 0x6f, 0x02, 0x0f, 0x18, 0x61, 0xc5, 0x62, 0x8a, 0x0b, 0x32, 0x55, 0x98, 0xc7, 0xb4, 0x54,
	0xf9, 0xd4, 0xee, 0x90, 0x2a, 0xa5, 0x91, 0xf0, 0x22, 0xc5, 0x08, 0x2b, 0x86, 0xec, 0xe7, 0xe0,
	0x96, 0x5d, 0x4d, 0xb9, 0xa4, 0x2c, 0x8f, 0xdd, 0xed, 0xba, 0x2a, 0xb8, 0x51, 0xf5, 0x25, 0x6f,
	0x0c, 0x11, 0x9a, 0x49, 0x71, 0xba, 0x5a, 0x1f, 0x27, 0x7f, 0x77, 0xc1, 0x85, 0x43, 0x5c, 0x90,
	0x0c, 0xf3, 0x71, 0x55, 0x05, 0x3f, 0x6c, 0x81, 0x2b, 0xe9, 0x82, 0xe4, 0xaf, 0xb3, 0xe5, 0x8c,
	0x33, 0xbd, 0x48, 0xa5, 0x98, 0x33, 0xc3, 0xa4, 0x80, 0x31, 0xa2, 0x18, 0xfd, 0xe7, 0xf0, 0x92,
	0x13, 0x72, 0xb4, 0x24, 0xda, 0x04, 0x0f, 0x86, 0x17, 0x68, 0x25, 0x85, 0x26, 0x37, 0xcf, 0x9f,
	0xac, 0x46, 0x3b, 0xbb, 0x5f, 0x42, 0xf8, 0x1e, 0x5c, 0x3e, 0x24, 0xa6, 0x09, 0x96, 0xe2, 0x72,
	0x9e, 0x95, 0x52, 0x69, 0x18, 0x79, 0x3a, 0x7a, 0x38, 0x1b, 0x00, 0x0d, 0xc5, 0x1d, 0xfd, 0xd7,
	0x10, 0xbe, 0x03, 0xb0, 0xc9, 0xd8, 0x62, 0xe1, 0xbe, 0xa7, 0x5d, 0x17, 0xb3, 0xf2, 0x68, 0x20,
	0xed, 0xb8, 0xbf, 0xd5, 0xee, 0x31, 0xa3, 0x0b, 0x43, 0xc4, 0x26, 0x77, 0x17, 0xeb, 0x73, 0xfb,
	0x68, 0xc7, 0xfd, 0xbd, 0x76, 0xbb, 0xf7, 0x33, 0x66, 0xda, 0x78, 0xdd, 0x5d, 0xac, 0xcf, 0xed,
	0xa3, 0x1d, 0xf7, 0x8f, 0x10, 0x0a, 0xb0, 0xe7, 0x62, 0xf0, 0xee, 0xc6, 0x4e, 0xd6, 0x79, 0x6f,
	0x00, 0xe9, 0xf8, 0x7e, 0x86, 0xf0, 0x18, 0x5c, 0x7a, 0x4a, 0x38, 0x31, 0xa4, 0xad, 0xbc, 0xef,
	0x69, 0xd4, 0xa1, 0xac, 0x75, 0x7f, 0x18, 0xec, 0x88, 0x7f, 0x85, 0xf0, 0xe3, 0x16, 0xb8, 0x76,
	0x1a, 0xee, 0x19, 0xe3, 0x86, 0x94, 0x07, 0x6f, 0x5f, 0xe0, 0xd9, 0xf3, 0x39, 0x7c, 0xd8, 0xfb,
	0x23, 0x0e, 0x6b, 0x53, 0x24, 0x67, 0x29, 0x71, 0xb2, 0xfc, 0x0e, 0xe1, 0xab, 0xf6, 0xa5, 0xd7,
	0x8f, 0x7d, 0xbb, 0xb7, 0x5d, 0xf3, 0xd0, 0xc1, 0x9d, 0x01, 0x94, 0x56, 0x8d, 0xe7, 0x4f, 0x18,
	0x5c, 0x3f, 0x59, 0x8d, 0xf6, 0x2a, 0x3e, 0x52, 0x98, 0x47, 0x35, 0xff, 0x69, 0x35, 0xda, 0xa6,
	0xf2, 0x60, 0x02, 0xae, 0xe6, 0xb2, 0x40, 0x47, 0xcb, 0x63, 0x2c, 0x90, 0x31, 0xeb, 0xb1, 0x54,
	0xcd, 0xb9, 0x97, 0x09, 0x95, 0x1c, 0x0b, 0x8a, 0x1e, 0x27, 0xc6, 0xa0, 0x5c, 0x16, 0x71, 0x7d,
	0x94, 0x4b, 0x1e, 0x63, 0xa5, 0xe2, 0xf6, 0xb8, 0x7c, 0x62, 0x17, 0x9f, 0xb7, 0x77, 0x26, 0x59,
	0x3a, 0x3b, 0x57, 0x93, 0x8f, 0xfe, 0x05, 0x00, 0x00, 0xff, 0xff, 0x53, 0xa1, 0xd0, 0x55, 0x54,
	0x05, 0x00, 0x00,
}
