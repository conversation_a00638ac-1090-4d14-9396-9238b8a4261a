// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/channelguild_go/grpc_channelguild_go.proto

package channelguild_go_logic // import "golang.52tt.com/protocol/app/api/channelguild-go-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import channel "golang.52tt.com/protocol/app/channel"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelGuildGoLogicClient is the client API for ChannelGuildGoLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelGuildGoLogicClient interface {
	ChannelGuildGetListSummary(ctx context.Context, in *channel.ChannelGuildGetListSummaryReq, opts ...grpc.CallOption) (*channel.ChannelGuildGetListSummaryResp, error)
	ChannelGuildGetListByType(ctx context.Context, in *channel.ChannelGuildGetListByTypeReq, opts ...grpc.CallOption) (*channel.ChannelGuildGetListByTypeResp, error)
	ChannelGuildGetTotalMemberCount(ctx context.Context, in *channel.ChannelGuildGetTotalMemberCountReq, opts ...grpc.CallOption) (*channel.ChannelGuildGetTotalMemberCountResp, error)
}

type channelGuildGoLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelGuildGoLogicClient(cc *grpc.ClientConn) ChannelGuildGoLogicClient {
	return &channelGuildGoLogicClient{cc}
}

func (c *channelGuildGoLogicClient) ChannelGuildGetListSummary(ctx context.Context, in *channel.ChannelGuildGetListSummaryReq, opts ...grpc.CallOption) (*channel.ChannelGuildGetListSummaryResp, error) {
	out := new(channel.ChannelGuildGetListSummaryResp)
	err := c.cc.Invoke(ctx, "/ga.api.channelguild_go.ChannelGuildGoLogic/ChannelGuildGetListSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuildGoLogicClient) ChannelGuildGetListByType(ctx context.Context, in *channel.ChannelGuildGetListByTypeReq, opts ...grpc.CallOption) (*channel.ChannelGuildGetListByTypeResp, error) {
	out := new(channel.ChannelGuildGetListByTypeResp)
	err := c.cc.Invoke(ctx, "/ga.api.channelguild_go.ChannelGuildGoLogic/ChannelGuildGetListByType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuildGoLogicClient) ChannelGuildGetTotalMemberCount(ctx context.Context, in *channel.ChannelGuildGetTotalMemberCountReq, opts ...grpc.CallOption) (*channel.ChannelGuildGetTotalMemberCountResp, error) {
	out := new(channel.ChannelGuildGetTotalMemberCountResp)
	err := c.cc.Invoke(ctx, "/ga.api.channelguild_go.ChannelGuildGoLogic/ChannelGuildGetTotalMemberCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelGuildGoLogicServer is the server API for ChannelGuildGoLogic service.
type ChannelGuildGoLogicServer interface {
	ChannelGuildGetListSummary(context.Context, *channel.ChannelGuildGetListSummaryReq) (*channel.ChannelGuildGetListSummaryResp, error)
	ChannelGuildGetListByType(context.Context, *channel.ChannelGuildGetListByTypeReq) (*channel.ChannelGuildGetListByTypeResp, error)
	ChannelGuildGetTotalMemberCount(context.Context, *channel.ChannelGuildGetTotalMemberCountReq) (*channel.ChannelGuildGetTotalMemberCountResp, error)
}

func RegisterChannelGuildGoLogicServer(s *grpc.Server, srv ChannelGuildGoLogicServer) {
	s.RegisterService(&_ChannelGuildGoLogic_serviceDesc, srv)
}

func _ChannelGuildGoLogic_ChannelGuildGetListSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel.ChannelGuildGetListSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildGoLogicServer).ChannelGuildGetListSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channelguild_go.ChannelGuildGoLogic/ChannelGuildGetListSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildGoLogicServer).ChannelGuildGetListSummary(ctx, req.(*channel.ChannelGuildGetListSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuildGoLogic_ChannelGuildGetListByType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel.ChannelGuildGetListByTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildGoLogicServer).ChannelGuildGetListByType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channelguild_go.ChannelGuildGoLogic/ChannelGuildGetListByType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildGoLogicServer).ChannelGuildGetListByType(ctx, req.(*channel.ChannelGuildGetListByTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuildGoLogic_ChannelGuildGetTotalMemberCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel.ChannelGuildGetTotalMemberCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuildGoLogicServer).ChannelGuildGetTotalMemberCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channelguild_go.ChannelGuildGoLogic/ChannelGuildGetTotalMemberCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuildGoLogicServer).ChannelGuildGetTotalMemberCount(ctx, req.(*channel.ChannelGuildGetTotalMemberCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelGuildGoLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.channelguild_go.ChannelGuildGoLogic",
	HandlerType: (*ChannelGuildGoLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChannelGuildGetListSummary",
			Handler:    _ChannelGuildGoLogic_ChannelGuildGetListSummary_Handler,
		},
		{
			MethodName: "ChannelGuildGetListByType",
			Handler:    _ChannelGuildGoLogic_ChannelGuildGetListByType_Handler,
		},
		{
			MethodName: "ChannelGuildGetTotalMemberCount",
			Handler:    _ChannelGuildGoLogic_ChannelGuildGetTotalMemberCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/channelguild_go/grpc_channelguild_go.proto",
}

func init() {
	proto.RegisterFile("api/channelguild_go/grpc_channelguild_go.proto", fileDescriptor_grpc_channelguild_go_5b16022051483d70)
}

var fileDescriptor_grpc_channelguild_go_5b16022051483d70 = []byte{
	// 324 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x92, 0xc1, 0x4b, 0xc3, 0x30,
	0x14, 0xc6, 0xd9, 0x86, 0xa2, 0xb9, 0x2d, 0xc3, 0x31, 0x2b, 0x62, 0xb7, 0x83, 0x9e, 0x96, 0xc0,
	0xc4, 0x93, 0xe0, 0x61, 0x3b, 0xec, 0x32, 0x41, 0xe6, 0x4e, 0x5e, 0x46, 0x16, 0x43, 0x2c, 0xa6,
	0x79, 0x59, 0x97, 0xa2, 0x3b, 0x08, 0x65, 0x47, 0x8f, 0xfe, 0x09, 0xfe, 0x07, 0xfe, 0x7f, 0x2a,
	0x92, 0x16, 0xaa, 0x2b, 0x1d, 0xf3, 0xd6, 0x7e, 0xdf, 0xef, 0x7d, 0x1f, 0xe4, 0x3d, 0x44, 0x98,
	0x09, 0x28, 0x7f, 0x60, 0x5a, 0x0b, 0x25, 0xe3, 0x40, 0xdd, 0x4f, 0x25, 0x50, 0x19, 0x19, 0x3e,
	0x2d, 0x88, 0xc4, 0x44, 0x60, 0x01, 0x37, 0x25, 0x73, 0x23, 0xa4, 0xe0, 0x7a, 0x8d, 0x35, 0x21,
	0x83, 0xbd, 0x63, 0x17, 0x2e, 0x9e, 0xad, 0xd0, 0x8b, 0x00, 0xf4, 0xef, 0x57, 0x66, 0xf7, 0x3e,
	0x6a, 0xa8, 0x31, 0xc8, 0xc6, 0x86, 0x6e, 0x6c, 0x08, 0x23, 0x90, 0x01, 0xc7, 0x1a, 0x79, 0x6b,
	0xb2, 0xb0, 0xa3, 0x60, 0x61, 0x6f, 0xe3, 0x30, 0x64, 0xd1, 0x12, 0xb7, 0x89, 0x64, 0x64, 0xb3,
	0x3f, 0x16, 0x73, 0xaf, 0xb3, 0x0d, 0x59, 0x98, 0xce, 0xfe, 0x2a, 0xf1, 0x77, 0xf6, 0x3e, 0xeb,
	0xad, 0x0a, 0x7e, 0x44, 0x87, 0x25, 0x70, 0x7f, 0x39, 0x59, 0x1a, 0x81, 0xfd, 0x0d, 0x59, 0x99,
	0xed, 0xda, 0xda, 0x5b, 0x88, 0xbc, 0xec, 0xcb, 0x95, 0xbd, 0xa0, 0x93, 0x02, 0x3b, 0x01, 0xcb,
	0xd4, 0xb5, 0x08, 0x67, 0x22, 0x1a, 0x40, 0xac, 0x2d, 0x3e, 0x2d, 0x09, 0x2c, 0x42, 0xae, 0xf8,
	0xec, 0x5f, 0x5c, 0x5e, 0xff, 0x5d, 0x6f, 0x55, 0xbc, 0xab, 0x55, 0xe2, 0x1f, 0xfc, 0xdd, 0x55,
	0x57, 0x42, 0x57, 0xb9, 0x67, 0x7f, 0x4d, 0xfc, 0xaa, 0x84, 0xb7, 0xc4, 0x3f, 0xa2, 0xe9, 0x3f,
	0x29, 0x59, 0x0d, 0xed, 0x2b, 0xd4, 0xe4, 0x10, 0x92, 0x79, 0xfc, 0xc4, 0x34, 0xb1, 0x36, 0xdb,
	0xa4, 0xbb, 0x88, 0xbb, 0x91, 0x04, 0xc5, 0xb4, 0x24, 0x17, 0x3d, 0x6b, 0x09, 0x87, 0x90, 0xa6,
	0x16, 0x07, 0x45, 0x99, 0x31, 0xb4, 0x78, 0x66, 0x79, 0xed, 0x65, 0xa9, 0xfa, 0x5e, 0xad, 0x8d,
	0x6f, 0x06, 0xb3, 0xdd, 0x34, 0xe3, 0xfc, 0x27, 0x00, 0x00, 0xff, 0xff, 0xcb, 0xdf, 0x32, 0xab,
	0xa6, 0x02, 0x00, 0x00,
}
