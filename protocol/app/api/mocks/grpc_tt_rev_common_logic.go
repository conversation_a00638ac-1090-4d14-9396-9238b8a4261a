// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/app/api/tt_rev_common_logic (interfaces: TTRevCommonLogicClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	tt_rev_common_logic "golang.52tt.com/protocol/app/tt_rev_common_logic"
	grpc "google.golang.org/grpc"
)

// MockTTRevCommonLogicClient is a mock of TTRevCommonLogicClient interface.
type MockTTRevCommonLogicClient struct {
	ctrl     *gomock.Controller
	recorder *MockTTRevCommonLogicClientMockRecorder
}

// MockTTRevCommonLogicClientMockRecorder is the mock recorder for MockTTRevCommonLogicClient.
type MockTTRevCommonLogicClientMockRecorder struct {
	mock *MockTTRevCommonLogicClient
}

// NewMockTTRevCommonLogicClient creates a new mock instance.
func NewMockTTRevCommonLogicClient(ctrl *gomock.Controller) *MockTTRevCommonLogicClient {
	mock := &MockTTRevCommonLogicClient{ctrl: ctrl}
	mock.recorder = &MockTTRevCommonLogicClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTTRevCommonLogicClient) EXPECT() *MockTTRevCommonLogicClientMockRecorder {
	return m.recorder
}

// GetNewRechargeActEntryInfo mocks base method.
func (m *MockTTRevCommonLogicClient) GetNewRechargeActEntryInfo(arg0 context.Context, arg1 *tt_rev_common_logic.GetNewRechargeActEntryInfoRequest, arg2 ...grpc.CallOption) (*tt_rev_common_logic.GetNewRechargeActEntryInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewRechargeActEntryInfo", varargs...)
	ret0, _ := ret[0].(*tt_rev_common_logic.GetNewRechargeActEntryInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewRechargeActEntryInfo indicates an expected call of GetNewRechargeActEntryInfo.
func (mr *MockTTRevCommonLogicClientMockRecorder) GetNewRechargeActEntryInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewRechargeActEntryInfo", reflect.TypeOf((*MockTTRevCommonLogicClient)(nil).GetNewRechargeActEntryInfo), varargs...)
}

// GetNewRechargeActPopupInfo mocks base method.
func (m *MockTTRevCommonLogicClient) GetNewRechargeActPopupInfo(arg0 context.Context, arg1 *tt_rev_common_logic.GetNewRechargeActPopupInfoRequest, arg2 ...grpc.CallOption) (*tt_rev_common_logic.GetNewRechargeActPopupInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewRechargeActPopupInfo", varargs...)
	ret0, _ := ret[0].(*tt_rev_common_logic.GetNewRechargeActPopupInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewRechargeActPopupInfo indicates an expected call of GetNewRechargeActPopupInfo.
func (mr *MockTTRevCommonLogicClientMockRecorder) GetNewRechargeActPopupInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewRechargeActPopupInfo", reflect.TypeOf((*MockTTRevCommonLogicClient)(nil).GetNewRechargeActPopupInfo), varargs...)
}

// GetRechargeBannerInfo mocks base method.
func (m *MockTTRevCommonLogicClient) GetRechargeBannerInfo(arg0 context.Context, arg1 *tt_rev_common_logic.GetRechargeBannerInfoRequest, arg2 ...grpc.CallOption) (*tt_rev_common_logic.GetRechargeBannerInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRechargeBannerInfo", varargs...)
	ret0, _ := ret[0].(*tt_rev_common_logic.GetRechargeBannerInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRechargeBannerInfo indicates an expected call of GetRechargeBannerInfo.
func (mr *MockTTRevCommonLogicClientMockRecorder) GetRechargeBannerInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRechargeBannerInfo", reflect.TypeOf((*MockTTRevCommonLogicClient)(nil).GetRechargeBannerInfo), varargs...)
}
