// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/numeric/grpc_numeric.proto

package numeric // import "golang.52tt.com/protocol/app/api/numeric"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import numeric_logic "golang.52tt.com/protocol/app/numeric-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// NumericLogicClient is the client API for NumericLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type NumericLogicClient interface {
	GetUserRichSwitch(ctx context.Context, in *numeric_logic.GetUserRichSwitchReq, opts ...grpc.CallOption) (*numeric_logic.GetUserRichSwitchResp, error)
	SetUserRichSwitch(ctx context.Context, in *numeric_logic.SetUserRichSwitchReq, opts ...grpc.CallOption) (*numeric_logic.SetUserRichSwitchResp, error)
	// rank
	GetUserGloryRank(ctx context.Context, in *numeric_logic.GetUserGloryRankReq, opts ...grpc.CallOption) (*numeric_logic.GetUserGloryRankResp, error)
}

type numericLogicClient struct {
	cc *grpc.ClientConn
}

func NewNumericLogicClient(cc *grpc.ClientConn) NumericLogicClient {
	return &numericLogicClient{cc}
}

func (c *numericLogicClient) GetUserRichSwitch(ctx context.Context, in *numeric_logic.GetUserRichSwitchReq, opts ...grpc.CallOption) (*numeric_logic.GetUserRichSwitchResp, error) {
	out := new(numeric_logic.GetUserRichSwitchResp)
	err := c.cc.Invoke(ctx, "/ga.api.numeric.NumericLogic/GetUserRichSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericLogicClient) SetUserRichSwitch(ctx context.Context, in *numeric_logic.SetUserRichSwitchReq, opts ...grpc.CallOption) (*numeric_logic.SetUserRichSwitchResp, error) {
	out := new(numeric_logic.SetUserRichSwitchResp)
	err := c.cc.Invoke(ctx, "/ga.api.numeric.NumericLogic/SetUserRichSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericLogicClient) GetUserGloryRank(ctx context.Context, in *numeric_logic.GetUserGloryRankReq, opts ...grpc.CallOption) (*numeric_logic.GetUserGloryRankResp, error) {
	out := new(numeric_logic.GetUserGloryRankResp)
	err := c.cc.Invoke(ctx, "/ga.api.numeric.NumericLogic/GetUserGloryRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NumericLogicServer is the server API for NumericLogic service.
type NumericLogicServer interface {
	GetUserRichSwitch(context.Context, *numeric_logic.GetUserRichSwitchReq) (*numeric_logic.GetUserRichSwitchResp, error)
	SetUserRichSwitch(context.Context, *numeric_logic.SetUserRichSwitchReq) (*numeric_logic.SetUserRichSwitchResp, error)
	// rank
	GetUserGloryRank(context.Context, *numeric_logic.GetUserGloryRankReq) (*numeric_logic.GetUserGloryRankResp, error)
}

func RegisterNumericLogicServer(s *grpc.Server, srv NumericLogicServer) {
	s.RegisterService(&_NumericLogic_serviceDesc, srv)
}

func _NumericLogic_GetUserRichSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.GetUserRichSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).GetUserRichSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.numeric.NumericLogic/GetUserRichSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).GetUserRichSwitch(ctx, req.(*numeric_logic.GetUserRichSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericLogic_SetUserRichSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.SetUserRichSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).SetUserRichSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.numeric.NumericLogic/SetUserRichSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).SetUserRichSwitch(ctx, req.(*numeric_logic.SetUserRichSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericLogic_GetUserGloryRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.GetUserGloryRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).GetUserGloryRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.numeric.NumericLogic/GetUserGloryRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).GetUserGloryRank(ctx, req.(*numeric_logic.GetUserGloryRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _NumericLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.numeric.NumericLogic",
	HandlerType: (*NumericLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserRichSwitch",
			Handler:    _NumericLogic_GetUserRichSwitch_Handler,
		},
		{
			MethodName: "SetUserRichSwitch",
			Handler:    _NumericLogic_SetUserRichSwitch_Handler,
		},
		{
			MethodName: "GetUserGloryRank",
			Handler:    _NumericLogic_GetUserGloryRank_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/numeric/grpc_numeric.proto",
}

func init() {
	proto.RegisterFile("api/numeric/grpc_numeric.proto", fileDescriptor_grpc_numeric_55e4b674c0521e5d)
}

var fileDescriptor_grpc_numeric_55e4b674c0521e5d = []byte{
	// 295 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x92, 0x4b, 0x2c, 0xc8, 0xd4,
	0xcf, 0x2b, 0xcd, 0x4d, 0x2d, 0xca, 0x4c, 0xd6, 0x4f, 0x2f, 0x2a, 0x48, 0x8e, 0x87, 0x72, 0xf4,
	0x0a, 0x8a, 0xf2, 0x4b, 0xf2, 0x85, 0xf8, 0xd2, 0x13, 0xf5, 0x12, 0x0b, 0x32, 0xf5, 0xa0, 0xa2,
	0x52, 0x22, 0x50, 0x86, 0x6e, 0x4e, 0x7e, 0x7a, 0x66, 0x72, 0x3c, 0x44, 0x95, 0x94, 0x2c, 0xc8,
	0x94, 0xd4, 0x8a, 0x92, 0xd4, 0xbc, 0xe2, 0xcc, 0xfc, 0x3c, 0x04, 0x0b, 0x22, 0x6d, 0xd4, 0xc3,
	0xcc, 0xc5, 0xe3, 0x07, 0xd1, 0xe7, 0x03, 0xd2, 0x26, 0x94, 0xc7, 0x25, 0xe8, 0x9e, 0x5a, 0x12,
	0x5a, 0x9c, 0x5a, 0x14, 0x94, 0x99, 0x9c, 0x11, 0x5c, 0x9e, 0x59, 0x92, 0x9c, 0x21, 0xa4, 0xa6,
	0x97, 0x9e, 0x08, 0xb3, 0x27, 0x1e, 0x6c, 0xbc, 0x1e, 0x86, 0xa2, 0xa0, 0xd4, 0x42, 0x29, 0x75,
	0xa2, 0xd4, 0x15, 0x17, 0x28, 0x71, 0x34, 0x35, 0x28, 0xb0, 0x70, 0xac, 0xf8, 0xca, 0x08, 0xb2,
	0x2f, 0x98, 0x18, 0xfb, 0x82, 0x89, 0xb4, 0x2f, 0x18, 0x8f, 0x7d, 0x2b, 0xbf, 0x32, 0x0a, 0x65,
	0x73, 0x09, 0x40, 0x9d, 0xe4, 0x9e, 0x93, 0x5f, 0x54, 0x19, 0x94, 0x98, 0x97, 0x2d, 0xa4, 0x8a,
	0xd3, 0xd9, 0x70, 0x35, 0x20, 0xdb, 0xd4, 0x88, 0x51, 0x06, 0xb7, 0x6c, 0xd3, 0x57, 0x46, 0x29,
	0xfd, 0xa6, 0x06, 0x05, 0x5e, 0x94, 0x68, 0xe9, 0x6a, 0x50, 0x60, 0x4a, 0xcf, 0x9f, 0xd4, 0xa0,
	0x20, 0xa2, 0x0f, 0x31, 0x01, 0x39, 0xf0, 0xf5, 0x9d, 0x02, 0xb8, 0xc4, 0x92, 0xf3, 0x73, 0xf5,
	0x0a, 0x4b, 0xcb, 0x13, 0xf3, 0xf4, 0x4a, 0x4a, 0x20, 0x91, 0x04, 0x8a, 0xe5, 0x28, 0x83, 0xf4,
	0xfc, 0x9c, 0xc4, 0xbc, 0x74, 0x3d, 0x53, 0xa3, 0x92, 0x12, 0xbd, 0xe4, 0xfc, 0x5c, 0x7d, 0xb0,
	0x54, 0x72, 0x7e, 0x8e, 0x7e, 0x62, 0x41, 0x81, 0x3e, 0x52, 0x52, 0xb1, 0x86, 0xd2, 0x8b, 0x98,
	0x98, 0x83, 0x02, 0x9c, 0x93, 0xd8, 0xc0, 0xea, 0x8c, 0x01, 0x01, 0x00, 0x00, 0xff, 0xff, 0x87,
	0x9b, 0xc9, 0x3b, 0x4e, 0x02, 0x00, 0x00,
}
