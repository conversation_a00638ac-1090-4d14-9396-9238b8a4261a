// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/offer_room/grpc_offer_room.proto

package offer_room // import "golang.52tt.com/protocol/app/api/offer_room"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import offer_room "golang.52tt.com/protocol/app/offer_room"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// OfferRoomClient is the client API for OfferRoom service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type OfferRoomClient interface {
	// 获取报名队列
	OfferRoomGetApplyList(ctx context.Context, in *offer_room.OfferRoomGetApplyListRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomGetApplyListResponse, error)
	// 用户报名
	OfferRoomUserApply(ctx context.Context, in *offer_room.OfferRoomUserApplyRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomUserApplyResponse, error)
	// 用户取消报名
	OfferRoomUserCancelApply(ctx context.Context, in *offer_room.OfferRoomUserCancelApplyRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomUserCancelApplyResponse, error)
	// 获取当前游戏信息
	OfferRoomGetCurOfferingGameInfo(ctx context.Context, in *offer_room.OfferRoomGetCurOfferingGameInfoRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomGetCurOfferingGameInfoResponse, error)
	// 获取拍卖配置
	OfferRoomGetOfferingConfig(ctx context.Context, in *offer_room.OfferRoomGetOfferingConfigRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomGetOfferingConfigResponse, error)
	// 提交拍卖设置
	OfferRoomSubmitOfferingSetting(ctx context.Context, in *offer_room.OfferRoomSubmitOfferingSettingRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomSubmitOfferingSettingResponse, error)
	// 出价
	OfferRoomNamePriceOnce(ctx context.Context, in *offer_room.OfferRoomNamePriceOnceRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomNamePriceOnceResponse, error)
	// 一键定拍
	OfferRoomNamePriceMax(ctx context.Context, in *offer_room.OfferRoomNamePriceMaxRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomNamePriceMaxResponse, error)
	// 定拍
	OfferRoomOfferingSet(ctx context.Context, in *offer_room.OfferRoomOfferingSetRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomOfferingSetResponse, error)
	// 流拍
	OfferRoomOfferingPass(ctx context.Context, in *offer_room.OfferRoomOfferingPassRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomOfferingPassResponse, error)
	// 结束
	OfferRoomOfferingEnd(ctx context.Context, in *offer_room.OfferRoomOfferingEndRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomOfferingEndResponse, error)
	// 获取关系列表
	OfferRoomOfferingRelationships(ctx context.Context, in *offer_room.OfferRoomOfferingRelationshipsRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomOfferingRelationshipsResponse, error)
	// 删除关系
	OfferRoomDeleteRelationship(ctx context.Context, in *offer_room.OfferRoomDeleteRelationshipRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomDeleteRelationshipResponse, error)
	// 初始化游戏
	OfferRoomOfferingInit(ctx context.Context, in *offer_room.OfferRoomOfferingInitRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomOfferingInitResponse, error)
	// 资料卡关系信息
	OfferRoomCardInfo(ctx context.Context, in *offer_room.OfferRoomCardInfoRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomCardInfoResponse, error)
	// 获取拍卖铭牌资源配置
	OfferRoomNameplateConfig(ctx context.Context, in *offer_room.OfferRoomNameplateConfigReq, opts ...grpc.CallOption) (*offer_room.OfferRoomNameplateConfigResp, error)
	// 获取用户铭牌信息
	OfferRoomGetUserNameplateInfo(ctx context.Context, in *offer_room.OfferRoomGetUserNameplateInfoReq, opts ...grpc.CallOption) (*offer_room.OfferRoomGetUserNameplateInfoResp, error)
}

type offerRoomClient struct {
	cc *grpc.ClientConn
}

func NewOfferRoomClient(cc *grpc.ClientConn) OfferRoomClient {
	return &offerRoomClient{cc}
}

func (c *offerRoomClient) OfferRoomGetApplyList(ctx context.Context, in *offer_room.OfferRoomGetApplyListRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomGetApplyListResponse, error) {
	out := new(offer_room.OfferRoomGetApplyListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomGetApplyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomUserApply(ctx context.Context, in *offer_room.OfferRoomUserApplyRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomUserApplyResponse, error) {
	out := new(offer_room.OfferRoomUserApplyResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomUserApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomUserCancelApply(ctx context.Context, in *offer_room.OfferRoomUserCancelApplyRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomUserCancelApplyResponse, error) {
	out := new(offer_room.OfferRoomUserCancelApplyResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomUserCancelApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomGetCurOfferingGameInfo(ctx context.Context, in *offer_room.OfferRoomGetCurOfferingGameInfoRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomGetCurOfferingGameInfoResponse, error) {
	out := new(offer_room.OfferRoomGetCurOfferingGameInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomGetCurOfferingGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomGetOfferingConfig(ctx context.Context, in *offer_room.OfferRoomGetOfferingConfigRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomGetOfferingConfigResponse, error) {
	out := new(offer_room.OfferRoomGetOfferingConfigResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomGetOfferingConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomSubmitOfferingSetting(ctx context.Context, in *offer_room.OfferRoomSubmitOfferingSettingRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomSubmitOfferingSettingResponse, error) {
	out := new(offer_room.OfferRoomSubmitOfferingSettingResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomSubmitOfferingSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomNamePriceOnce(ctx context.Context, in *offer_room.OfferRoomNamePriceOnceRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomNamePriceOnceResponse, error) {
	out := new(offer_room.OfferRoomNamePriceOnceResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomNamePriceOnce", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomNamePriceMax(ctx context.Context, in *offer_room.OfferRoomNamePriceMaxRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomNamePriceMaxResponse, error) {
	out := new(offer_room.OfferRoomNamePriceMaxResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomNamePriceMax", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomOfferingSet(ctx context.Context, in *offer_room.OfferRoomOfferingSetRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomOfferingSetResponse, error) {
	out := new(offer_room.OfferRoomOfferingSetResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomOfferingSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomOfferingPass(ctx context.Context, in *offer_room.OfferRoomOfferingPassRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomOfferingPassResponse, error) {
	out := new(offer_room.OfferRoomOfferingPassResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomOfferingPass", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomOfferingEnd(ctx context.Context, in *offer_room.OfferRoomOfferingEndRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomOfferingEndResponse, error) {
	out := new(offer_room.OfferRoomOfferingEndResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomOfferingEnd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomOfferingRelationships(ctx context.Context, in *offer_room.OfferRoomOfferingRelationshipsRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomOfferingRelationshipsResponse, error) {
	out := new(offer_room.OfferRoomOfferingRelationshipsResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomOfferingRelationships", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomDeleteRelationship(ctx context.Context, in *offer_room.OfferRoomDeleteRelationshipRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomDeleteRelationshipResponse, error) {
	out := new(offer_room.OfferRoomDeleteRelationshipResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomDeleteRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomOfferingInit(ctx context.Context, in *offer_room.OfferRoomOfferingInitRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomOfferingInitResponse, error) {
	out := new(offer_room.OfferRoomOfferingInitResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomOfferingInit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomCardInfo(ctx context.Context, in *offer_room.OfferRoomCardInfoRequest, opts ...grpc.CallOption) (*offer_room.OfferRoomCardInfoResponse, error) {
	out := new(offer_room.OfferRoomCardInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomCardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomNameplateConfig(ctx context.Context, in *offer_room.OfferRoomNameplateConfigReq, opts ...grpc.CallOption) (*offer_room.OfferRoomNameplateConfigResp, error) {
	out := new(offer_room.OfferRoomNameplateConfigResp)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomNameplateConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offerRoomClient) OfferRoomGetUserNameplateInfo(ctx context.Context, in *offer_room.OfferRoomGetUserNameplateInfoReq, opts ...grpc.CallOption) (*offer_room.OfferRoomGetUserNameplateInfoResp, error) {
	out := new(offer_room.OfferRoomGetUserNameplateInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.offer_room.OfferRoom/OfferRoomGetUserNameplateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OfferRoomServer is the server API for OfferRoom service.
type OfferRoomServer interface {
	// 获取报名队列
	OfferRoomGetApplyList(context.Context, *offer_room.OfferRoomGetApplyListRequest) (*offer_room.OfferRoomGetApplyListResponse, error)
	// 用户报名
	OfferRoomUserApply(context.Context, *offer_room.OfferRoomUserApplyRequest) (*offer_room.OfferRoomUserApplyResponse, error)
	// 用户取消报名
	OfferRoomUserCancelApply(context.Context, *offer_room.OfferRoomUserCancelApplyRequest) (*offer_room.OfferRoomUserCancelApplyResponse, error)
	// 获取当前游戏信息
	OfferRoomGetCurOfferingGameInfo(context.Context, *offer_room.OfferRoomGetCurOfferingGameInfoRequest) (*offer_room.OfferRoomGetCurOfferingGameInfoResponse, error)
	// 获取拍卖配置
	OfferRoomGetOfferingConfig(context.Context, *offer_room.OfferRoomGetOfferingConfigRequest) (*offer_room.OfferRoomGetOfferingConfigResponse, error)
	// 提交拍卖设置
	OfferRoomSubmitOfferingSetting(context.Context, *offer_room.OfferRoomSubmitOfferingSettingRequest) (*offer_room.OfferRoomSubmitOfferingSettingResponse, error)
	// 出价
	OfferRoomNamePriceOnce(context.Context, *offer_room.OfferRoomNamePriceOnceRequest) (*offer_room.OfferRoomNamePriceOnceResponse, error)
	// 一键定拍
	OfferRoomNamePriceMax(context.Context, *offer_room.OfferRoomNamePriceMaxRequest) (*offer_room.OfferRoomNamePriceMaxResponse, error)
	// 定拍
	OfferRoomOfferingSet(context.Context, *offer_room.OfferRoomOfferingSetRequest) (*offer_room.OfferRoomOfferingSetResponse, error)
	// 流拍
	OfferRoomOfferingPass(context.Context, *offer_room.OfferRoomOfferingPassRequest) (*offer_room.OfferRoomOfferingPassResponse, error)
	// 结束
	OfferRoomOfferingEnd(context.Context, *offer_room.OfferRoomOfferingEndRequest) (*offer_room.OfferRoomOfferingEndResponse, error)
	// 获取关系列表
	OfferRoomOfferingRelationships(context.Context, *offer_room.OfferRoomOfferingRelationshipsRequest) (*offer_room.OfferRoomOfferingRelationshipsResponse, error)
	// 删除关系
	OfferRoomDeleteRelationship(context.Context, *offer_room.OfferRoomDeleteRelationshipRequest) (*offer_room.OfferRoomDeleteRelationshipResponse, error)
	// 初始化游戏
	OfferRoomOfferingInit(context.Context, *offer_room.OfferRoomOfferingInitRequest) (*offer_room.OfferRoomOfferingInitResponse, error)
	// 资料卡关系信息
	OfferRoomCardInfo(context.Context, *offer_room.OfferRoomCardInfoRequest) (*offer_room.OfferRoomCardInfoResponse, error)
	// 获取拍卖铭牌资源配置
	OfferRoomNameplateConfig(context.Context, *offer_room.OfferRoomNameplateConfigReq) (*offer_room.OfferRoomNameplateConfigResp, error)
	// 获取用户铭牌信息
	OfferRoomGetUserNameplateInfo(context.Context, *offer_room.OfferRoomGetUserNameplateInfoReq) (*offer_room.OfferRoomGetUserNameplateInfoResp, error)
}

func RegisterOfferRoomServer(s *grpc.Server, srv OfferRoomServer) {
	s.RegisterService(&_OfferRoom_serviceDesc, srv)
}

func _OfferRoom_OfferRoomGetApplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomGetApplyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomGetApplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomGetApplyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomGetApplyList(ctx, req.(*offer_room.OfferRoomGetApplyListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomUserApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomUserApplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomUserApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomUserApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomUserApply(ctx, req.(*offer_room.OfferRoomUserApplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomUserCancelApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomUserCancelApplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomUserCancelApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomUserCancelApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomUserCancelApply(ctx, req.(*offer_room.OfferRoomUserCancelApplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomGetCurOfferingGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomGetCurOfferingGameInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomGetCurOfferingGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomGetCurOfferingGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomGetCurOfferingGameInfo(ctx, req.(*offer_room.OfferRoomGetCurOfferingGameInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomGetOfferingConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomGetOfferingConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomGetOfferingConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomGetOfferingConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomGetOfferingConfig(ctx, req.(*offer_room.OfferRoomGetOfferingConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomSubmitOfferingSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomSubmitOfferingSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomSubmitOfferingSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomSubmitOfferingSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomSubmitOfferingSetting(ctx, req.(*offer_room.OfferRoomSubmitOfferingSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomNamePriceOnce_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomNamePriceOnceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomNamePriceOnce(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomNamePriceOnce",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomNamePriceOnce(ctx, req.(*offer_room.OfferRoomNamePriceOnceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomNamePriceMax_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomNamePriceMaxRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomNamePriceMax(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomNamePriceMax",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomNamePriceMax(ctx, req.(*offer_room.OfferRoomNamePriceMaxRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomOfferingSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomOfferingSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomOfferingSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomOfferingSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomOfferingSet(ctx, req.(*offer_room.OfferRoomOfferingSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomOfferingPass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomOfferingPassRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomOfferingPass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomOfferingPass",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomOfferingPass(ctx, req.(*offer_room.OfferRoomOfferingPassRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomOfferingEnd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomOfferingEndRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomOfferingEnd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomOfferingEnd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomOfferingEnd(ctx, req.(*offer_room.OfferRoomOfferingEndRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomOfferingRelationships_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomOfferingRelationshipsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomOfferingRelationships(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomOfferingRelationships",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomOfferingRelationships(ctx, req.(*offer_room.OfferRoomOfferingRelationshipsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomDeleteRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomDeleteRelationshipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomDeleteRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomDeleteRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomDeleteRelationship(ctx, req.(*offer_room.OfferRoomDeleteRelationshipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomOfferingInit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomOfferingInitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomOfferingInit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomOfferingInit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomOfferingInit(ctx, req.(*offer_room.OfferRoomOfferingInitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomCardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomCardInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomCardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomCardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomCardInfo(ctx, req.(*offer_room.OfferRoomCardInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomNameplateConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomNameplateConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomNameplateConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomNameplateConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomNameplateConfig(ctx, req.(*offer_room.OfferRoomNameplateConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferRoom_OfferRoomGetUserNameplateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(offer_room.OfferRoomGetUserNameplateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferRoomServer).OfferRoomGetUserNameplateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.offer_room.OfferRoom/OfferRoomGetUserNameplateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferRoomServer).OfferRoomGetUserNameplateInfo(ctx, req.(*offer_room.OfferRoomGetUserNameplateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _OfferRoom_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.offer_room.OfferRoom",
	HandlerType: (*OfferRoomServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OfferRoomGetApplyList",
			Handler:    _OfferRoom_OfferRoomGetApplyList_Handler,
		},
		{
			MethodName: "OfferRoomUserApply",
			Handler:    _OfferRoom_OfferRoomUserApply_Handler,
		},
		{
			MethodName: "OfferRoomUserCancelApply",
			Handler:    _OfferRoom_OfferRoomUserCancelApply_Handler,
		},
		{
			MethodName: "OfferRoomGetCurOfferingGameInfo",
			Handler:    _OfferRoom_OfferRoomGetCurOfferingGameInfo_Handler,
		},
		{
			MethodName: "OfferRoomGetOfferingConfig",
			Handler:    _OfferRoom_OfferRoomGetOfferingConfig_Handler,
		},
		{
			MethodName: "OfferRoomSubmitOfferingSetting",
			Handler:    _OfferRoom_OfferRoomSubmitOfferingSetting_Handler,
		},
		{
			MethodName: "OfferRoomNamePriceOnce",
			Handler:    _OfferRoom_OfferRoomNamePriceOnce_Handler,
		},
		{
			MethodName: "OfferRoomNamePriceMax",
			Handler:    _OfferRoom_OfferRoomNamePriceMax_Handler,
		},
		{
			MethodName: "OfferRoomOfferingSet",
			Handler:    _OfferRoom_OfferRoomOfferingSet_Handler,
		},
		{
			MethodName: "OfferRoomOfferingPass",
			Handler:    _OfferRoom_OfferRoomOfferingPass_Handler,
		},
		{
			MethodName: "OfferRoomOfferingEnd",
			Handler:    _OfferRoom_OfferRoomOfferingEnd_Handler,
		},
		{
			MethodName: "OfferRoomOfferingRelationships",
			Handler:    _OfferRoom_OfferRoomOfferingRelationships_Handler,
		},
		{
			MethodName: "OfferRoomDeleteRelationship",
			Handler:    _OfferRoom_OfferRoomDeleteRelationship_Handler,
		},
		{
			MethodName: "OfferRoomOfferingInit",
			Handler:    _OfferRoom_OfferRoomOfferingInit_Handler,
		},
		{
			MethodName: "OfferRoomCardInfo",
			Handler:    _OfferRoom_OfferRoomCardInfo_Handler,
		},
		{
			MethodName: "OfferRoomNameplateConfig",
			Handler:    _OfferRoom_OfferRoomNameplateConfig_Handler,
		},
		{
			MethodName: "OfferRoomGetUserNameplateInfo",
			Handler:    _OfferRoom_OfferRoomGetUserNameplateInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/offer_room/grpc_offer_room.proto",
}

func init() {
	proto.RegisterFile("api/offer_room/grpc_offer_room.proto", fileDescriptor_grpc_offer_room_d83d90b742263d31)
}

var fileDescriptor_grpc_offer_room_d83d90b742263d31 = []byte{
	// 676 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x96, 0xcd, 0x4f, 0xd4, 0x40,
	0x18, 0xc6, 0x53, 0x74, 0x0c, 0xce, 0x49, 0x26, 0x4a, 0x4c, 0x09, 0x4a, 0x8c, 0x89, 0x08, 0xd2,
	0x22, 0x0a, 0x17, 0x4f, 0xba, 0x12, 0x42, 0xa2, 0x42, 0x96, 0xe8, 0xc1, 0x0b, 0x19, 0xca, 0x6c,
	0x9d, 0xa4, 0x9d, 0x19, 0xda, 0xd9, 0x08, 0x89, 0x46, 0x82, 0xf1, 0xa2, 0xc6, 0xab, 0x89, 0x07,
	0x3f, 0xf0, 0xfb, 0xf3, 0x4f, 0x9b, 0xbf, 0xc1, 0xb4, 0xcb, 0x74, 0x67, 0x60, 0xbb, 0xed, 0xde,
	0xb6, 0xdb, 0xdf, 0xf3, 0x3e, 0xcf, 0xdb, 0xcc, 0x3b, 0x33, 0xf0, 0x22, 0x16, 0xd4, 0xe7, 0xad,
	0x16, 0x49, 0xd6, 0x13, 0xce, 0x63, 0x3f, 0x4c, 0x44, 0xb0, 0xde, 0x7d, 0xf6, 0x44, 0xc2, 0x25,
	0x47, 0x23, 0x21, 0xf6, 0xb0, 0xa0, 0x5e, 0xf7, 0x85, 0x3b, 0x9e, 0x09, 0xc9, 0xb6, 0x24, 0x2c,
	0xa5, 0x9c, 0x75, 0x7f, 0x75, 0x14, 0xee, 0x98, 0x51, 0xf3, 0x70, 0xb9, 0xb9, 0xe7, 0x08, 0x9e,
	0x5c, 0xc9, 0xfe, 0x6c, 0x72, 0x1e, 0xa3, 0x27, 0xf0, 0x4c, 0xf1, 0xb0, 0x44, 0xe4, 0x4d, 0x21,
	0xa2, 0x9d, 0x3b, 0x34, 0x95, 0x68, 0xda, 0x0b, 0xb1, 0x61, 0xe9, 0xf5, 0xa4, 0x9a, 0x64, 0xab,
	0x4d, 0x52, 0xe9, 0x5e, 0xa9, 0x07, 0xa7, 0x82, 0xb3, 0x94, 0x5c, 0x18, 0xde, 0xdb, 0x9d, 0x38,
	0x3e, 0xfc, 0x5e, 0x01, 0x94, 0x42, 0x54, 0xa0, 0xf7, 0x53, 0x92, 0xe4, 0x2c, 0x9a, 0x2c, 0xab,
	0x56, 0x20, 0xda, 0xf7, 0x72, 0x0d, 0xd2, 0x32, 0xfd, 0xa0, 0x00, 0x7a, 0xe1, 0xc0, 0xb3, 0x16,
	0xd8, 0xc0, 0x2c, 0x20, 0x51, 0xc7, 0xdb, 0xeb, 0x57, 0xd1, 0x00, 0x75, 0x02, 0xbf, 0x36, 0x6f,
	0xe5, 0xf8, 0xa8, 0x00, 0x7a, 0xe7, 0xc0, 0xf3, 0xe6, 0x87, 0x6a, 0xb4, 0x93, 0xfc, 0x91, 0xb2,
	0x70, 0x09, 0xc7, 0x64, 0x99, 0xb5, 0x38, 0x9a, 0xef, 0xf3, 0x61, 0x7b, 0xf0, 0x3a, 0xd5, 0xc2,
	0xa0, 0x32, 0x2b, 0xdc, 0x27, 0x05, 0xd0, 0x2b, 0x07, 0xba, 0xa6, 0x4a, 0x4b, 0x1a, 0x9c, 0xb5,
	0x68, 0x88, 0x66, 0xfb, 0x18, 0xd8, 0xa8, 0x8e, 0x74, 0x75, 0x00, 0x85, 0x95, 0x66, 0x5f, 0x01,
	0xf4, 0xd6, 0x81, 0xe7, 0x0a, 0xc1, 0x5a, 0x7b, 0x23, 0xa6, 0x85, 0x66, 0x8d, 0x48, 0x49, 0x59,
	0x88, 0xae, 0x97, 0xd5, 0xef, 0x89, 0xeb, 0x54, 0xf3, 0x03, 0xaa, 0xac, 0x64, 0x9f, 0x15, 0x40,
	0xcf, 0xe0, 0x68, 0xa1, 0xb9, 0x87, 0x63, 0xb2, 0x9a, 0xd0, 0x80, 0xac, 0xb0, 0x80, 0xa0, 0xd2,
	0x99, 0xb0, 0x30, 0x1d, 0x64, 0xa6, 0x26, 0x6d, 0x05, 0xf8, 0xa2, 0x80, 0x35, 0xc0, 0x05, 0x7b,
	0x17, 0x6f, 0x97, 0x0f, 0xb0, 0x49, 0x55, 0x0e, 0xb0, 0x0d, 0x5b, 0xee, 0x5f, 0x15, 0x40, 0x3b,
	0xf0, 0x74, 0x81, 0x1a, 0x1f, 0x0b, 0x4d, 0x95, 0xd5, 0x33, 0x20, 0xed, 0x3d, 0x5d, 0x8b, 0xb5,
	0xac, 0xbf, 0x1d, 0x6a, 0x5c, 0x93, 0xab, 0x38, 0x4d, 0x51, 0x65, 0xbd, 0x8c, 0xaa, 0x6c, 0xdc,
	0x86, 0x2d, 0xf7, 0xef, 0x25, 0x8d, 0x2f, 0xb2, 0xcd, 0xea, 0xc6, 0x17, 0xd9, 0x66, 0xed, 0xc6,
	0x73, 0xd6, 0xb2, 0xfe, 0x71, 0x78, 0x18, 0x34, 0xda, 0x24, 0x11, 0x96, 0x94, 0xb3, 0xf4, 0x11,
	0x15, 0x69, 0xf9, 0x30, 0xf4, 0xc4, 0x2b, 0x87, 0xa1, 0x44, 0x65, 0x25, 0xfb, 0xa9, 0x00, 0x7a,
	0xe3, 0xc0, 0xb1, 0x42, 0x74, 0x9b, 0x44, 0x44, 0x12, 0x53, 0x82, 0x4a, 0xf7, 0x80, 0xa3, 0xac,
	0xce, 0x34, 0x37, 0x88, 0xc4, 0x0a, 0xf4, 0xab, 0x64, 0x8d, 0x2c, 0x33, 0x2a, 0xab, 0xd7, 0x48,
	0x46, 0xd5, 0x5e, 0x23, 0x1d, 0xd8, 0x72, 0xff, 0xad, 0x00, 0x12, 0x70, 0xa4, 0x40, 0x1b, 0x38,
	0xd9, 0xcc, 0x77, 0xf4, 0x4b, 0x65, 0xc5, 0x34, 0xa1, 0x5d, 0x27, 0xab, 0x41, 0xcb, 0xf1, 0x8f,
	0x02, 0xe8, 0xa9, 0x71, 0xb2, 0x65, 0x93, 0x2b, 0x22, 0x2c, 0xc9, 0xc1, 0x96, 0x3d, 0xd5, 0x6f,
	0xc4, 0x0d, 0xb0, 0x49, 0xb6, 0xdc, 0xe9, 0xda, 0x6c, 0x2a, 0x0e, 0xec, 0xff, 0x2a, 0x80, 0x5e,
	0x3b, 0x70, 0xdc, 0xdc, 0xd7, 0xb3, 0x33, 0xb0, 0x50, 0xe4, 0xdd, 0xfb, 0x7d, 0x4e, 0x81, 0x23,
	0x74, 0x96, 0x64, 0x76, 0x30, 0x41, 0x11, 0xe7, 0x9f, 0x02, 0xae, 0xbb, 0xb7, 0x3b, 0x71, 0x2a,
	0xd7, 0xce, 0x64, 0xda, 0x99, 0x88, 0x87, 0x34, 0x78, 0xb9, 0x3b, 0x31, 0x14, 0xf2, 0x5b, 0x0f,
	0xe0, 0x68, 0xc0, 0x63, 0x6f, 0xab, 0xfd, 0x18, 0x33, 0x4f, 0xca, 0xce, 0xdd, 0x28, 0xbb, 0x66,
	0x3d, 0x5c, 0x08, 0x79, 0x84, 0x59, 0xe8, 0xcd, 0xcf, 0x49, 0xe9, 0x05, 0x3c, 0xf6, 0xf3, 0x57,
	0x01, 0x8f, 0x7c, 0x2c, 0x84, 0x6f, 0xdf, 0xd8, 0x6e, 0x74, 0x7f, 0xee, 0x0f, 0x1d, 0x6b, 0xae,
	0x36, 0x36, 0x4e, 0xe4, 0xf4, 0xb5, 0xff, 0x01, 0x00, 0x00, 0xff, 0xff, 0x38, 0xbb, 0xbf, 0xd3,
	0xdb, 0x09, 0x00, 0x00,
}
