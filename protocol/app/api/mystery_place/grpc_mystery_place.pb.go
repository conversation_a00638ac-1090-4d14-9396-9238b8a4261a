// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/mystery_place/grpc_mystery_place.proto

package mystery_place // import "golang.52tt.com/protocol/app/api/mystery_place"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import mystery_place_logic "golang.52tt.com/protocol/app/mystery-place-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MysteryPlaceLogicClient is the client API for MysteryPlaceLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MysteryPlaceLogicClient interface {
	ListScenarioInfo(ctx context.Context, in *mystery_place_logic.ListScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.ListScenarioInfoResp, error)
	GetScenarioInfo(ctx context.Context, in *mystery_place_logic.GetScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetScenarioInfoResp, error)
	GetLoginTask(ctx context.Context, in *mystery_place_logic.GetLoginTaskReq, opts ...grpc.CallOption) (*mystery_place_logic.GetLoginTaskResp, error)
	ReceiveLoginTaskAward(ctx context.Context, in *mystery_place_logic.ReceiveLoginTaskAwardReq, opts ...grpc.CallOption) (*mystery_place_logic.ReceiveLoginTaskAwardResp, error)
	GetBalanceInfo(ctx context.Context, in *mystery_place_logic.GetBalanceInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetBalanceInfoResp, error)
	CheckScenarioRoom(ctx context.Context, in *mystery_place_logic.CheckScenarioRoomReq, opts ...grpc.CallOption) (*mystery_place_logic.CheckScenarioRoomResp, error)
	Invite2MyRoom(ctx context.Context, in *mystery_place_logic.Invite2MyRoomReq, opts ...grpc.CallOption) (*mystery_place_logic.Invite2MyRoomResp, error)
	GetInvite2MyRoomResult(ctx context.Context, in *mystery_place_logic.GetInvite2MyRoomResultReq, opts ...grpc.CallOption) (*mystery_place_logic.GetInvite2MyRoomResultResp, error)
	ListRecommendedScenarioSimpleInfo(ctx context.Context, in *mystery_place_logic.ListRecommendedScenarioSimpleInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.ListRecommendedScenarioSimpleInfoResp, error)
	GetRecommendedScenarioDetailInfo(ctx context.Context, in *mystery_place_logic.GetRecommendedScenarioDetailInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetRecommendedScenarioDetailInfoResp, error)
	GetRoomShareLinkByTabId(ctx context.Context, in *mystery_place_logic.GetRoomShareLinkByTabIdReq, opts ...grpc.CallOption) (*mystery_place_logic.GetRoomShareLinkByTabIdResp, error)
	MysteryPlaceChannelList(ctx context.Context, in *mystery_place_logic.MysteryPlaceChannelListReq, opts ...grpc.CallOption) (*mystery_place_logic.MysteryPlaceChannelListResp, error)
	GetCommentPageParams(ctx context.Context, in *mystery_place_logic.GetCommentPageParamsReq, opts ...grpc.CallOption) (*mystery_place_logic.GetCommentPageParamsResp, error)
	CommentToScenario(ctx context.Context, in *mystery_place_logic.CommentToScenarioReq, opts ...grpc.CallOption) (*mystery_place_logic.CommentToScenarioResp, error)
	GetNewScenarioTip(ctx context.Context, in *mystery_place_logic.GetNewScenarioTipReq, opts ...grpc.CallOption) (*mystery_place_logic.GetNewScenarioTipResp, error)
	MarkNewScenarioTipRead(ctx context.Context, in *mystery_place_logic.MarkNewScenarioTipReadReq, opts ...grpc.CallOption) (*mystery_place_logic.MarkNewScenarioTipReadResp, error)
	MysteryPlaceClientConfig(ctx context.Context, in *mystery_place_logic.MysteryPlaceClientConfigReq, opts ...grpc.CallOption) (*mystery_place_logic.MysteryPlaceClientConfigResp, error)
	GetPlayedScenarioRecordList(ctx context.Context, in *mystery_place_logic.GetPlayedScenarioRecordListReq, opts ...grpc.CallOption) (*mystery_place_logic.GetPlayedScenarioRecordListResp, error)
	GetScenarioChapterSummary(ctx context.Context, in *mystery_place_logic.GetScenarioChapterSummaryReq, opts ...grpc.CallOption) (*mystery_place_logic.GetScenarioChapterSummaryResp, error)
	SetPlayedScenarioRecordVisibility(ctx context.Context, in *mystery_place_logic.SetPlayedScenarioRecordVisibilityReq, opts ...grpc.CallOption) (*mystery_place_logic.SetPlayedScenarioRecordVisibilityResp, error)
	GetPlayedScenarioRecordDetail(ctx context.Context, in *mystery_place_logic.GetPlayedScenarioRecordDetailReq, opts ...grpc.CallOption) (*mystery_place_logic.GetPlayedScenarioRecordDetailResp, error)
	GetRcmdMiJingTab(ctx context.Context, in *mystery_place_logic.GetRcmdMiJingTabReq, opts ...grpc.CallOption) (*mystery_place_logic.GetRcmdMiJingTabResp, error)
	ListRecommendedScenarioDetailInfo(ctx context.Context, in *mystery_place_logic.ListRecommendedScenarioDetailInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.ListRecommendedScenarioDetailInfoResp, error)
	IsUserHasScenarioFreeCoupons(ctx context.Context, in *mystery_place_logic.IsUserHasScenarioFreeCouponsReq, opts ...grpc.CallOption) (*mystery_place_logic.IsUserHasScenarioFreeCouponsResp, error)
	HomePageBigTofu(ctx context.Context, in *mystery_place_logic.HomePageBigTofuReq, opts ...grpc.CallOption) (*mystery_place_logic.HomePageBigTofuResp, error)
	HomePageRightTofu(ctx context.Context, in *mystery_place_logic.HomePageRightTofuReq, opts ...grpc.CallOption) (*mystery_place_logic.HomePageRightTofuResp, error)
	// deprecated (写错名但又删不了的)
	GetChannelScenarioInfoReq(ctx context.Context, in *mystery_place_logic.GetChannelScenarioInfoReqReq, opts ...grpc.CallOption) (*mystery_place_logic.GetChannelScenarioInfoReqResp, error)
	// 获取房间剧本信息
	GetChannelScenarioInfo(ctx context.Context, in *mystery_place_logic.GetChannelScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetChannelScenarioInfoResp, error)
}

type mysteryPlaceLogicClient struct {
	cc *grpc.ClientConn
}

func NewMysteryPlaceLogicClient(cc *grpc.ClientConn) MysteryPlaceLogicClient {
	return &mysteryPlaceLogicClient{cc}
}

func (c *mysteryPlaceLogicClient) ListScenarioInfo(ctx context.Context, in *mystery_place_logic.ListScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.ListScenarioInfoResp, error) {
	out := new(mystery_place_logic.ListScenarioInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/ListScenarioInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetScenarioInfo(ctx context.Context, in *mystery_place_logic.GetScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetScenarioInfoResp, error) {
	out := new(mystery_place_logic.GetScenarioInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/GetScenarioInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetLoginTask(ctx context.Context, in *mystery_place_logic.GetLoginTaskReq, opts ...grpc.CallOption) (*mystery_place_logic.GetLoginTaskResp, error) {
	out := new(mystery_place_logic.GetLoginTaskResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/GetLoginTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) ReceiveLoginTaskAward(ctx context.Context, in *mystery_place_logic.ReceiveLoginTaskAwardReq, opts ...grpc.CallOption) (*mystery_place_logic.ReceiveLoginTaskAwardResp, error) {
	out := new(mystery_place_logic.ReceiveLoginTaskAwardResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/ReceiveLoginTaskAward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetBalanceInfo(ctx context.Context, in *mystery_place_logic.GetBalanceInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetBalanceInfoResp, error) {
	out := new(mystery_place_logic.GetBalanceInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/GetBalanceInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) CheckScenarioRoom(ctx context.Context, in *mystery_place_logic.CheckScenarioRoomReq, opts ...grpc.CallOption) (*mystery_place_logic.CheckScenarioRoomResp, error) {
	out := new(mystery_place_logic.CheckScenarioRoomResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/CheckScenarioRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) Invite2MyRoom(ctx context.Context, in *mystery_place_logic.Invite2MyRoomReq, opts ...grpc.CallOption) (*mystery_place_logic.Invite2MyRoomResp, error) {
	out := new(mystery_place_logic.Invite2MyRoomResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/Invite2MyRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetInvite2MyRoomResult(ctx context.Context, in *mystery_place_logic.GetInvite2MyRoomResultReq, opts ...grpc.CallOption) (*mystery_place_logic.GetInvite2MyRoomResultResp, error) {
	out := new(mystery_place_logic.GetInvite2MyRoomResultResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/GetInvite2MyRoomResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) ListRecommendedScenarioSimpleInfo(ctx context.Context, in *mystery_place_logic.ListRecommendedScenarioSimpleInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.ListRecommendedScenarioSimpleInfoResp, error) {
	out := new(mystery_place_logic.ListRecommendedScenarioSimpleInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/ListRecommendedScenarioSimpleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetRecommendedScenarioDetailInfo(ctx context.Context, in *mystery_place_logic.GetRecommendedScenarioDetailInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetRecommendedScenarioDetailInfoResp, error) {
	out := new(mystery_place_logic.GetRecommendedScenarioDetailInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/GetRecommendedScenarioDetailInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetRoomShareLinkByTabId(ctx context.Context, in *mystery_place_logic.GetRoomShareLinkByTabIdReq, opts ...grpc.CallOption) (*mystery_place_logic.GetRoomShareLinkByTabIdResp, error) {
	out := new(mystery_place_logic.GetRoomShareLinkByTabIdResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/GetRoomShareLinkByTabId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) MysteryPlaceChannelList(ctx context.Context, in *mystery_place_logic.MysteryPlaceChannelListReq, opts ...grpc.CallOption) (*mystery_place_logic.MysteryPlaceChannelListResp, error) {
	out := new(mystery_place_logic.MysteryPlaceChannelListResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/MysteryPlaceChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetCommentPageParams(ctx context.Context, in *mystery_place_logic.GetCommentPageParamsReq, opts ...grpc.CallOption) (*mystery_place_logic.GetCommentPageParamsResp, error) {
	out := new(mystery_place_logic.GetCommentPageParamsResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/GetCommentPageParams", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) CommentToScenario(ctx context.Context, in *mystery_place_logic.CommentToScenarioReq, opts ...grpc.CallOption) (*mystery_place_logic.CommentToScenarioResp, error) {
	out := new(mystery_place_logic.CommentToScenarioResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/CommentToScenario", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetNewScenarioTip(ctx context.Context, in *mystery_place_logic.GetNewScenarioTipReq, opts ...grpc.CallOption) (*mystery_place_logic.GetNewScenarioTipResp, error) {
	out := new(mystery_place_logic.GetNewScenarioTipResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/GetNewScenarioTip", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) MarkNewScenarioTipRead(ctx context.Context, in *mystery_place_logic.MarkNewScenarioTipReadReq, opts ...grpc.CallOption) (*mystery_place_logic.MarkNewScenarioTipReadResp, error) {
	out := new(mystery_place_logic.MarkNewScenarioTipReadResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/MarkNewScenarioTipRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) MysteryPlaceClientConfig(ctx context.Context, in *mystery_place_logic.MysteryPlaceClientConfigReq, opts ...grpc.CallOption) (*mystery_place_logic.MysteryPlaceClientConfigResp, error) {
	out := new(mystery_place_logic.MysteryPlaceClientConfigResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/MysteryPlaceClientConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetPlayedScenarioRecordList(ctx context.Context, in *mystery_place_logic.GetPlayedScenarioRecordListReq, opts ...grpc.CallOption) (*mystery_place_logic.GetPlayedScenarioRecordListResp, error) {
	out := new(mystery_place_logic.GetPlayedScenarioRecordListResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/GetPlayedScenarioRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetScenarioChapterSummary(ctx context.Context, in *mystery_place_logic.GetScenarioChapterSummaryReq, opts ...grpc.CallOption) (*mystery_place_logic.GetScenarioChapterSummaryResp, error) {
	out := new(mystery_place_logic.GetScenarioChapterSummaryResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/GetScenarioChapterSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) SetPlayedScenarioRecordVisibility(ctx context.Context, in *mystery_place_logic.SetPlayedScenarioRecordVisibilityReq, opts ...grpc.CallOption) (*mystery_place_logic.SetPlayedScenarioRecordVisibilityResp, error) {
	out := new(mystery_place_logic.SetPlayedScenarioRecordVisibilityResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/SetPlayedScenarioRecordVisibility", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetPlayedScenarioRecordDetail(ctx context.Context, in *mystery_place_logic.GetPlayedScenarioRecordDetailReq, opts ...grpc.CallOption) (*mystery_place_logic.GetPlayedScenarioRecordDetailResp, error) {
	out := new(mystery_place_logic.GetPlayedScenarioRecordDetailResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/GetPlayedScenarioRecordDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetRcmdMiJingTab(ctx context.Context, in *mystery_place_logic.GetRcmdMiJingTabReq, opts ...grpc.CallOption) (*mystery_place_logic.GetRcmdMiJingTabResp, error) {
	out := new(mystery_place_logic.GetRcmdMiJingTabResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/GetRcmdMiJingTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) ListRecommendedScenarioDetailInfo(ctx context.Context, in *mystery_place_logic.ListRecommendedScenarioDetailInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.ListRecommendedScenarioDetailInfoResp, error) {
	out := new(mystery_place_logic.ListRecommendedScenarioDetailInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/ListRecommendedScenarioDetailInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) IsUserHasScenarioFreeCoupons(ctx context.Context, in *mystery_place_logic.IsUserHasScenarioFreeCouponsReq, opts ...grpc.CallOption) (*mystery_place_logic.IsUserHasScenarioFreeCouponsResp, error) {
	out := new(mystery_place_logic.IsUserHasScenarioFreeCouponsResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/IsUserHasScenarioFreeCoupons", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) HomePageBigTofu(ctx context.Context, in *mystery_place_logic.HomePageBigTofuReq, opts ...grpc.CallOption) (*mystery_place_logic.HomePageBigTofuResp, error) {
	out := new(mystery_place_logic.HomePageBigTofuResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/HomePageBigTofu", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) HomePageRightTofu(ctx context.Context, in *mystery_place_logic.HomePageRightTofuReq, opts ...grpc.CallOption) (*mystery_place_logic.HomePageRightTofuResp, error) {
	out := new(mystery_place_logic.HomePageRightTofuResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/HomePageRightTofu", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetChannelScenarioInfoReq(ctx context.Context, in *mystery_place_logic.GetChannelScenarioInfoReqReq, opts ...grpc.CallOption) (*mystery_place_logic.GetChannelScenarioInfoReqResp, error) {
	out := new(mystery_place_logic.GetChannelScenarioInfoReqResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/GetChannelScenarioInfoReq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetChannelScenarioInfo(ctx context.Context, in *mystery_place_logic.GetChannelScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetChannelScenarioInfoResp, error) {
	out := new(mystery_place_logic.GetChannelScenarioInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.mystery_place.MysteryPlaceLogic/GetChannelScenarioInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MysteryPlaceLogicServer is the server API for MysteryPlaceLogic service.
type MysteryPlaceLogicServer interface {
	ListScenarioInfo(context.Context, *mystery_place_logic.ListScenarioInfoReq) (*mystery_place_logic.ListScenarioInfoResp, error)
	GetScenarioInfo(context.Context, *mystery_place_logic.GetScenarioInfoReq) (*mystery_place_logic.GetScenarioInfoResp, error)
	GetLoginTask(context.Context, *mystery_place_logic.GetLoginTaskReq) (*mystery_place_logic.GetLoginTaskResp, error)
	ReceiveLoginTaskAward(context.Context, *mystery_place_logic.ReceiveLoginTaskAwardReq) (*mystery_place_logic.ReceiveLoginTaskAwardResp, error)
	GetBalanceInfo(context.Context, *mystery_place_logic.GetBalanceInfoReq) (*mystery_place_logic.GetBalanceInfoResp, error)
	CheckScenarioRoom(context.Context, *mystery_place_logic.CheckScenarioRoomReq) (*mystery_place_logic.CheckScenarioRoomResp, error)
	Invite2MyRoom(context.Context, *mystery_place_logic.Invite2MyRoomReq) (*mystery_place_logic.Invite2MyRoomResp, error)
	GetInvite2MyRoomResult(context.Context, *mystery_place_logic.GetInvite2MyRoomResultReq) (*mystery_place_logic.GetInvite2MyRoomResultResp, error)
	ListRecommendedScenarioSimpleInfo(context.Context, *mystery_place_logic.ListRecommendedScenarioSimpleInfoReq) (*mystery_place_logic.ListRecommendedScenarioSimpleInfoResp, error)
	GetRecommendedScenarioDetailInfo(context.Context, *mystery_place_logic.GetRecommendedScenarioDetailInfoReq) (*mystery_place_logic.GetRecommendedScenarioDetailInfoResp, error)
	GetRoomShareLinkByTabId(context.Context, *mystery_place_logic.GetRoomShareLinkByTabIdReq) (*mystery_place_logic.GetRoomShareLinkByTabIdResp, error)
	MysteryPlaceChannelList(context.Context, *mystery_place_logic.MysteryPlaceChannelListReq) (*mystery_place_logic.MysteryPlaceChannelListResp, error)
	GetCommentPageParams(context.Context, *mystery_place_logic.GetCommentPageParamsReq) (*mystery_place_logic.GetCommentPageParamsResp, error)
	CommentToScenario(context.Context, *mystery_place_logic.CommentToScenarioReq) (*mystery_place_logic.CommentToScenarioResp, error)
	GetNewScenarioTip(context.Context, *mystery_place_logic.GetNewScenarioTipReq) (*mystery_place_logic.GetNewScenarioTipResp, error)
	MarkNewScenarioTipRead(context.Context, *mystery_place_logic.MarkNewScenarioTipReadReq) (*mystery_place_logic.MarkNewScenarioTipReadResp, error)
	MysteryPlaceClientConfig(context.Context, *mystery_place_logic.MysteryPlaceClientConfigReq) (*mystery_place_logic.MysteryPlaceClientConfigResp, error)
	GetPlayedScenarioRecordList(context.Context, *mystery_place_logic.GetPlayedScenarioRecordListReq) (*mystery_place_logic.GetPlayedScenarioRecordListResp, error)
	GetScenarioChapterSummary(context.Context, *mystery_place_logic.GetScenarioChapterSummaryReq) (*mystery_place_logic.GetScenarioChapterSummaryResp, error)
	SetPlayedScenarioRecordVisibility(context.Context, *mystery_place_logic.SetPlayedScenarioRecordVisibilityReq) (*mystery_place_logic.SetPlayedScenarioRecordVisibilityResp, error)
	GetPlayedScenarioRecordDetail(context.Context, *mystery_place_logic.GetPlayedScenarioRecordDetailReq) (*mystery_place_logic.GetPlayedScenarioRecordDetailResp, error)
	GetRcmdMiJingTab(context.Context, *mystery_place_logic.GetRcmdMiJingTabReq) (*mystery_place_logic.GetRcmdMiJingTabResp, error)
	ListRecommendedScenarioDetailInfo(context.Context, *mystery_place_logic.ListRecommendedScenarioDetailInfoReq) (*mystery_place_logic.ListRecommendedScenarioDetailInfoResp, error)
	IsUserHasScenarioFreeCoupons(context.Context, *mystery_place_logic.IsUserHasScenarioFreeCouponsReq) (*mystery_place_logic.IsUserHasScenarioFreeCouponsResp, error)
	HomePageBigTofu(context.Context, *mystery_place_logic.HomePageBigTofuReq) (*mystery_place_logic.HomePageBigTofuResp, error)
	HomePageRightTofu(context.Context, *mystery_place_logic.HomePageRightTofuReq) (*mystery_place_logic.HomePageRightTofuResp, error)
	// deprecated (写错名但又删不了的)
	GetChannelScenarioInfoReq(context.Context, *mystery_place_logic.GetChannelScenarioInfoReqReq) (*mystery_place_logic.GetChannelScenarioInfoReqResp, error)
	// 获取房间剧本信息
	GetChannelScenarioInfo(context.Context, *mystery_place_logic.GetChannelScenarioInfoReq) (*mystery_place_logic.GetChannelScenarioInfoResp, error)
}

func RegisterMysteryPlaceLogicServer(s *grpc.Server, srv MysteryPlaceLogicServer) {
	s.RegisterService(&_MysteryPlaceLogic_serviceDesc, srv)
}

func _MysteryPlaceLogic_ListScenarioInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.ListScenarioInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).ListScenarioInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/ListScenarioInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).ListScenarioInfo(ctx, req.(*mystery_place_logic.ListScenarioInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetScenarioInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetScenarioInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetScenarioInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/GetScenarioInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetScenarioInfo(ctx, req.(*mystery_place_logic.GetScenarioInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetLoginTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetLoginTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetLoginTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/GetLoginTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetLoginTask(ctx, req.(*mystery_place_logic.GetLoginTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_ReceiveLoginTaskAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.ReceiveLoginTaskAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).ReceiveLoginTaskAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/ReceiveLoginTaskAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).ReceiveLoginTaskAward(ctx, req.(*mystery_place_logic.ReceiveLoginTaskAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetBalanceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetBalanceInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetBalanceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/GetBalanceInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetBalanceInfo(ctx, req.(*mystery_place_logic.GetBalanceInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_CheckScenarioRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.CheckScenarioRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).CheckScenarioRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/CheckScenarioRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).CheckScenarioRoom(ctx, req.(*mystery_place_logic.CheckScenarioRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_Invite2MyRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.Invite2MyRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).Invite2MyRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/Invite2MyRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).Invite2MyRoom(ctx, req.(*mystery_place_logic.Invite2MyRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetInvite2MyRoomResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetInvite2MyRoomResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetInvite2MyRoomResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/GetInvite2MyRoomResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetInvite2MyRoomResult(ctx, req.(*mystery_place_logic.GetInvite2MyRoomResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_ListRecommendedScenarioSimpleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.ListRecommendedScenarioSimpleInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).ListRecommendedScenarioSimpleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/ListRecommendedScenarioSimpleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).ListRecommendedScenarioSimpleInfo(ctx, req.(*mystery_place_logic.ListRecommendedScenarioSimpleInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetRecommendedScenarioDetailInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetRecommendedScenarioDetailInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetRecommendedScenarioDetailInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/GetRecommendedScenarioDetailInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetRecommendedScenarioDetailInfo(ctx, req.(*mystery_place_logic.GetRecommendedScenarioDetailInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetRoomShareLinkByTabId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetRoomShareLinkByTabIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetRoomShareLinkByTabId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/GetRoomShareLinkByTabId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetRoomShareLinkByTabId(ctx, req.(*mystery_place_logic.GetRoomShareLinkByTabIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_MysteryPlaceChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.MysteryPlaceChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).MysteryPlaceChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/MysteryPlaceChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).MysteryPlaceChannelList(ctx, req.(*mystery_place_logic.MysteryPlaceChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetCommentPageParams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetCommentPageParamsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetCommentPageParams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/GetCommentPageParams",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetCommentPageParams(ctx, req.(*mystery_place_logic.GetCommentPageParamsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_CommentToScenario_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.CommentToScenarioReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).CommentToScenario(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/CommentToScenario",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).CommentToScenario(ctx, req.(*mystery_place_logic.CommentToScenarioReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetNewScenarioTip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetNewScenarioTipReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetNewScenarioTip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/GetNewScenarioTip",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetNewScenarioTip(ctx, req.(*mystery_place_logic.GetNewScenarioTipReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_MarkNewScenarioTipRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.MarkNewScenarioTipReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).MarkNewScenarioTipRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/MarkNewScenarioTipRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).MarkNewScenarioTipRead(ctx, req.(*mystery_place_logic.MarkNewScenarioTipReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_MysteryPlaceClientConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.MysteryPlaceClientConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).MysteryPlaceClientConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/MysteryPlaceClientConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).MysteryPlaceClientConfig(ctx, req.(*mystery_place_logic.MysteryPlaceClientConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetPlayedScenarioRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetPlayedScenarioRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetPlayedScenarioRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/GetPlayedScenarioRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetPlayedScenarioRecordList(ctx, req.(*mystery_place_logic.GetPlayedScenarioRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetScenarioChapterSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetScenarioChapterSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetScenarioChapterSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/GetScenarioChapterSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetScenarioChapterSummary(ctx, req.(*mystery_place_logic.GetScenarioChapterSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_SetPlayedScenarioRecordVisibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.SetPlayedScenarioRecordVisibilityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).SetPlayedScenarioRecordVisibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/SetPlayedScenarioRecordVisibility",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).SetPlayedScenarioRecordVisibility(ctx, req.(*mystery_place_logic.SetPlayedScenarioRecordVisibilityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetPlayedScenarioRecordDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetPlayedScenarioRecordDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetPlayedScenarioRecordDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/GetPlayedScenarioRecordDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetPlayedScenarioRecordDetail(ctx, req.(*mystery_place_logic.GetPlayedScenarioRecordDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetRcmdMiJingTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetRcmdMiJingTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetRcmdMiJingTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/GetRcmdMiJingTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetRcmdMiJingTab(ctx, req.(*mystery_place_logic.GetRcmdMiJingTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_ListRecommendedScenarioDetailInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.ListRecommendedScenarioDetailInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).ListRecommendedScenarioDetailInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/ListRecommendedScenarioDetailInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).ListRecommendedScenarioDetailInfo(ctx, req.(*mystery_place_logic.ListRecommendedScenarioDetailInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_IsUserHasScenarioFreeCoupons_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.IsUserHasScenarioFreeCouponsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).IsUserHasScenarioFreeCoupons(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/IsUserHasScenarioFreeCoupons",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).IsUserHasScenarioFreeCoupons(ctx, req.(*mystery_place_logic.IsUserHasScenarioFreeCouponsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_HomePageBigTofu_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.HomePageBigTofuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).HomePageBigTofu(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/HomePageBigTofu",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).HomePageBigTofu(ctx, req.(*mystery_place_logic.HomePageBigTofuReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_HomePageRightTofu_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.HomePageRightTofuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).HomePageRightTofu(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/HomePageRightTofu",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).HomePageRightTofu(ctx, req.(*mystery_place_logic.HomePageRightTofuReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetChannelScenarioInfoReq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetChannelScenarioInfoReqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetChannelScenarioInfoReq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/GetChannelScenarioInfoReq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetChannelScenarioInfoReq(ctx, req.(*mystery_place_logic.GetChannelScenarioInfoReqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetChannelScenarioInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetChannelScenarioInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetChannelScenarioInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mystery_place.MysteryPlaceLogic/GetChannelScenarioInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetChannelScenarioInfo(ctx, req.(*mystery_place_logic.GetChannelScenarioInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MysteryPlaceLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.mystery_place.MysteryPlaceLogic",
	HandlerType: (*MysteryPlaceLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListScenarioInfo",
			Handler:    _MysteryPlaceLogic_ListScenarioInfo_Handler,
		},
		{
			MethodName: "GetScenarioInfo",
			Handler:    _MysteryPlaceLogic_GetScenarioInfo_Handler,
		},
		{
			MethodName: "GetLoginTask",
			Handler:    _MysteryPlaceLogic_GetLoginTask_Handler,
		},
		{
			MethodName: "ReceiveLoginTaskAward",
			Handler:    _MysteryPlaceLogic_ReceiveLoginTaskAward_Handler,
		},
		{
			MethodName: "GetBalanceInfo",
			Handler:    _MysteryPlaceLogic_GetBalanceInfo_Handler,
		},
		{
			MethodName: "CheckScenarioRoom",
			Handler:    _MysteryPlaceLogic_CheckScenarioRoom_Handler,
		},
		{
			MethodName: "Invite2MyRoom",
			Handler:    _MysteryPlaceLogic_Invite2MyRoom_Handler,
		},
		{
			MethodName: "GetInvite2MyRoomResult",
			Handler:    _MysteryPlaceLogic_GetInvite2MyRoomResult_Handler,
		},
		{
			MethodName: "ListRecommendedScenarioSimpleInfo",
			Handler:    _MysteryPlaceLogic_ListRecommendedScenarioSimpleInfo_Handler,
		},
		{
			MethodName: "GetRecommendedScenarioDetailInfo",
			Handler:    _MysteryPlaceLogic_GetRecommendedScenarioDetailInfo_Handler,
		},
		{
			MethodName: "GetRoomShareLinkByTabId",
			Handler:    _MysteryPlaceLogic_GetRoomShareLinkByTabId_Handler,
		},
		{
			MethodName: "MysteryPlaceChannelList",
			Handler:    _MysteryPlaceLogic_MysteryPlaceChannelList_Handler,
		},
		{
			MethodName: "GetCommentPageParams",
			Handler:    _MysteryPlaceLogic_GetCommentPageParams_Handler,
		},
		{
			MethodName: "CommentToScenario",
			Handler:    _MysteryPlaceLogic_CommentToScenario_Handler,
		},
		{
			MethodName: "GetNewScenarioTip",
			Handler:    _MysteryPlaceLogic_GetNewScenarioTip_Handler,
		},
		{
			MethodName: "MarkNewScenarioTipRead",
			Handler:    _MysteryPlaceLogic_MarkNewScenarioTipRead_Handler,
		},
		{
			MethodName: "MysteryPlaceClientConfig",
			Handler:    _MysteryPlaceLogic_MysteryPlaceClientConfig_Handler,
		},
		{
			MethodName: "GetPlayedScenarioRecordList",
			Handler:    _MysteryPlaceLogic_GetPlayedScenarioRecordList_Handler,
		},
		{
			MethodName: "GetScenarioChapterSummary",
			Handler:    _MysteryPlaceLogic_GetScenarioChapterSummary_Handler,
		},
		{
			MethodName: "SetPlayedScenarioRecordVisibility",
			Handler:    _MysteryPlaceLogic_SetPlayedScenarioRecordVisibility_Handler,
		},
		{
			MethodName: "GetPlayedScenarioRecordDetail",
			Handler:    _MysteryPlaceLogic_GetPlayedScenarioRecordDetail_Handler,
		},
		{
			MethodName: "GetRcmdMiJingTab",
			Handler:    _MysteryPlaceLogic_GetRcmdMiJingTab_Handler,
		},
		{
			MethodName: "ListRecommendedScenarioDetailInfo",
			Handler:    _MysteryPlaceLogic_ListRecommendedScenarioDetailInfo_Handler,
		},
		{
			MethodName: "IsUserHasScenarioFreeCoupons",
			Handler:    _MysteryPlaceLogic_IsUserHasScenarioFreeCoupons_Handler,
		},
		{
			MethodName: "HomePageBigTofu",
			Handler:    _MysteryPlaceLogic_HomePageBigTofu_Handler,
		},
		{
			MethodName: "HomePageRightTofu",
			Handler:    _MysteryPlaceLogic_HomePageRightTofu_Handler,
		},
		{
			MethodName: "GetChannelScenarioInfoReq",
			Handler:    _MysteryPlaceLogic_GetChannelScenarioInfoReq_Handler,
		},
		{
			MethodName: "GetChannelScenarioInfo",
			Handler:    _MysteryPlaceLogic_GetChannelScenarioInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/mystery_place/grpc_mystery_place.proto",
}

func init() {
	proto.RegisterFile("api/mystery_place/grpc_mystery_place.proto", fileDescriptor_grpc_mystery_place_64ab13bab2feca9a)
}

var fileDescriptor_grpc_mystery_place_64ab13bab2feca9a = []byte{
	// 1029 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x58, 0xcb, 0x6f, 0x1b, 0x45,
	0x1c, 0xd6, 0x0e, 0x08, 0x55, 0x23, 0x5e, 0x1d, 0x4a, 0xa1, 0x86, 0x0a, 0xc3, 0x05, 0x9a, 0x36,
	0x76, 0x9b, 0xb6, 0x50, 0x68, 0x7a, 0x20, 0x46, 0xb8, 0x41, 0x09, 0xb2, 0x6c, 0xc3, 0x01, 0x21,
	0x45, 0x93, 0xf5, 0x2f, 0xeb, 0x91, 0x77, 0x67, 0x36, 0xbb, 0xe3, 0x06, 0x73, 0x8a, 0x8a, 0x40,
	0x02, 0x21, 0x21, 0xf5, 0xc4, 0x08, 0x09, 0x41, 0x79, 0x14, 0x0a, 0xdc, 0x78, 0x73, 0x40, 0xdc,
	0x40, 0xf0, 0x47, 0xa1, 0xd9, 0xb5, 0x27, 0x5e, 0x7b, 0x77, 0x62, 0xbb, 0xb7, 0x6c, 0xf6, 0xfb,
	0xe6, 0xfb, 0xe6, 0x37, 0x3b, 0xbf, 0x87, 0xf1, 0x12, 0x0d, 0x59, 0x35, 0x18, 0xc4, 0x12, 0xa2,
	0xc1, 0x56, 0xe8, 0x53, 0x17, 0xaa, 0x5e, 0x14, 0xba, 0x5b, 0x99, 0x7f, 0x55, 0xc2, 0x48, 0x48,
	0x41, 0x8e, 0x79, 0xb4, 0x42, 0x43, 0x56, 0xc9, 0xbc, 0x2b, 0x95, 0x86, 0x8f, 0xcb, 0xc9, 0xe3,
	0xb2, 0x2f, 0x3c, 0xe6, 0x6e, 0xa5, 0x8c, 0xd2, 0x49, 0xbd, 0x3a, 0xbc, 0x25, 0x81, 0xc7, 0x4c,
	0xf0, 0x83, 0xbf, 0xd2, 0xd7, 0x2b, 0xef, 0x3e, 0x81, 0x8f, 0x6e, 0xa6, 0xec, 0x86, 0x26, 0x6f,
	0x68, 0x2e, 0xd9, 0xc3, 0x0f, 0x6e, 0xb0, 0x58, 0xb6, 0x5c, 0xe0, 0x34, 0x62, 0x62, 0x9d, 0xef,
	0x08, 0x72, 0xba, 0xe2, 0xd1, 0xac, 0xee, 0x56, 0x22, 0x54, 0x99, 0x44, 0x36, 0x61, 0xb7, 0x74,
	0x66, 0x76, 0x70, 0x1c, 0x3e, 0x75, 0xe4, 0xfa, 0x7e, 0xf9, 0xee, 0x23, 0x9f, 0x29, 0x44, 0x24,
	0x7e, 0xa0, 0x0e, 0x59, 0xdd, 0xa5, 0xa2, 0xa5, 0x26, 0x80, 0x5a, 0xf6, 0xf4, 0xcc, 0x58, 0xa3,
	0xfa, 0xb9, 0x42, 0xa4, 0x87, 0xef, 0xad, 0x83, 0xd4, 0x5b, 0xe7, 0x6d, 0x1a, 0xf7, 0xc8, 0xd3,
	0x96, 0x65, 0x0c, 0x4a, 0xeb, 0x3d, 0x33, 0x1b, 0xd0, 0x88, 0xdd, 0x54, 0x88, 0xbc, 0xe7, 0xe0,
	0x87, 0x9b, 0xe0, 0x02, 0xbb, 0x06, 0x06, 0xf2, 0xe2, 0x1e, 0x8d, 0x3a, 0xe4, 0x6c, 0xd1, 0x6a,
	0xb9, 0x70, 0xad, 0x7f, 0x6e, 0x4e, 0x86, 0x31, 0xf2, 0x85, 0x42, 0x64, 0x17, 0xdf, 0x5f, 0x07,
	0xb9, 0x46, 0x7d, 0xca, 0x5d, 0x48, 0x42, 0x7d, 0xca, 0xb2, 0x9d, 0x31, 0x9c, 0x56, 0x5e, 0x9a,
	0x15, 0x6a, 0x24, 0xbf, 0x54, 0x88, 0xbc, 0x8d, 0x8f, 0xd6, 0xba, 0xe0, 0xf6, 0x46, 0x67, 0xd1,
	0x14, 0x22, 0x20, 0x85, 0xdf, 0xca, 0x14, 0x54, 0x0b, 0x2f, 0xcf, 0x81, 0x36, 0xda, 0x5f, 0x29,
	0x44, 0x38, 0xbe, 0x6f, 0x9d, 0x5f, 0x63, 0x12, 0x56, 0x36, 0x07, 0x89, 0x6e, 0xe1, 0xe1, 0x65,
	0x60, 0x5a, 0xf3, 0xd4, 0x8c, 0x48, 0xa3, 0xf7, 0xb5, 0x42, 0xe4, 0x7d, 0x07, 0x1f, 0xaf, 0x83,
	0x9c, 0x84, 0xf4, 0x7d, 0x49, 0xce, 0x59, 0x82, 0x97, 0x83, 0xd7, 0x16, 0x56, 0xe6, 0xa5, 0x18,
	0x2f, 0xb7, 0x14, 0x22, 0xb7, 0x1d, 0xfc, 0xa4, 0xbe, 0x79, 0x4d, 0x70, 0x45, 0x10, 0x00, 0xef,
	0x40, 0x67, 0x14, 0xa9, 0x16, 0x0b, 0x42, 0x3f, 0x3d, 0xfe, 0x55, 0xdb, 0xa5, 0xb5, 0x52, 0xb5,
	0xc3, 0x2b, 0x77, 0xc0, 0x36, 0x66, 0xbf, 0x51, 0x88, 0xdc, 0x72, 0x70, 0xb9, 0x0e, 0x79, 0x94,
	0x97, 0x40, 0x52, 0xe6, 0x27, 0x5e, 0x2f, 0x5b, 0xe2, 0x61, 0x65, 0x6a, 0xab, 0xab, 0x8b, 0x93,
	0x8d, 0xd3, 0x6f, 0x15, 0x22, 0x1f, 0x3a, 0xf8, 0x11, 0x4d, 0x11, 0x22, 0x68, 0x75, 0x69, 0x04,
	0x1b, 0x8c, 0xf7, 0xd6, 0x06, 0x6d, 0xba, 0xbd, 0xde, 0x21, 0xb6, 0x03, 0xcb, 0x23, 0x68, 0x5f,
	0xe7, 0xe7, 0xe6, 0x18, 0x3b, 0xb7, 0x87, 0x76, 0xc6, 0x73, 0x79, 0xad, 0x4b, 0x39, 0x07, 0x5f,
	0xc7, 0xbf, 0xd8, 0x4e, 0x01, 0xc1, 0x6a, 0xa7, 0x90, 0x63, 0xec, 0x7c, 0xa7, 0x10, 0x79, 0xc7,
	0xc1, 0xc7, 0xea, 0x20, 0x6b, 0x49, 0x38, 0x65, 0x83, 0x7a, 0xd0, 0xa0, 0x11, 0x0d, 0x62, 0x52,
	0xb5, 0x6c, 0x73, 0x0a, 0xad, 0x8d, 0x9c, 0x9d, 0x8f, 0x60, 0x5c, 0x7c, 0x3f, 0x4c, 0x39, 0x29,
	0xa4, 0x2d, 0x46, 0x07, 0x6a, 0x49, 0x39, 0x93, 0x50, 0x7b, 0xca, 0x99, 0x46, 0x1b, 0xed, 0x1f,
	0x52, 0xed, 0x3a, 0xc8, 0x57, 0x61, 0x6f, 0xf4, 0xbe, 0xcd, 0xc2, 0x62, 0xed, 0x29, 0xa8, 0x55,
	0x3b, 0x07, 0x6d, 0xb4, 0x7f, 0x1c, 0xa6, 0x9f, 0x4d, 0x1a, 0xf5, 0x26, 0x41, 0xb4, 0x53, 0x9c,
	0x7e, 0xf2, 0xf1, 0xd6, 0xf4, 0x53, 0x44, 0x31, 0x5e, 0x7e, 0x52, 0x88, 0x7c, 0xe4, 0xe0, 0x47,
	0x33, 0xdf, 0x8c, 0xcf, 0x80, 0xcb, 0x9a, 0xe0, 0x3b, 0xcc, 0x23, 0xb3, 0x7d, 0x65, 0x63, 0x0c,
	0xed, 0xe7, 0xc2, 0xfc, 0x24, 0xe3, 0xe8, 0x67, 0x85, 0xc8, 0xc7, 0x0e, 0x7e, 0xac, 0x0e, 0xb2,
	0xe1, 0xd3, 0xc1, 0xc1, 0x3d, 0xd7, 0x57, 0x3f, 0xea, 0x24, 0xd7, 0xe5, 0x59, 0x4b, 0xd8, 0x8b,
	0x48, 0xda, 0xd7, 0x73, 0x0b, 0xf1, 0x8c, 0xb5, 0x5f, 0x14, 0x22, 0x37, 0x1c, 0x7c, 0x62, 0xac,
	0x5d, 0xa9, 0x75, 0x69, 0x28, 0x21, 0x6a, 0xf5, 0x83, 0x80, 0x46, 0x03, 0x72, 0x61, 0x86, 0x0e,
	0x27, 0x4b, 0xd1, 0xb6, 0x2e, 0x2e, 0xc0, 0x32, 0xa6, 0x7e, 0x1d, 0x16, 0x90, 0x56, 0xfe, 0x16,
	0x5e, 0x67, 0x31, 0xdb, 0x66, 0x3e, 0x93, 0x83, 0xe2, 0x02, 0x72, 0x28, 0xd5, 0x5a, 0x40, 0x66,
	0x60, 0x1b, 0xb3, 0xbf, 0x29, 0x44, 0x3e, 0x75, 0xf0, 0xc9, 0x82, 0x78, 0xa7, 0xa9, 0x9c, 0x5c,
	0x9a, 0xf3, 0x98, 0x52, 0x9a, 0x36, 0xf9, 0xfc, 0x82, 0x4c, 0x63, 0xf0, 0x77, 0x85, 0x74, 0x7b,
	0xad, 0x33, 0xba, 0x1b, 0x74, 0x36, 0xd9, 0x2b, 0x8c, 0x7b, 0x6d, 0xba, 0x4d, 0x6c, 0xad, 0x6b,
	0x06, 0x69, 0x6d, 0xaf, 0xa7, 0xc1, 0x46, 0xf8, 0x0f, 0x7b, 0x1f, 0x30, 0x56, 0x5b, 0xe7, 0xed,
	0x03, 0xb2, 0xc5, 0xf5, 0xca, 0x1d, 0xb0, 0x8d, 0xd9, 0x3f, 0x15, 0x22, 0x9f, 0x38, 0xf8, 0xf1,
	0xf5, 0xf8, 0xb5, 0x18, 0xa2, 0xab, 0x34, 0x1e, 0xa1, 0x5f, 0x8e, 0x00, 0x6a, 0xa2, 0x1f, 0x0a,
	0x1e, 0x93, 0xc2, 0xcb, 0x66, 0x63, 0x69, 0x8b, 0x97, 0x16, 0x23, 0x1a, 0x77, 0x7f, 0xa5, 0x93,
	0xca, 0x55, 0x11, 0x80, 0xae, 0x3b, 0x6b, 0xcc, 0x6b, 0x8b, 0x9d, 0x7e, 0xf1, 0xa4, 0x32, 0x01,
	0xb4, 0x4e, 0x2a, 0x53, 0x58, 0xa3, 0xfa, 0x77, 0x5a, 0x51, 0x46, 0x80, 0x26, 0xf3, 0xba, 0x32,
	0xd1, 0x3d, 0x73, 0xd8, 0x5a, 0x06, 0x6a, 0xad, 0x28, 0x39, 0x68, 0xa3, 0xfd, 0xcf, 0x41, 0x62,
	0x1a, 0x16, 0xfc, 0x89, 0xd1, 0xcb, 0x9a, 0x98, 0xf2, 0x29, 0x87, 0x25, 0xa6, 0x22, 0x96, 0x31,
	0xf5, 0xef, 0x41, 0x97, 0x9d, 0x83, 0xb5, 0x76, 0xd9, 0xf9, 0x6b, 0x5b, 0xbb, 0xec, 0x5c, 0x8a,
	0xf1, 0xf2, 0x9f, 0x42, 0xa5, 0x17, 0xae, 0xef, 0x97, 0x1f, 0xca, 0x19, 0xc5, 0x3f, 0xd8, 0x2f,
	0x23, 0x4f, 0xdc, 0xd8, 0x2f, 0x9f, 0xa8, 0x4e, 0x17, 0xa9, 0x64, 0xe0, 0xae, 0xae, 0xbd, 0x89,
	0x8f, 0xbb, 0x22, 0xa8, 0xec, 0xf6, 0xf7, 0x28, 0xaf, 0x48, 0x99, 0x4e, 0xe7, 0x7a, 0xd4, 0x7f,
	0x63, 0xd5, 0x13, 0x3e, 0xe5, 0x5e, 0xe5, 0xe2, 0x8a, 0x94, 0x15, 0x57, 0x04, 0xd5, 0xe4, 0x95,
	0x2b, 0xfc, 0x2a, 0x0d, 0xc3, 0xea, 0xd4, 0x6f, 0x07, 0x97, 0x33, 0x4f, 0x37, 0xd1, 0x5d, 0xcd,
	0x46, 0x6d, 0xfb, 0x9e, 0x84, 0x73, 0xfe, 0xff, 0x00, 0x00, 0x00, 0xff, 0xff, 0x2b, 0x3d, 0xdd,
	0x69, 0x6b, 0x10, 0x00, 0x00,
}
