// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/muse_interest_hub/grpc_muse_interest_hub.proto

package muse_interest_hub_logic // import "golang.52tt.com/protocol/app/api/muse_interest_hub_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import muse_interest_hub_logic "golang.52tt.com/protocol/app/muse-interest-hub-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MuseInterestHubLogicClient is the client API for MuseInterestHubLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MuseInterestHubLogicClient interface {
	MuseCommonReport(ctx context.Context, in *muse_interest_hub_logic.MuseCommonReportRequest, opts ...grpc.CallOption) (*muse_interest_hub_logic.MuseCommonReportResponse, error)
	GetMuseSwitchHub(ctx context.Context, in *muse_interest_hub_logic.GetMuseSwitchHubRequest, opts ...grpc.CallOption) (*muse_interest_hub_logic.GetMuseSwitchHubResponse, error)
	SetMuseSwitchHub(ctx context.Context, in *muse_interest_hub_logic.SetMuseSwitchHubRequest, opts ...grpc.CallOption) (*muse_interest_hub_logic.SetMuseSwitchHubResponse, error)
}

type museInterestHubLogicClient struct {
	cc *grpc.ClientConn
}

func NewMuseInterestHubLogicClient(cc *grpc.ClientConn) MuseInterestHubLogicClient {
	return &museInterestHubLogicClient{cc}
}

func (c *museInterestHubLogicClient) MuseCommonReport(ctx context.Context, in *muse_interest_hub_logic.MuseCommonReportRequest, opts ...grpc.CallOption) (*muse_interest_hub_logic.MuseCommonReportResponse, error) {
	out := new(muse_interest_hub_logic.MuseCommonReportResponse)
	err := c.cc.Invoke(ctx, "/ga.api.muse_interest_hub.MuseInterestHubLogic/MuseCommonReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museInterestHubLogicClient) GetMuseSwitchHub(ctx context.Context, in *muse_interest_hub_logic.GetMuseSwitchHubRequest, opts ...grpc.CallOption) (*muse_interest_hub_logic.GetMuseSwitchHubResponse, error) {
	out := new(muse_interest_hub_logic.GetMuseSwitchHubResponse)
	err := c.cc.Invoke(ctx, "/ga.api.muse_interest_hub.MuseInterestHubLogic/GetMuseSwitchHub", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museInterestHubLogicClient) SetMuseSwitchHub(ctx context.Context, in *muse_interest_hub_logic.SetMuseSwitchHubRequest, opts ...grpc.CallOption) (*muse_interest_hub_logic.SetMuseSwitchHubResponse, error) {
	out := new(muse_interest_hub_logic.SetMuseSwitchHubResponse)
	err := c.cc.Invoke(ctx, "/ga.api.muse_interest_hub.MuseInterestHubLogic/SetMuseSwitchHub", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MuseInterestHubLogicServer is the server API for MuseInterestHubLogic service.
type MuseInterestHubLogicServer interface {
	MuseCommonReport(context.Context, *muse_interest_hub_logic.MuseCommonReportRequest) (*muse_interest_hub_logic.MuseCommonReportResponse, error)
	GetMuseSwitchHub(context.Context, *muse_interest_hub_logic.GetMuseSwitchHubRequest) (*muse_interest_hub_logic.GetMuseSwitchHubResponse, error)
	SetMuseSwitchHub(context.Context, *muse_interest_hub_logic.SetMuseSwitchHubRequest) (*muse_interest_hub_logic.SetMuseSwitchHubResponse, error)
}

func RegisterMuseInterestHubLogicServer(s *grpc.Server, srv MuseInterestHubLogicServer) {
	s.RegisterService(&_MuseInterestHubLogic_serviceDesc, srv)
}

func _MuseInterestHubLogic_MuseCommonReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(muse_interest_hub_logic.MuseCommonReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseInterestHubLogicServer).MuseCommonReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.muse_interest_hub.MuseInterestHubLogic/MuseCommonReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseInterestHubLogicServer).MuseCommonReport(ctx, req.(*muse_interest_hub_logic.MuseCommonReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseInterestHubLogic_GetMuseSwitchHub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(muse_interest_hub_logic.GetMuseSwitchHubRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseInterestHubLogicServer).GetMuseSwitchHub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.muse_interest_hub.MuseInterestHubLogic/GetMuseSwitchHub",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseInterestHubLogicServer).GetMuseSwitchHub(ctx, req.(*muse_interest_hub_logic.GetMuseSwitchHubRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseInterestHubLogic_SetMuseSwitchHub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(muse_interest_hub_logic.SetMuseSwitchHubRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseInterestHubLogicServer).SetMuseSwitchHub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.muse_interest_hub.MuseInterestHubLogic/SetMuseSwitchHub",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseInterestHubLogicServer).SetMuseSwitchHub(ctx, req.(*muse_interest_hub_logic.SetMuseSwitchHubRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MuseInterestHubLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.muse_interest_hub.MuseInterestHubLogic",
	HandlerType: (*MuseInterestHubLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MuseCommonReport",
			Handler:    _MuseInterestHubLogic_MuseCommonReport_Handler,
		},
		{
			MethodName: "GetMuseSwitchHub",
			Handler:    _MuseInterestHubLogic_GetMuseSwitchHub_Handler,
		},
		{
			MethodName: "SetMuseSwitchHub",
			Handler:    _MuseInterestHubLogic_SetMuseSwitchHub_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/muse_interest_hub/grpc_muse_interest_hub.proto",
}

func init() {
	proto.RegisterFile("api/muse_interest_hub/grpc_muse_interest_hub.proto", fileDescriptor_grpc_muse_interest_hub_638244ed5b7dabcf)
}

var fileDescriptor_grpc_muse_interest_hub_638244ed5b7dabcf = []byte{
	// 321 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x92, 0x41, 0x4b, 0xc3, 0x30,
	0x14, 0xc7, 0xd9, 0x26, 0x32, 0x7a, 0x92, 0x21, 0x2a, 0xc5, 0x41, 0xf1, 0xbe, 0x04, 0x36, 0x3d,
	0x79, 0xd2, 0x1d, 0x9c, 0xa0, 0x30, 0xd6, 0x9b, 0x97, 0x92, 0x96, 0x90, 0x05, 0xd6, 0xbc, 0xac,
	0x79, 0x61, 0x1e, 0xcb, 0x2e, 0x82, 0x47, 0x3f, 0x82, 0x9f, 0x46, 0xfd, 0x40, 0xf3, 0x2a, 0x69,
	0x27, 0xc3, 0xd5, 0x0d, 0xe7, 0x2d, 0xbc, 0xff, 0xfb, 0xe5, 0xfd, 0x48, 0x9e, 0xd7, 0x65, 0x5a,
	0xd2, 0xd4, 0x1a, 0x1e, 0x49, 0x85, 0x3c, 0xe3, 0x06, 0xa3, 0xb1, 0x8d, 0xa9, 0xc8, 0x74, 0x12,
	0x55, 0xca, 0x44, 0x67, 0x80, 0xd0, 0x3a, 0x11, 0x8c, 0x30, 0x2d, 0x49, 0x25, 0xf7, 0xdb, 0x95,
	0x52, 0x34, 0x01, 0x21, 0x93, 0x12, 0xf4, 0xdb, 0x6e, 0x18, 0x7f, 0x44, 0xae, 0x8c, 0x04, 0xb5,
	0x3a, 0x95, 0x71, 0xf7, 0xb3, 0xe1, 0x1d, 0xde, 0x5b, 0xc3, 0x6f, 0x97, 0xfc, 0xc0, 0xc6, 0x77,
	0x8e, 0x6e, 0x3d, 0xd5, 0xbc, 0x03, 0x17, 0xf4, 0x21, 0x4d, 0x41, 0x8d, 0xb8, 0x86, 0x0c, 0x5b,
	0x3d, 0x22, 0x18, 0xd9, 0x34, 0x6f, 0xbd, 0x7b, 0xc4, 0xa7, 0x96, 0x1b, 0xf4, 0xcf, 0x77, 0x83,
	0x8c, 0x06, 0x65, 0xf8, 0x59, 0x73, 0x9e, 0x07, 0x7b, 0xcd, 0xb7, 0x45, 0xad, 0x30, 0xb9, 0xe1,
	0xe8, 0x3a, 0xc3, 0x99, 0xc4, 0x64, 0x3c, 0xb0, 0xf1, 0x76, 0x93, 0xf5, 0xee, 0x3f, 0x99, 0x54,
	0xa1, 0x1f, 0x26, 0xef, 0x4b, 0x93, 0x70, 0x27, 0x93, 0xf0, 0x3f, 0x26, 0xe1, 0x76, 0x93, 0x8f,
	0x45, 0xcd, 0xbf, 0x9a, 0xe7, 0xc1, 0xb1, 0xe3, 0x3b, 0xdf, 0x7c, 0x67, 0x6c, 0xe3, 0x4e, 0xc1,
	0x3f, 0xe7, 0x41, 0x5d, 0xc0, 0x4b, 0x1e, 0x9c, 0xd2, 0xd5, 0x1b, 0xaf, 0xff, 0x2f, 0xbd, 0xd6,
	0xde, 0x51, 0x02, 0x29, 0x99, 0xda, 0x19, 0x53, 0x04, 0xb1, 0xdc, 0x07, 0xb7, 0x63, 0x0f, 0x43,
	0x01, 0x13, 0xa6, 0x04, 0xb9, 0xe8, 0x22, 0x92, 0x04, 0x52, 0x5a, 0x44, 0x09, 0x4c, 0x28, 0xd3,
	0x9a, 0xfe, 0xba, 0xbc, 0xa5, 0xf9, 0xe5, 0x86, 0xfa, 0x6b, 0xbd, 0x31, 0x1a, 0xf6, 0xe3, 0xfd,
	0xe2, 0x9e, 0xde, 0x57, 0x00, 0x00, 0x00, 0xff, 0xff, 0xe3, 0x6d, 0xe0, 0x16, 0x00, 0x03, 0x00,
	0x00,
}
