// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/revenue_nameplate/grpc_revenue_nameplate.proto

package revenue_nameplate // import "golang.52tt.com/protocol/app/api/revenue_nameplate"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import revenuenameplatelogic "golang.52tt.com/protocol/app/revenuenameplatelogic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RevenueNameplateLogicClient is the client API for RevenueNameplateLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RevenueNameplateLogicClient interface {
	GetUserNameplateInfo(ctx context.Context, in *revenuenameplatelogic.GetUserNameplateInfoReq, opts ...grpc.CallOption) (*revenuenameplatelogic.GetUserNameplateInfoResp, error)
	SetUserNameplateInfo(ctx context.Context, in *revenuenameplatelogic.SetUserNameplateInfoReq, opts ...grpc.CallOption) (*revenuenameplatelogic.SetUserNameplateInfoResp, error)
	GetUserAllNameplateList(ctx context.Context, in *revenuenameplatelogic.GetUserAllNameplateListReq, opts ...grpc.CallOption) (*revenuenameplatelogic.GetUserAllNameplateListResp, error)
}

type revenueNameplateLogicClient struct {
	cc *grpc.ClientConn
}

func NewRevenueNameplateLogicClient(cc *grpc.ClientConn) RevenueNameplateLogicClient {
	return &revenueNameplateLogicClient{cc}
}

func (c *revenueNameplateLogicClient) GetUserNameplateInfo(ctx context.Context, in *revenuenameplatelogic.GetUserNameplateInfoReq, opts ...grpc.CallOption) (*revenuenameplatelogic.GetUserNameplateInfoResp, error) {
	out := new(revenuenameplatelogic.GetUserNameplateInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.revenue_nameplate.RevenueNameplateLogic/GetUserNameplateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueNameplateLogicClient) SetUserNameplateInfo(ctx context.Context, in *revenuenameplatelogic.SetUserNameplateInfoReq, opts ...grpc.CallOption) (*revenuenameplatelogic.SetUserNameplateInfoResp, error) {
	out := new(revenuenameplatelogic.SetUserNameplateInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.revenue_nameplate.RevenueNameplateLogic/SetUserNameplateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueNameplateLogicClient) GetUserAllNameplateList(ctx context.Context, in *revenuenameplatelogic.GetUserAllNameplateListReq, opts ...grpc.CallOption) (*revenuenameplatelogic.GetUserAllNameplateListResp, error) {
	out := new(revenuenameplatelogic.GetUserAllNameplateListResp)
	err := c.cc.Invoke(ctx, "/ga.api.revenue_nameplate.RevenueNameplateLogic/GetUserAllNameplateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RevenueNameplateLogicServer is the server API for RevenueNameplateLogic service.
type RevenueNameplateLogicServer interface {
	GetUserNameplateInfo(context.Context, *revenuenameplatelogic.GetUserNameplateInfoReq) (*revenuenameplatelogic.GetUserNameplateInfoResp, error)
	SetUserNameplateInfo(context.Context, *revenuenameplatelogic.SetUserNameplateInfoReq) (*revenuenameplatelogic.SetUserNameplateInfoResp, error)
	GetUserAllNameplateList(context.Context, *revenuenameplatelogic.GetUserAllNameplateListReq) (*revenuenameplatelogic.GetUserAllNameplateListResp, error)
}

func RegisterRevenueNameplateLogicServer(s *grpc.Server, srv RevenueNameplateLogicServer) {
	s.RegisterService(&_RevenueNameplateLogic_serviceDesc, srv)
}

func _RevenueNameplateLogic_GetUserNameplateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(revenuenameplatelogic.GetUserNameplateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateLogicServer).GetUserNameplateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.revenue_nameplate.RevenueNameplateLogic/GetUserNameplateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateLogicServer).GetUserNameplateInfo(ctx, req.(*revenuenameplatelogic.GetUserNameplateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueNameplateLogic_SetUserNameplateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(revenuenameplatelogic.SetUserNameplateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateLogicServer).SetUserNameplateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.revenue_nameplate.RevenueNameplateLogic/SetUserNameplateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateLogicServer).SetUserNameplateInfo(ctx, req.(*revenuenameplatelogic.SetUserNameplateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueNameplateLogic_GetUserAllNameplateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(revenuenameplatelogic.GetUserAllNameplateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateLogicServer).GetUserAllNameplateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.revenue_nameplate.RevenueNameplateLogic/GetUserAllNameplateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateLogicServer).GetUserAllNameplateList(ctx, req.(*revenuenameplatelogic.GetUserAllNameplateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RevenueNameplateLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.revenue_nameplate.RevenueNameplateLogic",
	HandlerType: (*RevenueNameplateLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserNameplateInfo",
			Handler:    _RevenueNameplateLogic_GetUserNameplateInfo_Handler,
		},
		{
			MethodName: "SetUserNameplateInfo",
			Handler:    _RevenueNameplateLogic_SetUserNameplateInfo_Handler,
		},
		{
			MethodName: "GetUserAllNameplateList",
			Handler:    _RevenueNameplateLogic_GetUserAllNameplateList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/revenue_nameplate/grpc_revenue_nameplate.proto",
}

func init() {
	proto.RegisterFile("api/revenue_nameplate/grpc_revenue_nameplate.proto", fileDescriptor_grpc_revenue_nameplate_30d6962499ed353d)
}

var fileDescriptor_grpc_revenue_nameplate_30d6962499ed353d = []byte{
	// 309 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x32, 0x4a, 0x2c, 0xc8, 0xd4,
	0x2f, 0x4a, 0x2d, 0x4b, 0xcd, 0x2b, 0x4d, 0x8d, 0xcf, 0x4b, 0xcc, 0x4d, 0x2d, 0xc8, 0x49, 0x2c,
	0x49, 0xd5, 0x4f, 0x2f, 0x2a, 0x48, 0x8e, 0xc7, 0x10, 0xd6, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17,
	0x92, 0x48, 0x4f, 0xd4, 0x4b, 0x2c, 0xc8, 0xd4, 0xc3, 0x90, 0x97, 0x92, 0x83, 0x0a, 0xe9, 0xc2,
	0x85, 0x74, 0x73, 0xf2, 0xd3, 0x33, 0x93, 0xe3, 0x21, 0x3a, 0xa5, 0x64, 0x41, 0xb6, 0xa5, 0x56,
	0x94, 0xa4, 0xe6, 0x15, 0x67, 0xe6, 0xe7, 0x21, 0x58, 0x10, 0x69, 0xa3, 0xff, 0xcc, 0x5c, 0xa2,
	0x41, 0x10, 0x13, 0xfc, 0x60, 0x06, 0xf8, 0x80, 0xf4, 0x0b, 0xb5, 0x31, 0x72, 0x89, 0xb8, 0xa7,
	0x96, 0x84, 0x16, 0xa7, 0x16, 0xc1, 0x65, 0x3c, 0xf3, 0xd2, 0xf2, 0x85, 0x0c, 0xf5, 0xd2, 0x13,
	0x61, 0x0e, 0x81, 0x5b, 0x0a, 0xb6, 0x53, 0x0f, 0x9b, 0xfa, 0xa0, 0xd4, 0x42, 0x29, 0x23, 0x52,
	0xb5, 0x14, 0x17, 0x28, 0xb1, 0x37, 0x35, 0x28, 0x30, 0x73, 0x4c, 0x95, 0x03, 0x3b, 0x24, 0x98,
	0x44, 0x87, 0x04, 0x93, 0xee, 0x90, 0x60, 0xfc, 0x0e, 0x99, 0x26, 0x27, 0xd4, 0xcf, 0xc8, 0x25,
	0x0e, 0x75, 0xae, 0x63, 0x4e, 0x0e, 0x22, 0xb8, 0x32, 0x8b, 0x4b, 0x84, 0x4c, 0x08, 0xfa, 0x10,
	0x5d, 0x0b, 0xc8, 0x39, 0xa6, 0x64, 0xe8, 0x82, 0xbb, 0x68, 0xba, 0x9c, 0x94, 0x53, 0x53, 0x83,
	0x82, 0x38, 0x8e, 0x04, 0xd0, 0xd5, 0xa0, 0xc0, 0x94, 0x9e, 0x3f, 0xa9, 0x41, 0x41, 0x56, 0x1f,
	0x62, 0x22, 0xd6, 0x68, 0xd6, 0x77, 0x4a, 0xe1, 0x12, 0x4b, 0xce, 0xcf, 0xd5, 0x2b, 0x2c, 0x2d,
	0x4f, 0xcc, 0xd3, 0x2b, 0x29, 0x81, 0xa4, 0x0b, 0x50, 0x62, 0x8b, 0x72, 0x49, 0xcf, 0xcf, 0x49,
	0xcc, 0x4b, 0xd7, 0x33, 0x35, 0x2a, 0x29, 0xd1, 0x4b, 0xce, 0xcf, 0xd5, 0x07, 0x4b, 0x25, 0xe7,
	0xe7, 0xe8, 0x27, 0x16, 0x14, 0xe8, 0x63, 0x4d, 0xc5, 0xd6, 0x18, 0x22, 0x8b, 0x98, 0x98, 0x83,
	0x02, 0x9c, 0x93, 0xd8, 0xc0, 0x7a, 0x8d, 0x01, 0x01, 0x00, 0x00, 0xff, 0xff, 0x7f, 0xc7, 0x8b,
	0x41, 0xfd, 0x02, 0x00, 0x00,
}
