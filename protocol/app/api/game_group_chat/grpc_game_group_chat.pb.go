// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/game_group_chat/grpc_game_group_chat.proto

package game_group_chat // import "golang.52tt.com/protocol/app/api/game_group_chat"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import game_group_chat_logic "golang.52tt.com/protocol/app/game-group-chat-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameGroupChatLogicClient is the client API for GameGroupChatLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameGroupChatLogicClient interface {
	// 获取创建群聊数据
	GetGroupChatCreateProp(ctx context.Context, in *game_group_chat_logic.GetGroupChatCreatePropReq, opts ...grpc.CallOption) (*game_group_chat_logic.GetGroupChatCreatePropResp, error)
	// 创建群聊
	CreateTGroupChat(ctx context.Context, in *game_group_chat_logic.CreateTGroupChatReq, opts ...grpc.CallOption) (*game_group_chat_logic.CreateTGroupChatResp, error)
	// 群聊列表
	GetGroupChatList(ctx context.Context, in *game_group_chat_logic.GetGroupChatListReq, opts ...grpc.CallOption) (*game_group_chat_logic.GetGroupChatListResp, error)
	// 专区建群额外信息
	GetGroupChatExtraInfo(ctx context.Context, in *game_group_chat_logic.GetGroupChatExtraInfoReq, opts ...grpc.CallOption) (*game_group_chat_logic.GetGroupChatExtraInfoResp, error)
	// 修改用户群额外信息
	SetGroupChatExtraInfo(ctx context.Context, in *game_group_chat_logic.SetGroupChatExtraInfoReq, opts ...grpc.CallOption) (*game_group_chat_logic.SetGroupChatExtraInfoResp, error)
	// 预览群聊消息
	PreviewGroupChatMsg(ctx context.Context, in *game_group_chat_logic.PreviewGroupChatMsgRequest, opts ...grpc.CallOption) (*game_group_chat_logic.PreviewGroupChatMsgResponse, error)
}

type gameGroupChatLogicClient struct {
	cc *grpc.ClientConn
}

func NewGameGroupChatLogicClient(cc *grpc.ClientConn) GameGroupChatLogicClient {
	return &gameGroupChatLogicClient{cc}
}

func (c *gameGroupChatLogicClient) GetGroupChatCreateProp(ctx context.Context, in *game_group_chat_logic.GetGroupChatCreatePropReq, opts ...grpc.CallOption) (*game_group_chat_logic.GetGroupChatCreatePropResp, error) {
	out := new(game_group_chat_logic.GetGroupChatCreatePropResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_group_chat.GameGroupChatLogic/GetGroupChatCreateProp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameGroupChatLogicClient) CreateTGroupChat(ctx context.Context, in *game_group_chat_logic.CreateTGroupChatReq, opts ...grpc.CallOption) (*game_group_chat_logic.CreateTGroupChatResp, error) {
	out := new(game_group_chat_logic.CreateTGroupChatResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_group_chat.GameGroupChatLogic/CreateTGroupChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameGroupChatLogicClient) GetGroupChatList(ctx context.Context, in *game_group_chat_logic.GetGroupChatListReq, opts ...grpc.CallOption) (*game_group_chat_logic.GetGroupChatListResp, error) {
	out := new(game_group_chat_logic.GetGroupChatListResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_group_chat.GameGroupChatLogic/GetGroupChatList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameGroupChatLogicClient) GetGroupChatExtraInfo(ctx context.Context, in *game_group_chat_logic.GetGroupChatExtraInfoReq, opts ...grpc.CallOption) (*game_group_chat_logic.GetGroupChatExtraInfoResp, error) {
	out := new(game_group_chat_logic.GetGroupChatExtraInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_group_chat.GameGroupChatLogic/GetGroupChatExtraInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameGroupChatLogicClient) SetGroupChatExtraInfo(ctx context.Context, in *game_group_chat_logic.SetGroupChatExtraInfoReq, opts ...grpc.CallOption) (*game_group_chat_logic.SetGroupChatExtraInfoResp, error) {
	out := new(game_group_chat_logic.SetGroupChatExtraInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_group_chat.GameGroupChatLogic/SetGroupChatExtraInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameGroupChatLogicClient) PreviewGroupChatMsg(ctx context.Context, in *game_group_chat_logic.PreviewGroupChatMsgRequest, opts ...grpc.CallOption) (*game_group_chat_logic.PreviewGroupChatMsgResponse, error) {
	out := new(game_group_chat_logic.PreviewGroupChatMsgResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_group_chat.GameGroupChatLogic/PreviewGroupChatMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameGroupChatLogicServer is the server API for GameGroupChatLogic service.
type GameGroupChatLogicServer interface {
	// 获取创建群聊数据
	GetGroupChatCreateProp(context.Context, *game_group_chat_logic.GetGroupChatCreatePropReq) (*game_group_chat_logic.GetGroupChatCreatePropResp, error)
	// 创建群聊
	CreateTGroupChat(context.Context, *game_group_chat_logic.CreateTGroupChatReq) (*game_group_chat_logic.CreateTGroupChatResp, error)
	// 群聊列表
	GetGroupChatList(context.Context, *game_group_chat_logic.GetGroupChatListReq) (*game_group_chat_logic.GetGroupChatListResp, error)
	// 专区建群额外信息
	GetGroupChatExtraInfo(context.Context, *game_group_chat_logic.GetGroupChatExtraInfoReq) (*game_group_chat_logic.GetGroupChatExtraInfoResp, error)
	// 修改用户群额外信息
	SetGroupChatExtraInfo(context.Context, *game_group_chat_logic.SetGroupChatExtraInfoReq) (*game_group_chat_logic.SetGroupChatExtraInfoResp, error)
	// 预览群聊消息
	PreviewGroupChatMsg(context.Context, *game_group_chat_logic.PreviewGroupChatMsgRequest) (*game_group_chat_logic.PreviewGroupChatMsgResponse, error)
}

func RegisterGameGroupChatLogicServer(s *grpc.Server, srv GameGroupChatLogicServer) {
	s.RegisterService(&_GameGroupChatLogic_serviceDesc, srv)
}

func _GameGroupChatLogic_GetGroupChatCreateProp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_group_chat_logic.GetGroupChatCreatePropReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameGroupChatLogicServer).GetGroupChatCreateProp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_group_chat.GameGroupChatLogic/GetGroupChatCreateProp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameGroupChatLogicServer).GetGroupChatCreateProp(ctx, req.(*game_group_chat_logic.GetGroupChatCreatePropReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameGroupChatLogic_CreateTGroupChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_group_chat_logic.CreateTGroupChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameGroupChatLogicServer).CreateTGroupChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_group_chat.GameGroupChatLogic/CreateTGroupChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameGroupChatLogicServer).CreateTGroupChat(ctx, req.(*game_group_chat_logic.CreateTGroupChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameGroupChatLogic_GetGroupChatList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_group_chat_logic.GetGroupChatListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameGroupChatLogicServer).GetGroupChatList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_group_chat.GameGroupChatLogic/GetGroupChatList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameGroupChatLogicServer).GetGroupChatList(ctx, req.(*game_group_chat_logic.GetGroupChatListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameGroupChatLogic_GetGroupChatExtraInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_group_chat_logic.GetGroupChatExtraInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameGroupChatLogicServer).GetGroupChatExtraInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_group_chat.GameGroupChatLogic/GetGroupChatExtraInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameGroupChatLogicServer).GetGroupChatExtraInfo(ctx, req.(*game_group_chat_logic.GetGroupChatExtraInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameGroupChatLogic_SetGroupChatExtraInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_group_chat_logic.SetGroupChatExtraInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameGroupChatLogicServer).SetGroupChatExtraInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_group_chat.GameGroupChatLogic/SetGroupChatExtraInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameGroupChatLogicServer).SetGroupChatExtraInfo(ctx, req.(*game_group_chat_logic.SetGroupChatExtraInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameGroupChatLogic_PreviewGroupChatMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_group_chat_logic.PreviewGroupChatMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameGroupChatLogicServer).PreviewGroupChatMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_group_chat.GameGroupChatLogic/PreviewGroupChatMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameGroupChatLogicServer).PreviewGroupChatMsg(ctx, req.(*game_group_chat_logic.PreviewGroupChatMsgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameGroupChatLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.game_group_chat.GameGroupChatLogic",
	HandlerType: (*GameGroupChatLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGroupChatCreateProp",
			Handler:    _GameGroupChatLogic_GetGroupChatCreateProp_Handler,
		},
		{
			MethodName: "CreateTGroupChat",
			Handler:    _GameGroupChatLogic_CreateTGroupChat_Handler,
		},
		{
			MethodName: "GetGroupChatList",
			Handler:    _GameGroupChatLogic_GetGroupChatList_Handler,
		},
		{
			MethodName: "GetGroupChatExtraInfo",
			Handler:    _GameGroupChatLogic_GetGroupChatExtraInfo_Handler,
		},
		{
			MethodName: "SetGroupChatExtraInfo",
			Handler:    _GameGroupChatLogic_SetGroupChatExtraInfo_Handler,
		},
		{
			MethodName: "PreviewGroupChatMsg",
			Handler:    _GameGroupChatLogic_PreviewGroupChatMsg_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/game_group_chat/grpc_game_group_chat.proto",
}

func init() {
	proto.RegisterFile("api/game_group_chat/grpc_game_group_chat.proto", fileDescriptor_grpc_game_group_chat_7d099e418acadf75)
}

var fileDescriptor_grpc_game_group_chat_7d099e418acadf75 = []byte{
	// 380 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x94, 0x4d, 0x4f, 0xc2, 0x30,
	0x18, 0xc7, 0x03, 0x24, 0x68, 0x7a, 0x32, 0x35, 0x70, 0x58, 0x62, 0x24, 0xde, 0xdb, 0x45, 0x90,
	0x93, 0x17, 0x23, 0x31, 0xc4, 0x04, 0x13, 0x02, 0x9e, 0xbc, 0xcc, 0xba, 0xd4, 0xb2, 0x84, 0xad,
	0xa5, 0x2d, 0x42, 0x3c, 0x11, 0x6e, 0xbe, 0x7c, 0x0a, 0x6f, 0xbe, 0x7e, 0x28, 0x3f, 0x89, 0xe9,
	0x46, 0x2a, 0x99, 0x13, 0x9c, 0xb7, 0xed, 0x79, 0xfe, 0xbf, 0xfe, 0x7f, 0x87, 0x75, 0x00, 0x13,
	0x11, 0xb8, 0x8c, 0x84, 0xd4, 0x63, 0x92, 0x8f, 0x85, 0xe7, 0x0f, 0x88, 0x76, 0x99, 0x14, 0xbe,
	0x97, 0x1a, 0x62, 0x21, 0xb9, 0xe6, 0xb0, 0xca, 0x88, 0x41, 0x70, 0x6a, 0xeb, 0xec, 0xa7, 0x06,
	0xde, 0x90, 0xb3, 0xc0, 0x77, 0x33, 0xa7, 0xc9, 0x51, 0xce, 0x8e, 0xa9, 0xa6, 0x53, 0x4d, 0x23,
	0x15, 0xf0, 0xe8, 0xfb, 0x29, 0x59, 0xd7, 0x3f, 0xcb, 0x00, 0xb6, 0x49, 0x48, 0xdb, 0x86, 0x6e,
	0x0d, 0x88, 0xee, 0x18, 0x16, 0x3e, 0x16, 0x40, 0xb5, 0x4d, 0xb5, 0x9d, 0xb6, 0x24, 0x25, 0x9a,
	0x76, 0x25, 0x17, 0xb0, 0x81, 0x19, 0xc1, 0xd9, 0x8d, 0xd9, 0x44, 0x8f, 0x8e, 0x9c, 0x83, 0xfc,
	0x90, 0x12, 0x7b, 0x1b, 0xf3, 0x59, 0xad, 0xb4, 0xf9, 0x8c, 0xe0, 0x2d, 0xd8, 0x4a, 0x56, 0xe7,
	0x36, 0x0a, 0xd1, 0xef, 0x47, 0xa6, 0xb3, 0xc6, 0x00, 0xe7, 0x89, 0xdb, 0xee, 0x97, 0xb8, 0x7b,
	0x59, 0xb1, 0x13, 0xa8, 0x95, 0xdd, 0xe9, 0xec, 0x9a, 0xee, 0x9f, 0x71, 0xdb, 0xfd, 0x8a, 0xe0,
	0x5d, 0x01, 0x54, 0x96, 0x13, 0x27, 0x53, 0x2d, 0xc9, 0x69, 0x74, 0xcd, 0x61, 0xfd, 0x6f, 0x47,
	0x5a, 0xc0, 0x68, 0x34, 0x72, 0x33, 0xd6, 0xe5, 0x2d, 0x71, 0xe9, 0xe7, 0x75, 0xe9, 0xff, 0xc3,
	0xa5, 0xbf, 0xc6, 0xe5, 0x1d, 0xc1, 0x87, 0x02, 0xd8, 0xee, 0x4a, 0x7a, 0x13, 0xd0, 0x89, 0x8d,
	0x9e, 0x29, 0x06, 0x57, 0x7c, 0x66, 0x19, 0xf1, 0x1e, 0x1d, 0x8d, 0xa9, 0xd2, 0x4e, 0x33, 0x27,
	0xa5, 0x04, 0x8f, 0x14, 0x5d, 0xd8, 0x7c, 0x20, 0x67, 0x77, 0x3e, 0xab, 0x55, 0x0c, 0x8f, 0x62,
	0x1e, 0x19, 0x1e, 0xc5, 0xfc, 0xfd, 0xac, 0x56, 0x64, 0xfc, 0xf8, 0x12, 0x54, 0x7d, 0x1e, 0xe2,
	0xd1, 0x78, 0x42, 0x22, 0xac, 0x17, 0x97, 0xdc, 0x5c, 0xf0, 0x8b, 0x23, 0xc6, 0x87, 0x24, 0x62,
	0xb8, 0x59, 0xd7, 0x1a, 0xfb, 0x3c, 0x74, 0xe3, 0x95, 0xcf, 0x87, 0x2e, 0x11, 0xc2, 0xcd, 0xf8,
	0x6b, 0x1c, 0xa6, 0xde, 0x9f, 0x8a, 0xa5, 0x5e, 0xb7, 0x75, 0x55, 0x8e, 0xb9, 0xc6, 0x57, 0x00,
	0x00, 0x00, 0xff, 0xff, 0xd7, 0xcb, 0xbb, 0xcd, 0x69, 0x04, 0x00, 0x00,
}
