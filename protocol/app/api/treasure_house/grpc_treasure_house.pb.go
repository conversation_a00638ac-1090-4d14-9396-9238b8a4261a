// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/treasure_house/grpc_treasure_house.proto

package treasure_house // import "golang.52tt.com/protocol/app/api/treasure_house"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import treasure_house_logic "golang.52tt.com/protocol/app/treasure_house_logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TreasureHouseLogicClient is the client API for TreasureHouseLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TreasureHouseLogicClient interface {
	// 获取珍宝馆活动列表
	GetTreasureActivityList(ctx context.Context, in *treasure_house_logic.GetTreasureActivityListReq, opts ...grpc.CallOption) (*treasure_house_logic.GetTreasureActivityListResp, error)
	// 获取珍宝馆活动
	GetTreasureActivity(ctx context.Context, in *treasure_house_logic.GetTreasureActivityReq, opts ...grpc.CallOption) (*treasure_house_logic.GetTreasureActivityResp, error)
	// 领取礼物使用权
	ClaimPresentPermission(ctx context.Context, in *treasure_house_logic.ClaimPresentPermissionReq, opts ...grpc.CallOption) (*treasure_house_logic.ClaimPresentPermissionResp, error)
	// 购买礼物使用权
	BuyPresentPermission(ctx context.Context, in *treasure_house_logic.BuyPresentPermissionReq, opts ...grpc.CallOption) (*treasure_house_logic.BuyPresentPermissionResp, error)
	// 获取活动更新提醒信息
	GetTreasureActivityUpdateInfo(ctx context.Context, in *treasure_house_logic.GetTreasureActivityUpdateInfoReq, opts ...grpc.CallOption) (*treasure_house_logic.GetTreasureActivityUpdateInfoResp, error)
}

type treasureHouseLogicClient struct {
	cc *grpc.ClientConn
}

func NewTreasureHouseLogicClient(cc *grpc.ClientConn) TreasureHouseLogicClient {
	return &treasureHouseLogicClient{cc}
}

func (c *treasureHouseLogicClient) GetTreasureActivityList(ctx context.Context, in *treasure_house_logic.GetTreasureActivityListReq, opts ...grpc.CallOption) (*treasure_house_logic.GetTreasureActivityListResp, error) {
	out := new(treasure_house_logic.GetTreasureActivityListResp)
	err := c.cc.Invoke(ctx, "/ga.api.treasure_house.TreasureHouseLogic/GetTreasureActivityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseLogicClient) GetTreasureActivity(ctx context.Context, in *treasure_house_logic.GetTreasureActivityReq, opts ...grpc.CallOption) (*treasure_house_logic.GetTreasureActivityResp, error) {
	out := new(treasure_house_logic.GetTreasureActivityResp)
	err := c.cc.Invoke(ctx, "/ga.api.treasure_house.TreasureHouseLogic/GetTreasureActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseLogicClient) ClaimPresentPermission(ctx context.Context, in *treasure_house_logic.ClaimPresentPermissionReq, opts ...grpc.CallOption) (*treasure_house_logic.ClaimPresentPermissionResp, error) {
	out := new(treasure_house_logic.ClaimPresentPermissionResp)
	err := c.cc.Invoke(ctx, "/ga.api.treasure_house.TreasureHouseLogic/ClaimPresentPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseLogicClient) BuyPresentPermission(ctx context.Context, in *treasure_house_logic.BuyPresentPermissionReq, opts ...grpc.CallOption) (*treasure_house_logic.BuyPresentPermissionResp, error) {
	out := new(treasure_house_logic.BuyPresentPermissionResp)
	err := c.cc.Invoke(ctx, "/ga.api.treasure_house.TreasureHouseLogic/BuyPresentPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *treasureHouseLogicClient) GetTreasureActivityUpdateInfo(ctx context.Context, in *treasure_house_logic.GetTreasureActivityUpdateInfoReq, opts ...grpc.CallOption) (*treasure_house_logic.GetTreasureActivityUpdateInfoResp, error) {
	out := new(treasure_house_logic.GetTreasureActivityUpdateInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.treasure_house.TreasureHouseLogic/GetTreasureActivityUpdateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TreasureHouseLogicServer is the server API for TreasureHouseLogic service.
type TreasureHouseLogicServer interface {
	// 获取珍宝馆活动列表
	GetTreasureActivityList(context.Context, *treasure_house_logic.GetTreasureActivityListReq) (*treasure_house_logic.GetTreasureActivityListResp, error)
	// 获取珍宝馆活动
	GetTreasureActivity(context.Context, *treasure_house_logic.GetTreasureActivityReq) (*treasure_house_logic.GetTreasureActivityResp, error)
	// 领取礼物使用权
	ClaimPresentPermission(context.Context, *treasure_house_logic.ClaimPresentPermissionReq) (*treasure_house_logic.ClaimPresentPermissionResp, error)
	// 购买礼物使用权
	BuyPresentPermission(context.Context, *treasure_house_logic.BuyPresentPermissionReq) (*treasure_house_logic.BuyPresentPermissionResp, error)
	// 获取活动更新提醒信息
	GetTreasureActivityUpdateInfo(context.Context, *treasure_house_logic.GetTreasureActivityUpdateInfoReq) (*treasure_house_logic.GetTreasureActivityUpdateInfoResp, error)
}

func RegisterTreasureHouseLogicServer(s *grpc.Server, srv TreasureHouseLogicServer) {
	s.RegisterService(&_TreasureHouseLogic_serviceDesc, srv)
}

func _TreasureHouseLogic_GetTreasureActivityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(treasure_house_logic.GetTreasureActivityListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseLogicServer).GetTreasureActivityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.treasure_house.TreasureHouseLogic/GetTreasureActivityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseLogicServer).GetTreasureActivityList(ctx, req.(*treasure_house_logic.GetTreasureActivityListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouseLogic_GetTreasureActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(treasure_house_logic.GetTreasureActivityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseLogicServer).GetTreasureActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.treasure_house.TreasureHouseLogic/GetTreasureActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseLogicServer).GetTreasureActivity(ctx, req.(*treasure_house_logic.GetTreasureActivityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouseLogic_ClaimPresentPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(treasure_house_logic.ClaimPresentPermissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseLogicServer).ClaimPresentPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.treasure_house.TreasureHouseLogic/ClaimPresentPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseLogicServer).ClaimPresentPermission(ctx, req.(*treasure_house_logic.ClaimPresentPermissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouseLogic_BuyPresentPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(treasure_house_logic.BuyPresentPermissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseLogicServer).BuyPresentPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.treasure_house.TreasureHouseLogic/BuyPresentPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseLogicServer).BuyPresentPermission(ctx, req.(*treasure_house_logic.BuyPresentPermissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TreasureHouseLogic_GetTreasureActivityUpdateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(treasure_house_logic.GetTreasureActivityUpdateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TreasureHouseLogicServer).GetTreasureActivityUpdateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.treasure_house.TreasureHouseLogic/GetTreasureActivityUpdateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TreasureHouseLogicServer).GetTreasureActivityUpdateInfo(ctx, req.(*treasure_house_logic.GetTreasureActivityUpdateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _TreasureHouseLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.treasure_house.TreasureHouseLogic",
	HandlerType: (*TreasureHouseLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetTreasureActivityList",
			Handler:    _TreasureHouseLogic_GetTreasureActivityList_Handler,
		},
		{
			MethodName: "GetTreasureActivity",
			Handler:    _TreasureHouseLogic_GetTreasureActivity_Handler,
		},
		{
			MethodName: "ClaimPresentPermission",
			Handler:    _TreasureHouseLogic_ClaimPresentPermission_Handler,
		},
		{
			MethodName: "BuyPresentPermission",
			Handler:    _TreasureHouseLogic_BuyPresentPermission_Handler,
		},
		{
			MethodName: "GetTreasureActivityUpdateInfo",
			Handler:    _TreasureHouseLogic_GetTreasureActivityUpdateInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/treasure_house/grpc_treasure_house.proto",
}

func init() {
	proto.RegisterFile("api/treasure_house/grpc_treasure_house.proto", fileDescriptor_grpc_treasure_house_ce4ce56796cf3056)
}

var fileDescriptor_grpc_treasure_house_ce4ce56796cf3056 = []byte{
	// 379 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x93, 0xcf, 0x4b, 0xc3, 0x30,
	0x14, 0xc7, 0xe9, 0x2a, 0x63, 0xe4, 0x18, 0xe7, 0x84, 0xc2, 0xa0, 0x78, 0x76, 0x89, 0x6e, 0x7a,
	0xf0, 0x07, 0x82, 0xdb, 0x41, 0x85, 0x1d, 0xc6, 0xd0, 0x8b, 0x07, 0x47, 0xac, 0xb1, 0x06, 0xda,
	0x26, 0x6b, 0x53, 0x75, 0xb7, 0x32, 0x10, 0x75, 0x83, 0x1e, 0xfc, 0x0b, 0xd4, 0xbf, 0x54, 0xd2,
	0xba, 0x8e, 0xb9, 0xee, 0x50, 0x6f, 0x6d, 0x5e, 0x3e, 0xdf, 0xf7, 0xc9, 0x83, 0x07, 0xb6, 0x89,
	0x60, 0x58, 0xfa, 0x94, 0x04, 0xa1, 0x4f, 0x07, 0x0f, 0x3c, 0x0c, 0x28, 0xb6, 0x7d, 0x61, 0x0d,
	0x16, 0xcf, 0x90, 0xf0, 0xb9, 0xe4, 0x70, 0xc3, 0x26, 0x88, 0x08, 0x86, 0x16, 0x8b, 0xc6, 0x9f,
	0x80, 0x81, 0xc3, 0x6d, 0x66, 0xe5, 0x1e, 0xa6, 0x39, 0x46, 0x5d, 0x75, 0xa5, 0xcf, 0x92, 0x7a,
	0x01, 0xe3, 0xde, 0xfc, 0x2b, 0x2d, 0x37, 0xbf, 0xca, 0x00, 0x5e, 0xfe, 0xd2, 0xe7, 0x0a, 0xee,
	0x2a, 0x16, 0xc6, 0x1a, 0xd8, 0x3c, 0xa3, 0x72, 0x56, 0x39, 0xb5, 0x24, 0x7b, 0x64, 0x72, 0xd4,
	0x65, 0x81, 0x84, 0x2d, 0x64, 0x13, 0x94, 0xdb, 0x71, 0x05, 0xd1, 0xa7, 0x43, 0x63, 0xaf, 0x38,
	0x14, 0x88, 0xad, 0xca, 0x38, 0x32, 0xd7, 0x2a, 0xaf, 0xb1, 0x0e, 0xc7, 0x1a, 0x58, 0xcf, 0xb9,
	0x09, 0x71, 0x91, 0x5c, 0x25, 0xb2, 0x53, 0x0c, 0xc8, 0x24, 0xde, 0x62, 0x1d, 0x4e, 0x35, 0x50,
	0xeb, 0x38, 0x84, 0xb9, 0x3d, 0x9f, 0x06, 0xd4, 0x93, 0x3d, 0xea, 0xbb, 0x2c, 0x50, 0xd3, 0x84,
	0xcd, 0x95, 0xb1, 0xf9, 0x80, 0x52, 0x69, 0x15, 0x66, 0x32, 0x9b, 0xf7, 0x58, 0x87, 0x2f, 0x1a,
	0xa8, 0xb6, 0xc3, 0xd1, 0xb2, 0xcb, 0xea, 0x27, 0xe6, 0x5d, 0x57, 0x26, 0xbb, 0x05, 0x89, 0xcc,
	0x63, 0x12, 0xeb, 0xf0, 0x53, 0x03, 0xf5, 0x9c, 0xd9, 0x5d, 0x89, 0x3b, 0x22, 0xe9, 0x85, 0x77,
	0xcf, 0xe1, 0x41, 0x91, 0x99, 0xcf, 0x39, 0x65, 0x76, 0xf8, 0x5f, 0x34, 0x53, 0x9c, 0xc6, 0xba,
	0x71, 0x3c, 0x8e, 0xcc, 0xea, 0x2c, 0xa5, 0x91, 0xa4, 0x34, 0x92, 0x94, 0x49, 0x64, 0x96, 0x6c,
	0xfe, 0x11, 0x99, 0x06, 0x4e, 0x53, 0x97, 0x77, 0x01, 0xb7, 0x6f, 0x40, 0xcd, 0xe2, 0x2e, 0x1a,
	0x86, 0x4f, 0xc4, 0x43, 0x52, 0xa6, 0x9b, 0xa3, 0x96, 0xf3, 0xfa, 0xc4, 0xe6, 0x0e, 0xf1, 0x6c,
	0xb4, 0xdf, 0x94, 0x12, 0x59, 0xdc, 0xc5, 0x49, 0xc9, 0xe2, 0x0e, 0x26, 0x42, 0xe0, 0xe5, 0x7d,
	0x3f, 0x5a, 0xfc, 0xfd, 0x2e, 0xe9, 0xfd, 0x5e, 0xe7, 0xb6, 0x9c, 0x50, 0xad, 0x9f, 0x00, 0x00,
	0x00, 0xff, 0xff, 0xfa, 0x75, 0xea, 0x86, 0x21, 0x04, 0x00, 0x00,
}
