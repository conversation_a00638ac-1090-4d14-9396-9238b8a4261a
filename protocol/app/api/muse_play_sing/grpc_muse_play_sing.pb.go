// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/muse_play_sing/grpc_muse_play_sing.proto

package muse_play_sing // import "golang.52tt.com/protocol/app/api/muse_play_sing"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import muse_play_sing_logic "golang.52tt.com/protocol/app/muse_play_sing_logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MusePlaySingLogicClient is the client API for MusePlaySingLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MusePlaySingLogicClient interface {
	// 检查歌曲状态，返回默认的音频信息
	CheckPlaySingStatus(ctx context.Context, in *muse_play_sing_logic.CheckPlaySingStatusRequest, opts ...grpc.CallOption) (*muse_play_sing_logic.CheckPlaySingStatusResponse, error)
	// 获取歌曲列表
	GetPlaySingList(ctx context.Context, in *muse_play_sing_logic.GetPlaySingListRequest, opts ...grpc.CallOption) (*muse_play_sing_logic.GetPlaySingListResponse, error)
	// 获取歌曲详细信息
	GetPlaySingDetail(ctx context.Context, in *muse_play_sing_logic.GetPlaySingDetailRequest, opts ...grpc.CallOption) (*muse_play_sing_logic.GetPlaySingDetailResponse, error)
}

type musePlaySingLogicClient struct {
	cc *grpc.ClientConn
}

func NewMusePlaySingLogicClient(cc *grpc.ClientConn) MusePlaySingLogicClient {
	return &musePlaySingLogicClient{cc}
}

func (c *musePlaySingLogicClient) CheckPlaySingStatus(ctx context.Context, in *muse_play_sing_logic.CheckPlaySingStatusRequest, opts ...grpc.CallOption) (*muse_play_sing_logic.CheckPlaySingStatusResponse, error) {
	out := new(muse_play_sing_logic.CheckPlaySingStatusResponse)
	err := c.cc.Invoke(ctx, "/ga.api.muse_play_sing.MusePlaySingLogic/CheckPlaySingStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musePlaySingLogicClient) GetPlaySingList(ctx context.Context, in *muse_play_sing_logic.GetPlaySingListRequest, opts ...grpc.CallOption) (*muse_play_sing_logic.GetPlaySingListResponse, error) {
	out := new(muse_play_sing_logic.GetPlaySingListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.muse_play_sing.MusePlaySingLogic/GetPlaySingList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musePlaySingLogicClient) GetPlaySingDetail(ctx context.Context, in *muse_play_sing_logic.GetPlaySingDetailRequest, opts ...grpc.CallOption) (*muse_play_sing_logic.GetPlaySingDetailResponse, error) {
	out := new(muse_play_sing_logic.GetPlaySingDetailResponse)
	err := c.cc.Invoke(ctx, "/ga.api.muse_play_sing.MusePlaySingLogic/GetPlaySingDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MusePlaySingLogicServer is the server API for MusePlaySingLogic service.
type MusePlaySingLogicServer interface {
	// 检查歌曲状态，返回默认的音频信息
	CheckPlaySingStatus(context.Context, *muse_play_sing_logic.CheckPlaySingStatusRequest) (*muse_play_sing_logic.CheckPlaySingStatusResponse, error)
	// 获取歌曲列表
	GetPlaySingList(context.Context, *muse_play_sing_logic.GetPlaySingListRequest) (*muse_play_sing_logic.GetPlaySingListResponse, error)
	// 获取歌曲详细信息
	GetPlaySingDetail(context.Context, *muse_play_sing_logic.GetPlaySingDetailRequest) (*muse_play_sing_logic.GetPlaySingDetailResponse, error)
}

func RegisterMusePlaySingLogicServer(s *grpc.Server, srv MusePlaySingLogicServer) {
	s.RegisterService(&_MusePlaySingLogic_serviceDesc, srv)
}

func _MusePlaySingLogic_CheckPlaySingStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(muse_play_sing_logic.CheckPlaySingStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusePlaySingLogicServer).CheckPlaySingStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.muse_play_sing.MusePlaySingLogic/CheckPlaySingStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusePlaySingLogicServer).CheckPlaySingStatus(ctx, req.(*muse_play_sing_logic.CheckPlaySingStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusePlaySingLogic_GetPlaySingList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(muse_play_sing_logic.GetPlaySingListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusePlaySingLogicServer).GetPlaySingList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.muse_play_sing.MusePlaySingLogic/GetPlaySingList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusePlaySingLogicServer).GetPlaySingList(ctx, req.(*muse_play_sing_logic.GetPlaySingListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusePlaySingLogic_GetPlaySingDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(muse_play_sing_logic.GetPlaySingDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusePlaySingLogicServer).GetPlaySingDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.muse_play_sing.MusePlaySingLogic/GetPlaySingDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusePlaySingLogicServer).GetPlaySingDetail(ctx, req.(*muse_play_sing_logic.GetPlaySingDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MusePlaySingLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.muse_play_sing.MusePlaySingLogic",
	HandlerType: (*MusePlaySingLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckPlaySingStatus",
			Handler:    _MusePlaySingLogic_CheckPlaySingStatus_Handler,
		},
		{
			MethodName: "GetPlaySingList",
			Handler:    _MusePlaySingLogic_GetPlaySingList_Handler,
		},
		{
			MethodName: "GetPlaySingDetail",
			Handler:    _MusePlaySingLogic_GetPlaySingDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/muse_play_sing/grpc_muse_play_sing.proto",
}

func init() {
	proto.RegisterFile("api/muse_play_sing/grpc_muse_play_sing.proto", fileDescriptor_grpc_muse_play_sing_9f7baeb23105e1bf)
}

var fileDescriptor_grpc_muse_play_sing_9f7baeb23105e1bf = []byte{
	// 323 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x92, 0xcd, 0x4a, 0x03, 0x31,
	0x14, 0x85, 0xe9, 0x54, 0xa4, 0x64, 0x23, 0x8d, 0x3f, 0x8b, 0x01, 0xa5, 0xb8, 0xb6, 0x89, 0xb6,
	0xba, 0x12, 0x5c, 0x58, 0xc1, 0x8d, 0x42, 0x69, 0x77, 0x2e, 0x1c, 0x62, 0x08, 0x31, 0x98, 0x26,
	0x69, 0x73, 0x07, 0xed, 0x46, 0x4a, 0xb1, 0x9b, 0x3e, 0x86, 0x2f, 0xe3, 0x53, 0xa8, 0xaf, 0x22,
	0xe9, 0x50, 0x75, 0xea, 0x08, 0xed, 0x6e, 0xe6, 0xde, 0xf3, 0x9d, 0x73, 0x20, 0x17, 0x1d, 0x30,
	0xa7, 0x68, 0x2f, 0xf5, 0x22, 0x71, 0x9a, 0x0d, 0x13, 0xaf, 0x8c, 0xa4, 0x72, 0xe0, 0x78, 0x92,
	0x9f, 0x11, 0x37, 0xb0, 0x60, 0xf1, 0xb6, 0x64, 0x84, 0x39, 0x45, 0xf2, 0xcb, 0x78, 0xc1, 0x20,
	0xd1, 0x56, 0x2a, 0x5e, 0x38, 0xcc, 0x7c, 0xe2, 0xdd, 0x90, 0x2a, 0x9e, 0x40, 0x18, 0xaf, 0xac,
	0xf9, 0xf9, 0xca, 0xd6, 0x8d, 0xb7, 0x32, 0xaa, 0x5e, 0xa7, 0x5e, 0xb4, 0x35, 0x1b, 0x76, 0x95,
	0x91, 0x57, 0x01, 0xc5, 0xd3, 0x12, 0xda, 0x6c, 0xdd, 0x0b, 0xfe, 0x30, 0x1f, 0x77, 0x81, 0x41,
	0xea, 0x71, 0x93, 0x48, 0x46, 0x0a, 0xc3, 0x0a, 0xd4, 0x1d, 0xd1, 0x4f, 0x85, 0x87, 0xf8, 0x78,
	0x35, 0xc8, 0x3b, 0x6b, 0xbc, 0xd8, 0xaf, 0x8c, 0x47, 0xb5, 0xb5, 0xca, 0xfb, 0x24, 0xc2, 0xcf,
	0x68, 0xe3, 0x52, 0xc0, 0x77, 0x41, 0xe5, 0x01, 0xd3, 0x7f, 0x2d, 0x17, 0x94, 0xf3, 0x0e, 0x87,
	0xcb, 0x03, 0xb9, 0xfc, 0x8f, 0x49, 0x84, 0x5f, 0x4a, 0xa8, 0xfa, 0x4b, 0x75, 0x21, 0x80, 0x29,
	0x8d, 0x8f, 0x96, 0x71, 0xcc, 0xb4, 0xf3, 0x12, 0x8d, 0x55, 0x90, 0x5c, 0x8d, 0xcf, 0x49, 0x14,
	0xef, 0x8d, 0x47, 0xb5, 0xad, 0x40, 0xd7, 0x03, 0x5d, 0x0f, 0x74, 0x7d, 0x46, 0x4f, 0x47, 0xb5,
	0x48, 0xda, 0xf3, 0x5b, 0xb4, 0xc3, 0x6d, 0x8f, 0xf4, 0xd3, 0x47, 0x66, 0x08, 0x40, 0xf6, 0xbe,
	0xe1, 0x84, 0x6e, 0xce, 0xa4, 0xd5, 0xcc, 0x48, 0x72, 0xd2, 0x00, 0x20, 0xdc, 0xf6, 0xe8, 0x6c,
	0xc5, 0xad, 0xa6, 0xcc, 0x39, 0xfa, 0xf7, 0x2a, 0x4f, 0xf3, 0xbf, 0xaf, 0x51, 0xb9, 0xd3, 0x6e,
	0xdd, 0xad, 0xcf, 0xa8, 0xe6, 0x57, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0c, 0x21, 0xc5, 0xe9, 0xc7,
	0x02, 0x00, 0x00,
}
