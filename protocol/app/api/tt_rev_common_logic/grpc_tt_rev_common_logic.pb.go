// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/tt_rev_common_logic/grpc_tt_rev_common_logic.proto

package tt_rev_common_logic // import "golang.52tt.com/protocol/app/api/tt_rev_common_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import tt_rev_common_logic "golang.52tt.com/protocol/app/tt_rev_common_logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TTRevCommonLogicClient is the client API for TTRevCommonLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TTRevCommonLogicClient interface {
	// 获取充值(首充)活动入口信息
	GetNewRechargeActEntryInfo(ctx context.Context, in *tt_rev_common_logic.GetNewRechargeActEntryInfoRequest, opts ...grpc.CallOption) (*tt_rev_common_logic.GetNewRechargeActEntryInfoResponse, error)
	// 获取充值(首充)活动弹窗信息
	GetNewRechargeActPopupInfo(ctx context.Context, in *tt_rev_common_logic.GetNewRechargeActPopupInfoRequest, opts ...grpc.CallOption) (*tt_rev_common_logic.GetNewRechargeActPopupInfoResponse, error)
	// 获取充值页banner信息
	GetRechargeBannerInfo(ctx context.Context, in *tt_rev_common_logic.GetRechargeBannerInfoRequest, opts ...grpc.CallOption) (*tt_rev_common_logic.GetRechargeBannerInfoResponse, error)
	// 检查能否切换性别
	CheckCanModifySex(ctx context.Context, in *tt_rev_common_logic.CheckCanModifySexRequest, opts ...grpc.CallOption) (*tt_rev_common_logic.CheckCanModifySexResponse, error)
}

type tTRevCommonLogicClient struct {
	cc *grpc.ClientConn
}

func NewTTRevCommonLogicClient(cc *grpc.ClientConn) TTRevCommonLogicClient {
	return &tTRevCommonLogicClient{cc}
}

func (c *tTRevCommonLogicClient) GetNewRechargeActEntryInfo(ctx context.Context, in *tt_rev_common_logic.GetNewRechargeActEntryInfoRequest, opts ...grpc.CallOption) (*tt_rev_common_logic.GetNewRechargeActEntryInfoResponse, error) {
	out := new(tt_rev_common_logic.GetNewRechargeActEntryInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.tt_rev_common_logic.TTRevCommonLogic/GetNewRechargeActEntryInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTRevCommonLogicClient) GetNewRechargeActPopupInfo(ctx context.Context, in *tt_rev_common_logic.GetNewRechargeActPopupInfoRequest, opts ...grpc.CallOption) (*tt_rev_common_logic.GetNewRechargeActPopupInfoResponse, error) {
	out := new(tt_rev_common_logic.GetNewRechargeActPopupInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.tt_rev_common_logic.TTRevCommonLogic/GetNewRechargeActPopupInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTRevCommonLogicClient) GetRechargeBannerInfo(ctx context.Context, in *tt_rev_common_logic.GetRechargeBannerInfoRequest, opts ...grpc.CallOption) (*tt_rev_common_logic.GetRechargeBannerInfoResponse, error) {
	out := new(tt_rev_common_logic.GetRechargeBannerInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.tt_rev_common_logic.TTRevCommonLogic/GetRechargeBannerInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tTRevCommonLogicClient) CheckCanModifySex(ctx context.Context, in *tt_rev_common_logic.CheckCanModifySexRequest, opts ...grpc.CallOption) (*tt_rev_common_logic.CheckCanModifySexResponse, error) {
	out := new(tt_rev_common_logic.CheckCanModifySexResponse)
	err := c.cc.Invoke(ctx, "/ga.api.tt_rev_common_logic.TTRevCommonLogic/CheckCanModifySex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TTRevCommonLogicServer is the server API for TTRevCommonLogic service.
type TTRevCommonLogicServer interface {
	// 获取充值(首充)活动入口信息
	GetNewRechargeActEntryInfo(context.Context, *tt_rev_common_logic.GetNewRechargeActEntryInfoRequest) (*tt_rev_common_logic.GetNewRechargeActEntryInfoResponse, error)
	// 获取充值(首充)活动弹窗信息
	GetNewRechargeActPopupInfo(context.Context, *tt_rev_common_logic.GetNewRechargeActPopupInfoRequest) (*tt_rev_common_logic.GetNewRechargeActPopupInfoResponse, error)
	// 获取充值页banner信息
	GetRechargeBannerInfo(context.Context, *tt_rev_common_logic.GetRechargeBannerInfoRequest) (*tt_rev_common_logic.GetRechargeBannerInfoResponse, error)
	// 检查能否切换性别
	CheckCanModifySex(context.Context, *tt_rev_common_logic.CheckCanModifySexRequest) (*tt_rev_common_logic.CheckCanModifySexResponse, error)
}

func RegisterTTRevCommonLogicServer(s *grpc.Server, srv TTRevCommonLogicServer) {
	s.RegisterService(&_TTRevCommonLogic_serviceDesc, srv)
}

func _TTRevCommonLogic_GetNewRechargeActEntryInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tt_rev_common_logic.GetNewRechargeActEntryInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTRevCommonLogicServer).GetNewRechargeActEntryInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.tt_rev_common_logic.TTRevCommonLogic/GetNewRechargeActEntryInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTRevCommonLogicServer).GetNewRechargeActEntryInfo(ctx, req.(*tt_rev_common_logic.GetNewRechargeActEntryInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTRevCommonLogic_GetNewRechargeActPopupInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tt_rev_common_logic.GetNewRechargeActPopupInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTRevCommonLogicServer).GetNewRechargeActPopupInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.tt_rev_common_logic.TTRevCommonLogic/GetNewRechargeActPopupInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTRevCommonLogicServer).GetNewRechargeActPopupInfo(ctx, req.(*tt_rev_common_logic.GetNewRechargeActPopupInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTRevCommonLogic_GetRechargeBannerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tt_rev_common_logic.GetRechargeBannerInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTRevCommonLogicServer).GetRechargeBannerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.tt_rev_common_logic.TTRevCommonLogic/GetRechargeBannerInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTRevCommonLogicServer).GetRechargeBannerInfo(ctx, req.(*tt_rev_common_logic.GetRechargeBannerInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TTRevCommonLogic_CheckCanModifySex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tt_rev_common_logic.CheckCanModifySexRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTRevCommonLogicServer).CheckCanModifySex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.tt_rev_common_logic.TTRevCommonLogic/CheckCanModifySex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTRevCommonLogicServer).CheckCanModifySex(ctx, req.(*tt_rev_common_logic.CheckCanModifySexRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _TTRevCommonLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.tt_rev_common_logic.TTRevCommonLogic",
	HandlerType: (*TTRevCommonLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNewRechargeActEntryInfo",
			Handler:    _TTRevCommonLogic_GetNewRechargeActEntryInfo_Handler,
		},
		{
			MethodName: "GetNewRechargeActPopupInfo",
			Handler:    _TTRevCommonLogic_GetNewRechargeActPopupInfo_Handler,
		},
		{
			MethodName: "GetRechargeBannerInfo",
			Handler:    _TTRevCommonLogic_GetRechargeBannerInfo_Handler,
		},
		{
			MethodName: "CheckCanModifySex",
			Handler:    _TTRevCommonLogic_CheckCanModifySex_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/tt_rev_common_logic/grpc_tt_rev_common_logic.proto",
}

func init() {
	proto.RegisterFile("api/tt_rev_common_logic/grpc_tt_rev_common_logic.proto", fileDescriptor_grpc_tt_rev_common_logic_493b098e915cc901)
}

var fileDescriptor_grpc_tt_rev_common_logic_493b098e915cc901 = []byte{
	// 363 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x93, 0xcd, 0x4a, 0xf3, 0x40,
	0x14, 0x86, 0x69, 0xbe, 0x4f, 0x29, 0x59, 0x69, 0x44, 0x17, 0x81, 0x42, 0x71, 0x9f, 0x89, 0x56,
	0x2b, 0xa8, 0x2b, 0x1b, 0xa4, 0x0a, 0x2a, 0x25, 0x76, 0xe5, 0x26, 0x8c, 0xe3, 0xe9, 0x34, 0xd8,
	0xcc, 0x4c, 0x93, 0xd3, 0xbf, 0x5d, 0xe9, 0x46, 0x70, 0xe1, 0x1d, 0xb8, 0xf1, 0x16, 0x72, 0x19,
	0xfe, 0x5c, 0x93, 0x34, 0xa5, 0x6a, 0x69, 0x22, 0xc4, 0x5d, 0xc8, 0xfb, 0x3e, 0xe7, 0x3c, 0x8b,
	0x39, 0xfa, 0x01, 0x55, 0xbe, 0x8d, 0xe8, 0x85, 0xd0, 0xf7, 0x98, 0x0c, 0x02, 0x29, 0xbc, 0x8e,
	0xe4, 0x3e, 0xb3, 0x79, 0xa8, 0x98, 0x97, 0x12, 0x10, 0x15, 0x4a, 0x94, 0x86, 0xc9, 0x29, 0xa1,
	0xca, 0x27, 0x29, 0x0d, 0xb3, 0x34, 0x9d, 0x09, 0x43, 0x04, 0x11, 0xf9, 0x52, 0x7c, 0x7f, 0xcd,
	0x50, 0xd3, 0x4a, 0x5b, 0x97, 0xb9, 0xa9, 0xf2, 0xb0, 0xa2, 0xaf, 0x35, 0x9b, 0x2e, 0xf4, 0x9d,
	0x24, 0xbb, 0x98, 0x46, 0xc6, 0x73, 0x41, 0x37, 0xeb, 0x80, 0x57, 0x30, 0x70, 0x81, 0xb5, 0x69,
	0xc8, 0xe1, 0x84, 0xe1, 0xa9, 0xc0, 0x70, 0x74, 0x2e, 0x5a, 0xd2, 0x38, 0x24, 0x9c, 0xa6, 0xa9,
	0x91, 0x6c, 0xc6, 0x85, 0x6e, 0x0f, 0x22, 0x34, 0x8f, 0xfe, 0x82, 0x46, 0x4a, 0x8a, 0x08, 0xb6,
	0x8b, 0x93, 0x71, 0xf9, 0x7f, 0xf1, 0x35, 0xd6, 0xd2, 0xfd, 0x1a, 0x52, 0xf5, 0x54, 0x4e, 0xbf,
	0x2f, 0x26, 0xbf, 0xdf, 0x0f, 0x74, 0xc1, 0xef, 0x2d, 0xd6, 0x8c, 0xa7, 0x82, 0xbe, 0x59, 0x07,
	0x9c, 0xb7, 0x6b, 0x54, 0x08, 0x08, 0x13, 0xb5, 0xfd, 0x5f, 0xe6, 0x2f, 0xd7, 0xe7, 0x56, 0xd5,
	0x9c, 0xd4, 0x82, 0xd0, 0x7b, 0xac, 0x19, 0x93, 0x82, 0xbe, 0xee, 0xb4, 0x81, 0xdd, 0x3b, 0x54,
	0x5c, 0xca, 0x3b, 0xbf, 0x35, 0xba, 0x86, 0xa1, 0xb1, 0x93, 0x35, 0x76, 0xa9, 0x3a, 0x17, 0xd9,
	0xcd, 0x41, 0x2c, 0x48, 0x7c, 0xc4, 0x9a, 0x59, 0x9a, 0x8c, 0xcb, 0x1b, 0x88, 0x56, 0x08, 0x7d,
	0x6b, 0x06, 0x5b, 0x09, 0xfc, 0x38, 0x2e, 0x6b, 0x5c, 0xd6, 0xda, 0xfa, 0x16, 0x93, 0x01, 0xe9,
	0xf6, 0x06, 0x54, 0x10, 0xc4, 0xd9, 0xfb, 0x9c, 0x5e, 0xc1, 0xcd, 0x19, 0x97, 0x1d, 0x2a, 0x38,
	0xa9, 0x56, 0x10, 0x09, 0x93, 0x81, 0x9d, 0x44, 0x4c, 0x76, 0x6c, 0xaa, 0x94, 0x9d, 0x71, 0x62,
	0xc7, 0x29, 0xff, 0x5e, 0xb4, 0x7f, 0x6e, 0xc3, 0xb9, 0x5d, 0x4d, 0xf8, 0xbd, 0xcf, 0x00, 0x00,
	0x00, 0xff, 0xff, 0x43, 0x84, 0x59, 0x7b, 0x9e, 0x03, 0x00, 0x00,
}
