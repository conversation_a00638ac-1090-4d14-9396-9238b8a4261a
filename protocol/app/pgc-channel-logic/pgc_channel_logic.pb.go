// Code generated by protoc-gen-go. DO NOT EDIT.
// source: pgc_channel_logic/pgc_channel_logic.proto

package pgc_channel_logic // import "golang.52tt.com/protocol/app/pgc-channel-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 冠名类型
type ETitleType int32

const (
	ETitleType_E_TITLE_TYPE_UNSPECIFIED ETitleType = 0
	ETitleType_E_TITLE_TYPE_WEEK        ETitleType = 1
	ETitleType_E_TITLE_TYPE_MONTH       ETitleType = 2
	ETitleType_E_TITLE_TYPE_DAY         ETitleType = 3
)

var ETitleType_name = map[int32]string{
	0: "E_TITLE_TYPE_UNSPECIFIED",
	1: "E_TITLE_TYPE_WEEK",
	2: "E_TITLE_TYPE_MONTH",
	3: "E_TITLE_TYPE_DAY",
}
var ETitleType_value = map[string]int32{
	"E_TITLE_TYPE_UNSPECIFIED": 0,
	"E_TITLE_TYPE_WEEK":        1,
	"E_TITLE_TYPE_MONTH":       2,
	"E_TITLE_TYPE_DAY":         3,
}

func (x ETitleType) String() string {
	return proto.EnumName(ETitleType_name, int32(x))
}
func (ETitleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{0}
}

// 权益类型 移位定义
type PrivilegeType int32

const (
	PrivilegeType_PRIVILEGE_TYPE_UNSPECIFIED  PrivilegeType = 0
	PrivilegeType_PRIVILEGE_TYPE_ENTER_EFFECT PrivilegeType = 1
	PrivilegeType_PRIVILEGE_TYPE_HEADWEAR     PrivilegeType = 2
	PrivilegeType_PRIVILEGE_TYPE_GIFT_SEND    PrivilegeType = 4
	PrivilegeType_PRIVILEGE_TYPE_POP_UP       PrivilegeType = 8
	PrivilegeType_PRIVILEGE_TYPE_PLATE        PrivilegeType = 16
)

var PrivilegeType_name = map[int32]string{
	0:  "PRIVILEGE_TYPE_UNSPECIFIED",
	1:  "PRIVILEGE_TYPE_ENTER_EFFECT",
	2:  "PRIVILEGE_TYPE_HEADWEAR",
	4:  "PRIVILEGE_TYPE_GIFT_SEND",
	8:  "PRIVILEGE_TYPE_POP_UP",
	16: "PRIVILEGE_TYPE_PLATE",
}
var PrivilegeType_value = map[string]int32{
	"PRIVILEGE_TYPE_UNSPECIFIED":  0,
	"PRIVILEGE_TYPE_ENTER_EFFECT": 1,
	"PRIVILEGE_TYPE_HEADWEAR":     2,
	"PRIVILEGE_TYPE_GIFT_SEND":    4,
	"PRIVILEGE_TYPE_POP_UP":       8,
	"PRIVILEGE_TYPE_PLATE":        16,
}

func (x PrivilegeType) String() string {
	return proto.EnumName(PrivilegeType_name, int32(x))
}
func (PrivilegeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{1}
}

// 图片样式
type TitlePrivilege_ImgStyle int32

const (
	TitlePrivilege_IMG_STYLE_UNSPECIFIED TitlePrivilege_ImgStyle = 0
	TitlePrivilege_IMG_STYLE_SHORT       TitlePrivilege_ImgStyle = 1
	TitlePrivilege_IMG_STYLE_LONG        TitlePrivilege_ImgStyle = 2
)

var TitlePrivilege_ImgStyle_name = map[int32]string{
	0: "IMG_STYLE_UNSPECIFIED",
	1: "IMG_STYLE_SHORT",
	2: "IMG_STYLE_LONG",
}
var TitlePrivilege_ImgStyle_value = map[string]int32{
	"IMG_STYLE_UNSPECIFIED": 0,
	"IMG_STYLE_SHORT":       1,
	"IMG_STYLE_LONG":        2,
}

func (x TitlePrivilege_ImgStyle) String() string {
	return proto.EnumName(TitlePrivilege_ImgStyle_name, int32(x))
}
func (TitlePrivilege_ImgStyle) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{13, 0}
}

// 图片资源类型
type TitlePrivilege_ImgType int32

const (
	TitlePrivilege_IMG_TYPE_UNSPECIFIED TitlePrivilege_ImgType = 0
	TitlePrivilege_IMG_TYPE_STATIC      TitlePrivilege_ImgType = 1
	TitlePrivilege_IMG_TYPE_DYNAMIC     TitlePrivilege_ImgType = 2
)

var TitlePrivilege_ImgType_name = map[int32]string{
	0: "IMG_TYPE_UNSPECIFIED",
	1: "IMG_TYPE_STATIC",
	2: "IMG_TYPE_DYNAMIC",
}
var TitlePrivilege_ImgType_value = map[string]int32{
	"IMG_TYPE_UNSPECIFIED": 0,
	"IMG_TYPE_STATIC":      1,
	"IMG_TYPE_DYNAMIC":     2,
}

func (x TitlePrivilege_ImgType) String() string {
	return proto.EnumName(TitlePrivilege_ImgType_name, int32(x))
}
func (TitlePrivilege_ImgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{13, 1}
}

// 体验券信息
type TicketInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	PopupMsg             string   `protobuf:"bytes,3,opt,name=popup_msg,json=popupMsg,proto3" json:"popup_msg,omitempty"`
	ExpireTs             uint32   `protobuf:"varint,4,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	IconUrl              string   `protobuf:"bytes,5,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	ChJumpRul            string   `protobuf:"bytes,6,opt,name=ch_jump_rul,json=chJumpRul,proto3" json:"ch_jump_rul,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TicketInfo) Reset()         { *m = TicketInfo{} }
func (m *TicketInfo) String() string { return proto.CompactTextString(m) }
func (*TicketInfo) ProtoMessage()    {}
func (*TicketInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{0}
}
func (m *TicketInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketInfo.Unmarshal(m, b)
}
func (m *TicketInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketInfo.Marshal(b, m, deterministic)
}
func (dst *TicketInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketInfo.Merge(dst, src)
}
func (m *TicketInfo) XXX_Size() int {
	return xxx_messageInfo_TicketInfo.Size(m)
}
func (m *TicketInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TicketInfo proto.InternalMessageInfo

func (m *TicketInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TicketInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TicketInfo) GetPopupMsg() string {
	if m != nil {
		return m.PopupMsg
	}
	return ""
}

func (m *TicketInfo) GetExpireTs() uint32 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

func (m *TicketInfo) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *TicketInfo) GetChJumpRul() string {
	if m != nil {
		return m.ChJumpRul
	}
	return ""
}

// 获取用户体验券列表
type GetUserTicketListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserTicketListRequest) Reset()         { *m = GetUserTicketListRequest{} }
func (m *GetUserTicketListRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserTicketListRequest) ProtoMessage()    {}
func (*GetUserTicketListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{1}
}
func (m *GetUserTicketListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTicketListRequest.Unmarshal(m, b)
}
func (m *GetUserTicketListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTicketListRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserTicketListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTicketListRequest.Merge(dst, src)
}
func (m *GetUserTicketListRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserTicketListRequest.Size(m)
}
func (m *GetUserTicketListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTicketListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTicketListRequest proto.InternalMessageInfo

func (m *GetUserTicketListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserTicketListResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TicketList           []*TicketInfo `protobuf:"bytes,2,rep,name=ticket_list,json=ticketList,proto3" json:"ticket_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserTicketListResponse) Reset()         { *m = GetUserTicketListResponse{} }
func (m *GetUserTicketListResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserTicketListResponse) ProtoMessage()    {}
func (*GetUserTicketListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{2}
}
func (m *GetUserTicketListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTicketListResponse.Unmarshal(m, b)
}
func (m *GetUserTicketListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTicketListResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserTicketListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTicketListResponse.Merge(dst, src)
}
func (m *GetUserTicketListResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserTicketListResponse.Size(m)
}
func (m *GetUserTicketListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTicketListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTicketListResponse proto.InternalMessageInfo

func (m *GetUserTicketListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserTicketListResponse) GetTicketList() []*TicketInfo {
	if m != nil {
		return m.TicketList
	}
	return nil
}

// 检测体验券的使用权限
type CheckTicketUsePerRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32       `protobuf:"varint,3,opt,name=cid,proto3" json:"cid,omitempty"`
	TicketId             uint32       `protobuf:"varint,4,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckTicketUsePerRequest) Reset()         { *m = CheckTicketUsePerRequest{} }
func (m *CheckTicketUsePerRequest) String() string { return proto.CompactTextString(m) }
func (*CheckTicketUsePerRequest) ProtoMessage()    {}
func (*CheckTicketUsePerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{3}
}
func (m *CheckTicketUsePerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTicketUsePerRequest.Unmarshal(m, b)
}
func (m *CheckTicketUsePerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTicketUsePerRequest.Marshal(b, m, deterministic)
}
func (dst *CheckTicketUsePerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTicketUsePerRequest.Merge(dst, src)
}
func (m *CheckTicketUsePerRequest) XXX_Size() int {
	return xxx_messageInfo_CheckTicketUsePerRequest.Size(m)
}
func (m *CheckTicketUsePerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTicketUsePerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTicketUsePerRequest proto.InternalMessageInfo

func (m *CheckTicketUsePerRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckTicketUsePerRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckTicketUsePerRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *CheckTicketUsePerRequest) GetTicketId() uint32 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

type CheckTicketUsePerResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	HasPerChannel        bool          `protobuf:"varint,2,opt,name=has_per_channel,json=hasPerChannel,proto3" json:"has_per_channel,omitempty"`
	HasPerUser           bool          `protobuf:"varint,3,opt,name=has_per_user,json=hasPerUser,proto3" json:"has_per_user,omitempty"`
	ChJumpRul            string        `protobuf:"bytes,4,opt,name=ch_jump_rul,json=chJumpRul,proto3" json:"ch_jump_rul,omitempty"`
	PkgTopMsg            string        `protobuf:"bytes,5,opt,name=pkg_top_msg,json=pkgTopMsg,proto3" json:"pkg_top_msg,omitempty"`
	UserNoPerToast       string        `protobuf:"bytes,6,opt,name=user_no_per_toast,json=userNoPerToast,proto3" json:"user_no_per_toast,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CheckTicketUsePerResponse) Reset()         { *m = CheckTicketUsePerResponse{} }
func (m *CheckTicketUsePerResponse) String() string { return proto.CompactTextString(m) }
func (*CheckTicketUsePerResponse) ProtoMessage()    {}
func (*CheckTicketUsePerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{4}
}
func (m *CheckTicketUsePerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTicketUsePerResponse.Unmarshal(m, b)
}
func (m *CheckTicketUsePerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTicketUsePerResponse.Marshal(b, m, deterministic)
}
func (dst *CheckTicketUsePerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTicketUsePerResponse.Merge(dst, src)
}
func (m *CheckTicketUsePerResponse) XXX_Size() int {
	return xxx_messageInfo_CheckTicketUsePerResponse.Size(m)
}
func (m *CheckTicketUsePerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTicketUsePerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTicketUsePerResponse proto.InternalMessageInfo

func (m *CheckTicketUsePerResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckTicketUsePerResponse) GetHasPerChannel() bool {
	if m != nil {
		return m.HasPerChannel
	}
	return false
}

func (m *CheckTicketUsePerResponse) GetHasPerUser() bool {
	if m != nil {
		return m.HasPerUser
	}
	return false
}

func (m *CheckTicketUsePerResponse) GetChJumpRul() string {
	if m != nil {
		return m.ChJumpRul
	}
	return ""
}

func (m *CheckTicketUsePerResponse) GetPkgTopMsg() string {
	if m != nil {
		return m.PkgTopMsg
	}
	return ""
}

func (m *CheckTicketUsePerResponse) GetUserNoPerToast() string {
	if m != nil {
		return m.UserNoPerToast
	}
	return ""
}

// 获取券的提醒信息
type GetUserTicketRemindRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserTicketRemindRequest) Reset()         { *m = GetUserTicketRemindRequest{} }
func (m *GetUserTicketRemindRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserTicketRemindRequest) ProtoMessage()    {}
func (*GetUserTicketRemindRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{5}
}
func (m *GetUserTicketRemindRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTicketRemindRequest.Unmarshal(m, b)
}
func (m *GetUserTicketRemindRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTicketRemindRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserTicketRemindRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTicketRemindRequest.Merge(dst, src)
}
func (m *GetUserTicketRemindRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserTicketRemindRequest.Size(m)
}
func (m *GetUserTicketRemindRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTicketRemindRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTicketRemindRequest proto.InternalMessageInfo

func (m *GetUserTicketRemindRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserTicketRemindRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

type GetUserTicketRemindResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsRemind             bool          `protobuf:"varint,2,opt,name=is_remind,json=isRemind,proto3" json:"is_remind,omitempty"`
	InfoList             []*TicketInfo `protobuf:"bytes,3,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserTicketRemindResponse) Reset()         { *m = GetUserTicketRemindResponse{} }
func (m *GetUserTicketRemindResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserTicketRemindResponse) ProtoMessage()    {}
func (*GetUserTicketRemindResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{6}
}
func (m *GetUserTicketRemindResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTicketRemindResponse.Unmarshal(m, b)
}
func (m *GetUserTicketRemindResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTicketRemindResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserTicketRemindResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTicketRemindResponse.Merge(dst, src)
}
func (m *GetUserTicketRemindResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserTicketRemindResponse.Size(m)
}
func (m *GetUserTicketRemindResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTicketRemindResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTicketRemindResponse proto.InternalMessageInfo

func (m *GetUserTicketRemindResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserTicketRemindResponse) GetIsRemind() bool {
	if m != nil {
		return m.IsRemind
	}
	return false
}

func (m *GetUserTicketRemindResponse) GetInfoList() []*TicketInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 用户房间可用券信息推送
type UserTicketListPushMsg struct {
	Cid                  uint32        `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	InfoList             []*TicketInfo `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UserTicketListPushMsg) Reset()         { *m = UserTicketListPushMsg{} }
func (m *UserTicketListPushMsg) String() string { return proto.CompactTextString(m) }
func (*UserTicketListPushMsg) ProtoMessage()    {}
func (*UserTicketListPushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{7}
}
func (m *UserTicketListPushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserTicketListPushMsg.Unmarshal(m, b)
}
func (m *UserTicketListPushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserTicketListPushMsg.Marshal(b, m, deterministic)
}
func (dst *UserTicketListPushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserTicketListPushMsg.Merge(dst, src)
}
func (m *UserTicketListPushMsg) XXX_Size() int {
	return xxx_messageInfo_UserTicketListPushMsg.Size(m)
}
func (m *UserTicketListPushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_UserTicketListPushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_UserTicketListPushMsg proto.InternalMessageInfo

func (m *UserTicketListPushMsg) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *UserTicketListPushMsg) GetInfoList() []*TicketInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 获取用户的被冠名信息
type GetUserTitledInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserTitledInfoRequest) Reset()         { *m = GetUserTitledInfoRequest{} }
func (m *GetUserTitledInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserTitledInfoRequest) ProtoMessage()    {}
func (*GetUserTitledInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{8}
}
func (m *GetUserTitledInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTitledInfoRequest.Unmarshal(m, b)
}
func (m *GetUserTitledInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTitledInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserTitledInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTitledInfoRequest.Merge(dst, src)
}
func (m *GetUserTitledInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserTitledInfoRequest.Size(m)
}
func (m *GetUserTitledInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTitledInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTitledInfoRequest proto.InternalMessageInfo

func (m *GetUserTitledInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserTitledInfoRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetUserTitledInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetUserTitledInfoResponse struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	HasEntry             bool               `protobuf:"varint,2,opt,name=has_entry,json=hasEntry,proto3" json:"has_entry,omitempty"`
	ChannelTitledRank    string             `protobuf:"bytes,3,opt,name=channel_titled_rank,json=channelTitledRank,proto3" json:"channel_titled_rank,omitempty"`
	TitledCnt            uint32             `protobuf:"varint,4,opt,name=titled_cnt,json=titledCnt,proto3" json:"titled_cnt,omitempty"`
	TopTitledList        []*app.UserProfile `protobuf:"bytes,5,rep,name=top_titled_list,json=topTitledList,proto3" json:"top_titled_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserTitledInfoResponse) Reset()         { *m = GetUserTitledInfoResponse{} }
func (m *GetUserTitledInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserTitledInfoResponse) ProtoMessage()    {}
func (*GetUserTitledInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{9}
}
func (m *GetUserTitledInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTitledInfoResponse.Unmarshal(m, b)
}
func (m *GetUserTitledInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTitledInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserTitledInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTitledInfoResponse.Merge(dst, src)
}
func (m *GetUserTitledInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserTitledInfoResponse.Size(m)
}
func (m *GetUserTitledInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTitledInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTitledInfoResponse proto.InternalMessageInfo

func (m *GetUserTitledInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserTitledInfoResponse) GetHasEntry() bool {
	if m != nil {
		return m.HasEntry
	}
	return false
}

func (m *GetUserTitledInfoResponse) GetChannelTitledRank() string {
	if m != nil {
		return m.ChannelTitledRank
	}
	return ""
}

func (m *GetUserTitledInfoResponse) GetTitledCnt() uint32 {
	if m != nil {
		return m.TitledCnt
	}
	return 0
}

func (m *GetUserTitledInfoResponse) GetTopTitledList() []*app.UserProfile {
	if m != nil {
		return m.TopTitledList
	}
	return nil
}

// 用户冠名公屏提醒
type UserTitleImRemindInfo struct {
	Msg                  string           `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	HighLightTxt         string           `protobuf:"bytes,2,opt,name=high_light_txt,json=highLightTxt,proto3" json:"high_light_txt,omitempty"`
	TitledUser           *app.UserProfile `protobuf:"bytes,3,opt,name=titled_user,json=titledUser,proto3" json:"titled_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserTitleImRemindInfo) Reset()         { *m = UserTitleImRemindInfo{} }
func (m *UserTitleImRemindInfo) String() string { return proto.CompactTextString(m) }
func (*UserTitleImRemindInfo) ProtoMessage()    {}
func (*UserTitleImRemindInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{10}
}
func (m *UserTitleImRemindInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserTitleImRemindInfo.Unmarshal(m, b)
}
func (m *UserTitleImRemindInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserTitleImRemindInfo.Marshal(b, m, deterministic)
}
func (dst *UserTitleImRemindInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserTitleImRemindInfo.Merge(dst, src)
}
func (m *UserTitleImRemindInfo) XXX_Size() int {
	return xxx_messageInfo_UserTitleImRemindInfo.Size(m)
}
func (m *UserTitleImRemindInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserTitleImRemindInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserTitleImRemindInfo proto.InternalMessageInfo

func (m *UserTitleImRemindInfo) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *UserTitleImRemindInfo) GetHighLightTxt() string {
	if m != nil {
		return m.HighLightTxt
	}
	return ""
}

func (m *UserTitleImRemindInfo) GetTitledUser() *app.UserProfile {
	if m != nil {
		return m.TitledUser
	}
	return nil
}

// 用户冠名弹窗提醒
type UserTitlePopRemindInfo struct {
	Title                string           `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Content              string           `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	TitledUser           *app.UserProfile `protobuf:"bytes,3,opt,name=titled_user,json=titledUser,proto3" json:"titled_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserTitlePopRemindInfo) Reset()         { *m = UserTitlePopRemindInfo{} }
func (m *UserTitlePopRemindInfo) String() string { return proto.CompactTextString(m) }
func (*UserTitlePopRemindInfo) ProtoMessage()    {}
func (*UserTitlePopRemindInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{11}
}
func (m *UserTitlePopRemindInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserTitlePopRemindInfo.Unmarshal(m, b)
}
func (m *UserTitlePopRemindInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserTitlePopRemindInfo.Marshal(b, m, deterministic)
}
func (dst *UserTitlePopRemindInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserTitlePopRemindInfo.Merge(dst, src)
}
func (m *UserTitlePopRemindInfo) XXX_Size() int {
	return xxx_messageInfo_UserTitlePopRemindInfo.Size(m)
}
func (m *UserTitlePopRemindInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserTitlePopRemindInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserTitlePopRemindInfo proto.InternalMessageInfo

func (m *UserTitlePopRemindInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *UserTitlePopRemindInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *UserTitlePopRemindInfo) GetTitledUser() *app.UserProfile {
	if m != nil {
		return m.TitledUser
	}
	return nil
}

// 冠名用户进房提示
type TitleUserEnterChannelMsg struct {
	Msg                  string           `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	TitleType            uint32           `protobuf:"varint,2,opt,name=title_type,json=titleType,proto3" json:"title_type,omitempty"`
	TitleUser            *app.UserProfile `protobuf:"bytes,3,opt,name=title_user,json=titleUser,proto3" json:"title_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *TitleUserEnterChannelMsg) Reset()         { *m = TitleUserEnterChannelMsg{} }
func (m *TitleUserEnterChannelMsg) String() string { return proto.CompactTextString(m) }
func (*TitleUserEnterChannelMsg) ProtoMessage()    {}
func (*TitleUserEnterChannelMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{12}
}
func (m *TitleUserEnterChannelMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TitleUserEnterChannelMsg.Unmarshal(m, b)
}
func (m *TitleUserEnterChannelMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TitleUserEnterChannelMsg.Marshal(b, m, deterministic)
}
func (dst *TitleUserEnterChannelMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TitleUserEnterChannelMsg.Merge(dst, src)
}
func (m *TitleUserEnterChannelMsg) XXX_Size() int {
	return xxx_messageInfo_TitleUserEnterChannelMsg.Size(m)
}
func (m *TitleUserEnterChannelMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_TitleUserEnterChannelMsg.DiscardUnknown(m)
}

var xxx_messageInfo_TitleUserEnterChannelMsg proto.InternalMessageInfo

func (m *TitleUserEnterChannelMsg) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *TitleUserEnterChannelMsg) GetTitleType() uint32 {
	if m != nil {
		return m.TitleType
	}
	return 0
}

func (m *TitleUserEnterChannelMsg) GetTitleUser() *app.UserProfile {
	if m != nil {
		return m.TitleUser
	}
	return nil
}

type TitlePrivilege struct {
	SmallImg             string   `protobuf:"bytes,1,opt,name=small_img,json=smallImg,proto3" json:"small_img,omitempty"`
	BigImg               string   `protobuf:"bytes,2,opt,name=big_img,json=bigImg,proto3" json:"big_img,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	DescMsg              string   `protobuf:"bytes,4,opt,name=desc_msg,json=descMsg,proto3" json:"desc_msg,omitempty"`
	ImgStyle             uint32   `protobuf:"varint,5,opt,name=img_style,json=imgStyle,proto3" json:"img_style,omitempty"`
	ImgType              uint32   `protobuf:"varint,6,opt,name=img_type,json=imgType,proto3" json:"img_type,omitempty"`
	PrivilegeType        uint32   `protobuf:"varint,7,opt,name=privilege_type,json=privilegeType,proto3" json:"privilege_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TitlePrivilege) Reset()         { *m = TitlePrivilege{} }
func (m *TitlePrivilege) String() string { return proto.CompactTextString(m) }
func (*TitlePrivilege) ProtoMessage()    {}
func (*TitlePrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{13}
}
func (m *TitlePrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TitlePrivilege.Unmarshal(m, b)
}
func (m *TitlePrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TitlePrivilege.Marshal(b, m, deterministic)
}
func (dst *TitlePrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TitlePrivilege.Merge(dst, src)
}
func (m *TitlePrivilege) XXX_Size() int {
	return xxx_messageInfo_TitlePrivilege.Size(m)
}
func (m *TitlePrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_TitlePrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_TitlePrivilege proto.InternalMessageInfo

func (m *TitlePrivilege) GetSmallImg() string {
	if m != nil {
		return m.SmallImg
	}
	return ""
}

func (m *TitlePrivilege) GetBigImg() string {
	if m != nil {
		return m.BigImg
	}
	return ""
}

func (m *TitlePrivilege) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TitlePrivilege) GetDescMsg() string {
	if m != nil {
		return m.DescMsg
	}
	return ""
}

func (m *TitlePrivilege) GetImgStyle() uint32 {
	if m != nil {
		return m.ImgStyle
	}
	return 0
}

func (m *TitlePrivilege) GetImgType() uint32 {
	if m != nil {
		return m.ImgType
	}
	return 0
}

func (m *TitlePrivilege) GetPrivilegeType() uint32 {
	if m != nil {
		return m.PrivilegeType
	}
	return 0
}

type TitlePrivilegeList struct {
	List                 []*TitlePrivilege `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *TitlePrivilegeList) Reset()         { *m = TitlePrivilegeList{} }
func (m *TitlePrivilegeList) String() string { return proto.CompactTextString(m) }
func (*TitlePrivilegeList) ProtoMessage()    {}
func (*TitlePrivilegeList) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{14}
}
func (m *TitlePrivilegeList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TitlePrivilegeList.Unmarshal(m, b)
}
func (m *TitlePrivilegeList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TitlePrivilegeList.Marshal(b, m, deterministic)
}
func (dst *TitlePrivilegeList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TitlePrivilegeList.Merge(dst, src)
}
func (m *TitlePrivilegeList) XXX_Size() int {
	return xxx_messageInfo_TitlePrivilegeList.Size(m)
}
func (m *TitlePrivilegeList) XXX_DiscardUnknown() {
	xxx_messageInfo_TitlePrivilegeList.DiscardUnknown(m)
}

var xxx_messageInfo_TitlePrivilegeList proto.InternalMessageInfo

func (m *TitlePrivilegeList) GetList() []*TitlePrivilege {
	if m != nil {
		return m.List
	}
	return nil
}

// 获取用户的冠名信息
type GetUserTitleInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserTitleInfoRequest) Reset()         { *m = GetUserTitleInfoRequest{} }
func (m *GetUserTitleInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserTitleInfoRequest) ProtoMessage()    {}
func (*GetUserTitleInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{15}
}
func (m *GetUserTitleInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTitleInfoRequest.Unmarshal(m, b)
}
func (m *GetUserTitleInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTitleInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserTitleInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTitleInfoRequest.Merge(dst, src)
}
func (m *GetUserTitleInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserTitleInfoRequest.Size(m)
}
func (m *GetUserTitleInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTitleInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTitleInfoRequest proto.InternalMessageInfo

func (m *GetUserTitleInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserTitleInfoRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetUserTitleInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetUserTitleInfoResponse struct {
	BaseResp             *app.BaseResp                  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TitleUser            *app.UserProfile               `protobuf:"bytes,2,opt,name=title_user,json=titleUser,proto3" json:"title_user,omitempty"`
	TitledUser           *app.UserProfile               `protobuf:"bytes,3,opt,name=titled_user,json=titledUser,proto3" json:"titled_user,omitempty"`
	TitledCnt            uint32                         `protobuf:"varint,4,opt,name=titled_cnt,json=titledCnt,proto3" json:"titled_cnt,omitempty"`
	ProcessMsg           string                         `protobuf:"bytes,5,opt,name=process_msg,json=processMsg,proto3" json:"process_msg,omitempty"`
	SendGiftVal          uint32                         `protobuf:"varint,6,opt,name=send_gift_val,json=sendGiftVal,proto3" json:"send_gift_val,omitempty"`
	MapTypeTotal         map[uint32]uint32              `protobuf:"bytes,7,rep,name=map_type_total,json=mapTypeTotal,proto3" json:"map_type_total,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapTypeExpireTs      map[uint32]uint32              `protobuf:"bytes,8,rep,name=map_type_expire_ts,json=mapTypeExpireTs,proto3" json:"map_type_expire_ts,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	PrivilegeSwitch      bool                           `protobuf:"varint,9,opt,name=privilege_switch,json=privilegeSwitch,proto3" json:"privilege_switch,omitempty"`
	TitleType            uint32                         `protobuf:"varint,10,opt,name=title_type,json=titleType,proto3" json:"title_type,omitempty"`
	MapTypePrivilege     map[uint32]*TitlePrivilegeList `protobuf:"bytes,11,rep,name=map_type_privilege,json=mapTypePrivilege,proto3" json:"map_type_privilege,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	DescMsg              string                         `protobuf:"bytes,12,opt,name=desc_msg,json=descMsg,proto3" json:"desc_msg,omitempty"`
	MapTypeSwitch        map[uint32]bool                `protobuf:"bytes,13,rep,name=map_type_switch,json=mapTypeSwitch,proto3" json:"map_type_switch,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *GetUserTitleInfoResponse) Reset()         { *m = GetUserTitleInfoResponse{} }
func (m *GetUserTitleInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserTitleInfoResponse) ProtoMessage()    {}
func (*GetUserTitleInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{16}
}
func (m *GetUserTitleInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTitleInfoResponse.Unmarshal(m, b)
}
func (m *GetUserTitleInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTitleInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserTitleInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTitleInfoResponse.Merge(dst, src)
}
func (m *GetUserTitleInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserTitleInfoResponse.Size(m)
}
func (m *GetUserTitleInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTitleInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTitleInfoResponse proto.InternalMessageInfo

func (m *GetUserTitleInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserTitleInfoResponse) GetTitleUser() *app.UserProfile {
	if m != nil {
		return m.TitleUser
	}
	return nil
}

func (m *GetUserTitleInfoResponse) GetTitledUser() *app.UserProfile {
	if m != nil {
		return m.TitledUser
	}
	return nil
}

func (m *GetUserTitleInfoResponse) GetTitledCnt() uint32 {
	if m != nil {
		return m.TitledCnt
	}
	return 0
}

func (m *GetUserTitleInfoResponse) GetProcessMsg() string {
	if m != nil {
		return m.ProcessMsg
	}
	return ""
}

func (m *GetUserTitleInfoResponse) GetSendGiftVal() uint32 {
	if m != nil {
		return m.SendGiftVal
	}
	return 0
}

func (m *GetUserTitleInfoResponse) GetMapTypeTotal() map[uint32]uint32 {
	if m != nil {
		return m.MapTypeTotal
	}
	return nil
}

func (m *GetUserTitleInfoResponse) GetMapTypeExpireTs() map[uint32]uint32 {
	if m != nil {
		return m.MapTypeExpireTs
	}
	return nil
}

func (m *GetUserTitleInfoResponse) GetPrivilegeSwitch() bool {
	if m != nil {
		return m.PrivilegeSwitch
	}
	return false
}

func (m *GetUserTitleInfoResponse) GetTitleType() uint32 {
	if m != nil {
		return m.TitleType
	}
	return 0
}

func (m *GetUserTitleInfoResponse) GetMapTypePrivilege() map[uint32]*TitlePrivilegeList {
	if m != nil {
		return m.MapTypePrivilege
	}
	return nil
}

func (m *GetUserTitleInfoResponse) GetDescMsg() string {
	if m != nil {
		return m.DescMsg
	}
	return ""
}

func (m *GetUserTitleInfoResponse) GetMapTypeSwitch() map[uint32]bool {
	if m != nil {
		return m.MapTypeSwitch
	}
	return nil
}

// 冠名成功弹窗
type TitleSuccessNotify struct {
	EffectUrl            string   `protobuf:"bytes,1,opt,name=effect_url,json=effectUrl,proto3" json:"effect_url,omitempty"`
	EffectMd5            string   `protobuf:"bytes,2,opt,name=effect_md5,json=effectMd5,proto3" json:"effect_md5,omitempty"`
	EffectExtendJson     string   `protobuf:"bytes,3,opt,name=effect_extend_json,json=effectExtendJson,proto3" json:"effect_extend_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TitleSuccessNotify) Reset()         { *m = TitleSuccessNotify{} }
func (m *TitleSuccessNotify) String() string { return proto.CompactTextString(m) }
func (*TitleSuccessNotify) ProtoMessage()    {}
func (*TitleSuccessNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{17}
}
func (m *TitleSuccessNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TitleSuccessNotify.Unmarshal(m, b)
}
func (m *TitleSuccessNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TitleSuccessNotify.Marshal(b, m, deterministic)
}
func (dst *TitleSuccessNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TitleSuccessNotify.Merge(dst, src)
}
func (m *TitleSuccessNotify) XXX_Size() int {
	return xxx_messageInfo_TitleSuccessNotify.Size(m)
}
func (m *TitleSuccessNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_TitleSuccessNotify.DiscardUnknown(m)
}

var xxx_messageInfo_TitleSuccessNotify proto.InternalMessageInfo

func (m *TitleSuccessNotify) GetEffectUrl() string {
	if m != nil {
		return m.EffectUrl
	}
	return ""
}

func (m *TitleSuccessNotify) GetEffectMd5() string {
	if m != nil {
		return m.EffectMd5
	}
	return ""
}

func (m *TitleSuccessNotify) GetEffectExtendJson() string {
	if m != nil {
		return m.EffectExtendJson
	}
	return ""
}

// 用户冠名权益开关
type UserTitlePrivilegeSwitchRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	IsOn                 bool         `protobuf:"varint,2,opt,name=is_on,json=isOn,proto3" json:"is_on,omitempty"`
	TargetUid            uint32       `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PrivilgeType         uint32       `protobuf:"varint,5,opt,name=privilge_type,json=privilgeType,proto3" json:"privilge_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UserTitlePrivilegeSwitchRequest) Reset()         { *m = UserTitlePrivilegeSwitchRequest{} }
func (m *UserTitlePrivilegeSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*UserTitlePrivilegeSwitchRequest) ProtoMessage()    {}
func (*UserTitlePrivilegeSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{18}
}
func (m *UserTitlePrivilegeSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserTitlePrivilegeSwitchRequest.Unmarshal(m, b)
}
func (m *UserTitlePrivilegeSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserTitlePrivilegeSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *UserTitlePrivilegeSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserTitlePrivilegeSwitchRequest.Merge(dst, src)
}
func (m *UserTitlePrivilegeSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_UserTitlePrivilegeSwitchRequest.Size(m)
}
func (m *UserTitlePrivilegeSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserTitlePrivilegeSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserTitlePrivilegeSwitchRequest proto.InternalMessageInfo

func (m *UserTitlePrivilegeSwitchRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UserTitlePrivilegeSwitchRequest) GetIsOn() bool {
	if m != nil {
		return m.IsOn
	}
	return false
}

func (m *UserTitlePrivilegeSwitchRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *UserTitlePrivilegeSwitchRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserTitlePrivilegeSwitchRequest) GetPrivilgeType() uint32 {
	if m != nil {
		return m.PrivilgeType
	}
	return 0
}

type UserTitlePrivilegeSwitchResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UserTitlePrivilegeSwitchResponse) Reset()         { *m = UserTitlePrivilegeSwitchResponse{} }
func (m *UserTitlePrivilegeSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*UserTitlePrivilegeSwitchResponse) ProtoMessage()    {}
func (*UserTitlePrivilegeSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{19}
}
func (m *UserTitlePrivilegeSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserTitlePrivilegeSwitchResponse.Unmarshal(m, b)
}
func (m *UserTitlePrivilegeSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserTitlePrivilegeSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *UserTitlePrivilegeSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserTitlePrivilegeSwitchResponse.Merge(dst, src)
}
func (m *UserTitlePrivilegeSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_UserTitlePrivilegeSwitchResponse.Size(m)
}
func (m *UserTitlePrivilegeSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserTitlePrivilegeSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserTitlePrivilegeSwitchResponse proto.InternalMessageInfo

func (m *UserTitlePrivilegeSwitchResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取房间冠名信息
type GetChannelTitleInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelTitleInfoRequest) Reset()         { *m = GetChannelTitleInfoRequest{} }
func (m *GetChannelTitleInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetChannelTitleInfoRequest) ProtoMessage()    {}
func (*GetChannelTitleInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{20}
}
func (m *GetChannelTitleInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTitleInfoRequest.Unmarshal(m, b)
}
func (m *GetChannelTitleInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTitleInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetChannelTitleInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTitleInfoRequest.Merge(dst, src)
}
func (m *GetChannelTitleInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetChannelTitleInfoRequest.Size(m)
}
func (m *GetChannelTitleInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTitleInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTitleInfoRequest proto.InternalMessageInfo

func (m *GetChannelTitleInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelTitleInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelTitleInfoResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	HasEntry             bool          `protobuf:"varint,2,opt,name=has_entry,json=hasEntry,proto3" json:"has_entry,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelTitleInfoResponse) Reset()         { *m = GetChannelTitleInfoResponse{} }
func (m *GetChannelTitleInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetChannelTitleInfoResponse) ProtoMessage()    {}
func (*GetChannelTitleInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{21}
}
func (m *GetChannelTitleInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTitleInfoResponse.Unmarshal(m, b)
}
func (m *GetChannelTitleInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTitleInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetChannelTitleInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTitleInfoResponse.Merge(dst, src)
}
func (m *GetChannelTitleInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetChannelTitleInfoResponse.Size(m)
}
func (m *GetChannelTitleInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTitleInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTitleInfoResponse proto.InternalMessageInfo

func (m *GetChannelTitleInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelTitleInfoResponse) GetHasEntry() bool {
	if m != nil {
		return m.HasEntry
	}
	return false
}

// 判断用户是否能被冠名
type CheckUserIsCanTitledRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckUserIsCanTitledRequest) Reset()         { *m = CheckUserIsCanTitledRequest{} }
func (m *CheckUserIsCanTitledRequest) String() string { return proto.CompactTextString(m) }
func (*CheckUserIsCanTitledRequest) ProtoMessage()    {}
func (*CheckUserIsCanTitledRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{22}
}
func (m *CheckUserIsCanTitledRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserIsCanTitledRequest.Unmarshal(m, b)
}
func (m *CheckUserIsCanTitledRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserIsCanTitledRequest.Marshal(b, m, deterministic)
}
func (dst *CheckUserIsCanTitledRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserIsCanTitledRequest.Merge(dst, src)
}
func (m *CheckUserIsCanTitledRequest) XXX_Size() int {
	return xxx_messageInfo_CheckUserIsCanTitledRequest.Size(m)
}
func (m *CheckUserIsCanTitledRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserIsCanTitledRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserIsCanTitledRequest proto.InternalMessageInfo

func (m *CheckUserIsCanTitledRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckUserIsCanTitledRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type CheckUserIsCanTitledResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsCan                bool          `protobuf:"varint,2,opt,name=is_can,json=isCan,proto3" json:"is_can,omitempty"`
	ToastMsg             string        `protobuf:"bytes,3,opt,name=toast_msg,json=toastMsg,proto3" json:"toast_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CheckUserIsCanTitledResponse) Reset()         { *m = CheckUserIsCanTitledResponse{} }
func (m *CheckUserIsCanTitledResponse) String() string { return proto.CompactTextString(m) }
func (*CheckUserIsCanTitledResponse) ProtoMessage()    {}
func (*CheckUserIsCanTitledResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{23}
}
func (m *CheckUserIsCanTitledResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserIsCanTitledResponse.Unmarshal(m, b)
}
func (m *CheckUserIsCanTitledResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserIsCanTitledResponse.Marshal(b, m, deterministic)
}
func (dst *CheckUserIsCanTitledResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserIsCanTitledResponse.Merge(dst, src)
}
func (m *CheckUserIsCanTitledResponse) XXX_Size() int {
	return xxx_messageInfo_CheckUserIsCanTitledResponse.Size(m)
}
func (m *CheckUserIsCanTitledResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserIsCanTitledResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserIsCanTitledResponse proto.InternalMessageInfo

func (m *CheckUserIsCanTitledResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckUserIsCanTitledResponse) GetIsCan() bool {
	if m != nil {
		return m.IsCan
	}
	return false
}

func (m *CheckUserIsCanTitledResponse) GetToastMsg() string {
	if m != nil {
		return m.ToastMsg
	}
	return ""
}

// 房间成员冠名周榜信息
type PgcGloryTitleAnchorRank struct {
	Rank                 uint32             `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"`
	AnchorInfo           *app.UserProfile   `protobuf:"bytes,2,opt,name=anchor_info,json=anchorInfo,proto3" json:"anchor_info,omitempty"`
	TitleUserNum         uint32             `protobuf:"varint,3,opt,name=title_user_num,json=titleUserNum,proto3" json:"title_user_num,omitempty"`
	LoveVal              int64              `protobuf:"varint,4,opt,name=love_val,json=loveVal,proto3" json:"love_val,omitempty"`
	TopUserList          []*app.UserProfile `protobuf:"bytes,5,rep,name=top_user_list,json=topUserList,proto3" json:"top_user_list,omitempty"`
	IsTitledMy           bool               `protobuf:"varint,6,opt,name=is_titled_my,json=isTitledMy,proto3" json:"is_titled_my,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PgcGloryTitleAnchorRank) Reset()         { *m = PgcGloryTitleAnchorRank{} }
func (m *PgcGloryTitleAnchorRank) String() string { return proto.CompactTextString(m) }
func (*PgcGloryTitleAnchorRank) ProtoMessage()    {}
func (*PgcGloryTitleAnchorRank) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{24}
}
func (m *PgcGloryTitleAnchorRank) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcGloryTitleAnchorRank.Unmarshal(m, b)
}
func (m *PgcGloryTitleAnchorRank) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcGloryTitleAnchorRank.Marshal(b, m, deterministic)
}
func (dst *PgcGloryTitleAnchorRank) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcGloryTitleAnchorRank.Merge(dst, src)
}
func (m *PgcGloryTitleAnchorRank) XXX_Size() int {
	return xxx_messageInfo_PgcGloryTitleAnchorRank.Size(m)
}
func (m *PgcGloryTitleAnchorRank) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcGloryTitleAnchorRank.DiscardUnknown(m)
}

var xxx_messageInfo_PgcGloryTitleAnchorRank proto.InternalMessageInfo

func (m *PgcGloryTitleAnchorRank) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *PgcGloryTitleAnchorRank) GetAnchorInfo() *app.UserProfile {
	if m != nil {
		return m.AnchorInfo
	}
	return nil
}

func (m *PgcGloryTitleAnchorRank) GetTitleUserNum() uint32 {
	if m != nil {
		return m.TitleUserNum
	}
	return 0
}

func (m *PgcGloryTitleAnchorRank) GetLoveVal() int64 {
	if m != nil {
		return m.LoveVal
	}
	return 0
}

func (m *PgcGloryTitleAnchorRank) GetTopUserList() []*app.UserProfile {
	if m != nil {
		return m.TopUserList
	}
	return nil
}

func (m *PgcGloryTitleAnchorRank) GetIsTitledMy() bool {
	if m != nil {
		return m.IsTitledMy
	}
	return false
}

// 麦上可冠名主播信息
type PgcGloryTitleMicAnchor struct {
	MicId                uint32           `protobuf:"varint,1,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	AnchorInfo           *app.UserProfile `protobuf:"bytes,2,opt,name=anchor_info,json=anchorInfo,proto3" json:"anchor_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PgcGloryTitleMicAnchor) Reset()         { *m = PgcGloryTitleMicAnchor{} }
func (m *PgcGloryTitleMicAnchor) String() string { return proto.CompactTextString(m) }
func (*PgcGloryTitleMicAnchor) ProtoMessage()    {}
func (*PgcGloryTitleMicAnchor) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{25}
}
func (m *PgcGloryTitleMicAnchor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcGloryTitleMicAnchor.Unmarshal(m, b)
}
func (m *PgcGloryTitleMicAnchor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcGloryTitleMicAnchor.Marshal(b, m, deterministic)
}
func (dst *PgcGloryTitleMicAnchor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcGloryTitleMicAnchor.Merge(dst, src)
}
func (m *PgcGloryTitleMicAnchor) XXX_Size() int {
	return xxx_messageInfo_PgcGloryTitleMicAnchor.Size(m)
}
func (m *PgcGloryTitleMicAnchor) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcGloryTitleMicAnchor.DiscardUnknown(m)
}

var xxx_messageInfo_PgcGloryTitleMicAnchor proto.InternalMessageInfo

func (m *PgcGloryTitleMicAnchor) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *PgcGloryTitleMicAnchor) GetAnchorInfo() *app.UserProfile {
	if m != nil {
		return m.AnchorInfo
	}
	return nil
}

// 获取房间主播冠名周榜信息
type GetPgcGloryTitleAnchorRankRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	LastRank             uint32       `protobuf:"varint,3,opt,name=last_rank,json=lastRank,proto3" json:"last_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPgcGloryTitleAnchorRankRequest) Reset()         { *m = GetPgcGloryTitleAnchorRankRequest{} }
func (m *GetPgcGloryTitleAnchorRankRequest) String() string { return proto.CompactTextString(m) }
func (*GetPgcGloryTitleAnchorRankRequest) ProtoMessage()    {}
func (*GetPgcGloryTitleAnchorRankRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{26}
}
func (m *GetPgcGloryTitleAnchorRankRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcGloryTitleAnchorRankRequest.Unmarshal(m, b)
}
func (m *GetPgcGloryTitleAnchorRankRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcGloryTitleAnchorRankRequest.Marshal(b, m, deterministic)
}
func (dst *GetPgcGloryTitleAnchorRankRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcGloryTitleAnchorRankRequest.Merge(dst, src)
}
func (m *GetPgcGloryTitleAnchorRankRequest) XXX_Size() int {
	return xxx_messageInfo_GetPgcGloryTitleAnchorRankRequest.Size(m)
}
func (m *GetPgcGloryTitleAnchorRankRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcGloryTitleAnchorRankRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcGloryTitleAnchorRankRequest proto.InternalMessageInfo

func (m *GetPgcGloryTitleAnchorRankRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPgcGloryTitleAnchorRankRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetPgcGloryTitleAnchorRankRequest) GetLastRank() uint32 {
	if m != nil {
		return m.LastRank
	}
	return 0
}

type GetPgcGloryTitleAnchorRankResponse struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RankList             []*PgcGloryTitleAnchorRank `protobuf:"bytes,2,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`
	LastTopList          []*app.UserProfile         `protobuf:"bytes,3,rep,name=last_top_list,json=lastTopList,proto3" json:"last_top_list,omitempty"`
	MyRank               *PgcGloryTitleAnchorRank   `protobuf:"bytes,4,opt,name=my_rank,json=myRank,proto3" json:"my_rank,omitempty"`
	MyDiffVal            int64                      `protobuf:"varint,5,opt,name=my_diff_val,json=myDiffVal,proto3" json:"my_diff_val,omitempty"`
	EndTs                int64                      `protobuf:"varint,6,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	IsAllEnd             bool                       `protobuf:"varint,7,opt,name=is_all_end,json=isAllEnd,proto3" json:"is_all_end,omitempty"`
	MicAnchorList        []*PgcGloryTitleMicAnchor  `protobuf:"bytes,8,rep,name=mic_anchor_list,json=micAnchorList,proto3" json:"mic_anchor_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetPgcGloryTitleAnchorRankResponse) Reset()         { *m = GetPgcGloryTitleAnchorRankResponse{} }
func (m *GetPgcGloryTitleAnchorRankResponse) String() string { return proto.CompactTextString(m) }
func (*GetPgcGloryTitleAnchorRankResponse) ProtoMessage()    {}
func (*GetPgcGloryTitleAnchorRankResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{27}
}
func (m *GetPgcGloryTitleAnchorRankResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcGloryTitleAnchorRankResponse.Unmarshal(m, b)
}
func (m *GetPgcGloryTitleAnchorRankResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcGloryTitleAnchorRankResponse.Marshal(b, m, deterministic)
}
func (dst *GetPgcGloryTitleAnchorRankResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcGloryTitleAnchorRankResponse.Merge(dst, src)
}
func (m *GetPgcGloryTitleAnchorRankResponse) XXX_Size() int {
	return xxx_messageInfo_GetPgcGloryTitleAnchorRankResponse.Size(m)
}
func (m *GetPgcGloryTitleAnchorRankResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcGloryTitleAnchorRankResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcGloryTitleAnchorRankResponse proto.InternalMessageInfo

func (m *GetPgcGloryTitleAnchorRankResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPgcGloryTitleAnchorRankResponse) GetRankList() []*PgcGloryTitleAnchorRank {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetPgcGloryTitleAnchorRankResponse) GetLastTopList() []*app.UserProfile {
	if m != nil {
		return m.LastTopList
	}
	return nil
}

func (m *GetPgcGloryTitleAnchorRankResponse) GetMyRank() *PgcGloryTitleAnchorRank {
	if m != nil {
		return m.MyRank
	}
	return nil
}

func (m *GetPgcGloryTitleAnchorRankResponse) GetMyDiffVal() int64 {
	if m != nil {
		return m.MyDiffVal
	}
	return 0
}

func (m *GetPgcGloryTitleAnchorRankResponse) GetEndTs() int64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *GetPgcGloryTitleAnchorRankResponse) GetIsAllEnd() bool {
	if m != nil {
		return m.IsAllEnd
	}
	return false
}

func (m *GetPgcGloryTitleAnchorRankResponse) GetMicAnchorList() []*PgcGloryTitleMicAnchor {
	if m != nil {
		return m.MicAnchorList
	}
	return nil
}

type PgcGloryTitleInvestorRank struct {
	Rank                 uint32           `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"`
	InvestorInfo         *app.UserProfile `protobuf:"bytes,2,opt,name=investor_info,json=investorInfo,proto3" json:"investor_info,omitempty"`
	LoveVal              int64            `protobuf:"varint,3,opt,name=love_val,json=loveVal,proto3" json:"love_val,omitempty"`
	ExpireTs             int64            `protobuf:"varint,4,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	TitleType            uint32           `protobuf:"varint,5,opt,name=title_type,json=titleType,proto3" json:"title_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PgcGloryTitleInvestorRank) Reset()         { *m = PgcGloryTitleInvestorRank{} }
func (m *PgcGloryTitleInvestorRank) String() string { return proto.CompactTextString(m) }
func (*PgcGloryTitleInvestorRank) ProtoMessage()    {}
func (*PgcGloryTitleInvestorRank) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{28}
}
func (m *PgcGloryTitleInvestorRank) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcGloryTitleInvestorRank.Unmarshal(m, b)
}
func (m *PgcGloryTitleInvestorRank) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcGloryTitleInvestorRank.Marshal(b, m, deterministic)
}
func (dst *PgcGloryTitleInvestorRank) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcGloryTitleInvestorRank.Merge(dst, src)
}
func (m *PgcGloryTitleInvestorRank) XXX_Size() int {
	return xxx_messageInfo_PgcGloryTitleInvestorRank.Size(m)
}
func (m *PgcGloryTitleInvestorRank) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcGloryTitleInvestorRank.DiscardUnknown(m)
}

var xxx_messageInfo_PgcGloryTitleInvestorRank proto.InternalMessageInfo

func (m *PgcGloryTitleInvestorRank) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *PgcGloryTitleInvestorRank) GetInvestorInfo() *app.UserProfile {
	if m != nil {
		return m.InvestorInfo
	}
	return nil
}

func (m *PgcGloryTitleInvestorRank) GetLoveVal() int64 {
	if m != nil {
		return m.LoveVal
	}
	return 0
}

func (m *PgcGloryTitleInvestorRank) GetExpireTs() int64 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

func (m *PgcGloryTitleInvestorRank) GetTitleType() uint32 {
	if m != nil {
		return m.TitleType
	}
	return 0
}

// 获取房间冠名-金主榜月榜信息
type GetPgcGloryTitleInvestorRankRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cid                  uint32       `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	TargetUid            uint32       `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	LastRank             uint32       `protobuf:"varint,4,opt,name=last_rank,json=lastRank,proto3" json:"last_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPgcGloryTitleInvestorRankRequest) Reset()         { *m = GetPgcGloryTitleInvestorRankRequest{} }
func (m *GetPgcGloryTitleInvestorRankRequest) String() string { return proto.CompactTextString(m) }
func (*GetPgcGloryTitleInvestorRankRequest) ProtoMessage()    {}
func (*GetPgcGloryTitleInvestorRankRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{29}
}
func (m *GetPgcGloryTitleInvestorRankRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcGloryTitleInvestorRankRequest.Unmarshal(m, b)
}
func (m *GetPgcGloryTitleInvestorRankRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcGloryTitleInvestorRankRequest.Marshal(b, m, deterministic)
}
func (dst *GetPgcGloryTitleInvestorRankRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcGloryTitleInvestorRankRequest.Merge(dst, src)
}
func (m *GetPgcGloryTitleInvestorRankRequest) XXX_Size() int {
	return xxx_messageInfo_GetPgcGloryTitleInvestorRankRequest.Size(m)
}
func (m *GetPgcGloryTitleInvestorRankRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcGloryTitleInvestorRankRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcGloryTitleInvestorRankRequest proto.InternalMessageInfo

func (m *GetPgcGloryTitleInvestorRankRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPgcGloryTitleInvestorRankRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetPgcGloryTitleInvestorRankRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetPgcGloryTitleInvestorRankRequest) GetLastRank() uint32 {
	if m != nil {
		return m.LastRank
	}
	return 0
}

type GetPgcGloryTitleInvestorRankResponse struct {
	BaseResp             *app.BaseResp                `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RankList             []*PgcGloryTitleInvestorRank `protobuf:"bytes,2,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`
	LastTopList          []*app.UserProfile           `protobuf:"bytes,3,rep,name=last_top_list,json=lastTopList,proto3" json:"last_top_list,omitempty"`
	TotalNum             uint32                       `protobuf:"varint,4,opt,name=total_num,json=totalNum,proto3" json:"total_num,omitempty"`
	EndTs                int64                        `protobuf:"varint,5,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	MyRank               *PgcGloryTitleInvestorRank   `protobuf:"bytes,6,opt,name=my_rank,json=myRank,proto3" json:"my_rank,omitempty"`
	MyDiffVal            int64                        `protobuf:"varint,7,opt,name=my_diff_val,json=myDiffVal,proto3" json:"my_diff_val,omitempty"`
	IsAllEnd             bool                         `protobuf:"varint,8,opt,name=is_all_end,json=isAllEnd,proto3" json:"is_all_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetPgcGloryTitleInvestorRankResponse) Reset()         { *m = GetPgcGloryTitleInvestorRankResponse{} }
func (m *GetPgcGloryTitleInvestorRankResponse) String() string { return proto.CompactTextString(m) }
func (*GetPgcGloryTitleInvestorRankResponse) ProtoMessage()    {}
func (*GetPgcGloryTitleInvestorRankResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4, []int{30}
}
func (m *GetPgcGloryTitleInvestorRankResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcGloryTitleInvestorRankResponse.Unmarshal(m, b)
}
func (m *GetPgcGloryTitleInvestorRankResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcGloryTitleInvestorRankResponse.Marshal(b, m, deterministic)
}
func (dst *GetPgcGloryTitleInvestorRankResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcGloryTitleInvestorRankResponse.Merge(dst, src)
}
func (m *GetPgcGloryTitleInvestorRankResponse) XXX_Size() int {
	return xxx_messageInfo_GetPgcGloryTitleInvestorRankResponse.Size(m)
}
func (m *GetPgcGloryTitleInvestorRankResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcGloryTitleInvestorRankResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcGloryTitleInvestorRankResponse proto.InternalMessageInfo

func (m *GetPgcGloryTitleInvestorRankResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPgcGloryTitleInvestorRankResponse) GetRankList() []*PgcGloryTitleInvestorRank {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetPgcGloryTitleInvestorRankResponse) GetLastTopList() []*app.UserProfile {
	if m != nil {
		return m.LastTopList
	}
	return nil
}

func (m *GetPgcGloryTitleInvestorRankResponse) GetTotalNum() uint32 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

func (m *GetPgcGloryTitleInvestorRankResponse) GetEndTs() int64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *GetPgcGloryTitleInvestorRankResponse) GetMyRank() *PgcGloryTitleInvestorRank {
	if m != nil {
		return m.MyRank
	}
	return nil
}

func (m *GetPgcGloryTitleInvestorRankResponse) GetMyDiffVal() int64 {
	if m != nil {
		return m.MyDiffVal
	}
	return 0
}

func (m *GetPgcGloryTitleInvestorRankResponse) GetIsAllEnd() bool {
	if m != nil {
		return m.IsAllEnd
	}
	return false
}

func init() {
	proto.RegisterType((*TicketInfo)(nil), "ga.pgc_channel_logic.TicketInfo")
	proto.RegisterType((*GetUserTicketListRequest)(nil), "ga.pgc_channel_logic.GetUserTicketListRequest")
	proto.RegisterType((*GetUserTicketListResponse)(nil), "ga.pgc_channel_logic.GetUserTicketListResponse")
	proto.RegisterType((*CheckTicketUsePerRequest)(nil), "ga.pgc_channel_logic.CheckTicketUsePerRequest")
	proto.RegisterType((*CheckTicketUsePerResponse)(nil), "ga.pgc_channel_logic.CheckTicketUsePerResponse")
	proto.RegisterType((*GetUserTicketRemindRequest)(nil), "ga.pgc_channel_logic.GetUserTicketRemindRequest")
	proto.RegisterType((*GetUserTicketRemindResponse)(nil), "ga.pgc_channel_logic.GetUserTicketRemindResponse")
	proto.RegisterType((*UserTicketListPushMsg)(nil), "ga.pgc_channel_logic.UserTicketListPushMsg")
	proto.RegisterType((*GetUserTitledInfoRequest)(nil), "ga.pgc_channel_logic.GetUserTitledInfoRequest")
	proto.RegisterType((*GetUserTitledInfoResponse)(nil), "ga.pgc_channel_logic.GetUserTitledInfoResponse")
	proto.RegisterType((*UserTitleImRemindInfo)(nil), "ga.pgc_channel_logic.UserTitleImRemindInfo")
	proto.RegisterType((*UserTitlePopRemindInfo)(nil), "ga.pgc_channel_logic.UserTitlePopRemindInfo")
	proto.RegisterType((*TitleUserEnterChannelMsg)(nil), "ga.pgc_channel_logic.TitleUserEnterChannelMsg")
	proto.RegisterType((*TitlePrivilege)(nil), "ga.pgc_channel_logic.TitlePrivilege")
	proto.RegisterType((*TitlePrivilegeList)(nil), "ga.pgc_channel_logic.TitlePrivilegeList")
	proto.RegisterType((*GetUserTitleInfoRequest)(nil), "ga.pgc_channel_logic.GetUserTitleInfoRequest")
	proto.RegisterType((*GetUserTitleInfoResponse)(nil), "ga.pgc_channel_logic.GetUserTitleInfoResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.pgc_channel_logic.GetUserTitleInfoResponse.MapTypeExpireTsEntry")
	proto.RegisterMapType((map[uint32]*TitlePrivilegeList)(nil), "ga.pgc_channel_logic.GetUserTitleInfoResponse.MapTypePrivilegeEntry")
	proto.RegisterMapType((map[uint32]bool)(nil), "ga.pgc_channel_logic.GetUserTitleInfoResponse.MapTypeSwitchEntry")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.pgc_channel_logic.GetUserTitleInfoResponse.MapTypeTotalEntry")
	proto.RegisterType((*TitleSuccessNotify)(nil), "ga.pgc_channel_logic.TitleSuccessNotify")
	proto.RegisterType((*UserTitlePrivilegeSwitchRequest)(nil), "ga.pgc_channel_logic.UserTitlePrivilegeSwitchRequest")
	proto.RegisterType((*UserTitlePrivilegeSwitchResponse)(nil), "ga.pgc_channel_logic.UserTitlePrivilegeSwitchResponse")
	proto.RegisterType((*GetChannelTitleInfoRequest)(nil), "ga.pgc_channel_logic.GetChannelTitleInfoRequest")
	proto.RegisterType((*GetChannelTitleInfoResponse)(nil), "ga.pgc_channel_logic.GetChannelTitleInfoResponse")
	proto.RegisterType((*CheckUserIsCanTitledRequest)(nil), "ga.pgc_channel_logic.CheckUserIsCanTitledRequest")
	proto.RegisterType((*CheckUserIsCanTitledResponse)(nil), "ga.pgc_channel_logic.CheckUserIsCanTitledResponse")
	proto.RegisterType((*PgcGloryTitleAnchorRank)(nil), "ga.pgc_channel_logic.PgcGloryTitleAnchorRank")
	proto.RegisterType((*PgcGloryTitleMicAnchor)(nil), "ga.pgc_channel_logic.PgcGloryTitleMicAnchor")
	proto.RegisterType((*GetPgcGloryTitleAnchorRankRequest)(nil), "ga.pgc_channel_logic.GetPgcGloryTitleAnchorRankRequest")
	proto.RegisterType((*GetPgcGloryTitleAnchorRankResponse)(nil), "ga.pgc_channel_logic.GetPgcGloryTitleAnchorRankResponse")
	proto.RegisterType((*PgcGloryTitleInvestorRank)(nil), "ga.pgc_channel_logic.PgcGloryTitleInvestorRank")
	proto.RegisterType((*GetPgcGloryTitleInvestorRankRequest)(nil), "ga.pgc_channel_logic.GetPgcGloryTitleInvestorRankRequest")
	proto.RegisterType((*GetPgcGloryTitleInvestorRankResponse)(nil), "ga.pgc_channel_logic.GetPgcGloryTitleInvestorRankResponse")
	proto.RegisterEnum("ga.pgc_channel_logic.ETitleType", ETitleType_name, ETitleType_value)
	proto.RegisterEnum("ga.pgc_channel_logic.PrivilegeType", PrivilegeType_name, PrivilegeType_value)
	proto.RegisterEnum("ga.pgc_channel_logic.TitlePrivilege_ImgStyle", TitlePrivilege_ImgStyle_name, TitlePrivilege_ImgStyle_value)
	proto.RegisterEnum("ga.pgc_channel_logic.TitlePrivilege_ImgType", TitlePrivilege_ImgType_name, TitlePrivilege_ImgType_value)
}

func init() {
	proto.RegisterFile("pgc_channel_logic/pgc_channel_logic.proto", fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4)
}

var fileDescriptor_pgc_channel_logic_1f7ba568c516b6f4 = []byte{
	// 2117 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x59, 0x4f, 0x73, 0x1b, 0x49,
	0x15, 0x67, 0x24, 0xdb, 0x92, 0x9e, 0x2c, 0x5b, 0xe9, 0xb5, 0x37, 0x72, 0x1c, 0x12, 0x33, 0x1b,
	0xb6, 0x92, 0xad, 0x8d, 0x42, 0x25, 0xa4, 0xd8, 0xa2, 0x0a, 0x58, 0xc7, 0x9e, 0x38, 0x0a, 0xb6,
	0xa2, 0x1a, 0x8f, 0xb3, 0x15, 0x2e, 0x53, 0xe3, 0x99, 0xd6, 0xa8, 0xd7, 0xf3, 0x2f, 0xd3, 0xad,
	0x10, 0x2d, 0x45, 0x01, 0xc5, 0x89, 0x1b, 0x77, 0xae, 0xf0, 0x2d, 0x38, 0x40, 0xf1, 0x41, 0xf8,
	0x00, 0x5c, 0xa9, 0xe2, 0xc0, 0x85, 0xea, 0xd7, 0x33, 0xd2, 0x48, 0x96, 0x9d, 0xc8, 0xd9, 0xe2,
	0x36, 0xf3, 0x5e, 0xcf, 0xfb, 0xfb, 0x7b, 0xef, 0xf5, 0x93, 0xe0, 0x5e, 0xe2, 0xbb, 0xb6, 0x3b,
	0x70, 0xa2, 0x88, 0x06, 0x76, 0x10, 0xfb, 0xcc, 0x7d, 0x70, 0x8e, 0xd2, 0x4e, 0xd2, 0x58, 0xc4,
	0x64, 0xc3, 0x77, 0xda, 0xe7, 0x78, 0x37, 0x1a, 0xbe, 0x63, 0x9f, 0x3a, 0x9c, 0xaa, 0x43, 0xfa,
	0x5f, 0x34, 0x00, 0x8b, 0xb9, 0x67, 0x54, 0x74, 0xa2, 0x7e, 0x4c, 0xd6, 0xa0, 0xc4, 0xbc, 0x96,
	0xb6, 0xa3, 0xdd, 0x6d, 0x98, 0x25, 0xe6, 0x11, 0x02, 0x4b, 0x91, 0x13, 0xd2, 0x56, 0x69, 0x47,
	0xbb, 0x5b, 0x33, 0xf1, 0x99, 0x6c, 0x43, 0x2d, 0x89, 0x93, 0x61, 0x62, 0x87, 0xdc, 0x6f, 0x95,
	0x91, 0x51, 0x45, 0xc2, 0x11, 0xf7, 0x25, 0x93, 0xbe, 0x4d, 0x58, 0x4a, 0x6d, 0xc1, 0x5b, 0x4b,
	0x28, 0xa7, 0xaa, 0x08, 0x16, 0x27, 0x5b, 0x50, 0x65, 0x6e, 0x1c, 0xd9, 0xc3, 0x34, 0x68, 0x2d,
	0xe3, 0x87, 0x15, 0xf9, 0x7e, 0x92, 0x06, 0xe4, 0x16, 0xd4, 0xdd, 0x81, 0xfd, 0xf5, 0x30, 0x4c,
	0xec, 0x74, 0x18, 0xb4, 0x56, 0x90, 0x5b, 0x73, 0x07, 0xcf, 0x87, 0x61, 0x62, 0x0e, 0x03, 0xfd,
	0x09, 0xb4, 0x0e, 0xa8, 0x38, 0xe1, 0x34, 0x55, 0xd6, 0x1e, 0x32, 0x2e, 0x4c, 0xfa, 0x7a, 0x48,
	0xb9, 0x20, 0x9f, 0x42, 0x55, 0x7a, 0x64, 0xa7, 0xf4, 0x35, 0x9a, 0x5e, 0x7f, 0x58, 0x6f, 0xfb,
	0x4e, 0xfb, 0x89, 0xc3, 0xa9, 0x49, 0x5f, 0x9b, 0x95, 0x53, 0xf5, 0xa0, 0xff, 0x41, 0x83, 0xad,
	0x39, 0x42, 0x78, 0x12, 0x47, 0x9c, 0x92, 0x7b, 0x50, 0xcb, 0xa4, 0xf0, 0x24, 0x13, 0xb3, 0x3a,
	0x11, 0xc3, 0x13, 0xb3, 0x7a, 0x9a, 0x3d, 0x91, 0x5d, 0xa8, 0x0b, 0x14, 0x60, 0x07, 0x8c, 0x8b,
	0x56, 0x69, 0xa7, 0x7c, 0xb7, 0xfe, 0x70, 0xa7, 0x3d, 0x2f, 0xde, 0xed, 0x49, 0x70, 0x4d, 0x10,
	0x63, 0xad, 0xfa, 0xef, 0x35, 0x68, 0xed, 0x0d, 0xa8, 0x7b, 0xa6, 0xf8, 0x27, 0x9c, 0xf6, 0x68,
	0xba, 0xa0, 0x43, 0xa4, 0x09, 0xe5, 0x21, 0xf3, 0x30, 0x39, 0x0d, 0x53, 0x3e, 0x4a, 0x8a, 0xcb,
	0x3c, 0xcc, 0x4a, 0xc3, 0x94, 0x8f, 0x32, 0x21, 0x99, 0xad, 0xcc, 0xcb, 0x13, 0xa2, 0x08, 0x1d,
	0x4f, 0xff, 0xaf, 0x06, 0x5b, 0x73, 0xac, 0x58, 0x3c, 0x22, 0x9f, 0xc2, 0xfa, 0xc0, 0xe1, 0x76,
	0x42, 0xd3, 0xdc, 0x7d, 0xb4, 0xaa, 0x6a, 0x36, 0x06, 0x0e, 0xef, 0xd1, 0x74, 0x4f, 0x11, 0xc9,
	0x0e, 0xac, 0xe6, 0xe7, 0x86, 0x9c, 0xa6, 0x68, 0x68, 0xd5, 0x04, 0x75, 0x48, 0x26, 0x66, 0x16,
	0x08, 0x4b, 0x33, 0x40, 0x90, 0xfc, 0xe4, 0xcc, 0xb7, 0x45, 0xac, 0xf0, 0xa7, 0x60, 0x54, 0x4b,
	0xce, 0x7c, 0x2b, 0x46, 0x00, 0xde, 0x83, 0x6b, 0x52, 0xb2, 0x1d, 0xc5, 0xa8, 0x45, 0xc4, 0x0e,
	0x17, 0x19, 0x9c, 0xd6, 0x24, 0xa3, 0x1b, 0xf7, 0x68, 0x6a, 0x49, 0xaa, 0xfe, 0x12, 0x6e, 0x4c,
	0xc1, 0xc1, 0xa4, 0x21, 0x8b, 0xbc, 0x2b, 0x24, 0xc1, 0x9d, 0x24, 0xc1, 0x65, 0x9e, 0xfe, 0x67,
	0x0d, 0xb6, 0xe7, 0x0a, 0x5e, 0x3c, 0xae, 0xdb, 0x50, 0x63, 0xdc, 0x4e, 0xf1, 0xfb, 0x2c, 0xa2,
	0x55, 0xc6, 0x95, 0x3c, 0xf2, 0x13, 0xa8, 0xb1, 0xa8, 0x1f, 0x2b, 0x10, 0x96, 0xdf, 0x13, 0x84,
	0x55, 0xf9, 0x09, 0x42, 0x70, 0x00, 0x9b, 0xd3, 0xa5, 0xd0, 0x1b, 0xf2, 0x81, 0x0c, 0x61, 0xe6,
	0x91, 0x36, 0x01, 0xd1, 0x94, 0xa6, 0xd2, 0xc2, 0x9a, 0x7e, 0xab, 0x15, 0xaa, 0x57, 0x04, 0xd4,
	0x43, 0xfe, 0x82, 0x71, 0xfe, 0x2e, 0x80, 0x70, 0x52, 0x9f, 0x0a, 0x7b, 0x82, 0xf9, 0x9a, 0xa2,
	0x9c, 0x30, 0x4f, 0xb2, 0x73, 0x4b, 0xc6, 0x05, 0x50, 0xcb, 0x28, 0x1d, 0x4f, 0xff, 0x57, 0xb1,
	0xf6, 0x27, 0x26, 0x5c, 0x29, 0x23, 0x12, 0xc1, 0x34, 0x12, 0xe9, 0x28, 0xcf, 0xc8, 0xc0, 0xe1,
	0x86, 0x7c, 0x27, 0x6d, 0xf8, 0x28, 0x37, 0x42, 0xa0, 0x16, 0x3b, 0x75, 0xa2, 0xb3, 0xac, 0x49,
	0x5e, 0xcb, 0x58, 0x4a, 0xbf, 0xe9, 0x44, 0x67, 0xe8, 0x93, 0x3a, 0xe7, 0x46, 0x22, 0xab, 0xce,
	0x9a, 0xa2, 0xec, 0x45, 0x82, 0xfc, 0x08, 0xd6, 0x25, 0xce, 0xb3, 0x23, 0x18, 0xfc, 0x65, 0x0c,
	0xfe, 0xba, 0x34, 0x4e, 0xfa, 0xd2, 0x4b, 0xe3, 0x3e, 0x0b, 0xa8, 0xd9, 0x10, 0x71, 0xa2, 0xe4,
	0x62, 0xc0, 0x7f, 0xa7, 0xe5, 0xb9, 0x15, 0x01, 0xed, 0x84, 0x0a, 0x2f, 0xd8, 0xe0, 0x9b, 0x50,
	0x96, 0x65, 0xa3, 0xa1, 0x45, 0xf2, 0x91, 0xdc, 0x81, 0xb5, 0x01, 0xf3, 0x07, 0x76, 0xc0, 0xfc,
	0x81, 0xb0, 0xc5, 0x5b, 0x91, 0x35, 0xfb, 0x55, 0x49, 0x3d, 0x94, 0x44, 0xeb, 0xad, 0x20, 0x3f,
	0x90, 0x2d, 0x0f, 0xcd, 0x18, 0xd7, 0xed, 0x1c, 0x33, 0x32, 0x6f, 0x24, 0x49, 0xff, 0x06, 0x3e,
	0x1e, 0x9b, 0xd0, 0x8b, 0x93, 0x82, 0x0d, 0x1b, 0xb0, 0x8c, 0xe7, 0x32, 0x2b, 0xd4, 0x0b, 0x69,
	0x41, 0xc5, 0x8d, 0x23, 0x41, 0xa3, 0xdc, 0x80, 0xfc, 0xf5, 0x0a, 0xba, 0x7f, 0x05, 0x2d, 0xd4,
	0x2b, 0x5f, 0x8c, 0x48, 0x8c, 0xfb, 0x4f, 0x86, 0xee, 0x99, 0x08, 0xe4, 0x59, 0xb0, 0xc5, 0x28,
	0xa1, 0x63, 0x64, 0x49, 0x8a, 0x35, 0x4a, 0x28, 0x69, 0xe7, 0xec, 0xcb, 0xb4, 0xab, 0xf3, 0xa8,
	0xfc, 0xdf, 0x25, 0x58, 0x53, 0x5e, 0xa7, 0xec, 0x0d, 0x0b, 0xa8, 0x8f, 0x23, 0x93, 0x87, 0x4e,
	0x10, 0xd8, 0x2c, 0xcc, 0x35, 0x57, 0x91, 0xd0, 0x09, 0x7d, 0x72, 0x1d, 0x2a, 0xa7, 0xcc, 0x47,
	0x96, 0x72, 0x7c, 0xe5, 0x94, 0xf9, 0x92, 0x91, 0x0f, 0xdf, 0x72, 0x61, 0xf8, 0x6e, 0x41, 0xd5,
	0xa3, 0xdc, 0xc5, 0xde, 0xa7, 0x7a, 0x63, 0x45, 0xbe, 0x67, 0xa3, 0x97, 0x85, 0xbe, 0xcd, 0xc5,
	0x28, 0xa0, 0xd8, 0x17, 0x1b, 0x66, 0x95, 0x85, 0xfe, 0xb1, 0x7c, 0xc7, 0xd1, 0x1b, 0xfa, 0xca,
	0xc3, 0x15, 0xe4, 0x55, 0x58, 0xe8, 0xa3, 0x7f, 0xdf, 0x87, 0xb5, 0x24, 0xb7, 0x54, 0x1d, 0xa8,
	0xe0, 0x81, 0xc6, 0x98, 0x2a, 0x8f, 0xe9, 0x5d, 0xa8, 0x76, 0x26, 0xd2, 0x36, 0x3b, 0x47, 0x07,
	0xf6, 0xb1, 0xf5, 0xea, 0xd0, 0xb0, 0x4f, 0xba, 0xc7, 0x3d, 0x63, 0xaf, 0xf3, 0xb4, 0x63, 0xec,
	0x37, 0xbf, 0x43, 0x3e, 0x82, 0xf5, 0x09, 0xeb, 0xf8, 0xd9, 0x0b, 0xd3, 0x6a, 0x6a, 0x84, 0xc0,
	0xda, 0x84, 0x78, 0xf8, 0xa2, 0x7b, 0xd0, 0x2c, 0xe9, 0x5d, 0xa8, 0x74, 0x32, 0x0b, 0x5a, 0xb0,
	0x21, 0xd9, 0xd6, 0xab, 0xde, 0x05, 0xd2, 0x90, 0x73, 0x6c, 0xed, 0x5a, 0x9d, 0xbd, 0xa6, 0x46,
	0x36, 0xa0, 0x39, 0x26, 0xee, 0xbf, 0xea, 0xee, 0x1e, 0x75, 0xf6, 0x50, 0x1e, 0x99, 0x8e, 0xba,
	0xac, 0x04, 0xf2, 0x05, 0x2c, 0x61, 0xdd, 0x68, 0x58, 0x37, 0x77, 0x2e, 0x6a, 0x5a, 0xc5, 0xef,
	0x4c, 0xfc, 0x42, 0xff, 0x0d, 0x5c, 0x2f, 0x36, 0x8c, 0xff, 0x7f, 0xcb, 0xfa, 0x67, 0x75, 0xba,
	0x6b, 0x5e, 0xb5, 0x63, 0x4d, 0xe3, 0xb7, 0xf4, 0x2e, 0xfc, 0x2e, 0x5e, 0x6e, 0xef, 0x6a, 0x63,
	0xb7, 0xa1, 0x9e, 0xa4, 0xb1, 0x4b, 0x39, 0x2f, 0x8c, 0x6c, 0xc8, 0x48, 0x12, 0xb9, 0x3a, 0x34,
	0x38, 0x8d, 0x3c, 0xdb, 0x67, 0x7d, 0x61, 0xbf, 0x71, 0x82, 0x0c, 0xa1, 0x75, 0x49, 0x3c, 0x60,
	0x7d, 0xf1, 0xd2, 0x09, 0x48, 0x1f, 0xd6, 0x42, 0x27, 0x41, 0x7c, 0xda, 0x22, 0x16, 0x4e, 0xd0,
	0xaa, 0x60, 0x4a, 0xbf, 0x9c, 0x9f, 0xd2, 0x8b, 0x02, 0xd7, 0x3e, 0x72, 0x12, 0x89, 0x39, 0x4b,
	0x8a, 0xc0, 0xa6, 0x6d, 0xae, 0x86, 0x05, 0x12, 0x49, 0x80, 0x8c, 0xf5, 0x4c, 0x6e, 0xb2, 0x55,
	0xd4, 0xb5, 0x77, 0x35, 0x5d, 0x46, 0x76, 0xff, 0x55, 0xea, 0xd6, 0xc3, 0x69, 0x2a, 0xb9, 0x07,
	0xcd, 0x49, 0xfd, 0xf1, 0x5f, 0x32, 0xe1, 0x0e, 0x5a, 0x35, 0x1c, 0x2c, 0xeb, 0x63, 0xfa, 0x31,
	0x92, 0x67, 0x3a, 0x15, 0xcc, 0x76, 0xaa, 0xb4, 0x60, 0xfb, 0xf8, 0xd3, 0x56, 0x1d, 0x6d, 0xdf,
	0xbf, 0x9a, 0xed, 0xe3, 0xaa, 0x50, 0xc6, 0x37, 0xc3, 0x19, 0xf2, 0x54, 0x43, 0x5a, 0x9d, 0x6e,
	0x48, 0x0c, 0xd6, 0xc7, 0xe6, 0x64, 0x7e, 0x35, 0xd0, 0x96, 0xdd, 0xab, 0xd9, 0xa2, 0x82, 0xa0,
	0x0c, 0x69, 0x84, 0x45, 0xda, 0x8d, 0x9f, 0xc1, 0xb5, 0x73, 0x89, 0x95, 0x9d, 0xfe, 0x8c, 0x8e,
	0xf2, 0x7b, 0xcc, 0x19, 0x1d, 0xc9, 0xc9, 0xf3, 0xc6, 0x09, 0x86, 0x79, 0x93, 0x57, 0x2f, 0x3f,
	0x2e, 0x7d, 0xa1, 0xdd, 0x78, 0x02, 0x1b, 0xf3, 0xb2, 0xb5, 0x90, 0x8c, 0x10, 0x36, 0xe7, 0x46,
	0x6d, 0x8e, 0x90, 0x9f, 0x16, 0x85, 0xd4, 0x1f, 0xde, 0x7d, 0x9f, 0xbe, 0x84, 0xdb, 0x4a, 0x41,
	0xdd, 0x97, 0x40, 0xce, 0x07, 0xe6, 0x5d, 0x06, 0x57, 0x0b, 0x12, 0xe4, 0xbd, 0x4c, 0xf5, 0xcc,
	0xe3, 0xa1, 0x2b, 0x6b, 0xb1, 0x1b, 0x0b, 0xd6, 0x1f, 0x49, 0x94, 0xd1, 0x7e, 0x9f, 0xba, 0x02,
	0x17, 0x35, 0x35, 0xae, 0x6a, 0x8a, 0x22, 0x57, 0xb5, 0x09, 0x3b, 0xf4, 0x1e, 0x67, 0x23, 0x2b,
	0x63, 0x1f, 0x79, 0x8f, 0xc9, 0xe7, 0x40, 0x32, 0x36, 0x7d, 0x2b, 0x64, 0x55, 0x7f, 0xcd, 0xe3,
	0x28, 0x9b, 0x61, 0x4d, 0xc5, 0x31, 0x90, 0xf1, 0x9c, 0xc7, 0x91, 0xfe, 0x77, 0x0d, 0x6e, 0x4f,
	0xae, 0x09, 0xd3, 0x70, 0x5f, 0xb4, 0xdd, 0x7e, 0x04, 0xcb, 0x8c, 0xdb, 0x71, 0x94, 0x39, 0xba,
	0xc4, 0xf8, 0x8b, 0x68, 0xa6, 0x07, 0x97, 0x2f, 0xef, 0xc1, 0x4b, 0x33, 0x3d, 0x98, 0x7c, 0x02,
	0xd9, 0x14, 0xcc, 0x47, 0xa3, 0x9a, 0xab, 0xab, 0x39, 0x11, 0x27, 0xe3, 0x11, 0xec, 0x5c, 0xec,
	0xc2, 0xc2, 0xfd, 0x5a, 0x77, 0x71, 0x2d, 0xd9, 0x2b, 0x5c, 0x16, 0xaf, 0x38, 0x7b, 0x0a, 0x8e,
	0x95, 0x66, 0x87, 0x0b, 0xc5, 0x15, 0xe5, 0xbc, 0x92, 0x6f, 0xf7, 0x42, 0xac, 0x7b, 0xb0, 0x8d,
	0xfb, 0xa5, 0x8c, 0x4f, 0x87, 0xef, 0x39, 0x51, 0x76, 0xf9, 0xfd, 0x56, 0x07, 0xa9, 0xfe, 0x6b,
	0xb8, 0x39, 0x5f, 0xcb, 0xe2, 0xde, 0x6c, 0xc2, 0x0a, 0xe3, 0xb6, 0xeb, 0xe4, 0x20, 0x5a, 0x66,
	0x52, 0x1e, 0x6e, 0xd1, 0x72, 0x67, 0x2c, 0xfe, 0xe6, 0x81, 0x84, 0x23, 0xee, 0xeb, 0xff, 0xd1,
	0xe0, 0x7a, 0xcf, 0x77, 0x0f, 0x82, 0x38, 0x1d, 0xa1, 0xe6, 0xdd, 0xc8, 0x1d, 0xc4, 0x29, 0xde,
	0xf0, 0x09, 0x2c, 0xe1, 0x0a, 0xa0, 0xea, 0x11, 0x9f, 0xe5, 0x80, 0x75, 0xf0, 0x84, 0x2d, 0x37,
	0xa4, 0x8b, 0x26, 0x32, 0xa8, 0x33, 0x78, 0x63, 0xbe, 0x03, 0x6b, 0x93, 0x11, 0x6e, 0x47, 0xc3,
	0x30, 0x03, 0xf2, 0xea, 0x78, 0x6a, 0x77, 0x87, 0xa1, 0x6c, 0xc5, 0x41, 0xfc, 0x86, 0xe2, 0x04,
	0x95, 0x48, 0x2e, 0x9b, 0x15, 0xf9, 0x2e, 0xa7, 0xe7, 0x23, 0x90, 0x1b, 0x82, 0xfa, 0xfc, 0xb2,
	0x3d, 0xa2, 0x2e, 0xe2, 0x44, 0xbe, 0xe3, 0xdd, 0x69, 0x07, 0x56, 0x19, 0xcf, 0xb7, 0x8f, 0x70,
	0x84, 0x53, 0xb9, 0x6a, 0x02, 0xe3, 0x2a, 0xbc, 0x47, 0x23, 0xdd, 0x81, 0x8f, 0xa7, 0x1c, 0x3f,
	0x62, 0xae, 0xf2, 0x5d, 0xc6, 0x31, 0x64, 0xae, 0x3d, 0x5e, 0x23, 0x97, 0x43, 0xe6, 0x76, 0xbc,
	0xc5, 0x5d, 0xd7, 0xbf, 0x81, 0xef, 0x1d, 0x50, 0x71, 0x41, 0x78, 0x3f, 0x78, 0x57, 0x97, 0x89,
	0x0d, 0x64, 0x5e, 0xc7, 0x7b, 0x5a, 0xc3, 0xac, 0x4a, 0x82, 0x94, 0xae, 0xff, 0xa3, 0x0c, 0xfa,
	0x65, 0xca, 0x17, 0x87, 0xd7, 0x73, 0xa8, 0x49, 0x4d, 0xc5, 0x45, 0xfa, 0xfe, 0xfc, 0xde, 0x7f,
	0x91, 0xd2, 0xaa, 0xfc, 0x1e, 0xd3, 0xf3, 0x08, 0x1a, 0x68, 0xba, 0x4c, 0x6c, 0xe1, 0x27, 0x80,
	0xf3, 0x39, 0x95, 0xa7, 0xac, 0x38, 0xc1, 0x8f, 0x9e, 0x42, 0x25, 0x1c, 0x29, 0x6f, 0x97, 0xd0,
	0xd2, 0x05, 0xd5, 0xaf, 0x84, 0x23, 0xc4, 0xf5, 0x2d, 0xa8, 0x87, 0x23, 0xdb, 0x63, 0xfd, 0x3e,
	0xc2, 0x6d, 0x19, 0xe1, 0x56, 0x0b, 0x47, 0xfb, 0xac, 0xdf, 0x97, 0x80, 0xdb, 0x84, 0x15, 0xd9,
	0xfb, 0x05, 0x47, 0xd4, 0x94, 0xcd, 0x65, 0x1a, 0x79, 0x16, 0x27, 0x37, 0x01, 0x18, 0xb7, 0xe5,
	0x26, 0x44, 0x23, 0x0f, 0xf7, 0x0c, 0xfc, 0x41, 0x63, 0x37, 0x08, 0x8c, 0xc8, 0x23, 0x16, 0xac,
	0x4b, 0xd0, 0x64, 0x08, 0x41, 0x9f, 0xd4, 0xc5, 0xeb, 0xf3, 0xf7, 0x30, 0x72, 0x8c, 0x3d, 0xb3,
	0x11, 0xe6, 0x8f, 0xb8, 0x0c, 0xff, 0x55, 0x83, 0xad, 0xa9, 0x93, 0x9d, 0xe8, 0x0d, 0xe5, 0xe2,
	0x92, 0x02, 0xfd, 0x21, 0x34, 0x58, 0x76, 0xe6, 0x52, 0x9c, 0xae, 0xe6, 0xa7, 0xb0, 0x48, 0x8b,
	0xe5, 0x57, 0x9e, 0x2e, 0xbf, 0x73, 0xbf, 0x8a, 0x96, 0x0b, 0xbf, 0x8a, 0x4e, 0x5f, 0xea, 0x96,
	0x67, 0x2e, 0x75, 0xfa, 0x9f, 0x34, 0xf8, 0x64, 0x16, 0x84, 0x45, 0x0f, 0x3e, 0xbc, 0x06, 0xde,
	0x31, 0x22, 0xa7, 0x4a, 0x64, 0x69, 0xa6, 0x44, 0xfe, 0x58, 0x86, 0x3b, 0x97, 0x5b, 0xb7, 0x78,
	0x91, 0x1c, 0x9e, 0x2f, 0x92, 0x07, 0xef, 0x01, 0x80, 0x29, 0xb5, 0x1f, 0x58, 0x26, 0xd8, 0xef,
	0x85, 0x13, 0x60, 0xaf, 0xcd, 0x7f, 0x35, 0x95, 0x04, 0xd9, 0x67, 0x27, 0xd8, 0x5e, 0x2e, 0x62,
	0xfb, 0xd9, 0xa4, 0xb4, 0x56, 0xd0, 0xbf, 0x85, 0x8d, 0xbe, 0xa0, 0xb8, 0x2a, 0xb3, 0xc5, 0x35,
	0x5d, 0x45, 0xd5, 0xe9, 0x2a, 0xfa, 0x2c, 0x06, 0x30, 0xac, 0xf1, 0x4e, 0x70, 0x13, 0x5a, 0x86,
	0x6d, 0x75, 0xac, 0x43, 0x63, 0xde, 0x7e, 0xbd, 0x09, 0xd7, 0xa6, 0xb8, 0x5f, 0x19, 0xc6, 0xcf,
	0x9b, 0x1a, 0xf9, 0x18, 0xc8, 0x14, 0xf9, 0xe8, 0x45, 0xd7, 0x7a, 0xd6, 0x2c, 0xc9, 0xcd, 0x7b,
	0x8a, 0xbe, 0xbf, 0xfb, 0xaa, 0x59, 0xfe, 0xec, 0x6f, 0x1a, 0x34, 0x7a, 0xc5, 0xdf, 0x0a, 0xc8,
	0x2d, 0xb8, 0xd1, 0x33, 0x3b, 0x2f, 0x3b, 0x87, 0xc6, 0xc1, 0x5c, 0xb5, 0xb7, 0x61, 0x7b, 0x86,
	0x6f, 0x74, 0x2d, 0xc3, 0xb4, 0x8d, 0xa7, 0x4f, 0x8d, 0x3d, 0xab, 0xa9, 0x91, 0x6d, 0xb8, 0x3e,
	0x73, 0xe0, 0x99, 0xb1, 0xbb, 0xff, 0x95, 0xb1, 0x6b, 0x36, 0x4b, 0xd2, 0xa5, 0x19, 0xe6, 0x41,
	0xe7, 0xa9, 0x65, 0x1f, 0x1b, 0xdd, 0xfd, 0xe6, 0x12, 0xd9, 0x82, 0xcd, 0x19, 0x6e, 0xef, 0x45,
	0xcf, 0x3e, 0xe9, 0x35, 0xab, 0xa4, 0x05, 0x1b, 0xb3, 0xac, 0xc3, 0x5d, 0xcb, 0x68, 0x36, 0x9f,
	0x3c, 0x87, 0x96, 0x1b, 0x87, 0xed, 0x11, 0x1b, 0xc5, 0x43, 0x99, 0xb5, 0x30, 0xf6, 0x68, 0xa0,
	0xfe, 0x22, 0xf9, 0x45, 0xdb, 0x8f, 0x03, 0x27, 0xf2, 0xdb, 0x8f, 0x1f, 0x0a, 0xd1, 0x76, 0xe3,
	0xf0, 0x01, 0x92, 0xdd, 0x38, 0x78, 0xe0, 0x24, 0xc9, 0x83, 0xc4, 0x77, 0xef, 0x67, 0xf9, 0xbd,
	0x8f, 0xf9, 0x3d, 0x5d, 0x41, 0xfe, 0xa3, 0xff, 0x05, 0x00, 0x00, 0xff, 0xff, 0x5e, 0xa9, 0x0c,
	0x6e, 0xab, 0x19, 0x00, 0x00,
}
