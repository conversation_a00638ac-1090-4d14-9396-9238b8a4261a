// Code generated by protoc-gen-go. DO NOT EDIT.
// source: im-activity-center-logic_.proto

package im_activity_center_logic // import "golang.52tt.com/protocol/app/im_activity_center_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ImActivityCenterEntranceReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Channel              string       `protobuf:"bytes,2,opt,name=channel,proto3" json:"channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ImActivityCenterEntranceReq) Reset()         { *m = ImActivityCenterEntranceReq{} }
func (m *ImActivityCenterEntranceReq) String() string { return proto.CompactTextString(m) }
func (*ImActivityCenterEntranceReq) ProtoMessage()    {}
func (*ImActivityCenterEntranceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_activity_center_logic__e67a6dce0ea1b2ef, []int{0}
}
func (m *ImActivityCenterEntranceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImActivityCenterEntranceReq.Unmarshal(m, b)
}
func (m *ImActivityCenterEntranceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImActivityCenterEntranceReq.Marshal(b, m, deterministic)
}
func (dst *ImActivityCenterEntranceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImActivityCenterEntranceReq.Merge(dst, src)
}
func (m *ImActivityCenterEntranceReq) XXX_Size() int {
	return xxx_messageInfo_ImActivityCenterEntranceReq.Size(m)
}
func (m *ImActivityCenterEntranceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ImActivityCenterEntranceReq.DiscardUnknown(m)
}

var xxx_messageInfo_ImActivityCenterEntranceReq proto.InternalMessageInfo

func (m *ImActivityCenterEntranceReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ImActivityCenterEntranceReq) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

type ImActivityCenterEntranceResp struct {
	BaseResp             *app.BaseResp         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TopActivity          *ImActivity           `protobuf:"bytes,2,opt,name=top_activity,json=topActivity,proto3" json:"top_activity,omitempty"`
	Categories           []*ImActivityCategory `protobuf:"bytes,3,rep,name=categories,proto3" json:"categories,omitempty"`
	Icon                 string                `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	Texts                []string              `protobuf:"bytes,5,rep,name=texts,proto3" json:"texts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ImActivityCenterEntranceResp) Reset()         { *m = ImActivityCenterEntranceResp{} }
func (m *ImActivityCenterEntranceResp) String() string { return proto.CompactTextString(m) }
func (*ImActivityCenterEntranceResp) ProtoMessage()    {}
func (*ImActivityCenterEntranceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_activity_center_logic__e67a6dce0ea1b2ef, []int{1}
}
func (m *ImActivityCenterEntranceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImActivityCenterEntranceResp.Unmarshal(m, b)
}
func (m *ImActivityCenterEntranceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImActivityCenterEntranceResp.Marshal(b, m, deterministic)
}
func (dst *ImActivityCenterEntranceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImActivityCenterEntranceResp.Merge(dst, src)
}
func (m *ImActivityCenterEntranceResp) XXX_Size() int {
	return xxx_messageInfo_ImActivityCenterEntranceResp.Size(m)
}
func (m *ImActivityCenterEntranceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ImActivityCenterEntranceResp.DiscardUnknown(m)
}

var xxx_messageInfo_ImActivityCenterEntranceResp proto.InternalMessageInfo

func (m *ImActivityCenterEntranceResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ImActivityCenterEntranceResp) GetTopActivity() *ImActivity {
	if m != nil {
		return m.TopActivity
	}
	return nil
}

func (m *ImActivityCenterEntranceResp) GetCategories() []*ImActivityCategory {
	if m != nil {
		return m.Categories
	}
	return nil
}

func (m *ImActivityCenterEntranceResp) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ImActivityCenterEntranceResp) GetTexts() []string {
	if m != nil {
		return m.Texts
	}
	return nil
}

type ImActivityCategory struct {
	Title                string        `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Activities           []*ImActivity `protobuf:"bytes,2,rep,name=activities,proto3" json:"activities,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ImActivityCategory) Reset()         { *m = ImActivityCategory{} }
func (m *ImActivityCategory) String() string { return proto.CompactTextString(m) }
func (*ImActivityCategory) ProtoMessage()    {}
func (*ImActivityCategory) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_activity_center_logic__e67a6dce0ea1b2ef, []int{2}
}
func (m *ImActivityCategory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImActivityCategory.Unmarshal(m, b)
}
func (m *ImActivityCategory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImActivityCategory.Marshal(b, m, deterministic)
}
func (dst *ImActivityCategory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImActivityCategory.Merge(dst, src)
}
func (m *ImActivityCategory) XXX_Size() int {
	return xxx_messageInfo_ImActivityCategory.Size(m)
}
func (m *ImActivityCategory) XXX_DiscardUnknown() {
	xxx_messageInfo_ImActivityCategory.DiscardUnknown(m)
}

var xxx_messageInfo_ImActivityCategory proto.InternalMessageInfo

func (m *ImActivityCategory) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ImActivityCategory) GetActivities() []*ImActivity {
	if m != nil {
		return m.Activities
	}
	return nil
}

type ImActivity struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	JumpUrl              string   `protobuf:"bytes,4,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	BeginAt              uint32   `protobuf:"varint,5,opt,name=begin_at,json=beginAt,proto3" json:"begin_at,omitempty"`
	EndAt                uint32   `protobuf:"varint,6,opt,name=end_at,json=endAt,proto3" json:"end_at,omitempty"`
	LastPushTime         uint32   `protobuf:"varint,7,opt,name=last_push_time,json=lastPushTime,proto3" json:"last_push_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImActivity) Reset()         { *m = ImActivity{} }
func (m *ImActivity) String() string { return proto.CompactTextString(m) }
func (*ImActivity) ProtoMessage()    {}
func (*ImActivity) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_activity_center_logic__e67a6dce0ea1b2ef, []int{3}
}
func (m *ImActivity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImActivity.Unmarshal(m, b)
}
func (m *ImActivity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImActivity.Marshal(b, m, deterministic)
}
func (dst *ImActivity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImActivity.Merge(dst, src)
}
func (m *ImActivity) XXX_Size() int {
	return xxx_messageInfo_ImActivity.Size(m)
}
func (m *ImActivity) XXX_DiscardUnknown() {
	xxx_messageInfo_ImActivity.DiscardUnknown(m)
}

var xxx_messageInfo_ImActivity proto.InternalMessageInfo

func (m *ImActivity) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ImActivity) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ImActivity) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ImActivity) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *ImActivity) GetBeginAt() uint32 {
	if m != nil {
		return m.BeginAt
	}
	return 0
}

func (m *ImActivity) GetEndAt() uint32 {
	if m != nil {
		return m.EndAt
	}
	return 0
}

func (m *ImActivity) GetLastPushTime() uint32 {
	if m != nil {
		return m.LastPushTime
	}
	return 0
}

func init() {
	proto.RegisterType((*ImActivityCenterEntranceReq)(nil), "ga.im_activity_center_logic.ImActivityCenterEntranceReq")
	proto.RegisterType((*ImActivityCenterEntranceResp)(nil), "ga.im_activity_center_logic.ImActivityCenterEntranceResp")
	proto.RegisterType((*ImActivityCategory)(nil), "ga.im_activity_center_logic.ImActivityCategory")
	proto.RegisterType((*ImActivity)(nil), "ga.im_activity_center_logic.ImActivity")
}

func init() {
	proto.RegisterFile("im-activity-center-logic_.proto", fileDescriptor_im_activity_center_logic__e67a6dce0ea1b2ef)
}

var fileDescriptor_im_activity_center_logic__e67a6dce0ea1b2ef = []byte{
	// 447 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x92, 0xd1, 0x8a, 0xd3, 0x40,
	0x14, 0x86, 0x49, 0xba, 0xdd, 0xb4, 0x27, 0xdd, 0xbd, 0x18, 0x14, 0xa2, 0x2b, 0x18, 0x8a, 0x68,
	0xbc, 0x68, 0x0a, 0x95, 0x7d, 0x80, 0xee, 0x22, 0xa2, 0x37, 0x6a, 0xd0, 0x1b, 0x6f, 0x86, 0xc9,
	0x64, 0x48, 0x47, 0x92, 0x99, 0xd9, 0xcc, 0x89, 0xd8, 0xb7, 0xf0, 0x75, 0x7c, 0x3b, 0x99, 0x49,
	0x6a, 0x0b, 0xb2, 0xb2, 0x77, 0xf3, 0xe7, 0xff, 0xe6, 0xfc, 0xe7, 0x1f, 0x02, 0xcf, 0x65, 0xbb,
	0x62, 0x1c, 0xe5, 0x0f, 0x89, 0xfb, 0x15, 0x17, 0x0a, 0x45, 0xb7, 0x6a, 0x74, 0x2d, 0x39, 0xcd,
	0x4d, 0xa7, 0x51, 0x93, 0xab, 0x9a, 0xe5, 0xb2, 0xa5, 0x07, 0x86, 0x0e, 0x0c, 0xf5, 0xcc, 0xd3,
	0x8b, 0x9a, 0xd1, 0x92, 0x59, 0x31, 0xb0, 0x4b, 0x0a, 0x57, 0xef, 0xdb, 0xed, 0x48, 0xde, 0x7a,
	0xf0, 0xad, 0xc2, 0x8e, 0x29, 0x2e, 0x0a, 0x71, 0x47, 0x5e, 0xc2, 0xcc, 0xc1, 0xb4, 0x13, 0x77,
	0x49, 0x90, 0x06, 0x59, 0xbc, 0x89, 0xf3, 0x9a, 0xe5, 0x37, 0xcc, 0x3a, 0xbb, 0x88, 0xca, 0xe1,
	0x40, 0x12, 0x88, 0xf8, 0x8e, 0x29, 0x25, 0x9a, 0x24, 0x4c, 0x83, 0x6c, 0x5e, 0x1c, 0xe4, 0xf2,
	0x57, 0x08, 0xcf, 0xee, 0x4f, 0xb0, 0x86, 0xbc, 0x86, 0xf9, 0x18, 0x61, 0xcd, 0x98, 0xb1, 0x38,
	0x66, 0x58, 0x53, 0xcc, 0xca, 0xf1, 0x44, 0x3e, 0xc0, 0x02, 0xb5, 0xf9, 0x5b, 0xcc, 0x47, 0xc5,
	0x9b, 0x57, 0xf9, 0x7f, 0xfa, 0xe6, 0xc7, 0xec, 0x22, 0x46, 0x6d, 0x0e, 0x82, 0x7c, 0x04, 0xe0,
	0x0c, 0x45, 0xad, 0x3b, 0x29, 0x6c, 0x32, 0x49, 0x27, 0x59, 0xbc, 0x59, 0x3f, 0x70, 0xd2, 0xed,
	0x70, 0x71, 0x5f, 0x9c, 0x8c, 0x20, 0x04, 0xce, 0x24, 0xd7, 0x2a, 0x39, 0xf3, 0xfd, 0xfd, 0x99,
	0x3c, 0x82, 0x29, 0x8a, 0x9f, 0x68, 0x93, 0x69, 0x3a, 0xc9, 0xe6, 0xc5, 0x20, 0x96, 0x16, 0xc8,
	0xbf, 0xb3, 0x3c, 0x2b, 0xb1, 0x11, 0xfe, 0x0d, 0x1c, 0xeb, 0x04, 0x79, 0x07, 0x30, 0x6e, 0xe3,
	0xd6, 0x0c, 0xfd, 0x9a, 0x0f, 0x2e, 0x7c, 0x72, 0x75, 0xf9, 0x3b, 0x00, 0x38, 0x5a, 0xe4, 0x12,
	0x42, 0x59, 0x8d, 0x51, 0xa1, 0xac, 0x8e, 0xe9, 0xe1, 0x69, 0xfa, 0xa1, 0xd3, 0xe4, 0xa4, 0xd3,
	0x13, 0x98, 0x7d, 0xef, 0x5b, 0x43, 0xfb, 0xae, 0x19, 0xbb, 0x46, 0x4e, 0x7f, 0xed, 0x1a, 0x67,
	0x95, 0xa2, 0x96, 0x8a, 0x32, 0x4c, 0xa6, 0x69, 0x90, 0x5d, 0x14, 0x91, 0xd7, 0x5b, 0x24, 0x8f,
	0xe1, 0x5c, 0xa8, 0xca, 0x19, 0xe7, 0xde, 0x98, 0x0a, 0x55, 0x6d, 0x91, 0xbc, 0x80, 0xcb, 0x86,
	0x59, 0xa4, 0xa6, 0xb7, 0x3b, 0x8a, 0xb2, 0x15, 0x49, 0xe4, 0xed, 0x85, 0xfb, 0xfa, 0xa9, 0xb7,
	0xbb, 0x2f, 0xb2, 0x15, 0x37, 0x9f, 0x21, 0xe1, 0xba, 0xcd, 0xf7, 0x72, 0xaf, 0x7b, 0xd7, 0xbd,
	0xd5, 0x95, 0x68, 0x86, 0x1f, 0xf8, 0xdb, 0x75, 0xad, 0x1b, 0xa6, 0xea, 0xfc, 0x7a, 0x83, 0x98,
	0x73, 0xdd, 0xae, 0xfd, 0x67, 0xae, 0x9b, 0x35, 0x33, 0x66, 0x7d, 0xdf, 0x2b, 0x95, 0xe7, 0x1e,
	0x7b, 0xf3, 0x27, 0x00, 0x00, 0xff, 0xff, 0xa6, 0x5e, 0x2d, 0x39, 0x4d, 0x03, 0x00, 0x00,
}
