// Code generated by protoc-gen-go. DO NOT EDIT.
// source: pgc-channel-game-logic_.proto

package pgc_channel_game_logic // import "golang.52tt.com/protocol/app/pgc_channel_game_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PgcChannelGameId int32

const (
	PgcChannelGameId_GAME_INVALID      PgcChannelGameId = 0
	PgcChannelGameId_GAME_THROW_BOMB   PgcChannelGameId = 1
	PgcChannelGameId_GAME_DIGITAL_BOMB PgcChannelGameId = 2
	PgcChannelGameId_GAME_ADVENTURE    PgcChannelGameId = 3
)

var PgcChannelGameId_name = map[int32]string{
	0: "GAME_INVALID",
	1: "GAME_THROW_BOMB",
	2: "GAME_DIGITAL_BOMB",
	3: "GAME_ADVENTURE",
}
var PgcChannelGameId_value = map[string]int32{
	"GAME_INVALID":      0,
	"GAME_THROW_BOMB":   1,
	"GAME_DIGITAL_BOMB": 2,
	"GAME_ADVENTURE":    3,
}

func (x PgcChannelGameId) String() string {
	return proto.EnumName(PgcChannelGameId_name, int32(x))
}
func (PgcChannelGameId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{0}
}

// 通用游戏用户群体类型
type GameUserType int32

const (
	GameUserType_GAME_USER_TYPE_UNKNOWN           GameUserType = 0
	GameUserType_GAME_USER_TYPE_HOST_MIC          GameUserType = 1
	GameUserType_GAME_USER_TYPE_MIC               GameUserType = 2
	GameUserType_GAME_USER_TYPE_HOST_CHANNEL_USER GameUserType = 3
	GameUserType_GAME_USER_TYPE_CHANNEL_USER      GameUserType = 4
)

var GameUserType_name = map[int32]string{
	0: "GAME_USER_TYPE_UNKNOWN",
	1: "GAME_USER_TYPE_HOST_MIC",
	2: "GAME_USER_TYPE_MIC",
	3: "GAME_USER_TYPE_HOST_CHANNEL_USER",
	4: "GAME_USER_TYPE_CHANNEL_USER",
}
var GameUserType_value = map[string]int32{
	"GAME_USER_TYPE_UNKNOWN":           0,
	"GAME_USER_TYPE_HOST_MIC":          1,
	"GAME_USER_TYPE_MIC":               2,
	"GAME_USER_TYPE_HOST_CHANNEL_USER": 3,
	"GAME_USER_TYPE_CHANNEL_USER":      4,
}

func (x GameUserType) String() string {
	return proto.EnumName(GameUserType_name, int32(x))
}
func (GameUserType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{1}
}

// ================= 数字炸弹 ================
type DigitalBombPhase int32

const (
	// 预留1-10作为通用phase, 见SetGamePhaseReq.GamePhaseType
	DigitalBombPhase_DIGITAL_BOMB_PHASE_UNKNOWN     DigitalBombPhase = 0
	DigitalBombPhase_DIGITAL_BOMB_PHASE_ENROLL      DigitalBombPhase = 1
	DigitalBombPhase_DIGITAL_BOMB_PHASE_DIRECT_END  DigitalBombPhase = 2
	DigitalBombPhase_DIGITAL_BOMB_PHASE_SELECTING   DigitalBombPhase = 11
	DigitalBombPhase_DIGITAL_BOMB_PHASE_SELECTED    DigitalBombPhase = 12
	DigitalBombPhase_DIGITAL_BOMB_PHASE_EXPLODE     DigitalBombPhase = 13
	DigitalBombPhase_DIGITAL_BOMB_PHASE_MANUAL_END  DigitalBombPhase = 14
	DigitalBombPhase_DIGITAL_BOMB_PHASE_SYS_END     DigitalBombPhase = 15
	DigitalBombPhase_DIGITAL_BOMB_PHASE_TIMEOUT_END DigitalBombPhase = 16
)

var DigitalBombPhase_name = map[int32]string{
	0:  "DIGITAL_BOMB_PHASE_UNKNOWN",
	1:  "DIGITAL_BOMB_PHASE_ENROLL",
	2:  "DIGITAL_BOMB_PHASE_DIRECT_END",
	11: "DIGITAL_BOMB_PHASE_SELECTING",
	12: "DIGITAL_BOMB_PHASE_SELECTED",
	13: "DIGITAL_BOMB_PHASE_EXPLODE",
	14: "DIGITAL_BOMB_PHASE_MANUAL_END",
	15: "DIGITAL_BOMB_PHASE_SYS_END",
	16: "DIGITAL_BOMB_PHASE_TIMEOUT_END",
}
var DigitalBombPhase_value = map[string]int32{
	"DIGITAL_BOMB_PHASE_UNKNOWN":     0,
	"DIGITAL_BOMB_PHASE_ENROLL":      1,
	"DIGITAL_BOMB_PHASE_DIRECT_END":  2,
	"DIGITAL_BOMB_PHASE_SELECTING":   11,
	"DIGITAL_BOMB_PHASE_SELECTED":    12,
	"DIGITAL_BOMB_PHASE_EXPLODE":     13,
	"DIGITAL_BOMB_PHASE_MANUAL_END":  14,
	"DIGITAL_BOMB_PHASE_SYS_END":     15,
	"DIGITAL_BOMB_PHASE_TIMEOUT_END": 16,
}

func (x DigitalBombPhase) String() string {
	return proto.EnumName(DigitalBombPhase_name, int32(x))
}
func (DigitalBombPhase) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{2}
}

// ================= 大冒险 ================
type AdventurePhase int32

const (
	// 预留1-10作为通用phase, 见SetGamePhaseReq.GamePhaseType
	AdventurePhase_ADVENTURE_PHASE_UNKNOWN      AdventurePhase = 0
	AdventurePhase_ADVENTURE_PHASE_ENROLL       AdventurePhase = 1
	AdventurePhase_ADVENTURE_PHASE_DIRECT_END   AdventurePhase = 2
	AdventurePhase_ADVENTURE_PHASE_RANDOM_STEPS AdventurePhase = 11
	AdventurePhase_ADVENTURE_PHASE_CONTROL_NEXT AdventurePhase = 12
)

var AdventurePhase_name = map[int32]string{
	0:  "ADVENTURE_PHASE_UNKNOWN",
	1:  "ADVENTURE_PHASE_ENROLL",
	2:  "ADVENTURE_PHASE_DIRECT_END",
	11: "ADVENTURE_PHASE_RANDOM_STEPS",
	12: "ADVENTURE_PHASE_CONTROL_NEXT",
}
var AdventurePhase_value = map[string]int32{
	"ADVENTURE_PHASE_UNKNOWN":      0,
	"ADVENTURE_PHASE_ENROLL":       1,
	"ADVENTURE_PHASE_DIRECT_END":   2,
	"ADVENTURE_PHASE_RANDOM_STEPS": 11,
	"ADVENTURE_PHASE_CONTROL_NEXT": 12,
}

func (x AdventurePhase) String() string {
	return proto.EnumName(AdventurePhase_name, int32(x))
}
func (AdventurePhase) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{3}
}

type EnrollFlag int32

const (
	EnrollFlag_ENROLL_FLAG_NONE          EnrollFlag = 0
	EnrollFlag_ENROLL_FLAG_NONE_SELECTED EnrollFlag = 1
)

var EnrollFlag_name = map[int32]string{
	0: "ENROLL_FLAG_NONE",
	1: "ENROLL_FLAG_NONE_SELECTED",
}
var EnrollFlag_value = map[string]int32{
	"ENROLL_FLAG_NONE":          0,
	"ENROLL_FLAG_NONE_SELECTED": 1,
}

func (x EnrollFlag) String() string {
	return proto.EnumName(EnrollFlag_name, int32(x))
}
func (EnrollFlag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{4}
}

type GameUserFlag int32

const (
	GameUserFlag_GAME_USER_FLAG_NONE        GameUserFlag = 0
	GameUserFlag_GAME_USER_FLAG_IN_PROGRESS GameUserFlag = 1
	GameUserFlag_GAME_USER_FLAG_QUIT        GameUserFlag = 2
)

var GameUserFlag_name = map[int32]string{
	0: "GAME_USER_FLAG_NONE",
	1: "GAME_USER_FLAG_IN_PROGRESS",
	2: "GAME_USER_FLAG_QUIT",
}
var GameUserFlag_value = map[string]int32{
	"GAME_USER_FLAG_NONE":        0,
	"GAME_USER_FLAG_IN_PROGRESS": 1,
	"GAME_USER_FLAG_QUIT":        2,
}

func (x GameUserFlag) String() string {
	return proto.EnumName(GameUserFlag_name, int32(x))
}
func (GameUserFlag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{5}
}

type GameOperateButton_ButtonType int32

const (
	GameOperateButton_BUTTON_INVALID GameOperateButton_ButtonType = 0
	GameOperateButton_BUTTON_COM     GameOperateButton_ButtonType = 1
)

var GameOperateButton_ButtonType_name = map[int32]string{
	0: "BUTTON_INVALID",
	1: "BUTTON_COM",
}
var GameOperateButton_ButtonType_value = map[string]int32{
	"BUTTON_INVALID": 0,
	"BUTTON_COM":     1,
}

func (x GameOperateButton_ButtonType) String() string {
	return proto.EnumName(GameOperateButton_ButtonType_name, int32(x))
}
func (GameOperateButton_ButtonType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{0, 0}
}

type SetGamePhaseReq_GamePhaseType int32

const (
	SetGamePhaseReq_GAME_PHASE_NOT_START SetGamePhaseReq_GamePhaseType = 0
	SetGamePhaseReq_GAME_PHASE_START     SetGamePhaseReq_GamePhaseType = 1
	SetGamePhaseReq_GAME_PHASE_FIN       SetGamePhaseReq_GamePhaseType = 2
)

var SetGamePhaseReq_GamePhaseType_name = map[int32]string{
	0: "GAME_PHASE_NOT_START",
	1: "GAME_PHASE_START",
	2: "GAME_PHASE_FIN",
}
var SetGamePhaseReq_GamePhaseType_value = map[string]int32{
	"GAME_PHASE_NOT_START": 0,
	"GAME_PHASE_START":     1,
	"GAME_PHASE_FIN":       2,
}

func (x SetGamePhaseReq_GamePhaseType) String() string {
	return proto.EnumName(SetGamePhaseReq_GamePhaseType_name, int32(x))
}
func (SetGamePhaseReq_GamePhaseType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{6, 0}
}

type GamePhaseChangeNotifyOpt_GameBombType int32

const (
	GamePhaseChangeNotifyOpt_GAME_BOMB_TYPE_INVALID GamePhaseChangeNotifyOpt_GameBombType = 0
	GamePhaseChangeNotifyOpt_GAME_WITH_HOST         GamePhaseChangeNotifyOpt_GameBombType = 1
	GamePhaseChangeNotifyOpt_GAME_WITHOUT_HOST      GamePhaseChangeNotifyOpt_GameBombType = 2
)

var GamePhaseChangeNotifyOpt_GameBombType_name = map[int32]string{
	0: "GAME_BOMB_TYPE_INVALID",
	1: "GAME_WITH_HOST",
	2: "GAME_WITHOUT_HOST",
}
var GamePhaseChangeNotifyOpt_GameBombType_value = map[string]int32{
	"GAME_BOMB_TYPE_INVALID": 0,
	"GAME_WITH_HOST":         1,
	"GAME_WITHOUT_HOST":      2,
}

func (x GamePhaseChangeNotifyOpt_GameBombType) String() string {
	return proto.EnumName(GamePhaseChangeNotifyOpt_GameBombType_name, int32(x))
}
func (GamePhaseChangeNotifyOpt_GameBombType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{8, 0}
}

type GameBombResultOpt_EndType int32

const (
	GameBombResultOpt_Common        GameBombResultOpt_EndType = 0
	GameBombResultOpt_Abnormal      GameBombResultOpt_EndType = 1
	GameBombResultOpt_EndWithNoBomb GameBombResultOpt_EndType = 2
)

var GameBombResultOpt_EndType_name = map[int32]string{
	0: "Common",
	1: "Abnormal",
	2: "EndWithNoBomb",
}
var GameBombResultOpt_EndType_value = map[string]int32{
	"Common":        0,
	"Abnormal":      1,
	"EndWithNoBomb": 2,
}

func (x GameBombResultOpt_EndType) String() string {
	return proto.EnumName(GameBombResultOpt_EndType_name, int32(x))
}
func (GameBombResultOpt_EndType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{22, 0}
}

type AdventureControlNextReq_NextOp int32

const (
	AdventureControlNextReq_UNKNOWN       AdventureControlNextReq_NextOp = 0
	AdventureControlNextReq_AGAIN         AdventureControlNextReq_NextOp = 1
	AdventureControlNextReq_NEXT_USER     AdventureControlNextReq_NextOp = 2
	AdventureControlNextReq_ANOTHER_ROUND AdventureControlNextReq_NextOp = 3
	AdventureControlNextReq_RESTART_GAME  AdventureControlNextReq_NextOp = 4
)

var AdventureControlNextReq_NextOp_name = map[int32]string{
	0: "UNKNOWN",
	1: "AGAIN",
	2: "NEXT_USER",
	3: "ANOTHER_ROUND",
	4: "RESTART_GAME",
}
var AdventureControlNextReq_NextOp_value = map[string]int32{
	"UNKNOWN":       0,
	"AGAIN":         1,
	"NEXT_USER":     2,
	"ANOTHER_ROUND": 3,
	"RESTART_GAME":  4,
}

func (x AdventureControlNextReq_NextOp) String() string {
	return proto.EnumName(AdventureControlNextReq_NextOp_name, int32(x))
}
func (AdventureControlNextReq_NextOp) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{36, 0}
}

type AdventureCellConf_AdventureCellAttr int32

const (
	AdventureCellConf_ADVENTURE_CELL_ATTR_UNSPECIFIED AdventureCellConf_AdventureCellAttr = 0
	AdventureCellConf_ADVENTURE_CELL_ATTR_WIN         AdventureCellConf_AdventureCellAttr = 1
	AdventureCellConf_ADVENTURE_CELL_ATTR_PUNISH      AdventureCellConf_AdventureCellAttr = 2
)

var AdventureCellConf_AdventureCellAttr_name = map[int32]string{
	0: "ADVENTURE_CELL_ATTR_UNSPECIFIED",
	1: "ADVENTURE_CELL_ATTR_WIN",
	2: "ADVENTURE_CELL_ATTR_PUNISH",
}
var AdventureCellConf_AdventureCellAttr_value = map[string]int32{
	"ADVENTURE_CELL_ATTR_UNSPECIFIED": 0,
	"ADVENTURE_CELL_ATTR_WIN":         1,
	"ADVENTURE_CELL_ATTR_PUNISH":      2,
}

func (x AdventureCellConf_AdventureCellAttr) String() string {
	return proto.EnumName(AdventureCellConf_AdventureCellAttr_name, int32(x))
}
func (AdventureCellConf_AdventureCellAttr) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{39, 0}
}

type AdventureSettleInfo_SettleType int32

const (
	AdventureSettleInfo_SETTLE_TYPE_UNSPECIFIED AdventureSettleInfo_SettleType = 0
	AdventureSettleInfo_SETTLE_TYPE_WIN         AdventureSettleInfo_SettleType = 1
	AdventureSettleInfo_SETTLE_TYPE_PUNISH      AdventureSettleInfo_SettleType = 2
)

var AdventureSettleInfo_SettleType_name = map[int32]string{
	0: "SETTLE_TYPE_UNSPECIFIED",
	1: "SETTLE_TYPE_WIN",
	2: "SETTLE_TYPE_PUNISH",
}
var AdventureSettleInfo_SettleType_value = map[string]int32{
	"SETTLE_TYPE_UNSPECIFIED": 0,
	"SETTLE_TYPE_WIN":         1,
	"SETTLE_TYPE_PUNISH":      2,
}

func (x AdventureSettleInfo_SettleType) String() string {
	return proto.EnumName(AdventureSettleInfo_SettleType_name, int32(x))
}
func (AdventureSettleInfo_SettleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{41, 0}
}

// 某个具体的操作按钮
type GameOperateButton struct {
	ButtonId             uint32   `protobuf:"varint,1,opt,name=button_id,json=buttonId,proto3" json:"button_id,omitempty"`
	ButtonType           uint32   `protobuf:"varint,2,opt,name=button_type,json=buttonType,proto3" json:"button_type,omitempty"`
	ButtonText           string   `protobuf:"bytes,3,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameOperateButton) Reset()         { *m = GameOperateButton{} }
func (m *GameOperateButton) String() string { return proto.CompactTextString(m) }
func (*GameOperateButton) ProtoMessage()    {}
func (*GameOperateButton) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{0}
}
func (m *GameOperateButton) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameOperateButton.Unmarshal(m, b)
}
func (m *GameOperateButton) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameOperateButton.Marshal(b, m, deterministic)
}
func (dst *GameOperateButton) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameOperateButton.Merge(dst, src)
}
func (m *GameOperateButton) XXX_Size() int {
	return xxx_messageInfo_GameOperateButton.Size(m)
}
func (m *GameOperateButton) XXX_DiscardUnknown() {
	xxx_messageInfo_GameOperateButton.DiscardUnknown(m)
}

var xxx_messageInfo_GameOperateButton proto.InternalMessageInfo

func (m *GameOperateButton) GetButtonId() uint32 {
	if m != nil {
		return m.ButtonId
	}
	return 0
}

func (m *GameOperateButton) GetButtonType() uint32 {
	if m != nil {
		return m.ButtonType
	}
	return 0
}

func (m *GameOperateButton) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

// 操作区域信息
type GameOperateInfo struct {
	OperateId            uint32               `protobuf:"varint,1,opt,name=operate_id,json=operateId,proto3" json:"operate_id,omitempty"`
	Title                string               `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	ButtonList           []*GameOperateButton `protobuf:"bytes,3,rep,name=button_list,json=buttonList,proto3" json:"button_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GameOperateInfo) Reset()         { *m = GameOperateInfo{} }
func (m *GameOperateInfo) String() string { return proto.CompactTextString(m) }
func (*GameOperateInfo) ProtoMessage()    {}
func (*GameOperateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{1}
}
func (m *GameOperateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameOperateInfo.Unmarshal(m, b)
}
func (m *GameOperateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameOperateInfo.Marshal(b, m, deterministic)
}
func (dst *GameOperateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameOperateInfo.Merge(dst, src)
}
func (m *GameOperateInfo) XXX_Size() int {
	return xxx_messageInfo_GameOperateInfo.Size(m)
}
func (m *GameOperateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameOperateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameOperateInfo proto.InternalMessageInfo

func (m *GameOperateInfo) GetOperateId() uint32 {
	if m != nil {
		return m.OperateId
	}
	return 0
}

func (m *GameOperateInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GameOperateInfo) GetButtonList() []*GameOperateButton {
	if m != nil {
		return m.ButtonList
	}
	return nil
}

type ChoseOperate struct {
	OperateId            uint32   `protobuf:"varint,1,opt,name=operate_id,json=operateId,proto3" json:"operate_id,omitempty"`
	ButtonId             uint32   `protobuf:"varint,2,opt,name=button_id,json=buttonId,proto3" json:"button_id,omitempty"`
	InputText            string   `protobuf:"bytes,3,opt,name=input_text,json=inputText,proto3" json:"input_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChoseOperate) Reset()         { *m = ChoseOperate{} }
func (m *ChoseOperate) String() string { return proto.CompactTextString(m) }
func (*ChoseOperate) ProtoMessage()    {}
func (*ChoseOperate) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{2}
}
func (m *ChoseOperate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChoseOperate.Unmarshal(m, b)
}
func (m *ChoseOperate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChoseOperate.Marshal(b, m, deterministic)
}
func (dst *ChoseOperate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChoseOperate.Merge(dst, src)
}
func (m *ChoseOperate) XXX_Size() int {
	return xxx_messageInfo_ChoseOperate.Size(m)
}
func (m *ChoseOperate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChoseOperate.DiscardUnknown(m)
}

var xxx_messageInfo_ChoseOperate proto.InternalMessageInfo

func (m *ChoseOperate) GetOperateId() uint32 {
	if m != nil {
		return m.OperateId
	}
	return 0
}

func (m *ChoseOperate) GetButtonId() uint32 {
	if m != nil {
		return m.ButtonId
	}
	return 0
}

func (m *ChoseOperate) GetInputText() string {
	if m != nil {
		return m.InputText
	}
	return ""
}

// 玩法页信息
type GameSummary struct {
	GameId               uint32             `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string             `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	GameIcon             string             `protobuf:"bytes,3,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon,omitempty"`
	CmsUrl               string             `protobuf:"bytes,4,opt,name=cms_url,json=cmsUrl,proto3" json:"cms_url,omitempty"`
	Info                 []*GameOperateInfo `protobuf:"bytes,5,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GameSummary) Reset()         { *m = GameSummary{} }
func (m *GameSummary) String() string { return proto.CompactTextString(m) }
func (*GameSummary) ProtoMessage()    {}
func (*GameSummary) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{3}
}
func (m *GameSummary) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameSummary.Unmarshal(m, b)
}
func (m *GameSummary) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameSummary.Marshal(b, m, deterministic)
}
func (dst *GameSummary) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameSummary.Merge(dst, src)
}
func (m *GameSummary) XXX_Size() int {
	return xxx_messageInfo_GameSummary.Size(m)
}
func (m *GameSummary) XXX_DiscardUnknown() {
	xxx_messageInfo_GameSummary.DiscardUnknown(m)
}

var xxx_messageInfo_GameSummary proto.InternalMessageInfo

func (m *GameSummary) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameSummary) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GameSummary) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

func (m *GameSummary) GetCmsUrl() string {
	if m != nil {
		return m.CmsUrl
	}
	return ""
}

func (m *GameSummary) GetInfo() []*GameOperateInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 获取小游戏列表
type GetGameListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGameListReq) Reset()         { *m = GetGameListReq{} }
func (m *GetGameListReq) String() string { return proto.CompactTextString(m) }
func (*GetGameListReq) ProtoMessage()    {}
func (*GetGameListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{4}
}
func (m *GetGameListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameListReq.Unmarshal(m, b)
}
func (m *GetGameListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameListReq.Marshal(b, m, deterministic)
}
func (dst *GetGameListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameListReq.Merge(dst, src)
}
func (m *GetGameListReq) XXX_Size() int {
	return xxx_messageInfo_GetGameListReq.Size(m)
}
func (m *GetGameListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameListReq proto.InternalMessageInfo

func (m *GetGameListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetGameListResp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GameList             []*GameSummary `protobuf:"bytes,2,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetGameListResp) Reset()         { *m = GetGameListResp{} }
func (m *GetGameListResp) String() string { return proto.CompactTextString(m) }
func (*GetGameListResp) ProtoMessage()    {}
func (*GetGameListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{5}
}
func (m *GetGameListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameListResp.Unmarshal(m, b)
}
func (m *GetGameListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameListResp.Marshal(b, m, deterministic)
}
func (dst *GetGameListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameListResp.Merge(dst, src)
}
func (m *GetGameListResp) XXX_Size() int {
	return xxx_messageInfo_GetGameListResp.Size(m)
}
func (m *GetGameListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameListResp proto.InternalMessageInfo

func (m *GetGameListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameListResp) GetGameList() []*GameSummary {
	if m != nil {
		return m.GameList
	}
	return nil
}

// 设置小游戏开始或者结束
type SetGamePhaseReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32          `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32          `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	TargetPhase          uint32          `protobuf:"varint,4,opt,name=target_phase,json=targetPhase,proto3" json:"target_phase,omitempty"`
	Chose                []*ChoseOperate `protobuf:"bytes,5,rep,name=chose,proto3" json:"chose,omitempty"`
	IsAgain              bool            `protobuf:"varint,6,opt,name=is_again,json=isAgain,proto3" json:"is_again,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SetGamePhaseReq) Reset()         { *m = SetGamePhaseReq{} }
func (m *SetGamePhaseReq) String() string { return proto.CompactTextString(m) }
func (*SetGamePhaseReq) ProtoMessage()    {}
func (*SetGamePhaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{6}
}
func (m *SetGamePhaseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGamePhaseReq.Unmarshal(m, b)
}
func (m *SetGamePhaseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGamePhaseReq.Marshal(b, m, deterministic)
}
func (dst *SetGamePhaseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGamePhaseReq.Merge(dst, src)
}
func (m *SetGamePhaseReq) XXX_Size() int {
	return xxx_messageInfo_SetGamePhaseReq.Size(m)
}
func (m *SetGamePhaseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGamePhaseReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGamePhaseReq proto.InternalMessageInfo

func (m *SetGamePhaseReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetGamePhaseReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetGamePhaseReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetGamePhaseReq) GetTargetPhase() uint32 {
	if m != nil {
		return m.TargetPhase
	}
	return 0
}

func (m *SetGamePhaseReq) GetChose() []*ChoseOperate {
	if m != nil {
		return m.Chose
	}
	return nil
}

func (m *SetGamePhaseReq) GetIsAgain() bool {
	if m != nil {
		return m.IsAgain
	}
	return false
}

type SetGamePhaseResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GameId               uint32        `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	TargetPhase          uint32        `protobuf:"varint,3,opt,name=target_phase,json=targetPhase,proto3" json:"target_phase,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetGamePhaseResp) Reset()         { *m = SetGamePhaseResp{} }
func (m *SetGamePhaseResp) String() string { return proto.CompactTextString(m) }
func (*SetGamePhaseResp) ProtoMessage()    {}
func (*SetGamePhaseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{7}
}
func (m *SetGamePhaseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGamePhaseResp.Unmarshal(m, b)
}
func (m *SetGamePhaseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGamePhaseResp.Marshal(b, m, deterministic)
}
func (dst *SetGamePhaseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGamePhaseResp.Merge(dst, src)
}
func (m *SetGamePhaseResp) XXX_Size() int {
	return xxx_messageInfo_SetGamePhaseResp.Size(m)
}
func (m *SetGamePhaseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGamePhaseResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGamePhaseResp proto.InternalMessageInfo

func (m *SetGamePhaseResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SetGamePhaseResp) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetGamePhaseResp) GetTargetPhase() uint32 {
	if m != nil {
		return m.TargetPhase
	}
	return 0
}

// 阶段变化的 notify
type GamePhaseChangeNotifyOpt struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	TargetPhase          uint32   `protobuf:"varint,3,opt,name=target_phase,json=targetPhase,proto3" json:"target_phase,omitempty"`
	GameBombType         uint32   `protobuf:"varint,4,opt,name=game_bomb_type,json=gameBombType,proto3" json:"game_bomb_type,omitempty"`
	GameMinTime          uint32   `protobuf:"varint,5,opt,name=game_min_time,json=gameMinTime,proto3" json:"game_min_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GamePhaseChangeNotifyOpt) Reset()         { *m = GamePhaseChangeNotifyOpt{} }
func (m *GamePhaseChangeNotifyOpt) String() string { return proto.CompactTextString(m) }
func (*GamePhaseChangeNotifyOpt) ProtoMessage()    {}
func (*GamePhaseChangeNotifyOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{8}
}
func (m *GamePhaseChangeNotifyOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GamePhaseChangeNotifyOpt.Unmarshal(m, b)
}
func (m *GamePhaseChangeNotifyOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GamePhaseChangeNotifyOpt.Marshal(b, m, deterministic)
}
func (dst *GamePhaseChangeNotifyOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GamePhaseChangeNotifyOpt.Merge(dst, src)
}
func (m *GamePhaseChangeNotifyOpt) XXX_Size() int {
	return xxx_messageInfo_GamePhaseChangeNotifyOpt.Size(m)
}
func (m *GamePhaseChangeNotifyOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_GamePhaseChangeNotifyOpt.DiscardUnknown(m)
}

var xxx_messageInfo_GamePhaseChangeNotifyOpt proto.InternalMessageInfo

func (m *GamePhaseChangeNotifyOpt) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GamePhaseChangeNotifyOpt) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GamePhaseChangeNotifyOpt) GetTargetPhase() uint32 {
	if m != nil {
		return m.TargetPhase
	}
	return 0
}

func (m *GamePhaseChangeNotifyOpt) GetGameBombType() uint32 {
	if m != nil {
		return m.GameBombType
	}
	return 0
}

func (m *GamePhaseChangeNotifyOpt) GetGameMinTime() uint32 {
	if m != nil {
		return m.GameMinTime
	}
	return 0
}

// 甩雷
type SetNextBombUserReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ThrowUid             uint32       `protobuf:"varint,3,opt,name=throw_uid,json=throwUid,proto3" json:"throw_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetNextBombUserReq) Reset()         { *m = SetNextBombUserReq{} }
func (m *SetNextBombUserReq) String() string { return proto.CompactTextString(m) }
func (*SetNextBombUserReq) ProtoMessage()    {}
func (*SetNextBombUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{9}
}
func (m *SetNextBombUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNextBombUserReq.Unmarshal(m, b)
}
func (m *SetNextBombUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNextBombUserReq.Marshal(b, m, deterministic)
}
func (dst *SetNextBombUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNextBombUserReq.Merge(dst, src)
}
func (m *SetNextBombUserReq) XXX_Size() int {
	return xxx_messageInfo_SetNextBombUserReq.Size(m)
}
func (m *SetNextBombUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNextBombUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetNextBombUserReq proto.InternalMessageInfo

func (m *SetNextBombUserReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetNextBombUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetNextBombUserReq) GetThrowUid() uint32 {
	if m != nil {
		return m.ThrowUid
	}
	return 0
}

type SetNextBombUserResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetNextBombUserResp) Reset()         { *m = SetNextBombUserResp{} }
func (m *SetNextBombUserResp) String() string { return proto.CompactTextString(m) }
func (*SetNextBombUserResp) ProtoMessage()    {}
func (*SetNextBombUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{10}
}
func (m *SetNextBombUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNextBombUserResp.Unmarshal(m, b)
}
func (m *SetNextBombUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNextBombUserResp.Marshal(b, m, deterministic)
}
func (dst *SetNextBombUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNextBombUserResp.Merge(dst, src)
}
func (m *SetNextBombUserResp) XXX_Size() int {
	return xxx_messageInfo_SetNextBombUserResp.Size(m)
}
func (m *SetNextBombUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNextBombUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetNextBombUserResp proto.InternalMessageInfo

func (m *SetNextBombUserResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 游戏信息集合
type GameInfoSet struct {
	// Types that are valid to be assigned to GameInfo:
	//	*GameInfoSet_AdventureInfo
	GameInfo             isGameInfoSet_GameInfo `protobuf_oneof:"game_info"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GameInfoSet) Reset()         { *m = GameInfoSet{} }
func (m *GameInfoSet) String() string { return proto.CompactTextString(m) }
func (*GameInfoSet) ProtoMessage()    {}
func (*GameInfoSet) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{11}
}
func (m *GameInfoSet) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInfoSet.Unmarshal(m, b)
}
func (m *GameInfoSet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInfoSet.Marshal(b, m, deterministic)
}
func (dst *GameInfoSet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInfoSet.Merge(dst, src)
}
func (m *GameInfoSet) XXX_Size() int {
	return xxx_messageInfo_GameInfoSet.Size(m)
}
func (m *GameInfoSet) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInfoSet.DiscardUnknown(m)
}

var xxx_messageInfo_GameInfoSet proto.InternalMessageInfo

type isGameInfoSet_GameInfo interface {
	isGameInfoSet_GameInfo()
}

type GameInfoSet_AdventureInfo struct {
	AdventureInfo *AdventureGameInfo `protobuf:"bytes,1,opt,name=adventure_info,json=adventureInfo,proto3,oneof"`
}

func (*GameInfoSet_AdventureInfo) isGameInfoSet_GameInfo() {}

func (m *GameInfoSet) GetGameInfo() isGameInfoSet_GameInfo {
	if m != nil {
		return m.GameInfo
	}
	return nil
}

func (m *GameInfoSet) GetAdventureInfo() *AdventureGameInfo {
	if x, ok := m.GetGameInfo().(*GameInfoSet_AdventureInfo); ok {
		return x.AdventureInfo
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*GameInfoSet) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _GameInfoSet_OneofMarshaler, _GameInfoSet_OneofUnmarshaler, _GameInfoSet_OneofSizer, []interface{}{
		(*GameInfoSet_AdventureInfo)(nil),
	}
}

func _GameInfoSet_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*GameInfoSet)
	// game_info
	switch x := m.GameInfo.(type) {
	case *GameInfoSet_AdventureInfo:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.AdventureInfo); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("GameInfoSet.GameInfo has unexpected type %T", x)
	}
	return nil
}

func _GameInfoSet_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*GameInfoSet)
	switch tag {
	case 1: // game_info.adventure_info
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(AdventureGameInfo)
		err := b.DecodeMessage(msg)
		m.GameInfo = &GameInfoSet_AdventureInfo{msg}
		return true, err
	default:
		return false, nil
	}
}

func _GameInfoSet_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*GameInfoSet)
	// game_info
	switch x := m.GameInfo.(type) {
	case *GameInfoSet_AdventureInfo:
		s := proto.Size(x.AdventureInfo)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// 进房获取游戏信息
type GetChannelGameInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelGameInfoReq) Reset()         { *m = GetChannelGameInfoReq{} }
func (m *GetChannelGameInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelGameInfoReq) ProtoMessage()    {}
func (*GetChannelGameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{12}
}
func (m *GetChannelGameInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelGameInfoReq.Unmarshal(m, b)
}
func (m *GetChannelGameInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelGameInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelGameInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelGameInfoReq.Merge(dst, src)
}
func (m *GetChannelGameInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelGameInfoReq.Size(m)
}
func (m *GetChannelGameInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelGameInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelGameInfoReq proto.InternalMessageInfo

func (m *GetChannelGameInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelGameInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelGameInfoResp struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32               `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CurrentGameId        uint32               `protobuf:"varint,3,opt,name=current_game_id,json=currentGameId,proto3" json:"current_game_id,omitempty"`
	Phase                uint32               `protobuf:"varint,4,opt,name=phase,proto3" json:"phase,omitempty"`
	BombInfo             *GameThrowBombInfo   `protobuf:"bytes,5,opt,name=bomb_info,json=bombInfo,proto3" json:"bomb_info,omitempty"`
	DigitalBombInfo      *DigitalBombGameInfo `protobuf:"bytes,6,opt,name=digital_bomb_info,json=digitalBombInfo,proto3" json:"digital_bomb_info,omitempty"`
	GameInfo             *GameInfoSet         `protobuf:"bytes,7,opt,name=game_info,json=gameInfo,proto3" json:"game_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetChannelGameInfoResp) Reset()         { *m = GetChannelGameInfoResp{} }
func (m *GetChannelGameInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelGameInfoResp) ProtoMessage()    {}
func (*GetChannelGameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{13}
}
func (m *GetChannelGameInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelGameInfoResp.Unmarshal(m, b)
}
func (m *GetChannelGameInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelGameInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelGameInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelGameInfoResp.Merge(dst, src)
}
func (m *GetChannelGameInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelGameInfoResp.Size(m)
}
func (m *GetChannelGameInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelGameInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelGameInfoResp proto.InternalMessageInfo

func (m *GetChannelGameInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelGameInfoResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelGameInfoResp) GetCurrentGameId() uint32 {
	if m != nil {
		return m.CurrentGameId
	}
	return 0
}

func (m *GetChannelGameInfoResp) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *GetChannelGameInfoResp) GetBombInfo() *GameThrowBombInfo {
	if m != nil {
		return m.BombInfo
	}
	return nil
}

func (m *GetChannelGameInfoResp) GetDigitalBombInfo() *DigitalBombGameInfo {
	if m != nil {
		return m.DigitalBombInfo
	}
	return nil
}

func (m *GetChannelGameInfoResp) GetGameInfo() *GameInfoSet {
	if m != nil {
		return m.GameInfo
	}
	return nil
}

type GameThrowBombInfo struct {
	Bomb                 *GameBombUserInfo     `protobuf:"bytes,1,opt,name=bomb,proto3" json:"bomb,omitempty"`
	Punish               []*GameBombPunishInfo `protobuf:"bytes,2,rep,name=punish,proto3" json:"punish,omitempty"`
	ThrowList            []*GameThrowInfo      `protobuf:"bytes,3,rep,name=throw_list,json=throwList,proto3" json:"throw_list,omitempty"`
	GameBombType         uint32                `protobuf:"varint,4,opt,name=game_bomb_type,json=gameBombType,proto3" json:"game_bomb_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GameThrowBombInfo) Reset()         { *m = GameThrowBombInfo{} }
func (m *GameThrowBombInfo) String() string { return proto.CompactTextString(m) }
func (*GameThrowBombInfo) ProtoMessage()    {}
func (*GameThrowBombInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{14}
}
func (m *GameThrowBombInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameThrowBombInfo.Unmarshal(m, b)
}
func (m *GameThrowBombInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameThrowBombInfo.Marshal(b, m, deterministic)
}
func (dst *GameThrowBombInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameThrowBombInfo.Merge(dst, src)
}
func (m *GameThrowBombInfo) XXX_Size() int {
	return xxx_messageInfo_GameThrowBombInfo.Size(m)
}
func (m *GameThrowBombInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameThrowBombInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameThrowBombInfo proto.InternalMessageInfo

func (m *GameThrowBombInfo) GetBomb() *GameBombUserInfo {
	if m != nil {
		return m.Bomb
	}
	return nil
}

func (m *GameThrowBombInfo) GetPunish() []*GameBombPunishInfo {
	if m != nil {
		return m.Punish
	}
	return nil
}

func (m *GameThrowBombInfo) GetThrowList() []*GameThrowInfo {
	if m != nil {
		return m.ThrowList
	}
	return nil
}

func (m *GameThrowBombInfo) GetGameBombType() uint32 {
	if m != nil {
		return m.GameBombType
	}
	return 0
}

// 麦上用户甩雷信息
type GameThrowInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GiftVal              uint32   `protobuf:"varint,2,opt,name=gift_val,json=giftVal,proto3" json:"gift_val,omitempty"`
	ThrowStatus          bool     `protobuf:"varint,3,opt,name=throw_status,json=throwStatus,proto3" json:"throw_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameThrowInfo) Reset()         { *m = GameThrowInfo{} }
func (m *GameThrowInfo) String() string { return proto.CompactTextString(m) }
func (*GameThrowInfo) ProtoMessage()    {}
func (*GameThrowInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{15}
}
func (m *GameThrowInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameThrowInfo.Unmarshal(m, b)
}
func (m *GameThrowInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameThrowInfo.Marshal(b, m, deterministic)
}
func (dst *GameThrowInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameThrowInfo.Merge(dst, src)
}
func (m *GameThrowInfo) XXX_Size() int {
	return xxx_messageInfo_GameThrowInfo.Size(m)
}
func (m *GameThrowInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameThrowInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameThrowInfo proto.InternalMessageInfo

func (m *GameThrowInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameThrowInfo) GetGiftVal() uint32 {
	if m != nil {
		return m.GiftVal
	}
	return 0
}

func (m *GameThrowInfo) GetThrowStatus() bool {
	if m != nil {
		return m.ThrowStatus
	}
	return false
}

// 雷所在用户信息
type GameBombUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	NeedRescue           bool     `protobuf:"varint,3,opt,name=need_rescue,json=needRescue,proto3" json:"need_rescue,omitempty"`
	AutoThrowTime        uint32   `protobuf:"varint,4,opt,name=auto_throw_time,json=autoThrowTime,proto3" json:"auto_throw_time,omitempty"`
	ServerTime           uint32   `protobuf:"varint,5,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameBombUserInfo) Reset()         { *m = GameBombUserInfo{} }
func (m *GameBombUserInfo) String() string { return proto.CompactTextString(m) }
func (*GameBombUserInfo) ProtoMessage()    {}
func (*GameBombUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{16}
}
func (m *GameBombUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameBombUserInfo.Unmarshal(m, b)
}
func (m *GameBombUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameBombUserInfo.Marshal(b, m, deterministic)
}
func (dst *GameBombUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameBombUserInfo.Merge(dst, src)
}
func (m *GameBombUserInfo) XXX_Size() int {
	return xxx_messageInfo_GameBombUserInfo.Size(m)
}
func (m *GameBombUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameBombUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameBombUserInfo proto.InternalMessageInfo

func (m *GameBombUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameBombUserInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GameBombUserInfo) GetNeedRescue() bool {
	if m != nil {
		return m.NeedRescue
	}
	return false
}

func (m *GameBombUserInfo) GetAutoThrowTime() uint32 {
	if m != nil {
		return m.AutoThrowTime
	}
	return 0
}

func (m *GameBombUserInfo) GetServerTime() uint32 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

// 雷所在用户信息 - 广播
type GameBombUserNotifyOPt struct {
	Bomb                 *GameBombUserInfo `protobuf:"bytes,1,opt,name=bomb,proto3" json:"bomb,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GameBombUserNotifyOPt) Reset()         { *m = GameBombUserNotifyOPt{} }
func (m *GameBombUserNotifyOPt) String() string { return proto.CompactTextString(m) }
func (*GameBombUserNotifyOPt) ProtoMessage()    {}
func (*GameBombUserNotifyOPt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{17}
}
func (m *GameBombUserNotifyOPt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameBombUserNotifyOPt.Unmarshal(m, b)
}
func (m *GameBombUserNotifyOPt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameBombUserNotifyOPt.Marshal(b, m, deterministic)
}
func (dst *GameBombUserNotifyOPt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameBombUserNotifyOPt.Merge(dst, src)
}
func (m *GameBombUserNotifyOPt) XXX_Size() int {
	return xxx_messageInfo_GameBombUserNotifyOPt.Size(m)
}
func (m *GameBombUserNotifyOPt) XXX_DiscardUnknown() {
	xxx_messageInfo_GameBombUserNotifyOPt.DiscardUnknown(m)
}

var xxx_messageInfo_GameBombUserNotifyOPt proto.InternalMessageInfo

func (m *GameBombUserNotifyOPt) GetBomb() *GameBombUserInfo {
	if m != nil {
		return m.Bomb
	}
	return nil
}

// 甩雷-惩罚信息
type GameBombPunishInfo struct {
	UserProfile          *app.UserProfile `protobuf:"bytes,1,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	EndTime              uint32           `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	RescueGiftId         uint32           `protobuf:"varint,3,opt,name=rescue_gift_id,json=rescueGiftId,proto3" json:"rescue_gift_id,omitempty"`
	RescueGiftName       string           `protobuf:"bytes,4,opt,name=rescue_gift_name,json=rescueGiftName,proto3" json:"rescue_gift_name,omitempty"`
	IconUrl              string           `protobuf:"bytes,5,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	IconMd5              string           `protobuf:"bytes,6,opt,name=icon_md5,json=iconMd5,proto3" json:"icon_md5,omitempty"`
	ServerTime           uint32           `protobuf:"varint,7,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GameBombPunishInfo) Reset()         { *m = GameBombPunishInfo{} }
func (m *GameBombPunishInfo) String() string { return proto.CompactTextString(m) }
func (*GameBombPunishInfo) ProtoMessage()    {}
func (*GameBombPunishInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{18}
}
func (m *GameBombPunishInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameBombPunishInfo.Unmarshal(m, b)
}
func (m *GameBombPunishInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameBombPunishInfo.Marshal(b, m, deterministic)
}
func (dst *GameBombPunishInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameBombPunishInfo.Merge(dst, src)
}
func (m *GameBombPunishInfo) XXX_Size() int {
	return xxx_messageInfo_GameBombPunishInfo.Size(m)
}
func (m *GameBombPunishInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameBombPunishInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameBombPunishInfo proto.InternalMessageInfo

func (m *GameBombPunishInfo) GetUserProfile() *app.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *GameBombPunishInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GameBombPunishInfo) GetRescueGiftId() uint32 {
	if m != nil {
		return m.RescueGiftId
	}
	return 0
}

func (m *GameBombPunishInfo) GetRescueGiftName() string {
	if m != nil {
		return m.RescueGiftName
	}
	return ""
}

func (m *GameBombPunishInfo) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *GameBombPunishInfo) GetIconMd5() string {
	if m != nil {
		return m.IconMd5
	}
	return ""
}

func (m *GameBombPunishInfo) GetServerTime() uint32 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

// 惩罚用户上麦房间广播
type GameBombPunishNotifyOPt struct {
	Punish               *GameBombPunishInfo `protobuf:"bytes,1,opt,name=punish,proto3" json:"punish,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GameBombPunishNotifyOPt) Reset()         { *m = GameBombPunishNotifyOPt{} }
func (m *GameBombPunishNotifyOPt) String() string { return proto.CompactTextString(m) }
func (*GameBombPunishNotifyOPt) ProtoMessage()    {}
func (*GameBombPunishNotifyOPt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{19}
}
func (m *GameBombPunishNotifyOPt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameBombPunishNotifyOPt.Unmarshal(m, b)
}
func (m *GameBombPunishNotifyOPt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameBombPunishNotifyOPt.Marshal(b, m, deterministic)
}
func (dst *GameBombPunishNotifyOPt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameBombPunishNotifyOPt.Merge(dst, src)
}
func (m *GameBombPunishNotifyOPt) XXX_Size() int {
	return xxx_messageInfo_GameBombPunishNotifyOPt.Size(m)
}
func (m *GameBombPunishNotifyOPt) XXX_DiscardUnknown() {
	xxx_messageInfo_GameBombPunishNotifyOPt.DiscardUnknown(m)
}

var xxx_messageInfo_GameBombPunishNotifyOPt proto.InternalMessageInfo

func (m *GameBombPunishNotifyOPt) GetPunish() *GameBombPunishInfo {
	if m != nil {
		return m.Punish
	}
	return nil
}

// 可以甩雷的用户变更广播 送礼上麦或者甩雷触发
type GameThrowUserNotifyOPt struct {
	ThrowList            []*GameThrowInfo `protobuf:"bytes,1,rep,name=throw_list,json=throwList,proto3" json:"throw_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GameThrowUserNotifyOPt) Reset()         { *m = GameThrowUserNotifyOPt{} }
func (m *GameThrowUserNotifyOPt) String() string { return proto.CompactTextString(m) }
func (*GameThrowUserNotifyOPt) ProtoMessage()    {}
func (*GameThrowUserNotifyOPt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{20}
}
func (m *GameThrowUserNotifyOPt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameThrowUserNotifyOPt.Unmarshal(m, b)
}
func (m *GameThrowUserNotifyOPt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameThrowUserNotifyOPt.Marshal(b, m, deterministic)
}
func (dst *GameThrowUserNotifyOPt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameThrowUserNotifyOPt.Merge(dst, src)
}
func (m *GameThrowUserNotifyOPt) XXX_Size() int {
	return xxx_messageInfo_GameThrowUserNotifyOPt.Size(m)
}
func (m *GameThrowUserNotifyOPt) XXX_DiscardUnknown() {
	xxx_messageInfo_GameThrowUserNotifyOPt.DiscardUnknown(m)
}

var xxx_messageInfo_GameThrowUserNotifyOPt proto.InternalMessageInfo

func (m *GameThrowUserNotifyOPt) GetThrowList() []*GameThrowInfo {
	if m != nil {
		return m.ThrowList
	}
	return nil
}

// 解救公屏消息
type GameRescueNotifyOPt struct {
	Toast                string   `protobuf:"bytes,1,opt,name=toast,proto3" json:"toast,omitempty"`
	RescueGiftName       string   `protobuf:"bytes,2,opt,name=rescue_gift_name,json=rescueGiftName,proto3" json:"rescue_gift_name,omitempty"`
	RescueGiftIcon       string   `protobuf:"bytes,3,opt,name=rescue_gift_icon,json=rescueGiftIcon,proto3" json:"rescue_gift_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameRescueNotifyOPt) Reset()         { *m = GameRescueNotifyOPt{} }
func (m *GameRescueNotifyOPt) String() string { return proto.CompactTextString(m) }
func (*GameRescueNotifyOPt) ProtoMessage()    {}
func (*GameRescueNotifyOPt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{21}
}
func (m *GameRescueNotifyOPt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameRescueNotifyOPt.Unmarshal(m, b)
}
func (m *GameRescueNotifyOPt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameRescueNotifyOPt.Marshal(b, m, deterministic)
}
func (dst *GameRescueNotifyOPt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameRescueNotifyOPt.Merge(dst, src)
}
func (m *GameRescueNotifyOPt) XXX_Size() int {
	return xxx_messageInfo_GameRescueNotifyOPt.Size(m)
}
func (m *GameRescueNotifyOPt) XXX_DiscardUnknown() {
	xxx_messageInfo_GameRescueNotifyOPt.DiscardUnknown(m)
}

var xxx_messageInfo_GameRescueNotifyOPt proto.InternalMessageInfo

func (m *GameRescueNotifyOPt) GetToast() string {
	if m != nil {
		return m.Toast
	}
	return ""
}

func (m *GameRescueNotifyOPt) GetRescueGiftName() string {
	if m != nil {
		return m.RescueGiftName
	}
	return ""
}

func (m *GameRescueNotifyOPt) GetRescueGiftIcon() string {
	if m != nil {
		return m.RescueGiftIcon
	}
	return ""
}

// 甩雷结束-广播
type GameBombResultOpt struct {
	EndType              uint32              `protobuf:"varint,1,opt,name=end_type,json=endType,proto3" json:"end_type,omitempty"`
	Punish               *GameBombPunishInfo `protobuf:"bytes,2,opt,name=punish,proto3" json:"punish,omitempty"`
	Toast                string              `protobuf:"bytes,3,opt,name=toast,proto3" json:"toast,omitempty"`
	BombMicId            uint32              `protobuf:"varint,4,opt,name=bomb_mic_id,json=bombMicId,proto3" json:"bomb_mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GameBombResultOpt) Reset()         { *m = GameBombResultOpt{} }
func (m *GameBombResultOpt) String() string { return proto.CompactTextString(m) }
func (*GameBombResultOpt) ProtoMessage()    {}
func (*GameBombResultOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{22}
}
func (m *GameBombResultOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameBombResultOpt.Unmarshal(m, b)
}
func (m *GameBombResultOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameBombResultOpt.Marshal(b, m, deterministic)
}
func (dst *GameBombResultOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameBombResultOpt.Merge(dst, src)
}
func (m *GameBombResultOpt) XXX_Size() int {
	return xxx_messageInfo_GameBombResultOpt.Size(m)
}
func (m *GameBombResultOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_GameBombResultOpt.DiscardUnknown(m)
}

var xxx_messageInfo_GameBombResultOpt proto.InternalMessageInfo

func (m *GameBombResultOpt) GetEndType() uint32 {
	if m != nil {
		return m.EndType
	}
	return 0
}

func (m *GameBombResultOpt) GetPunish() *GameBombPunishInfo {
	if m != nil {
		return m.Punish
	}
	return nil
}

func (m *GameBombResultOpt) GetToast() string {
	if m != nil {
		return m.Toast
	}
	return ""
}

func (m *GameBombResultOpt) GetBombMicId() uint32 {
	if m != nil {
		return m.BombMicId
	}
	return 0
}

// 报名/取消报名
type DigitalBombEnrollReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Op                   uint32       `protobuf:"varint,3,opt,name=op,proto3" json:"op,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DigitalBombEnrollReq) Reset()         { *m = DigitalBombEnrollReq{} }
func (m *DigitalBombEnrollReq) String() string { return proto.CompactTextString(m) }
func (*DigitalBombEnrollReq) ProtoMessage()    {}
func (*DigitalBombEnrollReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{23}
}
func (m *DigitalBombEnrollReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DigitalBombEnrollReq.Unmarshal(m, b)
}
func (m *DigitalBombEnrollReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DigitalBombEnrollReq.Marshal(b, m, deterministic)
}
func (dst *DigitalBombEnrollReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DigitalBombEnrollReq.Merge(dst, src)
}
func (m *DigitalBombEnrollReq) XXX_Size() int {
	return xxx_messageInfo_DigitalBombEnrollReq.Size(m)
}
func (m *DigitalBombEnrollReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DigitalBombEnrollReq.DiscardUnknown(m)
}

var xxx_messageInfo_DigitalBombEnrollReq proto.InternalMessageInfo

func (m *DigitalBombEnrollReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DigitalBombEnrollReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DigitalBombEnrollReq) GetOp() uint32 {
	if m != nil {
		return m.Op
	}
	return 0
}

type DigitalBombEnrollResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DigitalBombEnrollResp) Reset()         { *m = DigitalBombEnrollResp{} }
func (m *DigitalBombEnrollResp) String() string { return proto.CompactTextString(m) }
func (*DigitalBombEnrollResp) ProtoMessage()    {}
func (*DigitalBombEnrollResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{24}
}
func (m *DigitalBombEnrollResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DigitalBombEnrollResp.Unmarshal(m, b)
}
func (m *DigitalBombEnrollResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DigitalBombEnrollResp.Marshal(b, m, deterministic)
}
func (dst *DigitalBombEnrollResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DigitalBombEnrollResp.Merge(dst, src)
}
func (m *DigitalBombEnrollResp) XXX_Size() int {
	return xxx_messageInfo_DigitalBombEnrollResp.Size(m)
}
func (m *DigitalBombEnrollResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DigitalBombEnrollResp.DiscardUnknown(m)
}

var xxx_messageInfo_DigitalBombEnrollResp proto.InternalMessageInfo

func (m *DigitalBombEnrollResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 选择参与用户
type DigitalBombSelectGameUserReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32       `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Op                   uint32       `protobuf:"varint,4,opt,name=op,proto3" json:"op,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DigitalBombSelectGameUserReq) Reset()         { *m = DigitalBombSelectGameUserReq{} }
func (m *DigitalBombSelectGameUserReq) String() string { return proto.CompactTextString(m) }
func (*DigitalBombSelectGameUserReq) ProtoMessage()    {}
func (*DigitalBombSelectGameUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{25}
}
func (m *DigitalBombSelectGameUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DigitalBombSelectGameUserReq.Unmarshal(m, b)
}
func (m *DigitalBombSelectGameUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DigitalBombSelectGameUserReq.Marshal(b, m, deterministic)
}
func (dst *DigitalBombSelectGameUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DigitalBombSelectGameUserReq.Merge(dst, src)
}
func (m *DigitalBombSelectGameUserReq) XXX_Size() int {
	return xxx_messageInfo_DigitalBombSelectGameUserReq.Size(m)
}
func (m *DigitalBombSelectGameUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DigitalBombSelectGameUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_DigitalBombSelectGameUserReq proto.InternalMessageInfo

func (m *DigitalBombSelectGameUserReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DigitalBombSelectGameUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DigitalBombSelectGameUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DigitalBombSelectGameUserReq) GetOp() uint32 {
	if m != nil {
		return m.Op
	}
	return 0
}

type DigitalBombSelectGameUserResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DigitalBombSelectGameUserResp) Reset()         { *m = DigitalBombSelectGameUserResp{} }
func (m *DigitalBombSelectGameUserResp) String() string { return proto.CompactTextString(m) }
func (*DigitalBombSelectGameUserResp) ProtoMessage()    {}
func (*DigitalBombSelectGameUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{26}
}
func (m *DigitalBombSelectGameUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DigitalBombSelectGameUserResp.Unmarshal(m, b)
}
func (m *DigitalBombSelectGameUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DigitalBombSelectGameUserResp.Marshal(b, m, deterministic)
}
func (dst *DigitalBombSelectGameUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DigitalBombSelectGameUserResp.Merge(dst, src)
}
func (m *DigitalBombSelectGameUserResp) XXX_Size() int {
	return xxx_messageInfo_DigitalBombSelectGameUserResp.Size(m)
}
func (m *DigitalBombSelectGameUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DigitalBombSelectGameUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_DigitalBombSelectGameUserResp proto.InternalMessageInfo

func (m *DigitalBombSelectGameUserResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type DigitalBombUserInfo struct {
	UserInfo             *app.UserProfile `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	IsParticipate        bool             `protobuf:"varint,2,opt,name=is_participate,json=isParticipate,proto3" json:"is_participate,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *DigitalBombUserInfo) Reset()         { *m = DigitalBombUserInfo{} }
func (m *DigitalBombUserInfo) String() string { return proto.CompactTextString(m) }
func (*DigitalBombUserInfo) ProtoMessage()    {}
func (*DigitalBombUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{27}
}
func (m *DigitalBombUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DigitalBombUserInfo.Unmarshal(m, b)
}
func (m *DigitalBombUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DigitalBombUserInfo.Marshal(b, m, deterministic)
}
func (dst *DigitalBombUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DigitalBombUserInfo.Merge(dst, src)
}
func (m *DigitalBombUserInfo) XXX_Size() int {
	return xxx_messageInfo_DigitalBombUserInfo.Size(m)
}
func (m *DigitalBombUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DigitalBombUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DigitalBombUserInfo proto.InternalMessageInfo

func (m *DigitalBombUserInfo) GetUserInfo() *app.UserProfile {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *DigitalBombUserInfo) GetIsParticipate() bool {
	if m != nil {
		return m.IsParticipate
	}
	return false
}

type DigitalBombGameInfo struct {
	PlayId               uint32                 `protobuf:"varint,1,opt,name=play_id,json=playId,proto3" json:"play_id,omitempty"`
	UserProfile          *app.UserProfile       `protobuf:"bytes,3,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	Phase                uint32                 `protobuf:"varint,4,opt,name=phase,proto3" json:"phase,omitempty"`
	UserInfo             []*DigitalBombUserInfo `protobuf:"bytes,5,rep,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	ParticipateLimit     uint32                 `protobuf:"varint,6,opt,name=participate_limit,json=participateLimit,proto3" json:"participate_limit,omitempty"`
	Selected             uint32                 `protobuf:"varint,7,opt,name=selected,proto3" json:"selected,omitempty"`
	SelectRangeStart     uint32                 `protobuf:"varint,8,opt,name=select_range_start,json=selectRangeStart,proto3" json:"select_range_start,omitempty"`
	SelectRangeEnd       uint32                 `protobuf:"varint,9,opt,name=select_range_end,json=selectRangeEnd,proto3" json:"select_range_end,omitempty"`
	TotalRangeStart      uint32                 `protobuf:"varint,10,opt,name=total_range_start,json=totalRangeStart,proto3" json:"total_range_start,omitempty"`
	TotalRangeEnd        uint32                 `protobuf:"varint,11,opt,name=total_range_end,json=totalRangeEnd,proto3" json:"total_range_end,omitempty"`
	SelectEndTime        uint64                 `protobuf:"varint,12,opt,name=select_end_time,json=selectEndTime,proto3" json:"select_end_time,omitempty"`
	ServerTime           uint64                 `protobuf:"varint,13,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	TotalSelectTime      uint32                 `protobuf:"varint,14,opt,name=total_select_time,json=totalSelectTime,proto3" json:"total_select_time,omitempty"`
	EnrollLimit          uint32                 `protobuf:"varint,15,opt,name=enroll_limit,json=enrollLimit,proto3" json:"enroll_limit,omitempty"`
	EnrollType           uint32                 `protobuf:"varint,16,opt,name=enroll_type,json=enrollType,proto3" json:"enroll_type,omitempty"`
	TimeOutEnd           string                 `protobuf:"bytes,17,opt,name=time_out_end,json=timeOutEnd,proto3" json:"time_out_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *DigitalBombGameInfo) Reset()         { *m = DigitalBombGameInfo{} }
func (m *DigitalBombGameInfo) String() string { return proto.CompactTextString(m) }
func (*DigitalBombGameInfo) ProtoMessage()    {}
func (*DigitalBombGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{28}
}
func (m *DigitalBombGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DigitalBombGameInfo.Unmarshal(m, b)
}
func (m *DigitalBombGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DigitalBombGameInfo.Marshal(b, m, deterministic)
}
func (dst *DigitalBombGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DigitalBombGameInfo.Merge(dst, src)
}
func (m *DigitalBombGameInfo) XXX_Size() int {
	return xxx_messageInfo_DigitalBombGameInfo.Size(m)
}
func (m *DigitalBombGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DigitalBombGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DigitalBombGameInfo proto.InternalMessageInfo

func (m *DigitalBombGameInfo) GetPlayId() uint32 {
	if m != nil {
		return m.PlayId
	}
	return 0
}

func (m *DigitalBombGameInfo) GetUserProfile() *app.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *DigitalBombGameInfo) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *DigitalBombGameInfo) GetUserInfo() []*DigitalBombUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *DigitalBombGameInfo) GetParticipateLimit() uint32 {
	if m != nil {
		return m.ParticipateLimit
	}
	return 0
}

func (m *DigitalBombGameInfo) GetSelected() uint32 {
	if m != nil {
		return m.Selected
	}
	return 0
}

func (m *DigitalBombGameInfo) GetSelectRangeStart() uint32 {
	if m != nil {
		return m.SelectRangeStart
	}
	return 0
}

func (m *DigitalBombGameInfo) GetSelectRangeEnd() uint32 {
	if m != nil {
		return m.SelectRangeEnd
	}
	return 0
}

func (m *DigitalBombGameInfo) GetTotalRangeStart() uint32 {
	if m != nil {
		return m.TotalRangeStart
	}
	return 0
}

func (m *DigitalBombGameInfo) GetTotalRangeEnd() uint32 {
	if m != nil {
		return m.TotalRangeEnd
	}
	return 0
}

func (m *DigitalBombGameInfo) GetSelectEndTime() uint64 {
	if m != nil {
		return m.SelectEndTime
	}
	return 0
}

func (m *DigitalBombGameInfo) GetServerTime() uint64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *DigitalBombGameInfo) GetTotalSelectTime() uint32 {
	if m != nil {
		return m.TotalSelectTime
	}
	return 0
}

func (m *DigitalBombGameInfo) GetEnrollLimit() uint32 {
	if m != nil {
		return m.EnrollLimit
	}
	return 0
}

func (m *DigitalBombGameInfo) GetEnrollType() uint32 {
	if m != nil {
		return m.EnrollType
	}
	return 0
}

func (m *DigitalBombGameInfo) GetTimeOutEnd() string {
	if m != nil {
		return m.TimeOutEnd
	}
	return ""
}

// 选择数字
type DigitalBombSelectNumberReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SelectNum            uint32       `protobuf:"varint,3,opt,name=select_num,json=selectNum,proto3" json:"select_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DigitalBombSelectNumberReq) Reset()         { *m = DigitalBombSelectNumberReq{} }
func (m *DigitalBombSelectNumberReq) String() string { return proto.CompactTextString(m) }
func (*DigitalBombSelectNumberReq) ProtoMessage()    {}
func (*DigitalBombSelectNumberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{29}
}
func (m *DigitalBombSelectNumberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DigitalBombSelectNumberReq.Unmarshal(m, b)
}
func (m *DigitalBombSelectNumberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DigitalBombSelectNumberReq.Marshal(b, m, deterministic)
}
func (dst *DigitalBombSelectNumberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DigitalBombSelectNumberReq.Merge(dst, src)
}
func (m *DigitalBombSelectNumberReq) XXX_Size() int {
	return xxx_messageInfo_DigitalBombSelectNumberReq.Size(m)
}
func (m *DigitalBombSelectNumberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DigitalBombSelectNumberReq.DiscardUnknown(m)
}

var xxx_messageInfo_DigitalBombSelectNumberReq proto.InternalMessageInfo

func (m *DigitalBombSelectNumberReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DigitalBombSelectNumberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DigitalBombSelectNumberReq) GetSelectNum() uint32 {
	if m != nil {
		return m.SelectNum
	}
	return 0
}

type DigitalBombSelectNumberResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DigitalBombSelectNumberResp) Reset()         { *m = DigitalBombSelectNumberResp{} }
func (m *DigitalBombSelectNumberResp) String() string { return proto.CompactTextString(m) }
func (*DigitalBombSelectNumberResp) ProtoMessage()    {}
func (*DigitalBombSelectNumberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{30}
}
func (m *DigitalBombSelectNumberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DigitalBombSelectNumberResp.Unmarshal(m, b)
}
func (m *DigitalBombSelectNumberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DigitalBombSelectNumberResp.Marshal(b, m, deterministic)
}
func (dst *DigitalBombSelectNumberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DigitalBombSelectNumberResp.Merge(dst, src)
}
func (m *DigitalBombSelectNumberResp) XXX_Size() int {
	return xxx_messageInfo_DigitalBombSelectNumberResp.Size(m)
}
func (m *DigitalBombSelectNumberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DigitalBombSelectNumberResp.DiscardUnknown(m)
}

var xxx_messageInfo_DigitalBombSelectNumberResp proto.InternalMessageInfo

func (m *DigitalBombSelectNumberResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 游戏消息变更
type DigitalBombGameInfoChange struct {
	GameInfo             *DigitalBombGameInfo `protobuf:"bytes,1,opt,name=game_info,json=gameInfo,proto3" json:"game_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *DigitalBombGameInfoChange) Reset()         { *m = DigitalBombGameInfoChange{} }
func (m *DigitalBombGameInfoChange) String() string { return proto.CompactTextString(m) }
func (*DigitalBombGameInfoChange) ProtoMessage()    {}
func (*DigitalBombGameInfoChange) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{31}
}
func (m *DigitalBombGameInfoChange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DigitalBombGameInfoChange.Unmarshal(m, b)
}
func (m *DigitalBombGameInfoChange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DigitalBombGameInfoChange.Marshal(b, m, deterministic)
}
func (dst *DigitalBombGameInfoChange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DigitalBombGameInfoChange.Merge(dst, src)
}
func (m *DigitalBombGameInfoChange) XXX_Size() int {
	return xxx_messageInfo_DigitalBombGameInfoChange.Size(m)
}
func (m *DigitalBombGameInfoChange) XXX_DiscardUnknown() {
	xxx_messageInfo_DigitalBombGameInfoChange.DiscardUnknown(m)
}

var xxx_messageInfo_DigitalBombGameInfoChange proto.InternalMessageInfo

func (m *DigitalBombGameInfoChange) GetGameInfo() *DigitalBombGameInfo {
	if m != nil {
		return m.GameInfo
	}
	return nil
}

// 报名/取消报名
type AdventureEnrollReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Op                   uint32       `protobuf:"varint,3,opt,name=op,proto3" json:"op,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AdventureEnrollReq) Reset()         { *m = AdventureEnrollReq{} }
func (m *AdventureEnrollReq) String() string { return proto.CompactTextString(m) }
func (*AdventureEnrollReq) ProtoMessage()    {}
func (*AdventureEnrollReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{32}
}
func (m *AdventureEnrollReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdventureEnrollReq.Unmarshal(m, b)
}
func (m *AdventureEnrollReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdventureEnrollReq.Marshal(b, m, deterministic)
}
func (dst *AdventureEnrollReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdventureEnrollReq.Merge(dst, src)
}
func (m *AdventureEnrollReq) XXX_Size() int {
	return xxx_messageInfo_AdventureEnrollReq.Size(m)
}
func (m *AdventureEnrollReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AdventureEnrollReq.DiscardUnknown(m)
}

var xxx_messageInfo_AdventureEnrollReq proto.InternalMessageInfo

func (m *AdventureEnrollReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AdventureEnrollReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AdventureEnrollReq) GetOp() uint32 {
	if m != nil {
		return m.Op
	}
	return 0
}

type AdventureEnrollResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AdventureEnrollResp) Reset()         { *m = AdventureEnrollResp{} }
func (m *AdventureEnrollResp) String() string { return proto.CompactTextString(m) }
func (*AdventureEnrollResp) ProtoMessage()    {}
func (*AdventureEnrollResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{33}
}
func (m *AdventureEnrollResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdventureEnrollResp.Unmarshal(m, b)
}
func (m *AdventureEnrollResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdventureEnrollResp.Marshal(b, m, deterministic)
}
func (dst *AdventureEnrollResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdventureEnrollResp.Merge(dst, src)
}
func (m *AdventureEnrollResp) XXX_Size() int {
	return xxx_messageInfo_AdventureEnrollResp.Size(m)
}
func (m *AdventureEnrollResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AdventureEnrollResp.DiscardUnknown(m)
}

var xxx_messageInfo_AdventureEnrollResp proto.InternalMessageInfo

func (m *AdventureEnrollResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 选择参与用户
type AdventureSelectGameUserReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32       `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Op                   uint32       `protobuf:"varint,4,opt,name=op,proto3" json:"op,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AdventureSelectGameUserReq) Reset()         { *m = AdventureSelectGameUserReq{} }
func (m *AdventureSelectGameUserReq) String() string { return proto.CompactTextString(m) }
func (*AdventureSelectGameUserReq) ProtoMessage()    {}
func (*AdventureSelectGameUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{34}
}
func (m *AdventureSelectGameUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdventureSelectGameUserReq.Unmarshal(m, b)
}
func (m *AdventureSelectGameUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdventureSelectGameUserReq.Marshal(b, m, deterministic)
}
func (dst *AdventureSelectGameUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdventureSelectGameUserReq.Merge(dst, src)
}
func (m *AdventureSelectGameUserReq) XXX_Size() int {
	return xxx_messageInfo_AdventureSelectGameUserReq.Size(m)
}
func (m *AdventureSelectGameUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AdventureSelectGameUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_AdventureSelectGameUserReq proto.InternalMessageInfo

func (m *AdventureSelectGameUserReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AdventureSelectGameUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AdventureSelectGameUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AdventureSelectGameUserReq) GetOp() uint32 {
	if m != nil {
		return m.Op
	}
	return 0
}

type AdventureSelectGameUserResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AdventureSelectGameUserResp) Reset()         { *m = AdventureSelectGameUserResp{} }
func (m *AdventureSelectGameUserResp) String() string { return proto.CompactTextString(m) }
func (*AdventureSelectGameUserResp) ProtoMessage()    {}
func (*AdventureSelectGameUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{35}
}
func (m *AdventureSelectGameUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdventureSelectGameUserResp.Unmarshal(m, b)
}
func (m *AdventureSelectGameUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdventureSelectGameUserResp.Marshal(b, m, deterministic)
}
func (dst *AdventureSelectGameUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdventureSelectGameUserResp.Merge(dst, src)
}
func (m *AdventureSelectGameUserResp) XXX_Size() int {
	return xxx_messageInfo_AdventureSelectGameUserResp.Size(m)
}
func (m *AdventureSelectGameUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AdventureSelectGameUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_AdventureSelectGameUserResp proto.InternalMessageInfo

func (m *AdventureSelectGameUserResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 控制下一步操作
type AdventureControlNextReq struct {
	BaseReq              *app.BaseReq                   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                         `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Op                   AdventureControlNextReq_NextOp `protobuf:"varint,3,opt,name=op,proto3,enum=ga.pgc_channel_game_logic.AdventureControlNextReq_NextOp" json:"op,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *AdventureControlNextReq) Reset()         { *m = AdventureControlNextReq{} }
func (m *AdventureControlNextReq) String() string { return proto.CompactTextString(m) }
func (*AdventureControlNextReq) ProtoMessage()    {}
func (*AdventureControlNextReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{36}
}
func (m *AdventureControlNextReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdventureControlNextReq.Unmarshal(m, b)
}
func (m *AdventureControlNextReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdventureControlNextReq.Marshal(b, m, deterministic)
}
func (dst *AdventureControlNextReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdventureControlNextReq.Merge(dst, src)
}
func (m *AdventureControlNextReq) XXX_Size() int {
	return xxx_messageInfo_AdventureControlNextReq.Size(m)
}
func (m *AdventureControlNextReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AdventureControlNextReq.DiscardUnknown(m)
}

var xxx_messageInfo_AdventureControlNextReq proto.InternalMessageInfo

func (m *AdventureControlNextReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AdventureControlNextReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AdventureControlNextReq) GetOp() AdventureControlNextReq_NextOp {
	if m != nil {
		return m.Op
	}
	return AdventureControlNextReq_UNKNOWN
}

type AdventureControlNextResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AdventureControlNextResp) Reset()         { *m = AdventureControlNextResp{} }
func (m *AdventureControlNextResp) String() string { return proto.CompactTextString(m) }
func (*AdventureControlNextResp) ProtoMessage()    {}
func (*AdventureControlNextResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{37}
}
func (m *AdventureControlNextResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdventureControlNextResp.Unmarshal(m, b)
}
func (m *AdventureControlNextResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdventureControlNextResp.Marshal(b, m, deterministic)
}
func (dst *AdventureControlNextResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdventureControlNextResp.Merge(dst, src)
}
func (m *AdventureControlNextResp) XXX_Size() int {
	return xxx_messageInfo_AdventureControlNextResp.Size(m)
}
func (m *AdventureControlNextResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AdventureControlNextResp.DiscardUnknown(m)
}

var xxx_messageInfo_AdventureControlNextResp proto.InternalMessageInfo

func (m *AdventureControlNextResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GameUserInfo struct {
	UserInfo             *app.UserProfile `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	Flag                 uint32           `protobuf:"varint,2,opt,name=flag,proto3" json:"flag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GameUserInfo) Reset()         { *m = GameUserInfo{} }
func (m *GameUserInfo) String() string { return proto.CompactTextString(m) }
func (*GameUserInfo) ProtoMessage()    {}
func (*GameUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{38}
}
func (m *GameUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUserInfo.Unmarshal(m, b)
}
func (m *GameUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUserInfo.Marshal(b, m, deterministic)
}
func (dst *GameUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUserInfo.Merge(dst, src)
}
func (m *GameUserInfo) XXX_Size() int {
	return xxx_messageInfo_GameUserInfo.Size(m)
}
func (m *GameUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameUserInfo proto.InternalMessageInfo

func (m *GameUserInfo) GetUserInfo() *app.UserProfile {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *GameUserInfo) GetFlag() uint32 {
	if m != nil {
		return m.Flag
	}
	return 0
}

type AdventureCellConf struct {
	CellId               uint32   `protobuf:"varint,1,opt,name=cell_id,json=cellId,proto3" json:"cell_id,omitempty"`
	AdventureDesc        string   `protobuf:"bytes,2,opt,name=adventure_desc,json=adventureDesc,proto3" json:"adventure_desc,omitempty"`
	CellSpecialAttr      uint32   `protobuf:"varint,3,opt,name=cell_special_attr,json=cellSpecialAttr,proto3" json:"cell_special_attr,omitempty"`
	CellIconUrl          string   `protobuf:"bytes,4,opt,name=cell_icon_url,json=cellIconUrl,proto3" json:"cell_icon_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AdventureCellConf) Reset()         { *m = AdventureCellConf{} }
func (m *AdventureCellConf) String() string { return proto.CompactTextString(m) }
func (*AdventureCellConf) ProtoMessage()    {}
func (*AdventureCellConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{39}
}
func (m *AdventureCellConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdventureCellConf.Unmarshal(m, b)
}
func (m *AdventureCellConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdventureCellConf.Marshal(b, m, deterministic)
}
func (dst *AdventureCellConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdventureCellConf.Merge(dst, src)
}
func (m *AdventureCellConf) XXX_Size() int {
	return xxx_messageInfo_AdventureCellConf.Size(m)
}
func (m *AdventureCellConf) XXX_DiscardUnknown() {
	xxx_messageInfo_AdventureCellConf.DiscardUnknown(m)
}

var xxx_messageInfo_AdventureCellConf proto.InternalMessageInfo

func (m *AdventureCellConf) GetCellId() uint32 {
	if m != nil {
		return m.CellId
	}
	return 0
}

func (m *AdventureCellConf) GetAdventureDesc() string {
	if m != nil {
		return m.AdventureDesc
	}
	return ""
}

func (m *AdventureCellConf) GetCellSpecialAttr() uint32 {
	if m != nil {
		return m.CellSpecialAttr
	}
	return 0
}

func (m *AdventureCellConf) GetCellIconUrl() string {
	if m != nil {
		return m.CellIconUrl
	}
	return ""
}

type AdventureGameInfo struct {
	PlayId               uint32               `protobuf:"varint,1,opt,name=play_id,json=playId,proto3" json:"play_id,omitempty"`
	Phase                uint32               `protobuf:"varint,2,opt,name=phase,proto3" json:"phase,omitempty"`
	PhaseEndTs           int64                `protobuf:"varint,3,opt,name=phase_end_ts,json=phaseEndTs,proto3" json:"phase_end_ts,omitempty"`
	UserList             []*GameUserInfo      `protobuf:"bytes,4,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	CurrCellId           uint32               `protobuf:"varint,5,opt,name=curr_cell_id,json=currCellId,proto3" json:"curr_cell_id,omitempty"`
	CellList             []*AdventureCellConf `protobuf:"bytes,6,rep,name=cell_list,json=cellList,proto3" json:"cell_list,omitempty"`
	JoinUserMinCnt       uint32               `protobuf:"varint,7,opt,name=join_user_min_cnt,json=joinUserMinCnt,proto3" json:"join_user_min_cnt,omitempty"`
	JoinUserMaxCnt       uint32               `protobuf:"varint,8,opt,name=join_user_max_cnt,json=joinUserMaxCnt,proto3" json:"join_user_max_cnt,omitempty"`
	EnrollType           uint32               `protobuf:"varint,9,opt,name=enroll_type,json=enrollType,proto3" json:"enroll_type,omitempty"`
	ServerTimeMs         int64                `protobuf:"varint,10,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	SettleInfo           *AdventureSettleInfo `protobuf:"bytes,11,opt,name=settle_info,json=settleInfo,proto3" json:"settle_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AdventureGameInfo) Reset()         { *m = AdventureGameInfo{} }
func (m *AdventureGameInfo) String() string { return proto.CompactTextString(m) }
func (*AdventureGameInfo) ProtoMessage()    {}
func (*AdventureGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{40}
}
func (m *AdventureGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdventureGameInfo.Unmarshal(m, b)
}
func (m *AdventureGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdventureGameInfo.Marshal(b, m, deterministic)
}
func (dst *AdventureGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdventureGameInfo.Merge(dst, src)
}
func (m *AdventureGameInfo) XXX_Size() int {
	return xxx_messageInfo_AdventureGameInfo.Size(m)
}
func (m *AdventureGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AdventureGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AdventureGameInfo proto.InternalMessageInfo

func (m *AdventureGameInfo) GetPlayId() uint32 {
	if m != nil {
		return m.PlayId
	}
	return 0
}

func (m *AdventureGameInfo) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *AdventureGameInfo) GetPhaseEndTs() int64 {
	if m != nil {
		return m.PhaseEndTs
	}
	return 0
}

func (m *AdventureGameInfo) GetUserList() []*GameUserInfo {
	if m != nil {
		return m.UserList
	}
	return nil
}

func (m *AdventureGameInfo) GetCurrCellId() uint32 {
	if m != nil {
		return m.CurrCellId
	}
	return 0
}

func (m *AdventureGameInfo) GetCellList() []*AdventureCellConf {
	if m != nil {
		return m.CellList
	}
	return nil
}

func (m *AdventureGameInfo) GetJoinUserMinCnt() uint32 {
	if m != nil {
		return m.JoinUserMinCnt
	}
	return 0
}

func (m *AdventureGameInfo) GetJoinUserMaxCnt() uint32 {
	if m != nil {
		return m.JoinUserMaxCnt
	}
	return 0
}

func (m *AdventureGameInfo) GetEnrollType() uint32 {
	if m != nil {
		return m.EnrollType
	}
	return 0
}

func (m *AdventureGameInfo) GetServerTimeMs() int64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *AdventureGameInfo) GetSettleInfo() *AdventureSettleInfo {
	if m != nil {
		return m.SettleInfo
	}
	return nil
}

type AdventureSettleInfo struct {
	SettleType           uint32           `protobuf:"varint,1,opt,name=settle_type,json=settleType,proto3" json:"settle_type,omitempty"`
	UserInfo             *app.UserProfile `protobuf:"bytes,2,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AdventureSettleInfo) Reset()         { *m = AdventureSettleInfo{} }
func (m *AdventureSettleInfo) String() string { return proto.CompactTextString(m) }
func (*AdventureSettleInfo) ProtoMessage()    {}
func (*AdventureSettleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{41}
}
func (m *AdventureSettleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdventureSettleInfo.Unmarshal(m, b)
}
func (m *AdventureSettleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdventureSettleInfo.Marshal(b, m, deterministic)
}
func (dst *AdventureSettleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdventureSettleInfo.Merge(dst, src)
}
func (m *AdventureSettleInfo) XXX_Size() int {
	return xxx_messageInfo_AdventureSettleInfo.Size(m)
}
func (m *AdventureSettleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AdventureSettleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AdventureSettleInfo proto.InternalMessageInfo

func (m *AdventureSettleInfo) GetSettleType() uint32 {
	if m != nil {
		return m.SettleType
	}
	return 0
}

func (m *AdventureSettleInfo) GetUserInfo() *app.UserProfile {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

type AdventureSettleSource struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	SourceJson           string   `protobuf:"bytes,3,opt,name=source_json,json=sourceJson,proto3" json:"source_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AdventureSettleSource) Reset()         { *m = AdventureSettleSource{} }
func (m *AdventureSettleSource) String() string { return proto.CompactTextString(m) }
func (*AdventureSettleSource) ProtoMessage()    {}
func (*AdventureSettleSource) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{42}
}
func (m *AdventureSettleSource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdventureSettleSource.Unmarshal(m, b)
}
func (m *AdventureSettleSource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdventureSettleSource.Marshal(b, m, deterministic)
}
func (dst *AdventureSettleSource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdventureSettleSource.Merge(dst, src)
}
func (m *AdventureSettleSource) XXX_Size() int {
	return xxx_messageInfo_AdventureSettleSource.Size(m)
}
func (m *AdventureSettleSource) XXX_DiscardUnknown() {
	xxx_messageInfo_AdventureSettleSource.DiscardUnknown(m)
}

var xxx_messageInfo_AdventureSettleSource proto.InternalMessageInfo

func (m *AdventureSettleSource) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *AdventureSettleSource) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *AdventureSettleSource) GetSourceJson() string {
	if m != nil {
		return m.SourceJson
	}
	return ""
}

// 大冒险变更推送opt
type AdventureInfoChangeOpt struct {
	Info                 *AdventureGameInfo     `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	RandomStepNumber     uint32                 `protobuf:"varint,2,opt,name=random_step_number,json=randomStepNumber,proto3" json:"random_step_number,omitempty"`
	ChangeToast          string                 `protobuf:"bytes,3,opt,name=change_toast,json=changeToast,proto3" json:"change_toast,omitempty"`
	SettleSource         *AdventureSettleSource `protobuf:"bytes,4,opt,name=settle_source,json=settleSource,proto3" json:"settle_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AdventureInfoChangeOpt) Reset()         { *m = AdventureInfoChangeOpt{} }
func (m *AdventureInfoChangeOpt) String() string { return proto.CompactTextString(m) }
func (*AdventureInfoChangeOpt) ProtoMessage()    {}
func (*AdventureInfoChangeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{43}
}
func (m *AdventureInfoChangeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdventureInfoChangeOpt.Unmarshal(m, b)
}
func (m *AdventureInfoChangeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdventureInfoChangeOpt.Marshal(b, m, deterministic)
}
func (dst *AdventureInfoChangeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdventureInfoChangeOpt.Merge(dst, src)
}
func (m *AdventureInfoChangeOpt) XXX_Size() int {
	return xxx_messageInfo_AdventureInfoChangeOpt.Size(m)
}
func (m *AdventureInfoChangeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_AdventureInfoChangeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_AdventureInfoChangeOpt proto.InternalMessageInfo

func (m *AdventureInfoChangeOpt) GetInfo() *AdventureGameInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *AdventureInfoChangeOpt) GetRandomStepNumber() uint32 {
	if m != nil {
		return m.RandomStepNumber
	}
	return 0
}

func (m *AdventureInfoChangeOpt) GetChangeToast() string {
	if m != nil {
		return m.ChangeToast
	}
	return ""
}

func (m *AdventureInfoChangeOpt) GetSettleSource() *AdventureSettleSource {
	if m != nil {
		return m.SettleSource
	}
	return nil
}

// 大冒险摇步数
type AdventureRandomStepsReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AdventureRandomStepsReq) Reset()         { *m = AdventureRandomStepsReq{} }
func (m *AdventureRandomStepsReq) String() string { return proto.CompactTextString(m) }
func (*AdventureRandomStepsReq) ProtoMessage()    {}
func (*AdventureRandomStepsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{44}
}
func (m *AdventureRandomStepsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdventureRandomStepsReq.Unmarshal(m, b)
}
func (m *AdventureRandomStepsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdventureRandomStepsReq.Marshal(b, m, deterministic)
}
func (dst *AdventureRandomStepsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdventureRandomStepsReq.Merge(dst, src)
}
func (m *AdventureRandomStepsReq) XXX_Size() int {
	return xxx_messageInfo_AdventureRandomStepsReq.Size(m)
}
func (m *AdventureRandomStepsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AdventureRandomStepsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AdventureRandomStepsReq proto.InternalMessageInfo

func (m *AdventureRandomStepsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AdventureRandomStepsReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type AdventureRandomStepsResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AdventureRandomStepsResp) Reset()         { *m = AdventureRandomStepsResp{} }
func (m *AdventureRandomStepsResp) String() string { return proto.CompactTextString(m) }
func (*AdventureRandomStepsResp) ProtoMessage()    {}
func (*AdventureRandomStepsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6, []int{45}
}
func (m *AdventureRandomStepsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdventureRandomStepsResp.Unmarshal(m, b)
}
func (m *AdventureRandomStepsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdventureRandomStepsResp.Marshal(b, m, deterministic)
}
func (dst *AdventureRandomStepsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdventureRandomStepsResp.Merge(dst, src)
}
func (m *AdventureRandomStepsResp) XXX_Size() int {
	return xxx_messageInfo_AdventureRandomStepsResp.Size(m)
}
func (m *AdventureRandomStepsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AdventureRandomStepsResp.DiscardUnknown(m)
}

var xxx_messageInfo_AdventureRandomStepsResp proto.InternalMessageInfo

func (m *AdventureRandomStepsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*GameOperateButton)(nil), "ga.pgc_channel_game_logic.GameOperateButton")
	proto.RegisterType((*GameOperateInfo)(nil), "ga.pgc_channel_game_logic.GameOperateInfo")
	proto.RegisterType((*ChoseOperate)(nil), "ga.pgc_channel_game_logic.ChoseOperate")
	proto.RegisterType((*GameSummary)(nil), "ga.pgc_channel_game_logic.GameSummary")
	proto.RegisterType((*GetGameListReq)(nil), "ga.pgc_channel_game_logic.GetGameListReq")
	proto.RegisterType((*GetGameListResp)(nil), "ga.pgc_channel_game_logic.GetGameListResp")
	proto.RegisterType((*SetGamePhaseReq)(nil), "ga.pgc_channel_game_logic.SetGamePhaseReq")
	proto.RegisterType((*SetGamePhaseResp)(nil), "ga.pgc_channel_game_logic.SetGamePhaseResp")
	proto.RegisterType((*GamePhaseChangeNotifyOpt)(nil), "ga.pgc_channel_game_logic.GamePhaseChangeNotifyOpt")
	proto.RegisterType((*SetNextBombUserReq)(nil), "ga.pgc_channel_game_logic.SetNextBombUserReq")
	proto.RegisterType((*SetNextBombUserResp)(nil), "ga.pgc_channel_game_logic.SetNextBombUserResp")
	proto.RegisterType((*GameInfoSet)(nil), "ga.pgc_channel_game_logic.GameInfoSet")
	proto.RegisterType((*GetChannelGameInfoReq)(nil), "ga.pgc_channel_game_logic.GetChannelGameInfoReq")
	proto.RegisterType((*GetChannelGameInfoResp)(nil), "ga.pgc_channel_game_logic.GetChannelGameInfoResp")
	proto.RegisterType((*GameThrowBombInfo)(nil), "ga.pgc_channel_game_logic.GameThrowBombInfo")
	proto.RegisterType((*GameThrowInfo)(nil), "ga.pgc_channel_game_logic.GameThrowInfo")
	proto.RegisterType((*GameBombUserInfo)(nil), "ga.pgc_channel_game_logic.GameBombUserInfo")
	proto.RegisterType((*GameBombUserNotifyOPt)(nil), "ga.pgc_channel_game_logic.GameBombUserNotifyOPt")
	proto.RegisterType((*GameBombPunishInfo)(nil), "ga.pgc_channel_game_logic.GameBombPunishInfo")
	proto.RegisterType((*GameBombPunishNotifyOPt)(nil), "ga.pgc_channel_game_logic.GameBombPunishNotifyOPt")
	proto.RegisterType((*GameThrowUserNotifyOPt)(nil), "ga.pgc_channel_game_logic.GameThrowUserNotifyOPt")
	proto.RegisterType((*GameRescueNotifyOPt)(nil), "ga.pgc_channel_game_logic.GameRescueNotifyOPt")
	proto.RegisterType((*GameBombResultOpt)(nil), "ga.pgc_channel_game_logic.GameBombResultOpt")
	proto.RegisterType((*DigitalBombEnrollReq)(nil), "ga.pgc_channel_game_logic.DigitalBombEnrollReq")
	proto.RegisterType((*DigitalBombEnrollResp)(nil), "ga.pgc_channel_game_logic.DigitalBombEnrollResp")
	proto.RegisterType((*DigitalBombSelectGameUserReq)(nil), "ga.pgc_channel_game_logic.DigitalBombSelectGameUserReq")
	proto.RegisterType((*DigitalBombSelectGameUserResp)(nil), "ga.pgc_channel_game_logic.DigitalBombSelectGameUserResp")
	proto.RegisterType((*DigitalBombUserInfo)(nil), "ga.pgc_channel_game_logic.DigitalBombUserInfo")
	proto.RegisterType((*DigitalBombGameInfo)(nil), "ga.pgc_channel_game_logic.DigitalBombGameInfo")
	proto.RegisterType((*DigitalBombSelectNumberReq)(nil), "ga.pgc_channel_game_logic.DigitalBombSelectNumberReq")
	proto.RegisterType((*DigitalBombSelectNumberResp)(nil), "ga.pgc_channel_game_logic.DigitalBombSelectNumberResp")
	proto.RegisterType((*DigitalBombGameInfoChange)(nil), "ga.pgc_channel_game_logic.DigitalBombGameInfoChange")
	proto.RegisterType((*AdventureEnrollReq)(nil), "ga.pgc_channel_game_logic.AdventureEnrollReq")
	proto.RegisterType((*AdventureEnrollResp)(nil), "ga.pgc_channel_game_logic.AdventureEnrollResp")
	proto.RegisterType((*AdventureSelectGameUserReq)(nil), "ga.pgc_channel_game_logic.AdventureSelectGameUserReq")
	proto.RegisterType((*AdventureSelectGameUserResp)(nil), "ga.pgc_channel_game_logic.AdventureSelectGameUserResp")
	proto.RegisterType((*AdventureControlNextReq)(nil), "ga.pgc_channel_game_logic.AdventureControlNextReq")
	proto.RegisterType((*AdventureControlNextResp)(nil), "ga.pgc_channel_game_logic.AdventureControlNextResp")
	proto.RegisterType((*GameUserInfo)(nil), "ga.pgc_channel_game_logic.GameUserInfo")
	proto.RegisterType((*AdventureCellConf)(nil), "ga.pgc_channel_game_logic.AdventureCellConf")
	proto.RegisterType((*AdventureGameInfo)(nil), "ga.pgc_channel_game_logic.AdventureGameInfo")
	proto.RegisterType((*AdventureSettleInfo)(nil), "ga.pgc_channel_game_logic.AdventureSettleInfo")
	proto.RegisterType((*AdventureSettleSource)(nil), "ga.pgc_channel_game_logic.AdventureSettleSource")
	proto.RegisterType((*AdventureInfoChangeOpt)(nil), "ga.pgc_channel_game_logic.AdventureInfoChangeOpt")
	proto.RegisterType((*AdventureRandomStepsReq)(nil), "ga.pgc_channel_game_logic.AdventureRandomStepsReq")
	proto.RegisterType((*AdventureRandomStepsResp)(nil), "ga.pgc_channel_game_logic.AdventureRandomStepsResp")
	proto.RegisterEnum("ga.pgc_channel_game_logic.PgcChannelGameId", PgcChannelGameId_name, PgcChannelGameId_value)
	proto.RegisterEnum("ga.pgc_channel_game_logic.GameUserType", GameUserType_name, GameUserType_value)
	proto.RegisterEnum("ga.pgc_channel_game_logic.DigitalBombPhase", DigitalBombPhase_name, DigitalBombPhase_value)
	proto.RegisterEnum("ga.pgc_channel_game_logic.AdventurePhase", AdventurePhase_name, AdventurePhase_value)
	proto.RegisterEnum("ga.pgc_channel_game_logic.EnrollFlag", EnrollFlag_name, EnrollFlag_value)
	proto.RegisterEnum("ga.pgc_channel_game_logic.GameUserFlag", GameUserFlag_name, GameUserFlag_value)
	proto.RegisterEnum("ga.pgc_channel_game_logic.GameOperateButton_ButtonType", GameOperateButton_ButtonType_name, GameOperateButton_ButtonType_value)
	proto.RegisterEnum("ga.pgc_channel_game_logic.SetGamePhaseReq_GamePhaseType", SetGamePhaseReq_GamePhaseType_name, SetGamePhaseReq_GamePhaseType_value)
	proto.RegisterEnum("ga.pgc_channel_game_logic.GamePhaseChangeNotifyOpt_GameBombType", GamePhaseChangeNotifyOpt_GameBombType_name, GamePhaseChangeNotifyOpt_GameBombType_value)
	proto.RegisterEnum("ga.pgc_channel_game_logic.GameBombResultOpt_EndType", GameBombResultOpt_EndType_name, GameBombResultOpt_EndType_value)
	proto.RegisterEnum("ga.pgc_channel_game_logic.AdventureControlNextReq_NextOp", AdventureControlNextReq_NextOp_name, AdventureControlNextReq_NextOp_value)
	proto.RegisterEnum("ga.pgc_channel_game_logic.AdventureCellConf_AdventureCellAttr", AdventureCellConf_AdventureCellAttr_name, AdventureCellConf_AdventureCellAttr_value)
	proto.RegisterEnum("ga.pgc_channel_game_logic.AdventureSettleInfo_SettleType", AdventureSettleInfo_SettleType_name, AdventureSettleInfo_SettleType_value)
}

func init() {
	proto.RegisterFile("pgc-channel-game-logic_.proto", fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6)
}

var fileDescriptor_pgc_channel_game_logic__33ca3898f82703a6 = []byte{
	// 2793 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x1a, 0x5d, 0x6f, 0xdb, 0xd6,
	0x35, 0x94, 0xfc, 0x21, 0x1d, 0x7d, 0x98, 0xa6, 0x93, 0x58, 0xb1, 0xeb, 0xc4, 0xe5, 0xb2, 0xcc,
	0x75, 0x1b, 0xb7, 0x48, 0xd1, 0x01, 0x7b, 0xd8, 0x56, 0x59, 0x62, 0x64, 0xb6, 0x12, 0xa5, 0x91,
	0x54, 0xd2, 0x16, 0xc3, 0x58, 0x9a, 0xba, 0x96, 0xd9, 0xf1, 0x2b, 0x24, 0xd5, 0xc5, 0x4f, 0x43,
	0x0b, 0x14, 0x03, 0x86, 0xbd, 0x76, 0x0f, 0x7b, 0x1e, 0x06, 0xec, 0x61, 0x3f, 0x60, 0xc0, 0xb0,
	0x97, 0xfd, 0x94, 0xfd, 0x91, 0xe1, 0x7e, 0x88, 0xbc, 0x92, 0xe5, 0xaf, 0xcc, 0xc3, 0x9e, 0xcc,
	0x7b, 0xce, 0xb9, 0xe7, 0x9c, 0x7b, 0xbe, 0xef, 0x95, 0x61, 0x27, 0x1a, 0x3b, 0x4f, 0x9d, 0x53,
	0x3b, 0x08, 0x90, 0xf7, 0x74, 0x6c, 0xfb, 0xe8, 0xa9, 0x17, 0x8e, 0x5d, 0xc7, 0x3a, 0x88, 0xe2,
	0x30, 0x0d, 0xa5, 0x07, 0x63, 0xfb, 0x20, 0x1a, 0x3b, 0x16, 0xa3, 0xb0, 0x30, 0x85, 0x45, 0x28,
	0xb6, 0x6a, 0x63, 0xdb, 0x3a, 0xb6, 0x13, 0x44, 0x29, 0xe5, 0x3f, 0x0b, 0xb0, 0xde, 0xb1, 0x7d,
	0xd4, 0x8f, 0x50, 0x6c, 0xa7, 0xe8, 0x70, 0x92, 0xa6, 0x61, 0x20, 0x6d, 0x43, 0xf9, 0x98, 0x7c,
	0x59, 0xee, 0xa8, 0x21, 0xec, 0x0a, 0x7b, 0x35, 0xbd, 0x44, 0x01, 0xea, 0x48, 0x7a, 0x04, 0x15,
	0x86, 0x4c, 0xcf, 0x22, 0xd4, 0x28, 0x10, 0x34, 0x50, 0x90, 0x79, 0x16, 0x21, 0x9e, 0x00, 0xbd,
	0x4e, 0x1b, 0xc5, 0x5d, 0x61, 0xaf, 0x9c, 0x11, 0xa0, 0xd7, 0xa9, 0xfc, 0x01, 0xc0, 0x61, 0x4e,
	0x2e, 0x41, 0xfd, 0x70, 0x68, 0x9a, 0x7d, 0xcd, 0x52, 0xb5, 0x17, 0xcd, 0xae, 0xda, 0x16, 0xef,
	0x48, 0x75, 0x00, 0x06, 0x6b, 0xf5, 0x7b, 0xa2, 0x20, 0x7f, 0x2f, 0xc0, 0x1a, 0xa7, 0xa6, 0x1a,
	0x9c, 0x84, 0xd2, 0x0e, 0x40, 0x48, 0x97, 0xb9, 0x96, 0x65, 0x06, 0x51, 0x47, 0xd2, 0x5d, 0x58,
	0x4e, 0xdd, 0xd4, 0xa3, 0x0a, 0x96, 0x75, 0xba, 0x90, 0x7a, 0x99, 0x6e, 0x9e, 0x9b, 0x60, 0xdd,
	0x8a, 0x7b, 0x95, 0x67, 0xef, 0x1d, 0x5c, 0x68, 0xaf, 0x83, 0x73, 0xc6, 0x99, 0x9e, 0xa4, 0xeb,
	0x26, 0xa9, 0xec, 0x42, 0xb5, 0x75, 0x1a, 0x26, 0x53, 0x8a, 0xab, 0x74, 0x9a, 0xb1, 0x6b, 0x61,
	0xce, 0xae, 0x3b, 0x00, 0x6e, 0x10, 0x4d, 0x52, 0xde, 0x6a, 0x65, 0x02, 0x21, 0x46, 0xfb, 0xbb,
	0x00, 0x15, 0xac, 0x8c, 0x31, 0xf1, 0x7d, 0x3b, 0x3e, 0x93, 0x36, 0x61, 0x95, 0xa8, 0x99, 0xc9,
	0x59, 0xc1, 0x4b, 0x2a, 0x84, 0x20, 0x02, 0xdb, 0x9f, 0x1e, 0xbe, 0x84, 0x01, 0x9a, 0xed, 0xa3,
	0x0c, 0xe9, 0x3a, 0x61, 0xc0, 0x64, 0x10, 0xa4, 0xea, 0x84, 0x01, 0x66, 0xe9, 0xf8, 0x89, 0x35,
	0x89, 0xbd, 0xc6, 0x12, 0x41, 0xad, 0x38, 0x7e, 0x32, 0x8c, 0x3d, 0xe9, 0x67, 0xb0, 0xe4, 0x06,
	0x27, 0x61, 0x63, 0x99, 0x98, 0x6b, 0xff, 0x7a, 0xe6, 0xc2, 0x4e, 0xd2, 0xc9, 0x3e, 0xf9, 0x25,
	0xd4, 0x3b, 0x28, 0xc5, 0x38, 0x6c, 0x35, 0x1d, 0xbd, 0x92, 0x9e, 0x40, 0x09, 0x47, 0xa1, 0x15,
	0xa3, 0x57, 0x44, 0xfd, 0xca, 0xb3, 0x0a, 0xe6, 0x7a, 0x68, 0x27, 0x48, 0x47, 0xaf, 0xf4, 0xd5,
	0x63, 0xfa, 0x81, 0x8d, 0x32, 0x95, 0x92, 0x99, 0xac, 0xcc, 0x20, 0xea, 0x48, 0xfe, 0x06, 0xc7,
	0x05, 0xcf, 0x39, 0x89, 0xa4, 0x77, 0xa0, 0xcc, 0x58, 0x27, 0x11, 0xe3, 0x5d, 0xcd, 0x79, 0x27,
	0x91, 0x5e, 0x3a, 0x66, 0x5f, 0x52, 0x8b, 0x59, 0x83, 0xc4, 0x42, 0x81, 0x1c, 0xee, 0xc9, 0x15,
	0x87, 0x63, 0xe6, 0xa7, 0x56, 0x23, 0x31, 0xf0, 0xaf, 0x02, 0xac, 0x19, 0x54, 0x87, 0xc1, 0x29,
	0x53, 0xfb, 0x76, 0x8e, 0xc7, 0xfb, 0xb8, 0x38, 0xe3, 0xe3, 0xb7, 0xa1, 0x9a, 0xda, 0xf1, 0x18,
	0xa5, 0x56, 0x84, 0x45, 0x12, 0x77, 0xd5, 0xf4, 0x0a, 0x85, 0x11, 0x2d, 0xa4, 0x9f, 0xc2, 0xb2,
	0x83, 0x43, 0x93, 0x39, 0xed, 0x47, 0x97, 0x9c, 0x8b, 0x0f, 0x61, 0x9d, 0xee, 0x92, 0x1e, 0x40,
	0xc9, 0x4d, 0x2c, 0x7b, 0x6c, 0xbb, 0x41, 0x63, 0x65, 0x57, 0xd8, 0x2b, 0xe9, 0xab, 0x6e, 0xd2,
	0xc4, 0x4b, 0xd9, 0x80, 0x5a, 0x76, 0x58, 0x92, 0xc1, 0x0d, 0xb8, 0xdb, 0x69, 0xf6, 0x14, 0x6b,
	0x70, 0xd4, 0x34, 0x14, 0x4b, 0xeb, 0x9b, 0x96, 0x61, 0x36, 0x75, 0x53, 0xbc, 0x23, 0xdd, 0x05,
	0x91, 0xc3, 0x50, 0xa8, 0x80, 0x33, 0x9e, 0x83, 0x3e, 0x57, 0x35, 0xb1, 0x20, 0x9f, 0x81, 0x38,
	0x6b, 0xc4, 0x9b, 0x79, 0x92, 0xb3, 0x54, 0xe1, 0x52, 0x4b, 0x15, 0xcf, 0x59, 0x4a, 0xfe, 0x63,
	0x01, 0x1a, 0x99, 0xe0, 0xd6, 0xa9, 0x1d, 0x8c, 0x91, 0x16, 0xa6, 0xee, 0xc9, 0x59, 0x3f, 0x4a,
	0xe7, 0x3c, 0x24, 0x5c, 0xe2, 0xa1, 0x9b, 0xca, 0x95, 0x1e, 0x43, 0x9d, 0xec, 0x3d, 0x0e, 0xfd,
	0x63, 0x5a, 0x4b, 0xa9, 0x1b, 0xab, 0x18, 0x7a, 0x18, 0xfa, 0xc7, 0xc4, 0xb8, 0x32, 0xd4, 0x08,
	0x95, 0xef, 0x06, 0x56, 0xea, 0xfa, 0xd8, 0x9f, 0x84, 0x13, 0x06, 0xf6, 0xdc, 0xc0, 0x74, 0x7d,
	0x24, 0x0f, 0xa1, 0xda, 0xe1, 0xf7, 0x6c, 0xc1, 0x7d, 0x62, 0xe0, 0xc3, 0x7e, 0xef, 0xd0, 0x32,
	0x3f, 0x1f, 0x28, 0x5c, 0x69, 0x9d, 0x1a, 0xff, 0xa5, 0x6a, 0x1e, 0x59, 0x47, 0x7d, 0x03, 0x3b,
	0xe4, 0x1e, 0xac, 0x67, 0xb0, 0xfe, 0xd0, 0xa4, 0xe0, 0x82, 0xfc, 0x1a, 0x24, 0x03, 0xa5, 0x1a,
	0x7a, 0x9d, 0x62, 0xce, 0xc3, 0x04, 0xc5, 0xb7, 0x18, 0xdb, 0xdb, 0x50, 0x4e, 0x4f, 0xe3, 0xf0,
	0x37, 0xd6, 0x24, 0x8b, 0xee, 0x12, 0x01, 0x0c, 0xdd, 0x91, 0xfc, 0x31, 0x6c, 0x9c, 0x93, 0x7c,
	0xa3, 0x80, 0x90, 0x5f, 0xd1, 0x6a, 0x89, 0x8b, 0x90, 0x81, 0x52, 0x69, 0x08, 0x75, 0x7b, 0xf4,
	0x35, 0x0a, 0xd2, 0x49, 0x8c, 0x2c, 0x52, 0xcb, 0xe8, 0xf6, 0xcb, 0x4a, 0x7f, 0x73, 0xba, 0x61,
	0xca, 0xe8, 0xe8, 0x8e, 0x5e, 0xcb, 0xb8, 0x60, 0xc0, 0x61, 0x65, 0x5a, 0x4e, 0x71, 0x95, 0xfb,
	0x15, 0xdc, 0xeb, 0xa0, 0xb4, 0x45, 0x19, 0x4d, 0xf7, 0xdc, 0x62, 0xb1, 0xfb, 0x43, 0x11, 0xee,
	0x2f, 0x12, 0x70, 0xb3, 0x4c, 0xb9, 0xc2, 0x2d, 0x4f, 0x60, 0xcd, 0x99, 0xc4, 0x31, 0x0a, 0x52,
	0x6b, 0xb6, 0xf4, 0xd4, 0x18, 0xb8, 0x43, 0xe3, 0xfb, 0x2e, 0x2c, 0xf3, 0xa5, 0x87, 0x2e, 0x24,
	0x15, 0xca, 0x24, 0x9a, 0x59, 0xb7, 0x10, 0xae, 0xd1, 0x5c, 0x4d, 0xec, 0x73, 0xec, 0x65, 0x72,
	0x98, 0xd2, 0x31, 0xfb, 0x92, 0xbe, 0x80, 0xf5, 0x91, 0x3b, 0x76, 0x53, 0xdb, 0xb3, 0x72, 0x96,
	0x2b, 0x84, 0xe5, 0xc1, 0x25, 0x2c, 0xdb, 0x74, 0x0f, 0x66, 0x98, 0x59, 0x68, 0x6d, 0x94, 0x03,
	0x09, 0xef, 0x16, 0xe7, 0xb6, 0xc6, 0x2a, 0xe1, 0x79, 0x55, 0xdd, 0x67, 0x81, 0xc4, 0xba, 0x25,
	0x76, 0xf7, 0xef, 0x0b, 0x74, 0x74, 0x9a, 0x39, 0x80, 0xf4, 0x73, 0x58, 0xc2, 0xea, 0x32, 0x27,
	0xbc, 0x7b, 0x05, 0xd7, 0x69, 0x74, 0xd3, 0x5e, 0x89, 0x37, 0x4a, 0x0a, 0xac, 0x44, 0x93, 0xc0,
	0x4d, 0x4e, 0x59, 0x43, 0x7a, 0x7a, 0x0d, 0x16, 0x03, 0xb2, 0x81, 0x30, 0x61, 0x9b, 0xa5, 0x0e,
	0x00, 0x4d, 0x2f, 0x6e, 0xce, 0xd9, 0xbb, 0x8e, 0x2b, 0x08, 0x17, 0x9a, 0x9a, 0xb8, 0xbd, 0x5d,
	0xaf, 0x4a, 0xc9, 0x16, 0xed, 0x09, 0x19, 0x07, 0x49, 0x84, 0xe2, 0x24, 0x2b, 0x98, 0xf8, 0x13,
	0x77, 0x94, 0xb1, 0x7b, 0x92, 0x5a, 0x5f, 0xdb, 0x1e, 0x0b, 0xbb, 0x55, 0xbc, 0x7e, 0x61, 0x7b,
	0xa4, 0x58, 0x12, 0x65, 0x93, 0xd4, 0x4e, 0x27, 0x09, 0x89, 0xb8, 0x92, 0x5e, 0x21, 0x30, 0x83,
	0x80, 0xe4, 0xbf, 0x0a, 0x20, 0xce, 0x5b, 0x6c, 0xb1, 0x10, 0x14, 0x8c, 0x68, 0xa1, 0x64, 0x42,
	0x50, 0x30, 0xc2, 0x45, 0x12, 0x8f, 0xa5, 0x01, 0x42, 0x23, 0x9c, 0x23, 0xce, 0x04, 0x31, 0x19,
	0x80, 0x41, 0x3a, 0x81, 0xe0, 0xd0, 0xb7, 0x27, 0x69, 0x68, 0x51, 0x55, 0x08, 0x0b, 0x7a, 0xd4,
	0x1a, 0x06, 0x93, 0xa3, 0x4d, 0x19, 0x25, 0x28, 0xfe, 0x1a, 0xc5, 0x7c, 0x3d, 0x06, 0x0a, 0x22,
	0xe5, 0xf8, 0x33, 0xb8, 0xc7, 0xab, 0xca, 0x9a, 0xc9, 0x20, 0xfd, 0xaf, 0x83, 0x03, 0xc7, 0x9c,
	0x74, 0xde, 0xe9, 0xd2, 0x33, 0xa8, 0x4e, 0x12, 0x14, 0x5b, 0x51, 0x1c, 0x9e, 0xb8, 0x1e, 0x62,
	0xfc, 0xd7, 0x30, 0x7f, 0xcc, 0x60, 0x40, 0xc1, 0x7a, 0x65, 0x92, 0x2f, 0x2e, 0xb3, 0xd4, 0x63,
	0xa8, 0x53, 0x23, 0x59, 0xc4, 0x61, 0x59, 0x09, 0xa8, 0x52, 0x68, 0xc7, 0x3d, 0x49, 0xd5, 0x91,
	0xb4, 0x07, 0x22, 0x4f, 0x45, 0xc6, 0x4d, 0x3a, 0x36, 0xd6, 0x73, 0x3a, 0x32, 0x74, 0xe2, 0x59,
	0xc2, 0x09, 0x03, 0x32, 0x58, 0x2e, 0x13, 0x8a, 0x55, 0xbc, 0xc6, 0x93, 0xe5, 0x14, 0xe5, 0x8f,
	0x3e, 0x22, 0xc9, 0xcd, 0x50, 0xbd, 0xd1, 0x47, 0xf3, 0x66, 0x5e, 0x3d, 0x67, 0xe6, 0x2f, 0x61,
	0x73, 0xd6, 0x16, 0xb9, 0xa1, 0xf3, 0x24, 0xa2, 0xa6, 0x78, 0xb3, 0x24, 0x92, 0x6d, 0xb8, 0x9f,
	0x45, 0xf5, 0xac, 0x27, 0x67, 0xd3, 0x4b, 0x78, 0xe3, 0xf4, 0x92, 0x7f, 0x0b, 0x1b, 0x18, 0x47,
	0x43, 0x30, 0xe7, 0x8f, 0x6f, 0x2f, 0xa1, 0x4d, 0x58, 0xd3, 0xdb, 0x0b, 0x5e, 0x2c, 0x34, 0x79,
	0x61, 0xa1, 0xc9, 0xe7, 0x28, 0xb9, 0x71, 0x9f, 0xa3, 0xc4, 0x43, 0xbf, 0xfc, 0x6f, 0x76, 0x03,
	0xc4, 0x26, 0xd0, 0x51, 0x32, 0xf1, 0x52, 0x3c, 0xf6, 0x4c, 0xa3, 0x03, 0xe7, 0xbb, 0x90, 0x47,
	0x07, 0x1e, 0x2e, 0xf8, 0x02, 0xf5, 0xe6, 0xb6, 0xcd, 0x4f, 0x58, 0xe4, 0x4f, 0xf8, 0x10, 0x2a,
	0xa4, 0xd0, 0xf8, 0xae, 0x83, 0xe3, 0x8e, 0xe6, 0x1f, 0xe9, 0x29, 0x3d, 0xd7, 0x51, 0x47, 0xf2,
	0x8f, 0x61, 0x55, 0x61, 0x7a, 0x00, 0xac, 0xb4, 0x42, 0xdf, 0x0f, 0x03, 0xf1, 0x8e, 0x54, 0x85,
	0x52, 0xf3, 0x38, 0x08, 0x63, 0xdf, 0xf6, 0x44, 0x41, 0x5a, 0x87, 0x9a, 0x12, 0x8c, 0x5e, 0xba,
	0xe9, 0xa9, 0x16, 0x62, 0xe9, 0x62, 0x41, 0xf6, 0xe1, 0x2e, 0xd7, 0x19, 0x94, 0x20, 0x0e, 0x3d,
	0xef, 0x16, 0x87, 0x99, 0x3a, 0x14, 0xc2, 0x88, 0x65, 0x49, 0x21, 0x8c, 0xe4, 0x43, 0xb8, 0xb7,
	0x40, 0xdc, 0xcd, 0x26, 0x98, 0xdf, 0x09, 0xf0, 0x16, 0xc7, 0xc4, 0x40, 0x1e, 0x72, 0x48, 0xf7,
	0xbd, 0xe5, 0x41, 0x8c, 0x15, 0xd1, 0x62, 0x5e, 0x44, 0xe9, 0x69, 0x96, 0xb2, 0xd3, 0x7c, 0x02,
	0x3b, 0x97, 0x28, 0x72, 0xb3, 0x53, 0x7d, 0x05, 0x1b, 0x1c, 0xaf, 0xac, 0x92, 0xbf, 0x07, 0x65,
	0x52, 0xc1, 0xb8, 0xd1, 0xec, 0x5c, 0xf9, 0x2a, 0x4d, 0xa6, 0xd4, 0x3f, 0x84, 0xba, 0x9b, 0x58,
	0x91, 0x1d, 0xa7, 0xae, 0xe3, 0x46, 0x76, 0x4a, 0xb3, 0xa0, 0xa4, 0xd7, 0xdc, 0x64, 0x90, 0x03,
	0xe5, 0xef, 0x97, 0x67, 0x84, 0x4d, 0xdb, 0x38, 0x1e, 0xda, 0x23, 0xcf, 0x3e, 0xe3, 0xae, 0xce,
	0x78, 0xa9, 0x8e, 0xce, 0xd5, 0xd1, 0xe2, 0x35, 0xea, 0xe8, 0xe2, 0x41, 0xe8, 0x53, 0xfe, 0x3c,
	0xf4, 0x06, 0x76, 0xcd, 0xa9, 0x25, 0xab, 0xf8, 0xf9, 0x71, 0xdf, 0x85, 0x75, 0xee, 0xac, 0x96,
	0xe7, 0xfa, 0x6e, 0x4a, 0xaa, 0x65, 0x4d, 0x17, 0x39, 0x44, 0x17, 0xc3, 0xa5, 0x2d, 0x28, 0x25,
	0xc4, 0x43, 0x68, 0xc4, 0x6a, 0x66, 0xb6, 0x96, 0xde, 0x03, 0x89, 0x7e, 0x5b, 0x31, 0xbe, 0xe5,
	0xe0, 0x76, 0x1b, 0xa7, 0x8d, 0x12, 0xe5, 0x44, 0x31, 0x3a, 0x46, 0x18, 0x18, 0x8e, 0x6b, 0xc8,
	0x0c, 0x35, 0x0a, 0x46, 0x8d, 0x32, 0xa1, 0xad, 0x73, 0xb4, 0x4a, 0x30, 0x92, 0xf6, 0x61, 0x3d,
	0x0d, 0xf1, 0xa4, 0xc6, 0xb3, 0x05, 0x42, 0xba, 0x46, 0x10, 0x1c, 0xd7, 0x27, 0xb0, 0xc6, 0xd3,
	0x62, 0xa6, 0x15, 0xda, 0x65, 0x73, 0x4a, 0xcc, 0xf3, 0x09, 0xac, 0x31, 0xe9, 0x59, 0x9b, 0xaa,
	0xee, 0x0a, 0x7b, 0x4b, 0x7a, 0x8d, 0x82, 0x95, 0xbc, 0xad, 0xf3, 0x6d, 0xa2, 0x46, 0x68, 0xb8,
	0x36, 0x91, 0x2b, 0xc7, 0xd8, 0x11, 0xb2, 0x3a, 0xa7, 0x1c, 0x0d, 0x68, 0x42, 0xfb, 0x36, 0x54,
	0x11, 0x49, 0x56, 0x66, 0xe4, 0x35, 0x7a, 0xd7, 0xa2, 0x30, 0x6a, 0xdf, 0x47, 0xc0, 0x96, 0xb4,
	0x38, 0x8a, 0xb4, 0x2d, 0x51, 0x10, 0xa9, 0x4b, 0xbb, 0x50, 0xc5, 0x22, 0xac, 0x70, 0x42, 0x54,
	0x6f, 0xac, 0xd3, 0xf7, 0x2f, 0x0c, 0xeb, 0x4f, 0xb0, 0xda, 0xf2, 0xb7, 0x02, 0x6c, 0x9d, 0x4b,
	0x28, 0x6d, 0xe2, 0x1f, 0xdf, 0x6a, 0x5e, 0xef, 0x00, 0xb0, 0x13, 0x07, 0x13, 0x9f, 0xa5, 0x77,
	0x39, 0x99, 0xca, 0x92, 0x8f, 0x60, 0xfb, 0x42, 0x1d, 0x6e, 0x96, 0xd2, 0xa7, 0xf0, 0x60, 0x41,
	0x96, 0xd1, 0x8b, 0x34, 0x4e, 0x84, 0x7c, 0xd4, 0x16, 0xde, 0x68, 0x7c, 0xcf, 0x47, 0xee, 0x5f,
	0x83, 0x94, 0x5d, 0xca, 0xfe, 0xe7, 0x35, 0xfc, 0x63, 0xd8, 0x38, 0x27, 0xec, 0x66, 0x86, 0xf9,
	0x4e, 0x80, 0xad, 0x8c, 0xc5, 0xff, 0xb1, 0x7e, 0x1f, 0xc1, 0xf6, 0x85, 0x6a, 0xdc, 0xec, 0x44,
	0xdf, 0x14, 0x60, 0x33, 0x63, 0xd5, 0x0a, 0x83, 0x34, 0x0e, 0x3d, 0x7c, 0x4b, 0xbf, 0xc5, 0xe3,
	0xa8, 0x99, 0x1b, 0xea, 0xcf, 0x7e, 0x72, 0x9d, 0xdb, 0xf9, 0xac, 0x1a, 0x07, 0xf8, 0x6f, 0x3f,
	0x22, 0xe7, 0x36, 0x61, 0x85, 0xae, 0xa4, 0x0a, 0xac, 0x0e, 0xb5, 0x4f, 0xb5, 0xfe, 0x4b, 0x4d,
	0xbc, 0x23, 0x95, 0x61, 0xb9, 0xd9, 0x69, 0xaa, 0x9a, 0x28, 0x48, 0x35, 0x28, 0x6b, 0xca, 0x67,
	0xa6, 0x35, 0x34, 0x14, 0x5d, 0x2c, 0xe0, 0xc1, 0xa1, 0xa9, 0xf5, 0xcd, 0x23, 0x45, 0xb7, 0xf4,
	0xfe, 0x50, 0x6b, 0x8b, 0x45, 0x49, 0x84, 0xaa, 0xae, 0x90, 0x87, 0x2b, 0xab, 0xd3, 0xec, 0x29,
	0xe2, 0x92, 0xac, 0x40, 0x63, 0xb1, 0xec, 0x9b, 0x99, 0x72, 0x40, 0xdf, 0x6c, 0xde, 0xb0, 0x03,
	0x4a, 0xb0, 0x74, 0xe2, 0xd9, 0x63, 0x66, 0x3e, 0xf2, 0x2d, 0xff, 0xa9, 0x00, 0xeb, 0xb9, 0x66,
	0xc8, 0xf3, 0x5a, 0x61, 0x70, 0x42, 0x1e, 0x75, 0x91, 0xc7, 0xbd, 0x5e, 0xad, 0xe0, 0xa5, 0x3a,
	0xc2, 0x4d, 0x34, 0x7f, 0x12, 0x19, 0xa1, 0xc4, 0x61, 0xa3, 0x64, 0xfe, 0xc4, 0xd1, 0x46, 0x89,
	0x83, 0xcb, 0x27, 0xd9, 0x9f, 0x44, 0xc8, 0x71, 0x6d, 0xcf, 0xb2, 0xd3, 0x34, 0x66, 0xc1, 0xb6,
	0x86, 0x11, 0x06, 0x85, 0x37, 0xd3, 0x34, 0x96, 0x64, 0xa8, 0x51, 0x59, 0xd3, 0x69, 0x9f, 0xde,
	0x07, 0x2a, 0x44, 0x22, 0x9d, 0xf8, 0xe5, 0xc9, 0x9c, 0x92, 0x64, 0xe3, 0x0f, 0xe0, 0x51, 0xb3,
	0xfd, 0x42, 0xd1, 0xcc, 0xa1, 0xae, 0x58, 0x2d, 0xa5, 0xdb, 0xb5, 0x9a, 0xa6, 0xa9, 0x5b, 0x43,
	0xcd, 0x18, 0x28, 0x2d, 0xf5, 0xb9, 0xaa, 0xb4, 0xc5, 0x3b, 0xd2, 0x36, 0x6c, 0x2e, 0x22, 0x7a,
	0x49, 0x3c, 0xf9, 0x10, 0xb6, 0x16, 0x21, 0x07, 0x43, 0x4d, 0x35, 0x8e, 0xc4, 0x82, 0xfc, 0xdd,
	0x12, 0x27, 0xf7, 0xea, 0x49, 0x20, 0xeb, 0xea, 0x05, 0xbe, 0xab, 0xef, 0x42, 0x95, 0x7c, 0xd0,
	0x96, 0x44, 0xef, 0xa9, 0x45, 0x1d, 0x08, 0x0c, 0xf7, 0xa3, 0x44, 0x6a, 0x33, 0x2f, 0x92, 0x6b,
	0xc1, 0xd2, 0x95, 0x2f, 0xaf, 0x7c, 0x04, 0x50, 0xef, 0x92, 0x3b, 0xf7, 0x2e, 0x54, 0x9d, 0x49,
	0x1c, 0x5b, 0x53, 0xc7, 0xb1, 0x2b, 0x26, 0x86, 0xb5, 0xa8, 0xf3, 0x54, 0x28, 0x13, 0x24, 0x91,
	0xb3, 0x72, 0xe5, 0xaf, 0x18, 0xe7, 0xc2, 0x42, 0x2f, 0xe1, 0xed, 0x44, 0xd8, 0x3b, 0xb0, 0xfe,
	0x55, 0xe8, 0x06, 0x16, 0xd1, 0xdb, 0x77, 0x03, 0xcb, 0x09, 0x52, 0x36, 0x39, 0xd4, 0x31, 0x02,
	0xeb, 0xd7, 0x73, 0x83, 0x56, 0x30, 0x4f, 0x6a, 0xbf, 0x26, 0xa4, 0xa5, 0x39, 0x52, 0xfb, 0x35,
	0x26, 0x9d, 0x6b, 0x93, 0xe5, 0x73, 0x6d, 0xf2, 0x31, 0xd4, 0xb9, 0xbe, 0x6d, 0xf9, 0x09, 0x19,
	0x18, 0x8a, 0x7a, 0x35, 0x6f, 0xdd, 0xbd, 0x44, 0xea, 0xe3, 0xee, 0x9e, 0xa6, 0x1e, 0x6b, 0x20,
	0x95, 0x2b, 0x1b, 0x08, 0x57, 0xe8, 0xf0, 0x36, 0x62, 0x58, 0x48, 0xb2, 0x6f, 0xf9, 0x1f, 0x02,
	0x57, 0xd6, 0x73, 0x1a, 0x3a, 0x46, 0x10, 0x41, 0xdc, 0x9d, 0x87, 0x6d, 0x24, 0xfa, 0xce, 0xe4,
	0x67, 0xe1, 0x8a, 0xfc, 0x94, 0x5f, 0x00, 0x18, 0xf9, 0xde, 0x6d, 0xd8, 0x34, 0x14, 0xd3, 0xec,
	0x2a, 0xf4, 0x31, 0x76, 0x36, 0xac, 0x37, 0x60, 0x8d, 0x47, 0xd2, 0x70, 0xbe, 0x0f, 0x12, 0x0f,
	0xcc, 0xc2, 0xf8, 0x97, 0x70, 0x6f, 0x4e, 0x7b, 0x23, 0x9c, 0xc4, 0x0e, 0x22, 0x5d, 0x20, 0xf6,
	0xd8, 0x75, 0x11, 0x7f, 0x62, 0x08, 0xbe, 0x55, 0xd3, 0xa4, 0xc6, 0x9f, 0xe4, 0x8c, 0x84, 0xda,
	0xfa, 0x2a, 0xc9, 0xee, 0x83, 0x40, 0x41, 0x9f, 0x24, 0x61, 0x20, 0x7f, 0x5b, 0x80, 0xfb, 0x4d,
	0xfe, 0x81, 0x93, 0x36, 0x71, 0x7c, 0x21, 0xfc, 0x98, 0xfd, 0x04, 0xf4, 0x06, 0xcf, 0xa6, 0xf4,
	0x47, 0x20, 0x3c, 0x7c, 0xc6, 0x76, 0x30, 0x0a, 0x7d, 0x2b, 0x49, 0x51, 0x84, 0x87, 0x92, 0x63,
	0x14, 0xb3, 0xfc, 0x12, 0x29, 0xc6, 0x48, 0x51, 0x44, 0x67, 0x10, 0x3c, 0x89, 0x39, 0x44, 0xb8,
	0xc5, 0xdf, 0x12, 0x2b, 0x14, 0x66, 0x92, 0xbb, 0xe2, 0x10, 0x6a, 0xcc, 0x65, 0xf4, 0x08, 0xa4,
	0xda, 0x54, 0x9e, 0x7d, 0x70, 0xfd, 0xe8, 0xa0, 0xb6, 0xc3, 0x21, 0x97, 0xaf, 0xe4, 0x2f, 0xb9,
	0x16, 0xa7, 0x67, 0x6a, 0x25, 0xb7, 0xf8, 0x90, 0xcb, 0x77, 0x90, 0x19, 0x09, 0x37, 0xea, 0x20,
	0xfb, 0x23, 0x10, 0x07, 0x63, 0x87, 0x7f, 0x0e, 0xc6, 0xc3, 0x40, 0x95, 0xbc, 0xe4, 0xe7, 0xef,
	0xfd, 0x1b, 0xb0, 0x46, 0x20, 0xe6, 0x91, 0xde, 0x7f, 0x49, 0x7e, 0x11, 0xe0, 0x1e, 0xfc, 0xdb,
	0x6a, 0x47, 0x35, 0x9b, 0x5d, 0x0a, 0x2e, 0x64, 0xbf, 0x0d, 0x64, 0x95, 0x54, 0x2c, 0xee, 0xff,
	0x45, 0xc8, 0x1b, 0xd5, 0xcc, 0x8f, 0x0b, 0xb8, 0x67, 0x4e, 0xe3, 0x79, 0xda, 0x5a, 0xb7, 0x61,
	0x73, 0x0e, 0x77, 0xd4, 0x37, 0x4c, 0xab, 0xa7, 0xb6, 0x68, 0x4c, 0xcf, 0x21, 0x31, 0xbc, 0x20,
	0x3d, 0x86, 0xdd, 0x45, 0x9b, 0x5a, 0x47, 0x4d, 0x4d, 0x53, 0xba, 0xb4, 0x37, 0x17, 0xa5, 0x47,
	0xb0, 0x3d, 0x47, 0x35, 0x43, 0xb0, 0xb4, 0xff, 0xcf, 0x02, 0x88, 0xdc, 0xf8, 0x48, 0x7f, 0x63,
	0x79, 0x08, 0x5b, 0xfc, 0x19, 0xd9, 0x4f, 0x4e, 0xb9, 0xc2, 0x3b, 0xf0, 0x60, 0x01, 0x5e, 0xd1,
	0xf4, 0x7e, 0xb7, 0x2b, 0x0a, 0xd2, 0xdb, 0xb0, 0xb3, 0x00, 0xdd, 0x56, 0x75, 0xa5, 0x65, 0x5a,
	0x8a, 0xd6, 0x16, 0x0b, 0xd2, 0x2e, 0xbc, 0xb5, 0x80, 0xc4, 0x50, 0xba, 0x4a, 0xcb, 0x54, 0xb5,
	0x8e, 0x58, 0xc1, 0x9a, 0x5f, 0x48, 0xa1, 0xb4, 0xc5, 0xea, 0x05, 0x4a, 0x2a, 0x9f, 0x0d, 0xba,
	0xfd, 0xb6, 0x22, 0xd6, 0x2e, 0xd0, 0xa2, 0xd7, 0xd4, 0x86, 0xcd, 0x2e, 0xd1, 0xa2, 0x7e, 0x01,
	0x0b, 0xe3, 0x73, 0x83, 0xe0, 0xd7, 0x24, 0x19, 0x1e, 0x2e, 0xc0, 0x9b, 0x6a, 0x4f, 0xe9, 0x0f,
	0xe9, 0x49, 0xc4, 0xfd, 0xbf, 0x09, 0x50, 0xcf, 0xe2, 0x92, 0x9a, 0x6f, 0xa6, 0xe5, 0xce, 0xdb,
	0x6e, 0x0b, 0xee, 0xcf, 0x23, 0x33, 0xc3, 0xcd, 0xb4, 0xe3, 0xc5, 0x56, 0x9b, 0xc7, 0xeb, 0x4d,
	0xad, 0xdd, 0xef, 0x59, 0x86, 0xa9, 0x0c, 0x0c, 0xb1, 0xb2, 0x88, 0xa2, 0xd5, 0xd7, 0x4c, 0xbd,
	0xdf, 0xb5, 0xf0, 0xc8, 0x26, 0x56, 0xf7, 0x9b, 0x00, 0x74, 0x2e, 0x7f, 0xee, 0xd9, 0x63, 0xe9,
	0x2e, 0x88, 0x54, 0xba, 0xf5, 0xbc, 0xdb, 0xec, 0x58, 0x5a, 0x5f, 0x53, 0xa8, 0x7f, 0xe7, 0xa1,
	0xb9, 0xe5, 0x85, 0xfd, 0x2f, 0xf3, 0xd8, 0x26, 0x4c, 0x36, 0x61, 0x23, 0x0f, 0x32, 0x9e, 0xcf,
	0x43, 0xd8, 0x9a, 0x43, 0xa8, 0x9a, 0x35, 0xd0, 0xfb, 0x1d, 0x5d, 0x31, 0x0c, 0x51, 0x58, 0xb0,
	0xf1, 0x17, 0x43, 0xd5, 0x14, 0x0b, 0x87, 0x7d, 0x68, 0x38, 0xa1, 0x7f, 0x70, 0xe6, 0x9e, 0x85,
	0x13, 0x9c, 0xc7, 0x7e, 0x38, 0x42, 0x1e, 0xfd, 0xe7, 0x8b, 0x2f, 0x3e, 0x1c, 0x87, 0x9e, 0x1d,
	0x8c, 0x0f, 0x3e, 0x7a, 0x96, 0xa6, 0x07, 0x4e, 0xe8, 0xbf, 0x4f, 0xc0, 0x4e, 0xe8, 0xbd, 0x6f,
	0x47, 0xd1, 0xfb, 0x8b, 0x4b, 0xd8, 0xf1, 0x0a, 0x21, 0xfa, 0xf0, 0x3f, 0x01, 0x00, 0x00, 0xff,
	0xff, 0x10, 0x30, 0x20, 0x84, 0x03, 0x22, 0x00, 0x00,
}
