// Code generated by protoc-gen-go. DO NOT EDIT.
// source: knight-group-logic_.proto

package knightgrouplogic

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	app "golang.52tt.com/protocol/app"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type KnightGroupChannelMsg_KnightChannelMsgType int32

const (
	KnightG<PERSON>ChannelMsg_ENUM_UNKOWN_TYPE      KnightGroupChannelMsg_KnightChannelMsgType = 0
	KnightGroupChannelMsg_ENUM_JOIN_GROUP_TYPE  KnightGroupChannelMsg_KnightChannelMsgType = 1
	KnightGroupChannelMsg_ENUM_JOIN_CAMP_TYPE   KnightGroupChannelMsg_KnightChannelMsgType = 2
	KnightGroupChannelMsg_ENUM_GROUP_CHIEF_TYPE KnightGroupChannelMsg_KnightChannelMsgType = 3
)

var KnightGroupChannelMsg_KnightChannelMsgType_name = map[int32]string{
	0: "ENUM_UNKOWN_TYPE",
	1: "ENUM_JOIN_GROUP_TYPE",
	2: "ENUM_JOIN_CAMP_TYPE",
	3: "ENUM_GROUP_CHIEF_TYPE",
}

var KnightGroupChannelMsg_KnightChannelMsgType_value = map[string]int32{
	"ENUM_UNKOWN_TYPE":      0,
	"ENUM_JOIN_GROUP_TYPE":  1,
	"ENUM_JOIN_CAMP_TYPE":   2,
	"ENUM_GROUP_CHIEF_TYPE": 3,
}

func (x KnightGroupChannelMsg_KnightChannelMsgType) String() string {
	return proto.EnumName(KnightGroupChannelMsg_KnightChannelMsgType_name, int32(x))
}

func (KnightGroupChannelMsg_KnightChannelMsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{11, 0}
}

type JoinKnightGroupReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	AnchorUid            uint32       `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	KnightUid            uint32       `protobuf:"varint,4,opt,name=knight_uid,json=knightUid,proto3" json:"knight_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *JoinKnightGroupReq) Reset()         { *m = JoinKnightGroupReq{} }
func (m *JoinKnightGroupReq) String() string { return proto.CompactTextString(m) }
func (*JoinKnightGroupReq) ProtoMessage()    {}
func (*JoinKnightGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{0}
}

func (m *JoinKnightGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinKnightGroupReq.Unmarshal(m, b)
}
func (m *JoinKnightGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinKnightGroupReq.Marshal(b, m, deterministic)
}
func (m *JoinKnightGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinKnightGroupReq.Merge(m, src)
}
func (m *JoinKnightGroupReq) XXX_Size() int {
	return xxx_messageInfo_JoinKnightGroupReq.Size(m)
}
func (m *JoinKnightGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinKnightGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinKnightGroupReq proto.InternalMessageInfo

func (m *JoinKnightGroupReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *JoinKnightGroupReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *JoinKnightGroupReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *JoinKnightGroupReq) GetKnightUid() uint32 {
	if m != nil {
		return m.KnightUid
	}
	return 0
}

type JoinKnightGroupResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *JoinKnightGroupResp) Reset()         { *m = JoinKnightGroupResp{} }
func (m *JoinKnightGroupResp) String() string { return proto.CompactTextString(m) }
func (*JoinKnightGroupResp) ProtoMessage()    {}
func (*JoinKnightGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{1}
}

func (m *JoinKnightGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinKnightGroupResp.Unmarshal(m, b)
}
func (m *JoinKnightGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinKnightGroupResp.Marshal(b, m, deterministic)
}
func (m *JoinKnightGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinKnightGroupResp.Merge(m, src)
}
func (m *JoinKnightGroupResp) XXX_Size() int {
	return xxx_messageInfo_JoinKnightGroupResp.Size(m)
}
func (m *JoinKnightGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinKnightGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinKnightGroupResp proto.InternalMessageInfo

func (m *JoinKnightGroupResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type KnightUserInfo struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string           `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Alias                string           `protobuf:"bytes,3,opt,name=alias,proto3" json:"alias,omitempty"`
	Nickname             string           `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  int32            `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	KnightProfile        *app.UserProfile `protobuf:"bytes,6,opt,name=knight_profile,json=knightProfile,proto3" json:"knight_profile,omitempty"`
	KnightYkwInfo        *app.UserUKWInfo `protobuf:"bytes,7,opt,name=knight_ykw_info,json=knightYkwInfo,proto3" json:"knight_ykw_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *KnightUserInfo) Reset()         { *m = KnightUserInfo{} }
func (m *KnightUserInfo) String() string { return proto.CompactTextString(m) }
func (*KnightUserInfo) ProtoMessage()    {}
func (*KnightUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{2}
}

func (m *KnightUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnightUserInfo.Unmarshal(m, b)
}
func (m *KnightUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnightUserInfo.Marshal(b, m, deterministic)
}
func (m *KnightUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnightUserInfo.Merge(m, src)
}
func (m *KnightUserInfo) XXX_Size() int {
	return xxx_messageInfo_KnightUserInfo.Size(m)
}
func (m *KnightUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_KnightUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_KnightUserInfo proto.InternalMessageInfo

func (m *KnightUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *KnightUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *KnightUserInfo) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *KnightUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *KnightUserInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *KnightUserInfo) GetKnightProfile() *app.UserProfile {
	if m != nil {
		return m.KnightProfile
	}
	return nil
}

func (m *KnightUserInfo) GetKnightYkwInfo() *app.UserUKWInfo {
	if m != nil {
		return m.KnightYkwInfo
	}
	return nil
}

type RankValueInfo struct {
	KnightDays           uint32   `protobuf:"varint,1,opt,name=knight_days,json=knightDays,proto3" json:"knight_days,omitempty"`
	KnightLeftDay        uint32   `protobuf:"varint,2,opt,name=knight_left_day,json=knightLeftDay,proto3" json:"knight_left_day,omitempty"`
	LoveValue            uint32   `protobuf:"varint,3,opt,name=love_value,json=loveValue,proto3" json:"love_value,omitempty"`
	IsChief              bool     `protobuf:"varint,4,opt,name=is_chief,json=isChief,proto3" json:"is_chief,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RankValueInfo) Reset()         { *m = RankValueInfo{} }
func (m *RankValueInfo) String() string { return proto.CompactTextString(m) }
func (*RankValueInfo) ProtoMessage()    {}
func (*RankValueInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{3}
}

func (m *RankValueInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RankValueInfo.Unmarshal(m, b)
}
func (m *RankValueInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RankValueInfo.Marshal(b, m, deterministic)
}
func (m *RankValueInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RankValueInfo.Merge(m, src)
}
func (m *RankValueInfo) XXX_Size() int {
	return xxx_messageInfo_RankValueInfo.Size(m)
}
func (m *RankValueInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RankValueInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RankValueInfo proto.InternalMessageInfo

func (m *RankValueInfo) GetKnightDays() uint32 {
	if m != nil {
		return m.KnightDays
	}
	return 0
}

func (m *RankValueInfo) GetKnightLeftDay() uint32 {
	if m != nil {
		return m.KnightLeftDay
	}
	return 0
}

func (m *RankValueInfo) GetLoveValue() uint32 {
	if m != nil {
		return m.LoveValue
	}
	return 0
}

func (m *RankValueInfo) GetIsChief() bool {
	if m != nil {
		return m.IsChief
	}
	return false
}

type MyRankValueInfo struct {
	Index                uint32   `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	Value                uint32   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	LackValue            uint32   `protobuf:"varint,3,opt,name=lack_value,json=lackValue,proto3" json:"lack_value,omitempty"`
	IsInRank             bool     `protobuf:"varint,4,opt,name=is_in_rank,json=isInRank,proto3" json:"is_in_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MyRankValueInfo) Reset()         { *m = MyRankValueInfo{} }
func (m *MyRankValueInfo) String() string { return proto.CompactTextString(m) }
func (*MyRankValueInfo) ProtoMessage()    {}
func (*MyRankValueInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{4}
}

func (m *MyRankValueInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyRankValueInfo.Unmarshal(m, b)
}
func (m *MyRankValueInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyRankValueInfo.Marshal(b, m, deterministic)
}
func (m *MyRankValueInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyRankValueInfo.Merge(m, src)
}
func (m *MyRankValueInfo) XXX_Size() int {
	return xxx_messageInfo_MyRankValueInfo.Size(m)
}
func (m *MyRankValueInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MyRankValueInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MyRankValueInfo proto.InternalMessageInfo

func (m *MyRankValueInfo) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *MyRankValueInfo) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *MyRankValueInfo) GetLackValue() uint32 {
	if m != nil {
		return m.LackValue
	}
	return 0
}

func (m *MyRankValueInfo) GetIsInRank() bool {
	if m != nil {
		return m.IsInRank
	}
	return false
}

//各种等级,是否在线等
type RankExtInfo struct {
	Rich                 uint32             `protobuf:"varint,1,opt,name=rich,proto3" json:"rich,omitempty"`
	Charm                uint32             `protobuf:"varint,2,opt,name=charm,proto3" json:"charm,omitempty"`
	FanLevel             uint32             `protobuf:"varint,3,opt,name=fan_level,json=fanLevel,proto3" json:"fan_level,omitempty"`
	NobilityLevel        uint32             `protobuf:"varint,4,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	ChannelLevel         uint32             `protobuf:"varint,5,opt,name=channel_level,json=channelLevel,proto3" json:"channel_level,omitempty"`
	PlateInfo            *app.FansPlateInfo `protobuf:"bytes,6,opt,name=plate_info,json=plateInfo,proto3" json:"plate_info,omitempty"`
	IsOnline             bool               `protobuf:"varint,7,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *RankExtInfo) Reset()         { *m = RankExtInfo{} }
func (m *RankExtInfo) String() string { return proto.CompactTextString(m) }
func (*RankExtInfo) ProtoMessage()    {}
func (*RankExtInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{5}
}

func (m *RankExtInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RankExtInfo.Unmarshal(m, b)
}
func (m *RankExtInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RankExtInfo.Marshal(b, m, deterministic)
}
func (m *RankExtInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RankExtInfo.Merge(m, src)
}
func (m *RankExtInfo) XXX_Size() int {
	return xxx_messageInfo_RankExtInfo.Size(m)
}
func (m *RankExtInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RankExtInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RankExtInfo proto.InternalMessageInfo

func (m *RankExtInfo) GetRich() uint32 {
	if m != nil {
		return m.Rich
	}
	return 0
}

func (m *RankExtInfo) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *RankExtInfo) GetFanLevel() uint32 {
	if m != nil {
		return m.FanLevel
	}
	return 0
}

func (m *RankExtInfo) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *RankExtInfo) GetChannelLevel() uint32 {
	if m != nil {
		return m.ChannelLevel
	}
	return 0
}

func (m *RankExtInfo) GetPlateInfo() *app.FansPlateInfo {
	if m != nil {
		return m.PlateInfo
	}
	return nil
}

func (m *RankExtInfo) GetIsOnline() bool {
	if m != nil {
		return m.IsOnline
	}
	return false
}

type RankMemberInfo struct {
	UserInfo             *KnightUserInfo   `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	UserRankValue        *RankValueInfo    `protobuf:"bytes,2,opt,name=user_rank_value,json=userRankValue,proto3" json:"user_rank_value,omitempty"`
	UserExtInfo          *RankExtInfo      `protobuf:"bytes,3,opt,name=user_ext_info,json=userExtInfo,proto3" json:"user_ext_info,omitempty"`
	AnchorUserList       []*KnightUserInfo `protobuf:"bytes,4,rep,name=anchor_user_list,json=anchorUserList,proto3" json:"anchor_user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RankMemberInfo) Reset()         { *m = RankMemberInfo{} }
func (m *RankMemberInfo) String() string { return proto.CompactTextString(m) }
func (*RankMemberInfo) ProtoMessage()    {}
func (*RankMemberInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{6}
}

func (m *RankMemberInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RankMemberInfo.Unmarshal(m, b)
}
func (m *RankMemberInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RankMemberInfo.Marshal(b, m, deterministic)
}
func (m *RankMemberInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RankMemberInfo.Merge(m, src)
}
func (m *RankMemberInfo) XXX_Size() int {
	return xxx_messageInfo_RankMemberInfo.Size(m)
}
func (m *RankMemberInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RankMemberInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RankMemberInfo proto.InternalMessageInfo

func (m *RankMemberInfo) GetUserInfo() *KnightUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *RankMemberInfo) GetUserRankValue() *RankValueInfo {
	if m != nil {
		return m.UserRankValue
	}
	return nil
}

func (m *RankMemberInfo) GetUserExtInfo() *RankExtInfo {
	if m != nil {
		return m.UserExtInfo
	}
	return nil
}

func (m *RankMemberInfo) GetAnchorUserList() []*KnightUserInfo {
	if m != nil {
		return m.AnchorUserList
	}
	return nil
}

//真爱榜
type GetKnightLoveRankReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AnchorUid            uint32       `protobuf:"varint,3,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetKnightLoveRankReq) Reset()         { *m = GetKnightLoveRankReq{} }
func (m *GetKnightLoveRankReq) String() string { return proto.CompactTextString(m) }
func (*GetKnightLoveRankReq) ProtoMessage()    {}
func (*GetKnightLoveRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{7}
}

func (m *GetKnightLoveRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightLoveRankReq.Unmarshal(m, b)
}
func (m *GetKnightLoveRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightLoveRankReq.Marshal(b, m, deterministic)
}
func (m *GetKnightLoveRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightLoveRankReq.Merge(m, src)
}
func (m *GetKnightLoveRankReq) XXX_Size() int {
	return xxx_messageInfo_GetKnightLoveRankReq.Size(m)
}
func (m *GetKnightLoveRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightLoveRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightLoveRankReq proto.InternalMessageInfo

func (m *GetKnightLoveRankReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetKnightLoveRankReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetKnightLoveRankReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

type GetKnightLoveRankResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RankMemberList       []*RankMemberInfo `protobuf:"bytes,2,rep,name=rank_member_list,json=rankMemberList,proto3" json:"rank_member_list,omitempty"`
	MyRankValue          *MyRankValueInfo  `protobuf:"bytes,3,opt,name=my_rank_value,json=myRankValue,proto3" json:"my_rank_value,omitempty"`
	MyRankMemberInfo     *RankMemberInfo   `protobuf:"bytes,4,opt,name=my_rank_member_info,json=myRankMemberInfo,proto3" json:"my_rank_member_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetKnightLoveRankResp) Reset()         { *m = GetKnightLoveRankResp{} }
func (m *GetKnightLoveRankResp) String() string { return proto.CompactTextString(m) }
func (*GetKnightLoveRankResp) ProtoMessage()    {}
func (*GetKnightLoveRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{8}
}

func (m *GetKnightLoveRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightLoveRankResp.Unmarshal(m, b)
}
func (m *GetKnightLoveRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightLoveRankResp.Marshal(b, m, deterministic)
}
func (m *GetKnightLoveRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightLoveRankResp.Merge(m, src)
}
func (m *GetKnightLoveRankResp) XXX_Size() int {
	return xxx_messageInfo_GetKnightLoveRankResp.Size(m)
}
func (m *GetKnightLoveRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightLoveRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightLoveRankResp proto.InternalMessageInfo

func (m *GetKnightLoveRankResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetKnightLoveRankResp) GetRankMemberList() []*RankMemberInfo {
	if m != nil {
		return m.RankMemberList
	}
	return nil
}

func (m *GetKnightLoveRankResp) GetMyRankValue() *MyRankValueInfo {
	if m != nil {
		return m.MyRankValue
	}
	return nil
}

func (m *GetKnightLoveRankResp) GetMyRankMemberInfo() *RankMemberInfo {
	if m != nil {
		return m.MyRankMemberInfo
	}
	return nil
}

//周榜
type GetKnightWeekRankReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetKnightWeekRankReq) Reset()         { *m = GetKnightWeekRankReq{} }
func (m *GetKnightWeekRankReq) String() string { return proto.CompactTextString(m) }
func (*GetKnightWeekRankReq) ProtoMessage()    {}
func (*GetKnightWeekRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{9}
}

func (m *GetKnightWeekRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightWeekRankReq.Unmarshal(m, b)
}
func (m *GetKnightWeekRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightWeekRankReq.Marshal(b, m, deterministic)
}
func (m *GetKnightWeekRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightWeekRankReq.Merge(m, src)
}
func (m *GetKnightWeekRankReq) XXX_Size() int {
	return xxx_messageInfo_GetKnightWeekRankReq.Size(m)
}
func (m *GetKnightWeekRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightWeekRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightWeekRankReq proto.InternalMessageInfo

func (m *GetKnightWeekRankReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetKnightWeekRankReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetKnightWeekRankResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RankMemberList       []*RankMemberInfo `protobuf:"bytes,2,rep,name=rank_member_list,json=rankMemberList,proto3" json:"rank_member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetKnightWeekRankResp) Reset()         { *m = GetKnightWeekRankResp{} }
func (m *GetKnightWeekRankResp) String() string { return proto.CompactTextString(m) }
func (*GetKnightWeekRankResp) ProtoMessage()    {}
func (*GetKnightWeekRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{10}
}

func (m *GetKnightWeekRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightWeekRankResp.Unmarshal(m, b)
}
func (m *GetKnightWeekRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightWeekRankResp.Marshal(b, m, deterministic)
}
func (m *GetKnightWeekRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightWeekRankResp.Merge(m, src)
}
func (m *GetKnightWeekRankResp) XXX_Size() int {
	return xxx_messageInfo_GetKnightWeekRankResp.Size(m)
}
func (m *GetKnightWeekRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightWeekRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightWeekRankResp proto.InternalMessageInfo

func (m *GetKnightWeekRankResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetKnightWeekRankResp) GetRankMemberList() []*RankMemberInfo {
	if m != nil {
		return m.RankMemberList
	}
	return nil
}

// channel_.proto ChannelMsgType.KNIGHT_GROUP_CHANNEL_MSG = 264; // 骑士团
type KnightGroupChannelMsg struct {
	MsgType              KnightGroupChannelMsg_KnightChannelMsgType `protobuf:"varint,1,opt,name=msg_type,json=msgType,proto3,enum=ga.knightgrouplogic.KnightGroupChannelMsg_KnightChannelMsgType" json:"msg_type,omitempty"`
	MsgBin               []byte                                     `protobuf:"bytes,2,opt,name=msg_bin,json=msgBin,proto3" json:"msg_bin,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *KnightGroupChannelMsg) Reset()         { *m = KnightGroupChannelMsg{} }
func (m *KnightGroupChannelMsg) String() string { return proto.CompactTextString(m) }
func (*KnightGroupChannelMsg) ProtoMessage()    {}
func (*KnightGroupChannelMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{11}
}

func (m *KnightGroupChannelMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnightGroupChannelMsg.Unmarshal(m, b)
}
func (m *KnightGroupChannelMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnightGroupChannelMsg.Marshal(b, m, deterministic)
}
func (m *KnightGroupChannelMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnightGroupChannelMsg.Merge(m, src)
}
func (m *KnightGroupChannelMsg) XXX_Size() int {
	return xxx_messageInfo_KnightGroupChannelMsg.Size(m)
}
func (m *KnightGroupChannelMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_KnightGroupChannelMsg.DiscardUnknown(m)
}

var xxx_messageInfo_KnightGroupChannelMsg proto.InternalMessageInfo

func (m *KnightGroupChannelMsg) GetMsgType() KnightGroupChannelMsg_KnightChannelMsgType {
	if m != nil {
		return m.MsgType
	}
	return KnightGroupChannelMsg_ENUM_UNKOWN_TYPE
}

func (m *KnightGroupChannelMsg) GetMsgBin() []byte {
	if m != nil {
		return m.MsgBin
	}
	return nil
}

type KnightGroupJoinPushMsg struct {
	AnchorUser           *KnightUserInfo `protobuf:"bytes,1,opt,name=anchor_user,json=anchorUser,proto3" json:"anchor_user,omitempty"`
	KnightUser           *KnightUserInfo `protobuf:"bytes,2,opt,name=knight_user,json=knightUser,proto3" json:"knight_user,omitempty"`
	JoinNotifyMsg        string          `protobuf:"bytes,3,opt,name=join_notify_msg,json=joinNotifyMsg,proto3" json:"join_notify_msg,omitempty"`
	JoinTime             uint32          `protobuf:"varint,4,opt,name=join_time,json=joinTime,proto3" json:"join_time,omitempty"`
	ChannelJoinMsg       string          `protobuf:"bytes,5,opt,name=channel_join_msg,json=channelJoinMsg,proto3" json:"channel_join_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *KnightGroupJoinPushMsg) Reset()         { *m = KnightGroupJoinPushMsg{} }
func (m *KnightGroupJoinPushMsg) String() string { return proto.CompactTextString(m) }
func (*KnightGroupJoinPushMsg) ProtoMessage()    {}
func (*KnightGroupJoinPushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{12}
}

func (m *KnightGroupJoinPushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnightGroupJoinPushMsg.Unmarshal(m, b)
}
func (m *KnightGroupJoinPushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnightGroupJoinPushMsg.Marshal(b, m, deterministic)
}
func (m *KnightGroupJoinPushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnightGroupJoinPushMsg.Merge(m, src)
}
func (m *KnightGroupJoinPushMsg) XXX_Size() int {
	return xxx_messageInfo_KnightGroupJoinPushMsg.Size(m)
}
func (m *KnightGroupJoinPushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_KnightGroupJoinPushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_KnightGroupJoinPushMsg proto.InternalMessageInfo

func (m *KnightGroupJoinPushMsg) GetAnchorUser() *KnightUserInfo {
	if m != nil {
		return m.AnchorUser
	}
	return nil
}

func (m *KnightGroupJoinPushMsg) GetKnightUser() *KnightUserInfo {
	if m != nil {
		return m.KnightUser
	}
	return nil
}

func (m *KnightGroupJoinPushMsg) GetJoinNotifyMsg() string {
	if m != nil {
		return m.JoinNotifyMsg
	}
	return ""
}

func (m *KnightGroupJoinPushMsg) GetJoinTime() uint32 {
	if m != nil {
		return m.JoinTime
	}
	return 0
}

func (m *KnightGroupJoinPushMsg) GetChannelJoinMsg() string {
	if m != nil {
		return m.ChannelJoinMsg
	}
	return ""
}

type KnightGroupCampPushMsg struct {
	Version              uint32          `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	InChannelCnt         uint32          `protobuf:"varint,2,opt,name=in_channel_cnt,json=inChannelCnt,proto3" json:"in_channel_cnt,omitempty"`
	TopUser              *RankMemberInfo `protobuf:"bytes,3,opt,name=top_user,json=topUser,proto3" json:"top_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *KnightGroupCampPushMsg) Reset()         { *m = KnightGroupCampPushMsg{} }
func (m *KnightGroupCampPushMsg) String() string { return proto.CompactTextString(m) }
func (*KnightGroupCampPushMsg) ProtoMessage()    {}
func (*KnightGroupCampPushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{13}
}

func (m *KnightGroupCampPushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnightGroupCampPushMsg.Unmarshal(m, b)
}
func (m *KnightGroupCampPushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnightGroupCampPushMsg.Marshal(b, m, deterministic)
}
func (m *KnightGroupCampPushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnightGroupCampPushMsg.Merge(m, src)
}
func (m *KnightGroupCampPushMsg) XXX_Size() int {
	return xxx_messageInfo_KnightGroupCampPushMsg.Size(m)
}
func (m *KnightGroupCampPushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_KnightGroupCampPushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_KnightGroupCampPushMsg proto.InternalMessageInfo

func (m *KnightGroupCampPushMsg) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *KnightGroupCampPushMsg) GetInChannelCnt() uint32 {
	if m != nil {
		return m.InChannelCnt
	}
	return 0
}

func (m *KnightGroupCampPushMsg) GetTopUser() *RankMemberInfo {
	if m != nil {
		return m.TopUser
	}
	return nil
}

type KnightGroupChiefPushMsg struct {
	AnchorUser           *KnightUserInfo `protobuf:"bytes,1,opt,name=anchor_user,json=anchorUser,proto3" json:"anchor_user,omitempty"`
	KnightUser           *KnightUserInfo `protobuf:"bytes,2,opt,name=knight_user,json=knightUser,proto3" json:"knight_user,omitempty"`
	ChiefMsg             string          `protobuf:"bytes,3,opt,name=chief_msg,json=chiefMsg,proto3" json:"chief_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *KnightGroupChiefPushMsg) Reset()         { *m = KnightGroupChiefPushMsg{} }
func (m *KnightGroupChiefPushMsg) String() string { return proto.CompactTextString(m) }
func (*KnightGroupChiefPushMsg) ProtoMessage()    {}
func (*KnightGroupChiefPushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{14}
}

func (m *KnightGroupChiefPushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnightGroupChiefPushMsg.Unmarshal(m, b)
}
func (m *KnightGroupChiefPushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnightGroupChiefPushMsg.Marshal(b, m, deterministic)
}
func (m *KnightGroupChiefPushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnightGroupChiefPushMsg.Merge(m, src)
}
func (m *KnightGroupChiefPushMsg) XXX_Size() int {
	return xxx_messageInfo_KnightGroupChiefPushMsg.Size(m)
}
func (m *KnightGroupChiefPushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_KnightGroupChiefPushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_KnightGroupChiefPushMsg proto.InternalMessageInfo

func (m *KnightGroupChiefPushMsg) GetAnchorUser() *KnightUserInfo {
	if m != nil {
		return m.AnchorUser
	}
	return nil
}

func (m *KnightGroupChiefPushMsg) GetKnightUser() *KnightUserInfo {
	if m != nil {
		return m.KnightUser
	}
	return nil
}

func (m *KnightGroupChiefPushMsg) GetChiefMsg() string {
	if m != nil {
		return m.ChiefMsg
	}
	return ""
}

//任务
type GetKnightMissionReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetKnightMissionReq) Reset()         { *m = GetKnightMissionReq{} }
func (m *GetKnightMissionReq) String() string { return proto.CompactTextString(m) }
func (*GetKnightMissionReq) ProtoMessage()    {}
func (*GetKnightMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{15}
}

func (m *GetKnightMissionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightMissionReq.Unmarshal(m, b)
}
func (m *GetKnightMissionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightMissionReq.Marshal(b, m, deterministic)
}
func (m *GetKnightMissionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightMissionReq.Merge(m, src)
}
func (m *GetKnightMissionReq) XXX_Size() int {
	return xxx_messageInfo_GetKnightMissionReq.Size(m)
}
func (m *GetKnightMissionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightMissionReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightMissionReq proto.InternalMessageInfo

func (m *GetKnightMissionReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetKnightMissionReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetKnightMissionResp struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MemberList           []*KnightMissionMember `protobuf:"bytes,2,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	MissionText          []*KnightMissionText   `protobuf:"bytes,3,rep,name=mission_text,json=missionText,proto3" json:"mission_text,omitempty"`
	KnightNumber         uint32                 `protobuf:"varint,4,opt,name=knight_number,json=knightNumber,proto3" json:"knight_number,omitempty"`
	KnightMissionDesc    string                 `protobuf:"bytes,5,opt,name=knight_mission_desc,json=knightMissionDesc,proto3" json:"knight_mission_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetKnightMissionResp) Reset()         { *m = GetKnightMissionResp{} }
func (m *GetKnightMissionResp) String() string { return proto.CompactTextString(m) }
func (*GetKnightMissionResp) ProtoMessage()    {}
func (*GetKnightMissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{16}
}

func (m *GetKnightMissionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightMissionResp.Unmarshal(m, b)
}
func (m *GetKnightMissionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightMissionResp.Marshal(b, m, deterministic)
}
func (m *GetKnightMissionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightMissionResp.Merge(m, src)
}
func (m *GetKnightMissionResp) XXX_Size() int {
	return xxx_messageInfo_GetKnightMissionResp.Size(m)
}
func (m *GetKnightMissionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightMissionResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightMissionResp proto.InternalMessageInfo

func (m *GetKnightMissionResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetKnightMissionResp) GetMemberList() []*KnightMissionMember {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *GetKnightMissionResp) GetMissionText() []*KnightMissionText {
	if m != nil {
		return m.MissionText
	}
	return nil
}

func (m *GetKnightMissionResp) GetKnightNumber() uint32 {
	if m != nil {
		return m.KnightNumber
	}
	return 0
}

func (m *GetKnightMissionResp) GetKnightMissionDesc() string {
	if m != nil {
		return m.KnightMissionDesc
	}
	return ""
}

type KnightMission struct {
	TimeString           string   `protobuf:"bytes,1,opt,name=time_string,json=timeString,proto3" json:"time_string,omitempty"`
	TotalScore           uint32   `protobuf:"varint,2,opt,name=total_score,json=totalScore,proto3" json:"total_score,omitempty"`
	IsFinished           bool     `protobuf:"varint,3,opt,name=is_finished,json=isFinished,proto3" json:"is_finished,omitempty"`
	IsThisWeek           bool     `protobuf:"varint,4,opt,name=is_this_week,json=isThisWeek,proto3" json:"is_this_week,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KnightMission) Reset()         { *m = KnightMission{} }
func (m *KnightMission) String() string { return proto.CompactTextString(m) }
func (*KnightMission) ProtoMessage()    {}
func (*KnightMission) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{17}
}

func (m *KnightMission) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnightMission.Unmarshal(m, b)
}
func (m *KnightMission) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnightMission.Marshal(b, m, deterministic)
}
func (m *KnightMission) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnightMission.Merge(m, src)
}
func (m *KnightMission) XXX_Size() int {
	return xxx_messageInfo_KnightMission.Size(m)
}
func (m *KnightMission) XXX_DiscardUnknown() {
	xxx_messageInfo_KnightMission.DiscardUnknown(m)
}

var xxx_messageInfo_KnightMission proto.InternalMessageInfo

func (m *KnightMission) GetTimeString() string {
	if m != nil {
		return m.TimeString
	}
	return ""
}

func (m *KnightMission) GetTotalScore() uint32 {
	if m != nil {
		return m.TotalScore
	}
	return 0
}

func (m *KnightMission) GetIsFinished() bool {
	if m != nil {
		return m.IsFinished
	}
	return false
}

func (m *KnightMission) GetIsThisWeek() bool {
	if m != nil {
		return m.IsThisWeek
	}
	return false
}

type KnightMissionText struct {
	Text                 string   `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	KeyText              string   `protobuf:"bytes,2,opt,name=key_text,json=keyText,proto3" json:"key_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KnightMissionText) Reset()         { *m = KnightMissionText{} }
func (m *KnightMissionText) String() string { return proto.CompactTextString(m) }
func (*KnightMissionText) ProtoMessage()    {}
func (*KnightMissionText) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{18}
}

func (m *KnightMissionText) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnightMissionText.Unmarshal(m, b)
}
func (m *KnightMissionText) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnightMissionText.Marshal(b, m, deterministic)
}
func (m *KnightMissionText) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnightMissionText.Merge(m, src)
}
func (m *KnightMissionText) XXX_Size() int {
	return xxx_messageInfo_KnightMissionText.Size(m)
}
func (m *KnightMissionText) XXX_DiscardUnknown() {
	xxx_messageInfo_KnightMissionText.DiscardUnknown(m)
}

var xxx_messageInfo_KnightMissionText proto.InternalMessageInfo

func (m *KnightMissionText) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *KnightMissionText) GetKeyText() string {
	if m != nil {
		return m.KeyText
	}
	return ""
}

type KnightMissionMember struct {
	Nickname             string           `protobuf:"bytes,1,opt,name=nickname,proto3" json:"nickname,omitempty"`
	HeadMd5              string           `protobuf:"bytes,2,opt,name=head_md5,json=headMd5,proto3" json:"head_md5,omitempty"`
	MissionList          []*KnightMission `protobuf:"bytes,3,rep,name=mission_list,json=missionList,proto3" json:"mission_list,omitempty"`
	Sex                  uint32           `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Account              string           `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *KnightMissionMember) Reset()         { *m = KnightMissionMember{} }
func (m *KnightMissionMember) String() string { return proto.CompactTextString(m) }
func (*KnightMissionMember) ProtoMessage()    {}
func (*KnightMissionMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{19}
}

func (m *KnightMissionMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnightMissionMember.Unmarshal(m, b)
}
func (m *KnightMissionMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnightMissionMember.Marshal(b, m, deterministic)
}
func (m *KnightMissionMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnightMissionMember.Merge(m, src)
}
func (m *KnightMissionMember) XXX_Size() int {
	return xxx_messageInfo_KnightMissionMember.Size(m)
}
func (m *KnightMissionMember) XXX_DiscardUnknown() {
	xxx_messageInfo_KnightMissionMember.DiscardUnknown(m)
}

var xxx_messageInfo_KnightMissionMember proto.InternalMessageInfo

func (m *KnightMissionMember) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *KnightMissionMember) GetHeadMd5() string {
	if m != nil {
		return m.HeadMd5
	}
	return ""
}

func (m *KnightMissionMember) GetMissionList() []*KnightMission {
	if m != nil {
		return m.MissionList
	}
	return nil
}

func (m *KnightMissionMember) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *KnightMissionMember) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

//骑士营地入口信息
type GetKnightGroupCampInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AnchorUid            uint32       `protobuf:"varint,3,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetKnightGroupCampInfoReq) Reset()         { *m = GetKnightGroupCampInfoReq{} }
func (m *GetKnightGroupCampInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetKnightGroupCampInfoReq) ProtoMessage()    {}
func (*GetKnightGroupCampInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{20}
}

func (m *GetKnightGroupCampInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightGroupCampInfoReq.Unmarshal(m, b)
}
func (m *GetKnightGroupCampInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightGroupCampInfoReq.Marshal(b, m, deterministic)
}
func (m *GetKnightGroupCampInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightGroupCampInfoReq.Merge(m, src)
}
func (m *GetKnightGroupCampInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetKnightGroupCampInfoReq.Size(m)
}
func (m *GetKnightGroupCampInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightGroupCampInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightGroupCampInfoReq proto.InternalMessageInfo

func (m *GetKnightGroupCampInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetKnightGroupCampInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetKnightGroupCampInfoReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

type GetKnightGroupCampInfoResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	InChannelCnt         uint32          `protobuf:"varint,2,opt,name=in_channel_cnt,json=inChannelCnt,proto3" json:"in_channel_cnt,omitempty"`
	TopUser              *RankMemberInfo `protobuf:"bytes,3,opt,name=top_user,json=topUser,proto3" json:"top_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetKnightGroupCampInfoResp) Reset()         { *m = GetKnightGroupCampInfoResp{} }
func (m *GetKnightGroupCampInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetKnightGroupCampInfoResp) ProtoMessage()    {}
func (*GetKnightGroupCampInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_bf3d88cdc1081ae3, []int{21}
}

func (m *GetKnightGroupCampInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightGroupCampInfoResp.Unmarshal(m, b)
}
func (m *GetKnightGroupCampInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightGroupCampInfoResp.Marshal(b, m, deterministic)
}
func (m *GetKnightGroupCampInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightGroupCampInfoResp.Merge(m, src)
}
func (m *GetKnightGroupCampInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetKnightGroupCampInfoResp.Size(m)
}
func (m *GetKnightGroupCampInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightGroupCampInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightGroupCampInfoResp proto.InternalMessageInfo

func (m *GetKnightGroupCampInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetKnightGroupCampInfoResp) GetInChannelCnt() uint32 {
	if m != nil {
		return m.InChannelCnt
	}
	return 0
}

func (m *GetKnightGroupCampInfoResp) GetTopUser() *RankMemberInfo {
	if m != nil {
		return m.TopUser
	}
	return nil
}

func init() {
	proto.RegisterEnum("ga.knightgrouplogic.KnightGroupChannelMsg_KnightChannelMsgType", KnightGroupChannelMsg_KnightChannelMsgType_name, KnightGroupChannelMsg_KnightChannelMsgType_value)
	proto.RegisterType((*JoinKnightGroupReq)(nil), "ga.knightgrouplogic.JoinKnightGroupReq")
	proto.RegisterType((*JoinKnightGroupResp)(nil), "ga.knightgrouplogic.JoinKnightGroupResp")
	proto.RegisterType((*KnightUserInfo)(nil), "ga.knightgrouplogic.KnightUserInfo")
	proto.RegisterType((*RankValueInfo)(nil), "ga.knightgrouplogic.RankValueInfo")
	proto.RegisterType((*MyRankValueInfo)(nil), "ga.knightgrouplogic.MyRankValueInfo")
	proto.RegisterType((*RankExtInfo)(nil), "ga.knightgrouplogic.RankExtInfo")
	proto.RegisterType((*RankMemberInfo)(nil), "ga.knightgrouplogic.RankMemberInfo")
	proto.RegisterType((*GetKnightLoveRankReq)(nil), "ga.knightgrouplogic.GetKnightLoveRankReq")
	proto.RegisterType((*GetKnightLoveRankResp)(nil), "ga.knightgrouplogic.GetKnightLoveRankResp")
	proto.RegisterType((*GetKnightWeekRankReq)(nil), "ga.knightgrouplogic.GetKnightWeekRankReq")
	proto.RegisterType((*GetKnightWeekRankResp)(nil), "ga.knightgrouplogic.GetKnightWeekRankResp")
	proto.RegisterType((*KnightGroupChannelMsg)(nil), "ga.knightgrouplogic.KnightGroupChannelMsg")
	proto.RegisterType((*KnightGroupJoinPushMsg)(nil), "ga.knightgrouplogic.KnightGroupJoinPushMsg")
	proto.RegisterType((*KnightGroupCampPushMsg)(nil), "ga.knightgrouplogic.KnightGroupCampPushMsg")
	proto.RegisterType((*KnightGroupChiefPushMsg)(nil), "ga.knightgrouplogic.KnightGroupChiefPushMsg")
	proto.RegisterType((*GetKnightMissionReq)(nil), "ga.knightgrouplogic.GetKnightMissionReq")
	proto.RegisterType((*GetKnightMissionResp)(nil), "ga.knightgrouplogic.GetKnightMissionResp")
	proto.RegisterType((*KnightMission)(nil), "ga.knightgrouplogic.KnightMission")
	proto.RegisterType((*KnightMissionText)(nil), "ga.knightgrouplogic.KnightMissionText")
	proto.RegisterType((*KnightMissionMember)(nil), "ga.knightgrouplogic.KnightMissionMember")
	proto.RegisterType((*GetKnightGroupCampInfoReq)(nil), "ga.knightgrouplogic.GetKnightGroupCampInfoReq")
	proto.RegisterType((*GetKnightGroupCampInfoResp)(nil), "ga.knightgrouplogic.GetKnightGroupCampInfoResp")
}

func init() {
	proto.RegisterFile("knight-group-logic_.proto", fileDescriptor_bf3d88cdc1081ae3)
}

var fileDescriptor_bf3d88cdc1081ae3 = []byte{
	// 1468 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x57, 0x5f, 0x6f, 0x1b, 0x45,
	0x10, 0xe7, 0xec, 0xa4, 0xb1, 0xc7, 0xb1, 0x93, 0x6e, 0x52, 0xea, 0xb4, 0x20, 0xa2, 0x6b, 0xa9,
	0xc2, 0x43, 0x5d, 0x14, 0x54, 0x78, 0x83, 0x92, 0x3f, 0x6d, 0xdd, 0xc6, 0x49, 0x74, 0x4d, 0xa8,
	0x5a, 0x81, 0x56, 0x9b, 0xf3, 0xfa, 0xbc, 0xf8, 0x6e, 0xef, 0x7a, 0x7b, 0x4e, 0xe3, 0x8a, 0x17,
	0xde, 0x78, 0xe5, 0xcf, 0x03, 0x7c, 0x07, 0x10, 0x0f, 0x7c, 0x00, 0x24, 0xbe, 0x10, 0xe2, 0x1b,
	0xa0, 0xd9, 0xdd, 0xf3, 0x9f, 0x34, 0x4d, 0x1b, 0xa9, 0x2a, 0xbc, 0xed, 0xfc, 0x66, 0x76, 0x66,
	0x76, 0x7e, 0xbb, 0x73, 0x73, 0xb0, 0xd4, 0x93, 0x22, 0xe8, 0x66, 0xd7, 0x83, 0x34, 0xee, 0x27,
	0xd7, 0xc3, 0x38, 0x10, 0x3e, 0x6d, 0x24, 0x69, 0x9c, 0xc5, 0x64, 0x21, 0x60, 0x0d, 0xa3, 0xd5,
	0x4a, 0xad, 0xbb, 0x54, 0x0d, 0x18, 0x3d, 0x60, 0x8a, 0x1b, 0x1b, 0xf7, 0x17, 0x07, 0xc8, 0xbd,
	0x58, 0xc8, 0xfb, 0xda, 0xee, 0x0e, 0xda, 0x79, 0xfc, 0x09, 0xb9, 0x06, 0x25, 0x34, 0xa2, 0x29,
	0x7f, 0x52, 0x77, 0x96, 0x9d, 0x95, 0xca, 0x6a, 0xa5, 0x11, 0xb0, 0xc6, 0x1a, 0x53, 0xdc, 0xe3,
	0x4f, 0xbc, 0x99, 0x03, 0xb3, 0x20, 0xef, 0x02, 0x30, 0xe9, 0x77, 0xe3, 0x94, 0xf6, 0x45, 0xbb,
	0x5e, 0x58, 0x76, 0x56, 0xaa, 0x5e, 0xd9, 0x20, 0xfb, 0xa2, 0x8d, 0x6a, 0xbf, 0xcb, 0xa4, 0xe4,
	0x21, 0x15, 0xed, 0x7a, 0xd1, 0xa8, 0x2d, 0xd2, 0xd4, 0x6a, 0x93, 0x9f, 0xde, 0x3d, 0x65, 0xd4,
	0x06, 0xd9, 0x17, 0x6d, 0xf7, 0x16, 0x2c, 0x3c, 0x97, 0x9a, 0x4a, 0xc8, 0x07, 0x50, 0xb6, 0xb9,
	0xa9, 0xc4, 0x26, 0x37, 0x3b, 0x4a, 0x4e, 0x25, 0x5e, 0xe9, 0xc0, 0xae, 0xdc, 0x7f, 0x1c, 0xa8,
	0x99, 0xed, 0xfb, 0x8a, 0xa7, 0x4d, 0xd9, 0x89, 0xc9, 0x3c, 0x14, 0x31, 0x98, 0xa3, 0x83, 0xe1,
	0x92, 0xd4, 0x61, 0x86, 0xf9, 0x7e, 0xdc, 0x97, 0x99, 0x3e, 0x40, 0xd9, 0xcb, 0x45, 0xb2, 0x08,
	0xd3, 0x2c, 0x14, 0x4c, 0xe9, 0xcc, 0xcb, 0x9e, 0x11, 0xc8, 0x25, 0x28, 0x49, 0xe1, 0xf7, 0x24,
	0x8b, 0xb8, 0xce, 0xb9, 0xec, 0x0d, 0x65, 0xf4, 0xae, 0xf8, 0x51, 0x7d, 0x7a, 0xd9, 0x59, 0x99,
	0xf6, 0x70, 0x49, 0x3e, 0x86, 0x9a, 0x3d, 0x63, 0x92, 0xc6, 0x1d, 0x11, 0xf2, 0xfa, 0x39, 0x9d,
	0xf2, 0x1c, 0xa6, 0x8c, 0x59, 0xed, 0x1a, 0xd8, 0xab, 0x1a, 0x33, 0x2b, 0x92, 0x4f, 0x60, 0xce,
	0xee, 0x1b, 0xf4, 0x9e, 0x52, 0x21, 0x3b, 0x71, 0x7d, 0x66, 0x72, 0xe3, 0xfe, 0xfd, 0x87, 0x78,
	0xa2, 0x7c, 0xe3, 0xa3, 0xde, 0x53, 0x14, 0xdd, 0x1f, 0x1c, 0xa8, 0x7a, 0x4c, 0xf6, 0xbe, 0x60,
	0x61, 0x9f, 0xeb, 0x23, 0xbf, 0x07, 0x15, 0xeb, 0xaa, 0xcd, 0x06, 0xca, 0x1e, 0xdd, 0x56, 0x7e,
	0x83, 0x0d, 0x14, 0xb9, 0x36, 0x8c, 0x15, 0xf2, 0x8e, 0xb6, 0xb2, 0x54, 0x5a, 0xd7, 0x5b, 0xbc,
	0x83, 0x86, 0xc8, 0x57, 0x18, 0x1f, 0x72, 0x7a, 0x88, 0xae, 0x73, 0x3a, 0x11, 0xd1, 0xb1, 0xc8,
	0x12, 0x94, 0x84, 0xa2, 0x7e, 0x57, 0xf0, 0x8e, 0x2e, 0x4c, 0xc9, 0x9b, 0x11, 0x6a, 0x1d, 0x45,
	0xf7, 0x19, 0xcc, 0xb5, 0x06, 0x93, 0x59, 0x2d, 0xc2, 0xb4, 0x90, 0x6d, 0x7e, 0x64, 0xf3, 0x31,
	0x02, 0xa2, 0xc6, 0xbb, 0x49, 0xc0, 0x08, 0x3a, 0x30, 0xf3, 0x7b, 0xc7, 0x02, 0x33, 0xdf, 0xb8,
	0x23, 0xef, 0x00, 0x08, 0x45, 0x85, 0xa4, 0x29, 0x93, 0x3d, 0x1b, 0xba, 0x24, 0x54, 0x53, 0x62,
	0x44, 0xf7, 0x6f, 0x07, 0x2a, 0xb8, 0xd8, 0x3c, 0xca, 0x74, 0x60, 0x02, 0x53, 0xa9, 0xf0, 0xbb,
	0x36, 0xae, 0x5e, 0x63, 0x58, 0xbf, 0xcb, 0xd2, 0x28, 0x0f, 0xab, 0x05, 0x72, 0x19, 0xca, 0x1d,
	0x26, 0x69, 0xc8, 0x0f, 0x79, 0x68, 0xa3, 0x96, 0x3a, 0x4c, 0x6e, 0xa1, 0x4c, 0xde, 0x87, 0x9a,
	0x8c, 0x0f, 0x44, 0x28, 0xb2, 0x81, 0xb5, 0x30, 0x17, 0xb8, 0x9a, 0xa3, 0xc6, 0xec, 0x0a, 0x54,
	0xf3, 0x27, 0x60, 0xac, 0xa6, 0xb5, 0xd5, 0xac, 0x05, 0x8d, 0xd1, 0x87, 0x00, 0x49, 0xc8, 0x32,
	0x6e, 0x78, 0x36, 0x17, 0xe4, 0x3c, 0xf2, 0x7c, 0x9b, 0x49, 0xb5, 0x8b, 0x1a, 0xcd, 0x74, 0x39,
	0xc9, 0x97, 0x98, 0x9a, 0x50, 0x34, 0x96, 0xa1, 0x90, 0x5c, 0x5f, 0x0c, 0x7d, 0xe2, 0x1d, 0x2d,
	0xbb, 0x7f, 0x14, 0xa0, 0x86, 0x27, 0x6e, 0xf1, 0xe8, 0xc0, 0x5e, 0xfb, 0x5b, 0x50, 0xee, 0x2b,
	0x9e, 0x9a, 0x00, 0xe6, 0xd1, 0x5c, 0x69, 0x9c, 0xd0, 0x1f, 0x1a, 0x93, 0xcf, 0xc5, 0x2b, 0xf5,
	0xf3, 0x87, 0x73, 0x0f, 0xe6, 0xb4, 0x07, 0xac, 0x31, 0x1d, 0x71, 0x54, 0x59, 0x75, 0x4f, 0xf4,
	0x33, 0x41, 0xb6, 0x57, 0xc5, 0xad, 0x43, 0x88, 0x6c, 0x80, 0x06, 0x28, 0x3f, 0xca, 0x4c, 0x46,
	0x45, 0xed, 0x69, 0xf9, 0x85, 0x9e, 0x2c, 0x77, 0x5e, 0x05, 0xb7, 0xe5, 0x44, 0xb6, 0x60, 0x3e,
	0x6f, 0x3e, 0xe8, 0x2c, 0x14, 0x2a, 0xab, 0x4f, 0x2d, 0x17, 0x5f, 0xf5, 0x68, 0x35, 0xdb, 0xa7,
	0x14, 0x4f, 0xb7, 0x84, 0xca, 0xdc, 0x6f, 0x60, 0xf1, 0x0e, 0xcf, 0x8c, 0xd1, 0x56, 0x7c, 0xc8,
	0x31, 0xee, 0x19, 0x7b, 0xe1, 0x58, 0xb3, 0x2b, 0x9c, 0xd0, 0xec, 0xc6, 0x5a, 0x65, 0xf1, 0x58,
	0xab, 0x74, 0x7f, 0x2f, 0xc0, 0x85, 0x13, 0xc2, 0x9f, 0xa9, 0xdf, 0x61, 0x45, 0x34, 0x3d, 0x91,
	0x26, 0xde, 0x54, 0xa4, 0x70, 0x4a, 0x45, 0x26, 0x2f, 0x89, 0x57, 0x4b, 0x87, 0x32, 0x56, 0x84,
	0xdc, 0x85, 0x6a, 0x34, 0x18, 0x27, 0xdc, 0xd0, 0x74, 0xf5, 0x44, 0x5f, 0xc7, 0xde, 0xb7, 0x57,
	0x89, 0x46, 0x00, 0xf1, 0x60, 0x21, 0xf7, 0x64, 0x73, 0xd3, 0xb4, 0x4f, 0x9d, 0x72, 0x11, 0x8f,
	0xe5, 0x36, 0x6f, 0xdc, 0x8d, 0x10, 0xf7, 0xab, 0x31, 0xbe, 0x1e, 0x72, 0xde, 0x7b, 0xbd, 0x7c,
	0xb9, 0xdf, 0x3b, 0x63, 0x84, 0x8c, 0xfc, 0xff, 0x97, 0x84, 0xb8, 0xdf, 0x15, 0xe0, 0xc2, 0xd8,
	0xe7, 0x70, 0xdd, 0x24, 0xdb, 0x52, 0x01, 0x79, 0x0c, 0xa5, 0x48, 0x05, 0x34, 0x1b, 0x24, 0x5c,
	0xa7, 0x54, 0x5b, 0xfd, 0xec, 0x94, 0x37, 0x70, 0x6c, 0xb7, 0x45, 0x47, 0xc0, 0xde, 0x20, 0xe1,
	0xde, 0x4c, 0x64, 0x16, 0xe4, 0x22, 0xe0, 0x92, 0x1e, 0x08, 0xa9, 0xab, 0x34, 0xeb, 0x9d, 0x8b,
	0x54, 0xb0, 0x26, 0xa4, 0xfb, 0x0c, 0x16, 0x4f, 0xda, 0x49, 0x16, 0x61, 0x7e, 0x73, 0x7b, 0xbf,
	0x45, 0xf7, 0xb7, 0xef, 0xef, 0x3c, 0xdc, 0xa6, 0x7b, 0x8f, 0x76, 0x37, 0xe7, 0xdf, 0x22, 0x75,
	0x58, 0xd4, 0xe8, 0xbd, 0x9d, 0xe6, 0x36, 0xbd, 0xe3, 0xed, 0xec, 0xef, 0x1a, 0x8d, 0x43, 0x2e,
	0xc2, 0xc2, 0x48, 0xb3, 0xfe, 0x79, 0xcb, 0x2a, 0x0a, 0x64, 0x09, 0x2e, 0x68, 0x85, 0xb1, 0x5e,
	0xbf, 0xdb, 0xdc, 0xbc, 0x6d, 0x54, 0x45, 0xf7, 0xc7, 0x02, 0xbc, 0x3d, 0x76, 0x18, 0x1c, 0x14,
	0x76, 0xfb, 0xaa, 0x8b, 0xb5, 0xd8, 0x80, 0xca, 0x58, 0x5f, 0x38, 0x4b, 0xb7, 0x83, 0x51, 0x4b,
	0x40, 0x2f, 0xf9, 0x70, 0x82, 0x5e, 0x0a, 0x67, 0xf0, 0xd2, 0x1b, 0xca, 0xf8, 0x69, 0xfd, 0x3a,
	0x16, 0x92, 0xca, 0x38, 0x13, 0x9d, 0x01, 0x8d, 0x54, 0x60, 0x87, 0x89, 0x2a, 0xc2, 0xdb, 0x1a,
	0xc5, 0x9c, 0x2f, 0x43, 0x59, 0xdb, 0x65, 0xc2, 0x4e, 0x15, 0x55, 0xaf, 0x84, 0xc0, 0x9e, 0x88,
	0x38, 0x59, 0x81, 0xf9, 0xfc, 0xa6, 0x6a, 0x23, 0xf4, 0x32, 0xad, 0xbd, 0xd4, 0x2c, 0x8e, 0xc7,
	0x6f, 0xa9, 0xc0, 0xfd, 0xd9, 0x99, 0xa8, 0xca, 0x3a, 0x8b, 0x92, 0xbc, 0x2a, 0x75, 0x98, 0x39,
	0xe4, 0xa9, 0x12, 0xb1, 0xb4, 0x5f, 0xbe, 0x5c, 0x24, 0x57, 0xa1, 0x26, 0x24, 0xcd, 0x23, 0xf8,
	0x76, 0x0e, 0xaa, 0x7a, 0xb3, 0x42, 0x5a, 0x62, 0xd7, 0x65, 0x46, 0x3e, 0x85, 0x52, 0x16, 0x27,
	0xa6, 0x18, 0xc5, 0x57, 0x7f, 0xb7, 0x33, 0x59, 0x9c, 0x60, 0x25, 0xdc, 0xbf, 0x1c, 0xb8, 0x38,
	0x71, 0xfb, 0x04, 0xef, 0xfc, 0x1f, 0x19, 0xbb, 0x0c, 0x65, 0x3d, 0xc2, 0x8c, 0x71, 0x55, 0xd2,
	0x00, 0xd6, 0xf7, 0x4b, 0x58, 0x18, 0xf6, 0x84, 0x96, 0x50, 0x58, 0xbe, 0xd7, 0xd8, 0x72, 0x7e,
	0x2d, 0x8c, 0xb5, 0xb4, 0xa1, 0xfb, 0xb3, 0x75, 0x9c, 0x26, 0x54, 0x9e, 0x6f, 0x36, 0x2b, 0xa7,
	0x14, 0xc1, 0xc6, 0x31, 0x94, 0x79, 0x10, 0x8d, 0xda, 0x7f, 0x13, 0x66, 0x23, 0xa3, 0xa4, 0x19,
	0x3f, 0xca, 0xea, 0x45, 0xed, 0xeb, 0xda, 0xcb, 0x7d, 0xed, 0xf1, 0xa3, 0xcc, 0xab, 0x44, 0x23,
	0x01, 0xa7, 0x20, 0x4b, 0x8d, 0xec, 0xa3, 0x7f, 0x7b, 0xc5, 0x67, 0x0d, 0xb8, 0xad, 0x31, 0xd2,
	0x80, 0x05, 0x6b, 0x94, 0x87, 0x6d, 0x73, 0xe5, 0xdb, 0x9b, 0x7e, 0xbe, 0x37, 0x1e, 0x61, 0x83,
	0x2b, 0xdf, 0xfd, 0xc9, 0x81, 0xea, 0x44, 0x5c, 0x9c, 0x74, 0xf1, 0x01, 0x51, 0x95, 0xa5, 0x42,
	0x06, 0xba, 0x52, 0x65, 0x0f, 0x10, 0x7a, 0xa0, 0x11, 0x6d, 0x10, 0x67, 0x2c, 0xa4, 0xca, 0x8f,
	0xd3, 0x7c, 0xc8, 0x04, 0x0d, 0x3d, 0x40, 0x04, 0x0d, 0x84, 0xa2, 0x1d, 0x21, 0x85, 0xea, 0x72,
	0xf3, 0x99, 0x2e, 0x79, 0x20, 0xd4, 0x6d, 0x8b, 0x90, 0x65, 0x98, 0x15, 0x8a, 0x66, 0x5d, 0xa1,
	0xe8, 0x53, 0xce, 0xf3, 0x69, 0x13, 0x84, 0xda, 0xeb, 0x0a, 0x85, 0x9f, 0x09, 0x77, 0x0d, 0xce,
	0x3f, 0x57, 0x0d, 0x1c, 0x3a, 0x75, 0x0d, 0x4d, 0x4a, 0x7a, 0x8d, 0xf3, 0x72, 0x8f, 0x0f, 0x4c,
	0x6d, 0xed, 0x9f, 0x47, 0x8f, 0x0f, 0xd0, 0xdc, 0xfd, 0xd3, 0x81, 0x85, 0x13, 0xe8, 0x99, 0xf8,
	0xf7, 0x70, 0x8e, 0xfd, 0x7b, 0x2c, 0x41, 0xa9, 0xcb, 0x59, 0x9b, 0x46, 0xed, 0x9b, 0xb9, 0x3b,
	0x94, 0x5b, 0xed, 0x9b, 0x64, 0x73, 0xc4, 0xa4, 0xbe, 0x15, 0x86, 0x49, 0xf7, 0xe5, 0x4c, 0x0e,
	0x59, 0xd4, 0x17, 0xc2, 0xfe, 0xdd, 0x18, 0xee, 0xf4, 0xdf, 0xcd, 0xd8, 0xbf, 0xd3, 0xf4, 0xc4,
	0xbf, 0x93, 0xfb, 0xad, 0x03, 0x4b, 0xc3, 0xbb, 0x3c, 0x6c, 0x46, 0xfa, 0xb5, 0xbd, 0xb1, 0x99,
	0xea, 0x37, 0x07, 0x2e, 0xbd, 0x28, 0x87, 0xb3, 0xbd, 0xaa, 0x37, 0xd2, 0x22, 0xd7, 0x9a, 0x50,
	0xf7, 0xe3, 0xa8, 0x31, 0x10, 0x83, 0xb8, 0x8f, 0x1b, 0xa3, 0xb8, 0xcd, 0x43, 0xf3, 0xa3, 0xfe,
	0xf8, 0x7a, 0x10, 0x87, 0x4c, 0x06, 0x8d, 0x9b, 0xab, 0x59, 0xd6, 0xf0, 0xe3, 0xe8, 0x86, 0x86,
	0xfd, 0x38, 0xbc, 0xc1, 0x92, 0xe4, 0xc6, 0xf1, 0x10, 0x07, 0xe7, 0xb4, 0xfa, 0xa3, 0x7f, 0x03,
	0x00, 0x00, 0xff, 0xff, 0xca, 0x54, 0x3e, 0x94, 0x1f, 0x10, 0x00, 0x00,
}
