// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-performance_.proto

package channel_performance // import "golang.52tt.com/protocol/app/channel-performance"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChannelPerformanceStageRange int32

const (
	ChannelPerformanceStageRange_BEGIN ChannelPerformanceStageRange = 0
	ChannelPerformanceStageRange_END   ChannelPerformanceStageRange = 999
)

var ChannelPerformanceStageRange_name = map[int32]string{
	0:   "BEGIN",
	999: "END",
}
var ChannelPerformanceStageRange_value = map[string]int32{
	"BEGIN": 0,
	"END":   999,
}

func (x ChannelPerformanceStageRange) String() string {
	return proto.EnumName(ChannelPerformanceStageRange_name, int32(x))
}
func (ChannelPerformanceStageRange) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_performance__0f51caf88c4189c3, []int{0}
}

type ChannelPerformanceGuest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Introduction         string   `protobuf:"bytes,4,opt,name=introduction,proto3" json:"introduction,omitempty"`
	Sex                  int32    `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelPerformanceGuest) Reset()         { *m = ChannelPerformanceGuest{} }
func (m *ChannelPerformanceGuest) String() string { return proto.CompactTextString(m) }
func (*ChannelPerformanceGuest) ProtoMessage()    {}
func (*ChannelPerformanceGuest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_performance__0f51caf88c4189c3, []int{0}
}
func (m *ChannelPerformanceGuest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPerformanceGuest.Unmarshal(m, b)
}
func (m *ChannelPerformanceGuest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPerformanceGuest.Marshal(b, m, deterministic)
}
func (dst *ChannelPerformanceGuest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPerformanceGuest.Merge(dst, src)
}
func (m *ChannelPerformanceGuest) XXX_Size() int {
	return xxx_messageInfo_ChannelPerformanceGuest.Size(m)
}
func (m *ChannelPerformanceGuest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPerformanceGuest.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPerformanceGuest proto.InternalMessageInfo

func (m *ChannelPerformanceGuest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelPerformanceGuest) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ChannelPerformanceGuest) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ChannelPerformanceGuest) GetIntroduction() string {
	if m != nil {
		return m.Introduction
	}
	return ""
}

func (m *ChannelPerformanceGuest) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type ChannelPerformanceStage struct {
	Id                   uint32                     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string                     `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Icon                 string                     `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	BeginTime            string                     `protobuf:"bytes,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	IntroductionImg      string                     `protobuf:"bytes,5,opt,name=introduction_img,json=introductionImg,proto3" json:"introduction_img,omitempty"`
	GuestList            []*ChannelPerformanceGuest `protobuf:"bytes,6,rep,name=guest_list,json=guestList,proto3" json:"guest_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *ChannelPerformanceStage) Reset()         { *m = ChannelPerformanceStage{} }
func (m *ChannelPerformanceStage) String() string { return proto.CompactTextString(m) }
func (*ChannelPerformanceStage) ProtoMessage()    {}
func (*ChannelPerformanceStage) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_performance__0f51caf88c4189c3, []int{1}
}
func (m *ChannelPerformanceStage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPerformanceStage.Unmarshal(m, b)
}
func (m *ChannelPerformanceStage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPerformanceStage.Marshal(b, m, deterministic)
}
func (dst *ChannelPerformanceStage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPerformanceStage.Merge(dst, src)
}
func (m *ChannelPerformanceStage) XXX_Size() int {
	return xxx_messageInfo_ChannelPerformanceStage.Size(m)
}
func (m *ChannelPerformanceStage) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPerformanceStage.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPerformanceStage proto.InternalMessageInfo

func (m *ChannelPerformanceStage) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChannelPerformanceStage) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ChannelPerformanceStage) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ChannelPerformanceStage) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *ChannelPerformanceStage) GetIntroductionImg() string {
	if m != nil {
		return m.IntroductionImg
	}
	return ""
}

func (m *ChannelPerformanceStage) GetGuestList() []*ChannelPerformanceGuest {
	if m != nil {
		return m.GuestList
	}
	return nil
}

type ChannelPerformanceInfo struct {
	ChannelId             uint32                     `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Title                 string                     `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Icon                  string                     `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	CurrentStageId        uint32                     `protobuf:"varint,4,opt,name=current_stage_id,json=currentStageId,proto3" json:"current_stage_id,omitempty"`
	StageList             []*ChannelPerformanceStage `protobuf:"bytes,5,rep,name=stage_list,json=stageList,proto3" json:"stage_list,omitempty"`
	ChannelTag            string                     `protobuf:"bytes,6,opt,name=channel_tag,json=channelTag,proto3" json:"channel_tag,omitempty"`
	StageUpdateTime       int64                      `protobuf:"varint,7,opt,name=stage_update_time,json=stageUpdateTime,proto3" json:"stage_update_time,omitempty"`
	PerformanceUpdateTime int64                      `protobuf:"varint,8,opt,name=performance_update_time,json=performanceUpdateTime,proto3" json:"performance_update_time,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                   `json:"-"`
	XXX_unrecognized      []byte                     `json:"-"`
	XXX_sizecache         int32                      `json:"-"`
}

func (m *ChannelPerformanceInfo) Reset()         { *m = ChannelPerformanceInfo{} }
func (m *ChannelPerformanceInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelPerformanceInfo) ProtoMessage()    {}
func (*ChannelPerformanceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_performance__0f51caf88c4189c3, []int{2}
}
func (m *ChannelPerformanceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPerformanceInfo.Unmarshal(m, b)
}
func (m *ChannelPerformanceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPerformanceInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelPerformanceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPerformanceInfo.Merge(dst, src)
}
func (m *ChannelPerformanceInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelPerformanceInfo.Size(m)
}
func (m *ChannelPerformanceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPerformanceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPerformanceInfo proto.InternalMessageInfo

func (m *ChannelPerformanceInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelPerformanceInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ChannelPerformanceInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ChannelPerformanceInfo) GetCurrentStageId() uint32 {
	if m != nil {
		return m.CurrentStageId
	}
	return 0
}

func (m *ChannelPerformanceInfo) GetStageList() []*ChannelPerformanceStage {
	if m != nil {
		return m.StageList
	}
	return nil
}

func (m *ChannelPerformanceInfo) GetChannelTag() string {
	if m != nil {
		return m.ChannelTag
	}
	return ""
}

func (m *ChannelPerformanceInfo) GetStageUpdateTime() int64 {
	if m != nil {
		return m.StageUpdateTime
	}
	return 0
}

func (m *ChannelPerformanceInfo) GetPerformanceUpdateTime() int64 {
	if m != nil {
		return m.PerformanceUpdateTime
	}
	return 0
}

type GetChannelPerformanceReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelPerformanceReq) Reset()         { *m = GetChannelPerformanceReq{} }
func (m *GetChannelPerformanceReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelPerformanceReq) ProtoMessage()    {}
func (*GetChannelPerformanceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_performance__0f51caf88c4189c3, []int{3}
}
func (m *GetChannelPerformanceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPerformanceReq.Unmarshal(m, b)
}
func (m *GetChannelPerformanceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPerformanceReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelPerformanceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPerformanceReq.Merge(dst, src)
}
func (m *GetChannelPerformanceReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelPerformanceReq.Size(m)
}
func (m *GetChannelPerformanceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPerformanceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPerformanceReq proto.InternalMessageInfo

func (m *GetChannelPerformanceReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelPerformanceReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelPerformanceResp struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Performance          *ChannelPerformanceInfo `protobuf:"bytes,2,opt,name=performance,proto3" json:"performance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetChannelPerformanceResp) Reset()         { *m = GetChannelPerformanceResp{} }
func (m *GetChannelPerformanceResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelPerformanceResp) ProtoMessage()    {}
func (*GetChannelPerformanceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_performance__0f51caf88c4189c3, []int{4}
}
func (m *GetChannelPerformanceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPerformanceResp.Unmarshal(m, b)
}
func (m *GetChannelPerformanceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPerformanceResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelPerformanceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPerformanceResp.Merge(dst, src)
}
func (m *GetChannelPerformanceResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelPerformanceResp.Size(m)
}
func (m *GetChannelPerformanceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPerformanceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPerformanceResp proto.InternalMessageInfo

func (m *GetChannelPerformanceResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelPerformanceResp) GetPerformance() *ChannelPerformanceInfo {
	if m != nil {
		return m.Performance
	}
	return nil
}

type SetCurrentChannelPerformanceStageReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CurrentStageId       uint32       `protobuf:"varint,3,opt,name=current_stage_id,json=currentStageId,proto3" json:"current_stage_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetCurrentChannelPerformanceStageReq) Reset()         { *m = SetCurrentChannelPerformanceStageReq{} }
func (m *SetCurrentChannelPerformanceStageReq) String() string { return proto.CompactTextString(m) }
func (*SetCurrentChannelPerformanceStageReq) ProtoMessage()    {}
func (*SetCurrentChannelPerformanceStageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_performance__0f51caf88c4189c3, []int{5}
}
func (m *SetCurrentChannelPerformanceStageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCurrentChannelPerformanceStageReq.Unmarshal(m, b)
}
func (m *SetCurrentChannelPerformanceStageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCurrentChannelPerformanceStageReq.Marshal(b, m, deterministic)
}
func (dst *SetCurrentChannelPerformanceStageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCurrentChannelPerformanceStageReq.Merge(dst, src)
}
func (m *SetCurrentChannelPerformanceStageReq) XXX_Size() int {
	return xxx_messageInfo_SetCurrentChannelPerformanceStageReq.Size(m)
}
func (m *SetCurrentChannelPerformanceStageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCurrentChannelPerformanceStageReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetCurrentChannelPerformanceStageReq proto.InternalMessageInfo

func (m *SetCurrentChannelPerformanceStageReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetCurrentChannelPerformanceStageReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetCurrentChannelPerformanceStageReq) GetCurrentStageId() uint32 {
	if m != nil {
		return m.CurrentStageId
	}
	return 0
}

type SetCurrentChannelPerformanceStageResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetCurrentChannelPerformanceStageResp) Reset()         { *m = SetCurrentChannelPerformanceStageResp{} }
func (m *SetCurrentChannelPerformanceStageResp) String() string { return proto.CompactTextString(m) }
func (*SetCurrentChannelPerformanceStageResp) ProtoMessage()    {}
func (*SetCurrentChannelPerformanceStageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_performance__0f51caf88c4189c3, []int{6}
}
func (m *SetCurrentChannelPerformanceStageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCurrentChannelPerformanceStageResp.Unmarshal(m, b)
}
func (m *SetCurrentChannelPerformanceStageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCurrentChannelPerformanceStageResp.Marshal(b, m, deterministic)
}
func (dst *SetCurrentChannelPerformanceStageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCurrentChannelPerformanceStageResp.Merge(dst, src)
}
func (m *SetCurrentChannelPerformanceStageResp) XXX_Size() int {
	return xxx_messageInfo_SetCurrentChannelPerformanceStageResp.Size(m)
}
func (m *SetCurrentChannelPerformanceStageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCurrentChannelPerformanceStageResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetCurrentChannelPerformanceStageResp proto.InternalMessageInfo

func (m *SetCurrentChannelPerformanceStageResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 房间节目单变更采用
type ChannelPerformanceChange struct {
	Performance          *ChannelPerformanceInfo `protobuf:"bytes,1,opt,name=performance,proto3" json:"performance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ChannelPerformanceChange) Reset()         { *m = ChannelPerformanceChange{} }
func (m *ChannelPerformanceChange) String() string { return proto.CompactTextString(m) }
func (*ChannelPerformanceChange) ProtoMessage()    {}
func (*ChannelPerformanceChange) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_performance__0f51caf88c4189c3, []int{7}
}
func (m *ChannelPerformanceChange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPerformanceChange.Unmarshal(m, b)
}
func (m *ChannelPerformanceChange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPerformanceChange.Marshal(b, m, deterministic)
}
func (dst *ChannelPerformanceChange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPerformanceChange.Merge(dst, src)
}
func (m *ChannelPerformanceChange) XXX_Size() int {
	return xxx_messageInfo_ChannelPerformanceChange.Size(m)
}
func (m *ChannelPerformanceChange) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPerformanceChange.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPerformanceChange proto.InternalMessageInfo

func (m *ChannelPerformanceChange) GetPerformance() *ChannelPerformanceInfo {
	if m != nil {
		return m.Performance
	}
	return nil
}

// 房间当前节目变更
type ChannelPerformanceStageChange struct {
	ChannelId             uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CurrentStageId        uint32   `protobuf:"varint,2,opt,name=current_stage_id,json=currentStageId,proto3" json:"current_stage_id,omitempty"`
	StageUpdateTime       int64    `protobuf:"varint,3,opt,name=stage_update_time,json=stageUpdateTime,proto3" json:"stage_update_time,omitempty"`
	PerformanceUpdateTime int64    `protobuf:"varint,4,opt,name=performance_update_time,json=performanceUpdateTime,proto3" json:"performance_update_time,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *ChannelPerformanceStageChange) Reset()         { *m = ChannelPerformanceStageChange{} }
func (m *ChannelPerformanceStageChange) String() string { return proto.CompactTextString(m) }
func (*ChannelPerformanceStageChange) ProtoMessage()    {}
func (*ChannelPerformanceStageChange) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_performance__0f51caf88c4189c3, []int{8}
}
func (m *ChannelPerformanceStageChange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPerformanceStageChange.Unmarshal(m, b)
}
func (m *ChannelPerformanceStageChange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPerformanceStageChange.Marshal(b, m, deterministic)
}
func (dst *ChannelPerformanceStageChange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPerformanceStageChange.Merge(dst, src)
}
func (m *ChannelPerformanceStageChange) XXX_Size() int {
	return xxx_messageInfo_ChannelPerformanceStageChange.Size(m)
}
func (m *ChannelPerformanceStageChange) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPerformanceStageChange.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPerformanceStageChange proto.InternalMessageInfo

func (m *ChannelPerformanceStageChange) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelPerformanceStageChange) GetCurrentStageId() uint32 {
	if m != nil {
		return m.CurrentStageId
	}
	return 0
}

func (m *ChannelPerformanceStageChange) GetStageUpdateTime() int64 {
	if m != nil {
		return m.StageUpdateTime
	}
	return 0
}

func (m *ChannelPerformanceStageChange) GetPerformanceUpdateTime() int64 {
	if m != nil {
		return m.PerformanceUpdateTime
	}
	return 0
}

func init() {
	proto.RegisterType((*ChannelPerformanceGuest)(nil), "ga.channel_performance.ChannelPerformanceGuest")
	proto.RegisterType((*ChannelPerformanceStage)(nil), "ga.channel_performance.ChannelPerformanceStage")
	proto.RegisterType((*ChannelPerformanceInfo)(nil), "ga.channel_performance.ChannelPerformanceInfo")
	proto.RegisterType((*GetChannelPerformanceReq)(nil), "ga.channel_performance.GetChannelPerformanceReq")
	proto.RegisterType((*GetChannelPerformanceResp)(nil), "ga.channel_performance.GetChannelPerformanceResp")
	proto.RegisterType((*SetCurrentChannelPerformanceStageReq)(nil), "ga.channel_performance.SetCurrentChannelPerformanceStageReq")
	proto.RegisterType((*SetCurrentChannelPerformanceStageResp)(nil), "ga.channel_performance.SetCurrentChannelPerformanceStageResp")
	proto.RegisterType((*ChannelPerformanceChange)(nil), "ga.channel_performance.ChannelPerformanceChange")
	proto.RegisterType((*ChannelPerformanceStageChange)(nil), "ga.channel_performance.ChannelPerformanceStageChange")
	proto.RegisterEnum("ga.channel_performance.ChannelPerformanceStageRange", ChannelPerformanceStageRange_name, ChannelPerformanceStageRange_value)
}

func init() {
	proto.RegisterFile("channel-performance_.proto", fileDescriptor_channel_performance__0f51caf88c4189c3)
}

var fileDescriptor_channel_performance__0f51caf88c4189c3 = []byte{
	// 639 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x55, 0xef, 0x6e, 0xd3, 0x30,
	0x10, 0x27, 0x4d, 0xbb, 0x36, 0xd7, 0xfd, 0x29, 0x16, 0x6c, 0x61, 0x62, 0xa2, 0x8a, 0x00, 0x75,
	0x93, 0x48, 0x51, 0x27, 0x78, 0x80, 0x8d, 0x69, 0xaa, 0x34, 0x4d, 0x93, 0x37, 0xbe, 0xf0, 0x25,
	0x72, 0x53, 0xcf, 0x58, 0x24, 0x76, 0x96, 0x38, 0x12, 0x7b, 0x12, 0x78, 0x28, 0x1e, 0x82, 0x6f,
	0x7c, 0xe2, 0x1d, 0x90, 0x9d, 0x74, 0x0b, 0x6b, 0x0a, 0xab, 0xe0, 0xdb, 0xf9, 0xee, 0x7c, 0xf7,
	0xfb, 0xfd, 0xce, 0x27, 0xc3, 0x76, 0xf8, 0x91, 0x08, 0x41, 0xa3, 0x57, 0x09, 0x4d, 0x2f, 0x65,
	0x1a, 0x13, 0x11, 0xd2, 0xc0, 0x4f, 0x52, 0xa9, 0x24, 0xda, 0x64, 0xc4, 0x2f, 0xc3, 0x41, 0x25,
	0xbc, 0xbd, 0xc6, 0x48, 0x30, 0x21, 0x19, 0x2d, 0xd2, 0xbc, 0x2f, 0x16, 0x6c, 0x1d, 0x16, 0x69,
	0x67, 0xb7, 0x59, 0xc7, 0x39, 0xcd, 0x14, 0xea, 0x81, 0x9d, 0xf3, 0xa9, 0x6b, 0xf5, 0xad, 0xc1,
	0x1a, 0xd6, 0x26, 0x72, 0xa1, 0x4d, 0xc2, 0x50, 0xe6, 0x42, 0xb9, 0x8d, 0xbe, 0x35, 0x70, 0xf0,
	0xec, 0x88, 0xb6, 0xa1, 0x23, 0x78, 0xf8, 0x49, 0x90, 0x98, 0xba, 0xb6, 0x09, 0xdd, 0x9c, 0x91,
	0x07, 0xab, 0x5c, 0xa8, 0x54, 0x4e, 0xf3, 0x50, 0x71, 0x29, 0xdc, 0xa6, 0x89, 0xff, 0xe6, 0xd3,
	0xbd, 0x32, 0xfa, 0xd9, 0x6d, 0xf5, 0xad, 0x41, 0x0b, 0x6b, 0xd3, 0xfb, 0x59, 0x8b, 0xec, 0x5c,
	0x11, 0x46, 0xd1, 0x3a, 0x34, 0x6e, 0x80, 0x35, 0xf8, 0x14, 0x3d, 0x82, 0x96, 0xe2, 0x2a, 0xa2,
	0x25, 0xaa, 0xe2, 0x80, 0x10, 0x34, 0x79, 0x28, 0x45, 0x89, 0xc7, 0xd8, 0x68, 0x07, 0x60, 0x42,
	0x19, 0x17, 0x81, 0xe2, 0x31, 0x2d, 0x91, 0x38, 0xc6, 0x73, 0xc1, 0x63, 0x8a, 0x76, 0xa1, 0x57,
	0x85, 0x15, 0xf0, 0x98, 0x19, 0x4c, 0x0e, 0xde, 0xa8, 0xfa, 0xc7, 0x31, 0x43, 0xa7, 0x00, 0x4c,
	0xcb, 0x14, 0x44, 0x3c, 0x53, 0xee, 0x4a, 0xdf, 0x1e, 0x74, 0x47, 0x43, 0xbf, 0x5e, 0x75, 0x7f,
	0x81, 0xc4, 0xd8, 0x31, 0x25, 0x4e, 0x78, 0xa6, 0xbc, 0xef, 0x0d, 0xd8, 0x9c, 0x4f, 0x1b, 0x8b,
	0x4b, 0xa9, 0x41, 0xcf, 0x8a, 0xde, 0xd0, 0x76, 0x4a, 0xcf, 0x78, 0x19, 0xf6, 0x03, 0xe8, 0x85,
	0x79, 0x9a, 0x52, 0xa1, 0x82, 0x4c, 0x0b, 0xa9, 0xcb, 0x35, 0x4d, 0xb9, 0xf5, 0xd2, 0x6f, 0xf4,
	0x1d, 0x4f, 0x35, 0xbb, 0x22, 0xc3, 0xb0, 0x6b, 0x2d, 0xcb, 0xce, 0x94, 0xc1, 0x8e, 0x29, 0xa1,
	0xd9, 0xa1, 0x67, 0xd0, 0x9d, 0xdd, 0x54, 0x84, 0xb9, 0x2b, 0x06, 0xd4, 0x8c, 0xd5, 0x05, 0x61,
	0x68, 0x0f, 0x1e, 0x16, 0x0d, 0xf3, 0x64, 0x4a, 0x14, 0x2d, 0xe6, 0xd3, 0xee, 0x5b, 0x03, 0x1b,
	0x6f, 0x98, 0xc0, 0x7b, 0xe3, 0x37, 0x53, 0x7a, 0x0b, 0x5b, 0xd5, 0x17, 0x5f, 0xbd, 0xd1, 0x31,
	0x37, 0x1e, 0x57, 0xc2, 0xb7, 0xf7, 0x3c, 0x02, 0xee, 0x31, 0x55, 0xf3, 0x68, 0x31, 0xbd, 0x42,
	0x2f, 0xa1, 0xa3, 0xd7, 0x22, 0x48, 0xe9, 0x95, 0x51, 0xb8, 0x3b, 0xea, 0x6a, 0xba, 0x07, 0x24,
	0xd3, 0x61, 0xdc, 0x9e, 0x14, 0xc6, 0x9d, 0x59, 0x34, 0xee, 0xcc, 0xc2, 0xfb, 0x6a, 0xc1, 0x93,
	0x05, 0x3d, 0xb2, 0x04, 0xed, 0x82, 0x53, 0x36, 0xc9, 0x92, 0xb2, 0xcb, 0xea, 0x6d, 0x97, 0x2c,
	0xc1, 0x9d, 0x49, 0x69, 0xa1, 0x33, 0xe8, 0x56, 0x48, 0x98, 0x46, 0xdd, 0x91, 0x7f, 0xff, 0x09,
	0xe8, 0x87, 0x83, 0xab, 0x25, 0xf4, 0xaa, 0x3f, 0x3f, 0xa7, 0xea, 0xb0, 0x18, 0xf4, 0xa2, 0x99,
	0xfd, 0x37, 0x29, 0x6a, 0x1f, 0x9b, 0x5d, 0xf7, 0xd8, 0x3c, 0x0c, 0x2f, 0xee, 0x01, 0x6c, 0x29,
	0xfd, 0xbc, 0x08, 0xdc, 0xf9, 0x4a, 0xda, 0xc3, 0xe8, 0x5d, 0x6d, 0xad, 0x7f, 0xd7, 0xf6, 0x9b,
	0x05, 0x3b, 0x0b, 0x80, 0x97, 0x3d, 0xff, 0xb2, 0xc3, 0x75, 0x62, 0x35, 0x6a, 0x37, 0xb3, 0x76,
	0x51, 0xec, 0xa5, 0x17, 0xa5, 0xf9, 0x87, 0x45, 0xd9, 0xdb, 0x87, 0xa7, 0x8b, 0xc6, 0x60, 0xc8,
	0x38, 0xd0, 0x3a, 0x38, 0x3a, 0x1e, 0x9f, 0xf6, 0x1e, 0xa0, 0x0e, 0xd8, 0x47, 0xa7, 0xef, 0x7a,
	0x3f, 0xda, 0x07, 0x27, 0xe0, 0x86, 0x32, 0xf6, 0xaf, 0xf9, 0xb5, 0xcc, 0xb5, 0x96, 0xb1, 0x9c,
	0xd2, 0xa8, 0xf8, 0x66, 0x3e, 0xbc, 0x66, 0x32, 0x22, 0x82, 0xf9, 0x6f, 0x46, 0x4a, 0xf9, 0xa1,
	0x8c, 0x87, 0xc6, 0x1d, 0xca, 0x68, 0x48, 0x92, 0x64, 0x58, 0xf3, 0x8d, 0x4d, 0x56, 0x4c, 0xc6,
	0xfe, 0xaf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xf9, 0x94, 0x6f, 0x33, 0xe4, 0x06, 0x00, 0x00,
}
