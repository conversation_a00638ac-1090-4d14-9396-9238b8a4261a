// Code generated by protoc-gen-gogo.
// source: recruit_.proto
// DO NOT EDIT!

/*
	Package recruit is a generated protocol buffer package.

	It is generated from these files:
		recruit_.proto

	It has these top-level messages:
		GuildRecruitImage
		GuildRecruitDetail
		PostGuildGameMemberRecruitReq
		PostGuildGameMemberRecruitResp
		GetGuildRecruitListByGameIDReq
		GetGuildRecruitListByGameIDResp
		RecruitGameInfo
		GetRecruitGameListReq
		GetRecruitGameListResp
		GetGuildGameRecruitDetailReq
		GetGuildGameRecruitDetailResp
		SupportGuildGameMemberRecruitReq
		SupportGuildGameMemberRecruitResp
		ReportGuildGameMemberRecruitReq
		ReportGuildGameMemberRecruitResp
*/
package recruit

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 招募的平台类型
type GUILD_GAMERECRUIT_PLATFORM_TYPE int32

const (
	GUILD_GAMERECRUIT_PLATFORM_TYPE_GAMERECRUIT_PLATFORM_ALL     GUILD_GAMERECRUIT_PLATFORM_TYPE = 1
	GUILD_GAMERECRUIT_PLATFORM_TYPE_GAMERECRUIT_PLATFORM_ANDROID GUILD_GAMERECRUIT_PLATFORM_TYPE = 2
	GUILD_GAMERECRUIT_PLATFORM_TYPE_GAMERECRUIT_PLATFORM_IOS     GUILD_GAMERECRUIT_PLATFORM_TYPE = 3
)

var GUILD_GAMERECRUIT_PLATFORM_TYPE_name = map[int32]string{
	1: "GAMERECRUIT_PLATFORM_ALL",
	2: "GAMERECRUIT_PLATFORM_ANDROID",
	3: "GAMERECRUIT_PLATFORM_IOS",
}
var GUILD_GAMERECRUIT_PLATFORM_TYPE_value = map[string]int32{
	"GAMERECRUIT_PLATFORM_ALL":     1,
	"GAMERECRUIT_PLATFORM_ANDROID": 2,
	"GAMERECRUIT_PLATFORM_IOS":     3,
}

func (x GUILD_GAMERECRUIT_PLATFORM_TYPE) Enum() *GUILD_GAMERECRUIT_PLATFORM_TYPE {
	p := new(GUILD_GAMERECRUIT_PLATFORM_TYPE)
	*p = x
	return p
}
func (x GUILD_GAMERECRUIT_PLATFORM_TYPE) String() string {
	return proto.EnumName(GUILD_GAMERECRUIT_PLATFORM_TYPE_name, int32(x))
}
func (x *GUILD_GAMERECRUIT_PLATFORM_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GUILD_GAMERECRUIT_PLATFORM_TYPE_value, data, "GUILD_GAMERECRUIT_PLATFORM_TYPE")
	if err != nil {
		return err
	}
	*x = GUILD_GAMERECRUIT_PLATFORM_TYPE(value)
	return nil
}
func (GUILD_GAMERECRUIT_PLATFORM_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorRecruit_, []int{0}
}

// 发起招募的类型
type GUILD_GAMERECRUIT_POST_TYPE int32

const (
	GUILD_GAMERECRUIT_POST_TYPE_RECRUIT_CREATE GUILD_GAMERECRUIT_POST_TYPE = 1
	GUILD_GAMERECRUIT_POST_TYPE_RECRUIT_MODIFY GUILD_GAMERECRUIT_POST_TYPE = 2
	GUILD_GAMERECRUIT_POST_TYPE_RECRUIT_DELETE GUILD_GAMERECRUIT_POST_TYPE = 3
)

var GUILD_GAMERECRUIT_POST_TYPE_name = map[int32]string{
	1: "RECRUIT_CREATE",
	2: "RECRUIT_MODIFY",
	3: "RECRUIT_DELETE",
}
var GUILD_GAMERECRUIT_POST_TYPE_value = map[string]int32{
	"RECRUIT_CREATE": 1,
	"RECRUIT_MODIFY": 2,
	"RECRUIT_DELETE": 3,
}

func (x GUILD_GAMERECRUIT_POST_TYPE) Enum() *GUILD_GAMERECRUIT_POST_TYPE {
	p := new(GUILD_GAMERECRUIT_POST_TYPE)
	*p = x
	return p
}
func (x GUILD_GAMERECRUIT_POST_TYPE) String() string {
	return proto.EnumName(GUILD_GAMERECRUIT_POST_TYPE_name, int32(x))
}
func (x *GUILD_GAMERECRUIT_POST_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GUILD_GAMERECRUIT_POST_TYPE_value, data, "GUILD_GAMERECRUIT_POST_TYPE")
	if err != nil {
		return err
	}
	*x = GUILD_GAMERECRUIT_POST_TYPE(value)
	return nil
}
func (GUILD_GAMERECRUIT_POST_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorRecruit_, []int{1}
}

// 发布招募 会长需要支付的货币类型
type EPostRecruitCurrencyType int32

const (
	EPostRecruitCurrencyType_RECRUIT_CURRENCY_TYPE_RED_DIAMOND        EPostRecruitCurrencyType = 1
	EPostRecruitCurrencyType_RECRUIT_CURRENCY_TYPE_GUILD_CONTRIBUTION EPostRecruitCurrencyType = 2
)

var EPostRecruitCurrencyType_name = map[int32]string{
	1: "RECRUIT_CURRENCY_TYPE_RED_DIAMOND",
	2: "RECRUIT_CURRENCY_TYPE_GUILD_CONTRIBUTION",
}
var EPostRecruitCurrencyType_value = map[string]int32{
	"RECRUIT_CURRENCY_TYPE_RED_DIAMOND":        1,
	"RECRUIT_CURRENCY_TYPE_GUILD_CONTRIBUTION": 2,
}

func (x EPostRecruitCurrencyType) Enum() *EPostRecruitCurrencyType {
	p := new(EPostRecruitCurrencyType)
	*p = x
	return p
}
func (x EPostRecruitCurrencyType) String() string {
	return proto.EnumName(EPostRecruitCurrencyType_name, int32(x))
}
func (x *EPostRecruitCurrencyType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EPostRecruitCurrencyType_value, data, "EPostRecruitCurrencyType")
	if err != nil {
		return err
	}
	*x = EPostRecruitCurrencyType(value)
	return nil
}
func (EPostRecruitCurrencyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorRecruit_, []int{2}
}

// 招募的图
type GuildRecruitImage struct {
	ThumbUrl    string `protobuf:"bytes,1,req,name=thumb_url,json=thumbUrl" json:"thumb_url"`
	ImageUrl    string `protobuf:"bytes,2,req,name=image_url,json=imageUrl" json:"image_url"`
	ImageWidth  uint32 `protobuf:"varint,3,opt,name=image_width,json=imageWidth" json:"image_width"`
	ImageHeight uint32 `protobuf:"varint,4,opt,name=image_height,json=imageHeight" json:"image_height"`
}

func (m *GuildRecruitImage) Reset()                    { *m = GuildRecruitImage{} }
func (m *GuildRecruitImage) String() string            { return proto.CompactTextString(m) }
func (*GuildRecruitImage) ProtoMessage()               {}
func (*GuildRecruitImage) Descriptor() ([]byte, []int) { return fileDescriptorRecruit_, []int{0} }

func (m *GuildRecruitImage) GetThumbUrl() string {
	if m != nil {
		return m.ThumbUrl
	}
	return ""
}

func (m *GuildRecruitImage) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *GuildRecruitImage) GetImageWidth() uint32 {
	if m != nil {
		return m.ImageWidth
	}
	return 0
}

func (m *GuildRecruitImage) GetImageHeight() uint32 {
	if m != nil {
		return m.ImageHeight
	}
	return 0
}

// 招募帖数据结构
type GuildRecruitDetail struct {
	RecruitId       uint32               `protobuf:"varint,1,req,name=recruit_id,json=recruitId" json:"recruit_id"`
	GameId          uint32               `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	GuildId         uint32               `protobuf:"varint,3,req,name=guild_id,json=guildId" json:"guild_id"`
	TagetCount      uint32               `protobuf:"varint,4,req,name=taget_count,json=tagetCount" json:"taget_count"`
	FinishedCount   uint32               `protobuf:"varint,5,req,name=finished_count,json=finishedCount" json:"finished_count"`
	SupportCount    uint32               `protobuf:"varint,6,req,name=support_count,json=supportCount" json:"support_count"`
	CreatorUid      uint32               `protobuf:"varint,7,req,name=creator_uid,json=creatorUid" json:"creator_uid"`
	GuildName       string               `protobuf:"bytes,8,req,name=guild_name,json=guildName" json:"guild_name"`
	GuildGameServer string               `protobuf:"bytes,9,req,name=guild_game_server,json=guildGameServer" json:"guild_game_server"`
	RecruitContent  string               `protobuf:"bytes,10,req,name=recruit_content,json=recruitContent" json:"recruit_content"`
	ImageList       []*GuildRecruitImage `protobuf:"bytes,11,rep,name=image_list,json=imageList" json:"image_list,omitempty"`
	CreatorAccount  string               `protobuf:"bytes,12,opt,name=creator_account,json=creatorAccount" json:"creator_account"`
	PlatformType    uint32               `protobuf:"varint,13,opt,name=platform_type,json=platformType" json:"platform_type"`
	GuildNeedVerify bool                 `protobuf:"varint,14,opt,name=guild_need_verify,json=guildNeedVerify" json:"guild_need_verify"`
	GuildShortId    uint32               `protobuf:"varint,15,opt,name=guild_short_id,json=guildShortId" json:"guild_short_id"`
}

func (m *GuildRecruitDetail) Reset()                    { *m = GuildRecruitDetail{} }
func (m *GuildRecruitDetail) String() string            { return proto.CompactTextString(m) }
func (*GuildRecruitDetail) ProtoMessage()               {}
func (*GuildRecruitDetail) Descriptor() ([]byte, []int) { return fileDescriptorRecruit_, []int{1} }

func (m *GuildRecruitDetail) GetRecruitId() uint32 {
	if m != nil {
		return m.RecruitId
	}
	return 0
}

func (m *GuildRecruitDetail) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GuildRecruitDetail) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildRecruitDetail) GetTagetCount() uint32 {
	if m != nil {
		return m.TagetCount
	}
	return 0
}

func (m *GuildRecruitDetail) GetFinishedCount() uint32 {
	if m != nil {
		return m.FinishedCount
	}
	return 0
}

func (m *GuildRecruitDetail) GetSupportCount() uint32 {
	if m != nil {
		return m.SupportCount
	}
	return 0
}

func (m *GuildRecruitDetail) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *GuildRecruitDetail) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *GuildRecruitDetail) GetGuildGameServer() string {
	if m != nil {
		return m.GuildGameServer
	}
	return ""
}

func (m *GuildRecruitDetail) GetRecruitContent() string {
	if m != nil {
		return m.RecruitContent
	}
	return ""
}

func (m *GuildRecruitDetail) GetImageList() []*GuildRecruitImage {
	if m != nil {
		return m.ImageList
	}
	return nil
}

func (m *GuildRecruitDetail) GetCreatorAccount() string {
	if m != nil {
		return m.CreatorAccount
	}
	return ""
}

func (m *GuildRecruitDetail) GetPlatformType() uint32 {
	if m != nil {
		return m.PlatformType
	}
	return 0
}

func (m *GuildRecruitDetail) GetGuildNeedVerify() bool {
	if m != nil {
		return m.GuildNeedVerify
	}
	return false
}

func (m *GuildRecruitDetail) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

// 发起招募
type PostGuildGameMemberRecruitReq struct {
	BaseReq          *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GameId           uint32      `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	PostType         uint32      `protobuf:"varint,3,req,name=post_type,json=postType" json:"post_type"`
	GuildGameServer  string      `protobuf:"bytes,4,req,name=guild_game_server,json=guildGameServer" json:"guild_game_server"`
	RecruitContent   string      `protobuf:"bytes,5,req,name=recruit_content,json=recruitContent" json:"recruit_content"`
	ImageKeyList     []string    `protobuf:"bytes,6,rep,name=image_key_list,json=imageKeyList" json:"image_key_list,omitempty"`
	RecruitId        uint32      `protobuf:"varint,7,opt,name=recruit_id,json=recruitId" json:"recruit_id"`
	PlatformType     uint32      `protobuf:"varint,8,opt,name=platform_type,json=platformType" json:"platform_type"`
	PostCurrencyType uint32      `protobuf:"varint,9,opt,name=post_currency_type,json=postCurrencyType" json:"post_currency_type"`
}

func (m *PostGuildGameMemberRecruitReq) Reset()         { *m = PostGuildGameMemberRecruitReq{} }
func (m *PostGuildGameMemberRecruitReq) String() string { return proto.CompactTextString(m) }
func (*PostGuildGameMemberRecruitReq) ProtoMessage()    {}
func (*PostGuildGameMemberRecruitReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRecruit_, []int{2}
}

func (m *PostGuildGameMemberRecruitReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PostGuildGameMemberRecruitReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *PostGuildGameMemberRecruitReq) GetPostType() uint32 {
	if m != nil {
		return m.PostType
	}
	return 0
}

func (m *PostGuildGameMemberRecruitReq) GetGuildGameServer() string {
	if m != nil {
		return m.GuildGameServer
	}
	return ""
}

func (m *PostGuildGameMemberRecruitReq) GetRecruitContent() string {
	if m != nil {
		return m.RecruitContent
	}
	return ""
}

func (m *PostGuildGameMemberRecruitReq) GetImageKeyList() []string {
	if m != nil {
		return m.ImageKeyList
	}
	return nil
}

func (m *PostGuildGameMemberRecruitReq) GetRecruitId() uint32 {
	if m != nil {
		return m.RecruitId
	}
	return 0
}

func (m *PostGuildGameMemberRecruitReq) GetPlatformType() uint32 {
	if m != nil {
		return m.PlatformType
	}
	return 0
}

func (m *PostGuildGameMemberRecruitReq) GetPostCurrencyType() uint32 {
	if m != nil {
		return m.PostCurrencyType
	}
	return 0
}

type PostGuildGameMemberRecruitResp struct {
	BaseResp              *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	RecruitId             uint32       `protobuf:"varint,2,req,name=recruit_id,json=recruitId" json:"recruit_id"`
	PostType              uint32       `protobuf:"varint,3,opt,name=post_type,json=postType" json:"post_type"`
	RedDiamondCost        uint32       `protobuf:"varint,4,opt,name=red_diamond_cost,json=redDiamondCost" json:"red_diamond_cost"`
	GuildContributionCost uint32       `protobuf:"varint,5,opt,name=guild_contribution_cost,json=guildContributionCost" json:"guild_contribution_cost"`
}

func (m *PostGuildGameMemberRecruitResp) Reset()         { *m = PostGuildGameMemberRecruitResp{} }
func (m *PostGuildGameMemberRecruitResp) String() string { return proto.CompactTextString(m) }
func (*PostGuildGameMemberRecruitResp) ProtoMessage()    {}
func (*PostGuildGameMemberRecruitResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRecruit_, []int{3}
}

func (m *PostGuildGameMemberRecruitResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PostGuildGameMemberRecruitResp) GetRecruitId() uint32 {
	if m != nil {
		return m.RecruitId
	}
	return 0
}

func (m *PostGuildGameMemberRecruitResp) GetPostType() uint32 {
	if m != nil {
		return m.PostType
	}
	return 0
}

func (m *PostGuildGameMemberRecruitResp) GetRedDiamondCost() uint32 {
	if m != nil {
		return m.RedDiamondCost
	}
	return 0
}

func (m *PostGuildGameMemberRecruitResp) GetGuildContributionCost() uint32 {
	if m != nil {
		return m.GuildContributionCost
	}
	return 0
}

// 获取游戏的招募列表
type GetGuildRecruitListByGameIDReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	BeginIdx     uint32      `protobuf:"varint,2,req,name=begin_idx,json=beginIdx" json:"begin_idx"`
	MaxGetsize   uint32      `protobuf:"varint,3,req,name=max_getsize,json=maxGetsize" json:"max_getsize"`
	GameId       uint32      `protobuf:"varint,4,req,name=game_id,json=gameId" json:"game_id"`
	PlatformType uint32      `protobuf:"varint,5,opt,name=platform_type,json=platformType" json:"platform_type"`
}

func (m *GetGuildRecruitListByGameIDReq) Reset()         { *m = GetGuildRecruitListByGameIDReq{} }
func (m *GetGuildRecruitListByGameIDReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildRecruitListByGameIDReq) ProtoMessage()    {}
func (*GetGuildRecruitListByGameIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRecruit_, []int{4}
}

func (m *GetGuildRecruitListByGameIDReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGuildRecruitListByGameIDReq) GetBeginIdx() uint32 {
	if m != nil {
		return m.BeginIdx
	}
	return 0
}

func (m *GetGuildRecruitListByGameIDReq) GetMaxGetsize() uint32 {
	if m != nil {
		return m.MaxGetsize
	}
	return 0
}

func (m *GetGuildRecruitListByGameIDReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGuildRecruitListByGameIDReq) GetPlatformType() uint32 {
	if m != nil {
		return m.PlatformType
	}
	return 0
}

type GetGuildRecruitListByGameIDResp struct {
	BaseResp       *ga.BaseResp          `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GameId         uint32                `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	RecruitList    []*GuildRecruitDetail `protobuf:"bytes,3,rep,name=recruit_list,json=recruitList" json:"recruit_list,omitempty"`
	AllRecruitSize uint32                `protobuf:"varint,4,req,name=all_recruit_size,json=allRecruitSize" json:"all_recruit_size"`
}

func (m *GetGuildRecruitListByGameIDResp) Reset()         { *m = GetGuildRecruitListByGameIDResp{} }
func (m *GetGuildRecruitListByGameIDResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildRecruitListByGameIDResp) ProtoMessage()    {}
func (*GetGuildRecruitListByGameIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRecruit_, []int{5}
}

func (m *GetGuildRecruitListByGameIDResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGuildRecruitListByGameIDResp) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGuildRecruitListByGameIDResp) GetRecruitList() []*GuildRecruitDetail {
	if m != nil {
		return m.RecruitList
	}
	return nil
}

func (m *GetGuildRecruitListByGameIDResp) GetAllRecruitSize() uint32 {
	if m != nil {
		return m.AllRecruitSize
	}
	return 0
}

// 获取有公会正在进行成员招募的游戏列表(目前只返回运营配置的推荐游戏)
type RecruitGameInfo struct {
	GameId     uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	GameName   string `protobuf:"bytes,2,req,name=game_name,json=gameName" json:"game_name"`
	GameIcoUrl string `protobuf:"bytes,3,req,name=game_ico_url,json=gameIcoUrl" json:"game_ico_url"`
	RecruitCnt uint32 `protobuf:"varint,4,req,name=recruit_cnt,json=recruitCnt" json:"recruit_cnt"`
	Summary    string `protobuf:"bytes,5,opt,name=summary" json:"summary"`
	JumpUrl    string `protobuf:"bytes,6,opt,name=jump_url,json=jumpUrl" json:"jump_url"`
}

func (m *RecruitGameInfo) Reset()                    { *m = RecruitGameInfo{} }
func (m *RecruitGameInfo) String() string            { return proto.CompactTextString(m) }
func (*RecruitGameInfo) ProtoMessage()               {}
func (*RecruitGameInfo) Descriptor() ([]byte, []int) { return fileDescriptorRecruit_, []int{6} }

func (m *RecruitGameInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *RecruitGameInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *RecruitGameInfo) GetGameIcoUrl() string {
	if m != nil {
		return m.GameIcoUrl
	}
	return ""
}

func (m *RecruitGameInfo) GetRecruitCnt() uint32 {
	if m != nil {
		return m.RecruitCnt
	}
	return 0
}

func (m *RecruitGameInfo) GetSummary() string {
	if m != nil {
		return m.Summary
	}
	return ""
}

func (m *RecruitGameInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

type GetRecruitGameListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetRecruitGameListReq) Reset()                    { *m = GetRecruitGameListReq{} }
func (m *GetRecruitGameListReq) String() string            { return proto.CompactTextString(m) }
func (*GetRecruitGameListReq) ProtoMessage()               {}
func (*GetRecruitGameListReq) Descriptor() ([]byte, []int) { return fileDescriptorRecruit_, []int{7} }

func (m *GetRecruitGameListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetRecruitGameListResp struct {
	BaseResp *ga.BaseResp       `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GameList []*RecruitGameInfo `protobuf:"bytes,2,rep,name=game_list,json=gameList" json:"game_list,omitempty"`
}

func (m *GetRecruitGameListResp) Reset()                    { *m = GetRecruitGameListResp{} }
func (m *GetRecruitGameListResp) String() string            { return proto.CompactTextString(m) }
func (*GetRecruitGameListResp) ProtoMessage()               {}
func (*GetRecruitGameListResp) Descriptor() ([]byte, []int) { return fileDescriptorRecruit_, []int{8} }

func (m *GetRecruitGameListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRecruitGameListResp) GetGameList() []*RecruitGameInfo {
	if m != nil {
		return m.GameList
	}
	return nil
}

// 获取单个招募的详情(貌似没有用到)
type GetGuildGameRecruitDetailReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	RecruitId uint32      `protobuf:"varint,2,req,name=recruit_id,json=recruitId" json:"recruit_id"`
}

func (m *GetGuildGameRecruitDetailReq) Reset()         { *m = GetGuildGameRecruitDetailReq{} }
func (m *GetGuildGameRecruitDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildGameRecruitDetailReq) ProtoMessage()    {}
func (*GetGuildGameRecruitDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRecruit_, []int{9}
}

func (m *GetGuildGameRecruitDetailReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGuildGameRecruitDetailReq) GetRecruitId() uint32 {
	if m != nil {
		return m.RecruitId
	}
	return 0
}

type GetGuildGameRecruitDetailResp struct {
	BaseResp    *ga.BaseResp        `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ChannelInfo *GuildRecruitDetail `protobuf:"bytes,2,req,name=channel_info,json=channelInfo" json:"channel_info,omitempty"`
}

func (m *GetGuildGameRecruitDetailResp) Reset()         { *m = GetGuildGameRecruitDetailResp{} }
func (m *GetGuildGameRecruitDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildGameRecruitDetailResp) ProtoMessage()    {}
func (*GetGuildGameRecruitDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRecruit_, []int{10}
}

func (m *GetGuildGameRecruitDetailResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGuildGameRecruitDetailResp) GetChannelInfo() *GuildRecruitDetail {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

// 顶一下
type SupportGuildGameMemberRecruitReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	RecruitId uint32      `protobuf:"varint,2,req,name=recruit_id,json=recruitId" json:"recruit_id"`
	GameId    uint32      `protobuf:"varint,3,req,name=game_id,json=gameId" json:"game_id"`
}

func (m *SupportGuildGameMemberRecruitReq) Reset()         { *m = SupportGuildGameMemberRecruitReq{} }
func (m *SupportGuildGameMemberRecruitReq) String() string { return proto.CompactTextString(m) }
func (*SupportGuildGameMemberRecruitReq) ProtoMessage()    {}
func (*SupportGuildGameMemberRecruitReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRecruit_, []int{11}
}

func (m *SupportGuildGameMemberRecruitReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SupportGuildGameMemberRecruitReq) GetRecruitId() uint32 {
	if m != nil {
		return m.RecruitId
	}
	return 0
}

func (m *SupportGuildGameMemberRecruitReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type SupportGuildGameMemberRecruitResp struct {
	BaseResp   *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	RecruitId  uint32       `protobuf:"varint,2,req,name=recruit_id,json=recruitId" json:"recruit_id"`
	SupportCnt uint32       `protobuf:"varint,3,req,name=support_cnt,json=supportCnt" json:"support_cnt"`
}

func (m *SupportGuildGameMemberRecruitResp) Reset()         { *m = SupportGuildGameMemberRecruitResp{} }
func (m *SupportGuildGameMemberRecruitResp) String() string { return proto.CompactTextString(m) }
func (*SupportGuildGameMemberRecruitResp) ProtoMessage()    {}
func (*SupportGuildGameMemberRecruitResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRecruit_, []int{12}
}

func (m *SupportGuildGameMemberRecruitResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SupportGuildGameMemberRecruitResp) GetRecruitId() uint32 {
	if m != nil {
		return m.RecruitId
	}
	return 0
}

func (m *SupportGuildGameMemberRecruitResp) GetSupportCnt() uint32 {
	if m != nil {
		return m.SupportCnt
	}
	return 0
}

// 举报
type ReportGuildGameMemberRecruitReq struct {
	BaseReq       *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	RecruitId     uint32      `protobuf:"varint,2,req,name=recruit_id,json=recruitId" json:"recruit_id"`
	ReportContent string      `protobuf:"bytes,3,opt,name=report_content,json=reportContent" json:"report_content"`
}

func (m *ReportGuildGameMemberRecruitReq) Reset()         { *m = ReportGuildGameMemberRecruitReq{} }
func (m *ReportGuildGameMemberRecruitReq) String() string { return proto.CompactTextString(m) }
func (*ReportGuildGameMemberRecruitReq) ProtoMessage()    {}
func (*ReportGuildGameMemberRecruitReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRecruit_, []int{13}
}

func (m *ReportGuildGameMemberRecruitReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportGuildGameMemberRecruitReq) GetRecruitId() uint32 {
	if m != nil {
		return m.RecruitId
	}
	return 0
}

func (m *ReportGuildGameMemberRecruitReq) GetReportContent() string {
	if m != nil {
		return m.ReportContent
	}
	return ""
}

type ReportGuildGameMemberRecruitResp struct {
	BaseResp  *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	RecruitId uint32       `protobuf:"varint,2,req,name=recruit_id,json=recruitId" json:"recruit_id"`
}

func (m *ReportGuildGameMemberRecruitResp) Reset()         { *m = ReportGuildGameMemberRecruitResp{} }
func (m *ReportGuildGameMemberRecruitResp) String() string { return proto.CompactTextString(m) }
func (*ReportGuildGameMemberRecruitResp) ProtoMessage()    {}
func (*ReportGuildGameMemberRecruitResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRecruit_, []int{14}
}

func (m *ReportGuildGameMemberRecruitResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ReportGuildGameMemberRecruitResp) GetRecruitId() uint32 {
	if m != nil {
		return m.RecruitId
	}
	return 0
}

func init() {
	proto.RegisterType((*GuildRecruitImage)(nil), "ga.GuildRecruitImage")
	proto.RegisterType((*GuildRecruitDetail)(nil), "ga.GuildRecruitDetail")
	proto.RegisterType((*PostGuildGameMemberRecruitReq)(nil), "ga.PostGuildGameMemberRecruitReq")
	proto.RegisterType((*PostGuildGameMemberRecruitResp)(nil), "ga.PostGuildGameMemberRecruitResp")
	proto.RegisterType((*GetGuildRecruitListByGameIDReq)(nil), "ga.GetGuildRecruitListByGameIDReq")
	proto.RegisterType((*GetGuildRecruitListByGameIDResp)(nil), "ga.GetGuildRecruitListByGameIDResp")
	proto.RegisterType((*RecruitGameInfo)(nil), "ga.RecruitGameInfo")
	proto.RegisterType((*GetRecruitGameListReq)(nil), "ga.GetRecruitGameListReq")
	proto.RegisterType((*GetRecruitGameListResp)(nil), "ga.GetRecruitGameListResp")
	proto.RegisterType((*GetGuildGameRecruitDetailReq)(nil), "ga.GetGuildGameRecruitDetailReq")
	proto.RegisterType((*GetGuildGameRecruitDetailResp)(nil), "ga.GetGuildGameRecruitDetailResp")
	proto.RegisterType((*SupportGuildGameMemberRecruitReq)(nil), "ga.SupportGuildGameMemberRecruitReq")
	proto.RegisterType((*SupportGuildGameMemberRecruitResp)(nil), "ga.SupportGuildGameMemberRecruitResp")
	proto.RegisterType((*ReportGuildGameMemberRecruitReq)(nil), "ga.ReportGuildGameMemberRecruitReq")
	proto.RegisterType((*ReportGuildGameMemberRecruitResp)(nil), "ga.ReportGuildGameMemberRecruitResp")
	proto.RegisterEnum("ga.GUILD_GAMERECRUIT_PLATFORM_TYPE", GUILD_GAMERECRUIT_PLATFORM_TYPE_name, GUILD_GAMERECRUIT_PLATFORM_TYPE_value)
	proto.RegisterEnum("ga.GUILD_GAMERECRUIT_POST_TYPE", GUILD_GAMERECRUIT_POST_TYPE_name, GUILD_GAMERECRUIT_POST_TYPE_value)
	proto.RegisterEnum("ga.EPostRecruitCurrencyType", EPostRecruitCurrencyType_name, EPostRecruitCurrencyType_value)
}
func (m *GuildRecruitImage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildRecruitImage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(len(m.ThumbUrl)))
	i += copy(dAtA[i:], m.ThumbUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(len(m.ImageUrl)))
	i += copy(dAtA[i:], m.ImageUrl)
	dAtA[i] = 0x18
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.ImageWidth))
	dAtA[i] = 0x20
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.ImageHeight))
	return i, nil
}

func (m *GuildRecruitDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildRecruitDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.RecruitId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.TagetCount))
	dAtA[i] = 0x28
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.FinishedCount))
	dAtA[i] = 0x30
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.SupportCount))
	dAtA[i] = 0x38
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.CreatorUid))
	dAtA[i] = 0x42
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(len(m.GuildName)))
	i += copy(dAtA[i:], m.GuildName)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(len(m.GuildGameServer)))
	i += copy(dAtA[i:], m.GuildGameServer)
	dAtA[i] = 0x52
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(len(m.RecruitContent)))
	i += copy(dAtA[i:], m.RecruitContent)
	if len(m.ImageList) > 0 {
		for _, msg := range m.ImageList {
			dAtA[i] = 0x5a
			i++
			i = encodeVarintRecruit_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x62
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(len(m.CreatorAccount)))
	i += copy(dAtA[i:], m.CreatorAccount)
	dAtA[i] = 0x68
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.PlatformType))
	dAtA[i] = 0x70
	i++
	if m.GuildNeedVerify {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x78
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.GuildShortId))
	return i, nil
}

func (m *PostGuildGameMemberRecruitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PostGuildGameMemberRecruitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRecruit_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.PostType))
	dAtA[i] = 0x22
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(len(m.GuildGameServer)))
	i += copy(dAtA[i:], m.GuildGameServer)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(len(m.RecruitContent)))
	i += copy(dAtA[i:], m.RecruitContent)
	if len(m.ImageKeyList) > 0 {
		for _, s := range m.ImageKeyList {
			dAtA[i] = 0x32
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.RecruitId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.PlatformType))
	dAtA[i] = 0x48
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.PostCurrencyType))
	return i, nil
}

func (m *PostGuildGameMemberRecruitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PostGuildGameMemberRecruitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRecruit_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.RecruitId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.PostType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.RedDiamondCost))
	dAtA[i] = 0x28
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.GuildContributionCost))
	return i, nil
}

func (m *GetGuildRecruitListByGameIDReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildRecruitListByGameIDReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRecruit_(dAtA, i, uint64(m.BaseReq.Size()))
		n3, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.BeginIdx))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.MaxGetsize))
	dAtA[i] = 0x20
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.PlatformType))
	return i, nil
}

func (m *GetGuildRecruitListByGameIDResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildRecruitListByGameIDResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRecruit_(dAtA, i, uint64(m.BaseResp.Size()))
		n4, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.GameId))
	if len(m.RecruitList) > 0 {
		for _, msg := range m.RecruitList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintRecruit_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.AllRecruitSize))
	return i, nil
}

func (m *RecruitGameInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecruitGameInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(len(m.GameIcoUrl)))
	i += copy(dAtA[i:], m.GameIcoUrl)
	dAtA[i] = 0x20
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.RecruitCnt))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(len(m.Summary)))
	i += copy(dAtA[i:], m.Summary)
	dAtA[i] = 0x32
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(len(m.JumpUrl)))
	i += copy(dAtA[i:], m.JumpUrl)
	return i, nil
}

func (m *GetRecruitGameListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecruitGameListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRecruit_(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *GetRecruitGameListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecruitGameListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRecruit_(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if len(m.GameList) > 0 {
		for _, msg := range m.GameList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintRecruit_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGuildGameRecruitDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGameRecruitDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRecruit_(dAtA, i, uint64(m.BaseReq.Size()))
		n7, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.RecruitId))
	return i, nil
}

func (m *GetGuildGameRecruitDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGameRecruitDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRecruit_(dAtA, i, uint64(m.BaseResp.Size()))
		n8, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	if m.ChannelInfo == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRecruit_(dAtA, i, uint64(m.ChannelInfo.Size()))
		n9, err := m.ChannelInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	return i, nil
}

func (m *SupportGuildGameMemberRecruitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SupportGuildGameMemberRecruitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRecruit_(dAtA, i, uint64(m.BaseReq.Size()))
		n10, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.RecruitId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.GameId))
	return i, nil
}

func (m *SupportGuildGameMemberRecruitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SupportGuildGameMemberRecruitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRecruit_(dAtA, i, uint64(m.BaseResp.Size()))
		n11, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.RecruitId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.SupportCnt))
	return i, nil
}

func (m *ReportGuildGameMemberRecruitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportGuildGameMemberRecruitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRecruit_(dAtA, i, uint64(m.BaseReq.Size()))
		n12, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.RecruitId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(len(m.ReportContent)))
	i += copy(dAtA[i:], m.ReportContent)
	return i, nil
}

func (m *ReportGuildGameMemberRecruitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportGuildGameMemberRecruitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRecruit_(dAtA, i, uint64(m.BaseResp.Size()))
		n13, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintRecruit_(dAtA, i, uint64(m.RecruitId))
	return i, nil
}

func encodeFixed64Recruit_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Recruit_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintRecruit_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GuildRecruitImage) Size() (n int) {
	var l int
	_ = l
	l = len(m.ThumbUrl)
	n += 1 + l + sovRecruit_(uint64(l))
	l = len(m.ImageUrl)
	n += 1 + l + sovRecruit_(uint64(l))
	n += 1 + sovRecruit_(uint64(m.ImageWidth))
	n += 1 + sovRecruit_(uint64(m.ImageHeight))
	return n
}

func (m *GuildRecruitDetail) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRecruit_(uint64(m.RecruitId))
	n += 1 + sovRecruit_(uint64(m.GameId))
	n += 1 + sovRecruit_(uint64(m.GuildId))
	n += 1 + sovRecruit_(uint64(m.TagetCount))
	n += 1 + sovRecruit_(uint64(m.FinishedCount))
	n += 1 + sovRecruit_(uint64(m.SupportCount))
	n += 1 + sovRecruit_(uint64(m.CreatorUid))
	l = len(m.GuildName)
	n += 1 + l + sovRecruit_(uint64(l))
	l = len(m.GuildGameServer)
	n += 1 + l + sovRecruit_(uint64(l))
	l = len(m.RecruitContent)
	n += 1 + l + sovRecruit_(uint64(l))
	if len(m.ImageList) > 0 {
		for _, e := range m.ImageList {
			l = e.Size()
			n += 1 + l + sovRecruit_(uint64(l))
		}
	}
	l = len(m.CreatorAccount)
	n += 1 + l + sovRecruit_(uint64(l))
	n += 1 + sovRecruit_(uint64(m.PlatformType))
	n += 2
	n += 1 + sovRecruit_(uint64(m.GuildShortId))
	return n
}

func (m *PostGuildGameMemberRecruitReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovRecruit_(uint64(l))
	}
	n += 1 + sovRecruit_(uint64(m.GameId))
	n += 1 + sovRecruit_(uint64(m.PostType))
	l = len(m.GuildGameServer)
	n += 1 + l + sovRecruit_(uint64(l))
	l = len(m.RecruitContent)
	n += 1 + l + sovRecruit_(uint64(l))
	if len(m.ImageKeyList) > 0 {
		for _, s := range m.ImageKeyList {
			l = len(s)
			n += 1 + l + sovRecruit_(uint64(l))
		}
	}
	n += 1 + sovRecruit_(uint64(m.RecruitId))
	n += 1 + sovRecruit_(uint64(m.PlatformType))
	n += 1 + sovRecruit_(uint64(m.PostCurrencyType))
	return n
}

func (m *PostGuildGameMemberRecruitResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovRecruit_(uint64(l))
	}
	n += 1 + sovRecruit_(uint64(m.RecruitId))
	n += 1 + sovRecruit_(uint64(m.PostType))
	n += 1 + sovRecruit_(uint64(m.RedDiamondCost))
	n += 1 + sovRecruit_(uint64(m.GuildContributionCost))
	return n
}

func (m *GetGuildRecruitListByGameIDReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovRecruit_(uint64(l))
	}
	n += 1 + sovRecruit_(uint64(m.BeginIdx))
	n += 1 + sovRecruit_(uint64(m.MaxGetsize))
	n += 1 + sovRecruit_(uint64(m.GameId))
	n += 1 + sovRecruit_(uint64(m.PlatformType))
	return n
}

func (m *GetGuildRecruitListByGameIDResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovRecruit_(uint64(l))
	}
	n += 1 + sovRecruit_(uint64(m.GameId))
	if len(m.RecruitList) > 0 {
		for _, e := range m.RecruitList {
			l = e.Size()
			n += 1 + l + sovRecruit_(uint64(l))
		}
	}
	n += 1 + sovRecruit_(uint64(m.AllRecruitSize))
	return n
}

func (m *RecruitGameInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRecruit_(uint64(m.GameId))
	l = len(m.GameName)
	n += 1 + l + sovRecruit_(uint64(l))
	l = len(m.GameIcoUrl)
	n += 1 + l + sovRecruit_(uint64(l))
	n += 1 + sovRecruit_(uint64(m.RecruitCnt))
	l = len(m.Summary)
	n += 1 + l + sovRecruit_(uint64(l))
	l = len(m.JumpUrl)
	n += 1 + l + sovRecruit_(uint64(l))
	return n
}

func (m *GetRecruitGameListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovRecruit_(uint64(l))
	}
	return n
}

func (m *GetRecruitGameListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovRecruit_(uint64(l))
	}
	if len(m.GameList) > 0 {
		for _, e := range m.GameList {
			l = e.Size()
			n += 1 + l + sovRecruit_(uint64(l))
		}
	}
	return n
}

func (m *GetGuildGameRecruitDetailReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovRecruit_(uint64(l))
	}
	n += 1 + sovRecruit_(uint64(m.RecruitId))
	return n
}

func (m *GetGuildGameRecruitDetailResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovRecruit_(uint64(l))
	}
	if m.ChannelInfo != nil {
		l = m.ChannelInfo.Size()
		n += 1 + l + sovRecruit_(uint64(l))
	}
	return n
}

func (m *SupportGuildGameMemberRecruitReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovRecruit_(uint64(l))
	}
	n += 1 + sovRecruit_(uint64(m.RecruitId))
	n += 1 + sovRecruit_(uint64(m.GameId))
	return n
}

func (m *SupportGuildGameMemberRecruitResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovRecruit_(uint64(l))
	}
	n += 1 + sovRecruit_(uint64(m.RecruitId))
	n += 1 + sovRecruit_(uint64(m.SupportCnt))
	return n
}

func (m *ReportGuildGameMemberRecruitReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovRecruit_(uint64(l))
	}
	n += 1 + sovRecruit_(uint64(m.RecruitId))
	l = len(m.ReportContent)
	n += 1 + l + sovRecruit_(uint64(l))
	return n
}

func (m *ReportGuildGameMemberRecruitResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovRecruit_(uint64(l))
	}
	n += 1 + sovRecruit_(uint64(m.RecruitId))
	return n
}

func sovRecruit_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozRecruit_(x uint64) (n int) {
	return sovRecruit_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GuildRecruitImage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildRecruitImage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildRecruitImage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThumbUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ThumbUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageWidth", wireType)
			}
			m.ImageWidth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ImageWidth |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageHeight", wireType)
			}
			m.ImageHeight = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ImageHeight |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("thumb_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("image_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildRecruitDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildRecruitDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildRecruitDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecruitId", wireType)
			}
			m.RecruitId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecruitId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagetCount", wireType)
			}
			m.TagetCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagetCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinishedCount", wireType)
			}
			m.FinishedCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinishedCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SupportCount", wireType)
			}
			m.SupportCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SupportCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreatorUid", wireType)
			}
			m.CreatorUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreatorUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildGameServer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildGameServer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecruitContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecruitContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageList = append(m.ImageList, &GuildRecruitImage{})
			if err := m.ImageList[len(m.ImageList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreatorAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CreatorAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PlatformType", wireType)
			}
			m.PlatformType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PlatformType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildNeedVerify", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.GuildNeedVerify = bool(v != 0)
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildShortId", wireType)
			}
			m.GuildShortId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildShortId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("recruit_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("taget_count")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("finished_count")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("support_count")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("creator_uid")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_name")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_game_server")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("recruit_content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PostGuildGameMemberRecruitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PostGuildGameMemberRecruitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PostGuildGameMemberRecruitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PostType", wireType)
			}
			m.PostType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PostType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildGameServer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildGameServer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecruitContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecruitContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageKeyList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageKeyList = append(m.ImageKeyList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecruitId", wireType)
			}
			m.RecruitId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecruitId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PlatformType", wireType)
			}
			m.PlatformType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PlatformType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PostCurrencyType", wireType)
			}
			m.PostCurrencyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PostCurrencyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("post_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_game_server")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("recruit_content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PostGuildGameMemberRecruitResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PostGuildGameMemberRecruitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PostGuildGameMemberRecruitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecruitId", wireType)
			}
			m.RecruitId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecruitId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PostType", wireType)
			}
			m.PostType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PostType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedDiamondCost", wireType)
			}
			m.RedDiamondCost = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedDiamondCost |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildContributionCost", wireType)
			}
			m.GuildContributionCost = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildContributionCost |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("recruit_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildRecruitListByGameIDReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildRecruitListByGameIDReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildRecruitListByGameIDReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginIdx", wireType)
			}
			m.BeginIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxGetsize", wireType)
			}
			m.MaxGetsize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxGetsize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PlatformType", wireType)
			}
			m.PlatformType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PlatformType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("begin_idx")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("max_getsize")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildRecruitListByGameIDResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildRecruitListByGameIDResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildRecruitListByGameIDResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecruitList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecruitList = append(m.RecruitList, &GuildRecruitDetail{})
			if err := m.RecruitList[len(m.RecruitList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllRecruitSize", wireType)
			}
			m.AllRecruitSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AllRecruitSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("all_recruit_size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecruitGameInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecruitGameInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecruitGameInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameIcoUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameIcoUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecruitCnt", wireType)
			}
			m.RecruitCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecruitCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Summary", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Summary = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_ico_url")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("recruit_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecruitGameListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecruitGameListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecruitGameListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecruitGameListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecruitGameListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecruitGameListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameList = append(m.GameList, &RecruitGameInfo{})
			if err := m.GameList[len(m.GameList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGameRecruitDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGameRecruitDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGameRecruitDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecruitId", wireType)
			}
			m.RecruitId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecruitId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("recruit_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGameRecruitDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGameRecruitDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGameRecruitDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ChannelInfo == nil {
				m.ChannelInfo = &GuildRecruitDetail{}
			}
			if err := m.ChannelInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SupportGuildGameMemberRecruitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SupportGuildGameMemberRecruitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SupportGuildGameMemberRecruitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecruitId", wireType)
			}
			m.RecruitId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecruitId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("recruit_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SupportGuildGameMemberRecruitResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SupportGuildGameMemberRecruitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SupportGuildGameMemberRecruitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecruitId", wireType)
			}
			m.RecruitId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecruitId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SupportCnt", wireType)
			}
			m.SupportCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SupportCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("recruit_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("support_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportGuildGameMemberRecruitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportGuildGameMemberRecruitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportGuildGameMemberRecruitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecruitId", wireType)
			}
			m.RecruitId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecruitId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReportContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("recruit_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportGuildGameMemberRecruitResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportGuildGameMemberRecruitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportGuildGameMemberRecruitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecruit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecruitId", wireType)
			}
			m.RecruitId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecruitId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRecruit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRecruit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("recruit_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipRecruit_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowRecruit_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRecruit_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthRecruit_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowRecruit_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipRecruit_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthRecruit_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowRecruit_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("recruit_.proto", fileDescriptorRecruit_) }

var fileDescriptorRecruit_ = []byte{
	// 1293 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x57, 0xcd, 0x6f, 0x1b, 0xc5,
	0x1b, 0xee, 0xda, 0xf9, 0xb0, 0x5f, 0x3b, 0x8e, 0xbb, 0x3f, 0xb5, 0xbf, 0x15, 0xb4, 0x8e, 0xb3,
	0x34, 0x25, 0x4d, 0xc1, 0xad, 0x22, 0x38, 0x54, 0x42, 0x42, 0x89, 0xed, 0x9a, 0x15, 0x89, 0x1d,
	0x6d, 0x6c, 0x50, 0xb8, 0xac, 0xc6, 0xbb, 0x13, 0x7b, 0xe8, 0x7e, 0x75, 0x77, 0xb6, 0xc4, 0xbd,
	0x20, 0x0e, 0x5c, 0x11, 0x12, 0x9c, 0xe0, 0x2f, 0xe0, 0x3f, 0xe9, 0x91, 0x13, 0x27, 0x84, 0x50,
	0xfb, 0x8f, 0xa0, 0x99, 0xd9, 0x6d, 0xc6, 0x89, 0xeb, 0xd6, 0xe2, 0xe3, 0xb6, 0x7e, 0x9f, 0x67,
	0x3e, 0xde, 0xf7, 0x79, 0xe6, 0x9d, 0x31, 0x54, 0x22, 0x6c, 0x47, 0x09, 0xa1, 0x56, 0x23, 0x8c,
	0x02, 0x1a, 0xa8, 0xb9, 0x11, 0x7a, 0x6b, 0x6d, 0x84, 0xac, 0x21, 0x8a, 0xb1, 0x08, 0xe9, 0xbf,
	0x28, 0x70, 0xb5, 0x93, 0x10, 0xd7, 0x31, 0x05, 0xd5, 0xf0, 0xd0, 0x08, 0xab, 0x9b, 0x50, 0xa4,
	0xe3, 0xc4, 0x1b, 0x5a, 0x49, 0xe4, 0x6a, 0x4a, 0x3d, 0xb7, 0x5d, 0xdc, 0x5f, 0x7a, 0xf6, 0xc7,
	0xc6, 0x15, 0xb3, 0xc0, 0xc3, 0x83, 0xc8, 0x65, 0x14, 0xc2, 0xb8, 0x9c, 0x92, 0x93, 0x29, 0x3c,
	0xcc, 0x28, 0x5b, 0x50, 0x12, 0x94, 0xaf, 0x88, 0x43, 0xc7, 0x5a, 0xbe, 0xae, 0x6c, 0xaf, 0xa5,
	0x24, 0xe0, 0xc0, 0xe7, 0x2c, 0xae, 0xbe, 0x0b, 0x65, 0x41, 0x1b, 0x63, 0x32, 0x1a, 0x53, 0x6d,
	0x49, 0xe2, 0x89, 0x09, 0x3e, 0xe1, 0x80, 0xfe, 0xc3, 0x32, 0xa8, 0xf2, 0x5e, 0x5b, 0x98, 0x22,
	0xe2, 0xaa, 0xef, 0x00, 0x64, 0x79, 0x12, 0x87, 0xef, 0x36, 0x1b, 0x5d, 0x4c, 0xe3, 0x86, 0xa3,
	0xde, 0x84, 0xd5, 0x11, 0xf2, 0x30, 0x63, 0xe4, 0x24, 0xc6, 0x0a, 0x0b, 0x1a, 0x8e, 0xba, 0x01,
	0x85, 0x11, 0x9b, 0x99, 0xe1, 0x79, 0x09, 0x5f, 0xe5, 0x51, 0xc3, 0x61, 0xb9, 0x50, 0x34, 0xc2,
	0xd4, 0xb2, 0x83, 0xc4, 0x67, 0x7b, 0x3c, 0xe7, 0x00, 0x07, 0x9a, 0x2c, 0xae, 0xde, 0x85, 0xca,
	0x29, 0xf1, 0x49, 0x3c, 0xc6, 0x4e, 0xca, 0x5c, 0x96, 0x98, 0x6b, 0x19, 0x26, 0xc8, 0x77, 0x60,
	0x2d, 0x4e, 0xc2, 0x30, 0x88, 0xb2, 0x59, 0x57, 0x24, 0x6e, 0x39, 0x85, 0x04, 0x75, 0x0b, 0x4a,
	0x76, 0x84, 0x11, 0x0d, 0x22, 0x2b, 0x21, 0x8e, 0xb6, 0x2a, 0x2f, 0x9f, 0x02, 0x03, 0xe2, 0xb0,
	0x52, 0x88, 0x34, 0x7c, 0xe4, 0x61, 0xad, 0x20, 0xa9, 0x52, 0xe4, 0xf1, 0x2e, 0xf2, 0xb0, 0x7a,
	0x1f, 0xae, 0x0a, 0x12, 0x2f, 0x48, 0x8c, 0xa3, 0x27, 0x38, 0xd2, 0x8a, 0x12, 0x77, 0x9d, 0xc3,
	0x1d, 0xe4, 0xe1, 0x63, 0x0e, 0xaa, 0xef, 0xc3, 0x7a, 0x56, 0x61, 0x3b, 0xf0, 0x29, 0xf6, 0xa9,
	0x06, 0x12, 0x3f, 0xb3, 0x59, 0x53, 0x60, 0xea, 0x07, 0x20, 0xe4, 0xb5, 0x5c, 0x12, 0x53, 0xad,
	0x54, 0xcf, 0x6f, 0x97, 0x76, 0xaf, 0x35, 0x46, 0xa8, 0x71, 0xc9, 0x68, 0xa6, 0xf0, 0xd0, 0x01,
	0x89, 0x29, 0x5b, 0x24, 0x4b, 0x11, 0xd9, 0xa2, 0x1e, 0xe5, 0xba, 0x72, 0xbe, 0x48, 0x0a, 0xee,
	0x09, 0x8c, 0x15, 0x2f, 0x74, 0x11, 0x3d, 0x0d, 0x22, 0xcf, 0xa2, 0x93, 0x10, 0x6b, 0x6b, 0x92,
	0x6d, 0xca, 0x19, 0xd4, 0x9f, 0x84, 0x52, 0xc2, 0x3e, 0xc6, 0x8e, 0xf5, 0x04, 0x47, 0xe4, 0x74,
	0xa2, 0x55, 0xea, 0xca, 0x76, 0x61, 0x2a, 0xe1, 0x2e, 0xc6, 0xce, 0x67, 0x1c, 0x54, 0x77, 0xa0,
	0x22, 0x46, 0xc4, 0x63, 0xa6, 0x0e, 0x71, 0xb4, 0x75, 0x79, 0x76, 0x8e, 0x1d, 0x33, 0xc8, 0x70,
	0xf4, 0x1f, 0xf3, 0x70, 0xf3, 0x28, 0x88, 0x69, 0x27, 0x2b, 0xda, 0x21, 0xf6, 0x86, 0x38, 0x4a,
	0xd3, 0x34, 0xf1, 0x63, 0xf5, 0x36, 0x14, 0xd8, 0x89, 0xb3, 0x22, 0xfc, 0x98, 0xdb, 0xb3, 0xb4,
	0x5b, 0x62, 0xd5, 0xd8, 0x47, 0x31, 0x36, 0xf1, 0x63, 0x73, 0x75, 0x28, 0x3e, 0x5e, 0xe7, 0xd1,
	0x4d, 0x28, 0x86, 0x41, 0x4c, 0x45, 0xb6, 0xb2, 0x49, 0x0b, 0x2c, 0x3c, 0x9d, 0xa9, 0x2c, 0xed,
	0xd2, 0x82, 0xd2, 0x2e, 0xcf, 0x91, 0xf6, 0x16, 0x54, 0x84, 0xb4, 0x8f, 0xf0, 0x44, 0xc8, 0xbb,
	0x52, 0xcf, 0x6f, 0x17, 0x4d, 0x71, 0x82, 0x3f, 0xc5, 0x13, 0x2e, 0xe5, 0xf4, 0x89, 0x5c, 0x95,
	0x4a, 0x27, 0x9d, 0xc8, 0x4b, 0x02, 0x16, 0x5e, 0x29, 0xe0, 0x2e, 0xa8, 0x3c, 0x73, 0x3b, 0x89,
	0x22, 0xec, 0xdb, 0x13, 0xc1, 0x2f, 0x4a, 0xfc, 0x2a, 0xc3, 0x9b, 0x29, 0xcc, 0xc6, 0xe8, 0xdf,
	0xe4, 0xa0, 0x36, 0x4f, 0x96, 0x38, 0x54, 0xef, 0x40, 0x31, 0xd5, 0x25, 0x0e, 0x53, 0x61, 0xca,
	0xe7, 0xc2, 0xc4, 0xa1, 0x59, 0x18, 0xa6, 0x5f, 0x17, 0x32, 0xca, 0xcd, 0xee, 0x31, 0x17, 0x04,
	0x52, 0x66, 0x08, 0xd4, 0x80, 0x6a, 0x84, 0x1d, 0xcb, 0x21, 0xc8, 0x0b, 0x7c, 0xd6, 0x22, 0xe2,
	0xe9, 0x7e, 0x57, 0x89, 0xb0, 0xd3, 0x12, 0x60, 0x33, 0x88, 0xa9, 0xfa, 0x11, 0xfc, 0x5f, 0x08,
	0xca, 0xc4, 0x89, 0xc8, 0x30, 0xa1, 0x24, 0xf0, 0xc5, 0xb0, 0x65, 0x69, 0xd8, 0x35, 0x4e, 0x6a,
	0x4a, 0x1c, 0x36, 0x5a, 0xff, 0x5d, 0x81, 0x5a, 0x07, 0x53, 0xf9, 0xd8, 0x31, 0x7d, 0xf6, 0x27,
	0xac, 0x1e, 0x46, 0x6b, 0x11, 0x6f, 0x6e, 0x42, 0x71, 0x88, 0x47, 0xc4, 0xb7, 0x88, 0x73, 0x36,
	0x95, 0x7f, 0x81, 0x87, 0x0d, 0xe7, 0x8c, 0xf5, 0x28, 0x0f, 0x9d, 0x59, 0x23, 0x4c, 0x63, 0xf2,
	0x74, 0xda, 0xa1, 0xe0, 0xa1, 0xb3, 0x8e, 0x88, 0xcb, 0x2e, 0x5f, 0x9a, 0xe1, 0xf2, 0x4b, 0xb6,
	0x58, 0x7e, 0x95, 0x2d, 0xf4, 0xdf, 0x14, 0xd8, 0x98, 0x9b, 0xde, 0x62, 0x1a, 0xbf, 0xe6, 0xf8,
	0x3d, 0x80, 0x72, 0x66, 0x01, 0x6e, 0xfc, 0x3c, 0xef, 0x6b, 0xd7, 0x2f, 0xf6, 0x35, 0x71, 0x29,
	0x99, 0xa5, 0xe8, 0x7c, 0x43, 0x4c, 0x75, 0xe4, 0xba, 0x56, 0x36, 0x9c, 0x97, 0x47, 0xce, 0xbd,
	0x82, 0x5c, 0x37, 0x1d, 0x7e, 0x4c, 0x9e, 0x62, 0xfd, 0x85, 0x02, 0xeb, 0xe9, 0x6f, 0x9e, 0x8a,
	0x7f, 0x1a, 0xc8, 0xbb, 0x53, 0x66, 0x37, 0x07, 0x0e, 0xf3, 0xc6, 0x3f, 0x75, 0x1d, 0xb3, 0x30,
	0xef, 0xfb, 0xb7, 0xa1, 0x2c, 0x66, 0xb0, 0x03, 0x7e, 0x69, 0xe7, 0x25, 0x16, 0xf0, 0x69, 0xec,
	0x20, 0xbd, 0xb6, 0x5f, 0xb6, 0x84, 0x8b, 0x57, 0x5d, 0xd6, 0x0e, 0x7c, 0xaa, 0xd6, 0x60, 0x35,
	0x4e, 0x3c, 0x0f, 0x45, 0x13, 0x2e, 0x51, 0x36, 0x53, 0x16, 0x64, 0x57, 0xea, 0x97, 0x89, 0x17,
	0xf2, 0xa5, 0x56, 0x64, 0x02, 0x8b, 0x0e, 0x22, 0x57, 0xff, 0x18, 0xae, 0x75, 0x30, 0x95, 0xf2,
	0x64, 0xb5, 0x5a, 0xc0, 0x93, 0x7a, 0x02, 0xd7, 0x67, 0x4d, 0xb0, 0x98, 0xea, 0xf7, 0xd3, 0xc2,
	0x71, 0x4d, 0x73, 0x5c, 0xd3, 0xff, 0x31, 0xea, 0x85, 0xfa, 0x8b, 0x3a, 0xb2, 0x05, 0xf4, 0x47,
	0x70, 0x23, 0x73, 0x1d, 0x43, 0xa7, 0x75, 0x5f, 0xe0, 0x48, 0xbd, 0x49, 0x4f, 0xd1, 0xbf, 0x55,
	0xe0, 0xe6, 0x9c, 0xd5, 0x16, 0xcb, 0xf5, 0x01, 0x94, 0xed, 0x31, 0xf2, 0x7d, 0xec, 0x5a, 0xc4,
	0x3f, 0x0d, 0xf8, 0x9a, 0x73, 0x2c, 0x9c, 0x72, 0x59, 0xfa, 0xfa, 0x77, 0x0a, 0xd4, 0x8f, 0xc5,
	0x8b, 0xe4, 0xef, 0x5f, 0x74, 0x6f, 0xd4, 0x4d, 0x25, 0xc3, 0xe7, 0x2f, 0x1b, 0x5e, 0xff, 0x49,
	0x81, 0xcd, 0xd7, 0x6c, 0xe8, 0x5f, 0x68, 0xf1, 0x5b, 0x50, 0x7a, 0xf9, 0x64, 0xf3, 0xe9, 0x74,
	0x8f, 0xcb, 0x1e, 0x6c, 0x3e, 0xd5, 0x7f, 0x56, 0x60, 0xc3, 0xc4, 0xff, 0x61, 0xb1, 0xee, 0xb2,
	0xb7, 0x7e, 0xfa, 0x92, 0x14, 0xb7, 0x78, 0x5e, 0x3a, 0x72, 0x6b, 0x02, 0x4b, 0x2f, 0x71, 0x3d,
	0x82, 0xfa, 0xfc, 0xcd, 0xfd, 0xf3, 0x85, 0xdb, 0xf9, 0x1a, 0x36, 0x3a, 0x03, 0xe3, 0xa0, 0x65,
	0x75, 0xf6, 0x0e, 0xdb, 0x66, 0xbb, 0x69, 0x0e, 0x8c, 0xbe, 0x75, 0x74, 0xb0, 0xd7, 0x7f, 0xd8,
	0x33, 0x0f, 0xad, 0xfe, 0xc9, 0x51, 0x5b, 0xbd, 0x01, 0xda, 0x4c, 0x70, 0xef, 0xe0, 0xa0, 0xaa,
	0xa8, 0x75, 0xb8, 0x31, 0x1b, 0xed, 0xb6, 0xcc, 0x9e, 0xd1, 0xaa, 0xe6, 0x5e, 0x39, 0xde, 0xe8,
	0x1d, 0x57, 0xf3, 0x3b, 0x27, 0xf0, 0xf6, 0x8c, 0x0d, 0xf4, 0x8e, 0xfb, 0x62, 0x71, 0x15, 0x2a,
	0x59, 0xb0, 0x69, 0xb6, 0xf7, 0xfa, 0xed, 0xaa, 0x22, 0xc7, 0x0e, 0x7b, 0x2d, 0xe3, 0xe1, 0x49,
	0x35, 0x27, 0xc7, 0x5a, 0xed, 0x83, 0x76, 0xbf, 0x5d, 0xcd, 0xef, 0x04, 0xa0, 0xb5, 0xd9, 0x53,
	0x23, 0xad, 0x9f, 0xfc, 0x0c, 0x51, 0xb7, 0x60, 0xf3, 0xe5, 0xbc, 0x03, 0xd3, 0x6c, 0x77, 0x9b,
	0x27, 0x7c, 0x41, 0xcb, 0x6c, 0xb7, 0xac, 0x96, 0xb1, 0x77, 0xd8, 0xeb, 0xb6, 0xaa, 0x8a, 0xfa,
	0x1e, 0x6c, 0xcf, 0xa6, 0x89, 0x3d, 0x37, 0x7b, 0xdd, 0xbe, 0x69, 0xec, 0x0f, 0xfa, 0x46, 0xaf,
	0x5b, 0xcd, 0xed, 0x1f, 0x3d, 0x7b, 0x5e, 0x53, 0x7e, 0x7d, 0x5e, 0x53, 0xfe, 0x7c, 0x5e, 0x53,
	0xbe, 0x7f, 0x51, 0xbb, 0x02, 0x9a, 0x1d, 0x78, 0x8d, 0x09, 0x99, 0x04, 0x09, 0x13, 0xc9, 0x0b,
	0x1c, 0xec, 0x8a, 0x3f, 0x78, 0x5f, 0xdc, 0x1a, 0x05, 0x2e, 0xf2, 0x47, 0x8d, 0x0f, 0x77, 0x29,
	0x6d, 0xd8, 0x81, 0x77, 0x8f, 0x87, 0xed, 0xc0, 0xbd, 0x87, 0xc2, 0xf0, 0x5e, 0x2a, 0xd0, 0x5f,
	0x01, 0x00, 0x00, 0xff, 0xff, 0xe1, 0xa9, 0xe5, 0x87, 0x2a, 0x0e, 0x00, 0x00,
}
