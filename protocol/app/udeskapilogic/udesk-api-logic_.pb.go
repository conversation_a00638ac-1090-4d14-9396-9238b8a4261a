// Code generated by protoc-gen-gogo.
// source: udesk-api-logic_.proto
// DO NOT EDIT!

/*
	Package udeskapilogic is a generated protocol buffer package.

	It is generated from these files:
		udesk-api-logic_.proto

	It has these top-level messages:
		GetUdeskUnReadMsgReq
		GetUdeskUnReadMsgResp
		UdeskUnreadPush
		CheckVipKefuAccessReq
		CheckVipKefuAccessResp
		AckVipKefuAccessReq
		AckVipKefuAccessResp
		EnterVipKefuReq
		EnterVipKefuResp
		VipKefuPush
*/
package udeskapilogic

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// vip客服入口权限类型
type VipKefuAccessType int32

const (
	VipKefuAccessType_VIP_KEFU_ACCESS_TYPE_UNKNOWN VipKefuAccessType = 0
	VipKefuAccessType_VIP_KEFU_ACCESS_TYPE_ENABLE  VipKefuAccessType = 1
	VipKefuAccessType_VIP_KEFU_ACCESS_TYPE_DISABLE VipKefuAccessType = 2
)

var VipKefuAccessType_name = map[int32]string{
	0: "VIP_KEFU_ACCESS_TYPE_UNKNOWN",
	1: "VIP_KEFU_ACCESS_TYPE_ENABLE",
	2: "VIP_KEFU_ACCESS_TYPE_DISABLE",
}
var VipKefuAccessType_value = map[string]int32{
	"VIP_KEFU_ACCESS_TYPE_UNKNOWN": 0,
	"VIP_KEFU_ACCESS_TYPE_ENABLE":  1,
	"VIP_KEFU_ACCESS_TYPE_DISABLE": 2,
}

func (x VipKefuAccessType) Enum() *VipKefuAccessType {
	p := new(VipKefuAccessType)
	*p = x
	return p
}
func (x VipKefuAccessType) String() string {
	return proto.EnumName(VipKefuAccessType_name, int32(x))
}
func (x *VipKefuAccessType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(VipKefuAccessType_value, data, "VipKefuAccessType")
	if err != nil {
		return err
	}
	*x = VipKefuAccessType(value)
	return nil
}
func (VipKefuAccessType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorUdeskApiLogic_, []int{0}
}

// 获取小红点情况
type GetUdeskUnReadMsgReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetUdeskUnReadMsgReq) Reset()         { *m = GetUdeskUnReadMsgReq{} }
func (m *GetUdeskUnReadMsgReq) String() string { return proto.CompactTextString(m) }
func (*GetUdeskUnReadMsgReq) ProtoMessage()    {}
func (*GetUdeskUnReadMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUdeskApiLogic_, []int{0}
}

func (m *GetUdeskUnReadMsgReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUdeskUnReadMsgResp struct {
	BaseResp       *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	LastUnreadTime int64        `protobuf:"varint,2,opt,name=last_unread_time,json=lastUnreadTime" json:"last_unread_time"`
}

func (m *GetUdeskUnReadMsgResp) Reset()         { *m = GetUdeskUnReadMsgResp{} }
func (m *GetUdeskUnReadMsgResp) String() string { return proto.CompactTextString(m) }
func (*GetUdeskUnReadMsgResp) ProtoMessage()    {}
func (*GetUdeskUnReadMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUdeskApiLogic_, []int{1}
}

func (m *GetUdeskUnReadMsgResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUdeskUnReadMsgResp) GetLastUnreadTime() int64 {
	if m != nil {
		return m.LastUnreadTime
	}
	return 0
}

// push的未读信息
type UdeskUnreadPush struct {
	LastUnreadTime int64 `protobuf:"varint,1,opt,name=last_unread_time,json=lastUnreadTime" json:"last_unread_time"`
}

func (m *UdeskUnreadPush) Reset()                    { *m = UdeskUnreadPush{} }
func (m *UdeskUnreadPush) String() string            { return proto.CompactTextString(m) }
func (*UdeskUnreadPush) ProtoMessage()               {}
func (*UdeskUnreadPush) Descriptor() ([]byte, []int) { return fileDescriptorUdeskApiLogic_, []int{2} }

func (m *UdeskUnreadPush) GetLastUnreadTime() int64 {
	if m != nil {
		return m.LastUnreadTime
	}
	return 0
}

// 检查vip客服入口权限
type CheckVipKefuAccessReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *CheckVipKefuAccessReq) Reset()         { *m = CheckVipKefuAccessReq{} }
func (m *CheckVipKefuAccessReq) String() string { return proto.CompactTextString(m) }
func (*CheckVipKefuAccessReq) ProtoMessage()    {}
func (*CheckVipKefuAccessReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUdeskApiLogic_, []int{3}
}

func (m *CheckVipKefuAccessReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type CheckVipKefuAccessResp struct {
	BaseResp       *ga.BaseResp      `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	AccessType     VipKefuAccessType `protobuf:"varint,2,opt,name=access_type,json=accessType,enum=ga.udeskapilogic.VipKefuAccessType" json:"access_type"`
	UnreadMsgCnt   uint32            `protobuf:"varint,3,opt,name=unread_msg_cnt,json=unreadMsgCnt" json:"unread_msg_cnt"`
	LastMsgTs      int64             `protobuf:"varint,4,opt,name=last_msg_ts,json=lastMsgTs" json:"last_msg_ts"`
	VipKefuUrl     string            `protobuf:"bytes,5,opt,name=vip_kefu_url,json=vipKefuUrl" json:"vip_kefu_url"`
	LastMsgContent string            `protobuf:"bytes,6,opt,name=last_msg_content,json=lastMsgContent" json:"last_msg_content"`
}

func (m *CheckVipKefuAccessResp) Reset()         { *m = CheckVipKefuAccessResp{} }
func (m *CheckVipKefuAccessResp) String() string { return proto.CompactTextString(m) }
func (*CheckVipKefuAccessResp) ProtoMessage()    {}
func (*CheckVipKefuAccessResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUdeskApiLogic_, []int{4}
}

func (m *CheckVipKefuAccessResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckVipKefuAccessResp) GetAccessType() VipKefuAccessType {
	if m != nil {
		return m.AccessType
	}
	return VipKefuAccessType_VIP_KEFU_ACCESS_TYPE_UNKNOWN
}

func (m *CheckVipKefuAccessResp) GetUnreadMsgCnt() uint32 {
	if m != nil {
		return m.UnreadMsgCnt
	}
	return 0
}

func (m *CheckVipKefuAccessResp) GetLastMsgTs() int64 {
	if m != nil {
		return m.LastMsgTs
	}
	return 0
}

func (m *CheckVipKefuAccessResp) GetVipKefuUrl() string {
	if m != nil {
		return m.VipKefuUrl
	}
	return ""
}

func (m *CheckVipKefuAccessResp) GetLastMsgContent() string {
	if m != nil {
		return m.LastMsgContent
	}
	return ""
}

// 确认收到vip客服入口可见
type AckVipKefuAccessReq struct {
	BaseReq    *ga.BaseReq       `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	AccessType VipKefuAccessType `protobuf:"varint,2,opt,name=access_type,json=accessType,enum=ga.udeskapilogic.VipKefuAccessType" json:"access_type"`
}

func (m *AckVipKefuAccessReq) Reset()         { *m = AckVipKefuAccessReq{} }
func (m *AckVipKefuAccessReq) String() string { return proto.CompactTextString(m) }
func (*AckVipKefuAccessReq) ProtoMessage()    {}
func (*AckVipKefuAccessReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUdeskApiLogic_, []int{5}
}

func (m *AckVipKefuAccessReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AckVipKefuAccessReq) GetAccessType() VipKefuAccessType {
	if m != nil {
		return m.AccessType
	}
	return VipKefuAccessType_VIP_KEFU_ACCESS_TYPE_UNKNOWN
}

type AckVipKefuAccessResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *AckVipKefuAccessResp) Reset()         { *m = AckVipKefuAccessResp{} }
func (m *AckVipKefuAccessResp) String() string { return proto.CompactTextString(m) }
func (*AckVipKefuAccessResp) ProtoMessage()    {}
func (*AckVipKefuAccessResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUdeskApiLogic_, []int{6}
}

func (m *AckVipKefuAccessResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 进入vip客服
type EnterVipKefuReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *EnterVipKefuReq) Reset()                    { *m = EnterVipKefuReq{} }
func (m *EnterVipKefuReq) String() string            { return proto.CompactTextString(m) }
func (*EnterVipKefuReq) ProtoMessage()               {}
func (*EnterVipKefuReq) Descriptor() ([]byte, []int) { return fileDescriptorUdeskApiLogic_, []int{7} }

func (m *EnterVipKefuReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type EnterVipKefuResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *EnterVipKefuResp) Reset()                    { *m = EnterVipKefuResp{} }
func (m *EnterVipKefuResp) String() string            { return proto.CompactTextString(m) }
func (*EnterVipKefuResp) ProtoMessage()               {}
func (*EnterVipKefuResp) Descriptor() ([]byte, []int) { return fileDescriptorUdeskApiLogic_, []int{8} }

func (m *EnterVipKefuResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// vip客服事件推送
type VipKefuPush struct {
	AccessType     VipKefuAccessType `protobuf:"varint,1,opt,name=access_type,json=accessType,enum=ga.udeskapilogic.VipKefuAccessType" json:"access_type"`
	UnreadMsgCnt   uint32            `protobuf:"varint,2,opt,name=unread_msg_cnt,json=unreadMsgCnt" json:"unread_msg_cnt"`
	LastMsgTs      int64             `protobuf:"varint,3,opt,name=last_msg_ts,json=lastMsgTs" json:"last_msg_ts"`
	LastMsgContent string            `protobuf:"bytes,4,opt,name=last_msg_content,json=lastMsgContent" json:"last_msg_content"`
}

func (m *VipKefuPush) Reset()                    { *m = VipKefuPush{} }
func (m *VipKefuPush) String() string            { return proto.CompactTextString(m) }
func (*VipKefuPush) ProtoMessage()               {}
func (*VipKefuPush) Descriptor() ([]byte, []int) { return fileDescriptorUdeskApiLogic_, []int{9} }

func (m *VipKefuPush) GetAccessType() VipKefuAccessType {
	if m != nil {
		return m.AccessType
	}
	return VipKefuAccessType_VIP_KEFU_ACCESS_TYPE_UNKNOWN
}

func (m *VipKefuPush) GetUnreadMsgCnt() uint32 {
	if m != nil {
		return m.UnreadMsgCnt
	}
	return 0
}

func (m *VipKefuPush) GetLastMsgTs() int64 {
	if m != nil {
		return m.LastMsgTs
	}
	return 0
}

func (m *VipKefuPush) GetLastMsgContent() string {
	if m != nil {
		return m.LastMsgContent
	}
	return ""
}

func init() {
	proto.RegisterType((*GetUdeskUnReadMsgReq)(nil), "ga.udeskapilogic.GetUdeskUnReadMsgReq")
	proto.RegisterType((*GetUdeskUnReadMsgResp)(nil), "ga.udeskapilogic.GetUdeskUnReadMsgResp")
	proto.RegisterType((*UdeskUnreadPush)(nil), "ga.udeskapilogic.UdeskUnreadPush")
	proto.RegisterType((*CheckVipKefuAccessReq)(nil), "ga.udeskapilogic.CheckVipKefuAccessReq")
	proto.RegisterType((*CheckVipKefuAccessResp)(nil), "ga.udeskapilogic.CheckVipKefuAccessResp")
	proto.RegisterType((*AckVipKefuAccessReq)(nil), "ga.udeskapilogic.AckVipKefuAccessReq")
	proto.RegisterType((*AckVipKefuAccessResp)(nil), "ga.udeskapilogic.AckVipKefuAccessResp")
	proto.RegisterType((*EnterVipKefuReq)(nil), "ga.udeskapilogic.EnterVipKefuReq")
	proto.RegisterType((*EnterVipKefuResp)(nil), "ga.udeskapilogic.EnterVipKefuResp")
	proto.RegisterType((*VipKefuPush)(nil), "ga.udeskapilogic.VipKefuPush")
	proto.RegisterEnum("ga.udeskapilogic.VipKefuAccessType", VipKefuAccessType_name, VipKefuAccessType_value)
}
func (m *GetUdeskUnReadMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUdeskUnReadMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GetUdeskUnReadMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUdeskUnReadMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.LastUnreadTime))
	return i, nil
}

func (m *UdeskUnreadPush) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UdeskUnreadPush) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.LastUnreadTime))
	return i, nil
}

func (m *CheckVipKefuAccessReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckVipKefuAccessReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.BaseReq.Size()))
		n3, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *CheckVipKefuAccessResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckVipKefuAccessResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.BaseResp.Size()))
		n4, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.AccessType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.UnreadMsgCnt))
	dAtA[i] = 0x20
	i++
	i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.LastMsgTs))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(len(m.VipKefuUrl)))
	i += copy(dAtA[i:], m.VipKefuUrl)
	dAtA[i] = 0x32
	i++
	i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(len(m.LastMsgContent)))
	i += copy(dAtA[i:], m.LastMsgContent)
	return i, nil
}

func (m *AckVipKefuAccessReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AckVipKefuAccessReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.AccessType))
	return i, nil
}

func (m *AckVipKefuAccessResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AckVipKefuAccessResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *EnterVipKefuReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EnterVipKefuReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.BaseReq.Size()))
		n7, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *EnterVipKefuResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EnterVipKefuResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.BaseResp.Size()))
		n8, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *VipKefuPush) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VipKefuPush) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.AccessType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.UnreadMsgCnt))
	dAtA[i] = 0x18
	i++
	i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(m.LastMsgTs))
	dAtA[i] = 0x22
	i++
	i = encodeVarintUdeskApiLogic_(dAtA, i, uint64(len(m.LastMsgContent)))
	i += copy(dAtA[i:], m.LastMsgContent)
	return i, nil
}

func encodeFixed64UdeskApiLogic_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32UdeskApiLogic_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintUdeskApiLogic_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GetUdeskUnReadMsgReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUdeskApiLogic_(uint64(l))
	}
	return n
}

func (m *GetUdeskUnReadMsgResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUdeskApiLogic_(uint64(l))
	}
	n += 1 + sovUdeskApiLogic_(uint64(m.LastUnreadTime))
	return n
}

func (m *UdeskUnreadPush) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUdeskApiLogic_(uint64(m.LastUnreadTime))
	return n
}

func (m *CheckVipKefuAccessReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUdeskApiLogic_(uint64(l))
	}
	return n
}

func (m *CheckVipKefuAccessResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUdeskApiLogic_(uint64(l))
	}
	n += 1 + sovUdeskApiLogic_(uint64(m.AccessType))
	n += 1 + sovUdeskApiLogic_(uint64(m.UnreadMsgCnt))
	n += 1 + sovUdeskApiLogic_(uint64(m.LastMsgTs))
	l = len(m.VipKefuUrl)
	n += 1 + l + sovUdeskApiLogic_(uint64(l))
	l = len(m.LastMsgContent)
	n += 1 + l + sovUdeskApiLogic_(uint64(l))
	return n
}

func (m *AckVipKefuAccessReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUdeskApiLogic_(uint64(l))
	}
	n += 1 + sovUdeskApiLogic_(uint64(m.AccessType))
	return n
}

func (m *AckVipKefuAccessResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUdeskApiLogic_(uint64(l))
	}
	return n
}

func (m *EnterVipKefuReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUdeskApiLogic_(uint64(l))
	}
	return n
}

func (m *EnterVipKefuResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUdeskApiLogic_(uint64(l))
	}
	return n
}

func (m *VipKefuPush) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUdeskApiLogic_(uint64(m.AccessType))
	n += 1 + sovUdeskApiLogic_(uint64(m.UnreadMsgCnt))
	n += 1 + sovUdeskApiLogic_(uint64(m.LastMsgTs))
	l = len(m.LastMsgContent)
	n += 1 + l + sovUdeskApiLogic_(uint64(l))
	return n
}

func sovUdeskApiLogic_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozUdeskApiLogic_(x uint64) (n int) {
	return sovUdeskApiLogic_(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *GetUdeskUnReadMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUdeskApiLogic_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUdeskUnReadMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUdeskUnReadMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUdeskApiLogic_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUdeskUnReadMsgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUdeskApiLogic_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUdeskUnReadMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUdeskUnReadMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUnreadTime", wireType)
			}
			m.LastUnreadTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUnreadTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUdeskApiLogic_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UdeskUnreadPush) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUdeskApiLogic_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UdeskUnreadPush: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UdeskUnreadPush: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUnreadTime", wireType)
			}
			m.LastUnreadTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUnreadTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUdeskApiLogic_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckVipKefuAccessReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUdeskApiLogic_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckVipKefuAccessReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckVipKefuAccessReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUdeskApiLogic_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckVipKefuAccessResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUdeskApiLogic_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckVipKefuAccessResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckVipKefuAccessResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessType", wireType)
			}
			m.AccessType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AccessType |= (VipKefuAccessType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UnreadMsgCnt", wireType)
			}
			m.UnreadMsgCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UnreadMsgCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastMsgTs", wireType)
			}
			m.LastMsgTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastMsgTs |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VipKefuUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VipKefuUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastMsgContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LastMsgContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUdeskApiLogic_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AckVipKefuAccessReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUdeskApiLogic_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AckVipKefuAccessReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AckVipKefuAccessReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessType", wireType)
			}
			m.AccessType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AccessType |= (VipKefuAccessType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUdeskApiLogic_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AckVipKefuAccessResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUdeskApiLogic_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AckVipKefuAccessResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AckVipKefuAccessResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUdeskApiLogic_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *EnterVipKefuReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUdeskApiLogic_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: EnterVipKefuReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: EnterVipKefuReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUdeskApiLogic_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *EnterVipKefuResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUdeskApiLogic_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: EnterVipKefuResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: EnterVipKefuResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUdeskApiLogic_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VipKefuPush) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUdeskApiLogic_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VipKefuPush: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VipKefuPush: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessType", wireType)
			}
			m.AccessType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AccessType |= (VipKefuAccessType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UnreadMsgCnt", wireType)
			}
			m.UnreadMsgCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UnreadMsgCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastMsgTs", wireType)
			}
			m.LastMsgTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastMsgTs |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastMsgContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LastMsgContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUdeskApiLogic_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUdeskApiLogic_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipUdeskApiLogic_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUdeskApiLogic_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUdeskApiLogic_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthUdeskApiLogic_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowUdeskApiLogic_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipUdeskApiLogic_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthUdeskApiLogic_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUdeskApiLogic_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("udesk-api-logic_.proto", fileDescriptorUdeskApiLogic_) }

var fileDescriptorUdeskApiLogic_ = []byte{
	// 565 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x54, 0xdd, 0x6e, 0x12, 0x41,
	0x18, 0x65, 0x00, 0xb5, 0x0c, 0x94, 0xe2, 0xda, 0x36, 0x1b, 0x35, 0x94, 0xac, 0xa6, 0x41, 0x92,
	0x2e, 0x09, 0x89, 0x17, 0x5e, 0xa8, 0x59, 0x70, 0x35, 0x15, 0x41, 0xb2, 0xb0, 0x18, 0xbd, 0x99,
	0x0c, 0xcb, 0x74, 0xbb, 0x61, 0x7f, 0x86, 0x9d, 0xd9, 0x46, 0x1e, 0xc1, 0x3b, 0x5f, 0xc7, 0x37,
	0xe8, 0xa5, 0xf7, 0x26, 0xc6, 0xe0, 0x8b, 0x98, 0xfd, 0xb1, 0x96, 0xba, 0x35, 0xa2, 0xbd, 0x9b,
	0x7c, 0xe7, 0x7c, 0xe7, 0x9b, 0xef, 0xcc, 0xd9, 0x85, 0xbb, 0xc1, 0x94, 0xb0, 0xd9, 0x01, 0xa6,
	0xd6, 0x81, 0xed, 0x99, 0x96, 0x81, 0x64, 0xea, 0x7b, 0xdc, 0x13, 0x2a, 0x26, 0x96, 0x23, 0x08,
	0x53, 0x2b, 0x02, 0x6e, 0x6f, 0x9a, 0x18, 0x4d, 0x30, 0x23, 0x31, 0x41, 0x7a, 0x02, 0xb7, 0x5f,
	0x10, 0xae, 0x87, 0x14, 0xdd, 0xd5, 0x08, 0x9e, 0xf6, 0x98, 0xa9, 0x91, 0xb9, 0xb0, 0x0f, 0x37,
	0x42, 0x16, 0xf2, 0xc9, 0x5c, 0x04, 0xb5, 0x6c, 0xbd, 0xd8, 0x2a, 0xca, 0x26, 0x96, 0xdb, 0x98,
	0x11, 0x8d, 0xcc, 0xb5, 0x1b, 0x93, 0xf8, 0x20, 0xf9, 0x70, 0x27, 0xa5, 0x9f, 0x51, 0xe1, 0x01,
	0x2c, 0x24, 0x02, 0x8c, 0x26, 0x0a, 0xa5, 0x5f, 0x0a, 0x8c, 0x6a, 0x1b, 0x93, 0xe4, 0x24, 0xc8,
	0xb0, 0x62, 0x63, 0xc6, 0x51, 0xe0, 0xfa, 0x04, 0x4f, 0x11, 0xb7, 0x1c, 0x22, 0x66, 0x6b, 0xa0,
	0x9e, 0x6b, 0xe7, 0x4f, 0xbf, 0xee, 0x65, 0xb4, 0x72, 0x88, 0xea, 0x11, 0x38, 0xb2, 0x1c, 0x22,
	0x29, 0x70, 0x2b, 0x19, 0x18, 0x96, 0x06, 0x01, 0x3b, 0x4e, 0x95, 0x00, 0x7f, 0x90, 0x78, 0x0a,
	0x77, 0x3a, 0xc7, 0xc4, 0x98, 0x8d, 0x2d, 0xda, 0x25, 0x47, 0x81, 0x62, 0x18, 0x84, 0xb1, 0x75,
	0xf6, 0xfe, 0x94, 0x85, 0xbb, 0x69, 0x0a, 0xeb, 0x6d, 0xfe, 0x12, 0x16, 0x71, 0xd4, 0x88, 0xf8,
	0x82, 0xc6, 0x4b, 0x97, 0x5b, 0xf7, 0xe4, 0x8b, 0x8f, 0x26, 0xaf, 0x0c, 0x19, 0x2d, 0x28, 0x49,
	0xd6, 0x82, 0xf8, 0xac, 0x22, 0x34, 0x60, 0x39, 0xd9, 0xde, 0x61, 0x26, 0x32, 0x5c, 0x2e, 0xe6,
	0x6a, 0xa0, 0xbe, 0x99, 0x30, 0x4b, 0x31, 0xd6, 0x63, 0x66, 0xc7, 0xe5, 0xc2, 0x7d, 0x58, 0x8c,
	0xec, 0x0a, 0x99, 0x9c, 0x89, 0xf9, 0x73, 0x4e, 0x15, 0x42, 0xa0, 0xc7, 0xcc, 0x11, 0x13, 0xf6,
	0x61, 0xe9, 0xc4, 0xa2, 0x68, 0x46, 0x8e, 0x02, 0x14, 0xf8, 0xb6, 0x78, 0xad, 0x06, 0xea, 0x85,
	0x9f, 0x93, 0x4f, 0xe2, 0x2b, 0xe9, 0xbe, 0x7d, 0x66, 0x7e, 0x34, 0xd7, 0x73, 0x39, 0x71, 0xb9,
	0x78, 0xfd, 0x1c, 0xb7, 0x9c, 0x48, 0x76, 0x62, 0x4c, 0xfa, 0x00, 0xe0, 0x2d, 0xe5, 0xdf, 0xbd,
	0xbf, 0x4a, 0xd7, 0x24, 0x05, 0x6e, 0x2b, 0xff, 0xf7, 0x88, 0xd2, 0x23, 0xb8, 0xa5, 0xba, 0x9c,
	0xf8, 0x89, 0xc8, 0x3a, 0x29, 0x7a, 0x0c, 0x2b, 0xab, 0xad, 0xeb, 0x4d, 0xfe, 0x02, 0x60, 0x31,
	0x69, 0x8d, 0xbe, 0x82, 0x0b, 0xc6, 0x80, 0xab, 0x8d, 0x53, 0xf6, 0x6f, 0xe3, 0x94, 0x4b, 0x8f,
	0x53, 0x5a, 0x4c, 0xf2, 0x97, 0xc7, 0xa4, 0xf1, 0x1e, 0xde, 0xfc, 0xed, 0xa2, 0x42, 0x0d, 0xde,
	0x1d, 0x1f, 0x0e, 0x50, 0x57, 0x7d, 0xae, 0x23, 0xa5, 0xd3, 0x51, 0x87, 0x43, 0x34, 0x7a, 0x3b,
	0x50, 0x91, 0xde, 0xef, 0xf6, 0x5f, 0xbf, 0xe9, 0x57, 0x32, 0xc2, 0x1e, 0xbc, 0x93, 0xca, 0x50,
	0xfb, 0x4a, 0xfb, 0x95, 0x5a, 0x01, 0x97, 0x4a, 0x3c, 0x3b, 0x1c, 0x46, 0x8c, 0x6c, 0x7b, 0x7c,
	0xba, 0xac, 0x82, 0xcf, 0xcb, 0x2a, 0xf8, 0xb6, 0xac, 0x82, 0x8f, 0xdf, 0xab, 0x19, 0x28, 0x1a,
	0x9e, 0x23, 0x2f, 0xac, 0x85, 0x17, 0x84, 0x6e, 0x3a, 0xde, 0x94, 0xd8, 0xf1, 0x0f, 0xf4, 0x5d,
	0xc3, 0xf4, 0x6c, 0xec, 0x9a, 0xf2, 0xc3, 0x16, 0xe7, 0xb2, 0xe1, 0x39, 0xcd, 0xa8, 0x6c, 0x78,
	0x76, 0x13, 0x53, 0xda, 0x5c, 0xf1, 0xfd, 0x47, 0x00, 0x00, 0x00, 0xff, 0xff, 0xde, 0x21, 0xa0,
	0x0f, 0xa6, 0x05, 0x00, 0x00,
}
