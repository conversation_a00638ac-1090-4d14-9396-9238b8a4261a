// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-present-runway-logic_.proto

package channel_present_runway_logic // import "golang.52tt.com/protocol/app/channel-present-runway-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 房间送礼的火箭跑道信息
type ChannelPresentRunwayInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	FaceMd5              string   `protobuf:"bytes,4,opt,name=face_md5,json=faceMd5,proto3" json:"face_md5,omitempty"`
	ChannelId            uint32   `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Level                uint32   `protobuf:"varint,6,opt,name=level,proto3" json:"level,omitempty"`
	ExpiredTime          uint32   `protobuf:"varint,7,opt,name=expired_time,json=expiredTime,proto3" json:"expired_time,omitempty"`
	PresentValue         uint32   `protobuf:"varint,8,opt,name=present_value,json=presentValue,proto3" json:"present_value,omitempty"`
	PresentValueMin      uint32   `protobuf:"varint,9,opt,name=present_value_min,json=presentValueMin,proto3" json:"present_value_min,omitempty"`
	PresentValueMax      uint32   `protobuf:"varint,10,opt,name=present_value_max,json=presentValueMax,proto3" json:"present_value_max,omitempty"`
	PresentAddValue      uint32   `protobuf:"varint,11,opt,name=present_add_value,json=presentAddValue,proto3" json:"present_add_value,omitempty"`
	ItemId               uint32   `protobuf:"varint,12,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelPresentRunwayInfo) Reset()         { *m = ChannelPresentRunwayInfo{} }
func (m *ChannelPresentRunwayInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelPresentRunwayInfo) ProtoMessage()    {}
func (*ChannelPresentRunwayInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_present_runway_logic__b44b4f2c81740a08, []int{0}
}
func (m *ChannelPresentRunwayInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPresentRunwayInfo.Unmarshal(m, b)
}
func (m *ChannelPresentRunwayInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPresentRunwayInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelPresentRunwayInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPresentRunwayInfo.Merge(dst, src)
}
func (m *ChannelPresentRunwayInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelPresentRunwayInfo.Size(m)
}
func (m *ChannelPresentRunwayInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPresentRunwayInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPresentRunwayInfo proto.InternalMessageInfo

func (m *ChannelPresentRunwayInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelPresentRunwayInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ChannelPresentRunwayInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ChannelPresentRunwayInfo) GetFaceMd5() string {
	if m != nil {
		return m.FaceMd5
	}
	return ""
}

func (m *ChannelPresentRunwayInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelPresentRunwayInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *ChannelPresentRunwayInfo) GetExpiredTime() uint32 {
	if m != nil {
		return m.ExpiredTime
	}
	return 0
}

func (m *ChannelPresentRunwayInfo) GetPresentValue() uint32 {
	if m != nil {
		return m.PresentValue
	}
	return 0
}

func (m *ChannelPresentRunwayInfo) GetPresentValueMin() uint32 {
	if m != nil {
		return m.PresentValueMin
	}
	return 0
}

func (m *ChannelPresentRunwayInfo) GetPresentValueMax() uint32 {
	if m != nil {
		return m.PresentValueMax
	}
	return 0
}

func (m *ChannelPresentRunwayInfo) GetPresentAddValue() uint32 {
	if m != nil {
		return m.PresentAddValue
	}
	return 0
}

func (m *ChannelPresentRunwayInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

// 跑道特效
type ChannelPresentRunwayEffect struct {
	RunwayPresentBase    uint32   `protobuf:"varint,1,opt,name=runway_present_base,json=runwayPresentBase,proto3" json:"runway_present_base,omitempty"`
	RunwayAddSeconds     uint32   `protobuf:"varint,2,opt,name=runway_add_seconds,json=runwayAddSeconds,proto3" json:"runway_add_seconds,omitempty"`
	MaxRunwayAddSeconds  uint32   `protobuf:"varint,3,opt,name=max_runway_add_seconds,json=maxRunwayAddSeconds,proto3" json:"max_runway_add_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelPresentRunwayEffect) Reset()         { *m = ChannelPresentRunwayEffect{} }
func (m *ChannelPresentRunwayEffect) String() string { return proto.CompactTextString(m) }
func (*ChannelPresentRunwayEffect) ProtoMessage()    {}
func (*ChannelPresentRunwayEffect) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_present_runway_logic__b44b4f2c81740a08, []int{1}
}
func (m *ChannelPresentRunwayEffect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelPresentRunwayEffect.Unmarshal(m, b)
}
func (m *ChannelPresentRunwayEffect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelPresentRunwayEffect.Marshal(b, m, deterministic)
}
func (dst *ChannelPresentRunwayEffect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelPresentRunwayEffect.Merge(dst, src)
}
func (m *ChannelPresentRunwayEffect) XXX_Size() int {
	return xxx_messageInfo_ChannelPresentRunwayEffect.Size(m)
}
func (m *ChannelPresentRunwayEffect) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelPresentRunwayEffect.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelPresentRunwayEffect proto.InternalMessageInfo

func (m *ChannelPresentRunwayEffect) GetRunwayPresentBase() uint32 {
	if m != nil {
		return m.RunwayPresentBase
	}
	return 0
}

func (m *ChannelPresentRunwayEffect) GetRunwayAddSeconds() uint32 {
	if m != nil {
		return m.RunwayAddSeconds
	}
	return 0
}

func (m *ChannelPresentRunwayEffect) GetMaxRunwayAddSeconds() uint32 {
	if m != nil {
		return m.MaxRunwayAddSeconds
	}
	return 0
}

// 获取房间内的火箭跑道信息
type GetChannelPresentRunwayListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelPresentRunwayListReq) Reset()         { *m = GetChannelPresentRunwayListReq{} }
func (m *GetChannelPresentRunwayListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelPresentRunwayListReq) ProtoMessage()    {}
func (*GetChannelPresentRunwayListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_present_runway_logic__b44b4f2c81740a08, []int{2}
}
func (m *GetChannelPresentRunwayListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPresentRunwayListReq.Unmarshal(m, b)
}
func (m *GetChannelPresentRunwayListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPresentRunwayListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelPresentRunwayListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPresentRunwayListReq.Merge(dst, src)
}
func (m *GetChannelPresentRunwayListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelPresentRunwayListReq.Size(m)
}
func (m *GetChannelPresentRunwayListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPresentRunwayListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPresentRunwayListReq proto.InternalMessageInfo

func (m *GetChannelPresentRunwayListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelPresentRunwayListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelPresentRunwayListResp struct {
	BaseResp             *app.BaseResp               `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RunwayList           []*ChannelPresentRunwayInfo `protobuf:"bytes,2,rep,name=runway_list,json=runwayList,proto3" json:"runway_list,omitempty"`
	RunwayEffect         *ChannelPresentRunwayEffect `protobuf:"bytes,3,opt,name=runway_effect,json=runwayEffect,proto3" json:"runway_effect,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetChannelPresentRunwayListResp) Reset()         { *m = GetChannelPresentRunwayListResp{} }
func (m *GetChannelPresentRunwayListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelPresentRunwayListResp) ProtoMessage()    {}
func (*GetChannelPresentRunwayListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_present_runway_logic__b44b4f2c81740a08, []int{3}
}
func (m *GetChannelPresentRunwayListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPresentRunwayListResp.Unmarshal(m, b)
}
func (m *GetChannelPresentRunwayListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPresentRunwayListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelPresentRunwayListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPresentRunwayListResp.Merge(dst, src)
}
func (m *GetChannelPresentRunwayListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelPresentRunwayListResp.Size(m)
}
func (m *GetChannelPresentRunwayListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPresentRunwayListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPresentRunwayListResp proto.InternalMessageInfo

func (m *GetChannelPresentRunwayListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelPresentRunwayListResp) GetRunwayList() []*ChannelPresentRunwayInfo {
	if m != nil {
		return m.RunwayList
	}
	return nil
}

func (m *GetChannelPresentRunwayListResp) GetRunwayEffect() *ChannelPresentRunwayEffect {
	if m != nil {
		return m.RunwayEffect
	}
	return nil
}

func init() {
	proto.RegisterType((*ChannelPresentRunwayInfo)(nil), "ga.channel_present_runway_logic.ChannelPresentRunwayInfo")
	proto.RegisterType((*ChannelPresentRunwayEffect)(nil), "ga.channel_present_runway_logic.ChannelPresentRunwayEffect")
	proto.RegisterType((*GetChannelPresentRunwayListReq)(nil), "ga.channel_present_runway_logic.GetChannelPresentRunwayListReq")
	proto.RegisterType((*GetChannelPresentRunwayListResp)(nil), "ga.channel_present_runway_logic.GetChannelPresentRunwayListResp")
}

func init() {
	proto.RegisterFile("channel-present-runway-logic_.proto", fileDescriptor_channel_present_runway_logic__b44b4f2c81740a08)
}

var fileDescriptor_channel_present_runway_logic__b44b4f2c81740a08 = []byte{
	// 551 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x94, 0xdb, 0x6e, 0xd3, 0x4c,
	0x10, 0xc7, 0x95, 0xe4, 0x6b, 0x0e, 0xe3, 0x44, 0x5f, 0xbb, 0x45, 0xb0, 0x44, 0x82, 0x96, 0x54,
	0x42, 0x01, 0x11, 0x47, 0x4a, 0x95, 0x8b, 0x8a, 0xab, 0x16, 0x21, 0x14, 0x89, 0x4a, 0xc8, 0x1c,
	0x2e, 0x7a, 0x63, 0x36, 0xde, 0x89, 0x59, 0x61, 0xef, 0x3a, 0xb6, 0x53, 0x92, 0x57, 0xe0, 0x05,
	0x78, 0x0c, 0x5e, 0x11, 0xed, 0x21, 0x25, 0x8d, 0x42, 0x91, 0xb8, 0xdb, 0x99, 0xf9, 0x8d, 0xe7,
	0xbf, 0xf9, 0xef, 0x04, 0x4e, 0xa2, 0x2f, 0x4c, 0x4a, 0x4c, 0x06, 0x59, 0x8e, 0x05, 0xca, 0x72,
	0x90, 0x2f, 0xe4, 0x37, 0xb6, 0x1a, 0x24, 0x2a, 0x16, 0x51, 0xe8, 0x67, 0xb9, 0x2a, 0x15, 0x39,
	0x8a, 0x99, 0xef, 0xb8, 0xd0, 0x71, 0xa1, 0xe5, 0x42, 0xc3, 0x75, 0x3b, 0x31, 0x0b, 0xa7, 0xac,
	0x40, 0xcb, 0xf7, 0x7e, 0xd4, 0x80, 0xbe, 0xb2, 0xfc, 0x3b, 0x8b, 0x07, 0x86, 0x9e, 0xc8, 0x99,
	0x22, 0xfb, 0x50, 0x5b, 0x08, 0x4e, 0x2b, 0xc7, 0x95, 0x7e, 0x27, 0xd0, 0x47, 0x42, 0xa1, 0xc1,
	0xa2, 0x48, 0x2d, 0x64, 0x49, 0xab, 0xc7, 0x95, 0x7e, 0x2b, 0x58, 0x87, 0xa4, 0x0b, 0x4d, 0x29,
	0xa2, 0xaf, 0x92, 0xa5, 0x48, 0x6b, 0xa6, 0x74, 0x13, 0x93, 0x87, 0xd0, 0x9c, 0xb1, 0x08, 0xc3,
	0x94, 0x8f, 0xe9, 0x7f, 0xb6, 0x4d, 0xc7, 0x97, 0x7c, 0x4c, 0x1e, 0x01, 0xac, 0xe5, 0x0a, 0x4e,
	0xf7, 0xcc, 0xa4, 0x96, 0xcb, 0x4c, 0x38, 0xb9, 0x07, 0x7b, 0x09, 0x5e, 0x63, 0x42, 0xeb, 0xa6,
	0x62, 0x03, 0xf2, 0x04, 0xda, 0xb8, 0xcc, 0x44, 0x8e, 0x3c, 0x2c, 0x45, 0x8a, 0xb4, 0x61, 0x8a,
	0x9e, 0xcb, 0x7d, 0x10, 0x29, 0x92, 0x13, 0xe8, 0xac, 0xaf, 0x7f, 0xcd, 0x92, 0x05, 0xd2, 0xa6,
	0x61, 0xda, 0x2e, 0xf9, 0x49, 0xe7, 0xc8, 0x73, 0x38, 0xb8, 0x05, 0x85, 0xa9, 0x90, 0xb4, 0x65,
	0xc0, 0xff, 0x37, 0xc1, 0x4b, 0x21, 0x77, 0xb0, 0x6c, 0x49, 0x61, 0x07, 0xcb, 0x96, 0x9b, 0x2c,
	0xe3, 0xdc, 0x09, 0xf0, 0x6e, 0xb1, 0xe7, 0x9c, 0x5b, 0x0d, 0x0f, 0xa0, 0x21, 0x4a, 0x4c, 0xf5,
	0xed, 0xdb, 0x86, 0xa8, 0xeb, 0x70, 0xc2, 0x7b, 0x3f, 0x2b, 0xd0, 0xdd, 0xe5, 0xcc, 0xeb, 0xd9,
	0x0c, 0xa3, 0x92, 0xf8, 0x70, 0xe8, 0x7c, 0x5d, 0x8f, 0xd2, 0xae, 0x3a, 0xaf, 0x0e, 0x6c, 0xc9,
	0xf5, 0x5d, 0xb0, 0x02, 0xc9, 0x0b, 0x20, 0x8e, 0xd7, 0x92, 0x0a, 0x8c, 0x94, 0xe4, 0x85, 0x31,
	0xb1, 0x13, 0xec, 0xdb, 0xca, 0x39, 0xe7, 0xef, 0x6d, 0x9e, 0x9c, 0xc2, 0xfd, 0x94, 0x2d, 0xc3,
	0x1d, 0x1d, 0x35, 0xd3, 0x71, 0x98, 0xb2, 0x65, 0xb0, 0xd5, 0xd4, 0x8b, 0xe1, 0xf1, 0x1b, 0x2c,
	0x77, 0x69, 0x7e, 0x2b, 0x8a, 0x32, 0xc0, 0x39, 0x79, 0x0a, 0x4d, 0xad, 0x32, 0xcc, 0x71, 0x6e,
	0x94, 0x7a, 0x23, 0xcf, 0x8f, 0x99, 0xaf, 0x05, 0x06, 0x38, 0x0f, 0x1a, 0x53, 0x7b, 0xd8, 0x7a,
	0x15, 0xd5, 0xad, 0x57, 0xd1, 0xfb, 0x5e, 0x85, 0xa3, 0x3b, 0x27, 0x15, 0x19, 0x79, 0x06, 0x2d,
	0x37, 0xaa, 0xc8, 0xdc, 0xac, 0xf6, 0xef, 0x59, 0x45, 0x16, 0x34, 0xa7, 0xee, 0x44, 0xae, 0xc0,
	0x5b, 0xaf, 0x88, 0x28, 0xf4, 0xc3, 0xae, 0xf5, 0xbd, 0xd1, 0x99, 0xff, 0x97, 0x4d, 0xf2, 0xff,
	0xb4, 0x36, 0x01, 0xe4, 0x37, 0x52, 0xc8, 0x67, 0xe8, 0xb8, 0x26, 0x34, 0xbe, 0x99, 0xdf, 0xcf,
	0x1b, 0xbd, 0xfc, 0xa7, 0xaf, 0x5b, 0xeb, 0x83, 0x76, 0xbe, 0x11, 0x5d, 0x7c, 0x04, 0x1a, 0xa9,
	0xd4, 0x5f, 0x89, 0x95, 0x5a, 0xe8, 0xaf, 0xa6, 0x8a, 0x63, 0x62, 0xb7, 0xfb, 0xea, 0x2c, 0x56,
	0x09, 0x93, 0xb1, 0x3f, 0x1e, 0x95, 0xa5, 0x1f, 0xa9, 0x74, 0x68, 0xd2, 0x91, 0x4a, 0x86, 0x2c,
	0xcb, 0x86, 0x77, 0xfd, 0x9f, 0x4c, 0xeb, 0x06, 0x3d, 0xfd, 0x15, 0x00, 0x00, 0xff, 0xff, 0xf9,
	0x60, 0x02, 0xa1, 0x76, 0x04, 0x00, 0x00,
}
