// Code generated by protoc-gen-go. DO NOT EDIT.
// source: pushd.proto

package push // import "golang.52tt.com/protocol/app/push"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import any "github.com/golang/protobuf/ptypes/any"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SubscribeType int32

const (
	SubscribeType_SUBSCRIBE   SubscribeType = 0
	SubscribeType_UNSUBSCRIBE SubscribeType = 1
)

var SubscribeType_name = map[int32]string{
	0: "SUBSCRIBE",
	1: "UNSUBSCRIBE",
}
var SubscribeType_value = map[string]int32{
	"SUBSCRIBE":   0,
	"UNSUBSCRIBE": 1,
}

func (x SubscribeType) String() string {
	return proto.EnumName(SubscribeType_name, int32(x))
}
func (SubscribeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{0}
}

type BroadcastChannelType int32

const (
	BroadcastChannelType_DEFAULT BroadcastChannelType = 0
)

var BroadcastChannelType_name = map[int32]string{
	0: "DEFAULT",
}
var BroadcastChannelType_value = map[string]int32{
	"DEFAULT": 0,
}

func (x BroadcastChannelType) String() string {
	return proto.EnumName(BroadcastChannelType_name, int32(x))
}
func (BroadcastChannelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{1}
}

type SubscribeErrorCode int32

const (
	SubscribeErrorCode_REPEATED         SubscribeErrorCode = 0
	SubscribeErrorCode_INTERNAL         SubscribeErrorCode = 1
	SubscribeErrorCode_PermissionDenied SubscribeErrorCode = 2
)

var SubscribeErrorCode_name = map[int32]string{
	0: "REPEATED",
	1: "INTERNAL",
	2: "PermissionDenied",
}
var SubscribeErrorCode_value = map[string]int32{
	"REPEATED":         0,
	"INTERNAL":         1,
	"PermissionDenied": 2,
}

func (x SubscribeErrorCode) String() string {
	return proto.EnumName(SubscribeErrorCode_name, int32(x))
}
func (SubscribeErrorCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{2}
}

type CloseCode int32

const (
	CloseCode_NO_AUTH  CloseCode = 0
	CloseCode_REDIRECT CloseCode = 1
	CloseCode_EVICT    CloseCode = 2
	CloseCode_REFUSED  CloseCode = 3
)

var CloseCode_name = map[int32]string{
	0: "NO_AUTH",
	1: "REDIRECT",
	2: "EVICT",
	3: "REFUSED",
}
var CloseCode_value = map[string]int32{
	"NO_AUTH":  0,
	"REDIRECT": 1,
	"EVICT":    2,
	"REFUSED":  3,
}

func (x CloseCode) String() string {
	return proto.EnumName(CloseCode_name, int32(x))
}
func (CloseCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{3}
}

type RenewTokenError int32

const (
	RenewTokenError_Unknown         RenewTokenError = 0
	RenewTokenError_OK              RenewTokenError = 1
	RenewTokenError_InvalidToken    RenewTokenError = 2
	RenewTokenError_ChannelNotMatch RenewTokenError = 3
)

var RenewTokenError_name = map[int32]string{
	0: "Unknown",
	1: "OK",
	2: "InvalidToken",
	3: "ChannelNotMatch",
}
var RenewTokenError_value = map[string]int32{
	"Unknown":         0,
	"OK":              1,
	"InvalidToken":    2,
	"ChannelNotMatch": 3,
}

func (x RenewTokenError) String() string {
	return proto.EnumName(RenewTokenError_name, int32(x))
}
func (RenewTokenError) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{4}
}

type PushReq struct {
	// Types that are valid to be assigned to RequestType:
	//	*PushReq_Ping
	//	*PushReq_ChannelSubscribePushReq
	//	*PushReq_Private
	//	*PushReq_RenewTokenReq
	RequestType          isPushReq_RequestType `protobuf_oneof:"RequestType"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *PushReq) Reset()         { *m = PushReq{} }
func (m *PushReq) String() string { return proto.CompactTextString(m) }
func (*PushReq) ProtoMessage()    {}
func (*PushReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{0}
}
func (m *PushReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushReq.Unmarshal(m, b)
}
func (m *PushReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushReq.Marshal(b, m, deterministic)
}
func (dst *PushReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushReq.Merge(dst, src)
}
func (m *PushReq) XXX_Size() int {
	return xxx_messageInfo_PushReq.Size(m)
}
func (m *PushReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushReq proto.InternalMessageInfo

type isPushReq_RequestType interface {
	isPushReq_RequestType()
}

type PushReq_Ping struct {
	Ping *Ping `protobuf:"bytes,1,opt,name=ping,proto3,oneof"`
}

type PushReq_ChannelSubscribePushReq struct {
	ChannelSubscribePushReq *ChannelSubscribePushReq `protobuf:"bytes,2,opt,name=channel_subscribe_push_req,json=channelSubscribePushReq,proto3,oneof"`
}

type PushReq_Private struct {
	Private *PrivateSubscribeRequest `protobuf:"bytes,3,opt,name=private,proto3,oneof"`
}

type PushReq_RenewTokenReq struct {
	RenewTokenReq *RenewTokenReq `protobuf:"bytes,6,opt,name=renew_token_req,json=renewTokenReq,proto3,oneof"`
}

func (*PushReq_Ping) isPushReq_RequestType() {}

func (*PushReq_ChannelSubscribePushReq) isPushReq_RequestType() {}

func (*PushReq_Private) isPushReq_RequestType() {}

func (*PushReq_RenewTokenReq) isPushReq_RequestType() {}

func (m *PushReq) GetRequestType() isPushReq_RequestType {
	if m != nil {
		return m.RequestType
	}
	return nil
}

func (m *PushReq) GetPing() *Ping {
	if x, ok := m.GetRequestType().(*PushReq_Ping); ok {
		return x.Ping
	}
	return nil
}

func (m *PushReq) GetChannelSubscribePushReq() *ChannelSubscribePushReq {
	if x, ok := m.GetRequestType().(*PushReq_ChannelSubscribePushReq); ok {
		return x.ChannelSubscribePushReq
	}
	return nil
}

func (m *PushReq) GetPrivate() *PrivateSubscribeRequest {
	if x, ok := m.GetRequestType().(*PushReq_Private); ok {
		return x.Private
	}
	return nil
}

func (m *PushReq) GetRenewTokenReq() *RenewTokenReq {
	if x, ok := m.GetRequestType().(*PushReq_RenewTokenReq); ok {
		return x.RenewTokenReq
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*PushReq) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _PushReq_OneofMarshaler, _PushReq_OneofUnmarshaler, _PushReq_OneofSizer, []interface{}{
		(*PushReq_Ping)(nil),
		(*PushReq_ChannelSubscribePushReq)(nil),
		(*PushReq_Private)(nil),
		(*PushReq_RenewTokenReq)(nil),
	}
}

func _PushReq_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*PushReq)
	// RequestType
	switch x := m.RequestType.(type) {
	case *PushReq_Ping:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Ping); err != nil {
			return err
		}
	case *PushReq_ChannelSubscribePushReq:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ChannelSubscribePushReq); err != nil {
			return err
		}
	case *PushReq_Private:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Private); err != nil {
			return err
		}
	case *PushReq_RenewTokenReq:
		b.EncodeVarint(6<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.RenewTokenReq); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("PushReq.RequestType has unexpected type %T", x)
	}
	return nil
}

func _PushReq_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*PushReq)
	switch tag {
	case 1: // RequestType.ping
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(Ping)
		err := b.DecodeMessage(msg)
		m.RequestType = &PushReq_Ping{msg}
		return true, err
	case 2: // RequestType.channel_subscribe_push_req
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ChannelSubscribePushReq)
		err := b.DecodeMessage(msg)
		m.RequestType = &PushReq_ChannelSubscribePushReq{msg}
		return true, err
	case 3: // RequestType.private
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(PrivateSubscribeRequest)
		err := b.DecodeMessage(msg)
		m.RequestType = &PushReq_Private{msg}
		return true, err
	case 6: // RequestType.renew_token_req
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(RenewTokenReq)
		err := b.DecodeMessage(msg)
		m.RequestType = &PushReq_RenewTokenReq{msg}
		return true, err
	default:
		return false, nil
	}
}

func _PushReq_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*PushReq)
	// RequestType
	switch x := m.RequestType.(type) {
	case *PushReq_Ping:
		s := proto.Size(x.Ping)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *PushReq_ChannelSubscribePushReq:
		s := proto.Size(x.ChannelSubscribePushReq)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *PushReq_Private:
		s := proto.Size(x.Private)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *PushReq_RenewTokenReq:
		s := proto.Size(x.RenewTokenReq)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type PushResp struct {
	// Types that are valid to be assigned to ResponseType:
	//	*PushResp_Ping
	//	*PushResp_Welcome
	//	*PushResp_Message
	//	*PushResp_SubscribeResp
	//	*PushResp_Close
	//	*PushResp_Private
	//	*PushResp_RenewTokenResp
	ResponseType         isPushResp_ResponseType `protobuf_oneof:"ResponseType"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *PushResp) Reset()         { *m = PushResp{} }
func (m *PushResp) String() string { return proto.CompactTextString(m) }
func (*PushResp) ProtoMessage()    {}
func (*PushResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{1}
}
func (m *PushResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushResp.Unmarshal(m, b)
}
func (m *PushResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushResp.Marshal(b, m, deterministic)
}
func (dst *PushResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushResp.Merge(dst, src)
}
func (m *PushResp) XXX_Size() int {
	return xxx_messageInfo_PushResp.Size(m)
}
func (m *PushResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushResp proto.InternalMessageInfo

type isPushResp_ResponseType interface {
	isPushResp_ResponseType()
}

type PushResp_Ping struct {
	Ping *Ping `protobuf:"bytes,1,opt,name=ping,proto3,oneof"`
}

type PushResp_Welcome struct {
	Welcome *Welcome `protobuf:"bytes,2,opt,name=welcome,proto3,oneof"`
}

type PushResp_Message struct {
	Message *Message `protobuf:"bytes,3,opt,name=message,proto3,oneof"`
}

type PushResp_SubscribeResp struct {
	SubscribeResp *ChannelSubscribeResp `protobuf:"bytes,4,opt,name=subscribe_resp,json=subscribeResp,proto3,oneof"`
}

type PushResp_Close struct {
	Close *CloseResp `protobuf:"bytes,5,opt,name=close,proto3,oneof"`
}

type PushResp_Private struct {
	Private *PrivateSubscribeResponse `protobuf:"bytes,6,opt,name=private,proto3,oneof"`
}

type PushResp_RenewTokenResp struct {
	RenewTokenResp *RenewTokenResp `protobuf:"bytes,7,opt,name=renew_token_resp,json=renewTokenResp,proto3,oneof"`
}

func (*PushResp_Ping) isPushResp_ResponseType() {}

func (*PushResp_Welcome) isPushResp_ResponseType() {}

func (*PushResp_Message) isPushResp_ResponseType() {}

func (*PushResp_SubscribeResp) isPushResp_ResponseType() {}

func (*PushResp_Close) isPushResp_ResponseType() {}

func (*PushResp_Private) isPushResp_ResponseType() {}

func (*PushResp_RenewTokenResp) isPushResp_ResponseType() {}

func (m *PushResp) GetResponseType() isPushResp_ResponseType {
	if m != nil {
		return m.ResponseType
	}
	return nil
}

func (m *PushResp) GetPing() *Ping {
	if x, ok := m.GetResponseType().(*PushResp_Ping); ok {
		return x.Ping
	}
	return nil
}

func (m *PushResp) GetWelcome() *Welcome {
	if x, ok := m.GetResponseType().(*PushResp_Welcome); ok {
		return x.Welcome
	}
	return nil
}

func (m *PushResp) GetMessage() *Message {
	if x, ok := m.GetResponseType().(*PushResp_Message); ok {
		return x.Message
	}
	return nil
}

func (m *PushResp) GetSubscribeResp() *ChannelSubscribeResp {
	if x, ok := m.GetResponseType().(*PushResp_SubscribeResp); ok {
		return x.SubscribeResp
	}
	return nil
}

func (m *PushResp) GetClose() *CloseResp {
	if x, ok := m.GetResponseType().(*PushResp_Close); ok {
		return x.Close
	}
	return nil
}

func (m *PushResp) GetPrivate() *PrivateSubscribeResponse {
	if x, ok := m.GetResponseType().(*PushResp_Private); ok {
		return x.Private
	}
	return nil
}

func (m *PushResp) GetRenewTokenResp() *RenewTokenResp {
	if x, ok := m.GetResponseType().(*PushResp_RenewTokenResp); ok {
		return x.RenewTokenResp
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*PushResp) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _PushResp_OneofMarshaler, _PushResp_OneofUnmarshaler, _PushResp_OneofSizer, []interface{}{
		(*PushResp_Ping)(nil),
		(*PushResp_Welcome)(nil),
		(*PushResp_Message)(nil),
		(*PushResp_SubscribeResp)(nil),
		(*PushResp_Close)(nil),
		(*PushResp_Private)(nil),
		(*PushResp_RenewTokenResp)(nil),
	}
}

func _PushResp_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*PushResp)
	// ResponseType
	switch x := m.ResponseType.(type) {
	case *PushResp_Ping:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Ping); err != nil {
			return err
		}
	case *PushResp_Welcome:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Welcome); err != nil {
			return err
		}
	case *PushResp_Message:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Message); err != nil {
			return err
		}
	case *PushResp_SubscribeResp:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.SubscribeResp); err != nil {
			return err
		}
	case *PushResp_Close:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Close); err != nil {
			return err
		}
	case *PushResp_Private:
		b.EncodeVarint(6<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Private); err != nil {
			return err
		}
	case *PushResp_RenewTokenResp:
		b.EncodeVarint(7<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.RenewTokenResp); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("PushResp.ResponseType has unexpected type %T", x)
	}
	return nil
}

func _PushResp_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*PushResp)
	switch tag {
	case 1: // ResponseType.ping
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(Ping)
		err := b.DecodeMessage(msg)
		m.ResponseType = &PushResp_Ping{msg}
		return true, err
	case 2: // ResponseType.welcome
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(Welcome)
		err := b.DecodeMessage(msg)
		m.ResponseType = &PushResp_Welcome{msg}
		return true, err
	case 3: // ResponseType.message
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(Message)
		err := b.DecodeMessage(msg)
		m.ResponseType = &PushResp_Message{msg}
		return true, err
	case 4: // ResponseType.subscribe_resp
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ChannelSubscribeResp)
		err := b.DecodeMessage(msg)
		m.ResponseType = &PushResp_SubscribeResp{msg}
		return true, err
	case 5: // ResponseType.close
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(CloseResp)
		err := b.DecodeMessage(msg)
		m.ResponseType = &PushResp_Close{msg}
		return true, err
	case 6: // ResponseType.private
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(PrivateSubscribeResponse)
		err := b.DecodeMessage(msg)
		m.ResponseType = &PushResp_Private{msg}
		return true, err
	case 7: // ResponseType.renew_token_resp
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(RenewTokenResp)
		err := b.DecodeMessage(msg)
		m.ResponseType = &PushResp_RenewTokenResp{msg}
		return true, err
	default:
		return false, nil
	}
}

func _PushResp_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*PushResp)
	// ResponseType
	switch x := m.ResponseType.(type) {
	case *PushResp_Ping:
		s := proto.Size(x.Ping)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *PushResp_Welcome:
		s := proto.Size(x.Welcome)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *PushResp_Message:
		s := proto.Size(x.Message)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *PushResp_SubscribeResp:
		s := proto.Size(x.SubscribeResp)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *PushResp_Close:
		s := proto.Size(x.Close)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *PushResp_Private:
		s := proto.Size(x.Private)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *PushResp_RenewTokenResp:
		s := proto.Size(x.RenewTokenResp)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type Welcome struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Welcome) Reset()         { *m = Welcome{} }
func (m *Welcome) String() string { return proto.CompactTextString(m) }
func (*Welcome) ProtoMessage()    {}
func (*Welcome) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{2}
}
func (m *Welcome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Welcome.Unmarshal(m, b)
}
func (m *Welcome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Welcome.Marshal(b, m, deterministic)
}
func (dst *Welcome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Welcome.Merge(dst, src)
}
func (m *Welcome) XXX_Size() int {
	return xxx_messageInfo_Welcome.Size(m)
}
func (m *Welcome) XXX_DiscardUnknown() {
	xxx_messageInfo_Welcome.DiscardUnknown(m)
}

var xxx_messageInfo_Welcome proto.InternalMessageInfo

func (m *Welcome) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type PrivateSubscribeRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PrivateSubscribeRequest) Reset()         { *m = PrivateSubscribeRequest{} }
func (m *PrivateSubscribeRequest) String() string { return proto.CompactTextString(m) }
func (*PrivateSubscribeRequest) ProtoMessage()    {}
func (*PrivateSubscribeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{3}
}
func (m *PrivateSubscribeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrivateSubscribeRequest.Unmarshal(m, b)
}
func (m *PrivateSubscribeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrivateSubscribeRequest.Marshal(b, m, deterministic)
}
func (dst *PrivateSubscribeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrivateSubscribeRequest.Merge(dst, src)
}
func (m *PrivateSubscribeRequest) XXX_Size() int {
	return xxx_messageInfo_PrivateSubscribeRequest.Size(m)
}
func (m *PrivateSubscribeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PrivateSubscribeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PrivateSubscribeRequest proto.InternalMessageInfo

type PrivateSubscribeResponse struct {
	Success              bool     `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PrivateSubscribeResponse) Reset()         { *m = PrivateSubscribeResponse{} }
func (m *PrivateSubscribeResponse) String() string { return proto.CompactTextString(m) }
func (*PrivateSubscribeResponse) ProtoMessage()    {}
func (*PrivateSubscribeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{4}
}
func (m *PrivateSubscribeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrivateSubscribeResponse.Unmarshal(m, b)
}
func (m *PrivateSubscribeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrivateSubscribeResponse.Marshal(b, m, deterministic)
}
func (dst *PrivateSubscribeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrivateSubscribeResponse.Merge(dst, src)
}
func (m *PrivateSubscribeResponse) XXX_Size() int {
	return xxx_messageInfo_PrivateSubscribeResponse.Size(m)
}
func (m *PrivateSubscribeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PrivateSubscribeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PrivateSubscribeResponse proto.InternalMessageInfo

func (m *PrivateSubscribeResponse) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func (m *PrivateSubscribeResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type ChannelSubscribePushReq struct {
	ChannelId uint64 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// Deprecated
	ChannelType          uint32        `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Type                 SubscribeType `protobuf:"varint,3,opt,name=type,proto3,enum=ga.SubscribeType" json:"type,omitempty"`
	Token                []byte        `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelSubscribePushReq) Reset()         { *m = ChannelSubscribePushReq{} }
func (m *ChannelSubscribePushReq) String() string { return proto.CompactTextString(m) }
func (*ChannelSubscribePushReq) ProtoMessage()    {}
func (*ChannelSubscribePushReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{5}
}
func (m *ChannelSubscribePushReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSubscribePushReq.Unmarshal(m, b)
}
func (m *ChannelSubscribePushReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSubscribePushReq.Marshal(b, m, deterministic)
}
func (dst *ChannelSubscribePushReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSubscribePushReq.Merge(dst, src)
}
func (m *ChannelSubscribePushReq) XXX_Size() int {
	return xxx_messageInfo_ChannelSubscribePushReq.Size(m)
}
func (m *ChannelSubscribePushReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSubscribePushReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSubscribePushReq proto.InternalMessageInfo

func (m *ChannelSubscribePushReq) GetChannelId() uint64 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelSubscribePushReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelSubscribePushReq) GetType() SubscribeType {
	if m != nil {
		return m.Type
	}
	return SubscribeType_SUBSCRIBE
}

func (m *ChannelSubscribePushReq) GetToken() []byte {
	if m != nil {
		return m.Token
	}
	return nil
}

//  not implemented
type BroadcastChannel struct {
	BroadcastChannelId   uint64               `protobuf:"varint,1,opt,name=broadcast_channel_id,json=broadcastChannelId,proto3" json:"broadcast_channel_id,omitempty"`
	Type                 BroadcastChannelType `protobuf:"varint,2,opt,name=type,proto3,enum=ga.BroadcastChannelType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BroadcastChannel) Reset()         { *m = BroadcastChannel{} }
func (m *BroadcastChannel) String() string { return proto.CompactTextString(m) }
func (*BroadcastChannel) ProtoMessage()    {}
func (*BroadcastChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{6}
}
func (m *BroadcastChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BroadcastChannel.Unmarshal(m, b)
}
func (m *BroadcastChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BroadcastChannel.Marshal(b, m, deterministic)
}
func (dst *BroadcastChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BroadcastChannel.Merge(dst, src)
}
func (m *BroadcastChannel) XXX_Size() int {
	return xxx_messageInfo_BroadcastChannel.Size(m)
}
func (m *BroadcastChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_BroadcastChannel.DiscardUnknown(m)
}

var xxx_messageInfo_BroadcastChannel proto.InternalMessageInfo

func (m *BroadcastChannel) GetBroadcastChannelId() uint64 {
	if m != nil {
		return m.BroadcastChannelId
	}
	return 0
}

func (m *BroadcastChannel) GetType() BroadcastChannelType {
	if m != nil {
		return m.Type
	}
	return BroadcastChannelType_DEFAULT
}

type ChannelSubscribeResp struct {
	// Types that are valid to be assigned to SubscribeRespType:
	//	*ChannelSubscribeResp_Ok
	//	*ChannelSubscribeResp_Redirect
	//	*ChannelSubscribeResp_Error
	SubscribeRespType    isChannelSubscribeResp_SubscribeRespType `protobuf_oneof:"SubscribeRespType"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *ChannelSubscribeResp) Reset()         { *m = ChannelSubscribeResp{} }
func (m *ChannelSubscribeResp) String() string { return proto.CompactTextString(m) }
func (*ChannelSubscribeResp) ProtoMessage()    {}
func (*ChannelSubscribeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{7}
}
func (m *ChannelSubscribeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSubscribeResp.Unmarshal(m, b)
}
func (m *ChannelSubscribeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSubscribeResp.Marshal(b, m, deterministic)
}
func (dst *ChannelSubscribeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSubscribeResp.Merge(dst, src)
}
func (m *ChannelSubscribeResp) XXX_Size() int {
	return xxx_messageInfo_ChannelSubscribeResp.Size(m)
}
func (m *ChannelSubscribeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSubscribeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSubscribeResp proto.InternalMessageInfo

type isChannelSubscribeResp_SubscribeRespType interface {
	isChannelSubscribeResp_SubscribeRespType()
}

type ChannelSubscribeResp_Ok struct {
	Ok *SubscribeOk `protobuf:"bytes,1,opt,name=ok,proto3,oneof"`
}

type ChannelSubscribeResp_Redirect struct {
	Redirect *Redirect `protobuf:"bytes,2,opt,name=redirect,proto3,oneof"`
}

type ChannelSubscribeResp_Error struct {
	Error *SubscribeError `protobuf:"bytes,3,opt,name=error,proto3,oneof"`
}

func (*ChannelSubscribeResp_Ok) isChannelSubscribeResp_SubscribeRespType() {}

func (*ChannelSubscribeResp_Redirect) isChannelSubscribeResp_SubscribeRespType() {}

func (*ChannelSubscribeResp_Error) isChannelSubscribeResp_SubscribeRespType() {}

func (m *ChannelSubscribeResp) GetSubscribeRespType() isChannelSubscribeResp_SubscribeRespType {
	if m != nil {
		return m.SubscribeRespType
	}
	return nil
}

func (m *ChannelSubscribeResp) GetOk() *SubscribeOk {
	if x, ok := m.GetSubscribeRespType().(*ChannelSubscribeResp_Ok); ok {
		return x.Ok
	}
	return nil
}

func (m *ChannelSubscribeResp) GetRedirect() *Redirect {
	if x, ok := m.GetSubscribeRespType().(*ChannelSubscribeResp_Redirect); ok {
		return x.Redirect
	}
	return nil
}

func (m *ChannelSubscribeResp) GetError() *SubscribeError {
	if x, ok := m.GetSubscribeRespType().(*ChannelSubscribeResp_Error); ok {
		return x.Error
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*ChannelSubscribeResp) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _ChannelSubscribeResp_OneofMarshaler, _ChannelSubscribeResp_OneofUnmarshaler, _ChannelSubscribeResp_OneofSizer, []interface{}{
		(*ChannelSubscribeResp_Ok)(nil),
		(*ChannelSubscribeResp_Redirect)(nil),
		(*ChannelSubscribeResp_Error)(nil),
	}
}

func _ChannelSubscribeResp_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*ChannelSubscribeResp)
	// SubscribeRespType
	switch x := m.SubscribeRespType.(type) {
	case *ChannelSubscribeResp_Ok:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Ok); err != nil {
			return err
		}
	case *ChannelSubscribeResp_Redirect:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Redirect); err != nil {
			return err
		}
	case *ChannelSubscribeResp_Error:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Error); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("ChannelSubscribeResp.SubscribeRespType has unexpected type %T", x)
	}
	return nil
}

func _ChannelSubscribeResp_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*ChannelSubscribeResp)
	switch tag {
	case 1: // SubscribeRespType.ok
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SubscribeOk)
		err := b.DecodeMessage(msg)
		m.SubscribeRespType = &ChannelSubscribeResp_Ok{msg}
		return true, err
	case 2: // SubscribeRespType.redirect
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(Redirect)
		err := b.DecodeMessage(msg)
		m.SubscribeRespType = &ChannelSubscribeResp_Redirect{msg}
		return true, err
	case 3: // SubscribeRespType.error
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SubscribeError)
		err := b.DecodeMessage(msg)
		m.SubscribeRespType = &ChannelSubscribeResp_Error{msg}
		return true, err
	default:
		return false, nil
	}
}

func _ChannelSubscribeResp_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*ChannelSubscribeResp)
	// SubscribeRespType
	switch x := m.SubscribeRespType.(type) {
	case *ChannelSubscribeResp_Ok:
		s := proto.Size(x.Ok)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ChannelSubscribeResp_Redirect:
		s := proto.Size(x.Redirect)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ChannelSubscribeResp_Error:
		s := proto.Size(x.Error)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type SubscribeError struct {
	Code                 SubscribeErrorCode `protobuf:"varint,1,opt,name=code,proto3,enum=ga.SubscribeErrorCode" json:"code,omitempty"`
	Detail               string             `protobuf:"bytes,2,opt,name=detail,proto3" json:"detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SubscribeError) Reset()         { *m = SubscribeError{} }
func (m *SubscribeError) String() string { return proto.CompactTextString(m) }
func (*SubscribeError) ProtoMessage()    {}
func (*SubscribeError) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{8}
}
func (m *SubscribeError) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeError.Unmarshal(m, b)
}
func (m *SubscribeError) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeError.Marshal(b, m, deterministic)
}
func (dst *SubscribeError) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeError.Merge(dst, src)
}
func (m *SubscribeError) XXX_Size() int {
	return xxx_messageInfo_SubscribeError.Size(m)
}
func (m *SubscribeError) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeError.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeError proto.InternalMessageInfo

func (m *SubscribeError) GetCode() SubscribeErrorCode {
	if m != nil {
		return m.Code
	}
	return SubscribeErrorCode_REPEATED
}

func (m *SubscribeError) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

type Redirect struct {
	Locations            []*PushLocation `protobuf:"bytes,1,rep,name=locations,proto3" json:"locations,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *Redirect) Reset()         { *m = Redirect{} }
func (m *Redirect) String() string { return proto.CompactTextString(m) }
func (*Redirect) ProtoMessage()    {}
func (*Redirect) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{9}
}
func (m *Redirect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Redirect.Unmarshal(m, b)
}
func (m *Redirect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Redirect.Marshal(b, m, deterministic)
}
func (dst *Redirect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Redirect.Merge(dst, src)
}
func (m *Redirect) XXX_Size() int {
	return xxx_messageInfo_Redirect.Size(m)
}
func (m *Redirect) XXX_DiscardUnknown() {
	xxx_messageInfo_Redirect.DiscardUnknown(m)
}

var xxx_messageInfo_Redirect proto.InternalMessageInfo

func (m *Redirect) GetLocations() []*PushLocation {
	if m != nil {
		return m.Locations
	}
	return nil
}

type SubscribeOk struct {
	ChannelId            uint64   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubscribeOk) Reset()         { *m = SubscribeOk{} }
func (m *SubscribeOk) String() string { return proto.CompactTextString(m) }
func (*SubscribeOk) ProtoMessage()    {}
func (*SubscribeOk) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{10}
}
func (m *SubscribeOk) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeOk.Unmarshal(m, b)
}
func (m *SubscribeOk) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeOk.Marshal(b, m, deterministic)
}
func (dst *SubscribeOk) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeOk.Merge(dst, src)
}
func (m *SubscribeOk) XXX_Size() int {
	return xxx_messageInfo_SubscribeOk.Size(m)
}
func (m *SubscribeOk) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeOk.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeOk proto.InternalMessageInfo

func (m *SubscribeOk) GetChannelId() uint64 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type PushLocation struct {
	// 对应header [push-pushd]的值
	Proxy string `protobuf:"bytes,1,opt,name=proxy,proto3" json:"proxy,omitempty"`
	// dial地址 (理论上都是一样的) 如果和当前连接是一样的，不需要重新dial,重新请求Push流即可
	// /如果不一样, 需要断开旧连接 dial
	Addr                 string   `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushLocation) Reset()         { *m = PushLocation{} }
func (m *PushLocation) String() string { return proto.CompactTextString(m) }
func (*PushLocation) ProtoMessage()    {}
func (*PushLocation) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{11}
}
func (m *PushLocation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushLocation.Unmarshal(m, b)
}
func (m *PushLocation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushLocation.Marshal(b, m, deterministic)
}
func (dst *PushLocation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushLocation.Merge(dst, src)
}
func (m *PushLocation) XXX_Size() int {
	return xxx_messageInfo_PushLocation.Size(m)
}
func (m *PushLocation) XXX_DiscardUnknown() {
	xxx_messageInfo_PushLocation.DiscardUnknown(m)
}

var xxx_messageInfo_PushLocation proto.InternalMessageInfo

func (m *PushLocation) GetProxy() string {
	if m != nil {
		return m.Proxy
	}
	return ""
}

func (m *PushLocation) GetAddr() string {
	if m != nil {
		return m.Addr
	}
	return ""
}

// 服务端主动断开stream (异常断开不会触发)
type CloseResp struct {
	Code                 CloseCode `protobuf:"varint,1,opt,name=code,proto3,enum=ga.CloseCode" json:"code,omitempty"`
	Detail               string    `protobuf:"bytes,2,opt,name=detail,proto3" json:"detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *CloseResp) Reset()         { *m = CloseResp{} }
func (m *CloseResp) String() string { return proto.CompactTextString(m) }
func (*CloseResp) ProtoMessage()    {}
func (*CloseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{12}
}
func (m *CloseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CloseResp.Unmarshal(m, b)
}
func (m *CloseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CloseResp.Marshal(b, m, deterministic)
}
func (dst *CloseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseResp.Merge(dst, src)
}
func (m *CloseResp) XXX_Size() int {
	return xxx_messageInfo_CloseResp.Size(m)
}
func (m *CloseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseResp.DiscardUnknown(m)
}

var xxx_messageInfo_CloseResp proto.InternalMessageInfo

func (m *CloseResp) GetCode() CloseCode {
	if m != nil {
		return m.Code
	}
	return CloseCode_NO_AUTH
}

func (m *CloseResp) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

type Ping struct {
	Ack                  bool     `protobuf:"varint,1,opt,name=ack,proto3" json:"ack,omitempty"`
	Seq                  uint64   `protobuf:"varint,2,opt,name=seq,proto3" json:"seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ping) Reset()         { *m = Ping{} }
func (m *Ping) String() string { return proto.CompactTextString(m) }
func (*Ping) ProtoMessage()    {}
func (*Ping) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{13}
}
func (m *Ping) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ping.Unmarshal(m, b)
}
func (m *Ping) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ping.Marshal(b, m, deterministic)
}
func (dst *Ping) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ping.Merge(dst, src)
}
func (m *Ping) XXX_Size() int {
	return xxx_messageInfo_Ping.Size(m)
}
func (m *Ping) XXX_DiscardUnknown() {
	xxx_messageInfo_Ping.DiscardUnknown(m)
}

var xxx_messageInfo_Ping proto.InternalMessageInfo

func (m *Ping) GetAck() bool {
	if m != nil {
		return m.Ack
	}
	return false
}

func (m *Ping) GetSeq() uint64 {
	if m != nil {
		return m.Seq
	}
	return 0
}

type RenewTokenReq struct {
	Token                []byte   `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	ChannelId            uint64   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RenewTokenReq) Reset()         { *m = RenewTokenReq{} }
func (m *RenewTokenReq) String() string { return proto.CompactTextString(m) }
func (*RenewTokenReq) ProtoMessage()    {}
func (*RenewTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{14}
}
func (m *RenewTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RenewTokenReq.Unmarshal(m, b)
}
func (m *RenewTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RenewTokenReq.Marshal(b, m, deterministic)
}
func (dst *RenewTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RenewTokenReq.Merge(dst, src)
}
func (m *RenewTokenReq) XXX_Size() int {
	return xxx_messageInfo_RenewTokenReq.Size(m)
}
func (m *RenewTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RenewTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_RenewTokenReq proto.InternalMessageInfo

func (m *RenewTokenReq) GetToken() []byte {
	if m != nil {
		return m.Token
	}
	return nil
}

func (m *RenewTokenReq) GetChannelId() uint64 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type RenewTokenResp struct {
	Result               RenewTokenError `protobuf:"varint,1,opt,name=result,proto3,enum=ga.RenewTokenError" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *RenewTokenResp) Reset()         { *m = RenewTokenResp{} }
func (m *RenewTokenResp) String() string { return proto.CompactTextString(m) }
func (*RenewTokenResp) ProtoMessage()    {}
func (*RenewTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{15}
}
func (m *RenewTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RenewTokenResp.Unmarshal(m, b)
}
func (m *RenewTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RenewTokenResp.Marshal(b, m, deterministic)
}
func (dst *RenewTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RenewTokenResp.Merge(dst, src)
}
func (m *RenewTokenResp) XXX_Size() int {
	return xxx_messageInfo_RenewTokenResp.Size(m)
}
func (m *RenewTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RenewTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_RenewTokenResp proto.InternalMessageInfo

func (m *RenewTokenResp) GetResult() RenewTokenError {
	if m != nil {
		return m.Result
	}
	return RenewTokenError_Unknown
}

type Message struct {
	SeqId                uint64   `protobuf:"varint,1,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	Payload              []byte   `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	MessageId            []byte   `protobuf:"bytes,3,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	ServerTime           uint64   `protobuf:"varint,4,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	PayloadAny           *any.Any `protobuf:"bytes,5,opt,name=payload_any,json=payloadAny,proto3" json:"payload_any,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Message) Reset()         { *m = Message{} }
func (m *Message) String() string { return proto.CompactTextString(m) }
func (*Message) ProtoMessage()    {}
func (*Message) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{16}
}
func (m *Message) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message.Unmarshal(m, b)
}
func (m *Message) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message.Marshal(b, m, deterministic)
}
func (dst *Message) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message.Merge(dst, src)
}
func (m *Message) XXX_Size() int {
	return xxx_messageInfo_Message.Size(m)
}
func (m *Message) XXX_DiscardUnknown() {
	xxx_messageInfo_Message.DiscardUnknown(m)
}

var xxx_messageInfo_Message proto.InternalMessageInfo

func (m *Message) GetSeqId() uint64 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *Message) GetPayload() []byte {
	if m != nil {
		return m.Payload
	}
	return nil
}

func (m *Message) GetMessageId() []byte {
	if m != nil {
		return m.MessageId
	}
	return nil
}

func (m *Message) GetServerTime() uint64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *Message) GetPayloadAny() *any.Any {
	if m != nil {
		return m.PayloadAny
	}
	return nil
}

type SequenceScope struct {
	// Types that are valid to be assigned to Scope:
	//	*SequenceScope_List
	//	*SequenceScope_Range
	//	*SequenceScope_Important_
	Scope                isSequenceScope_Scope `protobuf_oneof:"scope"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *SequenceScope) Reset()         { *m = SequenceScope{} }
func (m *SequenceScope) String() string { return proto.CompactTextString(m) }
func (*SequenceScope) ProtoMessage()    {}
func (*SequenceScope) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{17}
}
func (m *SequenceScope) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SequenceScope.Unmarshal(m, b)
}
func (m *SequenceScope) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SequenceScope.Marshal(b, m, deterministic)
}
func (dst *SequenceScope) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SequenceScope.Merge(dst, src)
}
func (m *SequenceScope) XXX_Size() int {
	return xxx_messageInfo_SequenceScope.Size(m)
}
func (m *SequenceScope) XXX_DiscardUnknown() {
	xxx_messageInfo_SequenceScope.DiscardUnknown(m)
}

var xxx_messageInfo_SequenceScope proto.InternalMessageInfo

type isSequenceScope_Scope interface {
	isSequenceScope_Scope()
}

type SequenceScope_List struct {
	List *SequenceScope_SequenceList `protobuf:"bytes,1,opt,name=list,proto3,oneof"`
}

type SequenceScope_Range struct {
	Range *SequenceScope_SequenceRange `protobuf:"bytes,2,opt,name=range,proto3,oneof"`
}

type SequenceScope_Important_ struct {
	Important *SequenceScope_Important `protobuf:"bytes,3,opt,name=important,proto3,oneof"`
}

func (*SequenceScope_List) isSequenceScope_Scope() {}

func (*SequenceScope_Range) isSequenceScope_Scope() {}

func (*SequenceScope_Important_) isSequenceScope_Scope() {}

func (m *SequenceScope) GetScope() isSequenceScope_Scope {
	if m != nil {
		return m.Scope
	}
	return nil
}

func (m *SequenceScope) GetList() *SequenceScope_SequenceList {
	if x, ok := m.GetScope().(*SequenceScope_List); ok {
		return x.List
	}
	return nil
}

func (m *SequenceScope) GetRange() *SequenceScope_SequenceRange {
	if x, ok := m.GetScope().(*SequenceScope_Range); ok {
		return x.Range
	}
	return nil
}

func (m *SequenceScope) GetImportant() *SequenceScope_Important {
	if x, ok := m.GetScope().(*SequenceScope_Important_); ok {
		return x.Important
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*SequenceScope) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _SequenceScope_OneofMarshaler, _SequenceScope_OneofUnmarshaler, _SequenceScope_OneofSizer, []interface{}{
		(*SequenceScope_List)(nil),
		(*SequenceScope_Range)(nil),
		(*SequenceScope_Important_)(nil),
	}
}

func _SequenceScope_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*SequenceScope)
	// scope
	switch x := m.Scope.(type) {
	case *SequenceScope_List:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.List); err != nil {
			return err
		}
	case *SequenceScope_Range:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Range); err != nil {
			return err
		}
	case *SequenceScope_Important_:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Important); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("SequenceScope.Scope has unexpected type %T", x)
	}
	return nil
}

func _SequenceScope_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*SequenceScope)
	switch tag {
	case 1: // scope.list
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SequenceScope_SequenceList)
		err := b.DecodeMessage(msg)
		m.Scope = &SequenceScope_List{msg}
		return true, err
	case 2: // scope.range
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SequenceScope_SequenceRange)
		err := b.DecodeMessage(msg)
		m.Scope = &SequenceScope_Range{msg}
		return true, err
	case 3: // scope.important
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SequenceScope_Important)
		err := b.DecodeMessage(msg)
		m.Scope = &SequenceScope_Important_{msg}
		return true, err
	default:
		return false, nil
	}
}

func _SequenceScope_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*SequenceScope)
	// scope
	switch x := m.Scope.(type) {
	case *SequenceScope_List:
		s := proto.Size(x.List)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SequenceScope_Range:
		s := proto.Size(x.Range)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SequenceScope_Important_:
		s := proto.Size(x.Important)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// 序列号列表
type SequenceScope_SequenceList struct {
	Sequences            []uint64 `protobuf:"varint,1,rep,packed,name=sequences,proto3" json:"sequences,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SequenceScope_SequenceList) Reset()         { *m = SequenceScope_SequenceList{} }
func (m *SequenceScope_SequenceList) String() string { return proto.CompactTextString(m) }
func (*SequenceScope_SequenceList) ProtoMessage()    {}
func (*SequenceScope_SequenceList) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{17, 0}
}
func (m *SequenceScope_SequenceList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SequenceScope_SequenceList.Unmarshal(m, b)
}
func (m *SequenceScope_SequenceList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SequenceScope_SequenceList.Marshal(b, m, deterministic)
}
func (dst *SequenceScope_SequenceList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SequenceScope_SequenceList.Merge(dst, src)
}
func (m *SequenceScope_SequenceList) XXX_Size() int {
	return xxx_messageInfo_SequenceScope_SequenceList.Size(m)
}
func (m *SequenceScope_SequenceList) XXX_DiscardUnknown() {
	xxx_messageInfo_SequenceScope_SequenceList.DiscardUnknown(m)
}

var xxx_messageInfo_SequenceScope_SequenceList proto.InternalMessageInfo

func (m *SequenceScope_SequenceList) GetSequences() []uint64 {
	if m != nil {
		return m.Sequences
	}
	return nil
}

// 序列号范围
type SequenceScope_SequenceRange struct {
	Start                uint64   `protobuf:"varint,1,opt,name=start,proto3" json:"start,omitempty"`
	End                  uint64   `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SequenceScope_SequenceRange) Reset()         { *m = SequenceScope_SequenceRange{} }
func (m *SequenceScope_SequenceRange) String() string { return proto.CompactTextString(m) }
func (*SequenceScope_SequenceRange) ProtoMessage()    {}
func (*SequenceScope_SequenceRange) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{17, 1}
}
func (m *SequenceScope_SequenceRange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SequenceScope_SequenceRange.Unmarshal(m, b)
}
func (m *SequenceScope_SequenceRange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SequenceScope_SequenceRange.Marshal(b, m, deterministic)
}
func (dst *SequenceScope_SequenceRange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SequenceScope_SequenceRange.Merge(dst, src)
}
func (m *SequenceScope_SequenceRange) XXX_Size() int {
	return xxx_messageInfo_SequenceScope_SequenceRange.Size(m)
}
func (m *SequenceScope_SequenceRange) XXX_DiscardUnknown() {
	xxx_messageInfo_SequenceScope_SequenceRange.DiscardUnknown(m)
}

var xxx_messageInfo_SequenceScope_SequenceRange proto.InternalMessageInfo

func (m *SequenceScope_SequenceRange) GetStart() uint64 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *SequenceScope_SequenceRange) GetEnd() uint64 {
	if m != nil {
		return m.End
	}
	return 0
}

// 重要消息，如全房间推送
type SequenceScope_Important struct {
	MessageId            []byte   `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SequenceScope_Important) Reset()         { *m = SequenceScope_Important{} }
func (m *SequenceScope_Important) String() string { return proto.CompactTextString(m) }
func (*SequenceScope_Important) ProtoMessage()    {}
func (*SequenceScope_Important) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{17, 2}
}
func (m *SequenceScope_Important) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SequenceScope_Important.Unmarshal(m, b)
}
func (m *SequenceScope_Important) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SequenceScope_Important.Marshal(b, m, deterministic)
}
func (dst *SequenceScope_Important) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SequenceScope_Important.Merge(dst, src)
}
func (m *SequenceScope_Important) XXX_Size() int {
	return xxx_messageInfo_SequenceScope_Important.Size(m)
}
func (m *SequenceScope_Important) XXX_DiscardUnknown() {
	xxx_messageInfo_SequenceScope_Important.DiscardUnknown(m)
}

var xxx_messageInfo_SequenceScope_Important proto.InternalMessageInfo

func (m *SequenceScope_Important) GetMessageId() []byte {
	if m != nil {
		return m.MessageId
	}
	return nil
}

// 房间推送消息ACK
// 除重要消息外，客户端不需要每条消息报一次，目前提供两种方式，一种是上报序列号列表，一种是上报序列号范围
// 无法连接，连接异常中断等其它异常通过http接口上报
type MessageAck struct {
	Succeed              bool           `protobuf:"varint,1,opt,name=succeed,proto3" json:"succeed,omitempty"`
	Scope                *SequenceScope `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *MessageAck) Reset()         { *m = MessageAck{} }
func (m *MessageAck) String() string { return proto.CompactTextString(m) }
func (*MessageAck) ProtoMessage()    {}
func (*MessageAck) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{18}
}
func (m *MessageAck) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageAck.Unmarshal(m, b)
}
func (m *MessageAck) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageAck.Marshal(b, m, deterministic)
}
func (dst *MessageAck) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageAck.Merge(dst, src)
}
func (m *MessageAck) XXX_Size() int {
	return xxx_messageInfo_MessageAck.Size(m)
}
func (m *MessageAck) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageAck.DiscardUnknown(m)
}

var xxx_messageInfo_MessageAck proto.InternalMessageInfo

func (m *MessageAck) GetSucceed() bool {
	if m != nil {
		return m.Succeed
	}
	return false
}

func (m *MessageAck) GetScope() *SequenceScope {
	if m != nil {
		return m.Scope
	}
	return nil
}

type BatchMessageAck struct {
	Acks                 []*MessageAck `protobuf:"bytes,1,rep,name=acks,proto3" json:"acks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchMessageAck) Reset()         { *m = BatchMessageAck{} }
func (m *BatchMessageAck) String() string { return proto.CompactTextString(m) }
func (*BatchMessageAck) ProtoMessage()    {}
func (*BatchMessageAck) Descriptor() ([]byte, []int) {
	return fileDescriptor_pushd_6bee2e7c9bf157ae, []int{19}
}
func (m *BatchMessageAck) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchMessageAck.Unmarshal(m, b)
}
func (m *BatchMessageAck) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchMessageAck.Marshal(b, m, deterministic)
}
func (dst *BatchMessageAck) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchMessageAck.Merge(dst, src)
}
func (m *BatchMessageAck) XXX_Size() int {
	return xxx_messageInfo_BatchMessageAck.Size(m)
}
func (m *BatchMessageAck) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchMessageAck.DiscardUnknown(m)
}

var xxx_messageInfo_BatchMessageAck proto.InternalMessageInfo

func (m *BatchMessageAck) GetAcks() []*MessageAck {
	if m != nil {
		return m.Acks
	}
	return nil
}

func init() {
	proto.RegisterType((*PushReq)(nil), "ga.PushReq")
	proto.RegisterType((*PushResp)(nil), "ga.PushResp")
	proto.RegisterType((*Welcome)(nil), "ga.Welcome")
	proto.RegisterType((*PrivateSubscribeRequest)(nil), "ga.PrivateSubscribeRequest")
	proto.RegisterType((*PrivateSubscribeResponse)(nil), "ga.PrivateSubscribeResponse")
	proto.RegisterType((*ChannelSubscribePushReq)(nil), "ga.ChannelSubscribePushReq")
	proto.RegisterType((*BroadcastChannel)(nil), "ga.BroadcastChannel")
	proto.RegisterType((*ChannelSubscribeResp)(nil), "ga.ChannelSubscribeResp")
	proto.RegisterType((*SubscribeError)(nil), "ga.SubscribeError")
	proto.RegisterType((*Redirect)(nil), "ga.Redirect")
	proto.RegisterType((*SubscribeOk)(nil), "ga.SubscribeOk")
	proto.RegisterType((*PushLocation)(nil), "ga.PushLocation")
	proto.RegisterType((*CloseResp)(nil), "ga.CloseResp")
	proto.RegisterType((*Ping)(nil), "ga.Ping")
	proto.RegisterType((*RenewTokenReq)(nil), "ga.RenewTokenReq")
	proto.RegisterType((*RenewTokenResp)(nil), "ga.RenewTokenResp")
	proto.RegisterType((*Message)(nil), "ga.Message")
	proto.RegisterType((*SequenceScope)(nil), "ga.SequenceScope")
	proto.RegisterType((*SequenceScope_SequenceList)(nil), "ga.SequenceScope.SequenceList")
	proto.RegisterType((*SequenceScope_SequenceRange)(nil), "ga.SequenceScope.SequenceRange")
	proto.RegisterType((*SequenceScope_Important)(nil), "ga.SequenceScope.Important")
	proto.RegisterType((*MessageAck)(nil), "ga.MessageAck")
	proto.RegisterType((*BatchMessageAck)(nil), "ga.BatchMessageAck")
	proto.RegisterEnum("ga.SubscribeType", SubscribeType_name, SubscribeType_value)
	proto.RegisterEnum("ga.BroadcastChannelType", BroadcastChannelType_name, BroadcastChannelType_value)
	proto.RegisterEnum("ga.SubscribeErrorCode", SubscribeErrorCode_name, SubscribeErrorCode_value)
	proto.RegisterEnum("ga.CloseCode", CloseCode_name, CloseCode_value)
	proto.RegisterEnum("ga.RenewTokenError", RenewTokenError_name, RenewTokenError_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PushClient is the client API for Push service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PushClient interface {
	// headers
	// push-pushd 必填 随机连proxy填round 后面根据proxy返回的重定向填
	// push-auth  access_token 必须 客户端身分验证
	SubscribePush(ctx context.Context, opts ...grpc.CallOption) (Push_SubscribePushClient, error)
	// v2 个推需要单独订阅
	SubscribePushV2(ctx context.Context, opts ...grpc.CallOption) (Push_SubscribePushV2Client, error)
}

type pushClient struct {
	cc *grpc.ClientConn
}

func NewPushClient(cc *grpc.ClientConn) PushClient {
	return &pushClient{cc}
}

func (c *pushClient) SubscribePush(ctx context.Context, opts ...grpc.CallOption) (Push_SubscribePushClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Push_serviceDesc.Streams[0], "/ga.Push/SubscribePush", opts...)
	if err != nil {
		return nil, err
	}
	x := &pushSubscribePushClient{stream}
	return x, nil
}

type Push_SubscribePushClient interface {
	Send(*PushReq) error
	Recv() (*PushResp, error)
	grpc.ClientStream
}

type pushSubscribePushClient struct {
	grpc.ClientStream
}

func (x *pushSubscribePushClient) Send(m *PushReq) error {
	return x.ClientStream.SendMsg(m)
}

func (x *pushSubscribePushClient) Recv() (*PushResp, error) {
	m := new(PushResp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *pushClient) SubscribePushV2(ctx context.Context, opts ...grpc.CallOption) (Push_SubscribePushV2Client, error) {
	stream, err := c.cc.NewStream(ctx, &_Push_serviceDesc.Streams[1], "/ga.Push/SubscribePushV2", opts...)
	if err != nil {
		return nil, err
	}
	x := &pushSubscribePushV2Client{stream}
	return x, nil
}

type Push_SubscribePushV2Client interface {
	Send(*PushReq) error
	Recv() (*PushResp, error)
	grpc.ClientStream
}

type pushSubscribePushV2Client struct {
	grpc.ClientStream
}

func (x *pushSubscribePushV2Client) Send(m *PushReq) error {
	return x.ClientStream.SendMsg(m)
}

func (x *pushSubscribePushV2Client) Recv() (*PushResp, error) {
	m := new(PushResp)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PushServer is the server API for Push service.
type PushServer interface {
	// headers
	// push-pushd 必填 随机连proxy填round 后面根据proxy返回的重定向填
	// push-auth  access_token 必须 客户端身分验证
	SubscribePush(Push_SubscribePushServer) error
	// v2 个推需要单独订阅
	SubscribePushV2(Push_SubscribePushV2Server) error
}

func RegisterPushServer(s *grpc.Server, srv PushServer) {
	s.RegisterService(&_Push_serviceDesc, srv)
}

func _Push_SubscribePush_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(PushServer).SubscribePush(&pushSubscribePushServer{stream})
}

type Push_SubscribePushServer interface {
	Send(*PushResp) error
	Recv() (*PushReq, error)
	grpc.ServerStream
}

type pushSubscribePushServer struct {
	grpc.ServerStream
}

func (x *pushSubscribePushServer) Send(m *PushResp) error {
	return x.ServerStream.SendMsg(m)
}

func (x *pushSubscribePushServer) Recv() (*PushReq, error) {
	m := new(PushReq)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Push_SubscribePushV2_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(PushServer).SubscribePushV2(&pushSubscribePushV2Server{stream})
}

type Push_SubscribePushV2Server interface {
	Send(*PushResp) error
	Recv() (*PushReq, error)
	grpc.ServerStream
}

type pushSubscribePushV2Server struct {
	grpc.ServerStream
}

func (x *pushSubscribePushV2Server) Send(m *PushResp) error {
	return x.ServerStream.SendMsg(m)
}

func (x *pushSubscribePushV2Server) Recv() (*PushReq, error) {
	m := new(PushReq)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _Push_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.Push",
	HandlerType: (*PushServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SubscribePush",
			Handler:       _Push_SubscribePush_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "SubscribePushV2",
			Handler:       _Push_SubscribePushV2_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "pushd.proto",
}

func init() { proto.RegisterFile("pushd.proto", fileDescriptor_pushd_6bee2e7c9bf157ae) }

var fileDescriptor_pushd_6bee2e7c9bf157ae = []byte{
	// 1297 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x56, 0x5f, 0x6e, 0xdb, 0x46,
	0x13, 0x97, 0x68, 0xc9, 0xb2, 0x86, 0xfa, 0xc3, 0x4c, 0xfc, 0x25, 0x8a, 0x93, 0x2f, 0xa9, 0x59,
	0x04, 0x09, 0xd4, 0x40, 0x0e, 0xd4, 0x06, 0x0e, 0x1a, 0x34, 0x80, 0x64, 0xcb, 0x90, 0x50, 0xc7,
	0x36, 0xd6, 0x72, 0x0a, 0xe4, 0x45, 0xa0, 0xc9, 0xad, 0x4c, 0x88, 0xe2, 0x52, 0x5c, 0x2a, 0xae,
	0xae, 0xd0, 0x13, 0xf4, 0x04, 0x7d, 0xef, 0x69, 0x7a, 0x8d, 0x1e, 0xa1, 0xd8, 0xe5, 0x52, 0x14,
	0x65, 0x1b, 0xee, 0x1b, 0x67, 0xe6, 0x37, 0xb3, 0x33, 0xb3, 0xbf, 0x99, 0x25, 0xe8, 0xc1, 0x9c,
	0x5f, 0x39, 0xad, 0x20, 0x64, 0x11, 0x43, 0x6d, 0x6c, 0xed, 0x3c, 0x19, 0x33, 0x36, 0xf6, 0xe8,
	0x9e, 0xd4, 0x5c, 0xce, 0x7f, 0xdd, 0xb3, 0xfc, 0x45, 0x6c, 0x36, 0x7f, 0xd7, 0xa0, 0x74, 0x36,
	0xe7, 0x57, 0x84, 0xce, 0xf0, 0x39, 0x14, 0x02, 0xd7, 0x1f, 0x37, 0xf2, 0xdf, 0xe4, 0x5f, 0xeb,
	0xed, 0xad, 0xd6, 0xd8, 0x6a, 0x9d, 0xb9, 0xfe, 0xb8, 0x9f, 0x23, 0x52, 0x8f, 0x5f, 0x60, 0xc7,
	0xbe, 0xb2, 0x7c, 0x9f, 0x7a, 0x23, 0x3e, 0xbf, 0xe4, 0x76, 0xe8, 0x5e, 0xd2, 0x91, 0x38, 0x6b,
	0x14, 0xd2, 0x59, 0x43, 0x93, 0x5e, 0x4f, 0x85, 0xd7, 0x41, 0x8c, 0x3a, 0x4f, 0x40, 0xea, 0x80,
	0x7e, 0x8e, 0x3c, 0xb6, 0x6f, 0x37, 0xe1, 0x3e, 0x94, 0x82, 0xd0, 0xfd, 0x6a, 0x45, 0xb4, 0xb1,
	0x91, 0x06, 0x3a, 0x8b, 0x55, 0x4b, 0x34, 0xa1, 0xb3, 0x39, 0xe5, 0x51, 0x3f, 0x47, 0x12, 0x34,
	0x7e, 0x80, 0x7a, 0x48, 0x7d, 0x7a, 0x3d, 0x8a, 0xd8, 0x84, 0xfa, 0x32, 0x93, 0x4d, 0x19, 0xe0,
	0x81, 0x08, 0x40, 0x84, 0x69, 0x28, 0x2c, 0xf1, 0xf9, 0xd5, 0x70, 0x55, 0xd1, 0xad, 0x82, 0xae,
	0x42, 0x0e, 0x17, 0x01, 0x35, 0xff, 0xd1, 0x60, 0x2b, 0x4e, 0x88, 0x07, 0xf7, 0x76, 0xe3, 0x15,
	0x94, 0xae, 0xa9, 0x67, 0xb3, 0x29, 0x55, 0xa5, 0xeb, 0x02, 0xf2, 0x4b, 0xac, 0x12, 0x19, 0x2a,
	0xab, 0x00, 0x4e, 0x29, 0xe7, 0xd6, 0x38, 0x29, 0x4d, 0x02, 0x3f, 0xc5, 0x2a, 0x01, 0x54, 0x56,
	0xec, 0x40, 0x2d, 0xed, 0x6b, 0x48, 0x79, 0xd0, 0x28, 0x48, 0x7c, 0xe3, 0xb6, 0x9e, 0x8a, 0x1c,
	0x45, 0x41, 0x7c, 0x55, 0x81, 0x2f, 0xa1, 0x68, 0x7b, 0x8c, 0xd3, 0x46, 0x51, 0x7a, 0x56, 0xa5,
	0xa7, 0x50, 0x28, 0x78, 0x6c, 0xc5, 0xf7, 0x69, 0xb7, 0xe3, 0x66, 0x3d, 0xbb, 0xbd, 0xdb, 0x3c,
	0x60, 0x3e, 0xa7, 0xab, 0xed, 0xfe, 0x08, 0x46, 0xb6, 0xdd, 0x3c, 0x68, 0x94, 0x64, 0x08, 0x5c,
	0xef, 0xb7, 0x3c, 0xb0, 0x16, 0x66, 0x34, 0xdd, 0x1a, 0x54, 0x92, 0xb0, 0xb2, 0xe5, 0x4f, 0xa1,
	0xa4, 0x5a, 0x86, 0x06, 0x6c, 0x4c, 0x79, 0xdc, 0xef, 0x32, 0x11, 0x9f, 0xe6, 0x13, 0x78, 0x7c,
	0x07, 0x03, 0xcc, 0x13, 0x68, 0xdc, 0x95, 0x2e, 0x36, 0xa0, 0xc4, 0xe7, 0xb6, 0x4d, 0x39, 0x97,
	0xc1, 0xb6, 0x48, 0x22, 0x0a, 0x4b, 0x72, 0x15, 0x9a, 0x3c, 0x26, 0x11, 0xcd, 0x3f, 0xf2, 0xf0,
	0xf8, 0x0e, 0xda, 0xe2, 0xff, 0x01, 0x12, 0xde, 0xbb, 0x8e, 0x0c, 0x59, 0x20, 0x65, 0xa5, 0x19,
	0x38, 0xb8, 0x0b, 0x95, 0xc4, 0x1c, 0x2d, 0x82, 0x38, 0x72, 0x95, 0xe8, 0x4a, 0x27, 0xaa, 0xc4,
	0x97, 0x50, 0x90, 0x26, 0x71, 0xff, 0xb5, 0x98, 0x99, 0xcb, 0x53, 0x04, 0x80, 0x48, 0x33, 0x6e,
	0x43, 0x51, 0xb6, 0x55, 0xde, 0x7b, 0x85, 0xc4, 0x82, 0x19, 0x82, 0xd1, 0x0d, 0x99, 0xe5, 0xd8,
	0x16, 0x8f, 0x54, 0x8a, 0xf8, 0x16, 0xb6, 0x2f, 0x13, 0xdd, 0xe8, 0x46, 0x72, 0x78, 0xb9, 0x86,
	0x1f, 0x38, 0xf8, 0x46, 0xa5, 0xa0, 0xc9, 0x14, 0x24, 0xa5, 0xd6, 0xa3, 0xa6, 0x99, 0x98, 0x7f,
	0xe6, 0x61, 0xfb, 0x36, 0xc6, 0xe1, 0x2e, 0x68, 0x6c, 0xa2, 0x66, 0xa2, 0x9e, 0xa9, 0xe3, 0x74,
	0xd2, 0xcf, 0x11, 0x8d, 0x4d, 0xb0, 0x09, 0x5b, 0x21, 0x75, 0xdc, 0x90, 0xda, 0x91, 0x9a, 0x8c,
	0x4a, 0x4c, 0x8d, 0x58, 0xd7, 0xcf, 0x91, 0xa5, 0x1d, 0x9b, 0x50, 0xa4, 0x61, 0xc8, 0x42, 0x35,
	0x19, 0x98, 0x89, 0xd8, 0x13, 0x16, 0x41, 0x5a, 0x09, 0xe9, 0x3e, 0x84, 0x07, 0x99, 0x5c, 0x24,
	0x7f, 0x86, 0x50, 0xcb, 0xe2, 0xb1, 0x09, 0x05, 0x9b, 0x39, 0x54, 0xe6, 0x58, 0x6b, 0x3f, 0xba,
	0x19, 0xf1, 0x80, 0x39, 0x94, 0x48, 0x0c, 0x3e, 0x82, 0x4d, 0x87, 0x46, 0x96, 0xeb, 0x29, 0x3a,
	0x28, 0xc9, 0xfc, 0x11, 0xb6, 0x92, 0x74, 0xb1, 0x05, 0x65, 0x8f, 0xd9, 0x56, 0xe4, 0x32, 0x5f,
	0xf0, 0x69, 0xe3, 0xb5, 0xde, 0x36, 0xe4, 0xb4, 0xcc, 0xf9, 0xd5, 0xb1, 0x32, 0x90, 0x14, 0x62,
	0xbe, 0x01, 0x7d, 0xa5, 0x27, 0xf7, 0x90, 0xc7, 0x7c, 0x0f, 0x95, 0xd5, 0x40, 0x82, 0x02, 0x41,
	0xc8, 0x7e, 0x5b, 0xa8, 0x31, 0x88, 0x05, 0x44, 0x28, 0x58, 0x8e, 0x13, 0xaa, 0x2c, 0xe5, 0xb7,
	0x79, 0x04, 0xe5, 0xe5, 0x64, 0xe3, 0x6e, 0xa6, 0xe8, 0x74, 0xec, 0xff, 0x43, 0xad, 0x4d, 0x28,
	0x88, 0xbd, 0x26, 0xc6, 0xcf, 0xb2, 0x27, 0x6a, 0x62, 0xc4, 0xa7, 0xd0, 0x70, 0xb5, 0xd8, 0x0b,
	0x44, 0x7c, 0x9a, 0x87, 0x50, 0xcd, 0x6c, 0xd4, 0x94, 0xb1, 0xf9, 0x15, 0xc6, 0xae, 0xd5, 0xac,
	0xad, 0xd7, 0xfc, 0x13, 0xd4, 0xb2, 0x7b, 0x02, 0xbf, 0x83, 0xcd, 0x90, 0xf2, 0xb9, 0x17, 0xa9,
	0x02, 0x1e, 0x66, 0x77, 0x89, 0xbc, 0x36, 0xa2, 0x20, 0xe6, 0x5f, 0x79, 0x28, 0xa9, 0xed, 0x89,
	0xff, 0x83, 0x4d, 0x4e, 0x67, 0x69, 0x67, 0x8b, 0x9c, 0xce, 0x06, 0x8e, 0x98, 0xf3, 0xc0, 0x5a,
	0x78, 0xcc, 0x8a, 0x4f, 0xaf, 0x90, 0x44, 0x14, 0xa9, 0xa9, 0x91, 0x17, 0x4e, 0x1b, 0xd2, 0x58,
	0x56, 0x9a, 0x81, 0x83, 0x2f, 0x40, 0xe7, 0x34, 0xfc, 0x4a, 0xc3, 0x51, 0xe4, 0x4e, 0xa9, 0x9c,
	0xc3, 0x02, 0x81, 0x58, 0x35, 0x74, 0xa7, 0x14, 0xdf, 0x81, 0xae, 0x42, 0x8d, 0x2c, 0x7f, 0xa1,
	0xd6, 0xec, 0x76, 0x2b, 0x7e, 0x60, 0x5b, 0xc9, 0x03, 0xdb, 0xea, 0xf8, 0x0b, 0x02, 0x0a, 0xd8,
	0xf1, 0x17, 0xe6, 0xdf, 0x1a, 0x54, 0xcf, 0xc5, 0xea, 0xf2, 0x6d, 0x7a, 0x6e, 0xb3, 0x80, 0xe2,
	0x0f, 0x50, 0xf0, 0x5c, 0x1e, 0xa9, 0x51, 0x7a, 0x2e, 0x69, 0xba, 0x0a, 0x58, 0x4a, 0xc7, 0xae,
	0x7c, 0xf0, 0x24, 0x1a, 0xf7, 0xa1, 0x18, 0x5a, 0xfe, 0x38, 0x79, 0x72, 0x5e, 0xdc, 0xed, 0x46,
	0x04, 0x4c, 0x0c, 0x8f, 0xc4, 0xe3, 0x07, 0x28, 0xbb, 0xd3, 0x80, 0x85, 0x91, 0xe5, 0x47, 0xab,
	0x2f, 0x6c, 0xd6, 0x79, 0x90, 0x40, 0xfa, 0x39, 0x92, 0xe2, 0x77, 0xde, 0x40, 0x65, 0x35, 0x1b,
	0x7c, 0x06, 0x65, 0xae, 0xe4, 0x78, 0x24, 0x0a, 0x24, 0x55, 0xec, 0xec, 0xa7, 0xa5, 0xca, 0x24,
	0x04, 0x49, 0x78, 0x64, 0x85, 0xd1, 0xf2, 0x8e, 0x84, 0x20, 0xd8, 0x45, 0xfd, 0x84, 0x1d, 0xe2,
	0x73, 0xa7, 0x09, 0xe5, 0x65, 0x02, 0x6b, 0x17, 0x95, 0x5f, 0xbb, 0xa8, 0x6e, 0x09, 0x8a, 0x5c,
	0xa4, 0x6c, 0x9e, 0x02, 0x28, 0x32, 0x74, 0xec, 0xc9, 0x72, 0xf5, 0x53, 0x27, 0xb3, 0xfa, 0xa9,
	0x83, 0xaf, 0x94, 0x83, 0xea, 0xdc, 0x83, 0x1b, 0xc5, 0x13, 0x15, 0xf0, 0x1d, 0xd4, 0xbb, 0x56,
	0x64, 0x5f, 0xad, 0x44, 0x35, 0xa1, 0x60, 0xd9, 0x93, 0x64, 0xfa, 0x6b, 0x2b, 0xcf, 0x77, 0xc7,
	0x9e, 0x10, 0x69, 0x6b, 0xee, 0x41, 0x35, 0xb3, 0xd2, 0xb1, 0x0a, 0xe5, 0xf3, 0x8b, 0xee, 0xf9,
	0x01, 0x19, 0x74, 0x7b, 0x46, 0x0e, 0xeb, 0xa0, 0x5f, 0x9c, 0xa4, 0x8a, 0x7c, 0xf3, 0x5b, 0xd8,
	0xbe, 0x6d, 0x01, 0xa3, 0x0e, 0xa5, 0xc3, 0xde, 0x51, 0xe7, 0xe2, 0x78, 0x68, 0xe4, 0x9a, 0x47,
	0x80, 0x37, 0x97, 0x17, 0x56, 0x60, 0x8b, 0xf4, 0xce, 0x7a, 0x9d, 0x61, 0xef, 0xd0, 0xc8, 0x09,
	0x69, 0x70, 0x32, 0xec, 0x91, 0x93, 0xce, 0xb1, 0x91, 0xc7, 0x6d, 0x30, 0xce, 0x68, 0x38, 0x75,
	0x39, 0x77, 0x99, 0x7f, 0x48, 0x7d, 0x97, 0x3a, 0x86, 0xd6, 0xfc, 0xa8, 0x96, 0x85, 0x74, 0xd7,
	0xa1, 0x74, 0x72, 0x3a, 0xea, 0x5c, 0x0c, 0xfb, 0xb1, 0x37, 0xe9, 0x1d, 0x0e, 0x48, 0xef, 0x60,
	0x68, 0xe4, 0xb1, 0x0c, 0xc5, 0xde, 0xe7, 0xc1, 0xc1, 0xd0, 0xd0, 0x04, 0x8a, 0xf4, 0x8e, 0x2e,
	0xce, 0x7b, 0x87, 0xc6, 0x46, 0xf3, 0x13, 0xd4, 0xd7, 0xc6, 0x51, 0xd8, 0x2f, 0xfc, 0x89, 0xcf,
	0xae, 0x7d, 0x23, 0x87, 0x9b, 0xa0, 0x9d, 0xfe, 0x6c, 0xe4, 0xd1, 0x80, 0xca, 0xc0, 0xff, 0x6a,
	0x79, 0xae, 0x23, 0x91, 0x86, 0x86, 0x0f, 0xa1, 0xae, 0xaa, 0x3b, 0x61, 0xd1, 0x27, 0xd1, 0x58,
	0x63, 0xa3, 0xed, 0x41, 0x41, 0x6c, 0x3d, 0x7c, 0xbb, 0xd2, 0x34, 0xa9, 0xd0, 0x93, 0xcd, 0x4a,
	0xe8, 0x6c, 0xa7, 0x92, 0x0a, 0x3c, 0x30, 0x73, 0xaf, 0xf3, 0x6f, 0xf3, 0xd8, 0x86, 0x7a, 0xc6,
	0xe3, 0x73, 0xfb, 0x5e, 0x9f, 0xee, 0x3e, 0xa0, 0xcd, 0xa6, 0xad, 0xd9, 0xfc, 0xda, 0xf2, 0x5b,
	0x51, 0x14, 0x4f, 0xe9, 0x97, 0xdd, 0x31, 0xf3, 0x2c, 0x7f, 0xdc, 0x7a, 0xd7, 0x8e, 0xa2, 0x96,
	0xcd, 0xa6, 0xf1, 0xdf, 0xb1, 0xcd, 0xbc, 0x3d, 0x2b, 0x08, 0xf6, 0xc4, 0x6f, 0xed, 0xe5, 0xa6,
	0x54, 0x7d, 0xff, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xb3, 0xd3, 0xdd, 0x9c, 0x51, 0x0b, 0x00,
	0x00,
}
