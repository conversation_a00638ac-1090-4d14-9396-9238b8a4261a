// Code generated by protoc-gen-go. DO NOT EDIT.
// source: game_group_chat_logic/game_group_chat_logic.proto

package game_group_chat_logic // import "golang.52tt.com/protocol/app/game-group-chat-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import group "golang.52tt.com/protocol/app/group"
import sync "golang.52tt.com/protocol/app/sync"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GroupChatItemStatus int32

const (
	GroupChatItemStatus_GROUP_CHAT_ITEM_STATUS_INVALID_UNSPECIFIED GroupChatItemStatus = 0
	GroupChatItemStatus_GROUP_CHAT_ITEM_STATUS_HOT                 GroupChatItemStatus = 1
)

var GroupChatItemStatus_name = map[int32]string{
	0: "GROUP_CHAT_ITEM_STATUS_INVALID_UNSPECIFIED",
	1: "GROUP_CHAT_ITEM_STATUS_HOT",
}
var GroupChatItemStatus_value = map[string]int32{
	"GROUP_CHAT_ITEM_STATUS_INVALID_UNSPECIFIED": 0,
	"GROUP_CHAT_ITEM_STATUS_HOT":                 1,
}

func (x GroupChatItemStatus) String() string {
	return proto.EnumName(GroupChatItemStatus_name, int32(x))
}
func (GroupChatItemStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{0}
}

// CMD_GetGroupChatCreateProp = 5801; 获取创建群聊数据
type GetGroupChatCreatePropReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGroupChatCreatePropReq) Reset()         { *m = GetGroupChatCreatePropReq{} }
func (m *GetGroupChatCreatePropReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupChatCreatePropReq) ProtoMessage()    {}
func (*GetGroupChatCreatePropReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{0}
}
func (m *GetGroupChatCreatePropReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupChatCreatePropReq.Unmarshal(m, b)
}
func (m *GetGroupChatCreatePropReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupChatCreatePropReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupChatCreatePropReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupChatCreatePropReq.Merge(dst, src)
}
func (m *GetGroupChatCreatePropReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupChatCreatePropReq.Size(m)
}
func (m *GetGroupChatCreatePropReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupChatCreatePropReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupChatCreatePropReq proto.InternalMessageInfo

func (m *GetGroupChatCreatePropReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGroupChatCreatePropReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetGroupChatCreatePropResp struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 配置群名
	OfficialGroupName []string `protobuf:"bytes,2,rep,name=official_group_name,json=officialGroupName,proto3" json:"official_group_name,omitempty"`
	// 配置群介绍
	OfficialGroupIntro []string `protobuf:"bytes,3,rep,name=official_group_intro,json=officialGroupIntro,proto3" json:"official_group_intro,omitempty"`
	// 配置的群标签
	Labels []string `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels,omitempty"`
	// 最大可选标签数
	MaxSelectLabel       uint32   `protobuf:"varint,5,opt,name=max_select_label,json=maxSelectLabel,proto3" json:"max_select_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupChatCreatePropResp) Reset()         { *m = GetGroupChatCreatePropResp{} }
func (m *GetGroupChatCreatePropResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupChatCreatePropResp) ProtoMessage()    {}
func (*GetGroupChatCreatePropResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{1}
}
func (m *GetGroupChatCreatePropResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupChatCreatePropResp.Unmarshal(m, b)
}
func (m *GetGroupChatCreatePropResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupChatCreatePropResp.Marshal(b, m, deterministic)
}
func (dst *GetGroupChatCreatePropResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupChatCreatePropResp.Merge(dst, src)
}
func (m *GetGroupChatCreatePropResp) XXX_Size() int {
	return xxx_messageInfo_GetGroupChatCreatePropResp.Size(m)
}
func (m *GetGroupChatCreatePropResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupChatCreatePropResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupChatCreatePropResp proto.InternalMessageInfo

func (m *GetGroupChatCreatePropResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGroupChatCreatePropResp) GetOfficialGroupName() []string {
	if m != nil {
		return m.OfficialGroupName
	}
	return nil
}

func (m *GetGroupChatCreatePropResp) GetOfficialGroupIntro() []string {
	if m != nil {
		return m.OfficialGroupIntro
	}
	return nil
}

func (m *GetGroupChatCreatePropResp) GetLabels() []string {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *GetGroupChatCreatePropResp) GetMaxSelectLabel() uint32 {
	if m != nil {
		return m.MaxSelectLabel
	}
	return 0
}

// CMD_CreateTGroupChat = 5802; 创建群聊
type CreateTGroupChatReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Name                 string       `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Introduction         string       `protobuf:"bytes,4,opt,name=introduction,proto3" json:"introduction,omitempty"`
	UserSelectLabels     []string     `protobuf:"bytes,5,rep,name=user_select_labels,json=userSelectLabels,proto3" json:"user_select_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CreateTGroupChatReq) Reset()         { *m = CreateTGroupChatReq{} }
func (m *CreateTGroupChatReq) String() string { return proto.CompactTextString(m) }
func (*CreateTGroupChatReq) ProtoMessage()    {}
func (*CreateTGroupChatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{2}
}
func (m *CreateTGroupChatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTGroupChatReq.Unmarshal(m, b)
}
func (m *CreateTGroupChatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTGroupChatReq.Marshal(b, m, deterministic)
}
func (dst *CreateTGroupChatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTGroupChatReq.Merge(dst, src)
}
func (m *CreateTGroupChatReq) XXX_Size() int {
	return xxx_messageInfo_CreateTGroupChatReq.Size(m)
}
func (m *CreateTGroupChatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTGroupChatReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTGroupChatReq proto.InternalMessageInfo

func (m *CreateTGroupChatReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CreateTGroupChatReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *CreateTGroupChatReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateTGroupChatReq) GetIntroduction() string {
	if m != nil {
		return m.Introduction
	}
	return ""
}

func (m *CreateTGroupChatReq) GetUserSelectLabels() []string {
	if m != nil {
		return m.UserSelectLabels
	}
	return nil
}

type CreateTGroupChatResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GroupInfo            *group.TGroupInfo `protobuf:"bytes,2,opt,name=group_info,json=groupInfo,proto3" json:"group_info,omitempty"`
	CreateMessage        string            `protobuf:"bytes,3,opt,name=create_message,json=createMessage,proto3" json:"create_message,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CreateTGroupChatResp) Reset()         { *m = CreateTGroupChatResp{} }
func (m *CreateTGroupChatResp) String() string { return proto.CompactTextString(m) }
func (*CreateTGroupChatResp) ProtoMessage()    {}
func (*CreateTGroupChatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{3}
}
func (m *CreateTGroupChatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTGroupChatResp.Unmarshal(m, b)
}
func (m *CreateTGroupChatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTGroupChatResp.Marshal(b, m, deterministic)
}
func (dst *CreateTGroupChatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTGroupChatResp.Merge(dst, src)
}
func (m *CreateTGroupChatResp) XXX_Size() int {
	return xxx_messageInfo_CreateTGroupChatResp.Size(m)
}
func (m *CreateTGroupChatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTGroupChatResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTGroupChatResp proto.InternalMessageInfo

func (m *CreateTGroupChatResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CreateTGroupChatResp) GetGroupInfo() *group.TGroupInfo {
	if m != nil {
		return m.GroupInfo
	}
	return nil
}

func (m *CreateTGroupChatResp) GetCreateMessage() string {
	if m != nil {
		return m.CreateMessage
	}
	return ""
}

// CMD_GetGroupChatList = 5803;      // 群聊列表
type GetGroupChatListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabIds               []uint32     `protobuf:"varint,2,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	ReqType              uint32       `protobuf:"varint,3,opt,name=req_type,json=reqType,proto3" json:"req_type,omitempty"`
	NoBrowseGroupIds     []uint32     `protobuf:"varint,4,rep,packed,name=no_browse_group_ids,json=noBrowseGroupIds,proto3" json:"no_browse_group_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGroupChatListReq) Reset()         { *m = GetGroupChatListReq{} }
func (m *GetGroupChatListReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupChatListReq) ProtoMessage()    {}
func (*GetGroupChatListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{4}
}
func (m *GetGroupChatListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupChatListReq.Unmarshal(m, b)
}
func (m *GetGroupChatListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupChatListReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupChatListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupChatListReq.Merge(dst, src)
}
func (m *GetGroupChatListReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupChatListReq.Size(m)
}
func (m *GetGroupChatListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupChatListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupChatListReq proto.InternalMessageInfo

func (m *GetGroupChatListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGroupChatListReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *GetGroupChatListReq) GetReqType() uint32 {
	if m != nil {
		return m.ReqType
	}
	return 0
}

func (m *GetGroupChatListReq) GetNoBrowseGroupIds() []uint32 {
	if m != nil {
		return m.NoBrowseGroupIds
	}
	return nil
}

type GroupChatLisItem struct {
	// 群简单信息
	SimpleInfo *group.TGroupSimpleInfo `protobuf:"bytes,1,opt,name=simple_info,json=simpleInfo,proto3" json:"simple_info,omitempty"`
	// 人数+游戏别名+创群标签
	GroupLabels []string `protobuf:"bytes,2,rep,name=group_labels,json=groupLabels,proto3" json:"group_labels,omitempty"`
	// 1-热聊状态 see GroupChatItemStatus
	GroupStatus uint32 `protobuf:"varint,3,opt,name=group_status,json=groupStatus,proto3" json:"group_status,omitempty"`
	// 推荐追踪上报 跟房间列表footprint作用，客户端数据上报用上
	TraceId              string   `protobuf:"bytes,4,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupChatLisItem) Reset()         { *m = GroupChatLisItem{} }
func (m *GroupChatLisItem) String() string { return proto.CompactTextString(m) }
func (*GroupChatLisItem) ProtoMessage()    {}
func (*GroupChatLisItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{5}
}
func (m *GroupChatLisItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupChatLisItem.Unmarshal(m, b)
}
func (m *GroupChatLisItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupChatLisItem.Marshal(b, m, deterministic)
}
func (dst *GroupChatLisItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupChatLisItem.Merge(dst, src)
}
func (m *GroupChatLisItem) XXX_Size() int {
	return xxx_messageInfo_GroupChatLisItem.Size(m)
}
func (m *GroupChatLisItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupChatLisItem.DiscardUnknown(m)
}

var xxx_messageInfo_GroupChatLisItem proto.InternalMessageInfo

func (m *GroupChatLisItem) GetSimpleInfo() *group.TGroupSimpleInfo {
	if m != nil {
		return m.SimpleInfo
	}
	return nil
}

func (m *GroupChatLisItem) GetGroupLabels() []string {
	if m != nil {
		return m.GroupLabels
	}
	return nil
}

func (m *GroupChatLisItem) GetGroupStatus() uint32 {
	if m != nil {
		return m.GroupStatus
	}
	return 0
}

func (m *GroupChatLisItem) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

type GetGroupChatListResp struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Items                []*GroupChatLisItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	LoadFinish           bool                `protobuf:"varint,3,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetGroupChatListResp) Reset()         { *m = GetGroupChatListResp{} }
func (m *GetGroupChatListResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupChatListResp) ProtoMessage()    {}
func (*GetGroupChatListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{6}
}
func (m *GetGroupChatListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupChatListResp.Unmarshal(m, b)
}
func (m *GetGroupChatListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupChatListResp.Marshal(b, m, deterministic)
}
func (dst *GetGroupChatListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupChatListResp.Merge(dst, src)
}
func (m *GetGroupChatListResp) XXX_Size() int {
	return xxx_messageInfo_GetGroupChatListResp.Size(m)
}
func (m *GetGroupChatListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupChatListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupChatListResp proto.InternalMessageInfo

func (m *GetGroupChatListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGroupChatListResp) GetItems() []*GroupChatLisItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *GetGroupChatListResp) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

// CMD_GetGroupChatExtraInfo = 5804;      // 专区建群额外信息
type GetGroupChatExtraInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              uint32       `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGroupChatExtraInfoReq) Reset()         { *m = GetGroupChatExtraInfoReq{} }
func (m *GetGroupChatExtraInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupChatExtraInfoReq) ProtoMessage()    {}
func (*GetGroupChatExtraInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{7}
}
func (m *GetGroupChatExtraInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupChatExtraInfoReq.Unmarshal(m, b)
}
func (m *GetGroupChatExtraInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupChatExtraInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupChatExtraInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupChatExtraInfoReq.Merge(dst, src)
}
func (m *GetGroupChatExtraInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupChatExtraInfoReq.Size(m)
}
func (m *GetGroupChatExtraInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupChatExtraInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupChatExtraInfoReq proto.InternalMessageInfo

func (m *GetGroupChatExtraInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGroupChatExtraInfoReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetGroupChatExtraInfoResp struct {
	BaseResp         *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ConfigLabels     []string      `protobuf:"bytes,2,rep,name=config_labels,json=configLabels,proto3" json:"config_labels,omitempty"`
	UserSelectLabels []string      `protobuf:"bytes,3,rep,name=user_select_labels,json=userSelectLabels,proto3" json:"user_select_labels,omitempty"`
	// 最大可选标签数
	MaxSelectLabel       uint32   `protobuf:"varint,4,opt,name=max_select_label,json=maxSelectLabel,proto3" json:"max_select_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupChatExtraInfoResp) Reset()         { *m = GetGroupChatExtraInfoResp{} }
func (m *GetGroupChatExtraInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupChatExtraInfoResp) ProtoMessage()    {}
func (*GetGroupChatExtraInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{8}
}
func (m *GetGroupChatExtraInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupChatExtraInfoResp.Unmarshal(m, b)
}
func (m *GetGroupChatExtraInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupChatExtraInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetGroupChatExtraInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupChatExtraInfoResp.Merge(dst, src)
}
func (m *GetGroupChatExtraInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGroupChatExtraInfoResp.Size(m)
}
func (m *GetGroupChatExtraInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupChatExtraInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupChatExtraInfoResp proto.InternalMessageInfo

func (m *GetGroupChatExtraInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGroupChatExtraInfoResp) GetConfigLabels() []string {
	if m != nil {
		return m.ConfigLabels
	}
	return nil
}

func (m *GetGroupChatExtraInfoResp) GetUserSelectLabels() []string {
	if m != nil {
		return m.UserSelectLabels
	}
	return nil
}

func (m *GetGroupChatExtraInfoResp) GetMaxSelectLabel() uint32 {
	if m != nil {
		return m.MaxSelectLabel
	}
	return 0
}

// CMD_SetGroupChatExtraInfo = 5805;    // 修改用户群额外信息
type SetGroupChatExtraInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              uint32       `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	UserSelectLabels     []string     `protobuf:"bytes,3,rep,name=user_select_labels,json=userSelectLabels,proto3" json:"user_select_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetGroupChatExtraInfoReq) Reset()         { *m = SetGroupChatExtraInfoReq{} }
func (m *SetGroupChatExtraInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetGroupChatExtraInfoReq) ProtoMessage()    {}
func (*SetGroupChatExtraInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{9}
}
func (m *SetGroupChatExtraInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGroupChatExtraInfoReq.Unmarshal(m, b)
}
func (m *SetGroupChatExtraInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGroupChatExtraInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetGroupChatExtraInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGroupChatExtraInfoReq.Merge(dst, src)
}
func (m *SetGroupChatExtraInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetGroupChatExtraInfoReq.Size(m)
}
func (m *SetGroupChatExtraInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGroupChatExtraInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGroupChatExtraInfoReq proto.InternalMessageInfo

func (m *SetGroupChatExtraInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetGroupChatExtraInfoReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *SetGroupChatExtraInfoReq) GetUserSelectLabels() []string {
	if m != nil {
		return m.UserSelectLabels
	}
	return nil
}

type SetGroupChatExtraInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetGroupChatExtraInfoResp) Reset()         { *m = SetGroupChatExtraInfoResp{} }
func (m *SetGroupChatExtraInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetGroupChatExtraInfoResp) ProtoMessage()    {}
func (*SetGroupChatExtraInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{10}
}
func (m *SetGroupChatExtraInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGroupChatExtraInfoResp.Unmarshal(m, b)
}
func (m *SetGroupChatExtraInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGroupChatExtraInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetGroupChatExtraInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGroupChatExtraInfoResp.Merge(dst, src)
}
func (m *SetGroupChatExtraInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetGroupChatExtraInfoResp.Size(m)
}
func (m *SetGroupChatExtraInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGroupChatExtraInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGroupChatExtraInfoResp proto.InternalMessageInfo

func (m *SetGroupChatExtraInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GameGroupChatMsg struct {
	Message              *sync.NewMessageSync `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GameGroupChatMsg) Reset()         { *m = GameGroupChatMsg{} }
func (m *GameGroupChatMsg) String() string { return proto.CompactTextString(m) }
func (*GameGroupChatMsg) ProtoMessage()    {}
func (*GameGroupChatMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{11}
}
func (m *GameGroupChatMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameGroupChatMsg.Unmarshal(m, b)
}
func (m *GameGroupChatMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameGroupChatMsg.Marshal(b, m, deterministic)
}
func (dst *GameGroupChatMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameGroupChatMsg.Merge(dst, src)
}
func (m *GameGroupChatMsg) XXX_Size() int {
	return xxx_messageInfo_GameGroupChatMsg.Size(m)
}
func (m *GameGroupChatMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameGroupChatMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GameGroupChatMsg proto.InternalMessageInfo

func (m *GameGroupChatMsg) GetMessage() *sync.NewMessageSync {
	if m != nil {
		return m.Message
	}
	return nil
}

type PreviewGroupChatMsgRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              uint32       `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PreviewGroupChatMsgRequest) Reset()         { *m = PreviewGroupChatMsgRequest{} }
func (m *PreviewGroupChatMsgRequest) String() string { return proto.CompactTextString(m) }
func (*PreviewGroupChatMsgRequest) ProtoMessage()    {}
func (*PreviewGroupChatMsgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{12}
}
func (m *PreviewGroupChatMsgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreviewGroupChatMsgRequest.Unmarshal(m, b)
}
func (m *PreviewGroupChatMsgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreviewGroupChatMsgRequest.Marshal(b, m, deterministic)
}
func (dst *PreviewGroupChatMsgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreviewGroupChatMsgRequest.Merge(dst, src)
}
func (m *PreviewGroupChatMsgRequest) XXX_Size() int {
	return xxx_messageInfo_PreviewGroupChatMsgRequest.Size(m)
}
func (m *PreviewGroupChatMsgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PreviewGroupChatMsgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PreviewGroupChatMsgRequest proto.InternalMessageInfo

func (m *PreviewGroupChatMsgRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PreviewGroupChatMsgRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type PreviewGroupChatMsgResponse struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MsgList              []*GameGroupChatMsg `protobuf:"bytes,2,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PreviewGroupChatMsgResponse) Reset()         { *m = PreviewGroupChatMsgResponse{} }
func (m *PreviewGroupChatMsgResponse) String() string { return proto.CompactTextString(m) }
func (*PreviewGroupChatMsgResponse) ProtoMessage()    {}
func (*PreviewGroupChatMsgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_group_chat_logic_a2a5c57df453741b, []int{13}
}
func (m *PreviewGroupChatMsgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreviewGroupChatMsgResponse.Unmarshal(m, b)
}
func (m *PreviewGroupChatMsgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreviewGroupChatMsgResponse.Marshal(b, m, deterministic)
}
func (dst *PreviewGroupChatMsgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreviewGroupChatMsgResponse.Merge(dst, src)
}
func (m *PreviewGroupChatMsgResponse) XXX_Size() int {
	return xxx_messageInfo_PreviewGroupChatMsgResponse.Size(m)
}
func (m *PreviewGroupChatMsgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PreviewGroupChatMsgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PreviewGroupChatMsgResponse proto.InternalMessageInfo

func (m *PreviewGroupChatMsgResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PreviewGroupChatMsgResponse) GetMsgList() []*GameGroupChatMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

func init() {
	proto.RegisterType((*GetGroupChatCreatePropReq)(nil), "ga.game_group_chat_logic.GetGroupChatCreatePropReq")
	proto.RegisterType((*GetGroupChatCreatePropResp)(nil), "ga.game_group_chat_logic.GetGroupChatCreatePropResp")
	proto.RegisterType((*CreateTGroupChatReq)(nil), "ga.game_group_chat_logic.CreateTGroupChatReq")
	proto.RegisterType((*CreateTGroupChatResp)(nil), "ga.game_group_chat_logic.CreateTGroupChatResp")
	proto.RegisterType((*GetGroupChatListReq)(nil), "ga.game_group_chat_logic.GetGroupChatListReq")
	proto.RegisterType((*GroupChatLisItem)(nil), "ga.game_group_chat_logic.GroupChatLisItem")
	proto.RegisterType((*GetGroupChatListResp)(nil), "ga.game_group_chat_logic.GetGroupChatListResp")
	proto.RegisterType((*GetGroupChatExtraInfoReq)(nil), "ga.game_group_chat_logic.GetGroupChatExtraInfoReq")
	proto.RegisterType((*GetGroupChatExtraInfoResp)(nil), "ga.game_group_chat_logic.GetGroupChatExtraInfoResp")
	proto.RegisterType((*SetGroupChatExtraInfoReq)(nil), "ga.game_group_chat_logic.SetGroupChatExtraInfoReq")
	proto.RegisterType((*SetGroupChatExtraInfoResp)(nil), "ga.game_group_chat_logic.SetGroupChatExtraInfoResp")
	proto.RegisterType((*GameGroupChatMsg)(nil), "ga.game_group_chat_logic.GameGroupChatMsg")
	proto.RegisterType((*PreviewGroupChatMsgRequest)(nil), "ga.game_group_chat_logic.PreviewGroupChatMsgRequest")
	proto.RegisterType((*PreviewGroupChatMsgResponse)(nil), "ga.game_group_chat_logic.PreviewGroupChatMsgResponse")
	proto.RegisterEnum("ga.game_group_chat_logic.GroupChatItemStatus", GroupChatItemStatus_name, GroupChatItemStatus_value)
}

func init() {
	proto.RegisterFile("game_group_chat_logic/game_group_chat_logic.proto", fileDescriptor_game_group_chat_logic_a2a5c57df453741b)
}

var fileDescriptor_game_group_chat_logic_a2a5c57df453741b = []byte{
	// 883 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x56, 0x4f, 0x6f, 0xe3, 0x44,
	0x14, 0xc7, 0xdb, 0xb4, 0x49, 0x5f, 0x9a, 0x2a, 0x4c, 0x0a, 0xb8, 0x41, 0x82, 0x62, 0x04, 0x0a,
	0xd5, 0xc6, 0x85, 0xa0, 0xbd, 0x6f, 0xdb, 0x4d, 0xbb, 0x96, 0xda, 0x6c, 0x65, 0xa7, 0x1c, 0x56,
	0x42, 0xa3, 0x89, 0xf3, 0xe2, 0x5a, 0xb2, 0x3d, 0x8e, 0x67, 0x42, 0x9b, 0xcf, 0xc0, 0x81, 0x23,
	0x57, 0x0e, 0x9c, 0xf8, 0x08, 0x5c, 0xf9, 0x38, 0x7c, 0x09, 0xe4, 0x19, 0xbb, 0x4d, 0xba, 0xa9,
	0x44, 0xc4, 0x72, 0x1b, 0xff, 0xde, 0x7b, 0x33, 0xef, 0xf7, 0x7b, 0x7f, 0x64, 0xf8, 0x2e, 0x60,
	0x31, 0xd2, 0x20, 0xe3, 0xb3, 0x94, 0xfa, 0x37, 0x4c, 0xd2, 0x88, 0x07, 0xa1, 0x7f, 0xb4, 0x12,
	0xb5, 0xd3, 0x8c, 0x4b, 0x4e, 0xcc, 0x80, 0xd9, 0x2b, 0xed, 0x6d, 0x10, 0xf3, 0xa4, 0xf0, 0x6a,
	0x37, 0x02, 0x46, 0x47, 0x4c, 0x60, 0xf9, 0x29, 0xb5, 0xb7, 0xfe, 0xb4, 0xde, 0xc2, 0xfe, 0x39,
	0xca, 0xf3, 0x1c, 0x3a, 0xbd, 0x61, 0xf2, 0x34, 0x43, 0x26, 0xf1, 0x2a, 0xe3, 0xa9, 0x8b, 0x53,
	0xf2, 0x35, 0xd4, 0xf2, 0x48, 0x9a, 0xe1, 0xd4, 0x34, 0x0e, 0x8c, 0x4e, 0xbd, 0x57, 0xb7, 0x03,
	0x66, 0x9f, 0x30, 0x81, 0x2e, 0x4e, 0xdd, 0xea, 0x48, 0x1f, 0xc8, 0x47, 0xb0, 0x25, 0xd9, 0x88,
	0x86, 0x63, 0xf3, 0xd9, 0x81, 0xd1, 0x69, 0xb8, 0x9b, 0x92, 0x8d, 0x9c, 0xb1, 0xf5, 0xb7, 0x01,
	0xed, 0xa7, 0x2e, 0x17, 0x29, 0xf9, 0x06, 0xb6, 0x8b, 0xdb, 0x45, 0x5a, 0x5c, 0xbf, 0xf3, 0x70,
	0xbd, 0x48, 0xdd, 0xda, 0xa8, 0x38, 0x11, 0x1b, 0x5a, 0x7c, 0x32, 0x09, 0xfd, 0x90, 0x45, 0x05,
	0xd9, 0x84, 0xc5, 0x68, 0x3e, 0x3b, 0xd8, 0xe8, 0x6c, 0xbb, 0x1f, 0x96, 0x26, 0xf5, 0xd0, 0x80,
	0xc5, 0x48, 0xbe, 0x85, 0xbd, 0x47, 0xfe, 0x61, 0x22, 0x33, 0x6e, 0x6e, 0xa8, 0x00, 0xb2, 0x14,
	0xe0, 0xe4, 0x16, 0xf2, 0x31, 0x6c, 0x45, 0x6c, 0x84, 0x91, 0x30, 0x2b, 0xca, 0xa7, 0xf8, 0x22,
	0x1d, 0x68, 0xc6, 0xec, 0x8e, 0x0a, 0x8c, 0xd0, 0x97, 0x54, 0x81, 0xe6, 0xa6, 0x22, 0xb9, 0x1b,
	0xb3, 0x3b, 0x4f, 0xc1, 0x17, 0x39, 0x6a, 0xfd, 0x69, 0x40, 0x4b, 0x33, 0x1c, 0xde, 0x33, 0xfe,
	0xef, 0x22, 0x12, 0x02, 0x15, 0xc5, 0x75, 0xe3, 0xc0, 0xe8, 0x6c, 0xbb, 0xea, 0x4c, 0x2c, 0xd8,
	0x51, 0x7c, 0xc6, 0x33, 0x5f, 0x86, 0x3c, 0x31, 0x2b, 0xca, 0xb6, 0x84, 0x91, 0xe7, 0x40, 0x66,
	0x02, 0xb3, 0xa5, 0xcc, 0x85, 0xb9, 0xa9, 0xc8, 0x35, 0x73, 0xcb, 0x42, 0xee, 0xc2, 0xfa, 0xd5,
	0x80, 0xbd, 0x77, 0x93, 0x5f, 0xaf, 0x48, 0x5d, 0x80, 0x52, 0xeb, 0x09, 0x57, 0x24, 0xea, 0xbd,
	0xdd, 0xdc, 0x77, 0x58, 0xe8, 0x3c, 0xe1, 0xee, 0x76, 0x50, 0x1e, 0xc9, 0x57, 0xb0, 0xeb, 0xab,
	0x17, 0x69, 0x8c, 0x42, 0xb0, 0xa0, 0xa4, 0xd8, 0xd0, 0xe8, 0xa5, 0x06, 0xad, 0xdf, 0x0c, 0x68,
	0x2d, 0x36, 0xd1, 0x45, 0x28, 0xd6, 0x92, 0xf5, 0x13, 0xa8, 0x6a, 0x59, 0x85, 0x6a, 0x97, 0x86,
	0xbb, 0xa5, 0x74, 0x15, 0x64, 0x1f, 0x6a, 0x19, 0x4e, 0xa9, 0x9c, 0xa7, 0xfa, 0xe5, 0x86, 0x5b,
	0xcd, 0x70, 0x3a, 0x9c, 0xa7, 0x48, 0xba, 0xd0, 0x4a, 0x38, 0x1d, 0x65, 0xfc, 0x56, 0x94, 0xc3,
	0x95, 0xc7, 0x57, 0x54, 0x7c, 0x33, 0xe1, 0x27, 0xca, 0xa2, 0x59, 0x8d, 0x85, 0xf5, 0x87, 0x01,
	0xcd, 0xc5, 0xfc, 0x1c, 0x89, 0x31, 0x79, 0x01, 0x75, 0x11, 0xc6, 0x69, 0x84, 0x5a, 0x0e, 0x9d,
	0xe2, 0xde, 0x83, 0x1c, 0x9e, 0x32, 0x2a, 0x51, 0x40, 0xdc, 0x9f, 0xc9, 0x17, 0xb0, 0xa3, 0x1f,
	0x2c, 0x0a, 0xa6, 0x5b, 0xbc, 0xae, 0x30, 0x5d, 0xab, 0x07, 0x17, 0x21, 0x99, 0x9c, 0x89, 0x22,
	0x79, 0xed, 0xe2, 0x29, 0x28, 0xe7, 0x26, 0x33, 0xe6, 0x63, 0xde, 0x4d, 0xba, 0x39, 0xaa, 0xea,
	0xdb, 0x19, 0x5b, 0xbf, 0x1b, 0xb0, 0xf7, 0xae, 0x9e, 0xeb, 0x55, 0xfa, 0x25, 0x6c, 0x86, 0x12,
	0x63, 0x9d, 0x5d, 0xbd, 0x77, 0x68, 0x3f, 0xb5, 0x88, 0xec, 0xc7, 0xb2, 0xb8, 0x3a, 0x90, 0x7c,
	0x0e, 0xf5, 0x88, 0xb3, 0x31, 0x9d, 0x84, 0x49, 0x28, 0x6e, 0x14, 0x85, 0x9a, 0x0b, 0x39, 0x74,
	0xa6, 0x10, 0xeb, 0x47, 0x30, 0x17, 0xb3, 0xec, 0xdf, 0xc9, 0x8c, 0x29, 0xb1, 0xd6, 0x28, 0xfd,
	0x3e, 0xd4, 0xca, 0xe2, 0x15, 0x33, 0x55, 0xd5, 0xed, 0x37, 0xb6, 0xfe, 0x32, 0x96, 0xf7, 0xde,
	0xc2, 0xfd, 0xeb, 0x49, 0xf1, 0x25, 0x34, 0x7c, 0x9e, 0x4c, 0xc2, 0x60, 0xb9, 0x60, 0x3b, 0x1a,
	0x2c, 0x2a, 0xb6, 0x7a, 0x16, 0x37, 0x56, 0xcf, 0xe2, 0xca, 0x95, 0x53, 0x59, 0xb9, 0x72, 0x7e,
	0x36, 0xc0, 0xf4, 0xfe, 0x3f, 0x95, 0xd6, 0xcb, 0xdb, 0x3a, 0x83, 0x7d, 0xef, 0x3d, 0x48, 0x6a,
	0xbd, 0x84, 0xe6, 0x39, 0x8b, 0xf1, 0xfe, 0xa2, 0x4b, 0x11, 0x90, 0xe7, 0x50, 0x2d, 0xb7, 0x84,
	0x0e, 0x26, 0x79, 0xf0, 0x00, 0x6f, 0x8b, 0x35, 0xe1, 0xcd, 0x13, 0xdf, 0x2d, 0x5d, 0x2c, 0x0a,
	0xed, 0xab, 0x0c, 0x7f, 0x0a, 0xf1, 0x76, 0xf1, 0x12, 0x17, 0xa7, 0x33, 0x14, 0xf2, 0x7d, 0xb4,
	0xcf, 0x2f, 0x06, 0x7c, 0xba, 0xf2, 0x05, 0x91, 0xf2, 0x44, 0xe0, 0x3a, 0x0d, 0xd4, 0x87, 0x5a,
	0x2c, 0x02, 0x1a, 0x85, 0x42, 0xfe, 0x8b, 0x71, 0x7a, 0xa4, 0x8b, 0x5b, 0x8d, 0x45, 0x90, 0x4f,
	0xf0, 0x21, 0x42, 0xeb, 0xde, 0x90, 0x0f, 0x5a, 0xb1, 0x08, 0x6c, 0x38, 0x3c, 0x77, 0xdf, 0x5c,
	0x5f, 0xd1, 0xd3, 0xd7, 0xc7, 0x43, 0xea, 0x0c, 0xfb, 0x97, 0xd4, 0x1b, 0x1e, 0x0f, 0xaf, 0x3d,
	0xea, 0x0c, 0x7e, 0x38, 0xbe, 0x70, 0x5e, 0xd1, 0xeb, 0x81, 0x77, 0xd5, 0x3f, 0x75, 0xce, 0x9c,
	0xfe, 0xab, 0xe6, 0x07, 0xe4, 0x33, 0x68, 0x3f, 0xe1, 0xff, 0xfa, 0xcd, 0xb0, 0x69, 0x9c, 0x0c,
	0xc0, 0xf4, 0x79, 0x6c, 0xcf, 0xc3, 0x39, 0x9f, 0xe5, 0x69, 0xc6, 0x7c, 0x8c, 0x91, 0xfe, 0x95,
	0x78, 0xdb, 0x0b, 0x78, 0xc4, 0x92, 0xc0, 0x7e, 0xd1, 0x93, 0xd2, 0xf6, 0x79, 0x7c, 0xa4, 0x60,
	0x9f, 0x47, 0x47, 0x2c, 0x4d, 0xd5, 0x8f, 0x4c, 0x57, 0x11, 0xea, 0xe6, 0x84, 0xba, 0x8a, 0xd0,
	0x68, 0x4b, 0xf9, 0x7c, 0xff, 0x4f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xcf, 0x24, 0xca, 0x1a, 0xfe,
	0x08, 0x00, 0x00,
}
