// Code generated by protoc-gen-gogo.
// source: team_.proto
// DO NOT EDIT!

/*
	Package team is a generated protocol buffer package.

	It is generated from these files:
		team_.proto

	It has these top-level messages:
		GameRecruitMemberChange
		UserInfo
		GameRecruitChannelInfo
		GameRecruitDetail
		GetGameRecruitReq
		GetGameRecruitResp
		CreateGameRecruitReq
		CreateGameRecruitResp
		QuitGameRecruitReq
		QuitGameRecruitResp
		JoinGameRecruitReq
		JoinGameRecruitResp
		IntoGameRecruitChannelReq
		IntoGameRecruitChannelResp
		InviteGameRecruitReq
		InviteGameRecruitResp
*/
package team

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type GameRecruitMemberChange_PARTYSTATUS int32

const (
	GameRecruitMemberChange_JOIN_GR  GameRecruitMemberChange_PARTYSTATUS = 1
	GameRecruitMemberChange_LEAVE_GR GameRecruitMemberChange_PARTYSTATUS = 2
)

var GameRecruitMemberChange_PARTYSTATUS_name = map[int32]string{
	1: "JOIN_GR",
	2: "LEAVE_GR",
}
var GameRecruitMemberChange_PARTYSTATUS_value = map[string]int32{
	"JOIN_GR":  1,
	"LEAVE_GR": 2,
}

func (x GameRecruitMemberChange_PARTYSTATUS) Enum() *GameRecruitMemberChange_PARTYSTATUS {
	p := new(GameRecruitMemberChange_PARTYSTATUS)
	*p = x
	return p
}
func (x GameRecruitMemberChange_PARTYSTATUS) String() string {
	return proto.EnumName(GameRecruitMemberChange_PARTYSTATUS_name, int32(x))
}
func (x *GameRecruitMemberChange_PARTYSTATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GameRecruitMemberChange_PARTYSTATUS_value, data, "GameRecruitMemberChange_PARTYSTATUS")
	if err != nil {
		return err
	}
	*x = GameRecruitMemberChange_PARTYSTATUS(value)
	return nil
}
func (GameRecruitMemberChange_PARTYSTATUS) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorTeam_, []int{0, 0}
}

type CreateGameRecruitReq_PushToPublic int32

const (
	CreateGameRecruitReq_PUSH     CreateGameRecruitReq_PushToPublic = 1
	CreateGameRecruitReq_NOT_PUSH CreateGameRecruitReq_PushToPublic = 2
)

var CreateGameRecruitReq_PushToPublic_name = map[int32]string{
	1: "PUSH",
	2: "NOT_PUSH",
}
var CreateGameRecruitReq_PushToPublic_value = map[string]int32{
	"PUSH":     1,
	"NOT_PUSH": 2,
}

func (x CreateGameRecruitReq_PushToPublic) Enum() *CreateGameRecruitReq_PushToPublic {
	p := new(CreateGameRecruitReq_PushToPublic)
	*p = x
	return p
}
func (x CreateGameRecruitReq_PushToPublic) String() string {
	return proto.EnumName(CreateGameRecruitReq_PushToPublic_name, int32(x))
}
func (x *CreateGameRecruitReq_PushToPublic) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CreateGameRecruitReq_PushToPublic_value, data, "CreateGameRecruitReq_PushToPublic")
	if err != nil {
		return err
	}
	*x = CreateGameRecruitReq_PushToPublic(value)
	return nil
}
func (CreateGameRecruitReq_PushToPublic) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorTeam_, []int{6, 0}
}

type GameRecruitMemberChange struct {
	UserInfo    *UserInfo `protobuf:"bytes,1,req,name=user_info,json=userInfo" json:"user_info,omitempty"`
	PartyStatus uint32    `protobuf:"varint,2,req,name=party_status,json=partyStatus" json:"party_status"`
	GrId        uint32    `protobuf:"varint,3,req,name=gr_id,json=grId" json:"gr_id"`
}

func (m *GameRecruitMemberChange) Reset()                    { *m = GameRecruitMemberChange{} }
func (m *GameRecruitMemberChange) String() string            { return proto.CompactTextString(m) }
func (*GameRecruitMemberChange) ProtoMessage()               {}
func (*GameRecruitMemberChange) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{0} }

func (m *GameRecruitMemberChange) GetUserInfo() *UserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *GameRecruitMemberChange) GetPartyStatus() uint32 {
	if m != nil {
		return m.PartyStatus
	}
	return 0
}

func (m *GameRecruitMemberChange) GetGrId() uint32 {
	if m != nil {
		return m.GrId
	}
	return 0
}

type UserInfo struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Sex      uint32 `protobuf:"varint,2,req,name=sex" json:"sex"`
	Account  string `protobuf:"bytes,3,req,name=account" json:"account"`
	Nickname string `protobuf:"bytes,4,opt,name=nickname" json:"nickname"`
}

func (m *UserInfo) Reset()                    { *m = UserInfo{} }
func (m *UserInfo) String() string            { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()               {}
func (*UserInfo) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{1} }

func (m *UserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

type GameRecruitChannelInfo struct {
	ChannelId uint32   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	UidList   []uint32 `protobuf:"varint,2,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *GameRecruitChannelInfo) Reset()                    { *m = GameRecruitChannelInfo{} }
func (m *GameRecruitChannelInfo) String() string            { return proto.CompactTextString(m) }
func (*GameRecruitChannelInfo) ProtoMessage()               {}
func (*GameRecruitChannelInfo) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{2} }

func (m *GameRecruitChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GameRecruitChannelInfo) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type GameRecruitDetail struct {
	OwnerUid   uint32      `protobuf:"varint,1,req,name=owner_uid,json=ownerUid" json:"owner_uid"`
	UserInfos  []*UserInfo `protobuf:"bytes,2,rep,name=user_infos,json=userInfos" json:"user_infos,omitempty"`
	GameId     uint32      `protobuf:"varint,3,req,name=game_id,json=gameId" json:"game_id"`
	Title      string      `protobuf:"bytes,4,req,name=title" json:"title"`
	Desc       string      `protobuf:"bytes,5,req,name=desc" json:"desc"`
	GrId       uint32      `protobuf:"varint,6,opt,name=gr_id,json=grId" json:"gr_id"`
	GameName   string      `protobuf:"bytes,7,opt,name=game_name,json=gameName" json:"game_name"`
	InviterUid uint32      `protobuf:"varint,8,opt,name=inviter_uid,json=inviterUid" json:"inviter_uid"`
}

func (m *GameRecruitDetail) Reset()                    { *m = GameRecruitDetail{} }
func (m *GameRecruitDetail) String() string            { return proto.CompactTextString(m) }
func (*GameRecruitDetail) ProtoMessage()               {}
func (*GameRecruitDetail) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{3} }

func (m *GameRecruitDetail) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

func (m *GameRecruitDetail) GetUserInfos() []*UserInfo {
	if m != nil {
		return m.UserInfos
	}
	return nil
}

func (m *GameRecruitDetail) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameRecruitDetail) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GameRecruitDetail) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GameRecruitDetail) GetGrId() uint32 {
	if m != nil {
		return m.GrId
	}
	return 0
}

func (m *GameRecruitDetail) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GameRecruitDetail) GetInviterUid() uint32 {
	if m != nil {
		return m.InviterUid
	}
	return 0
}

// 查询招募信息
type GetGameRecruitReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uid     uint32      `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *GetGameRecruitReq) Reset()                    { *m = GetGameRecruitReq{} }
func (m *GetGameRecruitReq) String() string            { return proto.CompactTextString(m) }
func (*GetGameRecruitReq) ProtoMessage()               {}
func (*GetGameRecruitReq) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{4} }

func (m *GetGameRecruitReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameRecruitReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGameRecruitResp struct {
	BaseResp *ga.BaseResp       `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GrDetail *GameRecruitDetail `protobuf:"bytes,2,req,name=grDetail" json:"grDetail,omitempty"`
}

func (m *GetGameRecruitResp) Reset()                    { *m = GetGameRecruitResp{} }
func (m *GetGameRecruitResp) String() string            { return proto.CompactTextString(m) }
func (*GetGameRecruitResp) ProtoMessage()               {}
func (*GetGameRecruitResp) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{5} }

func (m *GetGameRecruitResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameRecruitResp) GetGrDetail() *GameRecruitDetail {
	if m != nil {
		return m.GrDetail
	}
	return nil
}

// 创建招募
type CreateGameRecruitReq struct {
	BaseReq      *ga.BaseReq        `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GrDetail     *GameRecruitDetail `protobuf:"bytes,2,req,name=grDetail" json:"grDetail,omitempty"`
	PushToPublic uint32             `protobuf:"varint,3,opt,name=push_to_public,json=pushToPublic" json:"push_to_public"`
}

func (m *CreateGameRecruitReq) Reset()                    { *m = CreateGameRecruitReq{} }
func (m *CreateGameRecruitReq) String() string            { return proto.CompactTextString(m) }
func (*CreateGameRecruitReq) ProtoMessage()               {}
func (*CreateGameRecruitReq) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{6} }

func (m *CreateGameRecruitReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CreateGameRecruitReq) GetGrDetail() *GameRecruitDetail {
	if m != nil {
		return m.GrDetail
	}
	return nil
}

func (m *CreateGameRecruitReq) GetPushToPublic() uint32 {
	if m != nil {
		return m.PushToPublic
	}
	return 0
}

type CreateGameRecruitResp struct {
	BaseResp *ga.BaseResp       `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GrDetail *GameRecruitDetail `protobuf:"bytes,2,req,name=gr_detail,json=grDetail" json:"gr_detail,omitempty"`
}

func (m *CreateGameRecruitResp) Reset()                    { *m = CreateGameRecruitResp{} }
func (m *CreateGameRecruitResp) String() string            { return proto.CompactTextString(m) }
func (*CreateGameRecruitResp) ProtoMessage()               {}
func (*CreateGameRecruitResp) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{7} }

func (m *CreateGameRecruitResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CreateGameRecruitResp) GetGrDetail() *GameRecruitDetail {
	if m != nil {
		return m.GrDetail
	}
	return nil
}

// 解散/退出 招募
type QuitGameRecruitReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GrId    uint32      `protobuf:"varint,2,req,name=gr_id,json=grId" json:"gr_id"`
	Uid     uint32      `protobuf:"varint,3,req,name=uid" json:"uid"`
}

func (m *QuitGameRecruitReq) Reset()                    { *m = QuitGameRecruitReq{} }
func (m *QuitGameRecruitReq) String() string            { return proto.CompactTextString(m) }
func (*QuitGameRecruitReq) ProtoMessage()               {}
func (*QuitGameRecruitReq) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{8} }

func (m *QuitGameRecruitReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *QuitGameRecruitReq) GetGrId() uint32 {
	if m != nil {
		return m.GrId
	}
	return 0
}

func (m *QuitGameRecruitReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type QuitGameRecruitResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *QuitGameRecruitResp) Reset()                    { *m = QuitGameRecruitResp{} }
func (m *QuitGameRecruitResp) String() string            { return proto.CompactTextString(m) }
func (*QuitGameRecruitResp) ProtoMessage()               {}
func (*QuitGameRecruitResp) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{9} }

func (m *QuitGameRecruitResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 加入队伍
type JoinGameRecruitReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GrId    uint32      `protobuf:"varint,2,req,name=gr_id,json=grId" json:"gr_id"`
	Uid     uint32      `protobuf:"varint,3,req,name=uid" json:"uid"`
	GameId  uint32      `protobuf:"varint,4,req,name=game_id,json=gameId" json:"game_id"`
}

func (m *JoinGameRecruitReq) Reset()                    { *m = JoinGameRecruitReq{} }
func (m *JoinGameRecruitReq) String() string            { return proto.CompactTextString(m) }
func (*JoinGameRecruitReq) ProtoMessage()               {}
func (*JoinGameRecruitReq) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{10} }

func (m *JoinGameRecruitReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *JoinGameRecruitReq) GetGrId() uint32 {
	if m != nil {
		return m.GrId
	}
	return 0
}

func (m *JoinGameRecruitReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *JoinGameRecruitReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type JoinGameRecruitResp struct {
	BaseResp *ga.BaseResp       `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GrDetail *GameRecruitDetail `protobuf:"bytes,2,req,name=grDetail" json:"grDetail,omitempty"`
}

func (m *JoinGameRecruitResp) Reset()                    { *m = JoinGameRecruitResp{} }
func (m *JoinGameRecruitResp) String() string            { return proto.CompactTextString(m) }
func (*JoinGameRecruitResp) ProtoMessage()               {}
func (*JoinGameRecruitResp) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{11} }

func (m *JoinGameRecruitResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *JoinGameRecruitResp) GetGrDetail() *GameRecruitDetail {
	if m != nil {
		return m.GrDetail
	}
	return nil
}

// 开启，进入房间
type IntoGameRecruitChannelReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GrId    uint32      `protobuf:"varint,2,req,name=gr_id,json=grId" json:"gr_id"`
	Uid     uint32      `protobuf:"varint,3,req,name=uid" json:"uid"`
}

func (m *IntoGameRecruitChannelReq) Reset()                    { *m = IntoGameRecruitChannelReq{} }
func (m *IntoGameRecruitChannelReq) String() string            { return proto.CompactTextString(m) }
func (*IntoGameRecruitChannelReq) ProtoMessage()               {}
func (*IntoGameRecruitChannelReq) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{12} }

func (m *IntoGameRecruitChannelReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *IntoGameRecruitChannelReq) GetGrId() uint32 {
	if m != nil {
		return m.GrId
	}
	return 0
}

func (m *IntoGameRecruitChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IntoGameRecruitChannelResp struct {
	BaseResp  *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ChannelId uint32       `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	UidList   []uint32     `protobuf:"varint,3,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *IntoGameRecruitChannelResp) Reset()                    { *m = IntoGameRecruitChannelResp{} }
func (m *IntoGameRecruitChannelResp) String() string            { return proto.CompactTextString(m) }
func (*IntoGameRecruitChannelResp) ProtoMessage()               {}
func (*IntoGameRecruitChannelResp) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{13} }

func (m *IntoGameRecruitChannelResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *IntoGameRecruitChannelResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *IntoGameRecruitChannelResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 招募邀请
type InviteGameRecruitReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uid        uint32      `protobuf:"varint,2,req,name=uid" json:"uid"`
	InviteeUid uint32      `protobuf:"varint,3,req,name=invitee_uid,json=inviteeUid" json:"invitee_uid"`
}

func (m *InviteGameRecruitReq) Reset()                    { *m = InviteGameRecruitReq{} }
func (m *InviteGameRecruitReq) String() string            { return proto.CompactTextString(m) }
func (*InviteGameRecruitReq) ProtoMessage()               {}
func (*InviteGameRecruitReq) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{14} }

func (m *InviteGameRecruitReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *InviteGameRecruitReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *InviteGameRecruitReq) GetInviteeUid() uint32 {
	if m != nil {
		return m.InviteeUid
	}
	return 0
}

type InviteGameRecruitResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *InviteGameRecruitResp) Reset()                    { *m = InviteGameRecruitResp{} }
func (m *InviteGameRecruitResp) String() string            { return proto.CompactTextString(m) }
func (*InviteGameRecruitResp) ProtoMessage()               {}
func (*InviteGameRecruitResp) Descriptor() ([]byte, []int) { return fileDescriptorTeam_, []int{15} }

func (m *InviteGameRecruitResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*GameRecruitMemberChange)(nil), "ga.GameRecruitMemberChange")
	proto.RegisterType((*UserInfo)(nil), "ga.UserInfo")
	proto.RegisterType((*GameRecruitChannelInfo)(nil), "ga.GameRecruitChannelInfo")
	proto.RegisterType((*GameRecruitDetail)(nil), "ga.GameRecruitDetail")
	proto.RegisterType((*GetGameRecruitReq)(nil), "ga.GetGameRecruitReq")
	proto.RegisterType((*GetGameRecruitResp)(nil), "ga.GetGameRecruitResp")
	proto.RegisterType((*CreateGameRecruitReq)(nil), "ga.CreateGameRecruitReq")
	proto.RegisterType((*CreateGameRecruitResp)(nil), "ga.CreateGameRecruitResp")
	proto.RegisterType((*QuitGameRecruitReq)(nil), "ga.QuitGameRecruitReq")
	proto.RegisterType((*QuitGameRecruitResp)(nil), "ga.QuitGameRecruitResp")
	proto.RegisterType((*JoinGameRecruitReq)(nil), "ga.JoinGameRecruitReq")
	proto.RegisterType((*JoinGameRecruitResp)(nil), "ga.JoinGameRecruitResp")
	proto.RegisterType((*IntoGameRecruitChannelReq)(nil), "ga.IntoGameRecruitChannelReq")
	proto.RegisterType((*IntoGameRecruitChannelResp)(nil), "ga.IntoGameRecruitChannelResp")
	proto.RegisterType((*InviteGameRecruitReq)(nil), "ga.InviteGameRecruitReq")
	proto.RegisterType((*InviteGameRecruitResp)(nil), "ga.InviteGameRecruitResp")
	proto.RegisterEnum("ga.GameRecruitMemberChange_PARTYSTATUS", GameRecruitMemberChange_PARTYSTATUS_name, GameRecruitMemberChange_PARTYSTATUS_value)
	proto.RegisterEnum("ga.CreateGameRecruitReq_PushToPublic", CreateGameRecruitReq_PushToPublic_name, CreateGameRecruitReq_PushToPublic_value)
}
func (m *GameRecruitMemberChange) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameRecruitMemberChange) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserInfo == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.UserInfo.Size()))
		n1, err := m.UserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.PartyStatus))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.GrId))
	return i, nil
}

func (m *UserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x22
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	return i, nil
}

func (m *GameRecruitChannelInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameRecruitChannelInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.ChannelId))
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintTeam_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GameRecruitDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameRecruitDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.OwnerUid))
	if len(m.UserInfos) > 0 {
		for _, msg := range m.UserInfos {
			dAtA[i] = 0x12
			i++
			i = encodeVarintTeam_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(len(m.Desc)))
	i += copy(dAtA[i:], m.Desc)
	dAtA[i] = 0x30
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.GrId))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	dAtA[i] = 0x40
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.InviterUid))
	return i, nil
}

func (m *GetGameRecruitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameRecruitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.BaseReq.Size()))
		n2, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetGameRecruitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameRecruitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.BaseResp.Size()))
		n3, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.GrDetail == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("grDetail")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.GrDetail.Size()))
		n4, err := m.GrDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *CreateGameRecruitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateGameRecruitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.GrDetail == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("grDetail")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.GrDetail.Size()))
		n6, err := m.GrDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.PushToPublic))
	return i, nil
}

func (m *CreateGameRecruitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateGameRecruitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.BaseResp.Size()))
		n7, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if m.GrDetail == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gr_detail")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.GrDetail.Size()))
		n8, err := m.GrDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *QuitGameRecruitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QuitGameRecruitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.GrId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *QuitGameRecruitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QuitGameRecruitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.BaseResp.Size()))
		n10, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	return i, nil
}

func (m *JoinGameRecruitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *JoinGameRecruitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.BaseReq.Size()))
		n11, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.GrId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.GameId))
	return i, nil
}

func (m *JoinGameRecruitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *JoinGameRecruitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.BaseResp.Size()))
		n12, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	if m.GrDetail == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("grDetail")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.GrDetail.Size()))
		n13, err := m.GrDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	return i, nil
}

func (m *IntoGameRecruitChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IntoGameRecruitChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.BaseReq.Size()))
		n14, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.GrId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *IntoGameRecruitChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IntoGameRecruitChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.BaseResp.Size()))
		n15, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.ChannelId))
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintTeam_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *InviteGameRecruitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InviteGameRecruitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.BaseReq.Size()))
		n16, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTeam_(dAtA, i, uint64(m.InviteeUid))
	return i, nil
}

func (m *InviteGameRecruitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InviteGameRecruitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTeam_(dAtA, i, uint64(m.BaseResp.Size()))
		n17, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	return i, nil
}

func encodeFixed64Team_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Team_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintTeam_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GameRecruitMemberChange) Size() (n int) {
	var l int
	_ = l
	if m.UserInfo != nil {
		l = m.UserInfo.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	n += 1 + sovTeam_(uint64(m.PartyStatus))
	n += 1 + sovTeam_(uint64(m.GrId))
	return n
}

func (m *UserInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTeam_(uint64(m.Uid))
	n += 1 + sovTeam_(uint64(m.Sex))
	l = len(m.Account)
	n += 1 + l + sovTeam_(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovTeam_(uint64(l))
	return n
}

func (m *GameRecruitChannelInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTeam_(uint64(m.ChannelId))
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovTeam_(uint64(e))
		}
	}
	return n
}

func (m *GameRecruitDetail) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTeam_(uint64(m.OwnerUid))
	if len(m.UserInfos) > 0 {
		for _, e := range m.UserInfos {
			l = e.Size()
			n += 1 + l + sovTeam_(uint64(l))
		}
	}
	n += 1 + sovTeam_(uint64(m.GameId))
	l = len(m.Title)
	n += 1 + l + sovTeam_(uint64(l))
	l = len(m.Desc)
	n += 1 + l + sovTeam_(uint64(l))
	n += 1 + sovTeam_(uint64(m.GrId))
	l = len(m.GameName)
	n += 1 + l + sovTeam_(uint64(l))
	n += 1 + sovTeam_(uint64(m.InviterUid))
	return n
}

func (m *GetGameRecruitReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	n += 1 + sovTeam_(uint64(m.Uid))
	return n
}

func (m *GetGameRecruitResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	if m.GrDetail != nil {
		l = m.GrDetail.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	return n
}

func (m *CreateGameRecruitReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	if m.GrDetail != nil {
		l = m.GrDetail.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	n += 1 + sovTeam_(uint64(m.PushToPublic))
	return n
}

func (m *CreateGameRecruitResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	if m.GrDetail != nil {
		l = m.GrDetail.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	return n
}

func (m *QuitGameRecruitReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	n += 1 + sovTeam_(uint64(m.GrId))
	n += 1 + sovTeam_(uint64(m.Uid))
	return n
}

func (m *QuitGameRecruitResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	return n
}

func (m *JoinGameRecruitReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	n += 1 + sovTeam_(uint64(m.GrId))
	n += 1 + sovTeam_(uint64(m.Uid))
	n += 1 + sovTeam_(uint64(m.GameId))
	return n
}

func (m *JoinGameRecruitResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	if m.GrDetail != nil {
		l = m.GrDetail.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	return n
}

func (m *IntoGameRecruitChannelReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	n += 1 + sovTeam_(uint64(m.GrId))
	n += 1 + sovTeam_(uint64(m.Uid))
	return n
}

func (m *IntoGameRecruitChannelResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	n += 1 + sovTeam_(uint64(m.ChannelId))
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovTeam_(uint64(e))
		}
	}
	return n
}

func (m *InviteGameRecruitReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	n += 1 + sovTeam_(uint64(m.Uid))
	n += 1 + sovTeam_(uint64(m.InviteeUid))
	return n
}

func (m *InviteGameRecruitResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovTeam_(uint64(l))
	}
	return n
}

func sovTeam_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozTeam_(x uint64) (n int) {
	return sovTeam_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GameRecruitMemberChange) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameRecruitMemberChange: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameRecruitMemberChange: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserInfo == nil {
				m.UserInfo = &UserInfo{}
			}
			if err := m.UserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PartyStatus", wireType)
			}
			m.PartyStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PartyStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrId", wireType)
			}
			m.GrId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GrId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("user_info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("party_status")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gr_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("sex")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameRecruitChannelInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameRecruitChannelInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameRecruitChannelInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTeam_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTeam_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTeam_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTeam_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameRecruitDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameRecruitDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameRecruitDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OwnerUid", wireType)
			}
			m.OwnerUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OwnerUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserInfos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserInfos = append(m.UserInfos, &UserInfo{})
			if err := m.UserInfos[len(m.UserInfos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrId", wireType)
			}
			m.GrId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GrId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InviterUid", wireType)
			}
			m.InviterUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InviterUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("owner_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("desc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameRecruitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameRecruitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameRecruitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameRecruitResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameRecruitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameRecruitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GrDetail == nil {
				m.GrDetail = &GameRecruitDetail{}
			}
			if err := m.GrDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("grDetail")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateGameRecruitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateGameRecruitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateGameRecruitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GrDetail == nil {
				m.GrDetail = &GameRecruitDetail{}
			}
			if err := m.GrDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushToPublic", wireType)
			}
			m.PushToPublic = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushToPublic |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("grDetail")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateGameRecruitResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateGameRecruitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateGameRecruitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GrDetail == nil {
				m.GrDetail = &GameRecruitDetail{}
			}
			if err := m.GrDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gr_detail")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QuitGameRecruitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QuitGameRecruitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QuitGameRecruitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrId", wireType)
			}
			m.GrId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GrId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gr_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QuitGameRecruitResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QuitGameRecruitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QuitGameRecruitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *JoinGameRecruitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: JoinGameRecruitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: JoinGameRecruitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrId", wireType)
			}
			m.GrId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GrId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gr_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *JoinGameRecruitResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: JoinGameRecruitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: JoinGameRecruitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GrDetail == nil {
				m.GrDetail = &GameRecruitDetail{}
			}
			if err := m.GrDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("grDetail")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IntoGameRecruitChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IntoGameRecruitChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IntoGameRecruitChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrId", wireType)
			}
			m.GrId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GrId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gr_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IntoGameRecruitChannelResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IntoGameRecruitChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IntoGameRecruitChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTeam_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTeam_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTeam_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTeam_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *InviteGameRecruitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: InviteGameRecruitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: InviteGameRecruitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InviteeUid", wireType)
			}
			m.InviteeUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InviteeUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("invitee_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *InviteGameRecruitResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: InviteGameRecruitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: InviteGameRecruitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTeam_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTeam_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTeam_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipTeam_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowTeam_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTeam_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthTeam_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowTeam_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipTeam_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthTeam_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowTeam_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("team_.proto", fileDescriptorTeam_) }

var fileDescriptorTeam_ = []byte{
	// 777 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x56, 0xcd, 0x6e, 0xf3, 0x44,
	0x14, 0xfd, 0xec, 0xa4, 0x8d, 0x73, 0x9d, 0x22, 0x3a, 0xfd, 0xc1, 0xad, 0x44, 0x48, 0x8d, 0x28,
	0x01, 0xa4, 0x54, 0x44, 0x62, 0x4f, 0x53, 0xaa, 0x92, 0xaa, 0x3f, 0xc1, 0x49, 0x10, 0xb0, 0xb1,
	0x26, 0xf6, 0xd4, 0x1d, 0x61, 0x7b, 0x5c, 0xcf, 0xb8, 0x50, 0x09, 0xd6, 0xec, 0x10, 0x2f, 0xc0,
	0x4b, 0xf0, 0x0e, 0x48, 0x5d, 0xf2, 0x04, 0x08, 0x95, 0x17, 0x41, 0x63, 0x3b, 0xa9, 0xe3, 0x14,
	0xa1, 0x54, 0xf4, 0xdb, 0xcd, 0x9c, 0x3b, 0xbe, 0x73, 0xef, 0xb9, 0x67, 0x8e, 0x0c, 0xba, 0x20,
	0x38, 0xb0, 0x3b, 0x51, 0xcc, 0x04, 0x43, 0xaa, 0x87, 0x77, 0xd7, 0x3c, 0x6c, 0x4f, 0x30, 0x27,
	0x19, 0x64, 0xfe, 0xa6, 0xc0, 0x5b, 0x27, 0x38, 0x20, 0x16, 0x71, 0xe2, 0x84, 0x8a, 0x73, 0x12,
	0x4c, 0x48, 0x7c, 0x74, 0x8d, 0x43, 0x8f, 0xa0, 0x0f, 0xa0, 0x9e, 0x70, 0x12, 0xdb, 0x34, 0xbc,
	0x62, 0x86, 0xd2, 0x52, 0xdb, 0x7a, 0xb7, 0xd1, 0xf1, 0x70, 0x67, 0xcc, 0x49, 0xdc, 0x0f, 0xaf,
	0x98, 0xa5, 0x25, 0xf9, 0x0a, 0xbd, 0x0f, 0x8d, 0x08, 0xc7, 0xe2, 0xce, 0xe6, 0x02, 0x8b, 0x84,
	0x1b, 0x6a, 0x4b, 0x6d, 0xaf, 0xf5, 0xaa, 0xf7, 0x7f, 0xbe, 0xf3, 0xca, 0xd2, 0xd3, 0xc8, 0x30,
	0x0d, 0xa0, 0x1d, 0x58, 0xf1, 0x62, 0x9b, 0xba, 0x46, 0xa5, 0x70, 0xa2, 0xea, 0xc5, 0x7d, 0xd7,
	0x6c, 0x83, 0x3e, 0x38, 0xb4, 0x46, 0x5f, 0x0f, 0x47, 0x87, 0xa3, 0xf1, 0x10, 0xe9, 0x50, 0x3b,
	0xbd, 0xec, 0x5f, 0xd8, 0x27, 0xd6, 0x9b, 0x0a, 0x6a, 0x80, 0x76, 0x76, 0x7c, 0xf8, 0xe5, 0xb1,
	0xdc, 0xa9, 0xe6, 0x0f, 0xa0, 0x4d, 0x6b, 0x40, 0xdb, 0x50, 0x49, 0xa8, 0x9b, 0x96, 0x37, 0x4d,
	0x27, 0x01, 0x89, 0x73, 0xf2, 0xfd, 0x5c, 0x21, 0x12, 0x40, 0x4d, 0xa8, 0x61, 0xc7, 0x61, 0x49,
	0x28, 0xd2, 0x12, 0xea, 0x79, 0x6c, 0x0a, 0xa2, 0x16, 0x68, 0x21, 0x75, 0xbe, 0x0d, 0x71, 0x40,
	0x8c, 0x6a, 0x4b, 0x99, 0x1d, 0x98, 0xa1, 0xe6, 0x57, 0xb0, 0x5d, 0x60, 0x4c, 0x72, 0x15, 0x12,
	0x3f, 0xad, 0xe5, 0x5d, 0x00, 0x27, 0xdb, 0xda, 0xa5, 0x92, 0xea, 0x39, 0xde, 0x77, 0xd1, 0x0e,
	0x68, 0x09, 0x75, 0x6d, 0x9f, 0x72, 0x61, 0xa8, 0xad, 0x4a, 0x7b, 0xcd, 0xaa, 0x25, 0xd4, 0x3d,
	0xa3, 0x5c, 0x98, 0xbf, 0xaa, 0xb0, 0x5e, 0x48, 0xfd, 0x19, 0x11, 0x98, 0xfa, 0x68, 0x0f, 0xea,
	0xec, 0xbb, 0x90, 0xc4, 0x76, 0xb9, 0x4f, 0x2d, 0x85, 0xc7, 0xd4, 0x45, 0x1f, 0x01, 0xcc, 0x26,
	0xc5, 0xd3, 0xac, 0xe5, 0x51, 0xd5, 0xa7, 0xa3, 0xe2, 0xe8, 0x6d, 0xa8, 0x79, 0x38, 0x20, 0xe5,
	0x21, 0xac, 0x4a, 0xb0, 0xef, 0xa2, 0x5d, 0x58, 0x11, 0x54, 0xf8, 0xb2, 0xfb, 0x47, 0x7a, 0x32,
	0x08, 0x19, 0x50, 0x75, 0x09, 0x77, 0x8c, 0x95, 0x42, 0x28, 0x45, 0x1e, 0xe7, 0xba, 0xda, 0x52,
	0xe6, 0xe7, 0x2a, 0xeb, 0x4f, 0xef, 0x4b, 0x29, 0xad, 0x15, 0x29, 0x95, 0xf0, 0x05, 0x0e, 0x08,
	0x7a, 0x0f, 0x74, 0x1a, 0xde, 0x52, 0x91, 0x37, 0xa9, 0x15, 0x72, 0x40, 0x1e, 0x18, 0x53, 0xd7,
	0x1c, 0xc2, 0xfa, 0x09, 0x11, 0x05, 0x86, 0x2c, 0x72, 0x83, 0xf6, 0x41, 0x93, 0x7a, 0xb6, 0x63,
	0x72, 0x93, 0x8b, 0x54, 0x97, 0x9d, 0xf7, 0x30, 0x27, 0x16, 0xb9, 0xb1, 0x6a, 0x93, 0x6c, 0x31,
	0x15, 0x8a, 0x5a, 0x12, 0x8a, 0x19, 0x03, 0x2a, 0x27, 0xe5, 0x91, 0xd4, 0x7e, 0x9e, 0x95, 0x47,
	0x45, 0xed, 0x67, 0x69, 0x79, 0x64, 0x69, 0x93, 0x7c, 0x85, 0x3e, 0x06, 0xcd, 0x8b, 0xb3, 0x59,
	0xa5, 0xd9, 0xf5, 0xee, 0x96, 0x3c, 0xb9, 0x30, 0x48, 0x6b, 0x76, 0xcc, 0xfc, 0x5d, 0x81, 0xcd,
	0xa3, 0x98, 0x60, 0x41, 0x9e, 0xd9, 0xcc, 0xf2, 0x77, 0xa2, 0x0f, 0xe1, 0x8d, 0x28, 0xe1, 0xd7,
	0xb6, 0x60, 0x76, 0x94, 0x4c, 0x7c, 0xea, 0x18, 0x95, 0x02, 0xcd, 0x0d, 0x19, 0x1b, 0xb1, 0x41,
	0x1a, 0x31, 0xf7, 0xa1, 0x31, 0x28, 0xec, 0x91, 0x06, 0xd5, 0xc1, 0x78, 0xf8, 0x79, 0xf6, 0x10,
	0x2f, 0x2e, 0x47, 0x76, 0xba, 0x53, 0xcd, 0x5b, 0xd8, 0x7a, 0xa2, 0x8d, 0xe5, 0xe8, 0xeb, 0x42,
	0xdd, 0x8b, 0x6d, 0x77, 0x29, 0xfe, 0x18, 0xa0, 0x2f, 0x12, 0xfa, 0x5c, 0x25, 0xcc, 0xb4, 0xaa,
	0x96, 0x3d, 0x68, 0x2a, 0x92, 0x4a, 0x59, 0x24, 0x9f, 0xc2, 0xc6, 0xc2, 0x85, 0x4b, 0xb5, 0x69,
	0xfe, 0xac, 0x00, 0x3a, 0x65, 0x34, 0x7c, 0x6d, 0x35, 0x17, 0xdf, 0x79, 0x75, 0xf1, 0x9d, 0x9b,
	0x1c, 0x36, 0x16, 0xea, 0x79, 0x71, 0xe1, 0xdf, 0xc2, 0x4e, 0x3f, 0x14, 0x6c, 0xd1, 0x3f, 0x5f,
	0x78, 0x7e, 0x3f, 0x29, 0xb0, 0xfb, 0x6f, 0x17, 0x2f, 0xd7, 0xf4, 0xbc, 0xc7, 0xab, 0xff, 0xed,
	0xf1, 0x95, 0x79, 0x8f, 0xff, 0x11, 0x36, 0xfb, 0xa9, 0xa3, 0xfd, 0xbf, 0x36, 0xf6, 0x68, 0xa1,
	0xc4, 0x2e, 0x33, 0x90, 0x5b, 0x28, 0x91, 0x16, 0xda, 0x83, 0xad, 0x27, 0xae, 0x5f, 0x8a, 0x82,
	0xde, 0xf9, 0xfd, 0x43, 0x53, 0xf9, 0xe3, 0xa1, 0xa9, 0xfc, 0xf5, 0xd0, 0x54, 0x7e, 0xf9, 0xbb,
	0xf9, 0x0a, 0x0c, 0x87, 0x05, 0x9d, 0x3b, 0x7a, 0xc7, 0x12, 0xf9, 0x49, 0xc0, 0x5c, 0xe2, 0x67,
	0xff, 0x17, 0xdf, 0xec, 0x79, 0xcc, 0xc7, 0xa1, 0xd7, 0xf9, 0xa4, 0x2b, 0x44, 0xc7, 0x61, 0xc1,
	0x41, 0x0a, 0x3b, 0xcc, 0x3f, 0xc0, 0x51, 0x74, 0x20, 0x7f, 0x4e, 0xfe, 0x09, 0x00, 0x00, 0xff,
	0xff, 0x59, 0x5b, 0x73, 0xf1, 0xa3, 0x08, 0x00, 0x00,
}
