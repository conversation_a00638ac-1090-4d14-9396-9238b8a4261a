// Code generated by protoc-gen-go. DO NOT EDIT.
// source: star-trek-logic_.proto

package star_trek_logic // import "golang.52tt.com/protocol/app/star-trek-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TrekResultType int32

const (
	TrekResultType_AllType TrekResultType = 0
	TrekResultType_Success TrekResultType = 1
	TrekResultType_Failed  TrekResultType = 2
)

var TrekResultType_name = map[int32]string{
	0: "AllType",
	1: "Success",
	2: "Failed",
}
var TrekResultType_value = map[string]int32{
	"AllType": 0,
	"Success": 1,
	"Failed":  2,
}

func (x TrekResultType) String() string {
	return proto.EnumName(TrekResultType_name, int32(x))
}
func (TrekResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{0}
}

type StarTrekResultOpt_AwardResult int32

const (
	StarTrekResultOpt_ConsolationAward StarTrekResultOpt_AwardResult = 0
	StarTrekResultOpt_BingoAward       StarTrekResultOpt_AwardResult = 1
	StarTrekResultOpt_TrekFail         StarTrekResultOpt_AwardResult = 2
)

var StarTrekResultOpt_AwardResult_name = map[int32]string{
	0: "ConsolationAward",
	1: "BingoAward",
	2: "TrekFail",
}
var StarTrekResultOpt_AwardResult_value = map[string]int32{
	"ConsolationAward": 0,
	"BingoAward":       1,
	"TrekFail":         2,
}

func (x StarTrekResultOpt_AwardResult) String() string {
	return proto.EnumName(StarTrekResultOpt_AwardResult_name, int32(x))
}
func (StarTrekResultOpt_AwardResult) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{2, 0}
}

// 奖励包裹信息
type StarTAwardInfo struct {
	PackId               uint32   `protobuf:"varint,1,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	PackName             string   `protobuf:"bytes,2,opt,name=pack_name,json=packName,proto3" json:"pack_name,omitempty"`
	PackPic              string   `protobuf:"bytes,3,opt,name=pack_pic,json=packPic,proto3" json:"pack_pic,omitempty"`
	PackAmount           uint32   `protobuf:"varint,4,opt,name=pack_amount,json=packAmount,proto3" json:"pack_amount,omitempty"`
	UnitPrice            uint32   `protobuf:"varint,5,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	FinTime              uint32   `protobuf:"varint,6,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StarTAwardInfo) Reset()         { *m = StarTAwardInfo{} }
func (m *StarTAwardInfo) String() string { return proto.CompactTextString(m) }
func (*StarTAwardInfo) ProtoMessage()    {}
func (*StarTAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{0}
}
func (m *StarTAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTAwardInfo.Unmarshal(m, b)
}
func (m *StarTAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTAwardInfo.Marshal(b, m, deterministic)
}
func (dst *StarTAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTAwardInfo.Merge(dst, src)
}
func (m *StarTAwardInfo) XXX_Size() int {
	return xxx_messageInfo_StarTAwardInfo.Size(m)
}
func (m *StarTAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StarTAwardInfo proto.InternalMessageInfo

func (m *StarTAwardInfo) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *StarTAwardInfo) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *StarTAwardInfo) GetPackPic() string {
	if m != nil {
		return m.PackPic
	}
	return ""
}

func (m *StarTAwardInfo) GetPackAmount() uint32 {
	if m != nil {
		return m.PackAmount
	}
	return 0
}

func (m *StarTAwardInfo) GetUnitPrice() uint32 {
	if m != nil {
		return m.UnitPrice
	}
	return 0
}

func (m *StarTAwardInfo) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

type StarTUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StarTUserInfo) Reset()         { *m = StarTUserInfo{} }
func (m *StarTUserInfo) String() string { return proto.CompactTextString(m) }
func (*StarTUserInfo) ProtoMessage()    {}
func (*StarTUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{1}
}
func (m *StarTUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTUserInfo.Unmarshal(m, b)
}
func (m *StarTUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTUserInfo.Marshal(b, m, deterministic)
}
func (dst *StarTUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTUserInfo.Merge(dst, src)
}
func (m *StarTUserInfo) XXX_Size() int {
	return xxx_messageInfo_StarTUserInfo.Size(m)
}
func (m *StarTUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StarTUserInfo proto.InternalMessageInfo

func (m *StarTUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StarTUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *StarTUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type StarTrekResultOpt struct {
	AwardResult          StarTrekResultOpt_AwardResult `protobuf:"varint,1,opt,name=award_result,json=awardResult,proto3,enum=ga.star_trek_logic.StarTrekResultOpt_AwardResult" json:"award_result,omitempty"`
	AwardInfo            *StarTAwardInfo               `protobuf:"bytes,2,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	NotifyText           string                        `protobuf:"bytes,3,opt,name=notify_text,json=notifyText,proto3" json:"notify_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *StarTrekResultOpt) Reset()         { *m = StarTrekResultOpt{} }
func (m *StarTrekResultOpt) String() string { return proto.CompactTextString(m) }
func (*StarTrekResultOpt) ProtoMessage()    {}
func (*StarTrekResultOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{2}
}
func (m *StarTrekResultOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrekResultOpt.Unmarshal(m, b)
}
func (m *StarTrekResultOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrekResultOpt.Marshal(b, m, deterministic)
}
func (dst *StarTrekResultOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrekResultOpt.Merge(dst, src)
}
func (m *StarTrekResultOpt) XXX_Size() int {
	return xxx_messageInfo_StarTrekResultOpt.Size(m)
}
func (m *StarTrekResultOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrekResultOpt.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrekResultOpt proto.InternalMessageInfo

func (m *StarTrekResultOpt) GetAwardResult() StarTrekResultOpt_AwardResult {
	if m != nil {
		return m.AwardResult
	}
	return StarTrekResultOpt_ConsolationAward
}

func (m *StarTrekResultOpt) GetAwardInfo() *StarTAwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *StarTrekResultOpt) GetNotifyText() string {
	if m != nil {
		return m.NotifyText
	}
	return ""
}

// 全服 中奖人数补充信息
type StarTrekBreakOpt struct {
	BingoUserNum         uint32   `protobuf:"varint,1,opt,name=bingo_user_num,json=bingoUserNum,proto3" json:"bingo_user_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StarTrekBreakOpt) Reset()         { *m = StarTrekBreakOpt{} }
func (m *StarTrekBreakOpt) String() string { return proto.CompactTextString(m) }
func (*StarTrekBreakOpt) ProtoMessage()    {}
func (*StarTrekBreakOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{3}
}
func (m *StarTrekBreakOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrekBreakOpt.Unmarshal(m, b)
}
func (m *StarTrekBreakOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrekBreakOpt.Marshal(b, m, deterministic)
}
func (dst *StarTrekBreakOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrekBreakOpt.Merge(dst, src)
}
func (m *StarTrekBreakOpt) XXX_Size() int {
	return xxx_messageInfo_StarTrekBreakOpt.Size(m)
}
func (m *StarTrekBreakOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrekBreakOpt.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrekBreakOpt proto.InternalMessageInfo

func (m *StarTrekBreakOpt) GetBingoUserNum() uint32 {
	if m != nil {
		return m.BingoUserNum
	}
	return 0
}

// 上期回顾
type LastRoundReview struct {
	RoundId              uint32           `protobuf:"varint,1,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	RoundTime            uint32           `protobuf:"varint,2,opt,name=round_time,json=roundTime,proto3" json:"round_time,omitempty"`
	TrekResult           bool             `protobuf:"varint,3,opt,name=trek_result,json=trekResult,proto3" json:"trek_result,omitempty"`
	AwardPack            *StarTAwardInfo  `protobuf:"bytes,4,opt,name=award_pack,json=awardPack,proto3" json:"award_pack,omitempty"`
	UserInfo             *StarTUserInfo   `protobuf:"bytes,5,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	UserList             []*StarTUserInfo `protobuf:"bytes,6,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	BingoUserNum         uint32           `protobuf:"varint,7,opt,name=bingo_user_num,json=bingoUserNum,proto3" json:"bingo_user_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *LastRoundReview) Reset()         { *m = LastRoundReview{} }
func (m *LastRoundReview) String() string { return proto.CompactTextString(m) }
func (*LastRoundReview) ProtoMessage()    {}
func (*LastRoundReview) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{4}
}
func (m *LastRoundReview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LastRoundReview.Unmarshal(m, b)
}
func (m *LastRoundReview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LastRoundReview.Marshal(b, m, deterministic)
}
func (dst *LastRoundReview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LastRoundReview.Merge(dst, src)
}
func (m *LastRoundReview) XXX_Size() int {
	return xxx_messageInfo_LastRoundReview.Size(m)
}
func (m *LastRoundReview) XXX_DiscardUnknown() {
	xxx_messageInfo_LastRoundReview.DiscardUnknown(m)
}

var xxx_messageInfo_LastRoundReview proto.InternalMessageInfo

func (m *LastRoundReview) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *LastRoundReview) GetRoundTime() uint32 {
	if m != nil {
		return m.RoundTime
	}
	return 0
}

func (m *LastRoundReview) GetTrekResult() bool {
	if m != nil {
		return m.TrekResult
	}
	return false
}

func (m *LastRoundReview) GetAwardPack() *StarTAwardInfo {
	if m != nil {
		return m.AwardPack
	}
	return nil
}

func (m *LastRoundReview) GetUserInfo() *StarTUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *LastRoundReview) GetUserList() []*StarTUserInfo {
	if m != nil {
		return m.UserList
	}
	return nil
}

func (m *LastRoundReview) GetBingoUserNum() uint32 {
	if m != nil {
		return m.BingoUserNum
	}
	return 0
}

// 下期预告
type NextRoundForecast struct {
	RoundId              uint32          `protobuf:"varint,1,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	AwardPack            *StarTAwardInfo `protobuf:"bytes,2,opt,name=award_pack,json=awardPack,proto3" json:"award_pack,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *NextRoundForecast) Reset()         { *m = NextRoundForecast{} }
func (m *NextRoundForecast) String() string { return proto.CompactTextString(m) }
func (*NextRoundForecast) ProtoMessage()    {}
func (*NextRoundForecast) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{5}
}
func (m *NextRoundForecast) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NextRoundForecast.Unmarshal(m, b)
}
func (m *NextRoundForecast) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NextRoundForecast.Marshal(b, m, deterministic)
}
func (dst *NextRoundForecast) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NextRoundForecast.Merge(dst, src)
}
func (m *NextRoundForecast) XXX_Size() int {
	return xxx_messageInfo_NextRoundForecast.Size(m)
}
func (m *NextRoundForecast) XXX_DiscardUnknown() {
	xxx_messageInfo_NextRoundForecast.DiscardUnknown(m)
}

var xxx_messageInfo_NextRoundForecast proto.InternalMessageInfo

func (m *NextRoundForecast) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *NextRoundForecast) GetAwardPack() *StarTAwardInfo {
	if m != nil {
		return m.AwardPack
	}
	return nil
}

// 中奖轮播信息
type RollingAwardInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	PackName             string   `protobuf:"bytes,3,opt,name=pack_name,json=packName,proto3" json:"pack_name,omitempty"`
	PackPic              string   `protobuf:"bytes,4,opt,name=pack_pic,json=packPic,proto3" json:"pack_pic,omitempty"`
	PackAmount           uint32   `protobuf:"varint,5,opt,name=pack_amount,json=packAmount,proto3" json:"pack_amount,omitempty"`
	BingoUserNum         uint32   `protobuf:"varint,6,opt,name=bingo_user_num,json=bingoUserNum,proto3" json:"bingo_user_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RollingAwardInfo) Reset()         { *m = RollingAwardInfo{} }
func (m *RollingAwardInfo) String() string { return proto.CompactTextString(m) }
func (*RollingAwardInfo) ProtoMessage()    {}
func (*RollingAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{6}
}
func (m *RollingAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RollingAwardInfo.Unmarshal(m, b)
}
func (m *RollingAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RollingAwardInfo.Marshal(b, m, deterministic)
}
func (dst *RollingAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RollingAwardInfo.Merge(dst, src)
}
func (m *RollingAwardInfo) XXX_Size() int {
	return xxx_messageInfo_RollingAwardInfo.Size(m)
}
func (m *RollingAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RollingAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RollingAwardInfo proto.InternalMessageInfo

func (m *RollingAwardInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RollingAwardInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *RollingAwardInfo) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *RollingAwardInfo) GetPackPic() string {
	if m != nil {
		return m.PackPic
	}
	return ""
}

func (m *RollingAwardInfo) GetPackAmount() uint32 {
	if m != nil {
		return m.PackAmount
	}
	return 0
}

func (m *RollingAwardInfo) GetBingoUserNum() uint32 {
	if m != nil {
		return m.BingoUserNum
	}
	return 0
}

// 获取星际巡航信息，每5s拉取刷新
type GetStatTrekInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetStatTrekInfoReq) Reset()         { *m = GetStatTrekInfoReq{} }
func (m *GetStatTrekInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetStatTrekInfoReq) ProtoMessage()    {}
func (*GetStatTrekInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{7}
}
func (m *GetStatTrekInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStatTrekInfoReq.Unmarshal(m, b)
}
func (m *GetStatTrekInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStatTrekInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetStatTrekInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStatTrekInfoReq.Merge(dst, src)
}
func (m *GetStatTrekInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetStatTrekInfoReq.Size(m)
}
func (m *GetStatTrekInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStatTrekInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStatTrekInfoReq proto.InternalMessageInfo

func (m *GetStatTrekInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetStatTrekInfoResp struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RoundId              uint32              `protobuf:"varint,2,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	BeginTime            uint32              `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32              `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	AwardPack            *StarTAwardInfo     `protobuf:"bytes,5,opt,name=award_pack,json=awardPack,proto3" json:"award_pack,omitempty"`
	InvestProgress       uint32              `protobuf:"varint,6,opt,name=invest_progress,json=investProgress,proto3" json:"invest_progress,omitempty"`
	UserCount            uint32              `protobuf:"varint,7,opt,name=user_count,json=userCount,proto3" json:"user_count,omitempty"`
	LastS                *LastRoundReview    `protobuf:"bytes,8,opt,name=last_s,json=lastS,proto3" json:"last_s,omitempty"`
	NextS                *NextRoundForecast  `protobuf:"bytes,9,opt,name=next_s,json=nextS,proto3" json:"next_s,omitempty"`
	Records              []*RollingAwardInfo `protobuf:"bytes,10,rep,name=records,proto3" json:"records,omitempty"`
	LatestPartitions     []*StarTUserInfo    `protobuf:"bytes,11,rep,name=latest_partitions,json=latestPartitions,proto3" json:"latest_partitions,omitempty"`
	UserSupply           uint32              `protobuf:"varint,12,opt,name=user_supply,json=userSupply,proto3" json:"user_supply,omitempty"`
	MinInvest            uint32              `protobuf:"varint,13,opt,name=min_invest,json=minInvest,proto3" json:"min_invest,omitempty"`
	RoundSupplyLimit     uint32              `protobuf:"varint,14,opt,name=round_supply_limit,json=roundSupplyLimit,proto3" json:"round_supply_limit,omitempty"`
	UserDailyInvest      uint32              `protobuf:"varint,15,opt,name=user_daily_invest,json=userDailyInvest,proto3" json:"user_daily_invest,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetStatTrekInfoResp) Reset()         { *m = GetStatTrekInfoResp{} }
func (m *GetStatTrekInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetStatTrekInfoResp) ProtoMessage()    {}
func (*GetStatTrekInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{8}
}
func (m *GetStatTrekInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStatTrekInfoResp.Unmarshal(m, b)
}
func (m *GetStatTrekInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStatTrekInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetStatTrekInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStatTrekInfoResp.Merge(dst, src)
}
func (m *GetStatTrekInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetStatTrekInfoResp.Size(m)
}
func (m *GetStatTrekInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStatTrekInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStatTrekInfoResp proto.InternalMessageInfo

func (m *GetStatTrekInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetStatTrekInfoResp) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetAwardPack() *StarTAwardInfo {
	if m != nil {
		return m.AwardPack
	}
	return nil
}

func (m *GetStatTrekInfoResp) GetInvestProgress() uint32 {
	if m != nil {
		return m.InvestProgress
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetUserCount() uint32 {
	if m != nil {
		return m.UserCount
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetLastS() *LastRoundReview {
	if m != nil {
		return m.LastS
	}
	return nil
}

func (m *GetStatTrekInfoResp) GetNextS() *NextRoundForecast {
	if m != nil {
		return m.NextS
	}
	return nil
}

func (m *GetStatTrekInfoResp) GetRecords() []*RollingAwardInfo {
	if m != nil {
		return m.Records
	}
	return nil
}

func (m *GetStatTrekInfoResp) GetLatestPartitions() []*StarTUserInfo {
	if m != nil {
		return m.LatestPartitions
	}
	return nil
}

func (m *GetStatTrekInfoResp) GetUserSupply() uint32 {
	if m != nil {
		return m.UserSupply
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetMinInvest() uint32 {
	if m != nil {
		return m.MinInvest
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetRoundSupplyLimit() uint32 {
	if m != nil {
		return m.RoundSupplyLimit
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetUserDailyInvest() uint32 {
	if m != nil {
		return m.UserDailyInvest
	}
	return 0
}

// 废弃
type GetSupplyValueChangeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSupplyValueChangeReq) Reset()         { *m = GetSupplyValueChangeReq{} }
func (m *GetSupplyValueChangeReq) String() string { return proto.CompactTextString(m) }
func (*GetSupplyValueChangeReq) ProtoMessage()    {}
func (*GetSupplyValueChangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{9}
}
func (m *GetSupplyValueChangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSupplyValueChangeReq.Unmarshal(m, b)
}
func (m *GetSupplyValueChangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSupplyValueChangeReq.Marshal(b, m, deterministic)
}
func (dst *GetSupplyValueChangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSupplyValueChangeReq.Merge(dst, src)
}
func (m *GetSupplyValueChangeReq) XXX_Size() int {
	return xxx_messageInfo_GetSupplyValueChangeReq.Size(m)
}
func (m *GetSupplyValueChangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSupplyValueChangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSupplyValueChangeReq proto.InternalMessageInfo

func (m *GetSupplyValueChangeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

// 废弃
type GetSupplyValueChangeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ServerSupply         uint32        `protobuf:"varint,2,opt,name=server_supply,json=serverSupply,proto3" json:"server_supply,omitempty"`
	MySupply             uint32        `protobuf:"varint,3,opt,name=my_supply,json=mySupply,proto3" json:"my_supply,omitempty"`
	MinInvest            uint32        `protobuf:"varint,4,opt,name=min_invest,json=minInvest,proto3" json:"min_invest,omitempty"`
	RoundSupplyLimit     uint32        `protobuf:"varint,5,opt,name=round_supply_limit,json=roundSupplyLimit,proto3" json:"round_supply_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSupplyValueChangeResp) Reset()         { *m = GetSupplyValueChangeResp{} }
func (m *GetSupplyValueChangeResp) String() string { return proto.CompactTextString(m) }
func (*GetSupplyValueChangeResp) ProtoMessage()    {}
func (*GetSupplyValueChangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{10}
}
func (m *GetSupplyValueChangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSupplyValueChangeResp.Unmarshal(m, b)
}
func (m *GetSupplyValueChangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSupplyValueChangeResp.Marshal(b, m, deterministic)
}
func (dst *GetSupplyValueChangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSupplyValueChangeResp.Merge(dst, src)
}
func (m *GetSupplyValueChangeResp) XXX_Size() int {
	return xxx_messageInfo_GetSupplyValueChangeResp.Size(m)
}
func (m *GetSupplyValueChangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSupplyValueChangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSupplyValueChangeResp proto.InternalMessageInfo

func (m *GetSupplyValueChangeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSupplyValueChangeResp) GetServerSupply() uint32 {
	if m != nil {
		return m.ServerSupply
	}
	return 0
}

func (m *GetSupplyValueChangeResp) GetMySupply() uint32 {
	if m != nil {
		return m.MySupply
	}
	return 0
}

func (m *GetSupplyValueChangeResp) GetMinInvest() uint32 {
	if m != nil {
		return m.MinInvest
	}
	return 0
}

func (m *GetSupplyValueChangeResp) GetRoundSupplyLimit() uint32 {
	if m != nil {
		return m.RoundSupplyLimit
	}
	return 0
}

type SupplyConf struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftType             uint32   `protobuf:"varint,2,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SupplyConf) Reset()         { *m = SupplyConf{} }
func (m *SupplyConf) String() string { return proto.CompactTextString(m) }
func (*SupplyConf) ProtoMessage()    {}
func (*SupplyConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{11}
}
func (m *SupplyConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SupplyConf.Unmarshal(m, b)
}
func (m *SupplyConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SupplyConf.Marshal(b, m, deterministic)
}
func (dst *SupplyConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SupplyConf.Merge(dst, src)
}
func (m *SupplyConf) XXX_Size() int {
	return xxx_messageInfo_SupplyConf.Size(m)
}
func (m *SupplyConf) XXX_DiscardUnknown() {
	xxx_messageInfo_SupplyConf.DiscardUnknown(m)
}

var xxx_messageInfo_SupplyConf proto.InternalMessageInfo

func (m *SupplyConf) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *SupplyConf) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

type GetSupplyConfReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSupplyConfReq) Reset()         { *m = GetSupplyConfReq{} }
func (m *GetSupplyConfReq) String() string { return proto.CompactTextString(m) }
func (*GetSupplyConfReq) ProtoMessage()    {}
func (*GetSupplyConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{12}
}
func (m *GetSupplyConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSupplyConfReq.Unmarshal(m, b)
}
func (m *GetSupplyConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSupplyConfReq.Marshal(b, m, deterministic)
}
func (dst *GetSupplyConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSupplyConfReq.Merge(dst, src)
}
func (m *GetSupplyConfReq) XXX_Size() int {
	return xxx_messageInfo_GetSupplyConfReq.Size(m)
}
func (m *GetSupplyConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSupplyConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSupplyConfReq proto.InternalMessageInfo

func (m *GetSupplyConfReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetSupplyConfResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ConfList             []*SupplyConf `protobuf:"bytes,2,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	DailyLimit           uint32        `protobuf:"varint,3,opt,name=daily_limit,json=dailyLimit,proto3" json:"daily_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSupplyConfResp) Reset()         { *m = GetSupplyConfResp{} }
func (m *GetSupplyConfResp) String() string { return proto.CompactTextString(m) }
func (*GetSupplyConfResp) ProtoMessage()    {}
func (*GetSupplyConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{13}
}
func (m *GetSupplyConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSupplyConfResp.Unmarshal(m, b)
}
func (m *GetSupplyConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSupplyConfResp.Marshal(b, m, deterministic)
}
func (dst *GetSupplyConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSupplyConfResp.Merge(dst, src)
}
func (m *GetSupplyConfResp) XXX_Size() int {
	return xxx_messageInfo_GetSupplyConfResp.Size(m)
}
func (m *GetSupplyConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSupplyConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSupplyConfResp proto.InternalMessageInfo

func (m *GetSupplyConfResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSupplyConfResp) GetConfList() []*SupplyConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

func (m *GetSupplyConfResp) GetDailyLimit() uint32 {
	if m != nil {
		return m.DailyLimit
	}
	return 0
}

type CostSupplyInfo struct {
	// uint32 user_item_id = 1;        // 礼物在用户背包中的item_id
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftType             uint32   `protobuf:"varint,3,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	GiftNum              uint32   `protobuf:"varint,4,opt,name=gift_num,json=giftNum,proto3" json:"gift_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CostSupplyInfo) Reset()         { *m = CostSupplyInfo{} }
func (m *CostSupplyInfo) String() string { return proto.CompactTextString(m) }
func (*CostSupplyInfo) ProtoMessage()    {}
func (*CostSupplyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{14}
}
func (m *CostSupplyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CostSupplyInfo.Unmarshal(m, b)
}
func (m *CostSupplyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CostSupplyInfo.Marshal(b, m, deterministic)
}
func (dst *CostSupplyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CostSupplyInfo.Merge(dst, src)
}
func (m *CostSupplyInfo) XXX_Size() int {
	return xxx_messageInfo_CostSupplyInfo.Size(m)
}
func (m *CostSupplyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CostSupplyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CostSupplyInfo proto.InternalMessageInfo

func (m *CostSupplyInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *CostSupplyInfo) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *CostSupplyInfo) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

// 去探险
type DoInvestReq struct {
	BaseReq              *app.BaseReq      `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32            `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32            `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SupplyList           []*CostSupplyInfo `protobuf:"bytes,4,rep,name=supply_list,json=supplyList,proto3" json:"supply_list,omitempty"`
	Cost                 uint32            `protobuf:"varint,5,opt,name=cost,proto3" json:"cost,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *DoInvestReq) Reset()         { *m = DoInvestReq{} }
func (m *DoInvestReq) String() string { return proto.CompactTextString(m) }
func (*DoInvestReq) ProtoMessage()    {}
func (*DoInvestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{15}
}
func (m *DoInvestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoInvestReq.Unmarshal(m, b)
}
func (m *DoInvestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoInvestReq.Marshal(b, m, deterministic)
}
func (dst *DoInvestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoInvestReq.Merge(dst, src)
}
func (m *DoInvestReq) XXX_Size() int {
	return xxx_messageInfo_DoInvestReq.Size(m)
}
func (m *DoInvestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DoInvestReq.DiscardUnknown(m)
}

var xxx_messageInfo_DoInvestReq proto.InternalMessageInfo

func (m *DoInvestReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DoInvestReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DoInvestReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *DoInvestReq) GetSupplyList() []*CostSupplyInfo {
	if m != nil {
		return m.SupplyList
	}
	return nil
}

func (m *DoInvestReq) GetCost() uint32 {
	if m != nil {
		return m.Cost
	}
	return 0
}

type DoInvestResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RoundId              uint32        `protobuf:"varint,2,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	MySupply             uint32        `protobuf:"varint,3,opt,name=my_supply,json=mySupply,proto3" json:"my_supply,omitempty"`
	RoundSupplyLimit     uint32        `protobuf:"varint,4,opt,name=round_supply_limit,json=roundSupplyLimit,proto3" json:"round_supply_limit,omitempty"`
	MinInvest            uint32        `protobuf:"varint,5,opt,name=min_invest,json=minInvest,proto3" json:"min_invest,omitempty"`
	ServerSupply         uint32        `protobuf:"varint,6,opt,name=server_supply,json=serverSupply,proto3" json:"server_supply,omitempty"`
	UserDailyInvest      uint32        `protobuf:"varint,7,opt,name=user_daily_invest,json=userDailyInvest,proto3" json:"user_daily_invest,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DoInvestResp) Reset()         { *m = DoInvestResp{} }
func (m *DoInvestResp) String() string { return proto.CompactTextString(m) }
func (*DoInvestResp) ProtoMessage()    {}
func (*DoInvestResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{16}
}
func (m *DoInvestResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoInvestResp.Unmarshal(m, b)
}
func (m *DoInvestResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoInvestResp.Marshal(b, m, deterministic)
}
func (dst *DoInvestResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoInvestResp.Merge(dst, src)
}
func (m *DoInvestResp) XXX_Size() int {
	return xxx_messageInfo_DoInvestResp.Size(m)
}
func (m *DoInvestResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DoInvestResp.DiscardUnknown(m)
}

var xxx_messageInfo_DoInvestResp proto.InternalMessageInfo

func (m *DoInvestResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *DoInvestResp) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *DoInvestResp) GetMySupply() uint32 {
	if m != nil {
		return m.MySupply
	}
	return 0
}

func (m *DoInvestResp) GetRoundSupplyLimit() uint32 {
	if m != nil {
		return m.RoundSupplyLimit
	}
	return 0
}

func (m *DoInvestResp) GetMinInvest() uint32 {
	if m != nil {
		return m.MinInvest
	}
	return 0
}

func (m *DoInvestResp) GetServerSupply() uint32 {
	if m != nil {
		return m.ServerSupply
	}
	return 0
}

func (m *DoInvestResp) GetUserDailyInvest() uint32 {
	if m != nil {
		return m.UserDailyInvest
	}
	return 0
}

// 获取用户巡航记录
type GetMyTrekRecordReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TrekResult           uint32       `protobuf:"varint,2,opt,name=trek_result,json=trekResult,proto3" json:"trek_result,omitempty"`
	LastPageFinalId      string       `protobuf:"bytes,3,opt,name=last_page_final_id,json=lastPageFinalId,proto3" json:"last_page_final_id,omitempty"`
	Limit                uint32       `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMyTrekRecordReq) Reset()         { *m = GetMyTrekRecordReq{} }
func (m *GetMyTrekRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetMyTrekRecordReq) ProtoMessage()    {}
func (*GetMyTrekRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{17}
}
func (m *GetMyTrekRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyTrekRecordReq.Unmarshal(m, b)
}
func (m *GetMyTrekRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyTrekRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetMyTrekRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyTrekRecordReq.Merge(dst, src)
}
func (m *GetMyTrekRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetMyTrekRecordReq.Size(m)
}
func (m *GetMyTrekRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyTrekRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyTrekRecordReq proto.InternalMessageInfo

func (m *GetMyTrekRecordReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMyTrekRecordReq) GetTrekResult() uint32 {
	if m != nil {
		return m.TrekResult
	}
	return 0
}

func (m *GetMyTrekRecordReq) GetLastPageFinalId() string {
	if m != nil {
		return m.LastPageFinalId
	}
	return ""
}

func (m *GetMyTrekRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type MyTrekRecord struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	RoundId              uint32   `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	RoundTime            uint32   `protobuf:"varint,4,opt,name=round_time,json=roundTime,proto3" json:"round_time,omitempty"`
	TrekResult           bool     `protobuf:"varint,5,opt,name=trek_result,json=trekResult,proto3" json:"trek_result,omitempty"`
	PrizeName            string   `protobuf:"bytes,6,opt,name=prize_name,json=prizeName,proto3" json:"prize_name,omitempty"`
	PrizePic             string   `protobuf:"bytes,7,opt,name=Prize_pic,json=PrizePic,proto3" json:"Prize_pic,omitempty"`
	LuckyUser            string   `protobuf:"bytes,8,opt,name=lucky_user,json=luckyUser,proto3" json:"lucky_user,omitempty"`
	MyInvest             uint32   `protobuf:"varint,9,opt,name=my_invest,json=myInvest,proto3" json:"my_invest,omitempty"`
	MySupplyDesc         string   `protobuf:"bytes,10,opt,name=my_supply_desc,json=mySupplyDesc,proto3" json:"my_supply_desc,omitempty"`
	MyAwardDesc          string   `protobuf:"bytes,11,opt,name=my_award_desc,json=myAwardDesc,proto3" json:"my_award_desc,omitempty"`
	BingoUserNum         uint32   `protobuf:"varint,12,opt,name=bingo_user_num,json=bingoUserNum,proto3" json:"bingo_user_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MyTrekRecord) Reset()         { *m = MyTrekRecord{} }
func (m *MyTrekRecord) String() string { return proto.CompactTextString(m) }
func (*MyTrekRecord) ProtoMessage()    {}
func (*MyTrekRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{18}
}
func (m *MyTrekRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyTrekRecord.Unmarshal(m, b)
}
func (m *MyTrekRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyTrekRecord.Marshal(b, m, deterministic)
}
func (dst *MyTrekRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyTrekRecord.Merge(dst, src)
}
func (m *MyTrekRecord) XXX_Size() int {
	return xxx_messageInfo_MyTrekRecord.Size(m)
}
func (m *MyTrekRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_MyTrekRecord.DiscardUnknown(m)
}

var xxx_messageInfo_MyTrekRecord proto.InternalMessageInfo

func (m *MyTrekRecord) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MyTrekRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MyTrekRecord) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *MyTrekRecord) GetRoundTime() uint32 {
	if m != nil {
		return m.RoundTime
	}
	return 0
}

func (m *MyTrekRecord) GetTrekResult() bool {
	if m != nil {
		return m.TrekResult
	}
	return false
}

func (m *MyTrekRecord) GetPrizeName() string {
	if m != nil {
		return m.PrizeName
	}
	return ""
}

func (m *MyTrekRecord) GetPrizePic() string {
	if m != nil {
		return m.PrizePic
	}
	return ""
}

func (m *MyTrekRecord) GetLuckyUser() string {
	if m != nil {
		return m.LuckyUser
	}
	return ""
}

func (m *MyTrekRecord) GetMyInvest() uint32 {
	if m != nil {
		return m.MyInvest
	}
	return 0
}

func (m *MyTrekRecord) GetMySupplyDesc() string {
	if m != nil {
		return m.MySupplyDesc
	}
	return ""
}

func (m *MyTrekRecord) GetMyAwardDesc() string {
	if m != nil {
		return m.MyAwardDesc
	}
	return ""
}

func (m *MyTrekRecord) GetBingoUserNum() uint32 {
	if m != nil {
		return m.BingoUserNum
	}
	return 0
}

type GetMyTrekRecordResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MyRecordList         []*MyTrekRecord `protobuf:"bytes,2,rep,name=my_record_list,json=myRecordList,proto3" json:"my_record_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetMyTrekRecordResp) Reset()         { *m = GetMyTrekRecordResp{} }
func (m *GetMyTrekRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetMyTrekRecordResp) ProtoMessage()    {}
func (*GetMyTrekRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{19}
}
func (m *GetMyTrekRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyTrekRecordResp.Unmarshal(m, b)
}
func (m *GetMyTrekRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyTrekRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetMyTrekRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyTrekRecordResp.Merge(dst, src)
}
func (m *GetMyTrekRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetMyTrekRecordResp.Size(m)
}
func (m *GetMyTrekRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyTrekRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyTrekRecordResp proto.InternalMessageInfo

func (m *GetMyTrekRecordResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMyTrekRecordResp) GetMyRecordList() []*MyTrekRecord {
	if m != nil {
		return m.MyRecordList
	}
	return nil
}

// 往期回顾
type GetAllTrekHistoryReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Offset               uint32       `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32       `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAllTrekHistoryReq) Reset()         { *m = GetAllTrekHistoryReq{} }
func (m *GetAllTrekHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetAllTrekHistoryReq) ProtoMessage()    {}
func (*GetAllTrekHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{20}
}
func (m *GetAllTrekHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllTrekHistoryReq.Unmarshal(m, b)
}
func (m *GetAllTrekHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllTrekHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetAllTrekHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllTrekHistoryReq.Merge(dst, src)
}
func (m *GetAllTrekHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetAllTrekHistoryReq.Size(m)
}
func (m *GetAllTrekHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllTrekHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllTrekHistoryReq proto.InternalMessageInfo

func (m *GetAllTrekHistoryReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAllTrekHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAllTrekHistoryReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetAllTrekHistoryResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ReviewList           []*LastRoundReview `protobuf:"bytes,2,rep,name=review_list,json=reviewList,proto3" json:"review_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetAllTrekHistoryResp) Reset()         { *m = GetAllTrekHistoryResp{} }
func (m *GetAllTrekHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetAllTrekHistoryResp) ProtoMessage()    {}
func (*GetAllTrekHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{21}
}
func (m *GetAllTrekHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllTrekHistoryResp.Unmarshal(m, b)
}
func (m *GetAllTrekHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllTrekHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetAllTrekHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllTrekHistoryResp.Merge(dst, src)
}
func (m *GetAllTrekHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetAllTrekHistoryResp.Size(m)
}
func (m *GetAllTrekHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllTrekHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllTrekHistoryResp proto.InternalMessageInfo

func (m *GetAllTrekHistoryResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAllTrekHistoryResp) GetReviewList() []*LastRoundReview {
	if m != nil {
		return m.ReviewList
	}
	return nil
}

type StarTrekNotify struct {
	HasFloating          bool     `protobuf:"varint,1,opt,name=has_floating,json=hasFloating,proto3" json:"has_floating,omitempty"`
	FloatingText         string   `protobuf:"bytes,2,opt,name=floating_text,json=floatingText,proto3" json:"floating_text,omitempty"`
	FloatingDuration     uint32   `protobuf:"varint,3,opt,name=floating_duration,json=floatingDuration,proto3" json:"floating_duration,omitempty"`
	HasPublic            bool     `protobuf:"varint,4,opt,name=has_public,json=hasPublic,proto3" json:"has_public,omitempty"`
	PublicText           string   `protobuf:"bytes,5,opt,name=public_text,json=publicText,proto3" json:"public_text,omitempty"`
	PublicColor          string   `protobuf:"bytes,6,opt,name=public_color,json=publicColor,proto3" json:"public_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StarTrekNotify) Reset()         { *m = StarTrekNotify{} }
func (m *StarTrekNotify) String() string { return proto.CompactTextString(m) }
func (*StarTrekNotify) ProtoMessage()    {}
func (*StarTrekNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{22}
}
func (m *StarTrekNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrekNotify.Unmarshal(m, b)
}
func (m *StarTrekNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrekNotify.Marshal(b, m, deterministic)
}
func (dst *StarTrekNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrekNotify.Merge(dst, src)
}
func (m *StarTrekNotify) XXX_Size() int {
	return xxx_messageInfo_StarTrekNotify.Size(m)
}
func (m *StarTrekNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrekNotify.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrekNotify proto.InternalMessageInfo

func (m *StarTrekNotify) GetHasFloating() bool {
	if m != nil {
		return m.HasFloating
	}
	return false
}

func (m *StarTrekNotify) GetFloatingText() string {
	if m != nil {
		return m.FloatingText
	}
	return ""
}

func (m *StarTrekNotify) GetFloatingDuration() uint32 {
	if m != nil {
		return m.FloatingDuration
	}
	return 0
}

func (m *StarTrekNotify) GetHasPublic() bool {
	if m != nil {
		return m.HasPublic
	}
	return false
}

func (m *StarTrekNotify) GetPublicText() string {
	if m != nil {
		return m.PublicText
	}
	return ""
}

func (m *StarTrekNotify) GetPublicColor() string {
	if m != nil {
		return m.PublicColor
	}
	return ""
}

// 是否可见入口，以及浮层显示
type StarTrekEntryAndNotifyReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StarTrekEntryAndNotifyReq) Reset()         { *m = StarTrekEntryAndNotifyReq{} }
func (m *StarTrekEntryAndNotifyReq) String() string { return proto.CompactTextString(m) }
func (*StarTrekEntryAndNotifyReq) ProtoMessage()    {}
func (*StarTrekEntryAndNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{23}
}
func (m *StarTrekEntryAndNotifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrekEntryAndNotifyReq.Unmarshal(m, b)
}
func (m *StarTrekEntryAndNotifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrekEntryAndNotifyReq.Marshal(b, m, deterministic)
}
func (dst *StarTrekEntryAndNotifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrekEntryAndNotifyReq.Merge(dst, src)
}
func (m *StarTrekEntryAndNotifyReq) XXX_Size() int {
	return xxx_messageInfo_StarTrekEntryAndNotifyReq.Size(m)
}
func (m *StarTrekEntryAndNotifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrekEntryAndNotifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrekEntryAndNotifyReq proto.InternalMessageInfo

func (m *StarTrekEntryAndNotifyReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StarTrekEntryAndNotifyReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type StarTrekEntryAndNotifyResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CannotSee            bool            `protobuf:"varint,2,opt,name=cannot_see,json=cannotSee,proto3" json:"cannot_see,omitempty"`
	DayActivityBegin     uint32          `protobuf:"varint,3,opt,name=day_activity_begin,json=dayActivityBegin,proto3" json:"day_activity_begin,omitempty"`
	DayActivityEnd       uint32          `protobuf:"varint,4,opt,name=day_activity_end,json=dayActivityEnd,proto3" json:"day_activity_end,omitempty"`
	StatTrekNotify       *StarTrekNotify `protobuf:"bytes,5,opt,name=stat_trek_notify,json=statTrekNotify,proto3" json:"stat_trek_notify,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *StarTrekEntryAndNotifyResp) Reset()         { *m = StarTrekEntryAndNotifyResp{} }
func (m *StarTrekEntryAndNotifyResp) String() string { return proto.CompactTextString(m) }
func (*StarTrekEntryAndNotifyResp) ProtoMessage()    {}
func (*StarTrekEntryAndNotifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_logic__550c12de9251210b, []int{24}
}
func (m *StarTrekEntryAndNotifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrekEntryAndNotifyResp.Unmarshal(m, b)
}
func (m *StarTrekEntryAndNotifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrekEntryAndNotifyResp.Marshal(b, m, deterministic)
}
func (dst *StarTrekEntryAndNotifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrekEntryAndNotifyResp.Merge(dst, src)
}
func (m *StarTrekEntryAndNotifyResp) XXX_Size() int {
	return xxx_messageInfo_StarTrekEntryAndNotifyResp.Size(m)
}
func (m *StarTrekEntryAndNotifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrekEntryAndNotifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrekEntryAndNotifyResp proto.InternalMessageInfo

func (m *StarTrekEntryAndNotifyResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *StarTrekEntryAndNotifyResp) GetCannotSee() bool {
	if m != nil {
		return m.CannotSee
	}
	return false
}

func (m *StarTrekEntryAndNotifyResp) GetDayActivityBegin() uint32 {
	if m != nil {
		return m.DayActivityBegin
	}
	return 0
}

func (m *StarTrekEntryAndNotifyResp) GetDayActivityEnd() uint32 {
	if m != nil {
		return m.DayActivityEnd
	}
	return 0
}

func (m *StarTrekEntryAndNotifyResp) GetStatTrekNotify() *StarTrekNotify {
	if m != nil {
		return m.StatTrekNotify
	}
	return nil
}

func init() {
	proto.RegisterType((*StarTAwardInfo)(nil), "ga.star_trek_logic.StarTAwardInfo")
	proto.RegisterType((*StarTUserInfo)(nil), "ga.star_trek_logic.StarTUserInfo")
	proto.RegisterType((*StarTrekResultOpt)(nil), "ga.star_trek_logic.StarTrekResultOpt")
	proto.RegisterType((*StarTrekBreakOpt)(nil), "ga.star_trek_logic.StarTrekBreakOpt")
	proto.RegisterType((*LastRoundReview)(nil), "ga.star_trek_logic.LastRoundReview")
	proto.RegisterType((*NextRoundForecast)(nil), "ga.star_trek_logic.NextRoundForecast")
	proto.RegisterType((*RollingAwardInfo)(nil), "ga.star_trek_logic.RollingAwardInfo")
	proto.RegisterType((*GetStatTrekInfoReq)(nil), "ga.star_trek_logic.GetStatTrekInfoReq")
	proto.RegisterType((*GetStatTrekInfoResp)(nil), "ga.star_trek_logic.GetStatTrekInfoResp")
	proto.RegisterType((*GetSupplyValueChangeReq)(nil), "ga.star_trek_logic.GetSupplyValueChangeReq")
	proto.RegisterType((*GetSupplyValueChangeResp)(nil), "ga.star_trek_logic.GetSupplyValueChangeResp")
	proto.RegisterType((*SupplyConf)(nil), "ga.star_trek_logic.SupplyConf")
	proto.RegisterType((*GetSupplyConfReq)(nil), "ga.star_trek_logic.GetSupplyConfReq")
	proto.RegisterType((*GetSupplyConfResp)(nil), "ga.star_trek_logic.GetSupplyConfResp")
	proto.RegisterType((*CostSupplyInfo)(nil), "ga.star_trek_logic.CostSupplyInfo")
	proto.RegisterType((*DoInvestReq)(nil), "ga.star_trek_logic.DoInvestReq")
	proto.RegisterType((*DoInvestResp)(nil), "ga.star_trek_logic.DoInvestResp")
	proto.RegisterType((*GetMyTrekRecordReq)(nil), "ga.star_trek_logic.GetMyTrekRecordReq")
	proto.RegisterType((*MyTrekRecord)(nil), "ga.star_trek_logic.MyTrekRecord")
	proto.RegisterType((*GetMyTrekRecordResp)(nil), "ga.star_trek_logic.GetMyTrekRecordResp")
	proto.RegisterType((*GetAllTrekHistoryReq)(nil), "ga.star_trek_logic.GetAllTrekHistoryReq")
	proto.RegisterType((*GetAllTrekHistoryResp)(nil), "ga.star_trek_logic.GetAllTrekHistoryResp")
	proto.RegisterType((*StarTrekNotify)(nil), "ga.star_trek_logic.StarTrekNotify")
	proto.RegisterType((*StarTrekEntryAndNotifyReq)(nil), "ga.star_trek_logic.StarTrekEntryAndNotifyReq")
	proto.RegisterType((*StarTrekEntryAndNotifyResp)(nil), "ga.star_trek_logic.StarTrekEntryAndNotifyResp")
	proto.RegisterEnum("ga.star_trek_logic.TrekResultType", TrekResultType_name, TrekResultType_value)
	proto.RegisterEnum("ga.star_trek_logic.StarTrekResultOpt_AwardResult", StarTrekResultOpt_AwardResult_name, StarTrekResultOpt_AwardResult_value)
}

func init() {
	proto.RegisterFile("star-trek-logic_.proto", fileDescriptor_star_trek_logic__550c12de9251210b)
}

var fileDescriptor_star_trek_logic__550c12de9251210b = []byte{
	// 1730 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0xdd, 0x6e, 0xe4, 0x48,
	0x15, 0xde, 0xee, 0x4e, 0xff, 0xf8, 0x74, 0xa7, 0xd3, 0x29, 0x86, 0x5d, 0xcf, 0xac, 0x86, 0x99,
	0xf1, 0x0e, 0x30, 0xec, 0x4f, 0x46, 0x0c, 0x02, 0xa1, 0x65, 0xb5, 0x52, 0x7e, 0x36, 0xbb, 0x91,
	0x86, 0x10, 0x39, 0x01, 0x24, 0x6e, 0xac, 0x8a, 0x5d, 0xdd, 0x29, 0xb5, 0xed, 0xf2, 0xb8, 0xaa,
	0x67, 0x63, 0x9e, 0x60, 0x2f, 0x90, 0xe0, 0x01, 0xb8, 0xe0, 0x41, 0x90, 0xb8, 0xe1, 0x82, 0x07,
	0x80, 0x77, 0xe0, 0x9e, 0x17, 0x40, 0xe7, 0x94, 0xdd, 0x3f, 0x69, 0x27, 0x3b, 0xbd, 0x9a, 0x3b,
	0xd7, 0x77, 0xea, 0xd4, 0xcf, 0x57, 0xdf, 0x39, 0x75, 0xca, 0xf0, 0xae, 0x36, 0x3c, 0xff, 0xc4,
	0xe4, 0x62, 0xfa, 0x49, 0xac, 0x26, 0x32, 0x0c, 0xf6, 0xb2, 0x5c, 0x19, 0xc5, 0xd8, 0x84, 0xef,
	0xa1, 0x29, 0x40, 0x53, 0x40, 0xa6, 0x07, 0xdb, 0x13, 0x1e, 0x5c, 0x72, 0x2d, 0x6c, 0x17, 0xef,
	0xef, 0x0d, 0x18, 0x9e, 0x1b, 0x9e, 0x5f, 0xec, 0x7f, 0xcd, 0xf3, 0xe8, 0x24, 0x1d, 0x2b, 0xf6,
	0x1e, 0x74, 0x33, 0x1e, 0x4e, 0x03, 0x19, 0xb9, 0x8d, 0xc7, 0x8d, 0x67, 0xdb, 0x7e, 0x07, 0x9b,
	0x27, 0x11, 0x7b, 0x1f, 0x1c, 0x32, 0xa4, 0x3c, 0x11, 0x6e, 0xf3, 0x71, 0xe3, 0x99, 0xe3, 0xf7,
	0x10, 0x38, 0xe5, 0x89, 0x60, 0xf7, 0x81, 0xbe, 0x83, 0x4c, 0x86, 0x6e, 0x8b, 0x6c, 0x34, 0xca,
	0x99, 0x0c, 0xd9, 0x23, 0xe8, 0x93, 0x89, 0x27, 0x6a, 0x96, 0x1a, 0x77, 0x8b, 0x06, 0x05, 0x84,
	0xf6, 0x09, 0x61, 0x0f, 0x01, 0x66, 0xa9, 0x34, 0x41, 0x96, 0xcb, 0x50, 0xb8, 0x6d, 0xb2, 0x3b,
	0x88, 0x9c, 0x21, 0x80, 0x43, 0x8f, 0x65, 0x1a, 0x18, 0x99, 0x08, 0xb7, 0x43, 0xc6, 0xee, 0x58,
	0xa6, 0x17, 0x32, 0x11, 0xde, 0xef, 0x61, 0x9b, 0x56, 0xff, 0x5b, 0x2d, 0x72, 0x5a, 0xfc, 0x08,
	0x5a, 0xb3, 0xf9, 0xc2, 0xf1, 0x93, 0x3d, 0x80, 0x5e, 0x2a, 0xc3, 0xe9, 0xf2, 0xa2, 0xab, 0x36,
	0x73, 0xa1, 0xcb, 0xc3, 0x90, 0x56, 0x55, 0xae, 0xb9, 0x6c, 0x7a, 0x7f, 0x69, 0xc2, 0x2e, 0x8d,
	0x9c, 0x8b, 0xa9, 0x2f, 0xf4, 0x2c, 0x36, 0xbf, 0xc9, 0x0c, 0xbb, 0x80, 0x01, 0x47, 0x9e, 0x82,
	0x9c, 0x20, 0x9a, 0x66, 0xf8, 0xe2, 0xa7, 0x7b, 0xeb, 0x3c, 0xef, 0xad, 0x39, 0xef, 0x11, 0xc3,
	0xb6, 0xe9, 0xf7, 0xf9, 0xa2, 0xc1, 0xf6, 0x01, 0xec, 0xa8, 0x32, 0x1d, 0x2b, 0x5a, 0x63, 0xff,
	0x85, 0x77, 0xeb, 0x98, 0xf3, 0x83, 0xf2, 0x1d, 0x3e, 0x3f, 0xb3, 0x47, 0xd0, 0x4f, 0x95, 0x91,
	0xe3, 0x22, 0x30, 0xe2, 0xba, 0xda, 0x0c, 0x58, 0xe8, 0x42, 0x5c, 0x1b, 0x6f, 0x1f, 0xfa, 0x4b,
	0xf3, 0xb3, 0x7b, 0x30, 0x3a, 0x54, 0xa9, 0x56, 0x31, 0x37, 0x52, 0xa5, 0x64, 0x19, 0xbd, 0xc3,
	0x86, 0x00, 0x07, 0x32, 0x9d, 0x28, 0xdb, 0x6e, 0xb0, 0x01, 0xf4, 0x70, 0x0b, 0xc7, 0x5c, 0xc6,
	0xa3, 0xa6, 0xf7, 0x4b, 0x18, 0x55, 0x9b, 0x3a, 0xc8, 0x05, 0x9f, 0x22, 0x21, 0x4f, 0x61, 0x78,
	0x89, 0x1e, 0xc1, 0x4c, 0x8b, 0x3c, 0x48, 0x67, 0x49, 0xc9, 0xfc, 0x80, 0x50, 0x3c, 0x95, 0xd3,
	0x59, 0xe2, 0xfd, 0xa7, 0x09, 0x3b, 0x2f, 0xb9, 0x36, 0xbe, 0x9a, 0xa5, 0x91, 0x2f, 0x5e, 0x4b,
	0xf1, 0x35, 0x1e, 0x6a, 0x8e, 0xcd, 0x85, 0xcc, 0xba, 0xd4, 0x3e, 0x89, 0x50, 0x0e, 0xd6, 0x44,
	0x27, 0xde, 0xb4, 0x72, 0x20, 0x04, 0xcf, 0x1c, 0xf7, 0x4a, 0x9c, 0x94, 0x67, 0x80, 0x7b, 0xed,
	0xf9, 0x60, 0xe6, 0x5c, 0x2f, 0xf8, 0x44, 0x89, 0x91, 0xdc, 0x36, 0xe1, 0xf3, 0x8c, 0x87, 0x53,
	0xf6, 0x39, 0x38, 0xb4, 0x23, 0x3a, 0x91, 0x36, 0x8d, 0xf0, 0xe4, 0xd6, 0x11, 0x2a, 0xf1, 0xf9,
	0xbd, 0x59, 0x25, 0xc3, 0xca, 0x3f, 0x96, 0xda, 0xb8, 0x9d, 0xc7, 0xad, 0x0d, 0xfc, 0x5f, 0x4a,
	0x5d, 0xc7, 0x6b, 0xb7, 0x86, 0xd7, 0x57, 0xb0, 0x7b, 0x2a, 0xae, 0x2d, 0xad, 0xc7, 0x2a, 0x17,
	0x21, 0xd7, 0xe6, 0x2e, 0x62, 0x57, 0x89, 0x69, 0x7e, 0x07, 0x62, 0xbc, 0x7f, 0x34, 0x60, 0xe4,
	0xab, 0x38, 0x96, 0xe9, 0x64, 0x91, 0x31, 0x36, 0x0b, 0xba, 0x95, 0x34, 0xd2, 0xba, 0x23, 0x8d,
	0x6c, 0xdd, 0x99, 0x46, 0xda, 0x6b, 0x69, 0x64, 0x9d, 0xb4, 0x4e, 0x0d, 0x69, 0x9f, 0x01, 0xfb,
	0x52, 0x98, 0x73, 0xc3, 0x0d, 0x2a, 0x99, 0xf6, 0x27, 0x5e, 0xb1, 0x1f, 0x41, 0x0f, 0xb3, 0x62,
	0x90, 0x8b, 0x57, 0xb4, 0x8f, 0xfe, 0x8b, 0x3e, 0x12, 0x73, 0xc0, 0xb5, 0xf0, 0xc5, 0x2b, 0xbf,
	0x7b, 0x69, 0x3f, 0xbc, 0x7f, 0xb6, 0xe1, 0x7b, 0x6b, 0xee, 0x3a, 0x63, 0x3f, 0x01, 0xa7, 0xf4,
	0xd7, 0x59, 0x39, 0xc0, 0x60, 0x31, 0x80, 0xce, 0xfc, 0xde, 0x65, 0xf9, 0xb5, 0x72, 0x40, 0xcd,
	0x35, 0xe5, 0x5f, 0x8a, 0x49, 0x95, 0xeb, 0x5a, 0x56, 0xf9, 0x84, 0x90, 0xf2, 0xef, 0x43, 0x4f,
	0x54, 0x61, 0x61, 0xb3, 0x68, 0x57, 0x94, 0x41, 0xb1, 0x7a, 0xb4, 0xed, 0xef, 0xa2, 0xf9, 0x1f,
	0xc3, 0x8e, 0x4c, 0x5f, 0x0b, 0x8d, 0x79, 0x58, 0x4d, 0x72, 0xa1, 0x75, 0xc9, 0xdf, 0xd0, 0xc2,
	0x67, 0x25, 0x4a, 0xe9, 0x1a, 0x19, 0xb6, 0x89, 0xb3, 0x5b, 0xa6, 0x6b, 0x2d, 0xf2, 0x43, 0x3a,
	0x86, 0x4f, 0xa1, 0x13, 0x73, 0x6d, 0x02, 0xed, 0xf6, 0x68, 0x19, 0x1f, 0xd4, 0x2d, 0xe3, 0x46,
	0x3a, 0xf0, 0xdb, 0xe8, 0x72, 0xce, 0x3e, 0x83, 0x4e, 0x2a, 0xae, 0xd1, 0xd7, 0x21, 0xdf, 0x1f,
	0xd6, 0xf9, 0xae, 0x69, 0xde, 0x6f, 0xa3, 0xd3, 0x39, 0xfb, 0x1c, 0xba, 0xb9, 0x08, 0x55, 0x1e,
	0x69, 0x17, 0x28, 0xe6, 0x9e, 0xd6, 0xb9, 0xdf, 0x94, 0xaf, 0x5f, 0x39, 0xb1, 0x53, 0xd8, 0x8d,
	0xb9, 0x21, 0x06, 0x78, 0x6e, 0x24, 0xa6, 0x46, 0xed, 0xf6, 0xdf, 0x34, 0x7a, 0x47, 0xd6, 0xf7,
	0x6c, 0xee, 0x8a, 0x8a, 0x25, 0xa2, 0xf4, 0x2c, 0xcb, 0xe2, 0xc2, 0x1d, 0x58, 0xc5, 0x22, 0x74,
	0x4e, 0x08, 0x32, 0x99, 0xc8, 0x34, 0xb0, 0xfc, 0xba, 0xdb, 0x96, 0xc9, 0x44, 0xa6, 0x27, 0x04,
	0xb0, 0x8f, 0x81, 0x59, 0xa5, 0xd8, 0x01, 0x82, 0x58, 0x26, 0xd2, 0xb8, 0x43, 0xea, 0x36, 0x22,
	0x8b, 0x1d, 0xe7, 0x25, 0xe2, 0xec, 0x43, 0xd8, 0xa5, 0xd9, 0x22, 0x2e, 0xe3, 0xa2, 0x1a, 0x73,
	0x87, 0x3a, 0xef, 0xa0, 0xe1, 0x08, 0x71, 0x3b, 0xb2, 0xb7, 0x0f, 0xef, 0xa1, 0x8a, 0xc9, 0xfb,
	0x77, 0x3c, 0x9e, 0x89, 0xc3, 0x2b, 0x9e, 0x4e, 0xc4, 0x26, 0x91, 0xf0, 0xef, 0x06, 0xb8, 0xf5,
	0x63, 0x6c, 0x16, 0x0e, 0x1f, 0xc0, 0xb6, 0x16, 0xf9, 0xeb, 0x05, 0x4d, 0x36, 0x26, 0x06, 0x16,
	0x2c, 0x89, 0x7a, 0x1f, 0x9c, 0xa4, 0xa8, 0x3a, 0xd8, 0xb8, 0xe8, 0x25, 0x45, 0x2d, 0x8b, 0x5b,
	0x6f, 0xc6, 0x62, 0xbb, 0x9e, 0x45, 0xef, 0x00, 0xc0, 0x36, 0x0f, 0x55, 0x3a, 0xc6, 0x5a, 0x68,
	0x22, 0xc7, 0x66, 0xa9, 0x16, 0xc2, 0xa6, 0xad, 0x85, 0xc8, 0x60, 0x8a, 0xac, 0xba, 0xa2, 0x7a,
	0x08, 0x5c, 0x14, 0x99, 0xf0, 0x3e, 0x85, 0xd1, 0x9c, 0x19, 0x1c, 0x66, 0x13, 0x5a, 0xff, 0xda,
	0x80, 0xdd, 0x1b, 0xce, 0x9b, 0xf1, 0xf9, 0x2b, 0x70, 0x42, 0x95, 0x8e, 0xed, 0xd5, 0xd3, 0x24,
	0xf1, 0xfe, 0xa0, 0x56, 0xbc, 0x8b, 0x19, 0x7a, 0xe8, 0x40, 0xf7, 0xce, 0x23, 0xe8, 0x5b, 0xf9,
	0x58, 0x92, 0x2c, 0xd3, 0x40, 0x90, 0xa5, 0x87, 0xc3, 0xf0, 0x50, 0xe9, 0x72, 0x79, 0x55, 0xb9,
	0x58, 0x51, 0xd4, 0xbc, 0x9d, 0xa2, 0xd6, 0x2a, 0x45, 0x98, 0xca, 0xc8, 0x88, 0x59, 0xba, 0x4c,
	0x65, 0xd8, 0xc6, 0x04, 0xfd, 0xaf, 0x06, 0xf4, 0x8f, 0x94, 0x3d, 0xbc, 0x0d, 0x98, 0x43, 0x19,
	0x84, 0x57, 0x3c, 0x4d, 0x45, 0xbc, 0x58, 0x8b, 0x53, 0x22, 0x27, 0xd1, 0x4a, 0xda, 0x6d, 0xad,
	0xa6, 0xdd, 0x43, 0xe8, 0xcf, 0xb5, 0x41, 0x0a, 0x6a, 0xdd, 0x96, 0x3d, 0x57, 0xf7, 0xee, 0x83,
	0x2e, 0x95, 0xa3, 0x0d, 0x63, 0xb0, 0x15, 0x2a, 0x5d, 0x09, 0x8b, 0xbe, 0xbd, 0x3f, 0x37, 0x61,
	0xb0, 0xd8, 0xca, 0x5b, 0xbb, 0x26, 0xee, 0x8c, 0x86, 0x7a, 0xb9, 0x6f, 0xdd, 0x92, 0x34, 0x56,
	0x63, 0xa7, 0x7d, 0x33, 0x76, 0xd6, 0x82, 0xb3, 0x53, 0x13, 0x9c, 0xb5, 0x89, 0xa7, 0x5b, 0x9f,
	0x78, 0xfe, 0xd6, 0xa0, 0xeb, 0xf7, 0xd7, 0x85, 0xad, 0x8d, 0x31, 0xef, 0x6e, 0x72, 0xc6, 0x37,
	0x6a, 0x3f, 0xcb, 0xcb, 0x72, 0xed, 0xf7, 0x11, 0x30, 0xba, 0x7c, 0x32, 0x3e, 0x11, 0xc1, 0x58,
	0xa6, 0x3c, 0xae, 0xce, 0xdb, 0xf1, 0x77, 0xd0, 0x72, 0xc6, 0x27, 0xe2, 0x18, 0xf1, 0x93, 0x88,
	0xdd, 0x83, 0xf6, 0x32, 0x3b, 0xb6, 0xe1, 0xfd, 0xaf, 0x09, 0x83, 0xe5, 0xf5, 0xb1, 0x21, 0x34,
	0xcb, 0xf8, 0x77, 0xfc, 0xa6, 0x8c, 0xaa, 0x72, 0xa7, 0xb9, 0x28, 0x77, 0xee, 0xd0, 0xd6, 0x6a,
	0x31, 0xbb, 0xf5, 0x2d, 0xc5, 0x6c, 0x7b, 0xad, 0x98, 0x7d, 0x08, 0x90, 0xe5, 0xf2, 0x8f, 0xc2,
	0x96, 0x4b, 0x1d, 0x5a, 0x84, 0x43, 0xc8, 0x69, 0x59, 0x4c, 0x9d, 0x91, 0x19, 0x0b, 0xa6, 0xae,
	0x2d, 0xa6, 0x08, 0xc0, 0x8a, 0xe9, 0x21, 0x40, 0x3c, 0x0b, 0xa7, 0x05, 0x15, 0x44, 0x74, 0x1b,
	0x3b, 0xbe, 0x43, 0x08, 0x5e, 0x5a, 0xa5, 0x8c, 0xca, 0xf3, 0x72, 0x2a, 0x19, 0x95, 0x27, 0xff,
	0x14, 0x86, 0x73, 0x8d, 0x05, 0x91, 0xd0, 0xa1, 0x0b, 0xe4, 0x3f, 0xa8, 0x84, 0x76, 0x24, 0x74,
	0xc8, 0x3c, 0xd8, 0x4e, 0x8a, 0xc0, 0x56, 0x1e, 0xd4, 0xa9, 0x4f, 0x9d, 0xfa, 0x49, 0x41, 0xd7,
	0x2b, 0xf5, 0x59, 0x2f, 0xcb, 0x06, 0x35, 0x65, 0xd9, 0x37, 0x0d, 0x2a, 0xac, 0x56, 0x85, 0xb1,
	0x59, 0xc4, 0x1c, 0xd3, 0x92, 0xed, 0x65, 0xbe, 0x9c, 0xfe, 0x1e, 0xd7, 0x45, 0xf2, 0xca, 0x44,
	0x83, 0xa4, 0xb0, 0x5f, 0x18, 0xc9, 0x5e, 0x0c, 0xf7, 0xbe, 0x14, 0x66, 0x3f, 0x8e, 0xb1, 0xc7,
	0x57, 0x52, 0x1b, 0x95, 0x17, 0x9b, 0x88, 0xf4, 0x5d, 0xe8, 0xa8, 0xf1, 0x58, 0x8b, 0x4a, 0x9f,
	0x65, 0x6b, 0x21, 0xb7, 0xd6, 0xb2, 0xdc, 0xbe, 0x69, 0xc0, 0xf7, 0x6b, 0xa6, 0xdb, 0x6c, 0xeb,
	0x47, 0xd0, 0xcf, 0xa9, 0x90, 0x5a, 0xde, 0xf7, 0x1b, 0x15, 0x5e, 0x60, 0xfd, 0x68, 0xe3, 0xff,
	0xad, 0x7e, 0x06, 0xe4, 0x62, 0x7a, 0x4a, 0x6f, 0x47, 0xf6, 0x04, 0x06, 0x57, 0x5c, 0x07, 0xe3,
	0x58, 0x71, 0x23, 0xd3, 0x09, 0x2d, 0xa3, 0xe7, 0xf7, 0xaf, 0xb8, 0x3e, 0x2e, 0x21, 0xcc, 0x11,
	0x95, 0xd9, 0xbe, 0x3e, 0x6d, 0xc1, 0x3f, 0xa8, 0x40, 0x7c, 0x7f, 0xb2, 0x8f, 0x60, 0x77, 0xde,
	0x29, 0x9a, 0xe5, 0xf4, 0xec, 0x2c, 0x79, 0x18, 0x55, 0x86, 0xa3, 0x12, 0x47, 0xdd, 0xe2, 0xa4,
	0xd9, 0xec, 0x32, 0x2e, 0x9f, 0x01, 0x3d, 0xdf, 0xb9, 0xe2, 0xfa, 0x8c, 0x00, 0x7a, 0x08, 0xd0,
	0x97, 0x9d, 0xae, 0x6d, 0x1f, 0xbb, 0x16, 0xa2, 0xc9, 0x9e, 0xc0, 0xa0, 0xec, 0x10, 0xaa, 0x58,
	0xe5, 0x65, 0xd4, 0x94, 0x4e, 0x87, 0x08, 0x79, 0x97, 0x70, 0xbf, 0xda, 0xe9, 0x17, 0xa9, 0xc9,
	0x8b, 0xfd, 0x34, 0xb2, 0x3b, 0x7e, 0x7b, 0x37, 0x8e, 0xf7, 0xa7, 0x26, 0x3c, 0xb8, 0x6d, 0x92,
	0xcd, 0x8e, 0x17, 0x27, 0xe2, 0x69, 0xaa, 0x4c, 0xa0, 0x85, 0x2d, 0x37, 0x7a, 0xbe, 0x63, 0x91,
	0x73, 0x21, 0x30, 0xe5, 0x47, 0xbc, 0x08, 0x78, 0x68, 0xe4, 0x6b, 0x69, 0x8a, 0x80, 0x5e, 0x0c,
	0x15, 0xbb, 0x11, 0x2f, 0xf6, 0x4b, 0xc3, 0x01, 0xe2, 0xec, 0x19, 0x8c, 0x56, 0x7a, 0x8b, 0x34,
	0x2a, 0xf3, 0xd2, 0x70, 0xa9, 0xef, 0x17, 0x69, 0xc4, 0x5e, 0xc2, 0x48, 0x1b, 0x6e, 0xac, 0x7c,
	0xec, 0xcf, 0x84, 0x6f, 0x7d, 0x5a, 0xcc, 0xa5, 0xe3, 0x0f, 0x75, 0xf9, 0x48, 0xb2, 0xed, 0x0f,
	0x7f, 0x01, 0xc3, 0xc5, 0x0f, 0x11, 0x2a, 0x02, 0xfa, 0xd0, 0x45, 0xd9, 0x17, 0x99, 0x18, 0xbd,
	0x83, 0x8d, 0xf3, 0x59, 0x18, 0x0a, 0xad, 0x47, 0x0d, 0x06, 0xd0, 0x39, 0xe6, 0x32, 0x16, 0xd1,
	0xa8, 0x79, 0xf0, 0x15, 0xb8, 0xa1, 0x4a, 0xf6, 0x0a, 0x59, 0xa8, 0x19, 0x4e, 0x9b, 0xa8, 0x48,
	0xc4, 0xf6, 0xf7, 0xd5, 0x1f, 0x3e, 0x9e, 0xa8, 0x98, 0xa7, 0x93, 0xbd, 0x9f, 0xbf, 0x30, 0x66,
	0x2f, 0x54, 0xc9, 0x73, 0x82, 0x43, 0x15, 0x3f, 0xe7, 0x59, 0xf6, 0xfc, 0xc6, 0x6f, 0xb1, 0xcb,
	0x0e, 0x59, 0x7f, 0xf6, 0xff, 0x00, 0x00, 0x00, 0xff, 0xff, 0xd9, 0x6f, 0x44, 0x7a, 0x30, 0x13,
	0x00, 0x00,
}
