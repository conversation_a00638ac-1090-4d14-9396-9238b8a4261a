// Code generated by protoc-gen-go. DO NOT EDIT.
// source: playerlogic_.proto

package playerlogic // import "golang.52tt.com/protocol/app/playerlogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import topic_channel "golang.52tt.com/protocol/app/topic-channel"
import ugc "golang.52tt.com/protocol/app/ugc"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 只有是默认值 或其他场景7才能通过
type PageNoSettings int32

const (
	PageNoSettings_NORMAL        PageNoSettings = 0
	PageNoSettings_IN_HOME       PageNoSettings = 1
	PageNoSettings_IN_IM         PageNoSettings = 2
	PageNoSettings_PULISH_POST   PageNoSettings = 3
	PageNoSettings_EDIT_POST     PageNoSettings = 4
	PageNoSettings_PULISH_ROOM   PageNoSettings = 5
	PageNoSettings_MATCHING_ROOM PageNoSettings = 6
	PageNoSettings_OTHERS        PageNoSettings = 7
	PageNoSettings_MUSIC_NEST    PageNoSettings = 8
)

var PageNoSettings_name = map[int32]string{
	0: "NORMAL",
	1: "IN_HOME",
	2: "IN_IM",
	3: "PULISH_POST",
	4: "EDIT_POST",
	5: "PULISH_ROOM",
	6: "MATCHING_ROOM",
	7: "OTHERS",
	8: "MUSIC_NEST",
}
var PageNoSettings_value = map[string]int32{
	"NORMAL":        0,
	"IN_HOME":       1,
	"IN_IM":         2,
	"PULISH_POST":   3,
	"EDIT_POST":     4,
	"PULISH_ROOM":   5,
	"MATCHING_ROOM": 6,
	"OTHERS":        7,
	"MUSIC_NEST":    8,
}

func (x PageNoSettings) String() string {
	return proto.EnumName(PageNoSettings_name, int32(x))
}
func (PageNoSettings) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{0}
}

// 发放玩伴请求类型
type PlayerProvidedReqType int32

const (
	PlayerProvidedReqType_NORMAL_TYPE               PlayerProvidedReqType = 0
	PlayerProvidedReqType_OPEN_TYPE                 PlayerProvidedReqType = 1
	PlayerProvidedReqType_NORMAL_TIME_ONLY_TYPE     PlayerProvidedReqType = 2
	PlayerProvidedReqType_OPEN_TIME_ONLY_TYPE       PlayerProvidedReqType = 3
	PlayerProvidedReqType_RADAR_OPEN_TIME_ONLY_TYPE PlayerProvidedReqType = 4
)

var PlayerProvidedReqType_name = map[int32]string{
	0: "NORMAL_TYPE",
	1: "OPEN_TYPE",
	2: "NORMAL_TIME_ONLY_TYPE",
	3: "OPEN_TIME_ONLY_TYPE",
	4: "RADAR_OPEN_TIME_ONLY_TYPE",
}
var PlayerProvidedReqType_value = map[string]int32{
	"NORMAL_TYPE":               0,
	"OPEN_TYPE":                 1,
	"NORMAL_TIME_ONLY_TYPE":     2,
	"OPEN_TIME_ONLY_TYPE":       3,
	"RADAR_OPEN_TIME_ONLY_TYPE": 4,
}

func (x PlayerProvidedReqType) String() string {
	return proto.EnumName(PlayerProvidedReqType_name, int32(x))
}
func (PlayerProvidedReqType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{1}
}

// 房间状态
type PlayerProvidedReqRoomType int32

const (
	PlayerProvidedReqRoomType_NOT_IN_ROOM    PlayerProvidedReqRoomType = 0
	PlayerProvidedReqRoomType_IN_ROOM_NORMAL PlayerProvidedReqRoomType = 1
)

var PlayerProvidedReqRoomType_name = map[int32]string{
	0: "NOT_IN_ROOM",
	1: "IN_ROOM_NORMAL",
}
var PlayerProvidedReqRoomType_value = map[string]int32{
	"NOT_IN_ROOM":    0,
	"IN_ROOM_NORMAL": 1,
}

func (x PlayerProvidedReqRoomType) String() string {
	return proto.EnumName(PlayerProvidedReqRoomType_name, int32(x))
}
func (PlayerProvidedReqRoomType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{2}
}

// 是否强制请求以下类型
type ForceRequestType int32

const (
	ForceRequestType_Force_Normal      ForceRequestType = 0
	ForceRequestType_Force_Chanel_Push ForceRequestType = 1
)

var ForceRequestType_name = map[int32]string{
	0: "Force_Normal",
	1: "Force_Chanel_Push",
}
var ForceRequestType_value = map[string]int32{
	"Force_Normal":      0,
	"Force_Chanel_Push": 1,
}

func (x ForceRequestType) String() string {
	return proto.EnumName(ForceRequestType_name, int32(x))
}
func (ForceRequestType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{3}
}

type TypePlayerProvidedRsp int32

const (
	TypePlayerProvidedRsp_playerProvided      TypePlayerProvidedRsp = 0
	TypePlayerProvidedRsp_dialogProvided      TypePlayerProvidedRsp = 1
	TypePlayerProvidedRsp_channelPushProvided TypePlayerProvidedRsp = 2
)

var TypePlayerProvidedRsp_name = map[int32]string{
	0: "playerProvided",
	1: "dialogProvided",
	2: "channelPushProvided",
}
var TypePlayerProvidedRsp_value = map[string]int32{
	"playerProvided":      0,
	"dialogProvided":      1,
	"channelPushProvided": 2,
}

func (x TypePlayerProvidedRsp) String() string {
	return proto.EnumName(TypePlayerProvidedRsp_name, int32(x))
}
func (TypePlayerProvidedRsp) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{4}
}

type Settings int32

const (
	Settings_PLAYER_FOUND Settings = 0
	Settings_CITY_TAG     Settings = 1
)

var Settings_name = map[int32]string{
	0: "PLAYER_FOUND",
	1: "CITY_TAG",
}
var Settings_value = map[string]int32{
	"PLAYER_FOUND": 0,
	"CITY_TAG":     1,
}

func (x Settings) String() string {
	return proto.EnumName(Settings_name, int32(x))
}
func (Settings) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{5}
}

type PlayerLoadMore struct {
	LastPage             uint32   `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	LastCount            uint32   `protobuf:"varint,2,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	LastTime             int64    `protobuf:"varint,3,opt,name=last_time,json=lastTime,proto3" json:"last_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayerLoadMore) Reset()         { *m = PlayerLoadMore{} }
func (m *PlayerLoadMore) String() string { return proto.CompactTextString(m) }
func (*PlayerLoadMore) ProtoMessage()    {}
func (*PlayerLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{0}
}
func (m *PlayerLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerLoadMore.Unmarshal(m, b)
}
func (m *PlayerLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerLoadMore.Marshal(b, m, deterministic)
}
func (dst *PlayerLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerLoadMore.Merge(dst, src)
}
func (m *PlayerLoadMore) XXX_Size() int {
	return xxx_messageInfo_PlayerLoadMore.Size(m)
}
func (m *PlayerLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerLoadMore proto.InternalMessageInfo

func (m *PlayerLoadMore) GetLastPage() uint32 {
	if m != nil {
		return m.LastPage
	}
	return 0
}

func (m *PlayerLoadMore) GetLastCount() uint32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

func (m *PlayerLoadMore) GetLastTime() int64 {
	if m != nil {
		return m.LastTime
	}
	return 0
}

type PlayerReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Gender               int32           `protobuf:"varint,2,opt,name=gender,proto3" json:"gender,omitempty"`
	Count                uint32          `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	Loadmore             *PlayerLoadMore `protobuf:"bytes,4,opt,name=loadmore,proto3" json:"loadmore,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PlayerReq) Reset()         { *m = PlayerReq{} }
func (m *PlayerReq) String() string { return proto.CompactTextString(m) }
func (*PlayerReq) ProtoMessage()    {}
func (*PlayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{1}
}
func (m *PlayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerReq.Unmarshal(m, b)
}
func (m *PlayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerReq.Marshal(b, m, deterministic)
}
func (dst *PlayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerReq.Merge(dst, src)
}
func (m *PlayerReq) XXX_Size() int {
	return xxx_messageInfo_PlayerReq.Size(m)
}
func (m *PlayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerReq proto.InternalMessageInfo

func (m *PlayerReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PlayerReq) GetGender() int32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *PlayerReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *PlayerReq) GetLoadmore() *PlayerLoadMore {
	if m != nil {
		return m.Loadmore
	}
	return nil
}

type PlayerResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PlayInfo             []*PlayerInfo   `protobuf:"bytes,2,rep,name=play_info,json=playInfo,proto3" json:"play_info,omitempty"`
	Loadmore             *PlayerLoadMore `protobuf:"bytes,3,opt,name=loadmore,proto3" json:"loadmore,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PlayerResp) Reset()         { *m = PlayerResp{} }
func (m *PlayerResp) String() string { return proto.CompactTextString(m) }
func (*PlayerResp) ProtoMessage()    {}
func (*PlayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{2}
}
func (m *PlayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerResp.Unmarshal(m, b)
}
func (m *PlayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerResp.Marshal(b, m, deterministic)
}
func (dst *PlayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerResp.Merge(dst, src)
}
func (m *PlayerResp) XXX_Size() int {
	return xxx_messageInfo_PlayerResp.Size(m)
}
func (m *PlayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerResp proto.InternalMessageInfo

func (m *PlayerResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PlayerResp) GetPlayInfo() []*PlayerInfo {
	if m != nil {
		return m.PlayInfo
	}
	return nil
}

func (m *PlayerResp) GetLoadmore() *PlayerLoadMore {
	if m != nil {
		return m.Loadmore
	}
	return nil
}

type PlayerPostInfo struct {
	PostId               string            `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostType             ugc.PostType      `protobuf:"varint,2,opt,name=post_type,json=postType,proto3,enum=ga.ugc.PostType" json:"post_type,omitempty"`
	Attachments          []*ugc.Attachment `protobuf:"bytes,3,rep,name=attachments,proto3" json:"attachments,omitempty"`
	PostTime             uint64            `protobuf:"varint,4,opt,name=post_time,json=postTime,proto3" json:"post_time,omitempty"`
	AttitudeCount        uint32            `protobuf:"varint,5,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	ShareCount           uint32            `protobuf:"varint,6,opt,name=share_count,json=shareCount,proto3" json:"share_count,omitempty"`
	CommentCount         uint32            `protobuf:"varint,7,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	Content              string            `protobuf:"bytes,8,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PlayerPostInfo) Reset()         { *m = PlayerPostInfo{} }
func (m *PlayerPostInfo) String() string { return proto.CompactTextString(m) }
func (*PlayerPostInfo) ProtoMessage()    {}
func (*PlayerPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{3}
}
func (m *PlayerPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerPostInfo.Unmarshal(m, b)
}
func (m *PlayerPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerPostInfo.Marshal(b, m, deterministic)
}
func (dst *PlayerPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerPostInfo.Merge(dst, src)
}
func (m *PlayerPostInfo) XXX_Size() int {
	return xxx_messageInfo_PlayerPostInfo.Size(m)
}
func (m *PlayerPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerPostInfo proto.InternalMessageInfo

func (m *PlayerPostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PlayerPostInfo) GetPostType() ugc.PostType {
	if m != nil {
		return m.PostType
	}
	return ugc.PostType_POST_TYPE_NONE
}

func (m *PlayerPostInfo) GetAttachments() []*ugc.Attachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *PlayerPostInfo) GetPostTime() uint64 {
	if m != nil {
		return m.PostTime
	}
	return 0
}

func (m *PlayerPostInfo) GetAttitudeCount() uint32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

func (m *PlayerPostInfo) GetShareCount() uint32 {
	if m != nil {
		return m.ShareCount
	}
	return 0
}

func (m *PlayerPostInfo) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *PlayerPostInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type PlayerInfo struct {
	PostInfos            []*PlayerPostInfo `protobuf:"bytes,1,rep,name=post_infos,json=postInfos,proto3" json:"post_infos,omitempty"`
	Uid                  uint32            `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string            `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Gender               int32             `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	CityTab              string            `protobuf:"bytes,5,opt,name=city_tab,json=cityTab,proto3" json:"city_tab,omitempty"`
	RecommendNote        string            `protobuf:"bytes,6,opt,name=recommend_note,json=recommendNote,proto3" json:"recommend_note,omitempty"`
	Nickname             string            `protobuf:"bytes,7,opt,name=nickname,proto3" json:"nickname,omitempty"`
	RecommendTextList    []string          `protobuf:"bytes,8,rep,name=recommend_text_list,json=recommendTextList,proto3" json:"recommend_text_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PlayerInfo) Reset()         { *m = PlayerInfo{} }
func (m *PlayerInfo) String() string { return proto.CompactTextString(m) }
func (*PlayerInfo) ProtoMessage()    {}
func (*PlayerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{4}
}
func (m *PlayerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerInfo.Unmarshal(m, b)
}
func (m *PlayerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerInfo.Marshal(b, m, deterministic)
}
func (dst *PlayerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerInfo.Merge(dst, src)
}
func (m *PlayerInfo) XXX_Size() int {
	return xxx_messageInfo_PlayerInfo.Size(m)
}
func (m *PlayerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerInfo proto.InternalMessageInfo

func (m *PlayerInfo) GetPostInfos() []*PlayerPostInfo {
	if m != nil {
		return m.PostInfos
	}
	return nil
}

func (m *PlayerInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PlayerInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *PlayerInfo) GetGender() int32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *PlayerInfo) GetCityTab() string {
	if m != nil {
		return m.CityTab
	}
	return ""
}

func (m *PlayerInfo) GetRecommendNote() string {
	if m != nil {
		return m.RecommendNote
	}
	return ""
}

func (m *PlayerInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *PlayerInfo) GetRecommendTextList() []string {
	if m != nil {
		return m.RecommendTextList
	}
	return nil
}

type PostsReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Account              string       `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PostsReq) Reset()         { *m = PostsReq{} }
func (m *PostsReq) String() string { return proto.CompactTextString(m) }
func (*PostsReq) ProtoMessage()    {}
func (*PostsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{5}
}
func (m *PostsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostsReq.Unmarshal(m, b)
}
func (m *PostsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostsReq.Marshal(b, m, deterministic)
}
func (dst *PostsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostsReq.Merge(dst, src)
}
func (m *PostsReq) XXX_Size() int {
	return xxx_messageInfo_PostsReq.Size(m)
}
func (m *PostsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PostsReq.DiscardUnknown(m)
}

var xxx_messageInfo_PostsReq proto.InternalMessageInfo

func (m *PostsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PostsReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type PostsRsp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	FeedInfos            []*ugc.Feed   `protobuf:"bytes,2,rep,name=Feed_infos,json=FeedInfos,proto3" json:"Feed_infos,omitempty"`
	Uid                  uint32        `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string        `protobuf:"bytes,4,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PostsRsp) Reset()         { *m = PostsRsp{} }
func (m *PostsRsp) String() string { return proto.CompactTextString(m) }
func (*PostsRsp) ProtoMessage()    {}
func (*PostsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{6}
}
func (m *PostsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostsRsp.Unmarshal(m, b)
}
func (m *PostsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostsRsp.Marshal(b, m, deterministic)
}
func (dst *PostsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostsRsp.Merge(dst, src)
}
func (m *PostsRsp) XXX_Size() int {
	return xxx_messageInfo_PostsRsp.Size(m)
}
func (m *PostsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PostsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PostsRsp proto.InternalMessageInfo

func (m *PostsRsp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PostsRsp) GetFeedInfos() []*ugc.Feed {
	if m != nil {
		return m.FeedInfos
	}
	return nil
}

func (m *PostsRsp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PostsRsp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type NewPostsReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uids                 []uint32     `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *NewPostsReq) Reset()         { *m = NewPostsReq{} }
func (m *NewPostsReq) String() string { return proto.CompactTextString(m) }
func (*NewPostsReq) ProtoMessage()    {}
func (*NewPostsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{7}
}
func (m *NewPostsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPostsReq.Unmarshal(m, b)
}
func (m *NewPostsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPostsReq.Marshal(b, m, deterministic)
}
func (dst *NewPostsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPostsReq.Merge(dst, src)
}
func (m *NewPostsReq) XXX_Size() int {
	return xxx_messageInfo_NewPostsReq.Size(m)
}
func (m *NewPostsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPostsReq.DiscardUnknown(m)
}

var xxx_messageInfo_NewPostsReq proto.InternalMessageInfo

func (m *NewPostsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *NewPostsReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type NewPostInfo struct {
	PostInfos            *PlayerPostInfo `protobuf:"bytes,1,opt,name=post_infos,json=postInfos,proto3" json:"post_infos,omitempty"`
	Uid                  uint32          `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *NewPostInfo) Reset()         { *m = NewPostInfo{} }
func (m *NewPostInfo) String() string { return proto.CompactTextString(m) }
func (*NewPostInfo) ProtoMessage()    {}
func (*NewPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{8}
}
func (m *NewPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPostInfo.Unmarshal(m, b)
}
func (m *NewPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPostInfo.Marshal(b, m, deterministic)
}
func (dst *NewPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPostInfo.Merge(dst, src)
}
func (m *NewPostInfo) XXX_Size() int {
	return xxx_messageInfo_NewPostInfo.Size(m)
}
func (m *NewPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NewPostInfo proto.InternalMessageInfo

func (m *NewPostInfo) GetPostInfos() *PlayerPostInfo {
	if m != nil {
		return m.PostInfos
	}
	return nil
}

func (m *NewPostInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type NewPostsRsp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	NewPostInfo          []*NewPostInfo `protobuf:"bytes,2,rep,name=new_post_info,json=newPostInfo,proto3" json:"new_post_info,omitempty"`
	Text                 string         `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *NewPostsRsp) Reset()         { *m = NewPostsRsp{} }
func (m *NewPostsRsp) String() string { return proto.CompactTextString(m) }
func (*NewPostsRsp) ProtoMessage()    {}
func (*NewPostsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{9}
}
func (m *NewPostsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPostsRsp.Unmarshal(m, b)
}
func (m *NewPostsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPostsRsp.Marshal(b, m, deterministic)
}
func (dst *NewPostsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPostsRsp.Merge(dst, src)
}
func (m *NewPostsRsp) XXX_Size() int {
	return xxx_messageInfo_NewPostsRsp.Size(m)
}
func (m *NewPostsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPostsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_NewPostsRsp proto.InternalMessageInfo

func (m *NewPostsRsp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *NewPostsRsp) GetNewPostInfo() []*NewPostInfo {
	if m != nil {
		return m.NewPostInfo
	}
	return nil
}

func (m *NewPostsRsp) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

// 游戏段位信息
type GameLevelInfo struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameLevel            map[string]uint32 `protobuf:"bytes,2,rep,name=game_level,json=gameLevel,proto3" json:"game_level,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GameLevelInfo) Reset()         { *m = GameLevelInfo{} }
func (m *GameLevelInfo) String() string { return proto.CompactTextString(m) }
func (*GameLevelInfo) ProtoMessage()    {}
func (*GameLevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{10}
}
func (m *GameLevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameLevelInfo.Unmarshal(m, b)
}
func (m *GameLevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameLevelInfo.Marshal(b, m, deterministic)
}
func (dst *GameLevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameLevelInfo.Merge(dst, src)
}
func (m *GameLevelInfo) XXX_Size() int {
	return xxx_messageInfo_GameLevelInfo.Size(m)
}
func (m *GameLevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameLevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameLevelInfo proto.InternalMessageInfo

func (m *GameLevelInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameLevelInfo) GetGameLevel() map[string]uint32 {
	if m != nil {
		return m.GameLevel
	}
	return nil
}

type PlayerProvidedReq struct {
	BaseReq              *app.BaseReq              `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32                    `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	PageNo               PageNoSettings            `protobuf:"varint,3,opt,name=page_no,json=pageNo,proto3,enum=ga.playerlogic.PageNoSettings" json:"page_no,omitempty"`
	Type                 PlayerProvidedReqType     `protobuf:"varint,4,opt,name=type,proto3,enum=ga.playerlogic.PlayerProvidedReqType" json:"type,omitempty"`
	RoomType             PlayerProvidedReqRoomType `protobuf:"varint,5,opt,name=room_type,json=roomType,proto3,enum=ga.playerlogic.PlayerProvidedReqRoomType" json:"room_type,omitempty"`
	GetCount             uint32                    `protobuf:"varint,6,opt,name=get_count,json=getCount,proto3" json:"get_count,omitempty"`
	NeverIntoChannel     bool                      `protobuf:"varint,7,opt,name=never_into_channel,json=neverIntoChannel,proto3" json:"never_into_channel,omitempty"`
	ChannelPackageId     string                    `protobuf:"bytes,8,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	IsPlayerProvideOpen  bool                      `protobuf:"varint,9,opt,name=is_player_provide_open,json=isPlayerProvideOpen,proto3" json:"is_player_provide_open,omitempty"`
	ForceType            ForceRequestType          `protobuf:"varint,10,opt,name=force_type,json=forceType,proto3,enum=ga.playerlogic.ForceRequestType" json:"force_type,omitempty"`
	InRoomDuration       uint32                    `protobuf:"varint,11,opt,name=in_room_duration,json=inRoomDuration,proto3" json:"in_room_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *PlayerProvidedReq) Reset()         { *m = PlayerProvidedReq{} }
func (m *PlayerProvidedReq) String() string { return proto.CompactTextString(m) }
func (*PlayerProvidedReq) ProtoMessage()    {}
func (*PlayerProvidedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{11}
}
func (m *PlayerProvidedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerProvidedReq.Unmarshal(m, b)
}
func (m *PlayerProvidedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerProvidedReq.Marshal(b, m, deterministic)
}
func (dst *PlayerProvidedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerProvidedReq.Merge(dst, src)
}
func (m *PlayerProvidedReq) XXX_Size() int {
	return xxx_messageInfo_PlayerProvidedReq.Size(m)
}
func (m *PlayerProvidedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerProvidedReq.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerProvidedReq proto.InternalMessageInfo

func (m *PlayerProvidedReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PlayerProvidedReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PlayerProvidedReq) GetPageNo() PageNoSettings {
	if m != nil {
		return m.PageNo
	}
	return PageNoSettings_NORMAL
}

func (m *PlayerProvidedReq) GetType() PlayerProvidedReqType {
	if m != nil {
		return m.Type
	}
	return PlayerProvidedReqType_NORMAL_TYPE
}

func (m *PlayerProvidedReq) GetRoomType() PlayerProvidedReqRoomType {
	if m != nil {
		return m.RoomType
	}
	return PlayerProvidedReqRoomType_NOT_IN_ROOM
}

func (m *PlayerProvidedReq) GetGetCount() uint32 {
	if m != nil {
		return m.GetCount
	}
	return 0
}

func (m *PlayerProvidedReq) GetNeverIntoChannel() bool {
	if m != nil {
		return m.NeverIntoChannel
	}
	return false
}

func (m *PlayerProvidedReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *PlayerProvidedReq) GetIsPlayerProvideOpen() bool {
	if m != nil {
		return m.IsPlayerProvideOpen
	}
	return false
}

func (m *PlayerProvidedReq) GetForceType() ForceRequestType {
	if m != nil {
		return m.ForceType
	}
	return ForceRequestType_Force_Normal
}

func (m *PlayerProvidedReq) GetInRoomDuration() uint32 {
	if m != nil {
		return m.InRoomDuration
	}
	return 0
}

type ClosePopUpReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ClosePopUpReq) Reset()         { *m = ClosePopUpReq{} }
func (m *ClosePopUpReq) String() string { return proto.CompactTextString(m) }
func (*ClosePopUpReq) ProtoMessage()    {}
func (*ClosePopUpReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{12}
}
func (m *ClosePopUpReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClosePopUpReq.Unmarshal(m, b)
}
func (m *ClosePopUpReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClosePopUpReq.Marshal(b, m, deterministic)
}
func (dst *ClosePopUpReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClosePopUpReq.Merge(dst, src)
}
func (m *ClosePopUpReq) XXX_Size() int {
	return xxx_messageInfo_ClosePopUpReq.Size(m)
}
func (m *ClosePopUpReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClosePopUpReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClosePopUpReq proto.InternalMessageInfo

func (m *ClosePopUpReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type ClosePopUpResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ClosePopUpResp) Reset()         { *m = ClosePopUpResp{} }
func (m *ClosePopUpResp) String() string { return proto.CompactTextString(m) }
func (*ClosePopUpResp) ProtoMessage()    {}
func (*ClosePopUpResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{13}
}
func (m *ClosePopUpResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClosePopUpResp.Unmarshal(m, b)
}
func (m *ClosePopUpResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClosePopUpResp.Marshal(b, m, deterministic)
}
func (dst *ClosePopUpResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClosePopUpResp.Merge(dst, src)
}
func (m *ClosePopUpResp) XXX_Size() int {
	return xxx_messageInfo_ClosePopUpResp.Size(m)
}
func (m *ClosePopUpResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClosePopUpResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClosePopUpResp proto.InternalMessageInfo

func (m *ClosePopUpResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type PlayerProvidedRsp struct {
	BaseResp             *app.BaseResp                  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Uid                  uint32                         `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string                         `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Gender               int32                          `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	Nickname             string                         `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname,omitempty"`
	City                 string                         `protobuf:"bytes,6,opt,name=city,proto3" json:"city,omitempty"`
	RecommendText        string                         `protobuf:"bytes,7,opt,name=recommend_text,json=recommendText,proto3" json:"recommend_text,omitempty"`
	RecommendTextList    []string                       `protobuf:"bytes,8,rep,name=recommend_text_list,json=recommendTextList,proto3" json:"recommend_text_list,omitempty"`
	FeedInfos            []*ugc.Feed                    `protobuf:"bytes,9,rep,name=Feed_infos,json=FeedInfos,proto3" json:"Feed_infos,omitempty"`
	GameInfoList         []*GameLevelInfo               `protobuf:"bytes,10,rep,name=game_info_list,json=gameInfoList,proto3" json:"game_info_list,omitempty"`
	QueryInterval        int64                          `protobuf:"varint,11,opt,name=query_interval,json=queryInterval,proto3" json:"query_interval,omitempty"`
	TypeResp             TypePlayerProvidedRsp          `protobuf:"varint,12,opt,name=typeResp,proto3,enum=ga.playerlogic.TypePlayerProvidedRsp" json:"typeResp,omitempty"`
	DialogInfo           *topic_channel.GetDialogV2Resp `protobuf:"bytes,13,opt,name=dialog_info,json=dialogInfo,proto3" json:"dialog_info,omitempty"`
	IsChannelPushClose   bool                           `protobuf:"varint,14,opt,name=is_channel_push_close,json=isChannelPushClose,proto3" json:"is_channel_push_close,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *PlayerProvidedRsp) Reset()         { *m = PlayerProvidedRsp{} }
func (m *PlayerProvidedRsp) String() string { return proto.CompactTextString(m) }
func (*PlayerProvidedRsp) ProtoMessage()    {}
func (*PlayerProvidedRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{14}
}
func (m *PlayerProvidedRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerProvidedRsp.Unmarshal(m, b)
}
func (m *PlayerProvidedRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerProvidedRsp.Marshal(b, m, deterministic)
}
func (dst *PlayerProvidedRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerProvidedRsp.Merge(dst, src)
}
func (m *PlayerProvidedRsp) XXX_Size() int {
	return xxx_messageInfo_PlayerProvidedRsp.Size(m)
}
func (m *PlayerProvidedRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerProvidedRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerProvidedRsp proto.InternalMessageInfo

func (m *PlayerProvidedRsp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PlayerProvidedRsp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PlayerProvidedRsp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *PlayerProvidedRsp) GetGender() int32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *PlayerProvidedRsp) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *PlayerProvidedRsp) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *PlayerProvidedRsp) GetRecommendText() string {
	if m != nil {
		return m.RecommendText
	}
	return ""
}

func (m *PlayerProvidedRsp) GetRecommendTextList() []string {
	if m != nil {
		return m.RecommendTextList
	}
	return nil
}

func (m *PlayerProvidedRsp) GetFeedInfos() []*ugc.Feed {
	if m != nil {
		return m.FeedInfos
	}
	return nil
}

func (m *PlayerProvidedRsp) GetGameInfoList() []*GameLevelInfo {
	if m != nil {
		return m.GameInfoList
	}
	return nil
}

func (m *PlayerProvidedRsp) GetQueryInterval() int64 {
	if m != nil {
		return m.QueryInterval
	}
	return 0
}

func (m *PlayerProvidedRsp) GetTypeResp() TypePlayerProvidedRsp {
	if m != nil {
		return m.TypeResp
	}
	return TypePlayerProvidedRsp_playerProvided
}

func (m *PlayerProvidedRsp) GetDialogInfo() *topic_channel.GetDialogV2Resp {
	if m != nil {
		return m.DialogInfo
	}
	return nil
}

func (m *PlayerProvidedRsp) GetIsChannelPushClose() bool {
	if m != nil {
		return m.IsChannelPushClose
	}
	return false
}

type StrangerCardReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetAccount        string       `protobuf:"bytes,3,opt,name=target_account,json=targetAccount,proto3" json:"target_account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StrangerCardReq) Reset()         { *m = StrangerCardReq{} }
func (m *StrangerCardReq) String() string { return proto.CompactTextString(m) }
func (*StrangerCardReq) ProtoMessage()    {}
func (*StrangerCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{15}
}
func (m *StrangerCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StrangerCardReq.Unmarshal(m, b)
}
func (m *StrangerCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StrangerCardReq.Marshal(b, m, deterministic)
}
func (dst *StrangerCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StrangerCardReq.Merge(dst, src)
}
func (m *StrangerCardReq) XXX_Size() int {
	return xxx_messageInfo_StrangerCardReq.Size(m)
}
func (m *StrangerCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StrangerCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_StrangerCardReq proto.InternalMessageInfo

func (m *StrangerCardReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StrangerCardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StrangerCardReq) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

type StrangerCardResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Uid                  uint32        `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	City                 string        `protobuf:"bytes,3,opt,name=city,proto3" json:"city,omitempty"`
	RecommendText        []string      `protobuf:"bytes,4,rep,name=recommend_text,json=recommendText,proto3" json:"recommend_text,omitempty"`
	TargetAccount        string        `protobuf:"bytes,5,opt,name=target_account,json=targetAccount,proto3" json:"target_account,omitempty"`
	Gender               int32         `protobuf:"varint,6,opt,name=gender,proto3" json:"gender,omitempty"`
	FeedInfos            []*ugc.Feed   `protobuf:"bytes,7,rep,name=Feed_infos,json=FeedInfos,proto3" json:"Feed_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StrangerCardResp) Reset()         { *m = StrangerCardResp{} }
func (m *StrangerCardResp) String() string { return proto.CompactTextString(m) }
func (*StrangerCardResp) ProtoMessage()    {}
func (*StrangerCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{16}
}
func (m *StrangerCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StrangerCardResp.Unmarshal(m, b)
}
func (m *StrangerCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StrangerCardResp.Marshal(b, m, deterministic)
}
func (dst *StrangerCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StrangerCardResp.Merge(dst, src)
}
func (m *StrangerCardResp) XXX_Size() int {
	return xxx_messageInfo_StrangerCardResp.Size(m)
}
func (m *StrangerCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StrangerCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_StrangerCardResp proto.InternalMessageInfo

func (m *StrangerCardResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *StrangerCardResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StrangerCardResp) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *StrangerCardResp) GetRecommendText() []string {
	if m != nil {
		return m.RecommendText
	}
	return nil
}

func (m *StrangerCardResp) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *StrangerCardResp) GetGender() int32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *StrangerCardResp) GetFeedInfos() []*ugc.Feed {
	if m != nil {
		return m.FeedInfos
	}
	return nil
}

type GetPlayerFoundSettingReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uids                 []uint32     `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	Type                 Settings     `protobuf:"varint,3,opt,name=type,proto3,enum=ga.playerlogic.Settings" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPlayerFoundSettingReq) Reset()         { *m = GetPlayerFoundSettingReq{} }
func (m *GetPlayerFoundSettingReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayerFoundSettingReq) ProtoMessage()    {}
func (*GetPlayerFoundSettingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{17}
}
func (m *GetPlayerFoundSettingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayerFoundSettingReq.Unmarshal(m, b)
}
func (m *GetPlayerFoundSettingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayerFoundSettingReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayerFoundSettingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayerFoundSettingReq.Merge(dst, src)
}
func (m *GetPlayerFoundSettingReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayerFoundSettingReq.Size(m)
}
func (m *GetPlayerFoundSettingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayerFoundSettingReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayerFoundSettingReq proto.InternalMessageInfo

func (m *GetPlayerFoundSettingReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPlayerFoundSettingReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *GetPlayerFoundSettingReq) GetType() Settings {
	if m != nil {
		return m.Type
	}
	return Settings_PLAYER_FOUND
}

type UserPlayerFoundSetting struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	On                   bool     `protobuf:"varint,2,opt,name=on,proto3" json:"on,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPlayerFoundSetting) Reset()         { *m = UserPlayerFoundSetting{} }
func (m *UserPlayerFoundSetting) String() string { return proto.CompactTextString(m) }
func (*UserPlayerFoundSetting) ProtoMessage()    {}
func (*UserPlayerFoundSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{18}
}
func (m *UserPlayerFoundSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPlayerFoundSetting.Unmarshal(m, b)
}
func (m *UserPlayerFoundSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPlayerFoundSetting.Marshal(b, m, deterministic)
}
func (dst *UserPlayerFoundSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPlayerFoundSetting.Merge(dst, src)
}
func (m *UserPlayerFoundSetting) XXX_Size() int {
	return xxx_messageInfo_UserPlayerFoundSetting.Size(m)
}
func (m *UserPlayerFoundSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPlayerFoundSetting.DiscardUnknown(m)
}

var xxx_messageInfo_UserPlayerFoundSetting proto.InternalMessageInfo

func (m *UserPlayerFoundSetting) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserPlayerFoundSetting) GetOn() bool {
	if m != nil {
		return m.On
	}
	return false
}

type GetPlayerFoundSettingResp struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Settings             []*UserPlayerFoundSetting `protobuf:"bytes,2,rep,name=settings,proto3" json:"settings,omitempty"`
	Type                 Settings                  `protobuf:"varint,3,opt,name=type,proto3,enum=ga.playerlogic.Settings" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetPlayerFoundSettingResp) Reset()         { *m = GetPlayerFoundSettingResp{} }
func (m *GetPlayerFoundSettingResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayerFoundSettingResp) ProtoMessage()    {}
func (*GetPlayerFoundSettingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{19}
}
func (m *GetPlayerFoundSettingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayerFoundSettingResp.Unmarshal(m, b)
}
func (m *GetPlayerFoundSettingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayerFoundSettingResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayerFoundSettingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayerFoundSettingResp.Merge(dst, src)
}
func (m *GetPlayerFoundSettingResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayerFoundSettingResp.Size(m)
}
func (m *GetPlayerFoundSettingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayerFoundSettingResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayerFoundSettingResp proto.InternalMessageInfo

func (m *GetPlayerFoundSettingResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPlayerFoundSettingResp) GetSettings() []*UserPlayerFoundSetting {
	if m != nil {
		return m.Settings
	}
	return nil
}

func (m *GetPlayerFoundSettingResp) GetType() Settings {
	if m != nil {
		return m.Type
	}
	return Settings_PLAYER_FOUND
}

type UpdatePlayerFoundSettingReq struct {
	BaseReq              *app.BaseReq            `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Setting              *UserPlayerFoundSetting `protobuf:"bytes,2,opt,name=setting,proto3" json:"setting,omitempty"`
	Type                 Settings                `protobuf:"varint,3,opt,name=type,proto3,enum=ga.playerlogic.Settings" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UpdatePlayerFoundSettingReq) Reset()         { *m = UpdatePlayerFoundSettingReq{} }
func (m *UpdatePlayerFoundSettingReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePlayerFoundSettingReq) ProtoMessage()    {}
func (*UpdatePlayerFoundSettingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{20}
}
func (m *UpdatePlayerFoundSettingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePlayerFoundSettingReq.Unmarshal(m, b)
}
func (m *UpdatePlayerFoundSettingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePlayerFoundSettingReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePlayerFoundSettingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePlayerFoundSettingReq.Merge(dst, src)
}
func (m *UpdatePlayerFoundSettingReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePlayerFoundSettingReq.Size(m)
}
func (m *UpdatePlayerFoundSettingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePlayerFoundSettingReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePlayerFoundSettingReq proto.InternalMessageInfo

func (m *UpdatePlayerFoundSettingReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpdatePlayerFoundSettingReq) GetSetting() *UserPlayerFoundSetting {
	if m != nil {
		return m.Setting
	}
	return nil
}

func (m *UpdatePlayerFoundSettingReq) GetType() Settings {
	if m != nil {
		return m.Type
	}
	return Settings_PLAYER_FOUND
}

type UpdatePlayerFoundSettingResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdatePlayerFoundSettingResp) Reset()         { *m = UpdatePlayerFoundSettingResp{} }
func (m *UpdatePlayerFoundSettingResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePlayerFoundSettingResp) ProtoMessage()    {}
func (*UpdatePlayerFoundSettingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{21}
}
func (m *UpdatePlayerFoundSettingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePlayerFoundSettingResp.Unmarshal(m, b)
}
func (m *UpdatePlayerFoundSettingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePlayerFoundSettingResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePlayerFoundSettingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePlayerFoundSettingResp.Merge(dst, src)
}
func (m *UpdatePlayerFoundSettingResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePlayerFoundSettingResp.Size(m)
}
func (m *UpdatePlayerFoundSettingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePlayerFoundSettingResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePlayerFoundSettingResp proto.InternalMessageInfo

func (m *UpdatePlayerFoundSettingResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// kafka produce
type UpdatePlayerFoundSettingEvent struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	On                   bool     `protobuf:"varint,2,opt,name=on,proto3" json:"on,omitempty"`
	ModifyTime           uint64   `protobuf:"varint,3,opt,name=modify_time,json=modifyTime,proto3" json:"modify_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePlayerFoundSettingEvent) Reset()         { *m = UpdatePlayerFoundSettingEvent{} }
func (m *UpdatePlayerFoundSettingEvent) String() string { return proto.CompactTextString(m) }
func (*UpdatePlayerFoundSettingEvent) ProtoMessage()    {}
func (*UpdatePlayerFoundSettingEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_playerlogic__65cbd0a184d19d68, []int{22}
}
func (m *UpdatePlayerFoundSettingEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePlayerFoundSettingEvent.Unmarshal(m, b)
}
func (m *UpdatePlayerFoundSettingEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePlayerFoundSettingEvent.Marshal(b, m, deterministic)
}
func (dst *UpdatePlayerFoundSettingEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePlayerFoundSettingEvent.Merge(dst, src)
}
func (m *UpdatePlayerFoundSettingEvent) XXX_Size() int {
	return xxx_messageInfo_UpdatePlayerFoundSettingEvent.Size(m)
}
func (m *UpdatePlayerFoundSettingEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePlayerFoundSettingEvent.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePlayerFoundSettingEvent proto.InternalMessageInfo

func (m *UpdatePlayerFoundSettingEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdatePlayerFoundSettingEvent) GetOn() bool {
	if m != nil {
		return m.On
	}
	return false
}

func (m *UpdatePlayerFoundSettingEvent) GetModifyTime() uint64 {
	if m != nil {
		return m.ModifyTime
	}
	return 0
}

func init() {
	proto.RegisterType((*PlayerLoadMore)(nil), "ga.playerlogic.PlayerLoadMore")
	proto.RegisterType((*PlayerReq)(nil), "ga.playerlogic.PlayerReq")
	proto.RegisterType((*PlayerResp)(nil), "ga.playerlogic.PlayerResp")
	proto.RegisterType((*PlayerPostInfo)(nil), "ga.playerlogic.PlayerPostInfo")
	proto.RegisterType((*PlayerInfo)(nil), "ga.playerlogic.PlayerInfo")
	proto.RegisterType((*PostsReq)(nil), "ga.playerlogic.PostsReq")
	proto.RegisterType((*PostsRsp)(nil), "ga.playerlogic.PostsRsp")
	proto.RegisterType((*NewPostsReq)(nil), "ga.playerlogic.NewPostsReq")
	proto.RegisterType((*NewPostInfo)(nil), "ga.playerlogic.NewPostInfo")
	proto.RegisterType((*NewPostsRsp)(nil), "ga.playerlogic.NewPostsRsp")
	proto.RegisterType((*GameLevelInfo)(nil), "ga.playerlogic.GameLevelInfo")
	proto.RegisterMapType((map[string]uint32)(nil), "ga.playerlogic.GameLevelInfo.GameLevelEntry")
	proto.RegisterType((*PlayerProvidedReq)(nil), "ga.playerlogic.PlayerProvidedReq")
	proto.RegisterType((*ClosePopUpReq)(nil), "ga.playerlogic.ClosePopUpReq")
	proto.RegisterType((*ClosePopUpResp)(nil), "ga.playerlogic.ClosePopUpResp")
	proto.RegisterType((*PlayerProvidedRsp)(nil), "ga.playerlogic.PlayerProvidedRsp")
	proto.RegisterType((*StrangerCardReq)(nil), "ga.playerlogic.StrangerCardReq")
	proto.RegisterType((*StrangerCardResp)(nil), "ga.playerlogic.StrangerCardResp")
	proto.RegisterType((*GetPlayerFoundSettingReq)(nil), "ga.playerlogic.GetPlayerFoundSettingReq")
	proto.RegisterType((*UserPlayerFoundSetting)(nil), "ga.playerlogic.UserPlayerFoundSetting")
	proto.RegisterType((*GetPlayerFoundSettingResp)(nil), "ga.playerlogic.GetPlayerFoundSettingResp")
	proto.RegisterType((*UpdatePlayerFoundSettingReq)(nil), "ga.playerlogic.UpdatePlayerFoundSettingReq")
	proto.RegisterType((*UpdatePlayerFoundSettingResp)(nil), "ga.playerlogic.UpdatePlayerFoundSettingResp")
	proto.RegisterType((*UpdatePlayerFoundSettingEvent)(nil), "ga.playerlogic.UpdatePlayerFoundSettingEvent")
	proto.RegisterEnum("ga.playerlogic.PageNoSettings", PageNoSettings_name, PageNoSettings_value)
	proto.RegisterEnum("ga.playerlogic.PlayerProvidedReqType", PlayerProvidedReqType_name, PlayerProvidedReqType_value)
	proto.RegisterEnum("ga.playerlogic.PlayerProvidedReqRoomType", PlayerProvidedReqRoomType_name, PlayerProvidedReqRoomType_value)
	proto.RegisterEnum("ga.playerlogic.ForceRequestType", ForceRequestType_name, ForceRequestType_value)
	proto.RegisterEnum("ga.playerlogic.TypePlayerProvidedRsp", TypePlayerProvidedRsp_name, TypePlayerProvidedRsp_value)
	proto.RegisterEnum("ga.playerlogic.Settings", Settings_name, Settings_value)
}

func init() { proto.RegisterFile("playerlogic_.proto", fileDescriptor_playerlogic__65cbd0a184d19d68) }

var fileDescriptor_playerlogic__65cbd0a184d19d68 = []byte{
	// 1734 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0xcd, 0x6f, 0x23, 0x49,
	0x15, 0x4f, 0xdb, 0x4e, 0xdc, 0xfd, 0x1c, 0x7b, 0x7b, 0x6a, 0x36, 0xb3, 0x4e, 0x86, 0x81, 0xc1,
	0x68, 0x56, 0xd9, 0x30, 0x78, 0x44, 0x16, 0x34, 0x30, 0x0b, 0xda, 0xf5, 0x24, 0x4e, 0xa6, 0x45,
	0x62, 0x5b, 0x15, 0x07, 0x31, 0x1c, 0x68, 0x55, 0xba, 0x2b, 0x9d, 0xd6, 0xb4, 0xbb, 0x3a, 0xdd,
	0xe5, 0xec, 0xfa, 0xce, 0x09, 0x09, 0x4e, 0x5c, 0xf7, 0xc2, 0x99, 0x13, 0x12, 0x12, 0x7f, 0x0c,
	0x7f, 0x07, 0x67, 0x54, 0x1f, 0x6d, 0xbb, 0x1d, 0x27, 0x8b, 0x15, 0x2e, 0x56, 0xd5, 0xfb, 0xa8,
	0xf7, 0x7b, 0x1f, 0xf5, 0x5e, 0xb5, 0x01, 0x25, 0x11, 0x99, 0xd0, 0x34, 0x62, 0x41, 0xe8, 0xb9,
	0xed, 0x24, 0x65, 0x9c, 0xa1, 0x46, 0x40, 0xda, 0x73, 0xe4, 0x9d, 0x7a, 0x40, 0xdc, 0x0b, 0x92,
	0x51, 0xc5, 0xde, 0x81, 0x71, 0x90, 0x8b, 0xee, 0x7c, 0xcc, 0x59, 0x12, 0x7a, 0xae, 0x77, 0x45,
	0xe2, 0x98, 0x46, 0x9a, 0xda, 0x0a, 0xa1, 0x31, 0x90, 0xfa, 0x27, 0x8c, 0xf8, 0xa7, 0x2c, 0xa5,
	0xe8, 0x29, 0x58, 0x11, 0xc9, 0xb8, 0x9b, 0x90, 0x80, 0x36, 0x8d, 0xe7, 0xc6, 0x6e, 0x1d, 0x9b,
	0x82, 0x30, 0x20, 0x01, 0x45, 0xcf, 0x00, 0x24, 0xd3, 0x63, 0xe3, 0x98, 0x37, 0x4b, 0x92, 0x2b,
	0xc5, 0x0f, 0x04, 0x61, 0xaa, 0xcb, 0xc3, 0x11, 0x6d, 0x96, 0x9f, 0x1b, 0xbb, 0x65, 0xa5, 0x3b,
	0x0c, 0x47, 0xb4, 0xf5, 0xad, 0x01, 0x96, 0xb2, 0x85, 0xe9, 0x35, 0xfa, 0x14, 0x4c, 0x01, 0xd4,
	0x4d, 0xe9, 0xb5, 0xb4, 0x52, 0xdb, 0xaf, 0xb5, 0x03, 0xd2, 0x7e, 0x4b, 0x32, 0x8a, 0xe9, 0x35,
	0xae, 0x5e, 0xa8, 0x05, 0x7a, 0x02, 0x1b, 0x01, 0x8d, 0x7d, 0x9a, 0x4a, 0x6b, 0xeb, 0x58, 0xef,
	0xd0, 0xc7, 0xb0, 0xae, 0x40, 0x94, 0x25, 0x08, 0xb5, 0x41, 0x6f, 0xc0, 0x8c, 0x18, 0xf1, 0x47,
	0x2c, 0xa5, 0xcd, 0x8a, 0x3c, 0xf5, 0xfb, 0xed, 0x62, 0x88, 0xda, 0x45, 0x77, 0xf1, 0x54, 0xbe,
	0xf5, 0x77, 0x03, 0x20, 0xc7, 0x97, 0x25, 0xe8, 0x33, 0xb0, 0x34, 0xc0, 0x2c, 0xd1, 0x08, 0x37,
	0x67, 0x08, 0xb3, 0x04, 0x9b, 0x17, 0x7a, 0x85, 0x5e, 0x83, 0x25, 0x2c, 0xb8, 0x61, 0x7c, 0xc9,
	0x9a, 0xa5, 0xe7, 0xe5, 0xdd, 0xda, 0xfe, 0xce, 0x72, 0xb3, 0x4e, 0x7c, 0xc9, 0xb0, 0x29, 0xe8,
	0x62, 0x55, 0x80, 0x5b, 0x5e, 0x11, 0xee, 0x3f, 0x4a, 0x79, 0xea, 0x06, 0x2c, 0xe3, 0xf2, 0xb8,
	0x4f, 0xa0, 0x9a, 0xb0, 0x8c, 0xbb, 0xa1, 0x2f, 0x01, 0x5b, 0x78, 0x43, 0x6c, 0x1d, 0x1f, 0xfd,
	0x04, 0x2c, 0xc9, 0xe0, 0x93, 0x84, 0xca, 0x38, 0x36, 0xf6, 0x6d, 0x61, 0x68, 0x1c, 0x78, 0x6d,
	0xa1, 0x3d, 0x9c, 0x24, 0x14, 0x9b, 0x89, 0x5e, 0xa1, 0x9f, 0x41, 0x8d, 0x70, 0x4e, 0xbc, 0xab,
	0x11, 0x8d, 0x79, 0xd6, 0x2c, 0x4b, 0x8f, 0x50, 0xae, 0xd0, 0x99, 0xb2, 0xf0, 0xbc, 0x98, 0x48,
	0xbe, 0x32, 0x22, 0x92, 0x2f, 0x82, 0x5f, 0xd1, 0x47, 0x86, 0x23, 0x8a, 0x5e, 0x40, 0x83, 0x70,
	0x1e, 0xf2, 0xb1, 0x4f, 0x75, 0xf1, 0xac, 0xcb, 0xbc, 0xd5, 0x73, 0xaa, 0x2a, 0xa0, 0x1f, 0x40,
	0x2d, 0xbb, 0x22, 0x69, 0x2e, 0xb3, 0x21, 0x65, 0x40, 0x92, 0x94, 0xc0, 0x8f, 0xa0, 0xee, 0xb1,
	0x91, 0x30, 0xa8, 0x45, 0xaa, 0x52, 0x64, 0x53, 0x13, 0x95, 0x50, 0x13, 0xaa, 0x1e, 0x8b, 0x39,
	0x8d, 0x79, 0xd3, 0x94, 0x71, 0xc8, 0xb7, 0xad, 0x6f, 0x4b, 0x79, 0x8e, 0x65, 0xc0, 0x7e, 0x0d,
	0xa0, 0x02, 0x16, 0x5f, 0xb2, 0xac, 0x69, 0x48, 0x3f, 0xef, 0xc8, 0x40, 0x1e, 0x64, 0x2c, 0x9d,
	0x14, 0xab, 0x0c, 0xd9, 0x50, 0x1e, 0x87, 0xbe, 0xbe, 0x06, 0x62, 0x29, 0x2c, 0x13, 0x6f, 0x56,
	0x97, 0x16, 0xce, 0xb7, 0x73, 0x75, 0x5c, 0x29, 0xd4, 0xf1, 0x36, 0x98, 0x5e, 0xc8, 0x27, 0x2e,
	0x27, 0x17, 0x32, 0x24, 0x02, 0x6c, 0xc8, 0x27, 0x43, 0x72, 0x21, 0x62, 0x96, 0x52, 0xe5, 0x98,
	0xef, 0xc6, 0x8c, 0x53, 0x19, 0x0f, 0x0b, 0xd7, 0xa7, 0xd4, 0x1e, 0xe3, 0x14, 0xed, 0x80, 0x19,
	0x87, 0xde, 0x87, 0x98, 0x8c, 0xa8, 0x8c, 0x86, 0x85, 0xa7, 0x7b, 0xd4, 0x86, 0xc7, 0xb3, 0x23,
	0x38, 0xfd, 0x86, 0xbb, 0x51, 0x98, 0x89, 0xa8, 0x94, 0x77, 0x2d, 0xfc, 0x68, 0xca, 0x1a, 0xd2,
	0x6f, 0xf8, 0x49, 0x98, 0xf1, 0xd6, 0x09, 0x98, 0xc2, 0xd1, 0x6c, 0x95, 0x1b, 0x3a, 0xe7, 0x73,
	0xa9, 0xe0, 0x73, 0xeb, 0x2f, 0x46, 0x7e, 0xdc, 0x6a, 0xf7, 0xe9, 0xc7, 0x00, 0x47, 0x94, 0xfa,
	0x3a, 0x2d, 0xea, 0x42, 0x6d, 0xe6, 0xe5, 0x27, 0x38, 0xd8, 0x12, 0xbf, 0x85, 0x24, 0x94, 0x97,
	0x26, 0xa1, 0x52, 0x04, 0xe4, 0x40, 0xad, 0x47, 0xbf, 0x5e, 0xd9, 0x43, 0x04, 0x95, 0x71, 0xe8,
	0x2b, 0x24, 0x75, 0x2c, 0xd7, 0xad, 0x3f, 0x4c, 0x8f, 0x5a, 0x5a, 0x49, 0xc6, 0x03, 0x2b, 0xa9,
	0xf5, 0x67, 0x63, 0x0e, 0xeb, 0x6a, 0xe1, 0xfb, 0x12, 0xea, 0x31, 0xfd, 0xda, 0x9d, 0xe2, 0xd1,
	0x11, 0x7c, 0xba, 0x08, 0x67, 0x0e, 0x3f, 0xae, 0xc5, 0x73, 0xce, 0x20, 0xa8, 0x88, 0x5a, 0xd1,
	0x25, 0x2c, 0xd7, 0xa2, 0x3b, 0xd6, 0x8f, 0xc9, 0x88, 0x9e, 0xd0, 0x1b, 0x1a, 0x49, 0x29, 0x8d,
	0xd9, 0x98, 0x05, 0xfe, 0x37, 0x00, 0x01, 0x19, 0x51, 0x37, 0x12, 0x32, 0xda, 0xea, 0xcb, 0x45,
	0xab, 0x85, 0x43, 0x66, 0xbb, 0x6e, 0xcc, 0xd3, 0x09, 0xb6, 0x82, 0x7c, 0xbf, 0xf3, 0x2b, 0x68,
	0x14, 0x99, 0xc2, 0xe0, 0x07, 0x3a, 0xd1, 0xad, 0x4d, 0x2c, 0xc5, 0x10, 0xb8, 0x21, 0xd1, 0x98,
	0xea, 0xc0, 0xa9, 0xcd, 0x9b, 0xd2, 0x2f, 0x8c, 0xd6, 0xdf, 0x2a, 0xf0, 0x48, 0x87, 0x3b, 0x65,
	0x37, 0xa1, 0x4f, 0xfd, 0x55, 0x12, 0x7e, 0xfb, 0x62, 0xbf, 0x86, 0xaa, 0x18, 0x88, 0x6e, 0xcc,
	0x64, 0x54, 0x1a, 0x4b, 0x92, 0x4b, 0x02, 0xda, 0x63, 0x67, 0x94, 0xf3, 0x30, 0x0e, 0x32, 0xbc,
	0x91, 0xc8, 0x3d, 0xfa, 0x25, 0x54, 0x64, 0xd7, 0xad, 0x48, 0xad, 0x17, 0x77, 0x94, 0xc4, 0x0c,
	0xa3, 0x6c, 0xc5, 0x52, 0x05, 0x1d, 0x81, 0x95, 0x32, 0x36, 0x52, 0x5d, 0x7b, 0x5d, 0xea, 0x7f,
	0xf6, 0x9d, 0xfa, 0x98, 0xb1, 0x91, 0x6a, 0xe7, 0xa9, 0x5e, 0x89, 0xc6, 0x1c, 0x50, 0x5e, 0x68,
	0xa9, 0x66, 0x40, 0x75, 0xaf, 0x7c, 0x09, 0x28, 0xa6, 0x37, 0x34, 0x75, 0xc3, 0x98, 0xb3, 0xfc,
	0x75, 0x20, 0xfb, 0x88, 0x89, 0x6d, 0xc9, 0x71, 0x62, 0xce, 0x0e, 0x14, 0x5d, 0x48, 0xe7, 0x0f,
	0x88, 0x84, 0x78, 0x1f, 0x44, 0x44, 0x42, 0x5f, 0x37, 0x59, 0x5b, 0x73, 0x06, 0x8a, 0xe1, 0xf8,
	0xe8, 0x73, 0x78, 0x12, 0x66, 0xae, 0x82, 0xeb, 0x26, 0x0a, 0xa3, 0xcb, 0x12, 0x1a, 0x37, 0x2d,
	0x79, 0xfe, 0xe3, 0x30, 0x2b, 0xe0, 0xef, 0x27, 0x34, 0x46, 0x5f, 0x02, 0x5c, 0xb2, 0xd4, 0xa3,
	0xca, 0x6d, 0x90, 0x6e, 0x3f, 0x5f, 0x74, 0xfb, 0x48, 0x48, 0x60, 0x7a, 0x3d, 0xa6, 0x7a, 0x78,
	0x59, 0x52, 0x47, 0xba, 0xbb, 0x0b, 0x76, 0x18, 0xbb, 0x32, 0x72, 0xfe, 0x38, 0x25, 0x3c, 0x64,
	0x71, 0xb3, 0x26, 0xbd, 0x6e, 0x84, 0xb1, 0x08, 0xcf, 0xa1, 0xa6, 0xb6, 0x5e, 0x43, 0xfd, 0x20,
	0x62, 0x19, 0x1d, 0xb0, 0xe4, 0x3c, 0x59, 0xa1, 0x3e, 0x5a, 0x5f, 0x40, 0x63, 0x5e, 0x71, 0xa5,
	0xeb, 0xd9, 0xfa, 0xf7, 0xed, 0xd2, 0x5c, 0xed, 0x7e, 0xff, 0x3f, 0xc6, 0xce, 0xfc, 0xd0, 0x58,
	0x5f, 0x18, 0x1a, 0x08, 0x2a, 0x62, 0x04, 0xe9, 0x69, 0x23, 0xd7, 0xc5, 0x59, 0x24, 0x9b, 0x43,
	0x75, 0x61, 0x16, 0x89, 0x19, 0xb2, 0xea, 0xbc, 0x59, 0xe8, 0xf4, 0xd6, 0xfd, 0x9d, 0xfe, 0x00,
	0x1a, 0xb2, 0xbd, 0x08, 0x61, 0x75, 0x2e, 0x48, 0x85, 0x67, 0xf7, 0xb6, 0x18, 0xbc, 0x29, 0x94,
	0xc4, 0x4a, 0x5a, 0x7c, 0x01, 0x8d, 0xeb, 0x31, 0x4d, 0xc5, 0x63, 0x8d, 0xd3, 0xf4, 0x86, 0x44,
	0xb2, 0x36, 0xca, 0xb8, 0x2e, 0xa9, 0x8e, 0x26, 0xa2, 0x0e, 0x98, 0xa2, 0xfe, 0x44, 0xbc, 0x9b,
	0x9b, 0xcb, 0xaf, 0xae, 0x28, 0xb6, 0x5b, 0x79, 0xc4, 0x53, 0x35, 0xf4, 0x16, 0x6a, 0x7e, 0x48,
	0x22, 0x16, 0xa8, 0x26, 0x5c, 0x97, 0x39, 0xfd, 0xa1, 0x38, 0xa5, 0xf0, 0x12, 0x6f, 0x1f, 0x53,
	0x7e, 0x28, 0xe5, 0x7e, 0xbb, 0x2f, 0x13, 0x0d, 0x4a, 0x4b, 0xf6, 0xd8, 0x9f, 0xc2, 0x56, 0x98,
	0x4d, 0xdf, 0xec, 0xc9, 0x38, 0xbb, 0x72, 0x3d, 0x51, 0x78, 0xcd, 0x86, 0xbc, 0x40, 0x28, 0xcc,
	0xf4, 0xcd, 0x1c, 0x8c, 0xb3, 0x2b, 0x59, 0x92, 0xad, 0x14, 0x3e, 0x3a, 0xe3, 0x29, 0x89, 0x03,
	0x9a, 0x1e, 0x90, 0xf4, 0x81, 0x6d, 0xef, 0x05, 0x34, 0x38, 0x49, 0x45, 0xf7, 0x28, 0xd6, 0x57,
	0x5d, 0x51, 0x3b, 0x7a, 0xae, 0xfe, 0xc7, 0x00, 0xbb, 0x68, 0xf4, 0xa1, 0x15, 0x9d, 0xd7, 0x60,
	0xf9, 0xde, 0x1a, 0xac, 0xc8, 0xba, 0x5a, 0xa8, 0xc1, 0xdb, 0x98, 0xd7, 0x97, 0x60, 0x9e, 0xbb,
	0x19, 0x1b, 0x85, 0x9b, 0x51, 0x2c, 0xc9, 0xea, 0xbd, 0x25, 0xd9, 0xfa, 0xa3, 0x01, 0xcd, 0x63,
	0xca, 0x55, 0x19, 0x1c, 0xb1, 0x71, 0xec, 0xeb, 0x01, 0xf0, 0xc0, 0xe7, 0x05, 0x7a, 0xa9, 0xc7,
	0x86, 0x1a, 0x36, 0xcd, 0xc5, 0xda, 0x9b, 0x8e, 0x19, 0x29, 0xd5, 0x7a, 0x03, 0x4f, 0xce, 0x33,
	0x9a, 0xde, 0x86, 0xb1, 0x64, 0x48, 0x37, 0xa0, 0xc4, 0x62, 0x19, 0x6a, 0x13, 0x97, 0x58, 0xdc,
	0xfa, 0x97, 0x01, 0xdb, 0x77, 0xb8, 0xb0, 0x5a, 0x12, 0xdf, 0x82, 0x99, 0x69, 0x58, 0x7a, 0xf6,
	0x7f, 0xba, 0x08, 0x7b, 0x39, 0x48, 0x3c, 0xd5, 0x5b, 0xd1, 0xed, 0x7f, 0x1a, 0xf0, 0xf4, 0x3c,
	0xf1, 0x09, 0xa7, 0x0f, 0x4b, 0xc0, 0x57, 0x50, 0xd5, 0x08, 0x64, 0x5c, 0xfe, 0x77, 0xe0, 0xb9,
	0xda, 0x8a, 0xb8, 0x1d, 0xf8, 0xde, 0xdd, 0xb0, 0x57, 0x1b, 0x26, 0x17, 0xf0, 0xec, 0xae, 0xa3,
	0xba, 0x37, 0x34, 0xe6, 0xdf, 0x5d, 0x00, 0xe2, 0x9b, 0x6b, 0xc4, 0xfc, 0xf0, 0x72, 0x32, 0xfb,
	0x6c, 0xaf, 0x60, 0x50, 0x24, 0xf1, 0xed, 0xb6, 0xf7, 0x57, 0x03, 0x1a, 0xc5, 0xd7, 0x0d, 0x02,
	0xd8, 0xe8, 0xf5, 0xf1, 0x69, 0xe7, 0xc4, 0x5e, 0x43, 0x35, 0xa8, 0x3a, 0x3d, 0xf7, 0x5d, 0xff,
	0xb4, 0x6b, 0x1b, 0xc8, 0x82, 0x75, 0xa7, 0xe7, 0x3a, 0xa7, 0x76, 0x09, 0x7d, 0x04, 0xb5, 0xc1,
	0xf9, 0x89, 0x73, 0xf6, 0xce, 0x1d, 0xf4, 0xcf, 0x86, 0x76, 0x19, 0xd5, 0xc1, 0xea, 0x1e, 0x3a,
	0x43, 0xb5, 0xad, 0xcc, 0xf1, 0x71, 0xbf, 0x7f, 0x6a, 0xaf, 0xa3, 0x47, 0x50, 0x3f, 0xed, 0x0c,
	0x0f, 0xde, 0x39, 0xbd, 0x63, 0x45, 0xda, 0x10, 0x76, 0xfa, 0xc3, 0x77, 0x5d, 0x7c, 0x66, 0x57,
	0x51, 0x03, 0xe0, 0xf4, 0xfc, 0xcc, 0x39, 0x70, 0x7b, 0xdd, 0xb3, 0xa1, 0x6d, 0xee, 0xfd, 0xc9,
	0x80, 0xad, 0xa5, 0xcf, 0x27, 0x71, 0xb2, 0x42, 0xe7, 0x0e, 0xdf, 0x0f, 0xba, 0xf6, 0x9a, 0xb0,
	0xdc, 0x1f, 0x74, 0x7b, 0x6a, 0x6b, 0xa0, 0x6d, 0xd8, 0xca, 0xf9, 0xce, 0x69, 0xd7, 0xed, 0xf7,
	0x4e, 0xde, 0x2b, 0x56, 0x09, 0x7d, 0x02, 0x8f, 0x95, 0x64, 0x91, 0x51, 0x46, 0xcf, 0x60, 0x1b,
	0x77, 0x0e, 0x3b, 0xd8, 0x5d, 0xc6, 0xae, 0xec, 0x7d, 0x05, 0xdb, 0x77, 0x3e, 0xc5, 0x14, 0x9e,
	0xa1, 0xeb, 0xf4, 0x94, 0x5b, 0x6b, 0x08, 0x41, 0x43, 0x6f, 0x5c, 0x1d, 0x46, 0x63, 0xef, 0x0b,
	0xb0, 0x17, 0x5f, 0x35, 0xc8, 0x86, 0x4d, 0x49, 0x73, 0x7b, 0x2c, 0x1d, 0x91, 0xc8, 0x5e, 0x43,
	0x5b, 0xf0, 0x48, 0x51, 0x44, 0xdf, 0xa7, 0x91, 0x2b, 0xfa, 0xbe, 0x6d, 0xec, 0xfd, 0x0e, 0xb6,
	0x96, 0x8e, 0x23, 0x61, 0x29, 0x29, 0x10, 0x95, 0x75, 0x35, 0x62, 0xa6, 0x34, 0x43, 0xf8, 0xed,
	0xcd, 0x26, 0xc9, 0x94, 0x51, 0xda, 0xdb, 0x03, 0x73, 0x9a, 0x75, 0x1b, 0x36, 0x07, 0x27, 0x9d,
	0xf7, 0x5d, 0xec, 0x1e, 0xf5, 0xcf, 0x7b, 0x87, 0xf6, 0x1a, 0xda, 0x04, 0xf3, 0xc0, 0x19, 0xbe,
	0x77, 0x87, 0x9d, 0x63, 0xdb, 0x78, 0x7b, 0x08, 0x4d, 0x8f, 0x8d, 0xda, 0x93, 0x70, 0xc2, 0xc6,
	0xa2, 0x5e, 0x47, 0xcc, 0xa7, 0x91, 0xfa, 0xa3, 0xe9, 0xf7, 0xbb, 0x01, 0x8b, 0x48, 0x1c, 0xb4,
	0x7f, 0xbe, 0xcf, 0x79, 0xdb, 0x63, 0xa3, 0x57, 0x92, 0xec, 0xb1, 0xe8, 0x15, 0x49, 0x92, 0x57,
	0x73, 0x97, 0xe5, 0x62, 0x43, 0x72, 0x3e, 0xff, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x6d, 0xb8,
	0xc1, 0xb3, 0xf0, 0x12, 0x00, 0x00,
}
