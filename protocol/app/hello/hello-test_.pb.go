// Code generated by protoc-gen-go. DO NOT EDIT.
// source: hello-test_.proto

package hello // import "golang.52tt.com/protocol/app/hello"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// say hello
type SayHelloReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Text                 string       `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SayHelloReq) Reset()         { *m = SayHelloReq{} }
func (m *SayHelloReq) String() string { return proto.CompactTextString(m) }
func (*SayHelloReq) ProtoMessage()    {}
func (*SayHelloReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hello_test__78a154c671fc4368, []int{0}
}
func (m *SayHelloReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SayHelloReq.Unmarshal(m, b)
}
func (m *SayHelloReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SayHelloReq.Marshal(b, m, deterministic)
}
func (dst *SayHelloReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SayHelloReq.Merge(dst, src)
}
func (m *SayHelloReq) XXX_Size() int {
	return xxx_messageInfo_SayHelloReq.Size(m)
}
func (m *SayHelloReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SayHelloReq.DiscardUnknown(m)
}

var xxx_messageInfo_SayHelloReq proto.InternalMessageInfo

func (m *SayHelloReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SayHelloReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type SayHelloResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Text                 string        `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SayHelloResp) Reset()         { *m = SayHelloResp{} }
func (m *SayHelloResp) String() string { return proto.CompactTextString(m) }
func (*SayHelloResp) ProtoMessage()    {}
func (*SayHelloResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hello_test__78a154c671fc4368, []int{1}
}
func (m *SayHelloResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SayHelloResp.Unmarshal(m, b)
}
func (m *SayHelloResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SayHelloResp.Marshal(b, m, deterministic)
}
func (dst *SayHelloResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SayHelloResp.Merge(dst, src)
}
func (m *SayHelloResp) XXX_Size() int {
	return xxx_messageInfo_SayHelloResp.Size(m)
}
func (m *SayHelloResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SayHelloResp.DiscardUnknown(m)
}

var xxx_messageInfo_SayHelloResp proto.InternalMessageInfo

func (m *SayHelloResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SayHelloResp) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func init() {
	proto.RegisterType((*SayHelloReq)(nil), "ga.hello.SayHelloReq")
	proto.RegisterType((*SayHelloResp)(nil), "ga.hello.SayHelloResp")
}

func init() { proto.RegisterFile("hello-test_.proto", fileDescriptor_hello_test__78a154c671fc4368) }

var fileDescriptor_hello_test__78a154c671fc4368 = []byte{
	// 209 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x8f, 0xb1, 0x6b, 0x84, 0x30,
	0x14, 0x87, 0xb1, 0x94, 0x56, 0xa3, 0x1d, 0x9a, 0x49, 0x3a, 0x89, 0x43, 0xb1, 0x43, 0x23, 0x58,
	0xba, 0x76, 0x70, 0x6a, 0x87, 0x2e, 0xe9, 0xd6, 0x45, 0x5e, 0xec, 0x23, 0x3d, 0x88, 0xf7, 0xa2,
	0xc9, 0xc1, 0xf9, 0xdf, 0x1f, 0x46, 0xc1, 0xe5, 0xb6, 0x1f, 0xdf, 0x47, 0x3e, 0xf2, 0xd8, 0xe3,
	0x3f, 0x1a, 0x43, 0xaf, 0x1e, 0x9d, 0xef, 0x84, 0x9d, 0xc8, 0x13, 0x8f, 0x35, 0x88, 0x40, 0x9f,
	0x1e, 0x34, 0x74, 0x0a, 0x1c, 0xae, 0xa2, 0xfc, 0x62, 0xe9, 0x0f, 0xcc, 0x9f, 0x8b, 0x92, 0x38,
	0xf2, 0x67, 0x16, 0x2f, 0xb2, 0x9b, 0x70, 0xcc, 0xa3, 0x22, 0xaa, 0xd2, 0x26, 0x15, 0x1a, 0x44,
	0x0b, 0x0e, 0x25, 0x8e, 0xf2, 0x5e, 0xad, 0x83, 0x73, 0x76, 0xeb, 0xf1, 0xec, 0xf3, 0x9b, 0x22,
	0xaa, 0x12, 0x19, 0x76, 0xf9, 0xcd, 0xb2, 0x3d, 0xe5, 0x2c, 0x7f, 0x61, 0xc9, 0xd6, 0x72, 0x76,
	0x8b, 0x65, 0x7b, 0xcc, 0x59, 0x19, 0xab, 0x6d, 0x5d, 0xcb, 0xb5, 0x1f, 0x2c, 0xef, 0x69, 0x10,
	0xf3, 0x61, 0xa6, 0xd3, 0xf2, 0x6c, 0xa0, 0x3f, 0x34, 0xeb, 0xaf, 0x7f, 0x4b, 0x4d, 0x06, 0x8e,
	0x5a, 0xbc, 0x37, 0xde, 0x8b, 0x9e, 0x86, 0x3a, 0xe0, 0x9e, 0x4c, 0x0d, 0xd6, 0xd6, 0xe1, 0x50,
	0x75, 0x17, 0xd8, 0xdb, 0x25, 0x00, 0x00, 0xff, 0xff, 0x7d, 0x6c, 0x69, 0x14, 0x0e, 0x01, 0x00,
	0x00,
}
