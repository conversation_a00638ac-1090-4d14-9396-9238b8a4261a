// Code generated by protoc-gen-go. DO NOT EDIT.
// source: game-tmp-channel_.proto

package game_tmp_channel // import "golang.52tt.com/protocol/app/game_tmp_channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ButtonInfo_ButtonStatus int32

const (
	ButtonInfo_Hide        ButtonInfo_ButtonStatus = 0
	ButtonInfo_Unclickable ButtonInfo_ButtonStatus = 1
	ButtonInfo_Clickable   ButtonInfo_ButtonStatus = 2
)

var ButtonInfo_ButtonStatus_name = map[int32]string{
	0: "Hide",
	1: "Unclickable",
	2: "Clickable",
}
var ButtonInfo_ButtonStatus_value = map[string]int32{
	"Hide":        0,
	"Unclickable": 1,
	"Clickable":   2,
}

func (x ButtonInfo_ButtonStatus) String() string {
	return proto.EnumName(ButtonInfo_ButtonStatus_name, int32(x))
}
func (ButtonInfo_ButtonStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_tmp_channel__e36e1600fe0bff55, []int{0, 0}
}

// 赛事按钮信息
type ButtonInfo struct {
	ButtonStatus         ButtonInfo_ButtonStatus `protobuf:"varint,1,opt,name=button_status,json=buttonStatus,proto3,enum=ga.game_tmp_channel.ButtonInfo_ButtonStatus" json:"button_status,omitempty"`
	Text                 string                  `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Url                  string                  `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ButtonInfo) Reset()         { *m = ButtonInfo{} }
func (m *ButtonInfo) String() string { return proto.CompactTextString(m) }
func (*ButtonInfo) ProtoMessage()    {}
func (*ButtonInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_tmp_channel__e36e1600fe0bff55, []int{0}
}
func (m *ButtonInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ButtonInfo.Unmarshal(m, b)
}
func (m *ButtonInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ButtonInfo.Marshal(b, m, deterministic)
}
func (dst *ButtonInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ButtonInfo.Merge(dst, src)
}
func (m *ButtonInfo) XXX_Size() int {
	return xxx_messageInfo_ButtonInfo.Size(m)
}
func (m *ButtonInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ButtonInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ButtonInfo proto.InternalMessageInfo

func (m *ButtonInfo) GetButtonStatus() ButtonInfo_ButtonStatus {
	if m != nil {
		return m.ButtonStatus
	}
	return ButtonInfo_Hide
}

func (m *ButtonInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ButtonInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

// 玩家信息
type PlayerLabel struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Label                string   `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	Ttid                 string   `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayerLabel) Reset()         { *m = PlayerLabel{} }
func (m *PlayerLabel) String() string { return proto.CompactTextString(m) }
func (*PlayerLabel) ProtoMessage()    {}
func (*PlayerLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_tmp_channel__e36e1600fe0bff55, []int{1}
}
func (m *PlayerLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerLabel.Unmarshal(m, b)
}
func (m *PlayerLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerLabel.Marshal(b, m, deterministic)
}
func (dst *PlayerLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerLabel.Merge(dst, src)
}
func (m *PlayerLabel) XXX_Size() int {
	return xxx_messageInfo_PlayerLabel.Size(m)
}
func (m *PlayerLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerLabel.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerLabel proto.InternalMessageInfo

func (m *PlayerLabel) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PlayerLabel) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *PlayerLabel) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

// 赛事信息
type ContestInfo struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle             string   `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Url                  string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	BeginTime            string   `protobuf:"bytes,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContestInfo) Reset()         { *m = ContestInfo{} }
func (m *ContestInfo) String() string { return proto.CompactTextString(m) }
func (*ContestInfo) ProtoMessage()    {}
func (*ContestInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_tmp_channel__e36e1600fe0bff55, []int{2}
}
func (m *ContestInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContestInfo.Unmarshal(m, b)
}
func (m *ContestInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContestInfo.Marshal(b, m, deterministic)
}
func (dst *ContestInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContestInfo.Merge(dst, src)
}
func (m *ContestInfo) XXX_Size() int {
	return xxx_messageInfo_ContestInfo.Size(m)
}
func (m *ContestInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ContestInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ContestInfo proto.InternalMessageInfo

func (m *ContestInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ContestInfo) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *ContestInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *ContestInfo) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

// 房间公告
type AnnouncementInfo struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnnouncementInfo) Reset()         { *m = AnnouncementInfo{} }
func (m *AnnouncementInfo) String() string { return proto.CompactTextString(m) }
func (*AnnouncementInfo) ProtoMessage()    {}
func (*AnnouncementInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_tmp_channel__e36e1600fe0bff55, []int{3}
}
func (m *AnnouncementInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnnouncementInfo.Unmarshal(m, b)
}
func (m *AnnouncementInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnnouncementInfo.Marshal(b, m, deterministic)
}
func (dst *AnnouncementInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnnouncementInfo.Merge(dst, src)
}
func (m *AnnouncementInfo) XXX_Size() int {
	return xxx_messageInfo_AnnouncementInfo.Size(m)
}
func (m *AnnouncementInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnnouncementInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnnouncementInfo proto.InternalMessageInfo

func (m *AnnouncementInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AnnouncementInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// 赛事信息变更通知
type ContestInfoChangeNotify struct {
	ContestInfo          *ContestInfo `protobuf:"bytes,1,opt,name=contest_info,json=contestInfo,proto3" json:"contest_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ContestInfoChangeNotify) Reset()         { *m = ContestInfoChangeNotify{} }
func (m *ContestInfoChangeNotify) String() string { return proto.CompactTextString(m) }
func (*ContestInfoChangeNotify) ProtoMessage()    {}
func (*ContestInfoChangeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_tmp_channel__e36e1600fe0bff55, []int{4}
}
func (m *ContestInfoChangeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContestInfoChangeNotify.Unmarshal(m, b)
}
func (m *ContestInfoChangeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContestInfoChangeNotify.Marshal(b, m, deterministic)
}
func (dst *ContestInfoChangeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContestInfoChangeNotify.Merge(dst, src)
}
func (m *ContestInfoChangeNotify) XXX_Size() int {
	return xxx_messageInfo_ContestInfoChangeNotify.Size(m)
}
func (m *ContestInfoChangeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ContestInfoChangeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ContestInfoChangeNotify proto.InternalMessageInfo

func (m *ContestInfoChangeNotify) GetContestInfo() *ContestInfo {
	if m != nil {
		return m.ContestInfo
	}
	return nil
}

// 玩家名单变更通知
type PlayerChangeNotify struct {
	Capacity             uint32         `protobuf:"varint,1,opt,name=capacity,proto3" json:"capacity,omitempty"`
	Players              []*PlayerLabel `protobuf:"bytes,2,rep,name=players,proto3" json:"players,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *PlayerChangeNotify) Reset()         { *m = PlayerChangeNotify{} }
func (m *PlayerChangeNotify) String() string { return proto.CompactTextString(m) }
func (*PlayerChangeNotify) ProtoMessage()    {}
func (*PlayerChangeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_tmp_channel__e36e1600fe0bff55, []int{5}
}
func (m *PlayerChangeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayerChangeNotify.Unmarshal(m, b)
}
func (m *PlayerChangeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayerChangeNotify.Marshal(b, m, deterministic)
}
func (dst *PlayerChangeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayerChangeNotify.Merge(dst, src)
}
func (m *PlayerChangeNotify) XXX_Size() int {
	return xxx_messageInfo_PlayerChangeNotify.Size(m)
}
func (m *PlayerChangeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayerChangeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_PlayerChangeNotify proto.InternalMessageInfo

func (m *PlayerChangeNotify) GetCapacity() uint32 {
	if m != nil {
		return m.Capacity
	}
	return 0
}

func (m *PlayerChangeNotify) GetPlayers() []*PlayerLabel {
	if m != nil {
		return m.Players
	}
	return nil
}

// 赛事按钮变更通知
type ButtonChangeNotify struct {
	ButtonInfo           *ButtonInfo `protobuf:"bytes,1,opt,name=button_info,json=buttonInfo,proto3" json:"button_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ButtonChangeNotify) Reset()         { *m = ButtonChangeNotify{} }
func (m *ButtonChangeNotify) String() string { return proto.CompactTextString(m) }
func (*ButtonChangeNotify) ProtoMessage()    {}
func (*ButtonChangeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_tmp_channel__e36e1600fe0bff55, []int{6}
}
func (m *ButtonChangeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ButtonChangeNotify.Unmarshal(m, b)
}
func (m *ButtonChangeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ButtonChangeNotify.Marshal(b, m, deterministic)
}
func (dst *ButtonChangeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ButtonChangeNotify.Merge(dst, src)
}
func (m *ButtonChangeNotify) XXX_Size() int {
	return xxx_messageInfo_ButtonChangeNotify.Size(m)
}
func (m *ButtonChangeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ButtonChangeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ButtonChangeNotify proto.InternalMessageInfo

func (m *ButtonChangeNotify) GetButtonInfo() *ButtonInfo {
	if m != nil {
		return m.ButtonInfo
	}
	return nil
}

func init() {
	proto.RegisterType((*ButtonInfo)(nil), "ga.game_tmp_channel.ButtonInfo")
	proto.RegisterType((*PlayerLabel)(nil), "ga.game_tmp_channel.PlayerLabel")
	proto.RegisterType((*ContestInfo)(nil), "ga.game_tmp_channel.ContestInfo")
	proto.RegisterType((*AnnouncementInfo)(nil), "ga.game_tmp_channel.AnnouncementInfo")
	proto.RegisterType((*ContestInfoChangeNotify)(nil), "ga.game_tmp_channel.ContestInfoChangeNotify")
	proto.RegisterType((*PlayerChangeNotify)(nil), "ga.game_tmp_channel.PlayerChangeNotify")
	proto.RegisterType((*ButtonChangeNotify)(nil), "ga.game_tmp_channel.ButtonChangeNotify")
	proto.RegisterEnum("ga.game_tmp_channel.ButtonInfo_ButtonStatus", ButtonInfo_ButtonStatus_name, ButtonInfo_ButtonStatus_value)
}

func init() {
	proto.RegisterFile("game-tmp-channel_.proto", fileDescriptor_game_tmp_channel__e36e1600fe0bff55)
}

var fileDescriptor_game_tmp_channel__e36e1600fe0bff55 = []byte{
	// 457 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x53, 0x4d, 0x8f, 0xd3, 0x30,
	0x14, 0x24, 0x6d, 0x97, 0x6d, 0x5e, 0x5a, 0xa8, 0xcc, 0x4a, 0x1b, 0x81, 0x10, 0x55, 0x4e, 0x7b,
	0xa0, 0xa9, 0x54, 0x84, 0x84, 0x38, 0x41, 0x7b, 0xa1, 0x12, 0x42, 0x10, 0x16, 0x0e, 0x1c, 0x88,
	0x6c, 0xc7, 0xcd, 0x5a, 0xf8, 0x23, 0x34, 0x2f, 0x12, 0xf9, 0x75, 0xfc, 0x35, 0x14, 0x27, 0xed,
	0x66, 0x61, 0x81, 0xdb, 0x8c, 0x47, 0x33, 0x6f, 0xf2, 0xec, 0xc0, 0x79, 0x4e, 0xb5, 0x58, 0xa0,
	0x2e, 0x16, 0xfc, 0x8a, 0x1a, 0x23, 0x54, 0x1a, 0x17, 0x7b, 0x8b, 0x96, 0x3c, 0xc8, 0x69, 0xdc,
	0x68, 0x29, 0xea, 0x22, 0xed, 0xb4, 0xe8, 0xa7, 0x07, 0xb0, 0xae, 0x10, 0xad, 0xd9, 0x9a, 0x9d,
	0x25, 0x1f, 0x60, 0xca, 0x1c, 0x4b, 0x4b, 0xa4, 0x58, 0x95, 0xa1, 0x37, 0xf7, 0x2e, 0xee, 0xad,
	0x9e, 0xc6, 0xb7, 0x78, 0xe3, 0x6b, 0x5f, 0x07, 0x3f, 0x3a, 0x4f, 0x32, 0x61, 0x3d, 0x46, 0x08,
	0x8c, 0x50, 0xfc, 0xc0, 0x70, 0x30, 0xf7, 0x2e, 0xfc, 0xc4, 0x61, 0x32, 0x83, 0x61, 0xb5, 0x57,
	0xe1, 0xd0, 0x1d, 0x35, 0x30, 0x7a, 0x01, 0x93, 0x7e, 0x06, 0x19, 0xc3, 0xe8, 0x8d, 0xcc, 0xc4,
	0xec, 0x0e, 0xb9, 0x0f, 0xc1, 0x27, 0xc3, 0x95, 0xe4, 0xdf, 0x28, 0x53, 0x62, 0xe6, 0x91, 0x29,
	0xf8, 0x9b, 0x23, 0x1d, 0x44, 0x5b, 0x08, 0xde, 0x2b, 0x5a, 0x8b, 0xfd, 0x5b, 0xca, 0x84, 0x72,
	0xd1, 0x32, 0x73, 0xbd, 0xa7, 0x49, 0x03, 0xc9, 0x19, 0x9c, 0xa8, 0x46, 0xea, 0x1a, 0xb4, 0xc4,
	0xd5, 0x42, 0x99, 0x75, 0x1d, 0x1c, 0x8e, 0xbe, 0x43, 0xb0, 0xb1, 0x06, 0x45, 0x89, 0x6e, 0x19,
	0x67, 0x70, 0x82, 0x12, 0x95, 0x70, 0x61, 0x7e, 0xd2, 0x12, 0xf2, 0x08, 0xfc, 0xb2, 0x62, 0x69,
	0xab, 0xb4, 0x91, 0xe3, 0xb2, 0x62, 0x97, 0x4e, 0xfc, 0xe3, 0xc3, 0xc8, 0x63, 0x00, 0x26, 0x72,
	0x69, 0x52, 0x94, 0x5a, 0x84, 0x23, 0x27, 0xf8, 0xee, 0xe4, 0x52, 0x6a, 0x11, 0xad, 0x61, 0xf6,
	0xda, 0x18, 0x5b, 0x19, 0x2e, 0xb4, 0x30, 0xff, 0x9a, 0x1b, 0xc2, 0x29, 0x6f, 0xca, 0x99, 0xc3,
	0x2a, 0x0f, 0x34, 0xfa, 0x0a, 0xe7, 0xbd, 0xda, 0x9b, 0x2b, 0x6a, 0x72, 0xf1, 0xce, 0xa2, 0xdc,
	0xd5, 0x64, 0x03, 0x13, 0xde, 0x4a, 0xa9, 0x34, 0x3b, 0xeb, 0x12, 0x83, 0xd5, 0xfc, 0xd6, 0xeb,
	0xec, 0x65, 0x24, 0x01, 0xbf, 0x26, 0x91, 0x02, 0xd2, 0x6e, 0xf8, 0x46, 0xf4, 0x43, 0x18, 0x73,
	0x5a, 0x50, 0x2e, 0xb1, 0xee, 0xb6, 0x7d, 0xe4, 0xe4, 0x25, 0x9c, 0x16, 0xce, 0x51, 0x86, 0x83,
	0xf9, 0xf0, 0xaf, 0x13, 0x7b, 0xf7, 0x96, 0x1c, 0x0c, 0xd1, 0x67, 0x20, 0xed, 0x4b, 0xb8, 0x31,
	0xed, 0x15, 0x04, 0xdd, 0xc3, 0xec, 0x7d, 0xc7, 0x93, 0xff, 0x3c, 0xcb, 0x04, 0xd8, 0x11, 0xaf,
	0xb7, 0x10, 0x72, 0xab, 0xe3, 0x5a, 0xd6, 0xb6, 0x6a, 0x7c, 0xda, 0x66, 0x42, 0xb5, 0xbf, 0xc6,
	0x97, 0x45, 0x6e, 0x15, 0x35, 0x79, 0xfc, 0x7c, 0x85, 0x18, 0x73, 0xab, 0x97, 0xee, 0x98, 0x5b,
	0xb5, 0xa4, 0x45, 0xb1, 0xfc, 0x7d, 0x02, 0xbb, 0xeb, 0xe4, 0x67, 0xbf, 0x02, 0x00, 0x00, 0xff,
	0xff, 0x47, 0xaf, 0xab, 0x61, 0x6b, 0x03, 0x00, 0x00,
}
