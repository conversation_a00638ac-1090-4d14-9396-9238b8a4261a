// Code generated by protoc-gen-go. DO NOT EDIT.
// source: function-card-logic_.proto

package functioncardlogic // import "golang.52tt.com/protocol/app/functioncardlogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type CardType int32

const (
	CardType_UNKNOWN_CARD           CardType = 0
	CardType_DY_HEAD_IMAGE_CARD     CardType = 1
	CardType_INTERACTION_EMOJI_CARD CardType = 2
	CardType_CHANNEL_INFO_BG_CARD   CardType = 3
)

var CardType_name = map[int32]string{
	0: "UNKNOWN_CARD",
	1: "DY_HEAD_IMAGE_CARD",
	2: "INTERACTION_EMOJI_CARD",
	3: "CHANNEL_INFO_BG_CARD",
}
var CardType_value = map[string]int32{
	"UNKNOWN_CARD":           0,
	"DY_HEAD_IMAGE_CARD":     1,
	"INTERACTION_EMOJI_CARD": 2,
	"CHANNEL_INFO_BG_CARD":   3,
}

func (x CardType) String() string {
	return proto.EnumName(CardType_name, int32(x))
}
func (CardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_function_card_logic__29bd1e80389a916b, []int{0}
}

type CardPrivilege struct {
	CardType             CardType `protobuf:"varint,1,opt,name=card_type,json=cardType,proto3,enum=ga.functioncardlogic.CardType" json:"card_type,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	Level                uint32   `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardPrivilege) Reset()         { *m = CardPrivilege{} }
func (m *CardPrivilege) String() string { return proto.CompactTextString(m) }
func (*CardPrivilege) ProtoMessage()    {}
func (*CardPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_function_card_logic__29bd1e80389a916b, []int{0}
}
func (m *CardPrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardPrivilege.Unmarshal(m, b)
}
func (m *CardPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardPrivilege.Marshal(b, m, deterministic)
}
func (dst *CardPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardPrivilege.Merge(dst, src)
}
func (m *CardPrivilege) XXX_Size() int {
	return xxx_messageInfo_CardPrivilege.Size(m)
}
func (m *CardPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_CardPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_CardPrivilege proto.InternalMessageInfo

func (m *CardPrivilege) GetCardType() CardType {
	if m != nil {
		return m.CardType
	}
	return CardType_UNKNOWN_CARD
}

func (m *CardPrivilege) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *CardPrivilege) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *CardPrivilege) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type GetUserFunctionCardReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	CardType             CardType     `protobuf:"varint,2,opt,name=card_type,json=cardType,proto3,enum=ga.functioncardlogic.CardType" json:"card_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserFunctionCardReq) Reset()         { *m = GetUserFunctionCardReq{} }
func (m *GetUserFunctionCardReq) String() string { return proto.CompactTextString(m) }
func (*GetUserFunctionCardReq) ProtoMessage()    {}
func (*GetUserFunctionCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_function_card_logic__29bd1e80389a916b, []int{1}
}
func (m *GetUserFunctionCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFunctionCardReq.Unmarshal(m, b)
}
func (m *GetUserFunctionCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFunctionCardReq.Marshal(b, m, deterministic)
}
func (dst *GetUserFunctionCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFunctionCardReq.Merge(dst, src)
}
func (m *GetUserFunctionCardReq) XXX_Size() int {
	return xxx_messageInfo_GetUserFunctionCardReq.Size(m)
}
func (m *GetUserFunctionCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFunctionCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFunctionCardReq proto.InternalMessageInfo

func (m *GetUserFunctionCardReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserFunctionCardReq) GetCardType() CardType {
	if m != nil {
		return m.CardType
	}
	return CardType_UNKNOWN_CARD
}

type GetUserFunctionCardResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CardPriList          []*CardPrivilege `protobuf:"bytes,2,rep,name=card_pri_list,json=cardPriList,proto3" json:"card_pri_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserFunctionCardResp) Reset()         { *m = GetUserFunctionCardResp{} }
func (m *GetUserFunctionCardResp) String() string { return proto.CompactTextString(m) }
func (*GetUserFunctionCardResp) ProtoMessage()    {}
func (*GetUserFunctionCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_function_card_logic__29bd1e80389a916b, []int{2}
}
func (m *GetUserFunctionCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFunctionCardResp.Unmarshal(m, b)
}
func (m *GetUserFunctionCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFunctionCardResp.Marshal(b, m, deterministic)
}
func (dst *GetUserFunctionCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFunctionCardResp.Merge(dst, src)
}
func (m *GetUserFunctionCardResp) XXX_Size() int {
	return xxx_messageInfo_GetUserFunctionCardResp.Size(m)
}
func (m *GetUserFunctionCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFunctionCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFunctionCardResp proto.InternalMessageInfo

func (m *GetUserFunctionCardResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserFunctionCardResp) GetCardPriList() []*CardPrivilege {
	if m != nil {
		return m.CardPriList
	}
	return nil
}

func init() {
	proto.RegisterType((*CardPrivilege)(nil), "ga.functioncardlogic.CardPrivilege")
	proto.RegisterType((*GetUserFunctionCardReq)(nil), "ga.functioncardlogic.GetUserFunctionCardReq")
	proto.RegisterType((*GetUserFunctionCardResp)(nil), "ga.functioncardlogic.GetUserFunctionCardResp")
	proto.RegisterEnum("ga.functioncardlogic.CardType", CardType_name, CardType_value)
}

func init() {
	proto.RegisterFile("function-card-logic_.proto", fileDescriptor_function_card_logic__29bd1e80389a916b)
}

var fileDescriptor_function_card_logic__29bd1e80389a916b = []byte{
	// 431 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x92, 0xdf, 0x8a, 0xd3, 0x40,
	0x14, 0xc6, 0x4d, 0xeb, 0x9f, 0xf6, 0x64, 0x2b, 0xcb, 0x50, 0x6a, 0x28, 0xa8, 0xa5, 0x82, 0x54,
	0x61, 0x53, 0xa8, 0x78, 0xe5, 0x55, 0xda, 0x66, 0xbb, 0x59, 0x77, 0x53, 0x19, 0xba, 0x88, 0xde,
	0x0c, 0x49, 0x7a, 0x0c, 0x23, 0x49, 0x67, 0x9a, 0xcc, 0x2e, 0xe6, 0xc2, 0x47, 0xf0, 0x25, 0x7c,
	0x52, 0xc9, 0x4c, 0x56, 0x11, 0xeb, 0xc5, 0xde, 0x9d, 0xf9, 0x7e, 0x27, 0x99, 0xdf, 0x47, 0x02,
	0xc3, 0x2f, 0xd7, 0xbb, 0x44, 0x71, 0xb1, 0x3b, 0x49, 0xa2, 0x62, 0x7b, 0x92, 0x89, 0x94, 0x27,
	0xcc, 0x95, 0x85, 0x50, 0x82, 0xf4, 0xd3, 0xc8, 0xbd, 0xc5, 0x35, 0xd5, 0x70, 0xd8, 0x4b, 0x23,
	0x16, 0x47, 0x25, 0x9a, 0xa5, 0xf1, 0x4f, 0x0b, 0x7a, 0x8b, 0xa8, 0xd8, 0x7e, 0x28, 0xf8, 0x0d,
	0xcf, 0x30, 0x45, 0xf2, 0x0e, 0xba, 0xf5, 0x36, 0x53, 0x95, 0x44, 0xc7, 0x1a, 0x59, 0x93, 0xc7,
	0xb3, 0x67, 0xee, 0xa1, 0x57, 0xb9, 0xf5, 0x73, 0x9b, 0x4a, 0x22, 0xed, 0x24, 0xcd, 0x44, 0x9e,
	0x02, 0xc4, 0x98, 0xf2, 0x1d, 0x53, 0x3c, 0x47, 0xa7, 0x35, 0xb2, 0x26, 0x3d, 0xda, 0xd5, 0xc9,
	0x86, 0xe7, 0x48, 0x9e, 0x83, 0x8d, 0xdf, 0x24, 0x2f, 0xd0, 0xf0, 0xb6, 0xe6, 0x60, 0x22, 0xbd,
	0xd0, 0x87, 0x07, 0x19, 0xde, 0x60, 0xe6, 0xdc, 0xd7, 0xc8, 0x1c, 0xc6, 0xdf, 0x61, 0xb0, 0x42,
	0x75, 0x55, 0x62, 0x71, 0xda, 0x48, 0xd4, 0x57, 0x53, 0xdc, 0x93, 0x97, 0xd0, 0xa9, 0xcb, 0xb0,
	0x02, 0xf7, 0xda, 0xd5, 0x9e, 0xd9, 0xb5, 0xeb, 0x3c, 0x2a, 0x91, 0xe2, 0x9e, 0x3e, 0x8a, 0xcd,
	0xf0, 0x77, 0xa9, 0xd6, 0xdd, 0x4a, 0x8d, 0x7f, 0x58, 0xf0, 0xe4, 0xe0, 0xfd, 0xa5, 0x24, 0xaf,
	0xa0, 0xdb, 0x08, 0x94, 0xb2, 0x31, 0x38, 0xfa, 0x63, 0x50, 0x4a, 0xda, 0x89, 0x9b, 0x89, 0xac,
	0xa0, 0xa7, 0x1d, 0x64, 0xc1, 0x59, 0xc6, 0x4b, 0xe5, 0xb4, 0x46, 0xed, 0x89, 0x3d, 0x7b, 0xf1,
	0x7f, 0x8f, 0xdf, 0x1f, 0x85, 0xda, 0x89, 0x39, 0x5e, 0xf0, 0x52, 0xbd, 0xfe, 0x0a, 0x9d, 0x5b,
	0x4b, 0x72, 0x0c, 0x47, 0x57, 0xe1, 0xfb, 0x70, 0xfd, 0x31, 0x64, 0x0b, 0x8f, 0x2e, 0x8f, 0xef,
	0x91, 0x01, 0x90, 0xe5, 0x27, 0x76, 0xe6, 0x7b, 0x4b, 0x16, 0x5c, 0x7a, 0x2b, 0xdf, 0xe4, 0x16,
	0x19, 0xc2, 0x20, 0x08, 0x37, 0x3e, 0xf5, 0x16, 0x9b, 0x60, 0x1d, 0x32, 0xff, 0x72, 0x7d, 0x1e,
	0x18, 0xd6, 0x22, 0x0e, 0xf4, 0x17, 0x67, 0x5e, 0x18, 0xfa, 0x17, 0x2c, 0x08, 0x4f, 0xd7, 0x6c,
	0xbe, 0x32, 0xa4, 0x3d, 0x3f, 0x07, 0x27, 0x11, 0xb9, 0x5b, 0xf1, 0x4a, 0x5c, 0xd7, 0xa2, 0xb9,
	0xd8, 0x62, 0x66, 0xfe, 0x9d, 0xcf, 0x6e, 0x2a, 0xb2, 0x68, 0x97, 0xba, 0x6f, 0x67, 0x4a, 0xb9,
	0x89, 0xc8, 0xa7, 0x3a, 0x4e, 0x44, 0x36, 0x8d, 0xa4, 0x9c, 0xfe, 0x53, 0x29, 0x7e, 0xa8, 0xf9,
	0x9b, 0x5f, 0x01, 0x00, 0x00, 0xff, 0xff, 0xfb, 0x71, 0xc7, 0x73, 0xb5, 0x02, 0x00, 0x00,
}
