// Code generated by protoc-gen-gogo.
// source: channel_trivia_game_.proto
// DO NOT EDIT!

/*
	Package channel is a generated protocol buffer package.

	It is generated from these files:
		channel_trivia_game_.proto

	It has these top-level messages:
		PhaseTimer
		PhaseAnimation
		PhaseQuestion
		PhaseEntry
		ChannelTriviaGameInfo
		ChannelTriviaGameEnterReq
		ChannelTriviaGameEnterResp
		ChannelTriviaGamePreEnterReq
		ChannelTriviaGameAdimn
		ChannelTriviaGamePreEnterResp
		ChannelTriviaGameQuestion
		ChannelTriviaGameAnswerReq
		ChannelTriviaGameAnswerResp
		ChannelTriviaGameGetPhaseListReq
		ChannelTriviaGameGetPhaseListResp
		ChannelTriviaGamePhaseForwardReq
		ChannelTriviaGamePhaseForwardResp
		ChannelTriviaGameShowSolutionReq
		ChannelTriviaGameShowSolutionResp
*/
package channel

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 答题
type ETriviaGameAnswerResultStatus int32

const (
	ETriviaGameAnswerResultStatus_ENUM_TRIVIA_GAME_ANSWER_RESULT_CORRECT ETriviaGameAnswerResultStatus = 1
	ETriviaGameAnswerResultStatus_ENUM_TRIVIA_GAME_ANSWER_RESULT_ERROR   ETriviaGameAnswerResultStatus = 2
	ETriviaGameAnswerResultStatus_ENUM_TRIVIA_GAME_ANSWER_RESULT_TIMEOUT ETriviaGameAnswerResultStatus = 3
)

var ETriviaGameAnswerResultStatus_name = map[int32]string{
	1: "ENUM_TRIVIA_GAME_ANSWER_RESULT_CORRECT",
	2: "ENUM_TRIVIA_GAME_ANSWER_RESULT_ERROR",
	3: "ENUM_TRIVIA_GAME_ANSWER_RESULT_TIMEOUT",
}
var ETriviaGameAnswerResultStatus_value = map[string]int32{
	"ENUM_TRIVIA_GAME_ANSWER_RESULT_CORRECT": 1,
	"ENUM_TRIVIA_GAME_ANSWER_RESULT_ERROR":   2,
	"ENUM_TRIVIA_GAME_ANSWER_RESULT_TIMEOUT": 3,
}

func (x ETriviaGameAnswerResultStatus) Enum() *ETriviaGameAnswerResultStatus {
	p := new(ETriviaGameAnswerResultStatus)
	*p = x
	return p
}
func (x ETriviaGameAnswerResultStatus) String() string {
	return proto.EnumName(ETriviaGameAnswerResultStatus_name, int32(x))
}
func (x *ETriviaGameAnswerResultStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ETriviaGameAnswerResultStatus_value, data, "ETriviaGameAnswerResultStatus")
	if err != nil {
		return err
	}
	*x = ETriviaGameAnswerResultStatus(value)
	return nil
}
func (ETriviaGameAnswerResultStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{0}
}

type ETriviaGameAnswerId int32

const (
	ETriviaGameAnswerId_ENUM_TRIVIA_GAME_ANSWER_ID_A       ETriviaGameAnswerId = 1
	ETriviaGameAnswerId_ENUM_TRIVIA_GAME_ANSWER_ID_B       ETriviaGameAnswerId = 2
	ETriviaGameAnswerId_ENUM_TRIVIA_GAME_ANSWER_ID_C       ETriviaGameAnswerId = 3
	ETriviaGameAnswerId_ENUM_TRIVIA_GAME_ANSWER_ID_D       ETriviaGameAnswerId = 4
	ETriviaGameAnswerId_ENUM_TRIVIA_GAME_ANSWER_ID_TIMEOUT ETriviaGameAnswerId = 255
)

var ETriviaGameAnswerId_name = map[int32]string{
	1:   "ENUM_TRIVIA_GAME_ANSWER_ID_A",
	2:   "ENUM_TRIVIA_GAME_ANSWER_ID_B",
	3:   "ENUM_TRIVIA_GAME_ANSWER_ID_C",
	4:   "ENUM_TRIVIA_GAME_ANSWER_ID_D",
	255: "ENUM_TRIVIA_GAME_ANSWER_ID_TIMEOUT",
}
var ETriviaGameAnswerId_value = map[string]int32{
	"ENUM_TRIVIA_GAME_ANSWER_ID_A":       1,
	"ENUM_TRIVIA_GAME_ANSWER_ID_B":       2,
	"ENUM_TRIVIA_GAME_ANSWER_ID_C":       3,
	"ENUM_TRIVIA_GAME_ANSWER_ID_D":       4,
	"ENUM_TRIVIA_GAME_ANSWER_ID_TIMEOUT": 255,
}

func (x ETriviaGameAnswerId) Enum() *ETriviaGameAnswerId {
	p := new(ETriviaGameAnswerId)
	*p = x
	return p
}
func (x ETriviaGameAnswerId) String() string {
	return proto.EnumName(ETriviaGameAnswerId_name, int32(x))
}
func (x *ETriviaGameAnswerId) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ETriviaGameAnswerId_value, data, "ETriviaGameAnswerId")
	if err != nil {
		return err
	}
	*x = ETriviaGameAnswerId(value)
	return nil
}
func (ETriviaGameAnswerId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{1}
}

type PhaseAnimation_AnimationId int32

const (
	// 暂时hardcode三个动画id
	PhaseAnimation_Animation_Reward        PhaseAnimation_AnimationId = 1
	PhaseAnimation_Animation_Middle        PhaseAnimation_AnimationId = 2
	PhaseAnimation_Animation_Last_Question PhaseAnimation_AnimationId = 3
)

var PhaseAnimation_AnimationId_name = map[int32]string{
	1: "Animation_Reward",
	2: "Animation_Middle",
	3: "Animation_Last_Question",
}
var PhaseAnimation_AnimationId_value = map[string]int32{
	"Animation_Reward":        1,
	"Animation_Middle":        2,
	"Animation_Last_Question": 3,
}

func (x PhaseAnimation_AnimationId) Enum() *PhaseAnimation_AnimationId {
	p := new(PhaseAnimation_AnimationId)
	*p = x
	return p
}
func (x PhaseAnimation_AnimationId) String() string {
	return proto.EnumName(PhaseAnimation_AnimationId_name, int32(x))
}
func (x *PhaseAnimation_AnimationId) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PhaseAnimation_AnimationId_value, data, "PhaseAnimation_AnimationId")
	if err != nil {
		return err
	}
	*x = PhaseAnimation_AnimationId(value)
	return nil
}
func (PhaseAnimation_AnimationId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{1, 0}
}

type PhaseEntry_PhaseType int32

const (
	PhaseEntry_phase_none      PhaseEntry_PhaseType = 0
	PhaseEntry_phase_timer     PhaseEntry_PhaseType = 1
	PhaseEntry_phase_animation PhaseEntry_PhaseType = 2
	PhaseEntry_phase_question  PhaseEntry_PhaseType = 3
	PhaseEntry_phase_winner    PhaseEntry_PhaseType = 4
)

var PhaseEntry_PhaseType_name = map[int32]string{
	0: "phase_none",
	1: "phase_timer",
	2: "phase_animation",
	3: "phase_question",
	4: "phase_winner",
}
var PhaseEntry_PhaseType_value = map[string]int32{
	"phase_none":      0,
	"phase_timer":     1,
	"phase_animation": 2,
	"phase_question":  3,
	"phase_winner":    4,
}

func (x PhaseEntry_PhaseType) Enum() *PhaseEntry_PhaseType {
	p := new(PhaseEntry_PhaseType)
	*p = x
	return p
}
func (x PhaseEntry_PhaseType) String() string {
	return proto.EnumName(PhaseEntry_PhaseType_name, int32(x))
}
func (x *PhaseEntry_PhaseType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PhaseEntry_PhaseType_value, data, "PhaseEntry_PhaseType")
	if err != nil {
		return err
	}
	*x = PhaseEntry_PhaseType(value)
	return nil
}
func (PhaseEntry_PhaseType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{3, 0}
}

type PhaseTimer struct {
	EndTimestamp uint32 `protobuf:"varint,1,req,name=end_timestamp,json=endTimestamp" json:"end_timestamp"`
}

func (m *PhaseTimer) Reset()                    { *m = PhaseTimer{} }
func (m *PhaseTimer) String() string            { return proto.CompactTextString(m) }
func (*PhaseTimer) ProtoMessage()               {}
func (*PhaseTimer) Descriptor() ([]byte, []int) { return fileDescriptorChannelTriviaGame_, []int{0} }

func (m *PhaseTimer) GetEndTimestamp() uint32 {
	if m != nil {
		return m.EndTimestamp
	}
	return 0
}

type PhaseAnimation struct {
	AnimaId uint32 `protobuf:"varint,1,req,name=anima_id,json=animaId" json:"anima_id"`
	Text    string `protobuf:"bytes,2,opt,name=text" json:"text"`
}

func (m *PhaseAnimation) Reset()                    { *m = PhaseAnimation{} }
func (m *PhaseAnimation) String() string            { return proto.CompactTextString(m) }
func (*PhaseAnimation) ProtoMessage()               {}
func (*PhaseAnimation) Descriptor() ([]byte, []int) { return fileDescriptorChannelTriviaGame_, []int{1} }

func (m *PhaseAnimation) GetAnimaId() uint32 {
	if m != nil {
		return m.AnimaId
	}
	return 0
}

func (m *PhaseAnimation) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type PhaseQuestion struct {
	QuestionId     uint32 `protobuf:"varint,1,req,name=question_id,json=questionId" json:"question_id"`
	QuestionIdx    uint32 `protobuf:"varint,2,req,name=question_idx,json=questionIdx" json:"question_idx"`
	TipTime        uint32 `protobuf:"varint,3,req,name=tip_time,json=tipTime" json:"tip_time"`
	IsShowSolution bool   `protobuf:"varint,4,opt,name=is_show_solution,json=isShowSolution" json:"is_show_solution"`
}

func (m *PhaseQuestion) Reset()                    { *m = PhaseQuestion{} }
func (m *PhaseQuestion) String() string            { return proto.CompactTextString(m) }
func (*PhaseQuestion) ProtoMessage()               {}
func (*PhaseQuestion) Descriptor() ([]byte, []int) { return fileDescriptorChannelTriviaGame_, []int{2} }

func (m *PhaseQuestion) GetQuestionId() uint32 {
	if m != nil {
		return m.QuestionId
	}
	return 0
}

func (m *PhaseQuestion) GetQuestionIdx() uint32 {
	if m != nil {
		return m.QuestionIdx
	}
	return 0
}

func (m *PhaseQuestion) GetTipTime() uint32 {
	if m != nil {
		return m.TipTime
	}
	return 0
}

func (m *PhaseQuestion) GetIsShowSolution() bool {
	if m != nil {
		return m.IsShowSolution
	}
	return false
}

type PhaseEntry struct {
	PhaseIdx  uint32 `protobuf:"varint,1,req,name=phase_idx,json=phaseIdx" json:"phase_idx"`
	PhaseType uint32 `protobuf:"varint,2,opt,name=phase_type,json=phaseType" json:"phase_type"`
	PhaseName string `protobuf:"bytes,3,opt,name=phase_name,json=phaseName" json:"phase_name"`
	PhaseBin  []byte `protobuf:"bytes,4,opt,name=phase_bin,json=phaseBin" json:"phase_bin"`
}

func (m *PhaseEntry) Reset()                    { *m = PhaseEntry{} }
func (m *PhaseEntry) String() string            { return proto.CompactTextString(m) }
func (*PhaseEntry) ProtoMessage()               {}
func (*PhaseEntry) Descriptor() ([]byte, []int) { return fileDescriptorChannelTriviaGame_, []int{3} }

func (m *PhaseEntry) GetPhaseIdx() uint32 {
	if m != nil {
		return m.PhaseIdx
	}
	return 0
}

func (m *PhaseEntry) GetPhaseType() uint32 {
	if m != nil {
		return m.PhaseType
	}
	return 0
}

func (m *PhaseEntry) GetPhaseName() string {
	if m != nil {
		return m.PhaseName
	}
	return ""
}

func (m *PhaseEntry) GetPhaseBin() []byte {
	if m != nil {
		return m.PhaseBin
	}
	return nil
}

type ChannelTriviaGameInfo struct {
	ActId       uint32 `protobuf:"varint,1,req,name=act_id,json=actId" json:"act_id"`
	StartTime   uint32 `protobuf:"varint,2,req,name=start_time,json=startTime" json:"start_time"`
	Reward      uint32 `protobuf:"varint,3,req,name=reward" json:"reward"`
	QuestionNum uint32 `protobuf:"varint,4,req,name=question_num,json=questionNum" json:"question_num"`
}

func (m *ChannelTriviaGameInfo) Reset()         { *m = ChannelTriviaGameInfo{} }
func (m *ChannelTriviaGameInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGameInfo) ProtoMessage()    {}
func (*ChannelTriviaGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{4}
}

func (m *ChannelTriviaGameInfo) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *ChannelTriviaGameInfo) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ChannelTriviaGameInfo) GetReward() uint32 {
	if m != nil {
		return m.Reward
	}
	return 0
}

func (m *ChannelTriviaGameInfo) GetQuestionNum() uint32 {
	if m != nil {
		return m.QuestionNum
	}
	return 0
}

// 特殊的进房间请求
type ChannelTriviaGameEnterReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *ChannelTriviaGameEnterReq) Reset()         { *m = ChannelTriviaGameEnterReq{} }
func (m *ChannelTriviaGameEnterReq) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGameEnterReq) ProtoMessage()    {}
func (*ChannelTriviaGameEnterReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{5}
}

func (m *ChannelTriviaGameEnterReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelTriviaGameEnterReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChannelTriviaGameEnterResp struct {
	BaseResp         *ga.BaseResp           `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ServerTime       int64                  `protobuf:"varint,2,opt,name=server_time,json=serverTime" json:"server_time"`
	ChannelId        uint32                 `protobuf:"varint,3,req,name=channel_id,json=channelId" json:"channel_id"`
	SessionId        uint32                 `protobuf:"varint,4,req,name=session_id,json=sessionId" json:"session_id"`
	ChannelDisplayId uint32                 `protobuf:"varint,5,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	ChannelType      uint32                 `protobuf:"varint,6,opt,name=channel_type,json=channelType" json:"channel_type"`
	GameInfo         *ChannelTriviaGameInfo `protobuf:"bytes,7,opt,name=game_info,json=gameInfo" json:"game_info,omitempty"`
	CurrentPhase     *PhaseEntry            `protobuf:"bytes,8,opt,name=current_phase,json=currentPhase" json:"current_phase,omitempty"`
	ChannelMemberCnt uint32                 `protobuf:"varint,9,opt,name=channel_member_cnt,json=channelMemberCnt" json:"channel_member_cnt"`
	RemainReviveCnt  uint32                 `protobuf:"varint,10,opt,name=remain_revive_cnt,json=remainReviveCnt" json:"remain_revive_cnt"`
	IsAnswerQualify  bool                   `protobuf:"varint,11,opt,name=is_answer_qualify,json=isAnswerQualify" json:"is_answer_qualify"`
}

func (m *ChannelTriviaGameEnterResp) Reset()         { *m = ChannelTriviaGameEnterResp{} }
func (m *ChannelTriviaGameEnterResp) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGameEnterResp) ProtoMessage()    {}
func (*ChannelTriviaGameEnterResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{6}
}

func (m *ChannelTriviaGameEnterResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelTriviaGameEnterResp) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *ChannelTriviaGameEnterResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelTriviaGameEnterResp) GetSessionId() uint32 {
	if m != nil {
		return m.SessionId
	}
	return 0
}

func (m *ChannelTriviaGameEnterResp) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *ChannelTriviaGameEnterResp) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelTriviaGameEnterResp) GetGameInfo() *ChannelTriviaGameInfo {
	if m != nil {
		return m.GameInfo
	}
	return nil
}

func (m *ChannelTriviaGameEnterResp) GetCurrentPhase() *PhaseEntry {
	if m != nil {
		return m.CurrentPhase
	}
	return nil
}

func (m *ChannelTriviaGameEnterResp) GetChannelMemberCnt() uint32 {
	if m != nil {
		return m.ChannelMemberCnt
	}
	return 0
}

func (m *ChannelTriviaGameEnterResp) GetRemainReviveCnt() uint32 {
	if m != nil {
		return m.RemainReviveCnt
	}
	return 0
}

func (m *ChannelTriviaGameEnterResp) GetIsAnswerQualify() bool {
	if m != nil {
		return m.IsAnswerQualify
	}
	return false
}

// 进房前协议
type ChannelTriviaGamePreEnterReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *ChannelTriviaGamePreEnterReq) Reset()         { *m = ChannelTriviaGamePreEnterReq{} }
func (m *ChannelTriviaGamePreEnterReq) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGamePreEnterReq) ProtoMessage()    {}
func (*ChannelTriviaGamePreEnterReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{7}
}

func (m *ChannelTriviaGamePreEnterReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelTriviaGamePreEnterReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChannelTriviaGameAdimn struct {
	Uid  uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Role uint32 `protobuf:"varint,2,req,name=role" json:"role"`
}

func (m *ChannelTriviaGameAdimn) Reset()         { *m = ChannelTriviaGameAdimn{} }
func (m *ChannelTriviaGameAdimn) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGameAdimn) ProtoMessage()    {}
func (*ChannelTriviaGameAdimn) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{8}
}

func (m *ChannelTriviaGameAdimn) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelTriviaGameAdimn) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

type ChannelTriviaGamePreEnterResp struct {
	BaseResp       *ga.BaseResp              `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ChannelId      uint32                    `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	ServerTime     int64                     `protobuf:"varint,3,opt,name=server_time,json=serverTime" json:"server_time"`
	GameInfo       *ChannelTriviaGameInfo    `protobuf:"bytes,4,opt,name=game_info,json=gameInfo" json:"game_info,omitempty"`
	CurrentPhase   *PhaseEntry               `protobuf:"bytes,5,opt,name=current_phase,json=currentPhase" json:"current_phase,omitempty"`
	LeftReviveCnt  uint32                    `protobuf:"varint,6,opt,name=left_revive_cnt,json=leftReviveCnt" json:"left_revive_cnt"`
	UsrRewardTotal uint32                    `protobuf:"varint,7,opt,name=usr_reward_total,json=usrRewardTotal" json:"usr_reward_total"`
	AdminList      []*ChannelTriviaGameAdimn `protobuf:"bytes,8,rep,name=admin_list,json=adminList" json:"admin_list,omitempty"`
}

func (m *ChannelTriviaGamePreEnterResp) Reset()         { *m = ChannelTriviaGamePreEnterResp{} }
func (m *ChannelTriviaGamePreEnterResp) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGamePreEnterResp) ProtoMessage()    {}
func (*ChannelTriviaGamePreEnterResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{9}
}

func (m *ChannelTriviaGamePreEnterResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelTriviaGamePreEnterResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelTriviaGamePreEnterResp) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *ChannelTriviaGamePreEnterResp) GetGameInfo() *ChannelTriviaGameInfo {
	if m != nil {
		return m.GameInfo
	}
	return nil
}

func (m *ChannelTriviaGamePreEnterResp) GetCurrentPhase() *PhaseEntry {
	if m != nil {
		return m.CurrentPhase
	}
	return nil
}

func (m *ChannelTriviaGamePreEnterResp) GetLeftReviveCnt() uint32 {
	if m != nil {
		return m.LeftReviveCnt
	}
	return 0
}

func (m *ChannelTriviaGamePreEnterResp) GetUsrRewardTotal() uint32 {
	if m != nil {
		return m.UsrRewardTotal
	}
	return 0
}

func (m *ChannelTriviaGamePreEnterResp) GetAdminList() []*ChannelTriviaGameAdimn {
	if m != nil {
		return m.AdminList
	}
	return nil
}

type ChannelTriviaGameQuestion struct {
	ActiveId     uint32   `protobuf:"varint,1,req,name=active_id,json=activeId" json:"active_id"`
	QuestionId   uint32   `protobuf:"varint,2,req,name=question_id,json=questionId" json:"question_id"`
	QuestionIdx  uint32   `protobuf:"varint,3,req,name=question_idx,json=questionIdx" json:"question_idx"`
	QuestionDesc string   `protobuf:"bytes,4,req,name=question_desc,json=questionDesc" json:"question_desc"`
	OptionsList  []string `protobuf:"bytes,5,rep,name=options_list,json=optionsList" json:"options_list,omitempty"`
}

func (m *ChannelTriviaGameQuestion) Reset()         { *m = ChannelTriviaGameQuestion{} }
func (m *ChannelTriviaGameQuestion) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGameQuestion) ProtoMessage()    {}
func (*ChannelTriviaGameQuestion) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{10}
}

func (m *ChannelTriviaGameQuestion) GetActiveId() uint32 {
	if m != nil {
		return m.ActiveId
	}
	return 0
}

func (m *ChannelTriviaGameQuestion) GetQuestionId() uint32 {
	if m != nil {
		return m.QuestionId
	}
	return 0
}

func (m *ChannelTriviaGameQuestion) GetQuestionIdx() uint32 {
	if m != nil {
		return m.QuestionIdx
	}
	return 0
}

func (m *ChannelTriviaGameQuestion) GetQuestionDesc() string {
	if m != nil {
		return m.QuestionDesc
	}
	return ""
}

func (m *ChannelTriviaGameQuestion) GetOptionsList() []string {
	if m != nil {
		return m.OptionsList
	}
	return nil
}

type ChannelTriviaGameAnswerReq struct {
	BaseReq     *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId   uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	ActiveId    uint32      `protobuf:"varint,3,req,name=active_id,json=activeId" json:"active_id"`
	QuestionId  uint32      `protobuf:"varint,4,req,name=question_id,json=questionId" json:"question_id"`
	QuestionIdx uint32      `protobuf:"varint,5,req,name=question_idx,json=questionIdx" json:"question_idx"`
	AnswerId    uint32      `protobuf:"varint,6,req,name=answer_id,json=answerId" json:"answer_id"`
}

func (m *ChannelTriviaGameAnswerReq) Reset()         { *m = ChannelTriviaGameAnswerReq{} }
func (m *ChannelTriviaGameAnswerReq) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGameAnswerReq) ProtoMessage()    {}
func (*ChannelTriviaGameAnswerReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{11}
}

func (m *ChannelTriviaGameAnswerReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelTriviaGameAnswerReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelTriviaGameAnswerReq) GetActiveId() uint32 {
	if m != nil {
		return m.ActiveId
	}
	return 0
}

func (m *ChannelTriviaGameAnswerReq) GetQuestionId() uint32 {
	if m != nil {
		return m.QuestionId
	}
	return 0
}

func (m *ChannelTriviaGameAnswerReq) GetQuestionIdx() uint32 {
	if m != nil {
		return m.QuestionIdx
	}
	return 0
}

func (m *ChannelTriviaGameAnswerReq) GetAnswerId() uint32 {
	if m != nil {
		return m.AnswerId
	}
	return 0
}

type ChannelTriviaGameAnswerResp struct {
	BaseResp                  *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ChannelId                 uint32       `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	ActiveId                  uint32       `protobuf:"varint,3,req,name=active_id,json=activeId" json:"active_id"`
	QuestionIdx               uint32       `protobuf:"varint,4,req,name=question_idx,json=questionIdx" json:"question_idx"`
	ResulutStatus             uint32       `protobuf:"varint,5,req,name=resulut_status,json=resulutStatus" json:"resulut_status"`
	CorrectAnswerId           uint32       `protobuf:"varint,6,req,name=correct_answer_id,json=correctAnswerId" json:"correct_answer_id"`
	IsRevive                  bool         `protobuf:"varint,7,opt,name=is_revive,json=isRevive" json:"is_revive"`
	LeftReviveCnt             uint32       `protobuf:"varint,8,opt,name=left_revive_cnt,json=leftReviveCnt" json:"left_revive_cnt"`
	HistroyMaxPassQuestioncnt uint32       `protobuf:"varint,9,opt,name=histroy_max_pass_questioncnt,json=histroyMaxPassQuestioncnt" json:"histroy_max_pass_questioncnt"`
	IsHistroyMaxRefreshed     bool         `protobuf:"varint,10,opt,name=is_histroy_max_refreshed,json=isHistroyMaxRefreshed" json:"is_histroy_max_refreshed"`
}

func (m *ChannelTriviaGameAnswerResp) Reset()         { *m = ChannelTriviaGameAnswerResp{} }
func (m *ChannelTriviaGameAnswerResp) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGameAnswerResp) ProtoMessage()    {}
func (*ChannelTriviaGameAnswerResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{12}
}

func (m *ChannelTriviaGameAnswerResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelTriviaGameAnswerResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelTriviaGameAnswerResp) GetActiveId() uint32 {
	if m != nil {
		return m.ActiveId
	}
	return 0
}

func (m *ChannelTriviaGameAnswerResp) GetQuestionIdx() uint32 {
	if m != nil {
		return m.QuestionIdx
	}
	return 0
}

func (m *ChannelTriviaGameAnswerResp) GetResulutStatus() uint32 {
	if m != nil {
		return m.ResulutStatus
	}
	return 0
}

func (m *ChannelTriviaGameAnswerResp) GetCorrectAnswerId() uint32 {
	if m != nil {
		return m.CorrectAnswerId
	}
	return 0
}

func (m *ChannelTriviaGameAnswerResp) GetIsRevive() bool {
	if m != nil {
		return m.IsRevive
	}
	return false
}

func (m *ChannelTriviaGameAnswerResp) GetLeftReviveCnt() uint32 {
	if m != nil {
		return m.LeftReviveCnt
	}
	return 0
}

func (m *ChannelTriviaGameAnswerResp) GetHistroyMaxPassQuestioncnt() uint32 {
	if m != nil {
		return m.HistroyMaxPassQuestioncnt
	}
	return 0
}

func (m *ChannelTriviaGameAnswerResp) GetIsHistroyMaxRefreshed() bool {
	if m != nil {
		return m.IsHistroyMaxRefreshed
	}
	return false
}

// 主持人流程条
type ChannelTriviaGameGetPhaseListReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *ChannelTriviaGameGetPhaseListReq) Reset()         { *m = ChannelTriviaGameGetPhaseListReq{} }
func (m *ChannelTriviaGameGetPhaseListReq) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGameGetPhaseListReq) ProtoMessage()    {}
func (*ChannelTriviaGameGetPhaseListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{13}
}

func (m *ChannelTriviaGameGetPhaseListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelTriviaGameGetPhaseListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChannelTriviaGameGetPhaseListResp struct {
	BaseResp  *ga.BaseResp           `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GameInfo  *ChannelTriviaGameInfo `protobuf:"bytes,2,req,name=game_info,json=gameInfo" json:"game_info,omitempty"`
	PhaseList []*PhaseEntry          `protobuf:"bytes,3,rep,name=phase_list,json=phaseList" json:"phase_list,omitempty"`
}

func (m *ChannelTriviaGameGetPhaseListResp) Reset()         { *m = ChannelTriviaGameGetPhaseListResp{} }
func (m *ChannelTriviaGameGetPhaseListResp) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGameGetPhaseListResp) ProtoMessage()    {}
func (*ChannelTriviaGameGetPhaseListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{14}
}

func (m *ChannelTriviaGameGetPhaseListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelTriviaGameGetPhaseListResp) GetGameInfo() *ChannelTriviaGameInfo {
	if m != nil {
		return m.GameInfo
	}
	return nil
}

func (m *ChannelTriviaGameGetPhaseListResp) GetPhaseList() []*PhaseEntry {
	if m != nil {
		return m.PhaseList
	}
	return nil
}

// 流程切换
type ChannelTriviaGamePhaseForwardReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	ActiveId  uint32      `protobuf:"varint,3,req,name=active_id,json=activeId" json:"active_id"`
	PhaseIdx  uint32      `protobuf:"varint,4,opt,name=phase_idx,json=phaseIdx" json:"phase_idx"`
}

func (m *ChannelTriviaGamePhaseForwardReq) Reset()         { *m = ChannelTriviaGamePhaseForwardReq{} }
func (m *ChannelTriviaGamePhaseForwardReq) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGamePhaseForwardReq) ProtoMessage()    {}
func (*ChannelTriviaGamePhaseForwardReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{15}
}

func (m *ChannelTriviaGamePhaseForwardReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelTriviaGamePhaseForwardReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelTriviaGamePhaseForwardReq) GetActiveId() uint32 {
	if m != nil {
		return m.ActiveId
	}
	return 0
}

func (m *ChannelTriviaGamePhaseForwardReq) GetPhaseIdx() uint32 {
	if m != nil {
		return m.PhaseIdx
	}
	return 0
}

type ChannelTriviaGamePhaseForwardResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	PhaseIdx uint32       `protobuf:"varint,5,req,name=phase_idx,json=phaseIdx" json:"phase_idx"`
}

func (m *ChannelTriviaGamePhaseForwardResp) Reset()         { *m = ChannelTriviaGamePhaseForwardResp{} }
func (m *ChannelTriviaGamePhaseForwardResp) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGamePhaseForwardResp) ProtoMessage()    {}
func (*ChannelTriviaGamePhaseForwardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{16}
}

func (m *ChannelTriviaGamePhaseForwardResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelTriviaGamePhaseForwardResp) GetPhaseIdx() uint32 {
	if m != nil {
		return m.PhaseIdx
	}
	return 0
}

// 显示答案
type ChannelTriviaGameShowSolutionReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId  uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	ActiveId   uint32      `protobuf:"varint,3,req,name=active_id,json=activeId" json:"active_id"`
	QuestionId uint32      `protobuf:"varint,4,req,name=question_id,json=questionId" json:"question_id"`
}

func (m *ChannelTriviaGameShowSolutionReq) Reset()         { *m = ChannelTriviaGameShowSolutionReq{} }
func (m *ChannelTriviaGameShowSolutionReq) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGameShowSolutionReq) ProtoMessage()    {}
func (*ChannelTriviaGameShowSolutionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{17}
}

func (m *ChannelTriviaGameShowSolutionReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelTriviaGameShowSolutionReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelTriviaGameShowSolutionReq) GetActiveId() uint32 {
	if m != nil {
		return m.ActiveId
	}
	return 0
}

func (m *ChannelTriviaGameShowSolutionReq) GetQuestionId() uint32 {
	if m != nil {
		return m.QuestionId
	}
	return 0
}

type ChannelTriviaGameShowSolutionResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *ChannelTriviaGameShowSolutionResp) Reset()         { *m = ChannelTriviaGameShowSolutionResp{} }
func (m *ChannelTriviaGameShowSolutionResp) String() string { return proto.CompactTextString(m) }
func (*ChannelTriviaGameShowSolutionResp) ProtoMessage()    {}
func (*ChannelTriviaGameShowSolutionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelTriviaGame_, []int{18}
}

func (m *ChannelTriviaGameShowSolutionResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*PhaseTimer)(nil), "ga.PhaseTimer")
	proto.RegisterType((*PhaseAnimation)(nil), "ga.PhaseAnimation")
	proto.RegisterType((*PhaseQuestion)(nil), "ga.PhaseQuestion")
	proto.RegisterType((*PhaseEntry)(nil), "ga.PhaseEntry")
	proto.RegisterType((*ChannelTriviaGameInfo)(nil), "ga.ChannelTriviaGameInfo")
	proto.RegisterType((*ChannelTriviaGameEnterReq)(nil), "ga.ChannelTriviaGameEnterReq")
	proto.RegisterType((*ChannelTriviaGameEnterResp)(nil), "ga.ChannelTriviaGameEnterResp")
	proto.RegisterType((*ChannelTriviaGamePreEnterReq)(nil), "ga.ChannelTriviaGamePreEnterReq")
	proto.RegisterType((*ChannelTriviaGameAdimn)(nil), "ga.ChannelTriviaGameAdimn")
	proto.RegisterType((*ChannelTriviaGamePreEnterResp)(nil), "ga.ChannelTriviaGamePreEnterResp")
	proto.RegisterType((*ChannelTriviaGameQuestion)(nil), "ga.ChannelTriviaGameQuestion")
	proto.RegisterType((*ChannelTriviaGameAnswerReq)(nil), "ga.ChannelTriviaGameAnswerReq")
	proto.RegisterType((*ChannelTriviaGameAnswerResp)(nil), "ga.ChannelTriviaGameAnswerResp")
	proto.RegisterType((*ChannelTriviaGameGetPhaseListReq)(nil), "ga.ChannelTriviaGameGetPhaseListReq")
	proto.RegisterType((*ChannelTriviaGameGetPhaseListResp)(nil), "ga.ChannelTriviaGameGetPhaseListResp")
	proto.RegisterType((*ChannelTriviaGamePhaseForwardReq)(nil), "ga.ChannelTriviaGamePhaseForwardReq")
	proto.RegisterType((*ChannelTriviaGamePhaseForwardResp)(nil), "ga.ChannelTriviaGamePhaseForwardResp")
	proto.RegisterType((*ChannelTriviaGameShowSolutionReq)(nil), "ga.ChannelTriviaGameShowSolutionReq")
	proto.RegisterType((*ChannelTriviaGameShowSolutionResp)(nil), "ga.ChannelTriviaGameShowSolutionResp")
	proto.RegisterEnum("ga.ETriviaGameAnswerResultStatus", ETriviaGameAnswerResultStatus_name, ETriviaGameAnswerResultStatus_value)
	proto.RegisterEnum("ga.ETriviaGameAnswerId", ETriviaGameAnswerId_name, ETriviaGameAnswerId_value)
	proto.RegisterEnum("ga.PhaseAnimation_AnimationId", PhaseAnimation_AnimationId_name, PhaseAnimation_AnimationId_value)
	proto.RegisterEnum("ga.PhaseEntry_PhaseType", PhaseEntry_PhaseType_name, PhaseEntry_PhaseType_value)
}
func (m *PhaseTimer) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PhaseTimer) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.EndTimestamp))
	return i, nil
}

func (m *PhaseAnimation) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PhaseAnimation) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.AnimaId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(len(m.Text)))
	i += copy(dAtA[i:], m.Text)
	return i, nil
}

func (m *PhaseQuestion) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PhaseQuestion) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.QuestionId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.QuestionIdx))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.TipTime))
	dAtA[i] = 0x20
	i++
	if m.IsShowSolution {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *PhaseEntry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PhaseEntry) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.PhaseIdx))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.PhaseType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(len(m.PhaseName)))
	i += copy(dAtA[i:], m.PhaseName)
	if m.PhaseBin != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(len(m.PhaseBin)))
		i += copy(dAtA[i:], m.PhaseBin)
	}
	return i, nil
}

func (m *ChannelTriviaGameInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGameInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ActId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.StartTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.Reward))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.QuestionNum))
	return i, nil
}

func (m *ChannelTriviaGameEnterReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGameEnterReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *ChannelTriviaGameEnterResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGameEnterResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ServerTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.SessionId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ChannelType))
	if m.GameInfo != nil {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.GameInfo.Size()))
		n3, err := m.GameInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.CurrentPhase != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.CurrentPhase.Size()))
		n4, err := m.CurrentPhase.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x48
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ChannelMemberCnt))
	dAtA[i] = 0x50
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.RemainReviveCnt))
	dAtA[i] = 0x58
	i++
	if m.IsAnswerQualify {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ChannelTriviaGamePreEnterReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGamePreEnterReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *ChannelTriviaGameAdimn) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGameAdimn) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.Role))
	return i, nil
}

func (m *ChannelTriviaGamePreEnterResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGamePreEnterResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ServerTime))
	if m.GameInfo != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.GameInfo.Size()))
		n7, err := m.GameInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if m.CurrentPhase != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.CurrentPhase.Size()))
		n8, err := m.CurrentPhase.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.LeftReviveCnt))
	dAtA[i] = 0x38
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.UsrRewardTotal))
	if len(m.AdminList) > 0 {
		for _, msg := range m.AdminList {
			dAtA[i] = 0x42
			i++
			i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ChannelTriviaGameQuestion) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGameQuestion) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ActiveId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.QuestionId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.QuestionIdx))
	dAtA[i] = 0x22
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(len(m.QuestionDesc)))
	i += copy(dAtA[i:], m.QuestionDesc)
	if len(m.OptionsList) > 0 {
		for _, s := range m.OptionsList {
			dAtA[i] = 0x2a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *ChannelTriviaGameAnswerReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGameAnswerReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ActiveId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.QuestionId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.QuestionIdx))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.AnswerId))
	return i, nil
}

func (m *ChannelTriviaGameAnswerResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGameAnswerResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n10, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ActiveId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.QuestionIdx))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ResulutStatus))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.CorrectAnswerId))
	dAtA[i] = 0x38
	i++
	if m.IsRevive {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x40
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.LeftReviveCnt))
	dAtA[i] = 0x48
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.HistroyMaxPassQuestioncnt))
	dAtA[i] = 0x50
	i++
	if m.IsHistroyMaxRefreshed {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ChannelTriviaGameGetPhaseListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGameGetPhaseListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n11, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *ChannelTriviaGameGetPhaseListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGameGetPhaseListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n12, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	if m.GameInfo == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.GameInfo.Size()))
		n13, err := m.GameInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	if len(m.PhaseList) > 0 {
		for _, msg := range m.PhaseList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ChannelTriviaGamePhaseForwardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGamePhaseForwardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n14, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ActiveId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.PhaseIdx))
	return i, nil
}

func (m *ChannelTriviaGamePhaseForwardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGamePhaseForwardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n15, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.PhaseIdx))
	return i, nil
}

func (m *ChannelTriviaGameShowSolutionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGameShowSolutionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n16, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.ActiveId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.QuestionId))
	return i, nil
}

func (m *ChannelTriviaGameShowSolutionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTriviaGameShowSolutionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelTriviaGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n17, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	return i, nil
}

func encodeFixed64ChannelTriviaGame_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32ChannelTriviaGame_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannelTriviaGame_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *PhaseTimer) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelTriviaGame_(uint64(m.EndTimestamp))
	return n
}

func (m *PhaseAnimation) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelTriviaGame_(uint64(m.AnimaId))
	l = len(m.Text)
	n += 1 + l + sovChannelTriviaGame_(uint64(l))
	return n
}

func (m *PhaseQuestion) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelTriviaGame_(uint64(m.QuestionId))
	n += 1 + sovChannelTriviaGame_(uint64(m.QuestionIdx))
	n += 1 + sovChannelTriviaGame_(uint64(m.TipTime))
	n += 2
	return n
}

func (m *PhaseEntry) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelTriviaGame_(uint64(m.PhaseIdx))
	n += 1 + sovChannelTriviaGame_(uint64(m.PhaseType))
	l = len(m.PhaseName)
	n += 1 + l + sovChannelTriviaGame_(uint64(l))
	if m.PhaseBin != nil {
		l = len(m.PhaseBin)
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	return n
}

func (m *ChannelTriviaGameInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelTriviaGame_(uint64(m.ActId))
	n += 1 + sovChannelTriviaGame_(uint64(m.StartTime))
	n += 1 + sovChannelTriviaGame_(uint64(m.Reward))
	n += 1 + sovChannelTriviaGame_(uint64(m.QuestionNum))
	return n
}

func (m *ChannelTriviaGameEnterReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	n += 1 + sovChannelTriviaGame_(uint64(m.ChannelId))
	return n
}

func (m *ChannelTriviaGameEnterResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	n += 1 + sovChannelTriviaGame_(uint64(m.ServerTime))
	n += 1 + sovChannelTriviaGame_(uint64(m.ChannelId))
	n += 1 + sovChannelTriviaGame_(uint64(m.SessionId))
	n += 1 + sovChannelTriviaGame_(uint64(m.ChannelDisplayId))
	n += 1 + sovChannelTriviaGame_(uint64(m.ChannelType))
	if m.GameInfo != nil {
		l = m.GameInfo.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	if m.CurrentPhase != nil {
		l = m.CurrentPhase.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	n += 1 + sovChannelTriviaGame_(uint64(m.ChannelMemberCnt))
	n += 1 + sovChannelTriviaGame_(uint64(m.RemainReviveCnt))
	n += 2
	return n
}

func (m *ChannelTriviaGamePreEnterReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	n += 1 + sovChannelTriviaGame_(uint64(m.ChannelId))
	return n
}

func (m *ChannelTriviaGameAdimn) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelTriviaGame_(uint64(m.Uid))
	n += 1 + sovChannelTriviaGame_(uint64(m.Role))
	return n
}

func (m *ChannelTriviaGamePreEnterResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	n += 1 + sovChannelTriviaGame_(uint64(m.ChannelId))
	n += 1 + sovChannelTriviaGame_(uint64(m.ServerTime))
	if m.GameInfo != nil {
		l = m.GameInfo.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	if m.CurrentPhase != nil {
		l = m.CurrentPhase.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	n += 1 + sovChannelTriviaGame_(uint64(m.LeftReviveCnt))
	n += 1 + sovChannelTriviaGame_(uint64(m.UsrRewardTotal))
	if len(m.AdminList) > 0 {
		for _, e := range m.AdminList {
			l = e.Size()
			n += 1 + l + sovChannelTriviaGame_(uint64(l))
		}
	}
	return n
}

func (m *ChannelTriviaGameQuestion) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelTriviaGame_(uint64(m.ActiveId))
	n += 1 + sovChannelTriviaGame_(uint64(m.QuestionId))
	n += 1 + sovChannelTriviaGame_(uint64(m.QuestionIdx))
	l = len(m.QuestionDesc)
	n += 1 + l + sovChannelTriviaGame_(uint64(l))
	if len(m.OptionsList) > 0 {
		for _, s := range m.OptionsList {
			l = len(s)
			n += 1 + l + sovChannelTriviaGame_(uint64(l))
		}
	}
	return n
}

func (m *ChannelTriviaGameAnswerReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	n += 1 + sovChannelTriviaGame_(uint64(m.ChannelId))
	n += 1 + sovChannelTriviaGame_(uint64(m.ActiveId))
	n += 1 + sovChannelTriviaGame_(uint64(m.QuestionId))
	n += 1 + sovChannelTriviaGame_(uint64(m.QuestionIdx))
	n += 1 + sovChannelTriviaGame_(uint64(m.AnswerId))
	return n
}

func (m *ChannelTriviaGameAnswerResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	n += 1 + sovChannelTriviaGame_(uint64(m.ChannelId))
	n += 1 + sovChannelTriviaGame_(uint64(m.ActiveId))
	n += 1 + sovChannelTriviaGame_(uint64(m.QuestionIdx))
	n += 1 + sovChannelTriviaGame_(uint64(m.ResulutStatus))
	n += 1 + sovChannelTriviaGame_(uint64(m.CorrectAnswerId))
	n += 2
	n += 1 + sovChannelTriviaGame_(uint64(m.LeftReviveCnt))
	n += 1 + sovChannelTriviaGame_(uint64(m.HistroyMaxPassQuestioncnt))
	n += 2
	return n
}

func (m *ChannelTriviaGameGetPhaseListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	n += 1 + sovChannelTriviaGame_(uint64(m.ChannelId))
	return n
}

func (m *ChannelTriviaGameGetPhaseListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	if m.GameInfo != nil {
		l = m.GameInfo.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	if len(m.PhaseList) > 0 {
		for _, e := range m.PhaseList {
			l = e.Size()
			n += 1 + l + sovChannelTriviaGame_(uint64(l))
		}
	}
	return n
}

func (m *ChannelTriviaGamePhaseForwardReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	n += 1 + sovChannelTriviaGame_(uint64(m.ChannelId))
	n += 1 + sovChannelTriviaGame_(uint64(m.ActiveId))
	n += 1 + sovChannelTriviaGame_(uint64(m.PhaseIdx))
	return n
}

func (m *ChannelTriviaGamePhaseForwardResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	n += 1 + sovChannelTriviaGame_(uint64(m.PhaseIdx))
	return n
}

func (m *ChannelTriviaGameShowSolutionReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	n += 1 + sovChannelTriviaGame_(uint64(m.ChannelId))
	n += 1 + sovChannelTriviaGame_(uint64(m.ActiveId))
	n += 1 + sovChannelTriviaGame_(uint64(m.QuestionId))
	return n
}

func (m *ChannelTriviaGameShowSolutionResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelTriviaGame_(uint64(l))
	}
	return n
}

func sovChannelTriviaGame_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannelTriviaGame_(x uint64) (n int) {
	return sovChannelTriviaGame_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *PhaseTimer) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PhaseTimer: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PhaseTimer: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTimestamp", wireType)
			}
			m.EndTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTimestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("end_timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PhaseAnimation) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PhaseAnimation: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PhaseAnimation: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AnimaId", wireType)
			}
			m.AnimaId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AnimaId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("anima_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PhaseQuestion) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PhaseQuestion: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PhaseQuestion: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionId", wireType)
			}
			m.QuestionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuestionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionIdx", wireType)
			}
			m.QuestionIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuestionIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TipTime", wireType)
			}
			m.TipTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TipTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsShowSolution", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsShowSolution = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("question_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("question_idx")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("tip_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PhaseEntry) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PhaseEntry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PhaseEntry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhaseIdx", wireType)
			}
			m.PhaseIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhaseIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhaseType", wireType)
			}
			m.PhaseType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhaseType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhaseName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhaseName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhaseBin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhaseBin = append(m.PhaseBin[:0], dAtA[iNdEx:postIndex]...)
			if m.PhaseBin == nil {
				m.PhaseBin = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("phase_idx")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGameInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGameInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGameInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActId", wireType)
			}
			m.ActId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reward", wireType)
			}
			m.Reward = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Reward |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionNum", wireType)
			}
			m.QuestionNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuestionNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("act_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("reward")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("question_num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGameEnterReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGameEnterReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGameEnterReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGameEnterResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGameEnterResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGameEnterResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTime", wireType)
			}
			m.ServerTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SessionId", wireType)
			}
			m.SessionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SessionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GameInfo == nil {
				m.GameInfo = &ChannelTriviaGameInfo{}
			}
			if err := m.GameInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentPhase", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CurrentPhase == nil {
				m.CurrentPhase = &PhaseEntry{}
			}
			if err := m.CurrentPhase.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelMemberCnt", wireType)
			}
			m.ChannelMemberCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelMemberCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainReviveCnt", wireType)
			}
			m.RemainReviveCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainReviveCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAnswerQualify", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAnswerQualify = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("session_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGamePreEnterReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGamePreEnterReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGamePreEnterReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGameAdimn) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGameAdimn: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGameAdimn: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Role", wireType)
			}
			m.Role = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Role |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("role")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGamePreEnterResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGamePreEnterResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGamePreEnterResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTime", wireType)
			}
			m.ServerTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GameInfo == nil {
				m.GameInfo = &ChannelTriviaGameInfo{}
			}
			if err := m.GameInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentPhase", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CurrentPhase == nil {
				m.CurrentPhase = &PhaseEntry{}
			}
			if err := m.CurrentPhase.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeftReviveCnt", wireType)
			}
			m.LeftReviveCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeftReviveCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UsrRewardTotal", wireType)
			}
			m.UsrRewardTotal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UsrRewardTotal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdminList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AdminList = append(m.AdminList, &ChannelTriviaGameAdimn{})
			if err := m.AdminList[len(m.AdminList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGameQuestion) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGameQuestion: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGameQuestion: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveId", wireType)
			}
			m.ActiveId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActiveId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionId", wireType)
			}
			m.QuestionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuestionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionIdx", wireType)
			}
			m.QuestionIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuestionIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.QuestionDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptionsList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OptionsList = append(m.OptionsList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("active_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("question_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("question_idx")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("question_desc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGameAnswerReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGameAnswerReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGameAnswerReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveId", wireType)
			}
			m.ActiveId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActiveId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionId", wireType)
			}
			m.QuestionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuestionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionIdx", wireType)
			}
			m.QuestionIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuestionIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AnswerId", wireType)
			}
			m.AnswerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AnswerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("active_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("question_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("question_idx")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("answer_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGameAnswerResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGameAnswerResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGameAnswerResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveId", wireType)
			}
			m.ActiveId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActiveId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionIdx", wireType)
			}
			m.QuestionIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuestionIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResulutStatus", wireType)
			}
			m.ResulutStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResulutStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CorrectAnswerId", wireType)
			}
			m.CorrectAnswerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CorrectAnswerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsRevive", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsRevive = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeftReviveCnt", wireType)
			}
			m.LeftReviveCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeftReviveCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HistroyMaxPassQuestioncnt", wireType)
			}
			m.HistroyMaxPassQuestioncnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HistroyMaxPassQuestioncnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsHistroyMaxRefreshed", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsHistroyMaxRefreshed = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("active_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("question_idx")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("resulut_status")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("correct_answer_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGameGetPhaseListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGameGetPhaseListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGameGetPhaseListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGameGetPhaseListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGameGetPhaseListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGameGetPhaseListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GameInfo == nil {
				m.GameInfo = &ChannelTriviaGameInfo{}
			}
			if err := m.GameInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhaseList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhaseList = append(m.PhaseList, &PhaseEntry{})
			if err := m.PhaseList[len(m.PhaseList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGamePhaseForwardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGamePhaseForwardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGamePhaseForwardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveId", wireType)
			}
			m.ActiveId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActiveId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhaseIdx", wireType)
			}
			m.PhaseIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhaseIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("active_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGamePhaseForwardResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGamePhaseForwardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGamePhaseForwardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhaseIdx", wireType)
			}
			m.PhaseIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhaseIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("phase_idx")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGameShowSolutionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGameShowSolutionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGameShowSolutionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveId", wireType)
			}
			m.ActiveId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActiveId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionId", wireType)
			}
			m.QuestionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuestionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("active_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("question_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTriviaGameShowSolutionResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTriviaGameShowSolutionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTriviaGameShowSolutionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelTriviaGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelTriviaGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChannelTriviaGame_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannelTriviaGame_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelTriviaGame_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannelTriviaGame_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannelTriviaGame_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannelTriviaGame_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannelTriviaGame_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannelTriviaGame_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("channel_trivia_game_.proto", fileDescriptorChannelTriviaGame_) }

var fileDescriptorChannelTriviaGame_ = []byte{
	// 1425 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x58, 0xdf, 0x72, 0xdb, 0xc4,
	0x17, 0xae, 0x2c, 0x27, 0xb5, 0x8f, 0xe3, 0x44, 0x3f, 0xf5, 0xd7, 0xa2, 0xa6, 0x69, 0xea, 0x88,
	0xd2, 0xba, 0x05, 0x5c, 0x26, 0x0c, 0x30, 0x5c, 0x70, 0xe1, 0x24, 0xa6, 0x98, 0x69, 0xd2, 0x54,
	0x71, 0xcb, 0x0c, 0x37, 0x3b, 0x1b, 0x6b, 0xe3, 0xec, 0x60, 0xfd, 0xf1, 0xee, 0x2a, 0x89, 0x1f,
	0x80, 0x7b, 0xae, 0x19, 0x2e, 0x60, 0x78, 0x01, 0x6e, 0x7a, 0xc1, 0x0b, 0x30, 0xbd, 0xe4, 0x01,
	0x18, 0x86, 0x29, 0x0f, 0xc0, 0x23, 0x94, 0xd1, 0x4a, 0xb2, 0x25, 0x47, 0x6e, 0x9c, 0xa1, 0x1d,
	0xb8, 0xb3, 0xce, 0xf9, 0x76, 0xf7, 0xfc, 0xf9, 0xce, 0xb7, 0x92, 0x61, 0xb9, 0x7b, 0x88, 0x5d,
	0x97, 0xf4, 0x91, 0x60, 0xf4, 0x88, 0x62, 0xd4, 0xc3, 0x0e, 0x41, 0x0d, 0x9f, 0x79, 0xc2, 0xd3,
	0x0b, 0x3d, 0xbc, 0x5c, 0xed, 0x61, 0xb4, 0x8f, 0x39, 0x89, 0x4c, 0xe6, 0x47, 0x00, 0xbb, 0x87,
	0x98, 0x93, 0x0e, 0x75, 0x08, 0xd3, 0xef, 0x40, 0x95, 0xb8, 0x36, 0x12, 0xd4, 0x21, 0x5c, 0x60,
	0xc7, 0x37, 0x94, 0x5a, 0xa1, 0x5e, 0xdd, 0x28, 0x3e, 0xfb, 0xfd, 0xc6, 0x05, 0x6b, 0x81, 0xb8,
	0x76, 0x27, 0xf1, 0x98, 0x3f, 0x2a, 0xb0, 0x28, 0x57, 0x36, 0x5d, 0xea, 0x60, 0x41, 0x3d, 0x57,
	0xbf, 0x01, 0x25, 0x1c, 0x3e, 0x20, 0x6a, 0x67, 0x16, 0x5e, 0x94, 0xd6, 0xb6, 0xad, 0x1b, 0x50,
	0x14, 0xe4, 0x44, 0x18, 0x85, 0x9a, 0x52, 0x2f, 0xc7, 0x4e, 0x69, 0x31, 0x9f, 0x40, 0x65, 0xb4,
	0x4f, 0xdb, 0xd6, 0xff, 0x0f, 0xda, 0xe8, 0x11, 0x59, 0xe4, 0x18, 0x33, 0x5b, 0x53, 0xb2, 0xd6,
	0x6d, 0x6a, 0xdb, 0x7d, 0xa2, 0x15, 0xf4, 0x6b, 0xf0, 0xc6, 0xd8, 0xfa, 0x00, 0x73, 0x81, 0x1e,
	0x05, 0x84, 0x87, 0x4f, 0x9a, 0x6a, 0xfe, 0xa4, 0x40, 0x55, 0x46, 0x99, 0xd8, 0xf4, 0xb7, 0xa0,
	0x32, 0x88, 0x7f, 0x4f, 0xc6, 0x09, 0x89, 0xa3, 0x6d, 0xeb, 0xb7, 0x61, 0x21, 0x05, 0x3b, 0x31,
	0x0a, 0x29, 0x5c, 0x65, 0x8c, 0x3b, 0x09, 0x93, 0x16, 0xd4, 0x97, 0x25, 0x33, 0xd4, 0x74, 0xd2,
	0x82, 0xfa, 0x61, 0xb5, 0xf4, 0x06, 0x68, 0x94, 0x23, 0x7e, 0xe8, 0x1d, 0x23, 0xee, 0xf5, 0x83,
	0x70, 0x9d, 0x51, 0xac, 0x29, 0xf5, 0x52, 0x0c, 0x5c, 0xa4, 0x7c, 0xef, 0xd0, 0x3b, 0xde, 0x8b,
	0x7d, 0xe6, 0xd7, 0x85, 0xb8, 0x25, 0x2d, 0x57, 0xb0, 0xa1, 0xbe, 0x06, 0x65, 0x3f, 0x7c, 0x92,
	0x51, 0xa4, 0xa3, 0x2d, 0x49, 0x73, 0x18, 0xc2, 0x9b, 0x00, 0x11, 0x44, 0x0c, 0x7d, 0x22, 0x8b,
	0x9b, 0x60, 0xa2, 0xa5, 0x9d, 0xa1, 0x4f, 0xc6, 0x20, 0x17, 0xcb, 0x48, 0xc7, 0x1d, 0x88, 0x40,
	0x3b, 0xd8, 0x21, 0xe3, 0xc3, 0xf6, 0x69, 0x14, 0xe4, 0x42, 0xe6, 0xb0, 0x0d, 0xea, 0x9a, 0x3d,
	0x28, 0xef, 0x8e, 0x36, 0x5d, 0x1c, 0x6d, 0xea, 0xb9, 0x44, 0xbb, 0xa0, 0x2f, 0x41, 0x25, 0x8e,
	0x24, 0xa4, 0x93, 0xa6, 0xe8, 0x97, 0x60, 0x29, 0x32, 0xe0, 0xa4, 0x45, 0x5a, 0x41, 0xd7, 0x61,
	0x31, 0x32, 0x0e, 0x46, 0x8d, 0xd2, 0x35, 0x58, 0x88, 0x6c, 0xc7, 0xd4, 0x75, 0x09, 0xd3, 0x8a,
	0xe6, 0xf7, 0x0a, 0x5c, 0xde, 0x8c, 0xb8, 0xdc, 0x91, 0x54, 0xbe, 0x8f, 0x1d, 0xd2, 0x76, 0x0f,
	0x3c, 0xfd, 0x1a, 0xcc, 0xe3, 0xae, 0x98, 0xec, 0xde, 0x1c, 0xee, 0x8a, 0xb6, 0x1d, 0xe6, 0xc9,
	0x05, 0x66, 0x22, 0xea, 0x48, 0xba, 0x6d, 0x65, 0x69, 0x97, 0x3d, 0x59, 0x81, 0x79, 0x26, 0x59,
	0x95, 0x69, 0x59, 0x6c, 0xcb, 0xf4, 0xde, 0x0d, 0x1c, 0xa3, 0x98, 0xd7, 0xfb, 0x9d, 0xc0, 0x31,
	0x0f, 0xe1, 0xea, 0xa9, 0x08, 0x5b, 0xae, 0x20, 0xcc, 0x22, 0x03, 0xfd, 0x16, 0x94, 0xc2, 0x39,
	0x43, 0x8c, 0x0c, 0x64, 0x9c, 0x95, 0xf5, 0x4a, 0xa3, 0x87, 0x1b, 0x1b, 0x98, 0x13, 0x8b, 0x0c,
	0xac, 0x8b, 0xfb, 0xd1, 0x8f, 0x30, 0xe0, 0x64, 0x64, 0xa9, 0x9d, 0x0d, 0x38, 0xb6, 0xb7, 0x6d,
	0xf3, 0xdb, 0x22, 0x2c, 0x4f, 0x3b, 0x8a, 0xfb, 0xfa, 0x1d, 0x28, 0xc7, 0x67, 0x71, 0x3f, 0x3e,
	0x6c, 0x61, 0x7c, 0x18, 0xf7, 0xad, 0xd2, 0x7e, 0xfc, 0x2b, 0xe4, 0x3f, 0x27, 0xec, 0x88, 0xb0,
	0xa4, 0x40, 0x4a, 0x5d, 0x4d, 0xf8, 0x1f, 0x39, 0x64, 0x85, 0xb2, 0x51, 0xa9, 0xb9, 0x51, 0xc9,
	0x5a, 0x13, 0xce, 0xe3, 0x51, 0x2a, 0x66, 0x6a, 0x1d, 0xd9, 0xdb, 0xb6, 0xbe, 0x0e, 0x7a, 0xb2,
	0x93, 0x4d, 0xb9, 0xdf, 0xc7, 0xc3, 0x10, 0x3c, 0x97, 0x62, 0xa9, 0x16, 0xfb, 0xb7, 0x22, 0x77,
	0x34, 0x7d, 0x23, 0x19, 0x0b, 0x39, 0x3d, 0x9f, 0x42, 0x57, 0x62, 0x8f, 0x24, 0xe0, 0x87, 0x50,
	0x96, 0x02, 0x47, 0xdd, 0x03, 0xcf, 0xb8, 0x58, 0x53, 0xea, 0x95, 0xf5, 0xab, 0x61, 0xe2, 0xb9,
	0xc4, 0xb1, 0x4a, 0xbd, 0x84, 0x42, 0xef, 0x43, 0xb5, 0x1b, 0x30, 0x46, 0x5c, 0x81, 0x24, 0xed,
	0x8c, 0x92, 0x5c, 0xbb, 0x18, 0xae, 0x1d, 0x0f, 0x9f, 0xb5, 0x10, 0x83, 0xa4, 0x29, 0x9d, 0x89,
	0x43, 0x9c, 0x7d, 0xc2, 0x50, 0xd7, 0x15, 0x46, 0x39, 0x27, 0x93, 0x6d, 0xe9, 0xde, 0x74, 0x85,
	0xfe, 0x1e, 0xfc, 0x8f, 0x11, 0x07, 0x53, 0x17, 0x31, 0x72, 0x44, 0x8f, 0x88, 0x5c, 0x02, 0xa9,
	0x25, 0x4b, 0x91, 0xdb, 0x92, 0xde, 0x78, 0x05, 0xe5, 0x08, 0xbb, 0xfc, 0x98, 0x30, 0x34, 0x08,
	0x70, 0x9f, 0x1e, 0x0c, 0x8d, 0x4a, 0x4a, 0x30, 0x96, 0x28, 0x6f, 0x4a, 0xef, 0xa3, 0xc8, 0x69,
	0x7e, 0x05, 0x2b, 0xa7, 0xf2, 0xdd, 0x65, 0xaf, 0x89, 0x89, 0x9f, 0xc3, 0x95, 0x53, 0x87, 0x35,
	0x6d, 0xea, 0xb8, 0xfa, 0x15, 0x50, 0x83, 0x89, 0x99, 0x0c, 0x0d, 0xa1, 0xea, 0x33, 0xaf, 0x9f,
	0x9d, 0x45, 0x69, 0x31, 0xbf, 0x53, 0xe1, 0xfa, 0x4b, 0x22, 0x3f, 0x1f, 0xb1, 0x67, 0x89, 0x7e,
	0x92, 0xfd, 0xea, 0x14, 0xf6, 0x67, 0x68, 0x55, 0xfc, 0x07, 0xb4, 0x9a, 0x9b, 0x81, 0x56, 0xef,
	0xc0, 0x52, 0x9f, 0x1c, 0x88, 0x34, 0x41, 0xd2, 0x7c, 0xaf, 0x86, 0xce, 0x31, 0x3d, 0x1a, 0xa0,
	0x05, 0x9c, 0xa1, 0x48, 0xaa, 0x90, 0xf0, 0x04, 0xee, 0x4b, 0xe2, 0x27, 0xf0, 0xc5, 0x80, 0xb3,
	0xe8, 0xc6, 0xec, 0x84, 0x3e, 0xfd, 0x63, 0x00, 0x6c, 0x3b, 0xd4, 0x45, 0x7d, 0xca, 0x85, 0x51,
	0xaa, 0xa9, 0xf5, 0xca, 0xfa, 0x72, 0x6e, 0x2e, 0xb2, 0x8b, 0x56, 0x59, 0xa2, 0x1f, 0x50, 0x2e,
	0xcc, 0xdf, 0x94, 0x1c, 0x7d, 0x1b, 0x5d, 0xa4, 0x6b, 0x50, 0xc6, 0x5d, 0x11, 0x46, 0x3c, 0xd1,
	0xf4, 0x52, 0x64, 0x8e, 0xaa, 0x9d, 0xbe, 0x6b, 0x0b, 0x33, 0xde, 0xb5, 0xea, 0xb4, 0xbb, 0xf6,
	0x0e, 0x54, 0x47, 0x40, 0x9b, 0xf0, 0xae, 0x94, 0x9c, 0xe4, 0x1a, 0x1b, 0xed, 0xb1, 0x45, 0x78,
	0x57, 0x5f, 0x83, 0x05, 0xcf, 0x0f, 0x9f, 0x78, 0x94, 0xf8, 0x5c, 0x4d, 0xad, 0x97, 0xad, 0x4a,
	0x6c, 0x93, 0xe9, 0xbd, 0x50, 0x72, 0x34, 0x35, 0x9a, 0xac, 0x57, 0x3d, 0x35, 0xd9, 0x62, 0xa9,
	0xb3, 0x14, 0xab, 0x38, 0x63, 0xb1, 0xe6, 0xa6, 0x15, 0x2b, 0x3c, 0x32, 0x12, 0x11, 0x6a, 0x1b,
	0xf3, 0x99, 0x23, 0xa5, 0xb9, 0x6d, 0x9b, 0x7f, 0xa9, 0x70, 0x6d, 0x6a, 0x05, 0x5e, 0xc3, 0xf4,
	0xcd, 0x50, 0x85, 0xc9, 0xf4, 0x8a, 0xd3, 0xd2, 0x7b, 0x1b, 0x16, 0x19, 0xe1, 0x41, 0x3f, 0x10,
	0x88, 0x0b, 0x2c, 0x02, 0x9e, 0xa9, 0x44, 0x35, 0xf6, 0xed, 0x49, 0x57, 0xa8, 0xa9, 0x5d, 0x8f,
	0x31, 0xd2, 0x15, 0x28, 0xbf, 0x26, 0x4b, 0xb1, 0xbb, 0x19, 0x97, 0x26, 0x0c, 0x95, 0xf2, 0x78,
	0x24, 0xe5, 0x7c, 0x25, 0xea, 0x5b, 0xa2, 0x3c, 0x1a, 0xc6, 0xbc, 0xb9, 0x2d, 0x4d, 0x9f, 0xdb,
	0x16, 0xac, 0x1c, 0x52, 0x2e, 0x98, 0x37, 0x44, 0x0e, 0x3e, 0x41, 0x3e, 0xe6, 0x7c, 0xf4, 0xfe,
	0x33, 0x79, 0x8d, 0x5c, 0x8d, 0x91, 0xdb, 0xf8, 0x64, 0x17, 0x73, 0xfe, 0x68, 0x0c, 0xd3, 0x3f,
	0x01, 0x83, 0x72, 0x94, 0xde, 0x89, 0x91, 0x03, 0x46, 0xf8, 0x21, 0xb1, 0xe5, 0xb5, 0x92, 0x84,
	0x79, 0x99, 0xf2, 0xcf, 0x46, 0x9b, 0x58, 0x09, 0xc4, 0xf4, 0xa0, 0x76, 0xaa, 0xe1, 0xf7, 0x49,
	0x24, 0x44, 0xe1, 0x50, 0xbc, 0xf2, 0xeb, 0xe2, 0xa9, 0x02, 0x6b, 0x67, 0x9c, 0x78, 0x3e, 0xa2,
	0x65, 0xa4, 0xb9, 0x20, 0xa1, 0x33, 0x49, 0xf3, 0xbb, 0xc9, 0xab, 0xaa, 0x94, 0x03, 0x55, 0xea,
	0xe0, 0xa4, 0x2e, 0x47, 0x2f, 0xbf, 0x52, 0x1c, 0x9e, 0x2a, 0x39, 0x95, 0x92, 0xd0, 0x4f, 0x3d,
	0x16, 0xaa, 0xeb, 0xbf, 0x21, 0x11, 0x99, 0x6f, 0x81, 0x62, 0x8a, 0x30, 0xa3, 0x6f, 0x01, 0x73,
	0x90, 0x53, 0xee, 0x6c, 0xd8, 0xe7, 0x2b, 0x77, 0xe6, 0xc8, 0xb9, 0xbc, 0xcf, 0x0f, 0xf3, 0xe7,
	0xbc, 0x52, 0xa5, 0x3f, 0x69, 0xfe, 0xbb, 0x6a, 0x6a, 0xee, 0xe4, 0x94, 0x2b, 0x1b, 0xfa, 0xb9,
	0xca, 0x75, 0xf7, 0x07, 0x05, 0xae, 0xb7, 0x72, 0xb4, 0x34, 0xe8, 0x27, 0x52, 0x74, 0x17, 0x6e,
	0xb5, 0x76, 0x1e, 0x6f, 0xa3, 0x8e, 0xd5, 0x7e, 0xd2, 0x6e, 0xa2, 0xfb, 0xcd, 0xed, 0x16, 0x6a,
	0xee, 0xec, 0x7d, 0xd1, 0xb2, 0x90, 0xd5, 0xda, 0x7b, 0xfc, 0xa0, 0x83, 0x36, 0x1f, 0x5a, 0x56,
	0x6b, 0xb3, 0xa3, 0x29, 0x7a, 0x1d, 0x6e, 0x9e, 0x81, 0x6d, 0x59, 0xd6, 0x43, 0x4b, 0x2b, 0xcc,
	0xb0, 0x6b, 0xa7, 0xbd, 0xdd, 0x7a, 0xf8, 0xb8, 0xa3, 0xa9, 0x77, 0x7f, 0x51, 0xe0, 0xd2, 0xa9,
	0x18, 0xdb, 0xb6, 0x5e, 0x83, 0x95, 0x69, 0x7b, 0xb4, 0xb7, 0x50, 0x53, 0x53, 0xce, 0x40, 0x6c,
	0x68, 0x85, 0x33, 0x10, 0x9b, 0x9a, 0x7a, 0x06, 0x62, 0x4b, 0x2b, 0xea, 0xb7, 0xc1, 0x7c, 0x09,
	0x22, 0xc9, 0xe3, 0x85, 0xb2, 0xb1, 0xfb, 0xec, 0xf9, 0xaa, 0xf2, 0xeb, 0xf3, 0x55, 0xe5, 0x8f,
	0xe7, 0xab, 0xca, 0x37, 0x7f, 0xae, 0x5e, 0x00, 0xa3, 0xeb, 0x39, 0x8d, 0x21, 0x1d, 0x7a, 0x41,
	0xd8, 0x1f, 0xc7, 0xb3, 0x49, 0x3f, 0xfa, 0x9f, 0xe3, 0xcb, 0x9b, 0x3d, 0xaf, 0x8f, 0xdd, 0x5e,
	0xe3, 0x83, 0x75, 0x21, 0x1a, 0x5d, 0xcf, 0xb9, 0x27, 0xcd, 0x5d, 0xaf, 0x7f, 0x0f, 0xfb, 0xfe,
	0xbd, 0x98, 0x5a, 0x7f, 0x07, 0x00, 0x00, 0xff, 0xff, 0xa0, 0x22, 0x58, 0xfd, 0x3d, 0x11, 0x00,
	0x00,
}
