// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel/channel_opt_v2.proto

package channel // import "golang.52tt.com/protocol/app/channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import revenue "golang.52tt.com/protocol/app/revenue"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 进房扩展信息V2
type ChannelEnterOptV2 struct {
	MemberCnt           uint32 `protobuf:"varint,1,opt,name=member_cnt,json=memberCnt,proto3" json:"member_cnt,omitempty"`
	IsRobot             bool   `protobuf:"varint,2,opt,name=is_robot,json=isRobot,proto3" json:"is_robot,omitempty"`
	UserSex             int32  `protobuf:"varint,3,opt,name=user_sex,json=userSex,proto3" json:"user_sex,omitempty"`
	EnterSource         uint32 `protobuf:"varint,4,opt,name=enter_source,json=enterSource,proto3" json:"enter_source,omitempty"`
	OverflowWarning     uint32 `protobuf:"varint,5,opt,name=overflow_warning,json=overflowWarning,proto3" json:"overflow_warning,omitempty"`
	IsNewbie            bool   `protobuf:"varint,6,opt,name=is_newbie,json=isNewbie,proto3" json:"is_newbie,omitempty"`
	IsFreshRegisterUser bool   `protobuf:"varint,7,opt,name=is_fresh_register_user,json=isFreshRegisterUser,proto3" json:"is_fresh_register_user,omitempty"`
	// 在房间类型为 GUILD_HOME_CHANNEL_TYPE 的 公会主房间里面 才有(8，9, 10, 11)字段
	GuildOfficialName        string                  `protobuf:"bytes,8,opt,name=guild_official_name,json=guildOfficialName,proto3" json:"guild_official_name,omitempty"`
	IsGuildChannelPermission bool                    `protobuf:"varint,9,opt,name=is_guild_channel_permission,json=isGuildChannelPermission,proto3" json:"is_guild_channel_permission,omitempty"`
	GuildTitleName           string                  `protobuf:"bytes,10,opt,name=guild_title_name,json=guildTitleName,proto3" json:"guild_title_name,omitempty"`
	IsGuildMember            bool                    `protobuf:"varint,11,opt,name=is_guild_member,json=isGuildMember,proto3" json:"is_guild_member,omitempty"`
	FollowMember             *app.GenericMember      `protobuf:"bytes,12,opt,name=follow_member,json=followMember,proto3" json:"follow_member,omitempty"`
	FollowInfoMsg            string                  `protobuf:"bytes,13,opt,name=follow_info_msg,json=followInfoMsg,proto3" json:"follow_info_msg,omitempty"`
	RevenueInfo              *ChannelEnterOptRevenue `protobuf:"bytes,20,opt,name=revenue_info,json=revenueInfo,proto3" json:"revenue_info,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}                `json:"-"`
	XXX_unrecognized         []byte                  `json:"-"`
	XXX_sizecache            int32                   `json:"-"`
}

func (m *ChannelEnterOptV2) Reset()         { *m = ChannelEnterOptV2{} }
func (m *ChannelEnterOptV2) String() string { return proto.CompactTextString(m) }
func (*ChannelEnterOptV2) ProtoMessage()    {}
func (*ChannelEnterOptV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_opt_v2_0341464bc8ceae2d, []int{0}
}
func (m *ChannelEnterOptV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelEnterOptV2.Unmarshal(m, b)
}
func (m *ChannelEnterOptV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelEnterOptV2.Marshal(b, m, deterministic)
}
func (dst *ChannelEnterOptV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelEnterOptV2.Merge(dst, src)
}
func (m *ChannelEnterOptV2) XXX_Size() int {
	return xxx_messageInfo_ChannelEnterOptV2.Size(m)
}
func (m *ChannelEnterOptV2) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelEnterOptV2.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelEnterOptV2 proto.InternalMessageInfo

func (m *ChannelEnterOptV2) GetMemberCnt() uint32 {
	if m != nil {
		return m.MemberCnt
	}
	return 0
}

func (m *ChannelEnterOptV2) GetIsRobot() bool {
	if m != nil {
		return m.IsRobot
	}
	return false
}

func (m *ChannelEnterOptV2) GetUserSex() int32 {
	if m != nil {
		return m.UserSex
	}
	return 0
}

func (m *ChannelEnterOptV2) GetEnterSource() uint32 {
	if m != nil {
		return m.EnterSource
	}
	return 0
}

func (m *ChannelEnterOptV2) GetOverflowWarning() uint32 {
	if m != nil {
		return m.OverflowWarning
	}
	return 0
}

func (m *ChannelEnterOptV2) GetIsNewbie() bool {
	if m != nil {
		return m.IsNewbie
	}
	return false
}

func (m *ChannelEnterOptV2) GetIsFreshRegisterUser() bool {
	if m != nil {
		return m.IsFreshRegisterUser
	}
	return false
}

func (m *ChannelEnterOptV2) GetGuildOfficialName() string {
	if m != nil {
		return m.GuildOfficialName
	}
	return ""
}

func (m *ChannelEnterOptV2) GetIsGuildChannelPermission() bool {
	if m != nil {
		return m.IsGuildChannelPermission
	}
	return false
}

func (m *ChannelEnterOptV2) GetGuildTitleName() string {
	if m != nil {
		return m.GuildTitleName
	}
	return ""
}

func (m *ChannelEnterOptV2) GetIsGuildMember() bool {
	if m != nil {
		return m.IsGuildMember
	}
	return false
}

func (m *ChannelEnterOptV2) GetFollowMember() *app.GenericMember {
	if m != nil {
		return m.FollowMember
	}
	return nil
}

func (m *ChannelEnterOptV2) GetFollowInfoMsg() string {
	if m != nil {
		return m.FollowInfoMsg
	}
	return ""
}

func (m *ChannelEnterOptV2) GetRevenueInfo() *ChannelEnterOptRevenue {
	if m != nil {
		return m.RevenueInfo
	}
	return nil
}

// 进房扩展信息V2-营收
type ChannelEnterOptRevenue struct {
	NewRichLevel         uint32                             `protobuf:"varint,1,opt,name=new_rich_level,json=newRichLevel,proto3" json:"new_rich_level,omitempty"`
	NewCharmLevel        uint32                             `protobuf:"varint,2,opt,name=new_charm_level,json=newCharmLevel,proto3" json:"new_charm_level,omitempty"`
	RichCharmToprank     *app.RichAndCharmTopRank           `protobuf:"bytes,3,opt,name=rich_charm_toprank,json=richCharmToprank,proto3" json:"rich_charm_toprank,omitempty"`
	VipLevel             *app.ChannelMemberVipLevel         `protobuf:"bytes,4,opt,name=vip_level,json=vipLevel,proto3" json:"vip_level,omitempty"`
	SpecialEffect        *ChannelEnterSpecialEffect         `protobuf:"bytes,5,opt,name=special_effect,json=specialEffect,proto3" json:"special_effect,omitempty"`
	NobilityInfo         *app.NobilityInfo                  `protobuf:"bytes,6,opt,name=nobility_info,json=nobilityInfo,proto3" json:"nobility_info,omitempty"`
	FansInfo             *app.ChannelLiveFansInfo           `protobuf:"bytes,7,opt,name=fans_info,json=fansInfo,proto3" json:"fans_info,omitempty"`
	SuperPlayerLevel     uint32                             `protobuf:"varint,8,opt,name=super_player_level,json=superPlayerLevel,proto3" json:"super_player_level,omitempty"`
	TailLightList        *ChannelTailLightList              `protobuf:"bytes,9,opt,name=tail_light_list,json=tailLightList,proto3" json:"tail_light_list,omitempty"`
	IsYearMember         bool                               `protobuf:"varint,10,opt,name=is_year_member,json=isYearMember,proto3" json:"is_year_member,omitempty"`
	KnightInfo           *KnightInfo                        `protobuf:"bytes,11,opt,name=knight_info,json=knightInfo,proto3" json:"knight_info,omitempty"`
	UkwInfo              *app.UserUKWInfo                   `protobuf:"bytes,12,opt,name=ukw_info,json=ukwInfo,proto3" json:"ukw_info,omitempty"`
	RevenueNameplate     []*app.NameplateDetailInfo         `protobuf:"bytes,13,rep,name=revenue_nameplate,json=revenueNameplate,proto3" json:"revenue_nameplate,omitempty"`
	UserNameplateList    *ChannelUserNameplateList          `protobuf:"bytes,14,opt,name=user_nameplate_list,json=userNameplateList,proto3" json:"user_nameplate_list,omitempty"`
	RevenueInfo          *revenue.RevenueEnterChannelExtend `protobuf:"bytes,15,opt,name=revenue_info,json=revenueInfo,proto3" json:"revenue_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *ChannelEnterOptRevenue) Reset()         { *m = ChannelEnterOptRevenue{} }
func (m *ChannelEnterOptRevenue) String() string { return proto.CompactTextString(m) }
func (*ChannelEnterOptRevenue) ProtoMessage()    {}
func (*ChannelEnterOptRevenue) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_opt_v2_0341464bc8ceae2d, []int{1}
}
func (m *ChannelEnterOptRevenue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelEnterOptRevenue.Unmarshal(m, b)
}
func (m *ChannelEnterOptRevenue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelEnterOptRevenue.Marshal(b, m, deterministic)
}
func (dst *ChannelEnterOptRevenue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelEnterOptRevenue.Merge(dst, src)
}
func (m *ChannelEnterOptRevenue) XXX_Size() int {
	return xxx_messageInfo_ChannelEnterOptRevenue.Size(m)
}
func (m *ChannelEnterOptRevenue) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelEnterOptRevenue.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelEnterOptRevenue proto.InternalMessageInfo

func (m *ChannelEnterOptRevenue) GetNewRichLevel() uint32 {
	if m != nil {
		return m.NewRichLevel
	}
	return 0
}

func (m *ChannelEnterOptRevenue) GetNewCharmLevel() uint32 {
	if m != nil {
		return m.NewCharmLevel
	}
	return 0
}

func (m *ChannelEnterOptRevenue) GetRichCharmToprank() *app.RichAndCharmTopRank {
	if m != nil {
		return m.RichCharmToprank
	}
	return nil
}

func (m *ChannelEnterOptRevenue) GetVipLevel() *app.ChannelMemberVipLevel {
	if m != nil {
		return m.VipLevel
	}
	return nil
}

func (m *ChannelEnterOptRevenue) GetSpecialEffect() *ChannelEnterSpecialEffect {
	if m != nil {
		return m.SpecialEffect
	}
	return nil
}

func (m *ChannelEnterOptRevenue) GetNobilityInfo() *app.NobilityInfo {
	if m != nil {
		return m.NobilityInfo
	}
	return nil
}

func (m *ChannelEnterOptRevenue) GetFansInfo() *app.ChannelLiveFansInfo {
	if m != nil {
		return m.FansInfo
	}
	return nil
}

func (m *ChannelEnterOptRevenue) GetSuperPlayerLevel() uint32 {
	if m != nil {
		return m.SuperPlayerLevel
	}
	return 0
}

func (m *ChannelEnterOptRevenue) GetTailLightList() *ChannelTailLightList {
	if m != nil {
		return m.TailLightList
	}
	return nil
}

func (m *ChannelEnterOptRevenue) GetIsYearMember() bool {
	if m != nil {
		return m.IsYearMember
	}
	return false
}

func (m *ChannelEnterOptRevenue) GetKnightInfo() *KnightInfo {
	if m != nil {
		return m.KnightInfo
	}
	return nil
}

func (m *ChannelEnterOptRevenue) GetUkwInfo() *app.UserUKWInfo {
	if m != nil {
		return m.UkwInfo
	}
	return nil
}

func (m *ChannelEnterOptRevenue) GetRevenueNameplate() []*app.NameplateDetailInfo {
	if m != nil {
		return m.RevenueNameplate
	}
	return nil
}

func (m *ChannelEnterOptRevenue) GetUserNameplateList() *ChannelUserNameplateList {
	if m != nil {
		return m.UserNameplateList
	}
	return nil
}

func (m *ChannelEnterOptRevenue) GetRevenueInfo() *revenue.RevenueEnterChannelExtend {
	if m != nil {
		return m.RevenueInfo
	}
	return nil
}

func init() {
	proto.RegisterType((*ChannelEnterOptV2)(nil), "ga.channel.ChannelEnterOptV2")
	proto.RegisterType((*ChannelEnterOptRevenue)(nil), "ga.channel.ChannelEnterOptRevenue")
}

func init() {
	proto.RegisterFile("channel/channel_opt_v2.proto", fileDescriptor_channel_opt_v2_0341464bc8ceae2d)
}

var fileDescriptor_channel_opt_v2_0341464bc8ceae2d = []byte{
	// 878 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x55, 0xed, 0x6e, 0xdb, 0x36,
	0x14, 0x85, 0xdb, 0x34, 0xb6, 0x69, 0xcb, 0x1f, 0xcc, 0xd0, 0xa9, 0x5f, 0x80, 0x17, 0x14, 0x85,
	0x37, 0x0c, 0x0a, 0xe0, 0xae, 0xfd, 0x37, 0x20, 0x5b, 0x92, 0x16, 0x43, 0xdd, 0xb4, 0x50, 0xd2,
	0x16, 0xdb, 0x1f, 0x82, 0x56, 0xae, 0x64, 0xc2, 0x12, 0x29, 0x90, 0xb4, 0x1d, 0x3f, 0xee, 0xde,
	0x60, 0x8f, 0x30, 0xf0, 0x92, 0x76, 0xbc, 0x60, 0xbf, 0x2c, 0x9e, 0x7b, 0xce, 0x3d, 0xe4, 0xd5,
	0xa1, 0x4c, 0x9e, 0x67, 0x73, 0x2e, 0x25, 0x94, 0x27, 0xe1, 0x97, 0xa9, 0xda, 0xb2, 0xd5, 0x24,
	0xa9, 0xb5, 0xb2, 0x8a, 0x92, 0x82, 0x27, 0xa1, 0xf0, 0x34, 0x2a, 0x38, 0x9b, 0x71, 0x03, 0xbe,
	0xf4, 0x94, 0xee, 0x0b, 0x02, 0x16, 0x69, 0x58, 0x81, 0x5c, 0x06, 0xca, 0xf1, 0x3f, 0x07, 0x64,
	0x78, 0xe6, 0x59, 0x17, 0xd2, 0x82, 0xfe, 0x54, 0xdb, 0xaf, 0x13, 0xfa, 0x82, 0x90, 0x0a, 0xaa,
	0x19, 0x68, 0x96, 0x49, 0x1b, 0x37, 0x46, 0x8d, 0x71, 0x94, 0xb6, 0x3d, 0x72, 0x26, 0x2d, 0x7d,
	0x42, 0x5a, 0xc2, 0x30, 0xad, 0x66, 0xca, 0xc6, 0x0f, 0x46, 0x8d, 0x71, 0x2b, 0x6d, 0x0a, 0x93,
	0xba, 0xa5, 0x2b, 0x2d, 0x0d, 0x68, 0x66, 0xe0, 0x36, 0x7e, 0x38, 0x6a, 0x8c, 0x1f, 0xa5, 0x4d,
	0xb7, 0xbe, 0x82, 0x5b, 0xfa, 0x03, 0xe9, 0x82, 0xb3, 0x60, 0x46, 0x2d, 0x75, 0x06, 0xf1, 0x01,
	0xb6, 0xed, 0x20, 0x76, 0x85, 0x10, 0xfd, 0x91, 0x0c, 0xd4, 0x0a, 0x74, 0x5e, 0xaa, 0x35, 0x5b,
	0x73, 0x2d, 0x85, 0x2c, 0xe2, 0x47, 0x48, 0xeb, 0x6f, 0xf1, 0x6f, 0x1e, 0xa6, 0xcf, 0x48, 0x5b,
	0x18, 0x26, 0x61, 0x3d, 0x13, 0x10, 0x1f, 0xe2, 0x26, 0x5a, 0xc2, 0x5c, 0xe2, 0x9a, 0xbe, 0x26,
	0x8f, 0x85, 0x61, 0xb9, 0x06, 0x33, 0x67, 0x1a, 0x0a, 0x61, 0x9c, 0xad, 0xdb, 0x47, 0xdc, 0x44,
	0xe6, 0x91, 0x30, 0xef, 0x5c, 0x31, 0x0d, 0xb5, 0x2f, 0x06, 0x34, 0x4d, 0xc8, 0x51, 0xb1, 0x14,
	0xe5, 0x0d, 0x53, 0x79, 0x2e, 0x32, 0xc1, 0x4b, 0x26, 0x79, 0x05, 0x71, 0x6b, 0xd4, 0x18, 0xb7,
	0xd3, 0x21, 0x96, 0x3e, 0x85, 0xca, 0x25, 0xaf, 0x80, 0xfe, 0x4a, 0x9e, 0x09, 0xc3, 0xbc, 0x64,
	0x3b, 0xe8, 0x1a, 0x74, 0x25, 0x8c, 0x11, 0x4a, 0xc6, 0x6d, 0x74, 0x8a, 0x85, 0x79, 0xef, 0x18,
	0x61, 0xc6, 0x9f, 0x77, 0x75, 0x3a, 0x26, 0x03, 0xaf, 0xb5, 0xc2, 0x96, 0xe0, 0xbd, 0x08, 0x7a,
	0xf5, 0x10, 0xbf, 0x76, 0x30, 0x1a, 0xbd, 0x22, 0xfd, 0x9d, 0x91, 0x7f, 0x09, 0x71, 0x07, 0x9b,
	0x47, 0xa1, 0xf9, 0x47, 0x04, 0xe9, 0x5b, 0x12, 0xe5, 0xaa, 0x74, 0xb3, 0x0b, 0xac, 0xee, 0xa8,
	0x31, 0xee, 0x4c, 0x86, 0x49, 0xc1, 0x93, 0xf7, 0x20, 0x41, 0x8b, 0xcc, 0x33, 0xd3, 0xae, 0xe7,
	0x05, 0xdd, 0x2b, 0xd2, 0x0f, 0x3a, 0x21, 0x73, 0xc5, 0x2a, 0x53, 0xc4, 0x11, 0x6e, 0x24, 0xb4,
	0xfb, 0x43, 0xe6, 0xea, 0xa3, 0x29, 0xe8, 0x05, 0xe9, 0x86, 0xf0, 0x20, 0x31, 0xfe, 0x0e, 0xdb,
	0x1f, 0x27, 0x77, 0x01, 0x4c, 0xee, 0x45, 0x29, 0xf5, 0xf4, 0xb4, 0x13, 0x74, 0xae, 0xd3, 0xf1,
	0xdf, 0x87, 0xe4, 0xf1, 0xff, 0xf3, 0xe8, 0x4b, 0xd2, 0x93, 0xb0, 0x66, 0x5a, 0x64, 0x73, 0x56,
	0xc2, 0x0a, 0xca, 0x90, 0xbd, 0xae, 0x84, 0x75, 0x2a, 0xb2, 0xf9, 0xd4, 0x61, 0x6e, 0xbf, 0x8e,
	0x95, 0xcd, 0xb9, 0xae, 0x02, 0xed, 0x01, 0xd2, 0x22, 0x09, 0xeb, 0x33, 0x87, 0x7a, 0xde, 0x05,
	0xa1, 0xd8, 0xc9, 0x13, 0xad, 0xaa, 0x35, 0x97, 0x0b, 0x4c, 0x65, 0x67, 0xf2, 0xbd, 0xdb, 0xb5,
	0x6b, 0xf9, 0x9b, 0xbc, 0x41, 0xc9, 0xb5, 0xaa, 0x53, 0x2e, 0x17, 0xe9, 0xc0, 0x49, 0xb6, 0x88,
	0x13, 0xd0, 0xb7, 0xa4, 0xbd, 0x12, 0x75, 0x30, 0x3a, 0x40, 0xf5, 0x13, 0xa7, 0x0e, 0x67, 0xf0,
	0x43, 0xfc, 0x2a, 0x6a, 0x34, 0x4d, 0x5b, 0xab, 0xf0, 0x44, 0xcf, 0x49, 0xcf, 0xd4, 0x80, 0x41,
	0x82, 0x3c, 0x87, 0xcc, 0x62, 0x94, 0x3b, 0x93, 0x17, 0x7b, 0x62, 0x1c, 0xc0, 0x95, 0x67, 0x5d,
	0x20, 0x29, 0x8d, 0xcc, 0xfe, 0x92, 0xbe, 0x21, 0x91, 0x54, 0x33, 0x51, 0x0a, 0xbb, 0xf1, 0x53,
	0x3f, 0xc4, 0x26, 0x03, 0xd7, 0xe4, 0x32, 0x14, 0xdc, 0x58, 0xd3, 0xae, 0xdc, 0x5b, 0xd1, 0x5f,
	0x48, 0x3b, 0xe7, 0xd2, 0x78, 0x49, 0xf3, 0xee, 0xc8, 0xc1, 0x77, 0x2a, 0x56, 0xf0, 0x8e, 0x4b,
	0x83, 0xca, 0x56, 0x1e, 0x9e, 0xe8, 0xcf, 0x84, 0x9a, 0x65, 0x0d, 0x9a, 0xd5, 0x25, 0xdf, 0x80,
	0x0e, 0x67, 0x6e, 0xe1, 0x70, 0x07, 0x58, 0xf9, 0x8c, 0x05, 0x7f, 0xc0, 0x53, 0xd2, 0xb7, 0x5c,
	0x94, 0xac, 0x14, 0xc5, 0xdc, 0xb2, 0x52, 0x18, 0x8b, 0xa1, 0xef, 0x4c, 0xe2, 0x3d, 0xa7, 0x6b,
	0x2e, 0xca, 0xa9, 0x23, 0x4c, 0x85, 0xb1, 0x69, 0x64, 0xf7, 0x97, 0xee, 0x7d, 0x0b, 0xc3, 0x36,
	0xc0, 0xf5, 0x36, 0xb2, 0x04, 0x83, 0xdd, 0x15, 0xe6, 0x4f, 0xe0, 0x3a, 0xe4, 0xf3, 0x84, 0x74,
	0x16, 0x12, 0x3d, 0xf0, 0x34, 0x1d, 0xf4, 0xe8, 0x39, 0x8f, 0x0f, 0x08, 0xe3, 0x21, 0xc8, 0x62,
	0xf7, 0x4c, 0x7f, 0x22, 0xad, 0xe5, 0xc2, 0xa7, 0x39, 0xdc, 0x81, 0xbe, 0x63, 0xbb, 0x5b, 0xfe,
	0xe5, 0xc3, 0x37, 0xa4, 0x37, 0x97, 0x0b, 0xcc, 0x35, 0x3d, 0x27, 0xc3, 0x6d, 0xa8, 0xdd, 0x15,
	0xac, 0x4b, 0x6e, 0x21, 0x8e, 0x46, 0x0f, 0xb7, 0x03, 0xbb, 0xdc, 0x82, 0xe7, 0xe0, 0xf6, 0x8e,
	0xe2, 0x41, 0x50, 0xec, 0x6a, 0x74, 0x4a, 0x8e, 0xf0, 0xb3, 0xb7, 0x6b, 0xe1, 0xc7, 0xd1, 0x43,
	0xf3, 0xe7, 0x7b, 0xe3, 0x70, 0x7b, 0xd8, 0xc9, 0x70, 0x24, 0xc3, 0xe5, 0x7d, 0x88, 0x9e, 0xde,
	0xbb, 0x68, 0xfd, 0xbb, 0xdc, 0x84, 0x9b, 0x82, 0xb9, 0xd9, 0x66, 0xe8, 0xd6, 0x82, 0xbc, 0xf9,
	0xcf, 0x1d, 0xfb, 0xfd, 0x94, 0xc4, 0x99, 0xaa, 0x92, 0x8d, 0xd8, 0xa8, 0xa5, 0x93, 0x55, 0xea,
	0x06, 0x4a, 0xff, 0xc9, 0xff, 0xeb, 0x65, 0xa1, 0x4a, 0x2e, 0x8b, 0xe4, 0xcd, 0xc4, 0xda, 0x24,
	0x53, 0xd5, 0x09, 0xc2, 0x99, 0x2a, 0x4f, 0x78, 0x5d, 0x6f, 0xff, 0x63, 0x66, 0x87, 0x88, 0xbe,
	0xfe, 0x37, 0x00, 0x00, 0xff, 0xff, 0x4d, 0xdb, 0x3b, 0x96, 0x7d, 0x06, 0x00, 0x00,
}
