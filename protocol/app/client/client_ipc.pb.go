// Code generated by protoc-gen-gogo.
// source: client_ipc.proto
// DO NOT EDIT!

/*
Package client is a generated protocol buffer package.

It is generated from these files:
	client_ipc.proto

It has these top-level messages:
	NewAppOnTop
*/
package client

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"

import "io"
import fmt1 "fmt"
import github_com_gogo_protobuf_proto "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type IPCCMDType int32

const (
	// Server endpoint starts from 1 to 1000000
	IPCCMDType_TYPE_NOTIFY_NEW_APP_ON_TOP IPCCMDType = 1000
)

var IPCCMDType_name = map[int32]string{
	1000: "TYPE_NOTIFY_NEW_APP_ON_TOP",
}
var IPCCMDType_value = map[string]int32{
	"TYPE_NOTIFY_NEW_APP_ON_TOP": 1000,
}

func (x IPCCMDType) Enum() *IPCCMDType {
	p := new(IPCCMDType)
	*p = x
	return p
}
func (x IPCCMDType) String() string {
	return proto.EnumName(IPCCMDType_name, int32(x))
}
func (x *IPCCMDType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(IPCCMDType_value, data, "IPCCMDType")
	if err != nil {
		return err
	}
	*x = IPCCMDType(value)
	return nil
}
func (IPCCMDType) EnumDescriptor() ([]byte, []int) { return fileDescriptorClientIpc, []int{0} }

type NewAppOnTop struct {
	PackageName string `protobuf:"bytes,1,req,name=package_name,json=packageName" json:"package_name"`
}

func (m *NewAppOnTop) Reset()                    { *m = NewAppOnTop{} }
func (m *NewAppOnTop) String() string            { return proto.CompactTextString(m) }
func (*NewAppOnTop) ProtoMessage()               {}
func (*NewAppOnTop) Descriptor() ([]byte, []int) { return fileDescriptorClientIpc, []int{0} }

func (m *NewAppOnTop) GetPackageName() string {
	if m != nil {
		return m.PackageName
	}
	return ""
}

func init() {
	proto.RegisterType((*NewAppOnTop)(nil), "ga.NewAppOnTop")
	proto.RegisterEnum("ga.IPCCMDType", IPCCMDType_name, IPCCMDType_value)
}
func (m *NewAppOnTop) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NewAppOnTop) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintClientIpc(dAtA, i, uint64(len(m.PackageName)))
	i += copy(dAtA[i:], m.PackageName)
	return i, nil
}

func encodeFixed64ClientIpc(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32ClientIpc(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintClientIpc(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *NewAppOnTop) Size() (n int) {
	var l int
	_ = l
	l = len(m.PackageName)
	n += 1 + l + sovClientIpc(uint64(l))
	return n
}

func sovClientIpc(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozClientIpc(x uint64) (n int) {
	return sovClientIpc(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *NewAppOnTop) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientIpc
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: NewAppOnTop: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: NewAppOnTop: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field PackageName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientIpc
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthClientIpc
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PackageName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipClientIpc(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientIpc
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("package_name")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipClientIpc(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowClientIpc
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowClientIpc
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowClientIpc
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthClientIpc
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowClientIpc
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipClientIpc(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt1.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthClientIpc = fmt1.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowClientIpc   = fmt1.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("client_ipc.proto", fileDescriptorClientIpc) }

var fileDescriptorClientIpc = []byte{
	// 217 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0x48, 0xce, 0xc9, 0x4c,
	0xcd, 0x2b, 0x89, 0xcf, 0x2c, 0x48, 0xd6, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x4a, 0x4f,
	0x54, 0x32, 0xe3, 0xe2, 0xf6, 0x4b, 0x2d, 0x77, 0x2c, 0x28, 0xf0, 0xcf, 0x0b, 0xc9, 0x2f, 0x10,
	0x52, 0xe7, 0xe2, 0x29, 0x48, 0x4c, 0xce, 0x4e, 0x4c, 0x4f, 0x8d, 0xcf, 0x4b, 0xcc, 0x4d, 0x95,
	0x60, 0x54, 0x60, 0xd2, 0xe0, 0x74, 0x62, 0x39, 0x71, 0x4f, 0x9e, 0x21, 0x88, 0x1b, 0x2a, 0xe3,
	0x97, 0x98, 0x9b, 0xaa, 0xa5, 0xcb, 0xc5, 0xe5, 0x19, 0xe0, 0xec, 0xec, 0xeb, 0x12, 0x52, 0x59,
	0x90, 0x2a, 0x24, 0xcf, 0x25, 0x15, 0x12, 0x19, 0xe0, 0x1a, 0xef, 0xe7, 0x1f, 0xe2, 0xe9, 0x16,
	0x19, 0xef, 0xe7, 0x1a, 0x1e, 0xef, 0x18, 0x10, 0x10, 0xef, 0xef, 0x17, 0x1f, 0xe2, 0x1f, 0x20,
	0xf0, 0x82, 0xdd, 0x29, 0xf8, 0xc4, 0x23, 0x39, 0xc6, 0x0b, 0x8f, 0xe4, 0x18, 0x1f, 0x3c, 0x92,
	0x63, 0x9c, 0xf0, 0x58, 0x8e, 0x81, 0x4b, 0x26, 0x39, 0x3f, 0x57, 0xaf, 0x32, 0xb3, 0x32, 0xbf,
	0x54, 0x2f, 0x3d, 0x51, 0x2f, 0x37, 0x3f, 0x25, 0x35, 0x47, 0x0f, 0xee, 0xb4, 0x28, 0xe5, 0xf4,
	0xfc, 0x9c, 0xc4, 0xbc, 0x74, 0x3d, 0x53, 0xa3, 0x92, 0x12, 0xbd, 0xe4, 0xfc, 0x5c, 0x7d, 0xb0,
	0x70, 0x72, 0x7e, 0x8e, 0x7e, 0x62, 0x41, 0x81, 0x3e, 0xc4, 0x27, 0x80, 0x00, 0x00, 0x00, 0xff,
	0xff, 0x86, 0x59, 0x94, 0xf2, 0xd2, 0x00, 0x00, 0x00,
}
