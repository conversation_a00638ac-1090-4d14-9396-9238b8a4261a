// Code generated by protoc-gen-gogo.
// source: audit_.proto
// DO NOT EDIT!

/*
	Package audit is a generated protocol buffer package.

	It is generated from these files:
		audit_.proto

	It has these top-level messages:
		GetIosCheckStatusReq
		GetIosCheckStatusResp
*/
package audit

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 获取ios的版本审核状态
type GetIosCheckStatusReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetIosCheckStatusReq) Reset()                    { *m = GetIosCheckStatusReq{} }
func (m *GetIosCheckStatusReq) String() string            { return proto.CompactTextString(m) }
func (*GetIosCheckStatusReq) ProtoMessage()               {}
func (*GetIosCheckStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorAudit_, []int{0} }

func (m *GetIosCheckStatusReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetIosCheckStatusResp struct {
	BaseResp   *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	IsChecking bool         `protobuf:"varint,2,req,name=is_checking,json=isChecking" json:"is_checking"`
	// T豆充值页面 附加跳转链接信息
	TcoinRechargePageDesc     string `protobuf:"bytes,3,opt,name=tcoin_recharge_page_desc,json=tcoinRechargePageDesc" json:"tcoin_recharge_page_desc"`
	TcoinRechargePageJumpDesc string `protobuf:"bytes,4,opt,name=tcoin_recharge_page_jump_desc,json=tcoinRechargePageJumpDesc" json:"tcoin_recharge_page_jump_desc"`
	TcoinRechargePageJumpUrl1 string `protobuf:"bytes,5,opt,name=tcoin_recharge_page_jump_url1,json=tcoinRechargePageJumpUrl1" json:"tcoin_recharge_page_jump_url1"`
	TcoinRechargePageJumpUrl2 string `protobuf:"bytes,6,opt,name=tcoin_recharge_page_jump_url2,json=tcoinRechargePageJumpUrl2" json:"tcoin_recharge_page_jump_url2"`
	TcoinRechargePageJumpUrl3 string `protobuf:"bytes,7,opt,name=tcoin_recharge_page_jump_url3,json=tcoinRechargePageJumpUrl3" json:"tcoin_recharge_page_jump_url3"`
	// IOS 评分页面 文案
	ScorePageDesc   string `protobuf:"bytes,8,opt,name=score_page_desc,json=scorePageDesc" json:"score_page_desc"`
	ScorePageDaycnt uint32 `protobuf:"varint,9,opt,name=score_page_daycnt,json=scorePageDaycnt" json:"score_page_daycnt"`
}

func (m *GetIosCheckStatusResp) Reset()                    { *m = GetIosCheckStatusResp{} }
func (m *GetIosCheckStatusResp) String() string            { return proto.CompactTextString(m) }
func (*GetIosCheckStatusResp) ProtoMessage()               {}
func (*GetIosCheckStatusResp) Descriptor() ([]byte, []int) { return fileDescriptorAudit_, []int{1} }

func (m *GetIosCheckStatusResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetIosCheckStatusResp) GetIsChecking() bool {
	if m != nil {
		return m.IsChecking
	}
	return false
}

func (m *GetIosCheckStatusResp) GetTcoinRechargePageDesc() string {
	if m != nil {
		return m.TcoinRechargePageDesc
	}
	return ""
}

func (m *GetIosCheckStatusResp) GetTcoinRechargePageJumpDesc() string {
	if m != nil {
		return m.TcoinRechargePageJumpDesc
	}
	return ""
}

func (m *GetIosCheckStatusResp) GetTcoinRechargePageJumpUrl1() string {
	if m != nil {
		return m.TcoinRechargePageJumpUrl1
	}
	return ""
}

func (m *GetIosCheckStatusResp) GetTcoinRechargePageJumpUrl2() string {
	if m != nil {
		return m.TcoinRechargePageJumpUrl2
	}
	return ""
}

func (m *GetIosCheckStatusResp) GetTcoinRechargePageJumpUrl3() string {
	if m != nil {
		return m.TcoinRechargePageJumpUrl3
	}
	return ""
}

func (m *GetIosCheckStatusResp) GetScorePageDesc() string {
	if m != nil {
		return m.ScorePageDesc
	}
	return ""
}

func (m *GetIosCheckStatusResp) GetScorePageDaycnt() uint32 {
	if m != nil {
		return m.ScorePageDaycnt
	}
	return 0
}

func init() {
	proto.RegisterType((*GetIosCheckStatusReq)(nil), "ga.GetIosCheckStatusReq")
	proto.RegisterType((*GetIosCheckStatusResp)(nil), "ga.GetIosCheckStatusResp")
}
func (m *GetIosCheckStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIosCheckStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAudit_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GetIosCheckStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIosCheckStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAudit_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	if m.IsChecking {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAudit_(dAtA, i, uint64(len(m.TcoinRechargePageDesc)))
	i += copy(dAtA[i:], m.TcoinRechargePageDesc)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAudit_(dAtA, i, uint64(len(m.TcoinRechargePageJumpDesc)))
	i += copy(dAtA[i:], m.TcoinRechargePageJumpDesc)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAudit_(dAtA, i, uint64(len(m.TcoinRechargePageJumpUrl1)))
	i += copy(dAtA[i:], m.TcoinRechargePageJumpUrl1)
	dAtA[i] = 0x32
	i++
	i = encodeVarintAudit_(dAtA, i, uint64(len(m.TcoinRechargePageJumpUrl2)))
	i += copy(dAtA[i:], m.TcoinRechargePageJumpUrl2)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAudit_(dAtA, i, uint64(len(m.TcoinRechargePageJumpUrl3)))
	i += copy(dAtA[i:], m.TcoinRechargePageJumpUrl3)
	dAtA[i] = 0x42
	i++
	i = encodeVarintAudit_(dAtA, i, uint64(len(m.ScorePageDesc)))
	i += copy(dAtA[i:], m.ScorePageDesc)
	dAtA[i] = 0x48
	i++
	i = encodeVarintAudit_(dAtA, i, uint64(m.ScorePageDaycnt))
	return i, nil
}

func encodeFixed64Audit_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Audit_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintAudit_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GetIosCheckStatusReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovAudit_(uint64(l))
	}
	return n
}

func (m *GetIosCheckStatusResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovAudit_(uint64(l))
	}
	n += 2
	l = len(m.TcoinRechargePageDesc)
	n += 1 + l + sovAudit_(uint64(l))
	l = len(m.TcoinRechargePageJumpDesc)
	n += 1 + l + sovAudit_(uint64(l))
	l = len(m.TcoinRechargePageJumpUrl1)
	n += 1 + l + sovAudit_(uint64(l))
	l = len(m.TcoinRechargePageJumpUrl2)
	n += 1 + l + sovAudit_(uint64(l))
	l = len(m.TcoinRechargePageJumpUrl3)
	n += 1 + l + sovAudit_(uint64(l))
	l = len(m.ScorePageDesc)
	n += 1 + l + sovAudit_(uint64(l))
	n += 1 + sovAudit_(uint64(m.ScorePageDaycnt))
	return n
}

func sovAudit_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozAudit_(x uint64) (n int) {
	return sovAudit_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GetIosCheckStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIosCheckStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIosCheckStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAudit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAudit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAudit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIosCheckStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIosCheckStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIosCheckStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAudit_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsChecking", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsChecking = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TcoinRechargePageDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TcoinRechargePageDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TcoinRechargePageJumpDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TcoinRechargePageJumpDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TcoinRechargePageJumpUrl1", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TcoinRechargePageJumpUrl1 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TcoinRechargePageJumpUrl2", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TcoinRechargePageJumpUrl2 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TcoinRechargePageJumpUrl3", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TcoinRechargePageJumpUrl3 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScorePageDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ScorePageDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScorePageDaycnt", wireType)
			}
			m.ScorePageDaycnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ScorePageDaycnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAudit_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAudit_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_checking")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipAudit_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAudit_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAudit_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAudit_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthAudit_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowAudit_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipAudit_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthAudit_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAudit_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("audit_.proto", fileDescriptorAudit_) }

var fileDescriptorAudit_ = []byte{
	// 377 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0xd2, 0xc1, 0x4a, 0xf3, 0x40,
	0x10, 0x07, 0xf0, 0x26, 0x5f, 0x3f, 0x9b, 0x6e, 0x5b, 0x8a, 0xc1, 0xc2, 0x2a, 0x18, 0x43, 0x41,
	0x89, 0x20, 0xa9, 0x4d, 0xf1, 0xa8, 0x87, 0x2a, 0x8a, 0x1e, 0x44, 0x22, 0x5e, 0xbc, 0x84, 0xed,
	0x76, 0xd9, 0x46, 0x93, 0xec, 0x36, 0xbb, 0x39, 0xf4, 0x2d, 0x7c, 0x0c, 0x1f, 0xa5, 0x47, 0x9f,
	0x40, 0xa4, 0xbe, 0x88, 0x34, 0x09, 0x36, 0x60, 0x21, 0xbd, 0x0d, 0x33, 0xff, 0xf9, 0x1d, 0x86,
	0x01, 0x4d, 0x94, 0x8c, 0x7d, 0xe9, 0xd9, 0x3c, 0x66, 0x92, 0xe9, 0x2a, 0x45, 0x7b, 0x2d, 0x8a,
	0xbc, 0x11, 0x12, 0x24, 0x6b, 0x75, 0x2f, 0xc0, 0xce, 0x0d, 0x91, 0xb7, 0x4c, 0x5c, 0x4e, 0x08,
	0x7e, 0x7d, 0x94, 0x48, 0x26, 0xc2, 0x25, 0x53, 0xfd, 0x08, 0x68, 0xcb, 0x94, 0x17, 0x93, 0x29,
	0x54, 0x4c, 0xd5, 0x6a, 0x38, 0x0d, 0x9b, 0x22, 0x7b, 0x88, 0x04, 0x71, 0xc9, 0xd4, 0xad, 0x8d,
	0xb2, 0xa2, 0xfb, 0x5e, 0x05, 0x9d, 0x35, 0x80, 0xe0, 0xfa, 0x31, 0xa8, 0xe7, 0x82, 0xe0, 0x39,
	0xd1, 0x5c, 0x11, 0x82, 0xbb, 0xda, 0x28, 0xaf, 0xf4, 0x43, 0xd0, 0xf0, 0x85, 0x87, 0x97, 0x80,
	0x1f, 0x51, 0xa8, 0x9a, 0xaa, 0xa5, 0x0d, 0xab, 0xf3, 0xcf, 0x83, 0x8a, 0x0b, 0xfc, 0x0c, 0xf6,
	0x23, 0xaa, 0x9f, 0x03, 0x28, 0x31, 0xf3, 0x23, 0x2f, 0x26, 0x78, 0x82, 0x62, 0x4a, 0x3c, 0x8e,
	0x28, 0xf1, 0xc6, 0x44, 0x60, 0xf8, 0xcf, 0x54, 0xac, 0x7a, 0xbe, 0xd3, 0x49, 0x53, 0x6e, 0x1e,
	0x7a, 0x40, 0x94, 0x5c, 0x11, 0x81, 0xf5, 0x6b, 0xb0, 0xbf, 0x6e, 0xfd, 0x25, 0x09, 0x79, 0x66,
	0x54, 0x0b, 0xc6, 0xee, 0x1f, 0xe3, 0x2e, 0x09, 0x79, 0xa9, 0x93, 0xc4, 0x41, 0x1f, 0xfe, 0x2f,
	0x75, 0x9e, 0xe2, 0xa0, 0x5f, 0xe6, 0x38, 0x70, 0x6b, 0x13, 0xc7, 0x29, 0x73, 0x06, 0xb0, 0xb6,
	0x89, 0x33, 0xd0, 0x4f, 0x40, 0x5b, 0x60, 0x16, 0x17, 0xaf, 0xaa, 0x15, 0x36, 0x5b, 0xe9, 0xf0,
	0xf7, 0x9a, 0xa7, 0x60, 0xbb, 0x98, 0x46, 0x33, 0x1c, 0x49, 0x58, 0x37, 0x15, 0xab, 0x95, 0xe7,
	0xdb, 0xab, 0x7c, 0x3a, 0x1c, 0xde, 0xcf, 0x17, 0x86, 0xf2, 0xb1, 0x30, 0x94, 0xaf, 0x85, 0xa1,
	0xbc, 0x7d, 0x1b, 0x15, 0x00, 0x31, 0x0b, 0xed, 0x99, 0x3f, 0x63, 0xc9, 0xf2, 0x31, 0x42, 0x36,
	0x26, 0x41, 0xf6, 0x96, 0xcf, 0x5d, 0xca, 0x02, 0x14, 0x51, 0xfb, 0xcc, 0x91, 0xd2, 0xc6, 0x2c,
	0xec, 0xa5, 0x6d, 0xcc, 0x82, 0x1e, 0xe2, 0xbc, 0x97, 0x3e, 0xf5, 0x4f, 0x00, 0x00, 0x00, 0xff,
	0xff, 0x8c, 0x2b, 0xb2, 0xeb, 0xdc, 0x02, 0x00, 0x00,
}
