// Code generated by protoc-gen-gogo.
// source: hctran.proto
// DO NOT EDIT!

/*
	Package hctran is a generated protocol buffer package.

	It is generated from these files:
		hctran.proto

	It has these top-level messages:
		GuildGetLiveSummaryReq
		GuildGetLiveSummaryResp
		GuildLiveRoomInfo
		GuildGetLiveListReq
		GuildGetLiveListResp
		GetTTLivePublishingListReq
		GetTTLivePublishingListResp
		TTLivePublishingMessage
*/
package hctran

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 获取公会总群房间入口 直播 概要信息
type GuildGetLiveSummaryReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GuildGetLiveSummaryReq) Reset()                    { *m = GuildGetLiveSummaryReq{} }
func (m *GuildGetLiveSummaryReq) String() string            { return proto.CompactTextString(m) }
func (*GuildGetLiveSummaryReq) ProtoMessage()               {}
func (*GuildGetLiveSummaryReq) Descriptor() ([]byte, []int) { return fileDescriptorHctran, []int{0} }

func (m *GuildGetLiveSummaryReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildGetLiveSummaryReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GuildGetLiveSummaryResp struct {
	BaseResp          *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GuildId           uint32       `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	LiveMembersAmount uint32       `protobuf:"varint,3,req,name=live_members_amount,json=liveMembersAmount" json:"live_members_amount"`
	LiveMemberUid     uint32       `protobuf:"varint,4,req,name=live_member_uid,json=liveMemberUid" json:"live_member_uid"`
	LiveMemberAccount string       `protobuf:"bytes,5,req,name=live_member_account,json=liveMemberAccount" json:"live_member_account"`
	JumpUrl           string       `protobuf:"bytes,6,req,name=jump_url,json=jumpUrl" json:"jump_url"`
	NickName          string       `protobuf:"bytes,7,req,name=nick_name,json=nickName" json:"nick_name"`
}

func (m *GuildGetLiveSummaryResp) Reset()                    { *m = GuildGetLiveSummaryResp{} }
func (m *GuildGetLiveSummaryResp) String() string            { return proto.CompactTextString(m) }
func (*GuildGetLiveSummaryResp) ProtoMessage()               {}
func (*GuildGetLiveSummaryResp) Descriptor() ([]byte, []int) { return fileDescriptorHctran, []int{1} }

func (m *GuildGetLiveSummaryResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildGetLiveSummaryResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildGetLiveSummaryResp) GetLiveMembersAmount() uint32 {
	if m != nil {
		return m.LiveMembersAmount
	}
	return 0
}

func (m *GuildGetLiveSummaryResp) GetLiveMemberUid() uint32 {
	if m != nil {
		return m.LiveMemberUid
	}
	return 0
}

func (m *GuildGetLiveSummaryResp) GetLiveMemberAccount() string {
	if m != nil {
		return m.LiveMemberAccount
	}
	return ""
}

func (m *GuildGetLiveSummaryResp) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *GuildGetLiveSummaryResp) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

// 获取公会总群房间入口 直播 详细列表
type GuildLiveRoomInfo struct {
	LiveMemberUid     uint32 `protobuf:"varint,1,req,name=live_member_uid,json=liveMemberUid" json:"live_member_uid"`
	LiveMemberAccount string `protobuf:"bytes,2,req,name=live_member_account,json=liveMemberAccount" json:"live_member_account"`
	LiveTopic         string `protobuf:"bytes,3,req,name=live_topic,json=liveTopic" json:"live_topic"`
	UsersAmount       uint32 `protobuf:"varint,4,req,name=users_amount,json=usersAmount" json:"users_amount"`
	JumpUrl           string `protobuf:"bytes,5,req,name=jump_url,json=jumpUrl" json:"jump_url"`
	NickName          string `protobuf:"bytes,6,req,name=nick_name,json=nickName" json:"nick_name"`
}

func (m *GuildLiveRoomInfo) Reset()                    { *m = GuildLiveRoomInfo{} }
func (m *GuildLiveRoomInfo) String() string            { return proto.CompactTextString(m) }
func (*GuildLiveRoomInfo) ProtoMessage()               {}
func (*GuildLiveRoomInfo) Descriptor() ([]byte, []int) { return fileDescriptorHctran, []int{2} }

func (m *GuildLiveRoomInfo) GetLiveMemberUid() uint32 {
	if m != nil {
		return m.LiveMemberUid
	}
	return 0
}

func (m *GuildLiveRoomInfo) GetLiveMemberAccount() string {
	if m != nil {
		return m.LiveMemberAccount
	}
	return ""
}

func (m *GuildLiveRoomInfo) GetLiveTopic() string {
	if m != nil {
		return m.LiveTopic
	}
	return ""
}

func (m *GuildLiveRoomInfo) GetUsersAmount() uint32 {
	if m != nil {
		return m.UsersAmount
	}
	return 0
}

func (m *GuildLiveRoomInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *GuildLiveRoomInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

type GuildGetLiveListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GuildGetLiveListReq) Reset()                    { *m = GuildGetLiveListReq{} }
func (m *GuildGetLiveListReq) String() string            { return proto.CompactTextString(m) }
func (*GuildGetLiveListReq) ProtoMessage()               {}
func (*GuildGetLiveListReq) Descriptor() ([]byte, []int) { return fileDescriptorHctran, []int{3} }

func (m *GuildGetLiveListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildGetLiveListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GuildGetLiveListResp struct {
	BaseResp     *ga.BaseResp         `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GuildId      uint32               `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	LiveRoomList []*GuildLiveRoomInfo `protobuf:"bytes,3,rep,name=live_room_list,json=liveRoomList" json:"live_room_list,omitempty"`
}

func (m *GuildGetLiveListResp) Reset()                    { *m = GuildGetLiveListResp{} }
func (m *GuildGetLiveListResp) String() string            { return proto.CompactTextString(m) }
func (*GuildGetLiveListResp) ProtoMessage()               {}
func (*GuildGetLiveListResp) Descriptor() ([]byte, []int) { return fileDescriptorHctran, []int{4} }

func (m *GuildGetLiveListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildGetLiveListResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildGetLiveListResp) GetLiveRoomList() []*GuildLiveRoomInfo {
	if m != nil {
		return m.LiveRoomList
	}
	return nil
}

type GetTTLivePublishingListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetTTLivePublishingListReq) Reset()                    { *m = GetTTLivePublishingListReq{} }
func (m *GetTTLivePublishingListReq) String() string            { return proto.CompactTextString(m) }
func (*GetTTLivePublishingListReq) ProtoMessage()               {}
func (*GetTTLivePublishingListReq) Descriptor() ([]byte, []int) { return fileDescriptorHctran, []int{5} }

func (m *GetTTLivePublishingListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetTTLivePublishingListResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	UidList  []uint32     `protobuf:"varint,2,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *GetTTLivePublishingListResp) Reset()         { *m = GetTTLivePublishingListResp{} }
func (m *GetTTLivePublishingListResp) String() string { return proto.CompactTextString(m) }
func (*GetTTLivePublishingListResp) ProtoMessage()    {}
func (*GetTTLivePublishingListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorHctran, []int{6}
}

func (m *GetTTLivePublishingListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetTTLivePublishingListResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type TTLivePublishingMessage struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *TTLivePublishingMessage) Reset()                    { *m = TTLivePublishingMessage{} }
func (m *TTLivePublishingMessage) String() string            { return proto.CompactTextString(m) }
func (*TTLivePublishingMessage) ProtoMessage()               {}
func (*TTLivePublishingMessage) Descriptor() ([]byte, []int) { return fileDescriptorHctran, []int{7} }

func (m *TTLivePublishingMessage) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func init() {
	proto.RegisterType((*GuildGetLiveSummaryReq)(nil), "ga.GuildGetLiveSummaryReq")
	proto.RegisterType((*GuildGetLiveSummaryResp)(nil), "ga.GuildGetLiveSummaryResp")
	proto.RegisterType((*GuildLiveRoomInfo)(nil), "ga.GuildLiveRoomInfo")
	proto.RegisterType((*GuildGetLiveListReq)(nil), "ga.GuildGetLiveListReq")
	proto.RegisterType((*GuildGetLiveListResp)(nil), "ga.GuildGetLiveListResp")
	proto.RegisterType((*GetTTLivePublishingListReq)(nil), "ga.GetTTLivePublishingListReq")
	proto.RegisterType((*GetTTLivePublishingListResp)(nil), "ga.GetTTLivePublishingListResp")
	proto.RegisterType((*TTLivePublishingMessage)(nil), "ga.TTLivePublishingMessage")
}
func (m *GuildGetLiveSummaryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildGetLiveSummaryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintHctran(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintHctran(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GuildGetLiveSummaryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildGetLiveSummaryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintHctran(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintHctran(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintHctran(dAtA, i, uint64(m.LiveMembersAmount))
	dAtA[i] = 0x20
	i++
	i = encodeVarintHctran(dAtA, i, uint64(m.LiveMemberUid))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintHctran(dAtA, i, uint64(len(m.LiveMemberAccount)))
	i += copy(dAtA[i:], m.LiveMemberAccount)
	dAtA[i] = 0x32
	i++
	i = encodeVarintHctran(dAtA, i, uint64(len(m.JumpUrl)))
	i += copy(dAtA[i:], m.JumpUrl)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintHctran(dAtA, i, uint64(len(m.NickName)))
	i += copy(dAtA[i:], m.NickName)
	return i, nil
}

func (m *GuildLiveRoomInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildLiveRoomInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintHctran(dAtA, i, uint64(m.LiveMemberUid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintHctran(dAtA, i, uint64(len(m.LiveMemberAccount)))
	i += copy(dAtA[i:], m.LiveMemberAccount)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintHctran(dAtA, i, uint64(len(m.LiveTopic)))
	i += copy(dAtA[i:], m.LiveTopic)
	dAtA[i] = 0x20
	i++
	i = encodeVarintHctran(dAtA, i, uint64(m.UsersAmount))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintHctran(dAtA, i, uint64(len(m.JumpUrl)))
	i += copy(dAtA[i:], m.JumpUrl)
	dAtA[i] = 0x32
	i++
	i = encodeVarintHctran(dAtA, i, uint64(len(m.NickName)))
	i += copy(dAtA[i:], m.NickName)
	return i, nil
}

func (m *GuildGetLiveListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildGetLiveListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintHctran(dAtA, i, uint64(m.BaseReq.Size()))
		n3, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintHctran(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GuildGetLiveListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildGetLiveListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintHctran(dAtA, i, uint64(m.BaseResp.Size()))
		n4, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintHctran(dAtA, i, uint64(m.GuildId))
	if len(m.LiveRoomList) > 0 {
		for _, msg := range m.LiveRoomList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintHctran(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetTTLivePublishingListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTTLivePublishingListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintHctran(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *GetTTLivePublishingListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTTLivePublishingListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintHctran(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintHctran(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *TTLivePublishingMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TTLivePublishingMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintHctran(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func encodeFixed64Hctran(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Hctran(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintHctran(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GuildGetLiveSummaryReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovHctran(uint64(l))
	}
	n += 1 + sovHctran(uint64(m.GuildId))
	return n
}

func (m *GuildGetLiveSummaryResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovHctran(uint64(l))
	}
	n += 1 + sovHctran(uint64(m.GuildId))
	n += 1 + sovHctran(uint64(m.LiveMembersAmount))
	n += 1 + sovHctran(uint64(m.LiveMemberUid))
	l = len(m.LiveMemberAccount)
	n += 1 + l + sovHctran(uint64(l))
	l = len(m.JumpUrl)
	n += 1 + l + sovHctran(uint64(l))
	l = len(m.NickName)
	n += 1 + l + sovHctran(uint64(l))
	return n
}

func (m *GuildLiveRoomInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovHctran(uint64(m.LiveMemberUid))
	l = len(m.LiveMemberAccount)
	n += 1 + l + sovHctran(uint64(l))
	l = len(m.LiveTopic)
	n += 1 + l + sovHctran(uint64(l))
	n += 1 + sovHctran(uint64(m.UsersAmount))
	l = len(m.JumpUrl)
	n += 1 + l + sovHctran(uint64(l))
	l = len(m.NickName)
	n += 1 + l + sovHctran(uint64(l))
	return n
}

func (m *GuildGetLiveListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovHctran(uint64(l))
	}
	n += 1 + sovHctran(uint64(m.GuildId))
	return n
}

func (m *GuildGetLiveListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovHctran(uint64(l))
	}
	n += 1 + sovHctran(uint64(m.GuildId))
	if len(m.LiveRoomList) > 0 {
		for _, e := range m.LiveRoomList {
			l = e.Size()
			n += 1 + l + sovHctran(uint64(l))
		}
	}
	return n
}

func (m *GetTTLivePublishingListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovHctran(uint64(l))
	}
	return n
}

func (m *GetTTLivePublishingListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovHctran(uint64(l))
	}
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovHctran(uint64(e))
		}
	}
	return n
}

func (m *TTLivePublishingMessage) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovHctran(uint64(e))
		}
	}
	return n
}

func sovHctran(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozHctran(x uint64) (n int) {
	return sovHctran(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GuildGetLiveSummaryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHctran
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildGetLiveSummaryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildGetLiveSummaryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthHctran
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipHctran(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHctran
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildGetLiveSummaryResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHctran
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildGetLiveSummaryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildGetLiveSummaryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthHctran
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LiveMembersAmount", wireType)
			}
			m.LiveMembersAmount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LiveMembersAmount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LiveMemberUid", wireType)
			}
			m.LiveMemberUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LiveMemberUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LiveMemberAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthHctran
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LiveMemberAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthHctran
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NickName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthHctran
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NickName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		default:
			iNdEx = preIndex
			skippy, err := skipHctran(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHctran
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("live_members_amount")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("live_member_uid")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("live_member_account")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("jump_url")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nick_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildLiveRoomInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHctran
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildLiveRoomInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildLiveRoomInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LiveMemberUid", wireType)
			}
			m.LiveMemberUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LiveMemberUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LiveMemberAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthHctran
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LiveMemberAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LiveTopic", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthHctran
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LiveTopic = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UsersAmount", wireType)
			}
			m.UsersAmount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UsersAmount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthHctran
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NickName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthHctran
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NickName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipHctran(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHctran
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("live_member_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("live_member_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("live_topic")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("users_amount")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("jump_url")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nick_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildGetLiveListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHctran
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildGetLiveListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildGetLiveListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthHctran
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipHctran(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHctran
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildGetLiveListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHctran
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildGetLiveListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildGetLiveListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthHctran
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LiveRoomList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthHctran
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LiveRoomList = append(m.LiveRoomList, &GuildLiveRoomInfo{})
			if err := m.LiveRoomList[len(m.LiveRoomList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipHctran(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHctran
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTTLivePublishingListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHctran
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTTLivePublishingListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTTLivePublishingListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthHctran
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipHctran(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHctran
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTTLivePublishingListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHctran
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTTLivePublishingListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTTLivePublishingListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthHctran
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowHctran
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowHctran
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthHctran
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowHctran
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipHctran(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHctran
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TTLivePublishingMessage) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowHctran
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TTLivePublishingMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TTLivePublishingMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowHctran
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowHctran
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthHctran
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowHctran
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipHctran(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthHctran
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipHctran(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowHctran
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowHctran
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthHctran
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowHctran
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipHctran(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthHctran = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowHctran   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("hctran.proto", fileDescriptorHctran) }

var fileDescriptorHctran = []byte{
	// 533 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x54, 0xd1, 0x6e, 0xd3, 0x30,
	0x14, 0x5d, 0xd2, 0x6d, 0x6d, 0x6f, 0x5b, 0xd0, 0x32, 0x60, 0x61, 0x48, 0x5d, 0xe9, 0x24, 0x28,
	0x12, 0x4a, 0xa5, 0x6a, 0x3c, 0xf1, 0xb4, 0x0a, 0xa9, 0x9a, 0xb4, 0x01, 0x0a, 0xdd, 0x0b, 0x0f,
	0x58, 0x6e, 0x62, 0x32, 0x83, 0x1d, 0xa7, 0x71, 0x3c, 0xa9, 0x9f, 0xc0, 0x1b, 0x7f, 0xc0, 0xc7,
	0xf0, 0xb2, 0x47, 0xbe, 0x00, 0xa1, 0xf2, 0x23, 0xc8, 0x4e, 0xd0, 0xb2, 0xb5, 0x30, 0x55, 0xda,
	0x9b, 0x73, 0xee, 0xb9, 0xf7, 0x1c, 0x9f, 0x5c, 0x19, 0x9a, 0x67, 0x41, 0x96, 0xe2, 0xd8, 0x4b,
	0x52, 0x91, 0x09, 0xc7, 0x8e, 0xf0, 0x6e, 0x2b, 0xc2, 0x68, 0x82, 0x25, 0xc9, 0xa1, 0x2e, 0x86,
	0x07, 0x23, 0x45, 0x59, 0x38, 0x22, 0xd9, 0x31, 0x3d, 0x27, 0xef, 0x14, 0xe7, 0x38, 0x9d, 0xf9,
	0x64, 0xea, 0x3c, 0x81, 0x9a, 0xe6, 0xa1, 0x94, 0x4c, 0x5d, 0xab, 0x63, 0xf7, 0x1a, 0x83, 0x86,
	0x17, 0x61, 0x6f, 0x88, 0x25, 0xf1, 0xc9, 0xd4, 0xaf, 0x4e, 0xf2, 0x83, 0xb3, 0x07, 0xb5, 0x48,
	0x4f, 0x40, 0x34, 0x74, 0xed, 0x8e, 0xdd, 0x6b, 0x0d, 0xd7, 0x2f, 0x7e, 0xee, 0xad, 0xf9, 0x55,
	0x83, 0x1e, 0x85, 0xdd, 0xef, 0x36, 0xec, 0x2c, 0xd5, 0x90, 0x89, 0xf3, 0x0c, 0xea, 0x85, 0x88,
	0x4c, 0x0a, 0x95, 0xe6, 0xa5, 0x8a, 0x4c, 0xfc, 0xda, 0xa4, 0x38, 0xdd, 0xa8, 0xe3, 0x1c, 0xc0,
	0x36, 0xa3, 0xe7, 0x04, 0x71, 0xc2, 0x27, 0x24, 0x95, 0x08, 0x73, 0xa1, 0xe2, 0xcc, 0xad, 0x94,
	0xb8, 0x5b, 0x9a, 0x70, 0x92, 0xd7, 0x0f, 0x4d, 0xd9, 0x79, 0x0e, 0x77, 0x4b, 0x5d, 0x48, 0xd1,
	0xd0, 0x5d, 0x2f, 0x75, 0xb4, 0x2e, 0x3b, 0x4e, 0xe9, 0x75, 0x0d, 0x84, 0x83, 0xc0, 0x68, 0x6c,
	0x74, 0xec, 0x5e, 0x7d, 0x51, 0xe3, 0x30, 0x2f, 0x6b, 0xeb, 0x9f, 0x14, 0x4f, 0x90, 0x4a, 0x99,
	0xbb, 0x59, 0xa2, 0x56, 0x35, 0x7a, 0x9a, 0x32, 0xe7, 0x31, 0xd4, 0x63, 0x1a, 0x7c, 0x46, 0x31,
	0xe6, 0xc4, 0xad, 0x96, 0x18, 0x35, 0x0d, 0xbf, 0xc6, 0x9c, 0x74, 0xbf, 0xd8, 0xb0, 0x65, 0x52,
	0xd4, 0x11, 0xfa, 0x42, 0xf0, 0xa3, 0xf8, 0xa3, 0x58, 0xe6, 0xde, 0x5a, 0xd9, 0xbd, 0xfd, 0x7f,
	0xf7, 0xfb, 0x00, 0xa6, 0x2b, 0x13, 0x09, 0x0d, 0x4c, 0x9c, 0x7f, 0xc9, 0x75, 0x8d, 0x8f, 0x35,
	0xec, 0x3c, 0x85, 0xa6, 0x92, 0xa5, 0xd4, 0xcb, 0x19, 0x36, 0x4c, 0xa5, 0xc8, 0xbb, 0x9c, 0xc5,
	0xc6, 0x8d, 0x59, 0x6c, 0x2e, 0xcd, 0xe2, 0x03, 0x6c, 0x97, 0x17, 0xea, 0x98, 0xca, 0xec, 0x56,
	0x37, 0xf6, 0x9b, 0x05, 0xf7, 0x16, 0x05, 0x6e, 0x79, 0x5d, 0x5f, 0xc2, 0x1d, 0x13, 0x6b, 0x2a,
	0x04, 0x47, 0x8c, 0x4a, 0xbd, 0xa9, 0x95, 0x5e, 0x63, 0x70, 0x5f, 0x0f, 0x5c, 0xf8, 0xd3, 0x7e,
	0x93, 0x15, 0x5f, 0xda, 0x4c, 0xf7, 0x15, 0xec, 0x8e, 0x48, 0x36, 0x1e, 0x6b, 0xca, 0x5b, 0x35,
	0x61, 0x54, 0x9e, 0xd1, 0x38, 0x5a, 0x31, 0x88, 0x6e, 0x00, 0x8f, 0xfe, 0x39, 0x65, 0xb5, 0xdb,
	0x3e, 0x84, 0x9a, 0xa2, 0x61, 0x7e, 0x0d, 0xbb, 0x53, 0xe9, 0xb5, 0xfc, 0xaa, 0xa2, 0xa1, 0xb1,
	0x7a, 0x00, 0x3b, 0xd7, 0x15, 0x4e, 0x88, 0x94, 0x38, 0x22, 0x57, 0xba, 0xac, 0x2b, 0x5d, 0xc3,
	0x37, 0x17, 0xf3, 0xb6, 0xf5, 0x63, 0xde, 0xb6, 0x7e, 0xcd, 0xdb, 0xd6, 0xd7, 0xdf, 0xed, 0x35,
	0x70, 0x03, 0xc1, 0xbd, 0x19, 0x9d, 0x09, 0xa5, 0x3d, 0x70, 0x11, 0x12, 0x96, 0xbf, 0x61, 0xef,
	0xf7, 0x23, 0xc1, 0x70, 0x1c, 0x79, 0x2f, 0x06, 0x59, 0xe6, 0x05, 0x82, 0xf7, 0x0d, 0x1c, 0x08,
	0xd6, 0xc7, 0x49, 0xd2, 0xcf, 0x5f, 0xc0, 0x3f, 0x01, 0x00, 0x00, 0xff, 0xff, 0xc6, 0xf1, 0xae,
	0x86, 0x0a, 0x05, 0x00, 0x00,
}
