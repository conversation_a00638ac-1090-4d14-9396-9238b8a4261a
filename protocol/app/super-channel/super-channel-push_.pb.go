// Code generated by protoc-gen-go. DO NOT EDIT.
// source: super-channel-push_.proto

package super_channel // import "golang.52tt.com/protocol/app/super-channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SuperChannelMicOperateInfo_OperateType int32

const (
	SuperChannelMicOperateInfo_UNKNOWN SuperChannelMicOperateInfo_OperateType = 0
	SuperChannelMicOperateInfo_HOLD    SuperChannelMicOperateInfo_OperateType = 1
	SuperChannelMicOperateInfo_RELEASE SuperChannelMicOperateInfo_OperateType = 2
	SuperChannelMicOperateInfo_MUTE    SuperChannelMicOperateInfo_OperateType = 3
	SuperChannelMicOperateInfo_UN_MUTE SuperChannelMicOperateInfo_OperateType = 4
)

var SuperChannelMicOperateInfo_OperateType_name = map[int32]string{
	0: "UNKNOWN",
	1: "HOLD",
	2: "RELEASE",
	3: "MUTE",
	4: "UN_MUTE",
}
var SuperChannelMicOperateInfo_OperateType_value = map[string]int32{
	"UNKNOWN": 0,
	"HOLD":    1,
	"RELEASE": 2,
	"MUTE":    3,
	"UN_MUTE": 4,
}

func (x SuperChannelMicOperateInfo_OperateType) String() string {
	return proto.EnumName(SuperChannelMicOperateInfo_OperateType_name, int32(x))
}
func (SuperChannelMicOperateInfo_OperateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_push__33b0924205a7e421, []int{2, 0}
}

// 麦位相关推送
type SuperChannelMicHoldInfo struct {
	MicId                uint32   `protobuf:"varint,1,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	MicState             uint32   `protobuf:"varint,2,opt,name=mic_state,json=micState,proto3" json:"mic_state,omitempty"`
	MicUid               uint32   `protobuf:"varint,3,opt,name=mic_uid,json=micUid,proto3" json:"mic_uid,omitempty"`
	MicTs                int64    `protobuf:"varint,4,opt,name=mic_ts,json=micTs,proto3" json:"mic_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SuperChannelMicHoldInfo) Reset()         { *m = SuperChannelMicHoldInfo{} }
func (m *SuperChannelMicHoldInfo) String() string { return proto.CompactTextString(m) }
func (*SuperChannelMicHoldInfo) ProtoMessage()    {}
func (*SuperChannelMicHoldInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_push__33b0924205a7e421, []int{0}
}
func (m *SuperChannelMicHoldInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperChannelMicHoldInfo.Unmarshal(m, b)
}
func (m *SuperChannelMicHoldInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperChannelMicHoldInfo.Marshal(b, m, deterministic)
}
func (dst *SuperChannelMicHoldInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperChannelMicHoldInfo.Merge(dst, src)
}
func (m *SuperChannelMicHoldInfo) XXX_Size() int {
	return xxx_messageInfo_SuperChannelMicHoldInfo.Size(m)
}
func (m *SuperChannelMicHoldInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperChannelMicHoldInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SuperChannelMicHoldInfo proto.InternalMessageInfo

func (m *SuperChannelMicHoldInfo) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *SuperChannelMicHoldInfo) GetMicState() uint32 {
	if m != nil {
		return m.MicState
	}
	return 0
}

func (m *SuperChannelMicHoldInfo) GetMicUid() uint32 {
	if m != nil {
		return m.MicUid
	}
	return 0
}

func (m *SuperChannelMicHoldInfo) GetMicTs() int64 {
	if m != nil {
		return m.MicTs
	}
	return 0
}

type SuperChannelMicAddInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	FaceMd5              string   `protobuf:"bytes,5,opt,name=face_md5,json=faceMd5,proto3" json:"face_md5,omitempty"`
	NobilityLevel        uint32   `protobuf:"varint,6,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SuperChannelMicAddInfo) Reset()         { *m = SuperChannelMicAddInfo{} }
func (m *SuperChannelMicAddInfo) String() string { return proto.CompactTextString(m) }
func (*SuperChannelMicAddInfo) ProtoMessage()    {}
func (*SuperChannelMicAddInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_push__33b0924205a7e421, []int{1}
}
func (m *SuperChannelMicAddInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperChannelMicAddInfo.Unmarshal(m, b)
}
func (m *SuperChannelMicAddInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperChannelMicAddInfo.Marshal(b, m, deterministic)
}
func (dst *SuperChannelMicAddInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperChannelMicAddInfo.Merge(dst, src)
}
func (m *SuperChannelMicAddInfo) XXX_Size() int {
	return xxx_messageInfo_SuperChannelMicAddInfo.Size(m)
}
func (m *SuperChannelMicAddInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperChannelMicAddInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SuperChannelMicAddInfo proto.InternalMessageInfo

func (m *SuperChannelMicAddInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SuperChannelMicAddInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SuperChannelMicAddInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *SuperChannelMicAddInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *SuperChannelMicAddInfo) GetFaceMd5() string {
	if m != nil {
		return m.FaceMd5
	}
	return ""
}

func (m *SuperChannelMicAddInfo) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

type SuperChannelMicOperateInfo struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	OperateType          uint32   `protobuf:"varint,2,opt,name=operate_type,json=operateType,proto3" json:"operate_type,omitempty"`
	OperateUid           uint32   `protobuf:"varint,3,opt,name=operate_uid,json=operateUid,proto3" json:"operate_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SuperChannelMicOperateInfo) Reset()         { *m = SuperChannelMicOperateInfo{} }
func (m *SuperChannelMicOperateInfo) String() string { return proto.CompactTextString(m) }
func (*SuperChannelMicOperateInfo) ProtoMessage()    {}
func (*SuperChannelMicOperateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_push__33b0924205a7e421, []int{2}
}
func (m *SuperChannelMicOperateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperChannelMicOperateInfo.Unmarshal(m, b)
}
func (m *SuperChannelMicOperateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperChannelMicOperateInfo.Marshal(b, m, deterministic)
}
func (dst *SuperChannelMicOperateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperChannelMicOperateInfo.Merge(dst, src)
}
func (m *SuperChannelMicOperateInfo) XXX_Size() int {
	return xxx_messageInfo_SuperChannelMicOperateInfo.Size(m)
}
func (m *SuperChannelMicOperateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperChannelMicOperateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SuperChannelMicOperateInfo proto.InternalMessageInfo

func (m *SuperChannelMicOperateInfo) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SuperChannelMicOperateInfo) GetOperateType() uint32 {
	if m != nil {
		return m.OperateType
	}
	return 0
}

func (m *SuperChannelMicOperateInfo) GetOperateUid() uint32 {
	if m != nil {
		return m.OperateUid
	}
	return 0
}

type SuperChannelMicChangeEvent struct {
	MicMode              uint32                      `protobuf:"varint,1,opt,name=mic_mode,json=micMode,proto3" json:"mic_mode,omitempty"`
	ServerTimeMs         int64                       `protobuf:"varint,2,opt,name=server_time_ms,json=serverTimeMs,proto3" json:"server_time_ms,omitempty"`
	AllMicList           []*SuperChannelMicHoldInfo  `protobuf:"bytes,3,rep,name=all_mic_list,json=allMicList,proto3" json:"all_mic_list,omitempty"`
	AddInfoList          []*SuperChannelMicAddInfo   `protobuf:"bytes,4,rep,name=add_info_list,json=addInfoList,proto3" json:"add_info_list,omitempty"`
	IsAllowClientSync    bool                        `protobuf:"varint,5,opt,name=is_allow_client_sync,json=isAllowClientSync,proto3" json:"is_allow_client_sync,omitempty"`
	OperateInfo          *SuperChannelMicOperateInfo `protobuf:"bytes,6,opt,name=operate_info,json=operateInfo,proto3" json:"operate_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *SuperChannelMicChangeEvent) Reset()         { *m = SuperChannelMicChangeEvent{} }
func (m *SuperChannelMicChangeEvent) String() string { return proto.CompactTextString(m) }
func (*SuperChannelMicChangeEvent) ProtoMessage()    {}
func (*SuperChannelMicChangeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_push__33b0924205a7e421, []int{3}
}
func (m *SuperChannelMicChangeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperChannelMicChangeEvent.Unmarshal(m, b)
}
func (m *SuperChannelMicChangeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperChannelMicChangeEvent.Marshal(b, m, deterministic)
}
func (dst *SuperChannelMicChangeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperChannelMicChangeEvent.Merge(dst, src)
}
func (m *SuperChannelMicChangeEvent) XXX_Size() int {
	return xxx_messageInfo_SuperChannelMicChangeEvent.Size(m)
}
func (m *SuperChannelMicChangeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperChannelMicChangeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_SuperChannelMicChangeEvent proto.InternalMessageInfo

func (m *SuperChannelMicChangeEvent) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

func (m *SuperChannelMicChangeEvent) GetServerTimeMs() int64 {
	if m != nil {
		return m.ServerTimeMs
	}
	return 0
}

func (m *SuperChannelMicChangeEvent) GetAllMicList() []*SuperChannelMicHoldInfo {
	if m != nil {
		return m.AllMicList
	}
	return nil
}

func (m *SuperChannelMicChangeEvent) GetAddInfoList() []*SuperChannelMicAddInfo {
	if m != nil {
		return m.AddInfoList
	}
	return nil
}

func (m *SuperChannelMicChangeEvent) GetIsAllowClientSync() bool {
	if m != nil {
		return m.IsAllowClientSync
	}
	return false
}

func (m *SuperChannelMicChangeEvent) GetOperateInfo() *SuperChannelMicOperateInfo {
	if m != nil {
		return m.OperateInfo
	}
	return nil
}

type SuperChannelHoldMicInviteEvent struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MicId                uint32   `protobuf:"varint,2,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	Ticket               string   `protobuf:"bytes,3,opt,name=ticket,proto3" json:"ticket,omitempty"`
	ServerTime           int64    `protobuf:"varint,4,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	ExpireTime           int64    `protobuf:"varint,5,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SuperChannelHoldMicInviteEvent) Reset()         { *m = SuperChannelHoldMicInviteEvent{} }
func (m *SuperChannelHoldMicInviteEvent) String() string { return proto.CompactTextString(m) }
func (*SuperChannelHoldMicInviteEvent) ProtoMessage()    {}
func (*SuperChannelHoldMicInviteEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_push__33b0924205a7e421, []int{4}
}
func (m *SuperChannelHoldMicInviteEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperChannelHoldMicInviteEvent.Unmarshal(m, b)
}
func (m *SuperChannelHoldMicInviteEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperChannelHoldMicInviteEvent.Marshal(b, m, deterministic)
}
func (dst *SuperChannelHoldMicInviteEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperChannelHoldMicInviteEvent.Merge(dst, src)
}
func (m *SuperChannelHoldMicInviteEvent) XXX_Size() int {
	return xxx_messageInfo_SuperChannelHoldMicInviteEvent.Size(m)
}
func (m *SuperChannelHoldMicInviteEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperChannelHoldMicInviteEvent.DiscardUnknown(m)
}

var xxx_messageInfo_SuperChannelHoldMicInviteEvent proto.InternalMessageInfo

func (m *SuperChannelHoldMicInviteEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SuperChannelHoldMicInviteEvent) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *SuperChannelHoldMicInviteEvent) GetTicket() string {
	if m != nil {
		return m.Ticket
	}
	return ""
}

func (m *SuperChannelHoldMicInviteEvent) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *SuperChannelHoldMicInviteEvent) GetExpireTime() int64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

// topN和在线人数
type SuperChannelTopNMemberInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	GiftCount            uint32   `protobuf:"varint,3,opt,name=gift_count,json=giftCount,proto3" json:"gift_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SuperChannelTopNMemberInfo) Reset()         { *m = SuperChannelTopNMemberInfo{} }
func (m *SuperChannelTopNMemberInfo) String() string { return proto.CompactTextString(m) }
func (*SuperChannelTopNMemberInfo) ProtoMessage()    {}
func (*SuperChannelTopNMemberInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_push__33b0924205a7e421, []int{5}
}
func (m *SuperChannelTopNMemberInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperChannelTopNMemberInfo.Unmarshal(m, b)
}
func (m *SuperChannelTopNMemberInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperChannelTopNMemberInfo.Marshal(b, m, deterministic)
}
func (dst *SuperChannelTopNMemberInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperChannelTopNMemberInfo.Merge(dst, src)
}
func (m *SuperChannelTopNMemberInfo) XXX_Size() int {
	return xxx_messageInfo_SuperChannelTopNMemberInfo.Size(m)
}
func (m *SuperChannelTopNMemberInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperChannelTopNMemberInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SuperChannelTopNMemberInfo proto.InternalMessageInfo

func (m *SuperChannelTopNMemberInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SuperChannelTopNMemberInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SuperChannelTopNMemberInfo) GetGiftCount() uint32 {
	if m != nil {
		return m.GiftCount
	}
	return 0
}

type SuperChannelMemberTopN struct {
	AllMemberCnt         uint32                        `protobuf:"varint,1,opt,name=all_member_cnt,json=allMemberCnt,proto3" json:"all_member_cnt,omitempty"`
	MemberList           []*SuperChannelTopNMemberInfo `protobuf:"bytes,2,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *SuperChannelMemberTopN) Reset()         { *m = SuperChannelMemberTopN{} }
func (m *SuperChannelMemberTopN) String() string { return proto.CompactTextString(m) }
func (*SuperChannelMemberTopN) ProtoMessage()    {}
func (*SuperChannelMemberTopN) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_push__33b0924205a7e421, []int{6}
}
func (m *SuperChannelMemberTopN) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperChannelMemberTopN.Unmarshal(m, b)
}
func (m *SuperChannelMemberTopN) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperChannelMemberTopN.Marshal(b, m, deterministic)
}
func (dst *SuperChannelMemberTopN) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperChannelMemberTopN.Merge(dst, src)
}
func (m *SuperChannelMemberTopN) XXX_Size() int {
	return xxx_messageInfo_SuperChannelMemberTopN.Size(m)
}
func (m *SuperChannelMemberTopN) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperChannelMemberTopN.DiscardUnknown(m)
}

var xxx_messageInfo_SuperChannelMemberTopN proto.InternalMessageInfo

func (m *SuperChannelMemberTopN) GetAllMemberCnt() uint32 {
	if m != nil {
		return m.AllMemberCnt
	}
	return 0
}

func (m *SuperChannelMemberTopN) GetMemberList() []*SuperChannelTopNMemberInfo {
	if m != nil {
		return m.MemberList
	}
	return nil
}

type SuperChannelInfoChangeEvent struct {
	Info                 *SuperChannelInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SuperChannelInfoChangeEvent) Reset()         { *m = SuperChannelInfoChangeEvent{} }
func (m *SuperChannelInfoChangeEvent) String() string { return proto.CompactTextString(m) }
func (*SuperChannelInfoChangeEvent) ProtoMessage()    {}
func (*SuperChannelInfoChangeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_super_channel_push__33b0924205a7e421, []int{7}
}
func (m *SuperChannelInfoChangeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuperChannelInfoChangeEvent.Unmarshal(m, b)
}
func (m *SuperChannelInfoChangeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuperChannelInfoChangeEvent.Marshal(b, m, deterministic)
}
func (dst *SuperChannelInfoChangeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuperChannelInfoChangeEvent.Merge(dst, src)
}
func (m *SuperChannelInfoChangeEvent) XXX_Size() int {
	return xxx_messageInfo_SuperChannelInfoChangeEvent.Size(m)
}
func (m *SuperChannelInfoChangeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_SuperChannelInfoChangeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_SuperChannelInfoChangeEvent proto.InternalMessageInfo

func (m *SuperChannelInfoChangeEvent) GetInfo() *SuperChannelInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func init() {
	proto.RegisterType((*SuperChannelMicHoldInfo)(nil), "ga.SuperChannelMicHoldInfo")
	proto.RegisterType((*SuperChannelMicAddInfo)(nil), "ga.SuperChannelMicAddInfo")
	proto.RegisterType((*SuperChannelMicOperateInfo)(nil), "ga.SuperChannelMicOperateInfo")
	proto.RegisterType((*SuperChannelMicChangeEvent)(nil), "ga.SuperChannelMicChangeEvent")
	proto.RegisterType((*SuperChannelHoldMicInviteEvent)(nil), "ga.SuperChannelHoldMicInviteEvent")
	proto.RegisterType((*SuperChannelTopNMemberInfo)(nil), "ga.SuperChannelTopNMemberInfo")
	proto.RegisterType((*SuperChannelMemberTopN)(nil), "ga.SuperChannelMemberTopN")
	proto.RegisterType((*SuperChannelInfoChangeEvent)(nil), "ga.SuperChannelInfoChangeEvent")
	proto.RegisterEnum("ga.SuperChannelMicOperateInfo_OperateType", SuperChannelMicOperateInfo_OperateType_name, SuperChannelMicOperateInfo_OperateType_value)
}

func init() {
	proto.RegisterFile("super-channel-push_.proto", fileDescriptor_super_channel_push__33b0924205a7e421)
}

var fileDescriptor_super_channel_push__33b0924205a7e421 = []byte{
	// 752 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x54, 0x5d, 0x8f, 0xe3, 0x34,
	0x14, 0x25, 0xfd, 0x9a, 0xf6, 0x76, 0x66, 0x55, 0xa2, 0x61, 0x37, 0x3b, 0xa3, 0x59, 0x86, 0x08,
	0xa4, 0x0a, 0x69, 0x5b, 0x69, 0xd0, 0x3c, 0x02, 0x2a, 0xa5, 0x30, 0x15, 0xfd, 0x90, 0xd2, 0x56,
	0x48, 0xbc, 0x58, 0x1e, 0xc7, 0xcd, 0x5a, 0x9b, 0xd8, 0x51, 0xe2, 0x96, 0xed, 0x03, 0xe2, 0x2f,
	0xf1, 0xc2, 0x8f, 0xe0, 0x8d, 0x9f, 0x84, 0xae, 0x9d, 0x6e, 0xd3, 0xee, 0xf0, 0xb0, 0x6f, 0xbe,
	0xe7, 0x1e, 0xdf, 0x6b, 0xfb, 0x9c, 0x6b, 0x78, 0x99, 0x6f, 0x52, 0x9e, 0xbd, 0x66, 0x6f, 0xa8,
	0x94, 0x3c, 0x7e, 0x9d, 0x6e, 0xf2, 0x37, 0xa4, 0x97, 0x66, 0x4a, 0x2b, 0xb7, 0x12, 0xd1, 0xab,
	0xcb, 0xa3, 0x74, 0x91, 0xf1, 0xff, 0x80, 0x17, 0x0b, 0xc4, 0x87, 0x16, 0x9e, 0x0a, 0xf6, 0xa0,
	0xe2, 0x70, 0x2c, 0xd7, 0xca, 0xfd, 0x0c, 0x1a, 0x89, 0x60, 0x44, 0x84, 0x9e, 0x73, 0xeb, 0x74,
	0x2f, 0x82, 0x7a, 0x22, 0xd8, 0x38, 0x74, 0xaf, 0xa1, 0x85, 0x70, 0xae, 0xa9, 0xe6, 0x5e, 0xc5,
	0x64, 0x9a, 0x89, 0x60, 0x0b, 0x8c, 0xdd, 0x17, 0x70, 0x86, 0xc9, 0x8d, 0x08, 0xbd, 0xaa, 0x49,
	0x61, 0x89, 0x95, 0x08, 0xf7, 0xc5, 0x74, 0xee, 0xd5, 0x6e, 0x9d, 0x6e, 0xd5, 0x14, 0x5b, 0xe6,
	0xfe, 0xdf, 0x0e, 0x3c, 0x3f, 0xe9, 0x3f, 0x08, 0x6d, 0xfb, 0x0e, 0x54, 0x37, 0xef, 0x7b, 0xe3,
	0xd2, 0xf5, 0xe0, 0x8c, 0x32, 0xa6, 0x36, 0x52, 0x9b, 0xbe, 0xad, 0x60, 0x1f, 0xe2, 0x99, 0xa4,
	0x60, 0x6f, 0x89, 0xa4, 0x09, 0x37, 0x8d, 0x5b, 0x41, 0x13, 0x81, 0x19, 0x4d, 0x38, 0x16, 0xca,
	0xf9, 0x3b, 0xd3, 0xb7, 0x1e, 0xe0, 0xd2, 0x7d, 0x09, 0xcd, 0x35, 0x65, 0x9c, 0x24, 0xe1, 0xbd,
	0x57, 0xb7, 0x95, 0x30, 0x9e, 0x86, 0xf7, 0xee, 0x57, 0xf0, 0x4c, 0xaa, 0x47, 0x11, 0x0b, 0xbd,
	0x23, 0x31, 0xdf, 0xf2, 0xd8, 0x6b, 0x98, 0x03, 0x5c, 0xec, 0xd1, 0x09, 0x82, 0xfe, 0x3f, 0x0e,
	0x5c, 0x9d, 0x9c, 0x7b, 0x9e, 0xf2, 0x8c, 0x6a, 0x6e, 0xce, 0x7e, 0x03, 0xa0, 0x69, 0x16, 0x71,
	0x4d, 0x0e, 0x57, 0x68, 0x59, 0x04, 0x1f, 0xe3, 0x0b, 0x38, 0x57, 0x96, 0x4d, 0xf4, 0x2e, 0xdd,
	0xbf, 0x62, 0xbb, 0xc0, 0x96, 0xbb, 0x94, 0xbb, 0x9f, 0xc3, 0x3e, 0x2c, 0x3d, 0x26, 0x14, 0xd0,
	0x4a, 0x84, 0xfe, 0x03, 0xb4, 0xe7, 0x25, 0x7e, 0x1b, 0xce, 0x56, 0xb3, 0x5f, 0x66, 0xf3, 0x5f,
	0x67, 0x9d, 0x4f, 0xdc, 0x26, 0xd4, 0x1e, 0xe6, 0x93, 0x1f, 0x3b, 0x0e, 0xc2, 0xc1, 0x68, 0x32,
	0x1a, 0x2c, 0x46, 0x9d, 0x0a, 0xc2, 0xd3, 0xd5, 0x72, 0xd4, 0xa9, 0x5a, 0x36, 0x31, 0x41, 0xcd,
	0xff, 0xb7, 0xf2, 0xc1, 0x5d, 0x70, 0x15, 0xf1, 0xd1, 0x96, 0x4b, 0x8d, 0x8f, 0x85, 0xca, 0x25,
	0x2a, 0xe4, 0xc5, 0x4d, 0x50, 0xe2, 0xa9, 0x0a, 0xb9, 0xfb, 0x25, 0x3c, 0xcb, 0x79, 0xb6, 0xe5,
	0x19, 0xd1, 0x22, 0xe1, 0x24, 0xc9, 0xcd, 0x4d, 0xaa, 0xc1, 0xb9, 0x45, 0x97, 0x22, 0xe1, 0xd3,
	0xdc, 0xfd, 0x16, 0xce, 0x69, 0x1c, 0x13, 0x2c, 0x12, 0x8b, 0x5c, 0x7b, 0xd5, 0xdb, 0x6a, 0xb7,
	0x7d, 0x77, 0xdd, 0x8b, 0x68, 0xef, 0x7f, 0xac, 0x17, 0x00, 0x8d, 0x31, 0x9e, 0x88, 0x5c, 0xbb,
	0xdf, 0xc1, 0x05, 0x0d, 0x43, 0x22, 0xe4, 0x5a, 0xd9, 0xfd, 0x35, 0xb3, 0xff, 0xea, 0x89, 0xfd,
	0x85, 0x75, 0x82, 0x36, 0xb5, 0x0b, 0xb3, 0xbf, 0x0f, 0x97, 0x22, 0x27, 0x34, 0x8e, 0xd5, 0xef,
	0x84, 0xc5, 0x82, 0x4b, 0x4d, 0xf2, 0x9d, 0x64, 0x46, 0xf8, 0x66, 0xf0, 0xa9, 0xc8, 0x07, 0x98,
	0x1a, 0x9a, 0xcc, 0x62, 0x27, 0x99, 0x3b, 0x38, 0xa8, 0x83, 0x4d, 0x8d, 0x01, 0xda, 0x77, 0xaf,
	0x9e, 0xe8, 0x57, 0x92, 0xfc, 0xbd, 0x7a, 0x18, 0xf8, 0x7f, 0x39, 0xf0, 0xaa, 0xcc, 0xc5, 0x8b,
	0x4d, 0x05, 0x1b, 0xcb, 0xad, 0xd0, 0xc5, 0xb3, 0xde, 0x00, 0xec, 0x47, 0xf1, 0x60, 0x91, 0x02,
	0x19, 0x87, 0xa5, 0xe1, 0xab, 0x94, 0x87, 0xef, 0x39, 0x34, 0xb4, 0x60, 0x6f, 0xb9, 0x2e, 0x5c,
	0x5e, 0x44, 0x68, 0x97, 0x92, 0x12, 0xc5, 0x8c, 0xc1, 0x41, 0x06, 0x24, 0xf0, 0x77, 0xa9, 0xc8,
	0xb8, 0x25, 0xd4, 0x2d, 0xc1, 0x42, 0x48, 0xf0, 0xa3, 0x63, 0x13, 0x2c, 0x55, 0x3a, 0x9b, 0xf2,
	0xe4, 0x91, 0x67, 0x1f, 0x3d, 0x8c, 0x37, 0x00, 0x91, 0x58, 0x6b, 0x62, 0x93, 0xd6, 0xb9, 0x2d,
	0x44, 0x86, 0x08, 0xf8, 0x7f, 0x9e, 0x4c, 0xbc, 0x69, 0x82, 0xed, 0xd0, 0x4e, 0xc6, 0x28, 0x06,
	0x21, 0x4c, 0xea, 0xa2, 0x1f, 0xda, 0xc7, 0xd2, 0x86, 0x52, 0xbb, 0xdf, 0x43, 0xbb, 0x60, 0x18,
	0x37, 0x54, 0x8c, 0x1b, 0x3e, 0x50, 0xe7, 0xf8, 0xfc, 0x01, 0xd8, 0x2d, 0x68, 0x08, 0xff, 0x67,
	0xb8, 0x2e, 0x33, 0x31, 0x5f, 0xf6, 0x7b, 0x17, 0x6a, 0x46, 0x76, 0xc7, 0xc8, 0x7e, 0x79, 0x5a,
	0xd8, 0x94, 0x33, 0x8c, 0x1f, 0x7e, 0x02, 0x8f, 0xa9, 0xa4, 0xb7, 0x13, 0x3b, 0xb5, 0x41, 0x1a,
	0x8e, 0x48, 0x6c, 0xff, 0xd5, 0xdf, 0xbe, 0x8e, 0x54, 0x4c, 0x65, 0xd4, 0xbb, 0xbf, 0xd3, 0xba,
	0xc7, 0x54, 0xd2, 0x37, 0x30, 0x53, 0x71, 0x9f, 0xa6, 0x69, 0xff, 0xe8, 0x2b, 0x7e, 0x6c, 0x98,
	0xdc, 0x37, 0xff, 0x05, 0x00, 0x00, 0xff, 0xff, 0x09, 0xe8, 0x5e, 0x2e, 0xc1, 0x05, 0x00, 0x00,
}
