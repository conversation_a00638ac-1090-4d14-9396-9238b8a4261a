// Code generated by protoc-gen-gogo.
// source: feature.proto
// DO NOT EDIT!

/*
	Package feature is a generated protocol buffer package.

	It is generated from these files:
		feature.proto

	It has these top-level messages:
		FeatureCode
*/
package feature

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"

import "io"
import fmt1 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type FeatureCode struct {
	Guild      uint32 `protobuf:"varint,1,opt,name=guild" json:"guild"`
	Channel    string `protobuf:"bytes,2,opt,name=channel" json:"channel"`
	InviteCode string `protobuf:"bytes,3,opt,name=invite_code,json=inviteCode" json:"invite_code"`
	Timestamp  uint32 `protobuf:"varint,4,opt,name=timestamp" json:"timestamp"`
	TtGameId   uint32 `protobuf:"varint,5,opt,name=tt_game_id,json=ttGameId" json:"tt_game_id"`
	// 新加字段在上面搞
	Sentinel string `protobuf:"bytes,128,opt,name=sentinel" json:"sentinel"`
}

func (m *FeatureCode) Reset()                    { *m = FeatureCode{} }
func (m *FeatureCode) String() string            { return proto.CompactTextString(m) }
func (*FeatureCode) ProtoMessage()               {}
func (*FeatureCode) Descriptor() ([]byte, []int) { return fileDescriptorFeature, []int{0} }

func (m *FeatureCode) GetGuild() uint32 {
	if m != nil {
		return m.Guild
	}
	return 0
}

func (m *FeatureCode) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *FeatureCode) GetInviteCode() string {
	if m != nil {
		return m.InviteCode
	}
	return ""
}

func (m *FeatureCode) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *FeatureCode) GetTtGameId() uint32 {
	if m != nil {
		return m.TtGameId
	}
	return 0
}

func (m *FeatureCode) GetSentinel() string {
	if m != nil {
		return m.Sentinel
	}
	return ""
}

func init() {
	proto.RegisterType((*FeatureCode)(nil), "ga.FeatureCode")
}
func (m *FeatureCode) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FeatureCode) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFeature(dAtA, i, uint64(m.Guild))
	dAtA[i] = 0x12
	i++
	i = encodeVarintFeature(dAtA, i, uint64(len(m.Channel)))
	i += copy(dAtA[i:], m.Channel)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFeature(dAtA, i, uint64(len(m.InviteCode)))
	i += copy(dAtA[i:], m.InviteCode)
	dAtA[i] = 0x20
	i++
	i = encodeVarintFeature(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x28
	i++
	i = encodeVarintFeature(dAtA, i, uint64(m.TtGameId))
	dAtA[i] = 0x82
	i++
	dAtA[i] = 0x8
	i++
	i = encodeVarintFeature(dAtA, i, uint64(len(m.Sentinel)))
	i += copy(dAtA[i:], m.Sentinel)
	return i, nil
}

func encodeFixed64Feature(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Feature(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintFeature(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *FeatureCode) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFeature(uint64(m.Guild))
	l = len(m.Channel)
	n += 1 + l + sovFeature(uint64(l))
	l = len(m.InviteCode)
	n += 1 + l + sovFeature(uint64(l))
	n += 1 + sovFeature(uint64(m.Timestamp))
	n += 1 + sovFeature(uint64(m.TtGameId))
	l = len(m.Sentinel)
	n += 2 + l + sovFeature(uint64(l))
	return n
}

func sovFeature(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozFeature(x uint64) (n int) {
	return sovFeature(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *FeatureCode) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFeature
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: FeatureCode: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: FeatureCode: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Guild", wireType)
			}
			m.Guild = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFeature
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Guild |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Channel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFeature
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFeature
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Channel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field InviteCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFeature
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFeature
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InviteCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFeature
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TtGameId", wireType)
			}
			m.TtGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFeature
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TtGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 128:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Sentinel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFeature
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFeature
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Sentinel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFeature(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFeature
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipFeature(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowFeature
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFeature
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFeature
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthFeature
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowFeature
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipFeature(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt1.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthFeature = fmt1.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowFeature   = fmt1.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("feature.proto", fileDescriptorFeature) }

var fileDescriptorFeature = []byte{
	// 254 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x4c, 0xd0, 0xb1, 0x4a, 0x3b, 0x41,
	0x10, 0x06, 0xf0, 0x6c, 0xfe, 0x09, 0xff, 0x64, 0x42, 0x9a, 0xad, 0x16, 0x8b, 0x33, 0x1e, 0x0a,
	0xa9, 0xf6, 0x40, 0xf0, 0x05, 0x22, 0x28, 0x76, 0x62, 0x69, 0x73, 0x2c, 0xb7, 0xe3, 0xba, 0xb0,
	0xbb, 0x73, 0x24, 0x73, 0x42, 0x3a, 0x1f, 0xc1, 0xc7, 0x4a, 0x69, 0x2f, 0x88, 0x9c, 0x2f, 0x22,
	0xe6, 0xa2, 0x5e, 0xfb, 0x9b, 0x0f, 0xbe, 0xe1, 0x83, 0xf9, 0x03, 0x1a, 0x6e, 0xd6, 0xa8, 0xeb,
	0x35, 0x31, 0xc9, 0xa1, 0x33, 0xf9, 0x9b, 0x80, 0xd9, 0x55, 0xa7, 0x97, 0x64, 0x51, 0x1e, 0xc1,
	0xd8, 0x35, 0x3e, 0x58, 0x25, 0x16, 0x62, 0x39, 0x5f, 0x8d, 0x76, 0xef, 0xc7, 0x83, 0xbb, 0x8e,
	0x64, 0x06, 0xff, 0xab, 0x47, 0x93, 0x12, 0x06, 0x35, 0x5c, 0x88, 0xe5, 0xf4, 0x70, 0xfd, 0x41,
	0x79, 0x06, 0x33, 0x9f, 0x9e, 0x3c, 0x63, 0x59, 0x91, 0x45, 0xf5, 0xaf, 0x97, 0x81, 0xee, 0xb0,
	0xaf, 0xc8, 0x61, 0xca, 0x3e, 0xe2, 0x86, 0x4d, 0xac, 0xd5, 0xa8, 0x57, 0xf3, 0xc7, 0x32, 0x07,
	0x60, 0x2e, 0x9d, 0x89, 0x58, 0x7a, 0xab, 0xc6, 0xbd, 0xd0, 0x84, 0xf9, 0xda, 0x44, 0xbc, 0xb1,
	0xf2, 0x04, 0x26, 0x1b, 0x4c, 0xec, 0xbf, 0xff, 0x79, 0x16, 0xbd, 0xb2, 0x5f, 0x5e, 0xdd, 0xee,
	0xda, 0x4c, 0xbc, 0xb6, 0x99, 0xf8, 0x68, 0x33, 0xf1, 0xf2, 0x99, 0x0d, 0x40, 0x55, 0x14, 0xf5,
	0xd6, 0x6f, 0xa9, 0xd1, 0xce, 0xe8, 0x48, 0x16, 0x43, 0xb7, 0xc6, 0xfd, 0xa9, 0xa3, 0x60, 0x92,
	0xd3, 0x17, 0xe7, 0xcc, 0xba, 0xa2, 0x58, 0xec, 0xb9, 0xa2, 0x50, 0x98, 0xba, 0x2e, 0x0e, 0xcb,
	0x7d, 0x05, 0x00, 0x00, 0xff, 0xff, 0xbd, 0xac, 0xa6, 0x4b, 0x43, 0x01, 0x00, 0x00,
}
