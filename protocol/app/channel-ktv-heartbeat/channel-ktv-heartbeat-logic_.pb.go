// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-ktv-heartbeat-logic_.proto

package channel_ktv_heartbeat // import "golang.52tt.com/protocol/app/channel-ktv-heartbeat"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import channel_ktv "golang.52tt.com/protocol/app/channel-ktv"
import concert_logic "golang.52tt.com/protocol/app/concert-logic"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 上报心跳
type ReportKTVHeartbeatReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ReportKTVHeartbeatReq) Reset()         { *m = ReportKTVHeartbeatReq{} }
func (m *ReportKTVHeartbeatReq) String() string { return proto.CompactTextString(m) }
func (*ReportKTVHeartbeatReq) ProtoMessage()    {}
func (*ReportKTVHeartbeatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_heartbeat_logic__dfe0a275ebdcd667, []int{0}
}
func (m *ReportKTVHeartbeatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportKTVHeartbeatReq.Unmarshal(m, b)
}
func (m *ReportKTVHeartbeatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportKTVHeartbeatReq.Marshal(b, m, deterministic)
}
func (dst *ReportKTVHeartbeatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportKTVHeartbeatReq.Merge(dst, src)
}
func (m *ReportKTVHeartbeatReq) XXX_Size() int {
	return xxx_messageInfo_ReportKTVHeartbeatReq.Size(m)
}
func (m *ReportKTVHeartbeatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportKTVHeartbeatReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportKTVHeartbeatReq proto.InternalMessageInfo

func (m *ReportKTVHeartbeatReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportKTVHeartbeatReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ReportKTVHeartbeatResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ReportDuration       uint32        `protobuf:"varint,2,opt,name=report_duration,json=reportDuration,proto3" json:"report_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportKTVHeartbeatResp) Reset()         { *m = ReportKTVHeartbeatResp{} }
func (m *ReportKTVHeartbeatResp) String() string { return proto.CompactTextString(m) }
func (*ReportKTVHeartbeatResp) ProtoMessage()    {}
func (*ReportKTVHeartbeatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_heartbeat_logic__dfe0a275ebdcd667, []int{1}
}
func (m *ReportKTVHeartbeatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportKTVHeartbeatResp.Unmarshal(m, b)
}
func (m *ReportKTVHeartbeatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportKTVHeartbeatResp.Marshal(b, m, deterministic)
}
func (dst *ReportKTVHeartbeatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportKTVHeartbeatResp.Merge(dst, src)
}
func (m *ReportKTVHeartbeatResp) XXX_Size() int {
	return xxx_messageInfo_ReportKTVHeartbeatResp.Size(m)
}
func (m *ReportKTVHeartbeatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportKTVHeartbeatResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportKTVHeartbeatResp proto.InternalMessageInfo

func (m *ReportKTVHeartbeatResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ReportKTVHeartbeatResp) GetReportDuration() uint32 {
	if m != nil {
		return m.ReportDuration
	}
	return 0
}

type ConfirmKTVHeartBeatReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string       `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	GameId               uint32       `protobuf:"varint,4,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ConfirmKTVHeartBeatReq) Reset()         { *m = ConfirmKTVHeartBeatReq{} }
func (m *ConfirmKTVHeartBeatReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmKTVHeartBeatReq) ProtoMessage()    {}
func (*ConfirmKTVHeartBeatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_heartbeat_logic__dfe0a275ebdcd667, []int{2}
}
func (m *ConfirmKTVHeartBeatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmKTVHeartBeatReq.Unmarshal(m, b)
}
func (m *ConfirmKTVHeartBeatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmKTVHeartBeatReq.Marshal(b, m, deterministic)
}
func (dst *ConfirmKTVHeartBeatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmKTVHeartBeatReq.Merge(dst, src)
}
func (m *ConfirmKTVHeartBeatReq) XXX_Size() int {
	return xxx_messageInfo_ConfirmKTVHeartBeatReq.Size(m)
}
func (m *ConfirmKTVHeartBeatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmKTVHeartBeatReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmKTVHeartBeatReq proto.InternalMessageInfo

func (m *ConfirmKTVHeartBeatReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ConfirmKTVHeartBeatReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ConfirmKTVHeartBeatReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *ConfirmKTVHeartBeatReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type ConfirmKTVHeartBeatResp struct {
	BaseResp             *app.BaseResp                 `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *channel_ktv.ChannelKTVUpdate `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	SingingInfo          *concert_logic.SingingInfo    `protobuf:"bytes,3,opt,name=singing_info,json=singingInfo,proto3" json:"singing_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *ConfirmKTVHeartBeatResp) Reset()         { *m = ConfirmKTVHeartBeatResp{} }
func (m *ConfirmKTVHeartBeatResp) String() string { return proto.CompactTextString(m) }
func (*ConfirmKTVHeartBeatResp) ProtoMessage()    {}
func (*ConfirmKTVHeartBeatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_heartbeat_logic__dfe0a275ebdcd667, []int{3}
}
func (m *ConfirmKTVHeartBeatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmKTVHeartBeatResp.Unmarshal(m, b)
}
func (m *ConfirmKTVHeartBeatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmKTVHeartBeatResp.Marshal(b, m, deterministic)
}
func (dst *ConfirmKTVHeartBeatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmKTVHeartBeatResp.Merge(dst, src)
}
func (m *ConfirmKTVHeartBeatResp) XXX_Size() int {
	return xxx_messageInfo_ConfirmKTVHeartBeatResp.Size(m)
}
func (m *ConfirmKTVHeartBeatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmKTVHeartBeatResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmKTVHeartBeatResp proto.InternalMessageInfo

func (m *ConfirmKTVHeartBeatResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ConfirmKTVHeartBeatResp) GetInfo() *channel_ktv.ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *ConfirmKTVHeartBeatResp) GetSingingInfo() *concert_logic.SingingInfo {
	if m != nil {
		return m.SingingInfo
	}
	return nil
}

func init() {
	proto.RegisterType((*ReportKTVHeartbeatReq)(nil), "ga.channel_ktv_heartbeat.ReportKTVHeartbeatReq")
	proto.RegisterType((*ReportKTVHeartbeatResp)(nil), "ga.channel_ktv_heartbeat.ReportKTVHeartbeatResp")
	proto.RegisterType((*ConfirmKTVHeartBeatReq)(nil), "ga.channel_ktv_heartbeat.ConfirmKTVHeartBeatReq")
	proto.RegisterType((*ConfirmKTVHeartBeatResp)(nil), "ga.channel_ktv_heartbeat.ConfirmKTVHeartBeatResp")
}

func init() {
	proto.RegisterFile("channel-ktv-heartbeat-logic_.proto", fileDescriptor_channel_ktv_heartbeat_logic__dfe0a275ebdcd667)
}

var fileDescriptor_channel_ktv_heartbeat_logic__dfe0a275ebdcd667 = []byte{
	// 395 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x52, 0x4d, 0x6b, 0xdb, 0x30,
	0x18, 0xc6, 0x6b, 0x49, 0x1b, 0xb9, 0xdd, 0xc0, 0x6c, 0xad, 0x09, 0x14, 0x8c, 0x0f, 0x5b, 0x76,
	0x88, 0x02, 0xde, 0x76, 0x1f, 0xe9, 0x0e, 0x0b, 0x85, 0x1d, 0xb4, 0xae, 0x87, 0x1d, 0x66, 0x64,
	0x5b, 0x51, 0x45, 0x6d, 0xbd, 0x8a, 0xa4, 0x04, 0xf2, 0x33, 0xf6, 0x7f, 0xf6, 0xe3, 0x86, 0x24,
	0x67, 0x49, 0x20, 0x97, 0x41, 0x6f, 0xef, 0xc7, 0xf3, 0xf1, 0x4a, 0x3c, 0x28, 0xaf, 0x1f, 0xa9,
	0x94, 0xac, 0x9d, 0x3c, 0xd9, 0xf5, 0xe4, 0x91, 0x51, 0x6d, 0x2b, 0x46, 0xed, 0xa4, 0x05, 0x2e,
	0xea, 0x12, 0x2b, 0x0d, 0x16, 0x92, 0x94, 0x53, 0xdc, 0xc3, 0xca, 0x27, 0xbb, 0x2e, 0xff, 0xc1,
	0x46, 0x97, 0x9c, 0x96, 0x15, 0x35, 0x2c, 0x00, 0x47, 0xe9, 0xbe, 0xd8, 0xbe, 0xc4, 0xe8, 0x75,
	0x0d, 0xb2, 0x66, 0xfa, 0x50, 0x38, 0xff, 0x85, 0xde, 0x10, 0xa6, 0x40, 0xdb, 0xbb, 0xfb, 0x87,
	0xaf, 0x5b, 0x51, 0xc2, 0x96, 0xc9, 0x5b, 0x74, 0xee, 0x64, 0x4b, 0xcd, 0x96, 0x69, 0x94, 0x45,
	0xe3, 0xb8, 0x88, 0x31, 0xa7, 0x78, 0x46, 0x0d, 0x23, 0x6c, 0x49, 0xce, 0xaa, 0x50, 0x24, 0x37,
	0x08, 0x6d, 0x0f, 0x13, 0x4d, 0xfa, 0x22, 0x8b, 0xc6, 0x97, 0x64, 0xd8, 0x4f, 0xe6, 0x4d, 0xde,
	0xa2, 0xab, 0x63, 0xfa, 0x46, 0x25, 0xef, 0xd1, 0xb0, 0x37, 0x30, 0xaa, 0x77, 0xb8, 0xd8, 0x39,
	0x18, 0x45, 0xce, 0xab, 0xbe, 0x4a, 0xde, 0xa1, 0x57, 0xda, 0x8b, 0x94, 0xcd, 0x4a, 0x53, 0x2b,
	0x40, 0xf6, 0x46, 0x2f, 0xc3, 0xf8, 0x4b, 0x3f, 0xcd, 0x7f, 0x47, 0xe8, 0xea, 0x16, 0xe4, 0x42,
	0xe8, 0x6e, 0xeb, 0x37, 0x7b, 0xd6, 0xf7, 0x24, 0xd7, 0xe8, 0xcc, 0x80, 0xe4, 0x6e, 0x77, 0x92,
	0x45, 0xe3, 0x21, 0x19, 0xb8, 0x36, 0x2c, 0x38, 0xed, 0x98, 0x5b, 0x9c, 0x7a, 0xd2, 0xc0, 0xb5,
	0xf3, 0x26, 0xff, 0x13, 0xa1, 0xeb, 0xa3, 0x37, 0xfd, 0xdf, 0x1f, 0x7c, 0x44, 0xa7, 0x42, 0x2e,
	0xc0, 0x5f, 0x14, 0x17, 0x19, 0x3e, 0x0c, 0x04, 0xbe, 0x0d, 0xf5, 0xdd, 0xfd, 0xc3, 0x0f, 0xd5,
	0x50, 0xcb, 0x88, 0x47, 0x27, 0x9f, 0xd1, 0x85, 0x11, 0x92, 0x0b, 0x77, 0xb1, 0x63, 0x9f, 0x78,
	0xf6, 0x8d, 0x67, 0x87, 0x38, 0x94, 0x3e, 0x0e, 0xf8, 0x7b, 0x40, 0xcd, 0xe5, 0x02, 0x48, 0x6c,
	0x76, 0xcd, 0xec, 0x1b, 0x4a, 0x6b, 0xe8, 0xf0, 0x46, 0x6c, 0x60, 0xe5, 0x68, 0x1d, 0x34, 0xac,
	0x0d, 0xe1, 0xf9, 0x59, 0x70, 0x68, 0xa9, 0xe4, 0xf8, 0x53, 0x61, 0x2d, 0xae, 0xa1, 0x9b, 0xfa,
	0x71, 0x0d, 0xed, 0x94, 0x2a, 0x35, 0x3d, 0x1a, 0xeb, 0x6a, 0xe0, 0x31, 0x1f, 0xfe, 0x06, 0x00,
	0x00, 0xff, 0xff, 0x09, 0x61, 0xdd, 0x39, 0xf6, 0x02, 0x00, 0x00,
}
