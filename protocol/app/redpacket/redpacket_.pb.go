// Code generated by protoc-gen-gogo.
// source: redpacket_.proto
// DO NOT EDIT!

/*
	Package redpacket is a generated protocol buffer package.

	It is generated from these files:
		redpacket_.proto

	It has these top-level messages:
		RedPacketActivityInfo
		RedPacketStageInfo
		RandomRedPacketReq
		RandomRedPacketResp
		RedPacketGiftInfoBase
		RedPacketGuildInfoBase
		PullRedPacketReq
		PullRedPacketResp
		GetFriendGiftReq
		GetFriendGiftResp
*/
package redpacket

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 礼物
// 礼物类型
type EREDPACKET_GIFT_TYPE int32

const (
	EREDPACKET_GIFT_TYPE_EREDPACKET_GIFT_NOMAL   EREDPACKET_GIFT_TYPE = 1
	EREDPACKET_GIFT_TYPE_EREDPACKET_GIFT_DEBRIS  EREDPACKET_GIFT_TYPE = 2
	EREDPACKET_GIFT_TYPE_EREDPACKET_GIFT_VOUCHER EREDPACKET_GIFT_TYPE = 3
)

var EREDPACKET_GIFT_TYPE_name = map[int32]string{
	1: "EREDPACKET_GIFT_NOMAL",
	2: "EREDPACKET_GIFT_DEBRIS",
	3: "EREDPACKET_GIFT_VOUCHER",
}
var EREDPACKET_GIFT_TYPE_value = map[string]int32{
	"EREDPACKET_GIFT_NOMAL":   1,
	"EREDPACKET_GIFT_DEBRIS":  2,
	"EREDPACKET_GIFT_VOUCHER": 3,
}

func (x EREDPACKET_GIFT_TYPE) Enum() *EREDPACKET_GIFT_TYPE {
	p := new(EREDPACKET_GIFT_TYPE)
	*p = x
	return p
}
func (x EREDPACKET_GIFT_TYPE) String() string {
	return proto.EnumName(EREDPACKET_GIFT_TYPE_name, int32(x))
}
func (x *EREDPACKET_GIFT_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EREDPACKET_GIFT_TYPE_value, data, "EREDPACKET_GIFT_TYPE")
	if err != nil {
		return err
	}
	*x = EREDPACKET_GIFT_TYPE(value)
	return nil
}
func (EREDPACKET_GIFT_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorRedpacket_, []int{0}
}

type RedPacketStageInfo_StageType int32

const (
	RedPacketStageInfo_DEFAULT        RedPacketStageInfo_StageType = 1
	RedPacketStageInfo_LOTTERY        RedPacketStageInfo_StageType = 2
	RedPacketStageInfo_MASS_GUILDBUFF RedPacketStageInfo_StageType = 3
	RedPacketStageInfo_PREPARE        RedPacketStageInfo_StageType = 4
)

var RedPacketStageInfo_StageType_name = map[int32]string{
	1: "DEFAULT",
	2: "LOTTERY",
	3: "MASS_GUILDBUFF",
	4: "PREPARE",
}
var RedPacketStageInfo_StageType_value = map[string]int32{
	"DEFAULT":        1,
	"LOTTERY":        2,
	"MASS_GUILDBUFF": 3,
	"PREPARE":        4,
}

func (x RedPacketStageInfo_StageType) Enum() *RedPacketStageInfo_StageType {
	p := new(RedPacketStageInfo_StageType)
	*p = x
	return p
}
func (x RedPacketStageInfo_StageType) String() string {
	return proto.EnumName(RedPacketStageInfo_StageType_name, int32(x))
}
func (x *RedPacketStageInfo_StageType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RedPacketStageInfo_StageType_value, data, "RedPacketStageInfo_StageType")
	if err != nil {
		return err
	}
	*x = RedPacketStageInfo_StageType(value)
	return nil
}
func (RedPacketStageInfo_StageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorRedpacket_, []int{1, 0}
}

// *
// 红包活动定义
type RedPacketActivityInfo struct {
	Id           uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Name         string `protobuf:"bytes,2,req,name=name" json:"name"`
	BeginTime    uint64 `protobuf:"varint,3,req,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime      uint64 `protobuf:"varint,4,req,name=end_time,json=endTime" json:"end_time"`
	ComboTimeout uint32 `protobuf:"varint,5,opt,name=combo_timeout,json=comboTimeout" json:"combo_timeout"`
	Url          string `protobuf:"bytes,6,opt,name=url" json:"url"`
}

func (m *RedPacketActivityInfo) Reset()                    { *m = RedPacketActivityInfo{} }
func (m *RedPacketActivityInfo) String() string            { return proto.CompactTextString(m) }
func (*RedPacketActivityInfo) ProtoMessage()               {}
func (*RedPacketActivityInfo) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket_, []int{0} }

func (m *RedPacketActivityInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RedPacketActivityInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RedPacketActivityInfo) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *RedPacketActivityInfo) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *RedPacketActivityInfo) GetComboTimeout() uint32 {
	if m != nil {
		return m.ComboTimeout
	}
	return 0
}

func (m *RedPacketActivityInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

// *
// 红包阶段定义
type RedPacketStageInfo struct {
	StageId    uint32   `protobuf:"varint,1,req,name=stage_id,json=stageId" json:"stage_id"`
	ActivityId uint32   `protobuf:"varint,2,req,name=activity_id,json=activityId" json:"activity_id"`
	Type       uint32   `protobuf:"varint,3,req,name=type" json:"type"`
	Name       string   `protobuf:"bytes,4,opt,name=name" json:"name"`
	BeginTime  uint64   `protobuf:"varint,5,opt,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime    uint64   `protobuf:"varint,6,opt,name=end_time,json=endTime" json:"end_time"`
	AdTextList []string `protobuf:"bytes,7,rep,name=ad_text_list,json=adTextList" json:"ad_text_list,omitempty"`
	// 阶段内发送抢红包请求的最小间隔, 精确至`毫秒`
	// 客户端必须严格按照此间隔控制请求频率
	MinReqInterval uint32 `protobuf:"varint,8,req,name=min_req_interval,json=minReqInterval" json:"min_req_interval"`
}

func (m *RedPacketStageInfo) Reset()                    { *m = RedPacketStageInfo{} }
func (m *RedPacketStageInfo) String() string            { return proto.CompactTextString(m) }
func (*RedPacketStageInfo) ProtoMessage()               {}
func (*RedPacketStageInfo) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket_, []int{1} }

func (m *RedPacketStageInfo) GetStageId() uint32 {
	if m != nil {
		return m.StageId
	}
	return 0
}

func (m *RedPacketStageInfo) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *RedPacketStageInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *RedPacketStageInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RedPacketStageInfo) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *RedPacketStageInfo) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *RedPacketStageInfo) GetAdTextList() []string {
	if m != nil {
		return m.AdTextList
	}
	return nil
}

func (m *RedPacketStageInfo) GetMinReqInterval() uint32 {
	if m != nil {
		return m.MinReqInterval
	}
	return 0
}

type RandomRedPacketReq struct {
}

func (m *RandomRedPacketReq) Reset()                    { *m = RandomRedPacketReq{} }
func (m *RandomRedPacketReq) String() string            { return proto.CompactTextString(m) }
func (*RandomRedPacketReq) ProtoMessage()               {}
func (*RandomRedPacketReq) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket_, []int{2} }

type RandomRedPacketResp struct {
}

func (m *RandomRedPacketResp) Reset()                    { *m = RandomRedPacketResp{} }
func (m *RandomRedPacketResp) String() string            { return proto.CompactTextString(m) }
func (*RandomRedPacketResp) ProtoMessage()               {}
func (*RandomRedPacketResp) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket_, []int{3} }

type RedPacketGiftInfoBase struct {
	GiftId       uint32 `protobuf:"varint,1,req,name=gift_id,json=giftId" json:"gift_id"`
	GiftName     string `protobuf:"bytes,2,req,name=gift_name,json=giftName" json:"gift_name"`
	GiftType     uint32 `protobuf:"varint,3,req,name=gift_type,json=giftType" json:"gift_type"`
	GiftPlatform uint32 `protobuf:"varint,4,req,name=gift_platform,json=giftPlatform" json:"gift_platform"`
}

func (m *RedPacketGiftInfoBase) Reset()                    { *m = RedPacketGiftInfoBase{} }
func (m *RedPacketGiftInfoBase) String() string            { return proto.CompactTextString(m) }
func (*RedPacketGiftInfoBase) ProtoMessage()               {}
func (*RedPacketGiftInfoBase) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket_, []int{4} }

func (m *RedPacketGiftInfoBase) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *RedPacketGiftInfoBase) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *RedPacketGiftInfoBase) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *RedPacketGiftInfoBase) GetGiftPlatform() uint32 {
	if m != nil {
		return m.GiftPlatform
	}
	return 0
}

type RedPacketGuildInfoBase struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GuildBuff uint32 `protobuf:"varint,2,req,name=guild_buff,json=guildBuff" json:"guild_buff"`
}

func (m *RedPacketGuildInfoBase) Reset()                    { *m = RedPacketGuildInfoBase{} }
func (m *RedPacketGuildInfoBase) String() string            { return proto.CompactTextString(m) }
func (*RedPacketGuildInfoBase) ProtoMessage()               {}
func (*RedPacketGuildInfoBase) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket_, []int{5} }

func (m *RedPacketGuildInfoBase) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *RedPacketGuildInfoBase) GetGuildBuff() uint32 {
	if m != nil {
		return m.GuildBuff
	}
	return 0
}

// 拉红包
type PullRedPacketReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *PullRedPacketReq) Reset()                    { *m = PullRedPacketReq{} }
func (m *PullRedPacketReq) String() string            { return proto.CompactTextString(m) }
func (*PullRedPacketReq) ProtoMessage()               {}
func (*PullRedPacketReq) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket_, []int{6} }

func (m *PullRedPacketReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type PullRedPacketResp struct {
	BaseResp      *ga.BaseResp            `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ActivityId    uint32                  `protobuf:"varint,2,req,name=activity_id,json=activityId" json:"activity_id"`
	StageId       uint32                  `protobuf:"varint,3,req,name=stage_id,json=stageId" json:"stage_id"`
	Gift          *RedPacketGiftInfoBase  `protobuf:"bytes,4,opt,name=gift" json:"gift,omitempty"`
	GuildBuffInfo *RedPacketGuildInfoBase `protobuf:"bytes,5,opt,name=guild_buff_info,json=guildBuffInfo" json:"guild_buff_info,omitempty"`
}

func (m *PullRedPacketResp) Reset()                    { *m = PullRedPacketResp{} }
func (m *PullRedPacketResp) String() string            { return proto.CompactTextString(m) }
func (*PullRedPacketResp) ProtoMessage()               {}
func (*PullRedPacketResp) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket_, []int{7} }

func (m *PullRedPacketResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PullRedPacketResp) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *PullRedPacketResp) GetStageId() uint32 {
	if m != nil {
		return m.StageId
	}
	return 0
}

func (m *PullRedPacketResp) GetGift() *RedPacketGiftInfoBase {
	if m != nil {
		return m.Gift
	}
	return nil
}

func (m *PullRedPacketResp) GetGuildBuffInfo() *RedPacketGuildInfoBase {
	if m != nil {
		return m.GuildBuffInfo
	}
	return nil
}

// 获取好友赠送的碎片礼物
type GetFriendGiftReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	OrderId string      `protobuf:"bytes,2,req,name=order_id,json=orderId" json:"order_id"`
}

func (m *GetFriendGiftReq) Reset()                    { *m = GetFriendGiftReq{} }
func (m *GetFriendGiftReq) String() string            { return proto.CompactTextString(m) }
func (*GetFriendGiftReq) ProtoMessage()               {}
func (*GetFriendGiftReq) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket_, []int{8} }

func (m *GetFriendGiftReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFriendGiftReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetFriendGiftResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *GetFriendGiftResp) Reset()                    { *m = GetFriendGiftResp{} }
func (m *GetFriendGiftResp) String() string            { return proto.CompactTextString(m) }
func (*GetFriendGiftResp) ProtoMessage()               {}
func (*GetFriendGiftResp) Descriptor() ([]byte, []int) { return fileDescriptorRedpacket_, []int{9} }

func (m *GetFriendGiftResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*RedPacketActivityInfo)(nil), "ga.RedPacketActivityInfo")
	proto.RegisterType((*RedPacketStageInfo)(nil), "ga.RedPacketStageInfo")
	proto.RegisterType((*RandomRedPacketReq)(nil), "ga.RandomRedPacketReq")
	proto.RegisterType((*RandomRedPacketResp)(nil), "ga.RandomRedPacketResp")
	proto.RegisterType((*RedPacketGiftInfoBase)(nil), "ga.RedPacketGiftInfoBase")
	proto.RegisterType((*RedPacketGuildInfoBase)(nil), "ga.RedPacketGuildInfoBase")
	proto.RegisterType((*PullRedPacketReq)(nil), "ga.PullRedPacketReq")
	proto.RegisterType((*PullRedPacketResp)(nil), "ga.PullRedPacketResp")
	proto.RegisterType((*GetFriendGiftReq)(nil), "ga.GetFriendGiftReq")
	proto.RegisterType((*GetFriendGiftResp)(nil), "ga.GetFriendGiftResp")
	proto.RegisterEnum("ga.EREDPACKET_GIFT_TYPE", EREDPACKET_GIFT_TYPE_name, EREDPACKET_GIFT_TYPE_value)
	proto.RegisterEnum("ga.RedPacketStageInfo_StageType", RedPacketStageInfo_StageType_name, RedPacketStageInfo_StageType_value)
}
func (m *RedPacketActivityInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketActivityInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x18
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.ComboTimeout))
	dAtA[i] = 0x32
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	return i, nil
}

func (m *RedPacketStageInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketStageInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.StageId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x22
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x28
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.EndTime))
	if len(m.AdTextList) > 0 {
		for _, s := range m.AdTextList {
			dAtA[i] = 0x3a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x40
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.MinReqInterval))
	return i, nil
}

func (m *RandomRedPacketReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RandomRedPacketReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RandomRedPacketResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RandomRedPacketResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RedPacketGiftInfoBase) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketGiftInfoBase) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.GiftId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(len(m.GiftName)))
	i += copy(dAtA[i:], m.GiftName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.GiftType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.GiftPlatform))
	return i, nil
}

func (m *RedPacketGuildInfoBase) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketGuildInfoBase) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.GuildBuff))
	return i, nil
}

func (m *PullRedPacketReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PullRedPacketReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRedpacket_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *PullRedPacketResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PullRedPacketResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRedpacket_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(m.StageId))
	if m.Gift != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintRedpacket_(dAtA, i, uint64(m.Gift.Size()))
		n3, err := m.Gift.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.GuildBuffInfo != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintRedpacket_(dAtA, i, uint64(m.GuildBuffInfo.Size()))
		n4, err := m.GuildBuffInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *GetFriendGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFriendGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRedpacket_(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintRedpacket_(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	return i, nil
}

func (m *GetFriendGiftResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFriendGiftResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRedpacket_(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func encodeFixed64Redpacket_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Redpacket_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintRedpacket_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *RedPacketActivityInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket_(uint64(m.Id))
	l = len(m.Name)
	n += 1 + l + sovRedpacket_(uint64(l))
	n += 1 + sovRedpacket_(uint64(m.BeginTime))
	n += 1 + sovRedpacket_(uint64(m.EndTime))
	n += 1 + sovRedpacket_(uint64(m.ComboTimeout))
	l = len(m.Url)
	n += 1 + l + sovRedpacket_(uint64(l))
	return n
}

func (m *RedPacketStageInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket_(uint64(m.StageId))
	n += 1 + sovRedpacket_(uint64(m.ActivityId))
	n += 1 + sovRedpacket_(uint64(m.Type))
	l = len(m.Name)
	n += 1 + l + sovRedpacket_(uint64(l))
	n += 1 + sovRedpacket_(uint64(m.BeginTime))
	n += 1 + sovRedpacket_(uint64(m.EndTime))
	if len(m.AdTextList) > 0 {
		for _, s := range m.AdTextList {
			l = len(s)
			n += 1 + l + sovRedpacket_(uint64(l))
		}
	}
	n += 1 + sovRedpacket_(uint64(m.MinReqInterval))
	return n
}

func (m *RandomRedPacketReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RandomRedPacketResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RedPacketGiftInfoBase) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket_(uint64(m.GiftId))
	l = len(m.GiftName)
	n += 1 + l + sovRedpacket_(uint64(l))
	n += 1 + sovRedpacket_(uint64(m.GiftType))
	n += 1 + sovRedpacket_(uint64(m.GiftPlatform))
	return n
}

func (m *RedPacketGuildInfoBase) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRedpacket_(uint64(m.GuildId))
	n += 1 + sovRedpacket_(uint64(m.GuildBuff))
	return n
}

func (m *PullRedPacketReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovRedpacket_(uint64(l))
	}
	return n
}

func (m *PullRedPacketResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovRedpacket_(uint64(l))
	}
	n += 1 + sovRedpacket_(uint64(m.ActivityId))
	n += 1 + sovRedpacket_(uint64(m.StageId))
	if m.Gift != nil {
		l = m.Gift.Size()
		n += 1 + l + sovRedpacket_(uint64(l))
	}
	if m.GuildBuffInfo != nil {
		l = m.GuildBuffInfo.Size()
		n += 1 + l + sovRedpacket_(uint64(l))
	}
	return n
}

func (m *GetFriendGiftReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovRedpacket_(uint64(l))
	}
	l = len(m.OrderId)
	n += 1 + l + sovRedpacket_(uint64(l))
	return n
}

func (m *GetFriendGiftResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovRedpacket_(uint64(l))
	}
	return n
}

func sovRedpacket_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozRedpacket_(x uint64) (n int) {
	return sovRedpacket_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *RedPacketActivityInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketActivityInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketActivityInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ComboTimeout", wireType)
			}
			m.ComboTimeout = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ComboTimeout |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("end_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketStageInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketStageInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketStageInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageId", wireType)
			}
			m.StageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdTextList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AdTextList = append(m.AdTextList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MinReqInterval", wireType)
			}
			m.MinReqInterval = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MinReqInterval |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("stage_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("min_req_interval")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RandomRedPacketReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RandomRedPacketReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RandomRedPacketReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RandomRedPacketResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RandomRedPacketResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RandomRedPacketResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketGiftInfoBase) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketGiftInfoBase: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketGiftInfoBase: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftId", wireType)
			}
			m.GiftId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftType", wireType)
			}
			m.GiftType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPlatform", wireType)
			}
			m.GiftPlatform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPlatform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_platform")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketGuildInfoBase) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketGuildInfoBase: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketGuildInfoBase: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildBuff", wireType)
			}
			m.GuildBuff = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildBuff |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_buff")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PullRedPacketReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PullRedPacketReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PullRedPacketReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PullRedPacketResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PullRedPacketResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PullRedPacketResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageId", wireType)
			}
			m.StageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Gift", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Gift == nil {
				m.Gift = &RedPacketGiftInfoBase{}
			}
			if err := m.Gift.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildBuffInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GuildBuffInfo == nil {
				m.GuildBuffInfo = &RedPacketGuildInfoBase{}
			}
			if err := m.GuildBuffInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("stage_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFriendGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFriendGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFriendGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRedpacket_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("order_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFriendGiftResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRedpacket_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFriendGiftResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFriendGiftResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRedpacket_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRedpacket_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRedpacket_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipRedpacket_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowRedpacket_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRedpacket_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthRedpacket_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowRedpacket_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipRedpacket_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthRedpacket_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowRedpacket_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("redpacket_.proto", fileDescriptorRedpacket_) }

var fileDescriptorRedpacket_ = []byte{
	// 787 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x55, 0xd1, 0x6e, 0xe3, 0x44,
	0x14, 0x5d, 0x3b, 0xa1, 0x49, 0x6e, 0x9a, 0xc5, 0x3b, 0xb4, 0xc5, 0x5b, 0x44, 0x1b, 0x8c, 0x58,
	0xb5, 0x48, 0xa4, 0x52, 0x25, 0x5e, 0x78, 0x40, 0x4a, 0xb6, 0x4e, 0xb1, 0xc8, 0x6e, 0xa3, 0xa9,
	0x8b, 0xb4, 0x20, 0x61, 0x4d, 0x32, 0x63, 0x6b, 0xc0, 0xf6, 0xb8, 0xf6, 0x64, 0xb5, 0xfd, 0x0b,
	0xfe, 0x82, 0x5f, 0xd9, 0xc7, 0xfd, 0x02, 0x84, 0xca, 0x0f, 0xf0, 0xca, 0x1b, 0x9a, 0x89, 0xe3,
	0x7a, 0x93, 0x4a, 0xd0, 0x37, 0xfb, 0x9c, 0x73, 0x3d, 0xf7, 0x9e, 0x7b, 0x26, 0x01, 0x2b, 0x67,
	0x34, 0x23, 0xf3, 0x5f, 0x99, 0x0c, 0x06, 0x59, 0x2e, 0xa4, 0x40, 0x66, 0x44, 0xf6, 0x7b, 0x11,
	0x09, 0x66, 0xa4, 0x60, 0x4b, 0xc8, 0x79, 0x67, 0xc0, 0x2e, 0x66, 0x74, 0xaa, 0x75, 0xc3, 0xb9,
	0xe4, 0xaf, 0xb9, 0xbc, 0xf1, 0xd2, 0x50, 0xa0, 0x1d, 0x30, 0x39, 0xb5, 0x8d, 0xbe, 0x79, 0xd4,
	0x1b, 0x35, 0xdf, 0xfe, 0x71, 0xf8, 0x08, 0x9b, 0x9c, 0x22, 0x1b, 0x9a, 0x29, 0x49, 0x98, 0x6d,
	0xf6, 0xcd, 0xa3, 0x4e, 0x89, 0x6b, 0x04, 0x7d, 0x0e, 0x30, 0x63, 0x11, 0x4f, 0x03, 0xc9, 0x13,
	0x66, 0x37, 0xfa, 0xe6, 0x51, 0xb3, 0xe4, 0x3b, 0x1a, 0xf7, 0x79, 0xc2, 0xd0, 0x21, 0xb4, 0x59,
	0x4a, 0x97, 0x92, 0x66, 0x4d, 0xd2, 0x62, 0x29, 0xd5, 0x82, 0x63, 0xe8, 0xcd, 0x45, 0x32, 0x13,
	0x5a, 0x22, 0x16, 0xd2, 0xfe, 0xa0, 0x6f, 0x54, 0x0d, 0x6c, 0x6b, 0xca, 0x5f, 0x32, 0x68, 0x0f,
	0x1a, 0x8b, 0x3c, 0xb6, 0xb7, 0xfa, 0x46, 0xd5, 0x89, 0x02, 0x9c, 0xbf, 0x4d, 0x40, 0xd5, 0x48,
	0x97, 0x92, 0x44, 0x4c, 0xcf, 0x73, 0x08, 0xed, 0x42, 0xbd, 0x04, 0x6b, 0x53, 0xb5, 0x34, 0xea,
	0x51, 0xf4, 0x05, 0x74, 0x49, 0x69, 0x80, 0xd2, 0x98, 0x35, 0x0d, 0xac, 0x08, 0x4f, 0x3b, 0x20,
	0x6f, 0xb2, 0xe5, 0x84, 0x2b, 0x5e, 0x23, 0x95, 0x37, 0xcd, 0x5a, 0x47, 0xf7, 0x79, 0xa3, 0x46,
	0xfa, 0x0f, 0x6f, 0xb6, 0x6a, 0x92, 0xca, 0x9b, 0x3e, 0x6c, 0x13, 0x1a, 0x48, 0xf6, 0x46, 0x06,
	0x31, 0x2f, 0xa4, 0xdd, 0xea, 0x37, 0x8e, 0x3a, 0x18, 0x08, 0xf5, 0xd9, 0x1b, 0x39, 0xe1, 0x85,
	0x44, 0x03, 0xb0, 0x12, 0x9e, 0x06, 0x39, 0xbb, 0x0e, 0x78, 0x2a, 0x59, 0xfe, 0x9a, 0xc4, 0x76,
	0xbb, 0xd6, 0xe7, 0xe3, 0x84, 0xa7, 0x98, 0x5d, 0x7b, 0x25, 0xe7, 0x8c, 0xa1, 0xa3, 0x0d, 0xf2,
	0x55, 0xfb, 0x5d, 0x68, 0x9d, 0xb9, 0xe3, 0xe1, 0xd5, 0xc4, 0xb7, 0x0c, 0xf5, 0x32, 0xb9, 0xf0,
	0x7d, 0x17, 0xbf, 0xb2, 0x4c, 0x84, 0xe0, 0xf1, 0x8b, 0xe1, 0xe5, 0x65, 0x70, 0x7e, 0xe5, 0x4d,
	0xce, 0x46, 0x57, 0xe3, 0xb1, 0xd5, 0x50, 0x82, 0x29, 0x76, 0xa7, 0x43, 0xec, 0x5a, 0x4d, 0x67,
	0x07, 0x10, 0x26, 0x29, 0x15, 0x49, 0xe5, 0x3b, 0x66, 0xd7, 0xce, 0x2e, 0x7c, 0xb4, 0x81, 0x16,
	0x99, 0xf3, 0x7b, 0x3d, 0x72, 0xe7, 0x3c, 0x94, 0x6a, 0x3d, 0x23, 0x52, 0x30, 0xf4, 0x29, 0xb4,
	0x22, 0x1e, 0xca, 0xf5, 0x0d, 0x6d, 0x29, 0xd0, 0xa3, 0xe8, 0x33, 0xe8, 0x68, 0x7a, 0x23, 0x80,
	0x6d, 0x05, 0xbf, 0x54, 0x46, 0xaf, 0x24, 0x1b, 0x1b, 0xd2, 0x12, 0x3d, 0xe6, 0x31, 0xf4, 0xb4,
	0x24, 0x8b, 0x89, 0x0c, 0x45, 0x9e, 0xe8, 0x1c, 0x56, 0x09, 0x53, 0xd4, 0xb4, 0x64, 0x9c, 0x9f,
	0x61, 0xef, 0xae, 0xd1, 0x05, 0x8f, 0x69, 0xd5, 0xe9, 0x21, 0xb4, 0x23, 0x05, 0x6c, 0x84, 0x49,
	0xa3, 0x1e, 0x55, 0x1b, 0x5f, 0x0a, 0x66, 0x8b, 0x30, 0x7c, 0x2f, 0x4b, 0x1d, 0x8d, 0x8f, 0x16,
	0x61, 0xe8, 0x7c, 0x03, 0xd6, 0x74, 0x11, 0xc7, 0x75, 0xd3, 0xd0, 0x33, 0x68, 0xab, 0xeb, 0xa9,
	0x76, 0xa8, 0xbf, 0xdc, 0x3d, 0xed, 0x0e, 0x22, 0x32, 0x50, 0xa7, 0x62, 0x76, 0x8d, 0x5b, 0xb3,
	0xe5, 0x83, 0xf3, 0x8f, 0x01, 0x4f, 0xd6, 0x8a, 0x8b, 0x0c, 0x1d, 0x43, 0xa7, 0xac, 0x2e, 0xb2,
	0xb2, 0x7c, 0xfb, 0xae, 0xbc, 0xc8, 0x70, 0x7b, 0x56, 0x3e, 0xfd, 0xdf, 0xb8, 0xd7, 0xaf, 0x4d,
	0xe3, 0xbe, 0x6b, 0xf3, 0x15, 0x34, 0x95, 0x69, 0x3a, 0xf5, 0xdd, 0xd3, 0xa7, 0xea, 0xb4, 0x7b,
	0xb7, 0x8b, 0xb5, 0x0c, 0x8d, 0xe0, 0xc3, 0x3b, 0x63, 0x02, 0x9e, 0x86, 0x42, 0xdf, 0x87, 0xee,
	0xe9, 0xfe, 0xfb, 0x95, 0x75, 0xbb, 0x71, 0xaf, 0xf2, 0x4c, 0x41, 0xce, 0x4f, 0x60, 0x9d, 0x33,
	0x39, 0xce, 0x39, 0x4b, 0xa9, 0x3a, 0xe2, 0x01, 0xbe, 0xa9, 0x79, 0x44, 0x4e, 0x59, 0xbe, 0x9a,
	0x79, 0x95, 0xa1, 0x96, 0x46, 0x3d, 0xea, 0x7c, 0x0b, 0x4f, 0xd6, 0x3e, 0xfe, 0x20, 0x5f, 0xbf,
	0xfc, 0x05, 0x76, 0x5c, 0xec, 0x9e, 0x4d, 0x87, 0xcf, 0xbf, 0x77, 0xfd, 0xe0, 0xdc, 0x1b, 0xfb,
	0x81, 0xff, 0x6a, 0xea, 0xa2, 0xa7, 0xb0, 0xbb, 0x8e, 0xbf, 0xbc, 0x78, 0x31, 0x9c, 0x58, 0x06,
	0xda, 0x87, 0xbd, 0x75, 0xea, 0xcc, 0x1d, 0x61, 0xef, 0xd2, 0x32, 0xd1, 0x27, 0xf0, 0xf1, 0x3a,
	0xf7, 0xc3, 0xc5, 0xd5, 0xf3, 0xef, 0x5c, 0x6c, 0x35, 0x46, 0xf8, 0xed, 0xed, 0x81, 0xf1, 0xee,
	0xf6, 0xc0, 0xf8, 0xf3, 0xf6, 0xc0, 0xf8, 0xed, 0xaf, 0x83, 0x47, 0x60, 0xcf, 0x45, 0x32, 0xb8,
	0xe1, 0x37, 0x62, 0xa1, 0xda, 0x4b, 0x04, 0x65, 0xf1, 0xf2, 0x97, 0xfe, 0xc7, 0x67, 0x91, 0x88,
	0x49, 0x1a, 0x0d, 0xbe, 0x3e, 0x95, 0x72, 0x30, 0x17, 0xc9, 0x89, 0x86, 0xe7, 0x22, 0x3e, 0x21,
	0x59, 0x76, 0x52, 0xfd, 0x57, 0xfc, 0x1b, 0x00, 0x00, 0xff, 0xff, 0x3c, 0x23, 0xe8, 0x7a, 0x37,
	0x06, 0x00, 0x00,
}
