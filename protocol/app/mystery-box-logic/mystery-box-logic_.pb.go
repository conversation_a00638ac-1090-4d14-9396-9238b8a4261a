// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mystery-box-logic_.proto

package mystery_box_logic // import "golang.52tt.com/protocol/app/mystery-box-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 盲盒任务类型
type MysteryBoxTaskType int32

const (
	MysteryBoxTaskType_MysteryBoxTaskTypeNone            MysteryBoxTaskType = 0
	MysteryBoxTaskType_MysteryBoxTaskTypeEnterUgcChannel MysteryBoxTaskType = 1
)

var MysteryBoxTaskType_name = map[int32]string{
	0: "MysteryBoxTaskTypeNone",
	1: "MysteryBoxTaskTypeEnterUgcChannel",
}
var MysteryBoxTaskType_value = map[string]int32{
	"MysteryBoxTaskTypeNone":            0,
	"MysteryBoxTaskTypeEnterUgcChannel": 1,
}

func (x MysteryBoxTaskType) String() string {
	return proto.EnumName(MysteryBoxTaskType_name, int32(x))
}
func (MysteryBoxTaskType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_box_logic__5e5a0e9b3abd339e, []int{0}
}

// 任务状态
type MysteryBoxTask_State int32

const (
	MysteryBoxTask_StateNone   MysteryBoxTask_State = 0
	MysteryBoxTask_StateUndone MysteryBoxTask_State = 1
	MysteryBoxTask_StateDone   MysteryBoxTask_State = 2
)

var MysteryBoxTask_State_name = map[int32]string{
	0: "StateNone",
	1: "StateUndone",
	2: "StateDone",
}
var MysteryBoxTask_State_value = map[string]int32{
	"StateNone":   0,
	"StateUndone": 1,
	"StateDone":   2,
}

func (x MysteryBoxTask_State) String() string {
	return proto.EnumName(MysteryBoxTask_State_name, int32(x))
}
func (MysteryBoxTask_State) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_box_logic__5e5a0e9b3abd339e, []int{0, 0}
}

// 盲盒任务
type MysteryBoxTask struct {
	// 任务图标
	Icon string `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	// 任务跳转短链
	Link string `protobuf:"bytes,2,opt,name=link,proto3" json:"link,omitempty"`
	// 半屏任务描述
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// 任务状态
	State MysteryBoxTask_State `protobuf:"varint,4,opt,name=state,proto3,enum=ga.mystery_box_logic.MysteryBoxTask_State" json:"state,omitempty"`
	// 弹窗任务描述
	WindowDesc           string   `protobuf:"bytes,5,opt,name=window_desc,json=windowDesc,proto3" json:"window_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MysteryBoxTask) Reset()         { *m = MysteryBoxTask{} }
func (m *MysteryBoxTask) String() string { return proto.CompactTextString(m) }
func (*MysteryBoxTask) ProtoMessage()    {}
func (*MysteryBoxTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_box_logic__5e5a0e9b3abd339e, []int{0}
}
func (m *MysteryBoxTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryBoxTask.Unmarshal(m, b)
}
func (m *MysteryBoxTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryBoxTask.Marshal(b, m, deterministic)
}
func (dst *MysteryBoxTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryBoxTask.Merge(dst, src)
}
func (m *MysteryBoxTask) XXX_Size() int {
	return xxx_messageInfo_MysteryBoxTask.Size(m)
}
func (m *MysteryBoxTask) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryBoxTask.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryBoxTask proto.InternalMessageInfo

func (m *MysteryBoxTask) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *MysteryBoxTask) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *MysteryBoxTask) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *MysteryBoxTask) GetState() MysteryBoxTask_State {
	if m != nil {
		return m.State
	}
	return MysteryBoxTask_StateNone
}

func (m *MysteryBoxTask) GetWindowDesc() string {
	if m != nil {
		return m.WindowDesc
	}
	return ""
}

// 盲盒奖励
type MysteryBoxReward struct {
	// 半屏奖励配图
	Pic string `protobuf:"bytes,1,opt,name=pic,proto3" json:"pic,omitempty"`
	// 半屏奖励描述
	Desc string `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	// 弹窗奖励描述
	WindowDesc string `protobuf:"bytes,3,opt,name=window_desc,json=windowDesc,proto3" json:"window_desc,omitempty"`
	// 奖励名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// 弹窗奖励配图
	WindowPic            string   `protobuf:"bytes,5,opt,name=window_pic,json=windowPic,proto3" json:"window_pic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MysteryBoxReward) Reset()         { *m = MysteryBoxReward{} }
func (m *MysteryBoxReward) String() string { return proto.CompactTextString(m) }
func (*MysteryBoxReward) ProtoMessage()    {}
func (*MysteryBoxReward) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_box_logic__5e5a0e9b3abd339e, []int{1}
}
func (m *MysteryBoxReward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryBoxReward.Unmarshal(m, b)
}
func (m *MysteryBoxReward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryBoxReward.Marshal(b, m, deterministic)
}
func (dst *MysteryBoxReward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryBoxReward.Merge(dst, src)
}
func (m *MysteryBoxReward) XXX_Size() int {
	return xxx_messageInfo_MysteryBoxReward.Size(m)
}
func (m *MysteryBoxReward) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryBoxReward.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryBoxReward proto.InternalMessageInfo

func (m *MysteryBoxReward) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *MysteryBoxReward) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *MysteryBoxReward) GetWindowDesc() string {
	if m != nil {
		return m.WindowDesc
	}
	return ""
}

func (m *MysteryBoxReward) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MysteryBoxReward) GetWindowPic() string {
	if m != nil {
		return m.WindowPic
	}
	return ""
}

// 盲盒弹窗
type MysteryBoxWin struct {
	// 今日任务
	TodayTask *MysteryBoxTask `protobuf:"bytes,1,opt,name=today_task,json=todayTask,proto3" json:"today_task,omitempty"`
	// 今日奖励
	TodayReward *MysteryBoxReward `protobuf:"bytes,2,opt,name=today_reward,json=todayReward,proto3" json:"today_reward,omitempty"`
	// 昨日奖励
	YesterdayReward *MysteryBoxReward `protobuf:"bytes,3,opt,name=yesterday_reward,json=yesterdayReward,proto3" json:"yesterday_reward,omitempty"`
	// 昨日任务
	YesterdayTask        *MysteryBoxTask `protobuf:"bytes,4,opt,name=yesterday_task,json=yesterdayTask,proto3" json:"yesterday_task,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MysteryBoxWin) Reset()         { *m = MysteryBoxWin{} }
func (m *MysteryBoxWin) String() string { return proto.CompactTextString(m) }
func (*MysteryBoxWin) ProtoMessage()    {}
func (*MysteryBoxWin) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_box_logic__5e5a0e9b3abd339e, []int{2}
}
func (m *MysteryBoxWin) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryBoxWin.Unmarshal(m, b)
}
func (m *MysteryBoxWin) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryBoxWin.Marshal(b, m, deterministic)
}
func (dst *MysteryBoxWin) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryBoxWin.Merge(dst, src)
}
func (m *MysteryBoxWin) XXX_Size() int {
	return xxx_messageInfo_MysteryBoxWin.Size(m)
}
func (m *MysteryBoxWin) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryBoxWin.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryBoxWin proto.InternalMessageInfo

func (m *MysteryBoxWin) GetTodayTask() *MysteryBoxTask {
	if m != nil {
		return m.TodayTask
	}
	return nil
}

func (m *MysteryBoxWin) GetTodayReward() *MysteryBoxReward {
	if m != nil {
		return m.TodayReward
	}
	return nil
}

func (m *MysteryBoxWin) GetYesterdayReward() *MysteryBoxReward {
	if m != nil {
		return m.YesterdayReward
	}
	return nil
}

func (m *MysteryBoxWin) GetYesterdayTask() *MysteryBoxTask {
	if m != nil {
		return m.YesterdayTask
	}
	return nil
}

type GetMysteryBoxWinReq struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 是否可以领取今日任务
	CanReceive           bool     `protobuf:"varint,2,opt,name=can_receive,json=canReceive,proto3" json:"can_receive,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMysteryBoxWinReq) Reset()         { *m = GetMysteryBoxWinReq{} }
func (m *GetMysteryBoxWinReq) String() string { return proto.CompactTextString(m) }
func (*GetMysteryBoxWinReq) ProtoMessage()    {}
func (*GetMysteryBoxWinReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_box_logic__5e5a0e9b3abd339e, []int{3}
}
func (m *GetMysteryBoxWinReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMysteryBoxWinReq.Unmarshal(m, b)
}
func (m *GetMysteryBoxWinReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMysteryBoxWinReq.Marshal(b, m, deterministic)
}
func (dst *GetMysteryBoxWinReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMysteryBoxWinReq.Merge(dst, src)
}
func (m *GetMysteryBoxWinReq) XXX_Size() int {
	return xxx_messageInfo_GetMysteryBoxWinReq.Size(m)
}
func (m *GetMysteryBoxWinReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMysteryBoxWinReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMysteryBoxWinReq proto.InternalMessageInfo

func (m *GetMysteryBoxWinReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMysteryBoxWinReq) GetCanReceive() bool {
	if m != nil {
		return m.CanReceive
	}
	return false
}

type GetMysteryBoxWinResp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Win                  *MysteryBoxWin `protobuf:"bytes,2,opt,name=win,proto3" json:"win,omitempty"`
	PopAt                uint32         `protobuf:"varint,3,opt,name=pop_at,json=popAt,proto3" json:"pop_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetMysteryBoxWinResp) Reset()         { *m = GetMysteryBoxWinResp{} }
func (m *GetMysteryBoxWinResp) String() string { return proto.CompactTextString(m) }
func (*GetMysteryBoxWinResp) ProtoMessage()    {}
func (*GetMysteryBoxWinResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_box_logic__5e5a0e9b3abd339e, []int{4}
}
func (m *GetMysteryBoxWinResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMysteryBoxWinResp.Unmarshal(m, b)
}
func (m *GetMysteryBoxWinResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMysteryBoxWinResp.Marshal(b, m, deterministic)
}
func (dst *GetMysteryBoxWinResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMysteryBoxWinResp.Merge(dst, src)
}
func (m *GetMysteryBoxWinResp) XXX_Size() int {
	return xxx_messageInfo_GetMysteryBoxWinResp.Size(m)
}
func (m *GetMysteryBoxWinResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMysteryBoxWinResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMysteryBoxWinResp proto.InternalMessageInfo

func (m *GetMysteryBoxWinResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMysteryBoxWinResp) GetWin() *MysteryBoxWin {
	if m != nil {
		return m.Win
	}
	return nil
}

func (m *GetMysteryBoxWinResp) GetPopAt() uint32 {
	if m != nil {
		return m.PopAt
	}
	return 0
}

type GetMysteryBoxTaskReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMysteryBoxTaskReq) Reset()         { *m = GetMysteryBoxTaskReq{} }
func (m *GetMysteryBoxTaskReq) String() string { return proto.CompactTextString(m) }
func (*GetMysteryBoxTaskReq) ProtoMessage()    {}
func (*GetMysteryBoxTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_box_logic__5e5a0e9b3abd339e, []int{5}
}
func (m *GetMysteryBoxTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMysteryBoxTaskReq.Unmarshal(m, b)
}
func (m *GetMysteryBoxTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMysteryBoxTaskReq.Marshal(b, m, deterministic)
}
func (dst *GetMysteryBoxTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMysteryBoxTaskReq.Merge(dst, src)
}
func (m *GetMysteryBoxTaskReq) XXX_Size() int {
	return xxx_messageInfo_GetMysteryBoxTaskReq.Size(m)
}
func (m *GetMysteryBoxTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMysteryBoxTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMysteryBoxTaskReq proto.InternalMessageInfo

func (m *GetMysteryBoxTaskReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetMysteryBoxTaskResp struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 任务
	Task *MysteryBoxTask `protobuf:"bytes,2,opt,name=task,proto3" json:"task,omitempty"`
	// 奖励
	Reward               *MysteryBoxReward `protobuf:"bytes,3,opt,name=reward,proto3" json:"reward,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetMysteryBoxTaskResp) Reset()         { *m = GetMysteryBoxTaskResp{} }
func (m *GetMysteryBoxTaskResp) String() string { return proto.CompactTextString(m) }
func (*GetMysteryBoxTaskResp) ProtoMessage()    {}
func (*GetMysteryBoxTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_box_logic__5e5a0e9b3abd339e, []int{6}
}
func (m *GetMysteryBoxTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMysteryBoxTaskResp.Unmarshal(m, b)
}
func (m *GetMysteryBoxTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMysteryBoxTaskResp.Marshal(b, m, deterministic)
}
func (dst *GetMysteryBoxTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMysteryBoxTaskResp.Merge(dst, src)
}
func (m *GetMysteryBoxTaskResp) XXX_Size() int {
	return xxx_messageInfo_GetMysteryBoxTaskResp.Size(m)
}
func (m *GetMysteryBoxTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMysteryBoxTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMysteryBoxTaskResp proto.InternalMessageInfo

func (m *GetMysteryBoxTaskResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMysteryBoxTaskResp) GetTask() *MysteryBoxTask {
	if m != nil {
		return m.Task
	}
	return nil
}

func (m *GetMysteryBoxTaskResp) GetReward() *MysteryBoxReward {
	if m != nil {
		return m.Reward
	}
	return nil
}

// 盲盒任务推送
type MysteryBoxTaskNotify struct {
	// 任务类型
	TaskType MysteryBoxTaskType `protobuf:"varint,1,opt,name=task_type,json=taskType,proto3,enum=ga.mystery_box_logic.MysteryBoxTaskType" json:"task_type,omitempty"`
	// 房间id
	ChannelId uint32 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// 停留时长(秒)
	StaySec              uint32   `protobuf:"varint,3,opt,name=stay_sec,json=staySec,proto3" json:"stay_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MysteryBoxTaskNotify) Reset()         { *m = MysteryBoxTaskNotify{} }
func (m *MysteryBoxTaskNotify) String() string { return proto.CompactTextString(m) }
func (*MysteryBoxTaskNotify) ProtoMessage()    {}
func (*MysteryBoxTaskNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_box_logic__5e5a0e9b3abd339e, []int{7}
}
func (m *MysteryBoxTaskNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryBoxTaskNotify.Unmarshal(m, b)
}
func (m *MysteryBoxTaskNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryBoxTaskNotify.Marshal(b, m, deterministic)
}
func (dst *MysteryBoxTaskNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryBoxTaskNotify.Merge(dst, src)
}
func (m *MysteryBoxTaskNotify) XXX_Size() int {
	return xxx_messageInfo_MysteryBoxTaskNotify.Size(m)
}
func (m *MysteryBoxTaskNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryBoxTaskNotify.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryBoxTaskNotify proto.InternalMessageInfo

func (m *MysteryBoxTaskNotify) GetTaskType() MysteryBoxTaskType {
	if m != nil {
		return m.TaskType
	}
	return MysteryBoxTaskType_MysteryBoxTaskTypeNone
}

func (m *MysteryBoxTaskNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MysteryBoxTaskNotify) GetStaySec() uint32 {
	if m != nil {
		return m.StaySec
	}
	return 0
}

func init() {
	proto.RegisterType((*MysteryBoxTask)(nil), "ga.mystery_box_logic.MysteryBoxTask")
	proto.RegisterType((*MysteryBoxReward)(nil), "ga.mystery_box_logic.MysteryBoxReward")
	proto.RegisterType((*MysteryBoxWin)(nil), "ga.mystery_box_logic.MysteryBoxWin")
	proto.RegisterType((*GetMysteryBoxWinReq)(nil), "ga.mystery_box_logic.GetMysteryBoxWinReq")
	proto.RegisterType((*GetMysteryBoxWinResp)(nil), "ga.mystery_box_logic.GetMysteryBoxWinResp")
	proto.RegisterType((*GetMysteryBoxTaskReq)(nil), "ga.mystery_box_logic.GetMysteryBoxTaskReq")
	proto.RegisterType((*GetMysteryBoxTaskResp)(nil), "ga.mystery_box_logic.GetMysteryBoxTaskResp")
	proto.RegisterType((*MysteryBoxTaskNotify)(nil), "ga.mystery_box_logic.MysteryBoxTaskNotify")
	proto.RegisterEnum("ga.mystery_box_logic.MysteryBoxTaskType", MysteryBoxTaskType_name, MysteryBoxTaskType_value)
	proto.RegisterEnum("ga.mystery_box_logic.MysteryBoxTask_State", MysteryBoxTask_State_name, MysteryBoxTask_State_value)
}

func init() {
	proto.RegisterFile("mystery-box-logic_.proto", fileDescriptor_mystery_box_logic__5e5a0e9b3abd339e)
}

var fileDescriptor_mystery_box_logic__5e5a0e9b3abd339e = []byte{
	// 657 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x55, 0xdd, 0x4e, 0x13, 0x41,
	0x14, 0x76, 0xfb, 0x03, 0xdd, 0x53, 0x5a, 0x9a, 0x11, 0x4c, 0x25, 0x31, 0xe2, 0xaa, 0x04, 0x49,
	0x58, 0x92, 0x1a, 0x8c, 0x57, 0x44, 0xf9, 0x89, 0x41, 0x23, 0xd1, 0x01, 0x42, 0xe2, 0x85, 0x9b,
	0xe9, 0xec, 0x58, 0x37, 0xb4, 0x33, 0xc3, 0xce, 0x68, 0xd9, 0x77, 0x30, 0xf1, 0xd2, 0x97, 0x31,
	0xf1, 0x71, 0x7c, 0x0d, 0x33, 0x3f, 0x69, 0x81, 0x92, 0x40, 0xbd, 0x3b, 0xf3, 0xcd, 0x39, 0xdf,
	0x77, 0xbe, 0x73, 0xf6, 0x07, 0xda, 0x83, 0x42, 0x69, 0x96, 0x17, 0xeb, 0x5d, 0x71, 0xbe, 0xde,
	0x17, 0xbd, 0x8c, 0x26, 0xb1, 0xcc, 0x85, 0x16, 0x68, 0xa1, 0x47, 0x62, 0x7f, 0x99, 0x74, 0xc5,
	0x79, 0x62, 0x2f, 0x97, 0x1a, 0x3d, 0x92, 0x74, 0x89, 0x62, 0x2e, 0x29, 0xfa, 0x1b, 0x40, 0xf3,
	0xbd, 0x4b, 0xda, 0x16, 0xe7, 0x47, 0x44, 0x9d, 0x22, 0x04, 0x95, 0x8c, 0x0a, 0xde, 0x0e, 0x96,
	0x83, 0xd5, 0x10, 0xdb, 0xd8, 0x60, 0xfd, 0x8c, 0x9f, 0xb6, 0x4b, 0x0e, 0x33, 0xb1, 0xc1, 0x52,
	0xa6, 0x68, 0xbb, 0xec, 0x30, 0x13, 0xa3, 0x57, 0x50, 0x55, 0x9a, 0x68, 0xd6, 0xae, 0x2c, 0x07,
	0xab, 0xcd, 0xce, 0x5a, 0x7c, 0x5d, 0x0f, 0xf1, 0x65, 0xc1, 0xf8, 0xd0, 0x54, 0x60, 0x57, 0x88,
	0x1e, 0x42, 0x7d, 0x98, 0xf1, 0x54, 0x0c, 0x13, 0x4b, 0x5e, 0xb5, 0xe4, 0xe0, 0xa0, 0x5d, 0xa6,
	0x68, 0xf4, 0x02, 0xaa, 0xb6, 0x00, 0x35, 0x20, 0xb4, 0xc1, 0x81, 0xe0, 0xac, 0x75, 0x07, 0xcd,
	0x43, 0xdd, 0x1e, 0x8f, 0x79, 0x6a, 0x80, 0x60, 0x74, 0xbf, 0x6b, 0x8e, 0xa5, 0xe8, 0x47, 0x00,
	0xad, 0xb1, 0x30, 0x66, 0x43, 0x92, 0xa7, 0xa8, 0x05, 0x65, 0x99, 0x51, 0x6f, 0xd5, 0x84, 0x23,
	0x57, 0xa5, 0x0b, 0xae, 0xae, 0xf4, 0x54, 0xbe, 0xda, 0x93, 0x29, 0xe2, 0x64, 0xe0, 0x5c, 0x87,
	0xd8, 0xc6, 0xe8, 0x01, 0xf8, 0x8c, 0xc4, 0x28, 0x38, 0x1f, 0xa1, 0x43, 0x3e, 0x64, 0x34, 0xfa,
	0x53, 0x82, 0xc6, 0xb8, 0x9d, 0x93, 0x8c, 0xa3, 0x1d, 0x00, 0x2d, 0x52, 0x52, 0x24, 0x9a, 0xa8,
	0x53, 0xdb, 0x52, 0xbd, 0xf3, 0xe4, 0x36, 0x03, 0xc4, 0xa1, 0xad, 0xb3, 0xcb, 0xdb, 0x87, 0x39,
	0x47, 0x92, 0x5b, 0x83, 0xd6, 0x46, 0xbd, 0xb3, 0x72, 0x13, 0x8d, 0x1b, 0x07, 0xae, 0xdb, 0x5a,
	0x3f, 0x9b, 0x8f, 0xd0, 0x2a, 0x98, 0x49, 0xb8, 0x40, 0x57, 0x9e, 0x8a, 0x6e, 0x7e, 0x54, 0xef,
	0x29, 0xdf, 0x41, 0x73, 0x4c, 0x69, 0x6d, 0x56, 0xa6, 0xb0, 0xd9, 0x18, 0xd5, 0x9a, 0x63, 0xf4,
	0x19, 0xee, 0xbe, 0x61, 0xfa, 0xd2, 0x0c, 0x31, 0x3b, 0x43, 0x2b, 0x50, 0x33, 0xcf, 0x77, 0x92,
	0xb3, 0x33, 0x3f, 0xc4, 0xba, 0x61, 0xdf, 0x26, 0x8a, 0x61, 0x76, 0x86, 0x67, 0xbb, 0x2e, 0x30,
	0x4b, 0xa5, 0x84, 0x27, 0x39, 0xa3, 0x2c, 0xfb, 0xce, 0xec, 0xa0, 0x6a, 0x18, 0x28, 0xe1, 0xd8,
	0x21, 0xd1, 0xcf, 0x00, 0x16, 0x26, 0x05, 0x94, 0x44, 0xcf, 0x20, 0xf4, 0x0a, 0x4a, 0x7a, 0x89,
	0xb9, 0xb1, 0x84, 0x92, 0xb8, 0xd6, 0xf5, 0x11, 0xda, 0x84, 0xf2, 0x30, 0xe3, 0x7e, 0x0b, 0x8f,
	0x6f, 0x72, 0x69, 0x04, 0x4c, 0x3e, 0x5a, 0x84, 0x19, 0x29, 0x64, 0x42, 0xb4, 0x1d, 0x78, 0x03,
	0x57, 0xa5, 0x90, 0xaf, 0x75, 0xb4, 0x75, 0xa5, 0x21, 0x3b, 0x95, 0xdb, 0x5b, 0x8e, 0x7e, 0x07,
	0xb0, 0x78, 0x0d, 0xc1, 0x74, 0x96, 0x5e, 0x42, 0xc5, 0x6e, 0xae, 0x34, 0xc5, 0xe6, 0x6c, 0x05,
	0xda, 0x82, 0x99, 0xff, 0x7a, 0x8c, 0x7c, 0x55, 0xf4, 0x2b, 0x80, 0x85, 0xcb, 0xc4, 0x07, 0x42,
	0x67, 0x5f, 0x0a, 0xb4, 0x07, 0xa1, 0x11, 0x48, 0x74, 0x21, 0x99, 0xed, 0xbe, 0xd9, 0x59, 0xbd,
	0x4d, 0x5f, 0x47, 0x85, 0x64, 0xb8, 0xa6, 0x7d, 0x64, 0xde, 0x58, 0xfa, 0x95, 0x70, 0xce, 0xfa,
	0x49, 0xe6, 0xde, 0x9c, 0x06, 0x0e, 0x3d, 0xb2, 0x9f, 0xa2, 0xfb, 0x50, 0x53, 0x9a, 0x14, 0x89,
	0x62, 0xd4, 0xaf, 0x65, 0xd6, 0x9c, 0x0f, 0x19, 0x5d, 0x3b, 0x01, 0x34, 0xc9, 0x8c, 0x96, 0xe0,
	0xde, 0x24, 0xea, 0xbf, 0x56, 0x4f, 0xe1, 0xd1, 0xe4, 0xdd, 0x1e, 0xd7, 0x2c, 0x3f, 0xee, 0xd1,
	0x1d, 0xa7, 0xd9, 0x0a, 0xb6, 0xdf, 0x42, 0x9b, 0x8a, 0x41, 0x5c, 0x64, 0x85, 0xf8, 0x66, 0x1d,
	0x89, 0x94, 0xf5, 0xdd, 0xa7, 0xfb, 0x53, 0xdc, 0x13, 0x7d, 0xc2, 0x7b, 0xf1, 0x66, 0x47, 0xeb,
	0x98, 0x8a, 0xc1, 0x86, 0x85, 0xa9, 0xe8, 0x6f, 0x10, 0x29, 0x37, 0x26, 0x7e, 0x0b, 0xdd, 0x19,
	0x7b, 0xff, 0xfc, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x76, 0x17, 0xf0, 0x3b, 0x32, 0x06, 0x00,
	0x00,
}
