// Code generated by protoc-gen-go. DO NOT EDIT.
// source: character_channel_logic/character_channel_logic.proto

package character_channel_logic // import "golang.52tt.com/protocol/app/character-channel-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// const unsigned int CMD_CharacterApplyOnMic = 5700;         // 申请/邀请 上麦
type CharacterApplyOnMicReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	OpType               uint32       `protobuf:"varint,2,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	Cid                  uint32       `protobuf:"varint,3,opt,name=cid,proto3" json:"cid,omitempty"`
	InviteUid            uint32       `protobuf:"varint,4,opt,name=invite_uid,json=inviteUid,proto3" json:"invite_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CharacterApplyOnMicReq) Reset()         { *m = CharacterApplyOnMicReq{} }
func (m *CharacterApplyOnMicReq) String() string { return proto.CompactTextString(m) }
func (*CharacterApplyOnMicReq) ProtoMessage()    {}
func (*CharacterApplyOnMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_character_channel_logic_be46dc4628151f03, []int{0}
}
func (m *CharacterApplyOnMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CharacterApplyOnMicReq.Unmarshal(m, b)
}
func (m *CharacterApplyOnMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CharacterApplyOnMicReq.Marshal(b, m, deterministic)
}
func (dst *CharacterApplyOnMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CharacterApplyOnMicReq.Merge(dst, src)
}
func (m *CharacterApplyOnMicReq) XXX_Size() int {
	return xxx_messageInfo_CharacterApplyOnMicReq.Size(m)
}
func (m *CharacterApplyOnMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CharacterApplyOnMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_CharacterApplyOnMicReq proto.InternalMessageInfo

func (m *CharacterApplyOnMicReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CharacterApplyOnMicReq) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *CharacterApplyOnMicReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *CharacterApplyOnMicReq) GetInviteUid() uint32 {
	if m != nil {
		return m.InviteUid
	}
	return 0
}

type CharacterApplyOnMicResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CharacterApplyOnMicResp) Reset()         { *m = CharacterApplyOnMicResp{} }
func (m *CharacterApplyOnMicResp) String() string { return proto.CompactTextString(m) }
func (*CharacterApplyOnMicResp) ProtoMessage()    {}
func (*CharacterApplyOnMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_character_channel_logic_be46dc4628151f03, []int{1}
}
func (m *CharacterApplyOnMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CharacterApplyOnMicResp.Unmarshal(m, b)
}
func (m *CharacterApplyOnMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CharacterApplyOnMicResp.Marshal(b, m, deterministic)
}
func (dst *CharacterApplyOnMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CharacterApplyOnMicResp.Merge(dst, src)
}
func (m *CharacterApplyOnMicResp) XXX_Size() int {
	return xxx_messageInfo_CharacterApplyOnMicResp.Size(m)
}
func (m *CharacterApplyOnMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CharacterApplyOnMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_CharacterApplyOnMicResp proto.InternalMessageInfo

func (m *CharacterApplyOnMicResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// const unsigned int CMD_CharacterAgreeApplyMic = 5701;         // 同意/接受 上麦
type CharacterAgreeApplyMicReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	OpType               uint32       `protobuf:"varint,2,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	ApplicantUid         uint32       `protobuf:"varint,3,opt,name=applicant_uid,json=applicantUid,proto3" json:"applicant_uid,omitempty"`
	InviteCode           string       `protobuf:"bytes,4,opt,name=invite_code,json=inviteCode,proto3" json:"invite_code,omitempty"`
	Cid                  uint32       `protobuf:"varint,5,opt,name=cid,proto3" json:"cid,omitempty"`
	Ts                   int64        `protobuf:"varint,6,opt,name=ts,proto3" json:"ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CharacterAgreeApplyMicReq) Reset()         { *m = CharacterAgreeApplyMicReq{} }
func (m *CharacterAgreeApplyMicReq) String() string { return proto.CompactTextString(m) }
func (*CharacterAgreeApplyMicReq) ProtoMessage()    {}
func (*CharacterAgreeApplyMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_character_channel_logic_be46dc4628151f03, []int{2}
}
func (m *CharacterAgreeApplyMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CharacterAgreeApplyMicReq.Unmarshal(m, b)
}
func (m *CharacterAgreeApplyMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CharacterAgreeApplyMicReq.Marshal(b, m, deterministic)
}
func (dst *CharacterAgreeApplyMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CharacterAgreeApplyMicReq.Merge(dst, src)
}
func (m *CharacterAgreeApplyMicReq) XXX_Size() int {
	return xxx_messageInfo_CharacterAgreeApplyMicReq.Size(m)
}
func (m *CharacterAgreeApplyMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CharacterAgreeApplyMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_CharacterAgreeApplyMicReq proto.InternalMessageInfo

func (m *CharacterAgreeApplyMicReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CharacterAgreeApplyMicReq) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *CharacterAgreeApplyMicReq) GetApplicantUid() uint32 {
	if m != nil {
		return m.ApplicantUid
	}
	return 0
}

func (m *CharacterAgreeApplyMicReq) GetInviteCode() string {
	if m != nil {
		return m.InviteCode
	}
	return ""
}

func (m *CharacterAgreeApplyMicReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *CharacterAgreeApplyMicReq) GetTs() int64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

type CharacterAgreeApplyMicResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MicId                uint32        `protobuf:"varint,2,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	Token                string        `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CharacterAgreeApplyMicResp) Reset()         { *m = CharacterAgreeApplyMicResp{} }
func (m *CharacterAgreeApplyMicResp) String() string { return proto.CompactTextString(m) }
func (*CharacterAgreeApplyMicResp) ProtoMessage()    {}
func (*CharacterAgreeApplyMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_character_channel_logic_be46dc4628151f03, []int{3}
}
func (m *CharacterAgreeApplyMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CharacterAgreeApplyMicResp.Unmarshal(m, b)
}
func (m *CharacterAgreeApplyMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CharacterAgreeApplyMicResp.Marshal(b, m, deterministic)
}
func (dst *CharacterAgreeApplyMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CharacterAgreeApplyMicResp.Merge(dst, src)
}
func (m *CharacterAgreeApplyMicResp) XXX_Size() int {
	return xxx_messageInfo_CharacterAgreeApplyMicResp.Size(m)
}
func (m *CharacterAgreeApplyMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CharacterAgreeApplyMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_CharacterAgreeApplyMicResp proto.InternalMessageInfo

func (m *CharacterAgreeApplyMicResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CharacterAgreeApplyMicResp) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *CharacterAgreeApplyMicResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

// 申请/邀请 上麦推送
type CharacterApplyOnMicNotify struct {
	OpType               uint32   `protobuf:"varint,1,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	InviteCode           string   `protobuf:"bytes,5,opt,name=invite_code,json=inviteCode,proto3" json:"invite_code,omitempty"`
	Ts                   int64    `protobuf:"varint,6,opt,name=ts,proto3" json:"ts,omitempty"`
	Account              string   `protobuf:"bytes,7,opt,name=account,proto3" json:"account,omitempty"`
	Sex                  uint32   `protobuf:"varint,8,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CharacterApplyOnMicNotify) Reset()         { *m = CharacterApplyOnMicNotify{} }
func (m *CharacterApplyOnMicNotify) String() string { return proto.CompactTextString(m) }
func (*CharacterApplyOnMicNotify) ProtoMessage()    {}
func (*CharacterApplyOnMicNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_character_channel_logic_be46dc4628151f03, []int{4}
}
func (m *CharacterApplyOnMicNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CharacterApplyOnMicNotify.Unmarshal(m, b)
}
func (m *CharacterApplyOnMicNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CharacterApplyOnMicNotify.Marshal(b, m, deterministic)
}
func (dst *CharacterApplyOnMicNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CharacterApplyOnMicNotify.Merge(dst, src)
}
func (m *CharacterApplyOnMicNotify) XXX_Size() int {
	return xxx_messageInfo_CharacterApplyOnMicNotify.Size(m)
}
func (m *CharacterApplyOnMicNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_CharacterApplyOnMicNotify.DiscardUnknown(m)
}

var xxx_messageInfo_CharacterApplyOnMicNotify proto.InternalMessageInfo

func (m *CharacterApplyOnMicNotify) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *CharacterApplyOnMicNotify) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *CharacterApplyOnMicNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CharacterApplyOnMicNotify) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *CharacterApplyOnMicNotify) GetInviteCode() string {
	if m != nil {
		return m.InviteCode
	}
	return ""
}

func (m *CharacterApplyOnMicNotify) GetTs() int64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *CharacterApplyOnMicNotify) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *CharacterApplyOnMicNotify) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

// 上麦推送，只推给申请人
type CharacterAgreeOnMicNotify struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	MicId                uint32   `protobuf:"varint,3,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	Token                string   `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CharacterAgreeOnMicNotify) Reset()         { *m = CharacterAgreeOnMicNotify{} }
func (m *CharacterAgreeOnMicNotify) String() string { return proto.CompactTextString(m) }
func (*CharacterAgreeOnMicNotify) ProtoMessage()    {}
func (*CharacterAgreeOnMicNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_character_channel_logic_be46dc4628151f03, []int{5}
}
func (m *CharacterAgreeOnMicNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CharacterAgreeOnMicNotify.Unmarshal(m, b)
}
func (m *CharacterAgreeOnMicNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CharacterAgreeOnMicNotify.Marshal(b, m, deterministic)
}
func (dst *CharacterAgreeOnMicNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CharacterAgreeOnMicNotify.Merge(dst, src)
}
func (m *CharacterAgreeOnMicNotify) XXX_Size() int {
	return xxx_messageInfo_CharacterAgreeOnMicNotify.Size(m)
}
func (m *CharacterAgreeOnMicNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_CharacterAgreeOnMicNotify.DiscardUnknown(m)
}

var xxx_messageInfo_CharacterAgreeOnMicNotify proto.InternalMessageInfo

func (m *CharacterAgreeOnMicNotify) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *CharacterAgreeOnMicNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CharacterAgreeOnMicNotify) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *CharacterAgreeOnMicNotify) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

// 处理申请通知，房间广播，公屏消失
type CharacterHandleMicNotify struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CharacterHandleMicNotify) Reset()         { *m = CharacterHandleMicNotify{} }
func (m *CharacterHandleMicNotify) String() string { return proto.CompactTextString(m) }
func (*CharacterHandleMicNotify) ProtoMessage()    {}
func (*CharacterHandleMicNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_character_channel_logic_be46dc4628151f03, []int{6}
}
func (m *CharacterHandleMicNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CharacterHandleMicNotify.Unmarshal(m, b)
}
func (m *CharacterHandleMicNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CharacterHandleMicNotify.Marshal(b, m, deterministic)
}
func (dst *CharacterHandleMicNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CharacterHandleMicNotify.Merge(dst, src)
}
func (m *CharacterHandleMicNotify) XXX_Size() int {
	return xxx_messageInfo_CharacterHandleMicNotify.Size(m)
}
func (m *CharacterHandleMicNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_CharacterHandleMicNotify.DiscardUnknown(m)
}

var xxx_messageInfo_CharacterHandleMicNotify proto.InternalMessageInfo

func (m *CharacterHandleMicNotify) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *CharacterHandleMicNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func init() {
	proto.RegisterType((*CharacterApplyOnMicReq)(nil), "ga.character_channel_logic.CharacterApplyOnMicReq")
	proto.RegisterType((*CharacterApplyOnMicResp)(nil), "ga.character_channel_logic.CharacterApplyOnMicResp")
	proto.RegisterType((*CharacterAgreeApplyMicReq)(nil), "ga.character_channel_logic.CharacterAgreeApplyMicReq")
	proto.RegisterType((*CharacterAgreeApplyMicResp)(nil), "ga.character_channel_logic.CharacterAgreeApplyMicResp")
	proto.RegisterType((*CharacterApplyOnMicNotify)(nil), "ga.character_channel_logic.CharacterApplyOnMicNotify")
	proto.RegisterType((*CharacterAgreeOnMicNotify)(nil), "ga.character_channel_logic.CharacterAgreeOnMicNotify")
	proto.RegisterType((*CharacterHandleMicNotify)(nil), "ga.character_channel_logic.CharacterHandleMicNotify")
}

func init() {
	proto.RegisterFile("character_channel_logic/character_channel_logic.proto", fileDescriptor_character_channel_logic_be46dc4628151f03)
}

var fileDescriptor_character_channel_logic_be46dc4628151f03 = []byte{
	// 491 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x94, 0x5d, 0x6f, 0xd3, 0x3e,
	0x14, 0xc6, 0x95, 0x64, 0x7d, 0x3b, 0x5d, 0xff, 0x9a, 0xac, 0x3f, 0xcc, 0x54, 0x42, 0x54, 0x41,
	0x42, 0xe5, 0x62, 0xa9, 0x34, 0xd8, 0x2d, 0x12, 0x1b, 0x17, 0x70, 0xc1, 0x8b, 0x22, 0x76, 0xc3,
	0x4d, 0xe4, 0xda, 0x26, 0xb3, 0x96, 0xd8, 0x6e, 0xec, 0xa2, 0xe5, 0x2b, 0xf0, 0xb9, 0xf8, 0x02,
	0x7c, 0x23, 0x14, 0x27, 0x4d, 0x4a, 0xd7, 0x5e, 0x4c, 0xe2, 0xee, 0xf8, 0xf4, 0x91, 0xcf, 0xf3,
	0xfc, 0xdc, 0x13, 0xb8, 0xa0, 0x37, 0xa4, 0x20, 0xd4, 0xf2, 0x22, 0xa1, 0x37, 0x44, 0x4a, 0x9e,
	0x25, 0x99, 0x4a, 0x05, 0x5d, 0x1c, 0xe8, 0x47, 0xba, 0x50, 0x56, 0xa1, 0x69, 0x4a, 0xa2, 0x03,
	0x8a, 0xe9, 0x24, 0x25, 0xc9, 0x92, 0x18, 0x5e, 0x4b, 0xc3, 0x9f, 0x1e, 0x3c, 0xbe, 0xda, 0x48,
	0xdf, 0x6a, 0x9d, 0x95, 0x9f, 0xe5, 0x47, 0x41, 0x63, 0xbe, 0x42, 0x2f, 0x60, 0x58, 0x09, 0x93,
	0x82, 0xaf, 0xb0, 0x37, 0xf3, 0xe6, 0xe3, 0xf3, 0x71, 0x94, 0x92, 0xe8, 0x92, 0x18, 0x1e, 0xf3,
	0x55, 0x3c, 0x58, 0xd6, 0x05, 0x3a, 0x85, 0x81, 0xd2, 0x89, 0x2d, 0x35, 0xc7, 0xfe, 0xcc, 0x9b,
	0x4f, 0xe2, 0xbe, 0xd2, 0x5f, 0x4b, 0xcd, 0xd1, 0x09, 0x04, 0x54, 0x30, 0x1c, 0xb8, 0x66, 0x55,
	0xa2, 0xa7, 0x00, 0x42, 0xfe, 0x10, 0x96, 0x27, 0x6b, 0xc1, 0xf0, 0x91, 0xfb, 0x61, 0x54, 0x77,
	0xae, 0x05, 0x0b, 0xdf, 0xc1, 0xe9, 0x5e, 0x2f, 0x46, 0xa3, 0x97, 0x30, 0x6a, 0xcc, 0x18, 0xdd,
	0xb8, 0x39, 0xee, 0xdc, 0x18, 0x1d, 0x0f, 0x97, 0x4d, 0x15, 0xfe, 0xf2, 0xe0, 0x49, 0x77, 0x4d,
	0x5a, 0x70, 0xee, 0xee, 0xfa, 0x57, 0xa9, 0x9e, 0xc3, 0x84, 0x68, 0x9d, 0x09, 0x4a, 0xa4, 0x75,
	0x31, 0xea, 0x7c, 0xc7, 0x6d, 0xf3, 0x5a, 0x30, 0xf4, 0x0c, 0xc6, 0x4d, 0x50, 0xaa, 0x18, 0x77,
	0x49, 0x47, 0x71, 0x93, 0xfd, 0x4a, 0xb1, 0x96, 0x4d, 0xaf, 0x63, 0xf3, 0x1f, 0xf8, 0xd6, 0xe0,
	0xfe, 0xcc, 0x9b, 0x07, 0xb1, 0x6f, 0x4d, 0x68, 0x61, 0x7a, 0x28, 0xc5, 0x83, 0x78, 0xa0, 0x47,
	0xd0, 0xcf, 0x05, 0x4d, 0x04, 0x6b, 0x82, 0xf4, 0x72, 0x41, 0x3f, 0x30, 0xf4, 0x3f, 0xf4, 0xac,
	0xba, 0xe5, 0xd2, 0xf9, 0x1f, 0xc5, 0xf5, 0x21, 0xfc, 0xfd, 0x17, 0xbc, 0xf6, 0x0d, 0x3e, 0x29,
	0x2b, 0xbe, 0x97, 0xdb, 0x50, 0xbc, 0x7d, 0x4f, 0xed, 0x77, 0x71, 0x4e, 0x20, 0xe8, 0xe0, 0x54,
	0x25, 0x9a, 0xc2, 0x50, 0x0a, 0x7a, 0x2b, 0x49, 0xbe, 0x01, 0xd2, 0x9e, 0x77, 0x79, 0xf5, 0xee,
	0xf1, 0xda, 0xa1, 0x83, 0x30, 0x0c, 0x08, 0xa5, 0x6a, 0x2d, 0x2d, 0x1e, 0x38, 0xf1, 0xe6, 0x58,
	0x0d, 0x36, 0xfc, 0x0e, 0x0f, 0xeb, 0xc1, 0x86, 0xdf, 0x85, 0xd9, 0xee, 0xff, 0x61, 0x3b, 0x52,
	0xe3, 0xdc, 0xbb, 0xe7, 0xdc, 0xef, 0x9c, 0x77, 0x04, 0x83, 0xbd, 0x04, 0x8f, 0xb6, 0x09, 0xbe,
	0x01, 0xdc, 0x4e, 0x7b, 0x4f, 0x24, 0xcb, 0xf8, 0x83, 0x86, 0x5d, 0x7e, 0x01, 0x4c, 0x55, 0x1e,
	0x95, 0xa2, 0x54, 0xeb, 0xea, 0x45, 0x73, 0xc5, 0x78, 0x56, 0x6f, 0xeb, 0xb7, 0xd7, 0xa9, 0xca,
	0x88, 0x4c, 0xa3, 0x8b, 0x73, 0x6b, 0x23, 0xaa, 0xf2, 0x85, 0x6b, 0x53, 0x95, 0x2d, 0x88, 0xd6,
	0xdd, 0x47, 0xe1, 0xac, 0x59, 0xf9, 0x33, 0xb7, 0xf2, 0xcb, 0xbe, 0x53, 0xbd, 0xfa, 0x13, 0x00,
	0x00, 0xff, 0xff, 0x98, 0x09, 0x48, 0xa0, 0x4e, 0x04, 0x00, 0x00,
}
