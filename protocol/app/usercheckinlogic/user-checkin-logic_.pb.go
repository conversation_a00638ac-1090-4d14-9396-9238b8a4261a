// Code generated by protoc-gen-go. DO NOT EDIT.
// source: user-checkin-logic_.proto

package usercheckinlogic // import "golang.52tt.com/protocol/app/usercheckinlogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AwardType int32

const (
	AwardType_AwardInit       AwardType = 0
	AwardType_AwardNotRecieve AwardType = 1
	AwardType_AwardHasRecieve AwardType = 2
)

var AwardType_name = map[int32]string{
	0: "AwardInit",
	1: "AwardNotRecieve",
	2: "AwardHasRecieve",
}
var AwardType_value = map[string]int32{
	"AwardInit":       0,
	"AwardNotRecieve": 1,
	"AwardHasRecieve": 2,
}

func (x AwardType) String() string {
	return proto.EnumName(AwardType_name, int32(x))
}
func (AwardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_checkin_logic__44f910ffe2aeb1df, []int{0}
}

type ReceiveAwardReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	DayRounds            []uint32     `protobuf:"varint,2,rep,packed,name=day_rounds,json=dayRounds,proto3" json:"day_rounds,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ReceiveAwardReq) Reset()         { *m = ReceiveAwardReq{} }
func (m *ReceiveAwardReq) String() string { return proto.CompactTextString(m) }
func (*ReceiveAwardReq) ProtoMessage()    {}
func (*ReceiveAwardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_checkin_logic__44f910ffe2aeb1df, []int{0}
}
func (m *ReceiveAwardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveAwardReq.Unmarshal(m, b)
}
func (m *ReceiveAwardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveAwardReq.Marshal(b, m, deterministic)
}
func (dst *ReceiveAwardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveAwardReq.Merge(dst, src)
}
func (m *ReceiveAwardReq) XXX_Size() int {
	return xxx_messageInfo_ReceiveAwardReq.Size(m)
}
func (m *ReceiveAwardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveAwardReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveAwardReq proto.InternalMessageInfo

func (m *ReceiveAwardReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReceiveAwardReq) GetDayRounds() []uint32 {
	if m != nil {
		return m.DayRounds
	}
	return nil
}

type AwardInfo struct {
	DayRound             uint32    `protobuf:"varint,1,opt,name=day_round,json=dayRound,proto3" json:"day_round,omitempty"`
	IconUrl              string    `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	AwardType            AwardType `protobuf:"varint,3,opt,name=award_type,json=awardType,proto3,enum=ga.usercheckinlogic.AwardType" json:"award_type,omitempty"`
	Name                 string    `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	TbeanPrice           uint32    `protobuf:"varint,5,opt,name=tbean_price,json=tbeanPrice,proto3" json:"tbean_price,omitempty"`
	NeedAvatar           bool      `protobuf:"varint,6,opt,name=need_avatar,json=needAvatar,proto3" json:"need_avatar,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *AwardInfo) Reset()         { *m = AwardInfo{} }
func (m *AwardInfo) String() string { return proto.CompactTextString(m) }
func (*AwardInfo) ProtoMessage()    {}
func (*AwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_checkin_logic__44f910ffe2aeb1df, []int{1}
}
func (m *AwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardInfo.Unmarshal(m, b)
}
func (m *AwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardInfo.Marshal(b, m, deterministic)
}
func (dst *AwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardInfo.Merge(dst, src)
}
func (m *AwardInfo) XXX_Size() int {
	return xxx_messageInfo_AwardInfo.Size(m)
}
func (m *AwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AwardInfo proto.InternalMessageInfo

func (m *AwardInfo) GetDayRound() uint32 {
	if m != nil {
		return m.DayRound
	}
	return 0
}

func (m *AwardInfo) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *AwardInfo) GetAwardType() AwardType {
	if m != nil {
		return m.AwardType
	}
	return AwardType_AwardInit
}

func (m *AwardInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AwardInfo) GetTbeanPrice() uint32 {
	if m != nil {
		return m.TbeanPrice
	}
	return 0
}

func (m *AwardInfo) GetNeedAvatar() bool {
	if m != nil {
		return m.NeedAvatar
	}
	return false
}

type ReceiveAwardRsp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Uid                  uint32        `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Infos                []*AwardInfo  `protobuf:"bytes,3,rep,name=infos,proto3" json:"infos,omitempty"`
	Toast                string        `protobuf:"bytes,4,opt,name=toast,proto3" json:"toast,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReceiveAwardRsp) Reset()         { *m = ReceiveAwardRsp{} }
func (m *ReceiveAwardRsp) String() string { return proto.CompactTextString(m) }
func (*ReceiveAwardRsp) ProtoMessage()    {}
func (*ReceiveAwardRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_checkin_logic__44f910ffe2aeb1df, []int{2}
}
func (m *ReceiveAwardRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveAwardRsp.Unmarshal(m, b)
}
func (m *ReceiveAwardRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveAwardRsp.Marshal(b, m, deterministic)
}
func (dst *ReceiveAwardRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveAwardRsp.Merge(dst, src)
}
func (m *ReceiveAwardRsp) XXX_Size() int {
	return xxx_messageInfo_ReceiveAwardRsp.Size(m)
}
func (m *ReceiveAwardRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveAwardRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveAwardRsp proto.InternalMessageInfo

func (m *ReceiveAwardRsp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ReceiveAwardRsp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReceiveAwardRsp) GetInfos() []*AwardInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *ReceiveAwardRsp) GetToast() string {
	if m != nil {
		return m.Toast
	}
	return ""
}

type GetUserAwardInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserAwardInfoReq) Reset()         { *m = GetUserAwardInfoReq{} }
func (m *GetUserAwardInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAwardInfoReq) ProtoMessage()    {}
func (*GetUserAwardInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_checkin_logic__44f910ffe2aeb1df, []int{3}
}
func (m *GetUserAwardInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAwardInfoReq.Unmarshal(m, b)
}
func (m *GetUserAwardInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAwardInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAwardInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAwardInfoReq.Merge(dst, src)
}
func (m *GetUserAwardInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAwardInfoReq.Size(m)
}
func (m *GetUserAwardInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAwardInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAwardInfoReq proto.InternalMessageInfo

func (m *GetUserAwardInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserAwardInfoRsp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Uid                  uint32        `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Infos                []*AwardInfo  `protobuf:"bytes,3,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserAwardInfoRsp) Reset()         { *m = GetUserAwardInfoRsp{} }
func (m *GetUserAwardInfoRsp) String() string { return proto.CompactTextString(m) }
func (*GetUserAwardInfoRsp) ProtoMessage()    {}
func (*GetUserAwardInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_checkin_logic__44f910ffe2aeb1df, []int{4}
}
func (m *GetUserAwardInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAwardInfoRsp.Unmarshal(m, b)
}
func (m *GetUserAwardInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAwardInfoRsp.Marshal(b, m, deterministic)
}
func (dst *GetUserAwardInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAwardInfoRsp.Merge(dst, src)
}
func (m *GetUserAwardInfoRsp) XXX_Size() int {
	return xxx_messageInfo_GetUserAwardInfoRsp.Size(m)
}
func (m *GetUserAwardInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAwardInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAwardInfoRsp proto.InternalMessageInfo

func (m *GetUserAwardInfoRsp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserAwardInfoRsp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserAwardInfoRsp) GetInfos() []*AwardInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

// 入口信息
type CheckInEntranceInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckInEntranceInfoReq) Reset()         { *m = CheckInEntranceInfoReq{} }
func (m *CheckInEntranceInfoReq) String() string { return proto.CompactTextString(m) }
func (*CheckInEntranceInfoReq) ProtoMessage()    {}
func (*CheckInEntranceInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_checkin_logic__44f910ffe2aeb1df, []int{5}
}
func (m *CheckInEntranceInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInEntranceInfoReq.Unmarshal(m, b)
}
func (m *CheckInEntranceInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInEntranceInfoReq.Marshal(b, m, deterministic)
}
func (dst *CheckInEntranceInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInEntranceInfoReq.Merge(dst, src)
}
func (m *CheckInEntranceInfoReq) XXX_Size() int {
	return xxx_messageInfo_CheckInEntranceInfoReq.Size(m)
}
func (m *CheckInEntranceInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInEntranceInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInEntranceInfoReq proto.InternalMessageInfo

func (m *CheckInEntranceInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type CheckInEntranceInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsDisplay            bool          `protobuf:"varint,2,opt,name=is_display,json=isDisplay,proto3" json:"is_display,omitempty"`
	Infos                []*AwardInfo  `protobuf:"bytes,3,rep,name=infos,proto3" json:"infos,omitempty"`
	ImTitle              string        `protobuf:"bytes,4,opt,name=im_title,json=imTitle,proto3" json:"im_title,omitempty"`
	ImSubtitle           string        `protobuf:"bytes,5,opt,name=im_subtitle,json=imSubtitle,proto3" json:"im_subtitle,omitempty"`
	ActText              string        `protobuf:"bytes,6,opt,name=act_text,json=actText,proto3" json:"act_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CheckInEntranceInfoResp) Reset()         { *m = CheckInEntranceInfoResp{} }
func (m *CheckInEntranceInfoResp) String() string { return proto.CompactTextString(m) }
func (*CheckInEntranceInfoResp) ProtoMessage()    {}
func (*CheckInEntranceInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_checkin_logic__44f910ffe2aeb1df, []int{6}
}
func (m *CheckInEntranceInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInEntranceInfoResp.Unmarshal(m, b)
}
func (m *CheckInEntranceInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInEntranceInfoResp.Marshal(b, m, deterministic)
}
func (dst *CheckInEntranceInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInEntranceInfoResp.Merge(dst, src)
}
func (m *CheckInEntranceInfoResp) XXX_Size() int {
	return xxx_messageInfo_CheckInEntranceInfoResp.Size(m)
}
func (m *CheckInEntranceInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInEntranceInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInEntranceInfoResp proto.InternalMessageInfo

func (m *CheckInEntranceInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckInEntranceInfoResp) GetIsDisplay() bool {
	if m != nil {
		return m.IsDisplay
	}
	return false
}

func (m *CheckInEntranceInfoResp) GetInfos() []*AwardInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *CheckInEntranceInfoResp) GetImTitle() string {
	if m != nil {
		return m.ImTitle
	}
	return ""
}

func (m *CheckInEntranceInfoResp) GetImSubtitle() string {
	if m != nil {
		return m.ImSubtitle
	}
	return ""
}

func (m *CheckInEntranceInfoResp) GetActText() string {
	if m != nil {
		return m.ActText
	}
	return ""
}

type SetDeeplinkSourceReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              string       `protobuf:"bytes,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetDeeplinkSourceReq) Reset()         { *m = SetDeeplinkSourceReq{} }
func (m *SetDeeplinkSourceReq) String() string { return proto.CompactTextString(m) }
func (*SetDeeplinkSourceReq) ProtoMessage()    {}
func (*SetDeeplinkSourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_checkin_logic__44f910ffe2aeb1df, []int{7}
}
func (m *SetDeeplinkSourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDeeplinkSourceReq.Unmarshal(m, b)
}
func (m *SetDeeplinkSourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDeeplinkSourceReq.Marshal(b, m, deterministic)
}
func (dst *SetDeeplinkSourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDeeplinkSourceReq.Merge(dst, src)
}
func (m *SetDeeplinkSourceReq) XXX_Size() int {
	return xxx_messageInfo_SetDeeplinkSourceReq.Size(m)
}
func (m *SetDeeplinkSourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDeeplinkSourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetDeeplinkSourceReq proto.InternalMessageInfo

func (m *SetDeeplinkSourceReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetDeeplinkSourceReq) GetGroupId() string {
	if m != nil {
		return m.GroupId
	}
	return ""
}

type SetDeeplinkSourceRsp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetDeeplinkSourceRsp) Reset()         { *m = SetDeeplinkSourceRsp{} }
func (m *SetDeeplinkSourceRsp) String() string { return proto.CompactTextString(m) }
func (*SetDeeplinkSourceRsp) ProtoMessage()    {}
func (*SetDeeplinkSourceRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_checkin_logic__44f910ffe2aeb1df, []int{8}
}
func (m *SetDeeplinkSourceRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDeeplinkSourceRsp.Unmarshal(m, b)
}
func (m *SetDeeplinkSourceRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDeeplinkSourceRsp.Marshal(b, m, deterministic)
}
func (dst *SetDeeplinkSourceRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDeeplinkSourceRsp.Merge(dst, src)
}
func (m *SetDeeplinkSourceRsp) XXX_Size() int {
	return xxx_messageInfo_SetDeeplinkSourceRsp.Size(m)
}
func (m *SetDeeplinkSourceRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDeeplinkSourceRsp.DiscardUnknown(m)
}

var xxx_messageInfo_SetDeeplinkSourceRsp proto.InternalMessageInfo

func (m *SetDeeplinkSourceRsp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*ReceiveAwardReq)(nil), "ga.usercheckinlogic.ReceiveAwardReq")
	proto.RegisterType((*AwardInfo)(nil), "ga.usercheckinlogic.AwardInfo")
	proto.RegisterType((*ReceiveAwardRsp)(nil), "ga.usercheckinlogic.ReceiveAwardRsp")
	proto.RegisterType((*GetUserAwardInfoReq)(nil), "ga.usercheckinlogic.GetUserAwardInfoReq")
	proto.RegisterType((*GetUserAwardInfoRsp)(nil), "ga.usercheckinlogic.GetUserAwardInfoRsp")
	proto.RegisterType((*CheckInEntranceInfoReq)(nil), "ga.usercheckinlogic.CheckInEntranceInfoReq")
	proto.RegisterType((*CheckInEntranceInfoResp)(nil), "ga.usercheckinlogic.CheckInEntranceInfoResp")
	proto.RegisterType((*SetDeeplinkSourceReq)(nil), "ga.usercheckinlogic.SetDeeplinkSourceReq")
	proto.RegisterType((*SetDeeplinkSourceRsp)(nil), "ga.usercheckinlogic.SetDeeplinkSourceRsp")
	proto.RegisterEnum("ga.usercheckinlogic.AwardType", AwardType_name, AwardType_value)
}

func init() {
	proto.RegisterFile("user-checkin-logic_.proto", fileDescriptor_user_checkin_logic__44f910ffe2aeb1df)
}

var fileDescriptor_user_checkin_logic__44f910ffe2aeb1df = []byte{
	// 598 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x54, 0xd1, 0x6e, 0xd4, 0x3a,
	0x10, 0xbd, 0xe9, 0x76, 0xbb, 0xc9, 0xec, 0xdd, 0xdb, 0xca, 0xad, 0x2e, 0x29, 0xa8, 0xb0, 0xca,
	0x03, 0x5a, 0x90, 0x9a, 0x4a, 0x05, 0x1e, 0x2b, 0xd1, 0x52, 0x04, 0xfb, 0x82, 0x90, 0xdb, 0x4a,
	0xc0, 0x4b, 0xe4, 0x75, 0xa6, 0xc1, 0x6a, 0x12, 0xa7, 0xb6, 0x53, 0x9a, 0x3f, 0xe0, 0x1b, 0xf8,
	0x32, 0x3e, 0x83, 0x4f, 0x40, 0x76, 0xb2, 0xad, 0x54, 0xad, 0x10, 0xcb, 0x0b, 0x6f, 0x33, 0x67,
	0x8e, 0x27, 0x39, 0x67, 0xc6, 0x86, 0xed, 0x5a, 0xa3, 0xda, 0xe5, 0x9f, 0x91, 0x5f, 0x88, 0x72,
	0x37, 0x97, 0x99, 0xe0, 0x49, 0x5c, 0x29, 0x69, 0x24, 0xd9, 0xcc, 0x58, 0x6c, 0xab, 0x5d, 0xd1,
	0xd5, 0xee, 0x8f, 0x32, 0x96, 0xcc, 0x98, 0xc6, 0x96, 0x13, 0x7d, 0x80, 0x75, 0x8a, 0x1c, 0xc5,
	0x15, 0x1e, 0x7e, 0x61, 0x2a, 0xa5, 0x78, 0x49, 0x1e, 0x83, 0x6f, 0x09, 0x89, 0xc2, 0xcb, 0xd0,
	0x1b, 0x7b, 0x93, 0xe1, 0xfe, 0x30, 0xce, 0x58, 0x7c, 0xc4, 0x34, 0x52, 0xbc, 0xa4, 0x83, 0x59,
	0x1b, 0x90, 0x1d, 0x80, 0x94, 0x35, 0x89, 0x92, 0x75, 0x99, 0xea, 0x70, 0x65, 0xdc, 0x9b, 0x8c,
	0x68, 0x90, 0xb2, 0x86, 0x3a, 0x20, 0xfa, 0xee, 0x41, 0xe0, 0x7a, 0x4e, 0xcb, 0x73, 0x49, 0x1e,
	0x40, 0x70, 0x43, 0x76, 0x5d, 0x47, 0xd4, 0x9f, 0x73, 0xc9, 0x36, 0xf8, 0x82, 0xcb, 0x32, 0xa9,
	0x55, 0x1e, 0xae, 0x8c, 0xbd, 0x49, 0x40, 0x07, 0x36, 0x3f, 0x53, 0x39, 0x39, 0x00, 0x60, 0xb6,
	0x49, 0x62, 0x9a, 0x0a, 0xc3, 0xde, 0xd8, 0x9b, 0xfc, 0xb7, 0xff, 0x30, 0x5e, 0x20, 0x2c, 0x76,
	0xdf, 0x3a, 0x6d, 0x2a, 0xa4, 0x01, 0x9b, 0x87, 0x84, 0xc0, 0x6a, 0xc9, 0x0a, 0x0c, 0x57, 0x5d,
	0x57, 0x17, 0x93, 0x47, 0x30, 0x34, 0x33, 0x64, 0x65, 0x52, 0x29, 0xc1, 0x31, 0xec, 0xbb, 0x9f,
	0x01, 0x07, 0xbd, 0xb7, 0x88, 0x25, 0x94, 0x88, 0x69, 0xc2, 0xae, 0x98, 0x61, 0x2a, 0x5c, 0x1b,
	0x7b, 0x13, 0x9f, 0x82, 0x85, 0x0e, 0x1d, 0x12, 0x7d, 0xf3, 0xee, 0xb8, 0xa6, 0x2b, 0xf2, 0x04,
	0x82, 0xce, 0x35, 0x5d, 0x75, 0xb6, 0xfd, 0x7b, 0x6b, 0x9b, 0xae, 0xa8, 0x3f, 0xeb, 0x22, 0xb2,
	0x01, 0xbd, 0x5a, 0xa4, 0x4e, 0xe9, 0x88, 0xda, 0x90, 0x3c, 0x87, 0xbe, 0x28, 0xcf, 0xa5, 0x0e,
	0x7b, 0xe3, 0xde, 0x64, 0xf8, 0x2b, 0x81, 0xd6, 0x4c, 0xda, 0x92, 0xc9, 0x16, 0xf4, 0x8d, 0x64,
	0xda, 0x74, 0xea, 0xda, 0x24, 0x3a, 0x80, 0xcd, 0x37, 0x68, 0xce, 0x34, 0xaa, 0xdb, 0x03, 0xbf,
	0x3f, 0xd5, 0xe8, 0xab, 0xb7, 0xe0, 0xfc, 0x5f, 0xd1, 0x17, 0xbd, 0x84, 0xff, 0x5f, 0x59, 0xc2,
	0xb4, 0x7c, 0x5d, 0x1a, 0xc5, 0x4a, 0x8e, 0xcb, 0x8a, 0xf9, 0xe1, 0xc1, 0xbd, 0x85, 0x2d, 0x96,
	0x13, 0xb4, 0x03, 0x20, 0x74, 0x92, 0x0a, 0x5d, 0xe5, 0xac, 0x71, 0xba, 0x7c, 0x1a, 0x08, 0x7d,
	0xdc, 0x02, 0x7f, 0x38, 0x3d, 0xbb, 0xf4, 0x45, 0x62, 0x84, 0xc9, 0xe7, 0xeb, 0x39, 0x10, 0xc5,
	0xa9, 0x4d, 0xed, 0x02, 0x8a, 0x22, 0xd1, 0xf5, 0xac, 0xad, 0xf6, 0x5d, 0x15, 0x44, 0x71, 0xd2,
	0x21, 0xf6, 0x2c, 0xe3, 0x26, 0x31, 0x78, 0x6d, 0xdc, 0x7a, 0x06, 0x74, 0xc0, 0xb8, 0x39, 0xc5,
	0x6b, 0x13, 0x7d, 0x84, 0xad, 0x13, 0x34, 0xc7, 0x88, 0x55, 0x2e, 0xca, 0x8b, 0x13, 0x59, 0x2b,
	0x8e, 0xcb, 0xdc, 0xea, 0x6d, 0xf0, 0x33, 0x25, 0xeb, 0x2a, 0xe9, 0x26, 0x18, 0xd0, 0x81, 0xcb,
	0xa7, 0x69, 0x74, 0xb8, 0xa8, 0xf5, 0x52, 0x4e, 0x3e, 0x3d, 0xee, 0xde, 0x04, 0x77, 0x39, 0x47,
	0x37, 0x0f, 0x84, 0x30, 0x1b, 0xff, 0x90, 0x4d, 0x58, 0x77, 0xe9, 0x3b, 0x69, 0x28, 0x72, 0x81,
	0x57, 0xb8, 0xe1, 0xdd, 0x80, 0x6f, 0x99, 0x9e, 0x83, 0x2b, 0x47, 0x53, 0x08, 0xb9, 0x2c, 0xe2,
	0x46, 0x34, 0xb2, 0xb6, 0x1f, 0x2a, 0x64, 0x8a, 0x79, 0xfb, 0xa0, 0x7d, 0xda, 0xcd, 0x64, 0xce,
	0xca, 0x2c, 0x7e, 0xb1, 0x6f, 0x4c, 0xcc, 0x65, 0xb1, 0xe7, 0x60, 0x2e, 0xf3, 0x3d, 0x56, 0x55,
	0x7b, 0x77, 0xc7, 0x32, 0x5b, 0x73, 0xe5, 0x67, 0x3f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x4a, 0xb5,
	0xeb, 0xce, 0x47, 0x05, 0x00, 0x00,
}
