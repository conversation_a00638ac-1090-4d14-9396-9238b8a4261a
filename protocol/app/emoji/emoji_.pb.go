// Code generated by protoc-gen-gogo.
// source: emoji_.proto
// DO NOT EDIT!

/*
	Package emoji is a generated protocol buffer package.

	It is generated from these files:
		emoji_.proto

	It has these top-level messages:
		EmojiItem
		SaveEmojiReq
		SaveEmojiResp
		DeleteEmojiReq
		DeleteEmojiResp
		GetEmojiPkgListReq
		GetEmojiPkgListResp
		EmojiPackage
		GetEmojiListByPkgReq
		GetEmojiListByPkgResp
*/
package emoji

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type SaveEmojiReq_SaveEmojiMode int32

const (
	SaveEmojiReq_INVALID             SaveEmojiReq_SaveEmojiMode = 0
	SaveEmojiReq_SAVE_EMOJI_TO_EMOJI SaveEmojiReq_SaveEmojiMode = 1
	SaveEmojiReq_SAVE_IMG_TO_EMOJI   SaveEmojiReq_SaveEmojiMode = 2
)

var SaveEmojiReq_SaveEmojiMode_name = map[int32]string{
	0: "INVALID",
	1: "SAVE_EMOJI_TO_EMOJI",
	2: "SAVE_IMG_TO_EMOJI",
}
var SaveEmojiReq_SaveEmojiMode_value = map[string]int32{
	"INVALID":             0,
	"SAVE_EMOJI_TO_EMOJI": 1,
	"SAVE_IMG_TO_EMOJI":   2,
}

func (x SaveEmojiReq_SaveEmojiMode) Enum() *SaveEmojiReq_SaveEmojiMode {
	p := new(SaveEmojiReq_SaveEmojiMode)
	*p = x
	return p
}
func (x SaveEmojiReq_SaveEmojiMode) String() string {
	return proto.EnumName(SaveEmojiReq_SaveEmojiMode_name, int32(x))
}
func (x *SaveEmojiReq_SaveEmojiMode) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SaveEmojiReq_SaveEmojiMode_value, data, "SaveEmojiReq_SaveEmojiMode")
	if err != nil {
		return err
	}
	*x = SaveEmojiReq_SaveEmojiMode(value)
	return nil
}
func (SaveEmojiReq_SaveEmojiMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorEmoji_, []int{1, 0}
}

type EmojiItem struct {
	EmojiId   string `protobuf:"bytes,1,req,name=emoji_id,json=emojiId" json:"emoji_id"`
	Url       string `protobuf:"bytes,2,req,name=url" json:"url"`
	Thumbnail string `protobuf:"bytes,3,req,name=thumbnail" json:"thumbnail"`
	Name      string `protobuf:"bytes,4,opt,name=name" json:"name"`
	Desc      string `protobuf:"bytes,5,opt,name=desc" json:"desc"`
	Height    uint32 `protobuf:"varint,6,req,name=height" json:"height"`
	Width     uint32 `protobuf:"varint,7,req,name=width" json:"width"`
}

func (m *EmojiItem) Reset()                    { *m = EmojiItem{} }
func (m *EmojiItem) String() string            { return proto.CompactTextString(m) }
func (*EmojiItem) ProtoMessage()               {}
func (*EmojiItem) Descriptor() ([]byte, []int) { return fileDescriptorEmoji_, []int{0} }

func (m *EmojiItem) GetEmojiId() string {
	if m != nil {
		return m.EmojiId
	}
	return ""
}

func (m *EmojiItem) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *EmojiItem) GetThumbnail() string {
	if m != nil {
		return m.Thumbnail
	}
	return ""
}

func (m *EmojiItem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *EmojiItem) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *EmojiItem) GetHeight() uint32 {
	if m != nil {
		return m.Height
	}
	return 0
}

func (m *EmojiItem) GetWidth() uint32 {
	if m != nil {
		return m.Width
	}
	return 0
}

//
// 另存为“表情”到“我的表情”
// 另存为图片到“我的表情”
type SaveEmojiReq struct {
	BaseReq *ga.BaseReq                `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Mode    SaveEmojiReq_SaveEmojiMode `protobuf:"varint,2,req,name=mode,enum=ga.SaveEmojiReq_SaveEmojiMode" json:"mode"`
	Key     string                     `protobuf:"bytes,3,req,name=key" json:"key"`
}

func (m *SaveEmojiReq) Reset()                    { *m = SaveEmojiReq{} }
func (m *SaveEmojiReq) String() string            { return proto.CompactTextString(m) }
func (*SaveEmojiReq) ProtoMessage()               {}
func (*SaveEmojiReq) Descriptor() ([]byte, []int) { return fileDescriptorEmoji_, []int{1} }

func (m *SaveEmojiReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SaveEmojiReq) GetMode() SaveEmojiReq_SaveEmojiMode {
	if m != nil {
		return m.Mode
	}
	return SaveEmojiReq_INVALID
}

func (m *SaveEmojiReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type SaveEmojiResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Item     *EmojiItem   `protobuf:"bytes,2,req,name=item" json:"item,omitempty"`
}

func (m *SaveEmojiResp) Reset()                    { *m = SaveEmojiResp{} }
func (m *SaveEmojiResp) String() string            { return proto.CompactTextString(m) }
func (*SaveEmojiResp) ProtoMessage()               {}
func (*SaveEmojiResp) Descriptor() ([]byte, []int) { return fileDescriptorEmoji_, []int{2} }

func (m *SaveEmojiResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SaveEmojiResp) GetItem() *EmojiItem {
	if m != nil {
		return m.Item
	}
	return nil
}

//
// 删除表情
type DeleteEmojiReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	PackageId string      `protobuf:"bytes,2,req,name=package_id,json=packageId" json:"package_id"`
	EmojiIds  []string    `protobuf:"bytes,3,rep,name=emoji_ids,json=emojiIds" json:"emoji_ids,omitempty"`
}

func (m *DeleteEmojiReq) Reset()                    { *m = DeleteEmojiReq{} }
func (m *DeleteEmojiReq) String() string            { return proto.CompactTextString(m) }
func (*DeleteEmojiReq) ProtoMessage()               {}
func (*DeleteEmojiReq) Descriptor() ([]byte, []int) { return fileDescriptorEmoji_, []int{3} }

func (m *DeleteEmojiReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DeleteEmojiReq) GetPackageId() string {
	if m != nil {
		return m.PackageId
	}
	return ""
}

func (m *DeleteEmojiReq) GetEmojiIds() []string {
	if m != nil {
		return m.EmojiIds
	}
	return nil
}

type DeleteEmojiResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *DeleteEmojiResp) Reset()                    { *m = DeleteEmojiResp{} }
func (m *DeleteEmojiResp) String() string            { return proto.CompactTextString(m) }
func (*DeleteEmojiResp) ProtoMessage()               {}
func (*DeleteEmojiResp) Descriptor() ([]byte, []int) { return fileDescriptorEmoji_, []int{4} }

func (m *DeleteEmojiResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

//
// 获取我的表情包列表.
type GetEmojiPkgListReq struct {
	BaseReq        *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	StartIndex     int32       `protobuf:"varint,2,opt,name=start_index,json=startIndex" json:"start_index"`
	Count          int32       `protobuf:"varint,3,opt,name=count" json:"count"`
	LoadFirstItem  int32       `protobuf:"varint,4,opt,name=load_first_item,json=loadFirstItem" json:"load_first_item"`
	FirstItemLimit int32       `protobuf:"varint,5,opt,name=first_item_limit,json=firstItemLimit" json:"first_item_limit"`
}

func (m *GetEmojiPkgListReq) Reset()                    { *m = GetEmojiPkgListReq{} }
func (m *GetEmojiPkgListReq) String() string            { return proto.CompactTextString(m) }
func (*GetEmojiPkgListReq) ProtoMessage()               {}
func (*GetEmojiPkgListReq) Descriptor() ([]byte, []int) { return fileDescriptorEmoji_, []int{5} }

func (m *GetEmojiPkgListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetEmojiPkgListReq) GetStartIndex() int32 {
	if m != nil {
		return m.StartIndex
	}
	return 0
}

func (m *GetEmojiPkgListReq) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetEmojiPkgListReq) GetLoadFirstItem() int32 {
	if m != nil {
		return m.LoadFirstItem
	}
	return 0
}

func (m *GetEmojiPkgListReq) GetFirstItemLimit() int32 {
	if m != nil {
		return m.FirstItemLimit
	}
	return 0
}

type GetEmojiPkgListResp struct {
	BaseResp  *ga.BaseResp    `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	PkgList   []*EmojiPackage `protobuf:"bytes,2,rep,name=pkg_list,json=pkgList" json:"pkg_list,omitempty"`
	FirstItem []*EmojiItem    `protobuf:"bytes,3,rep,name=first_item,json=firstItem" json:"first_item,omitempty"`
}

func (m *GetEmojiPkgListResp) Reset()                    { *m = GetEmojiPkgListResp{} }
func (m *GetEmojiPkgListResp) String() string            { return proto.CompactTextString(m) }
func (*GetEmojiPkgListResp) ProtoMessage()               {}
func (*GetEmojiPkgListResp) Descriptor() ([]byte, []int) { return fileDescriptorEmoji_, []int{6} }

func (m *GetEmojiPkgListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetEmojiPkgListResp) GetPkgList() []*EmojiPackage {
	if m != nil {
		return m.PkgList
	}
	return nil
}

func (m *GetEmojiPkgListResp) GetFirstItem() []*EmojiItem {
	if m != nil {
		return m.FirstItem
	}
	return nil
}

type EmojiPackage struct {
	PackageId  string `protobuf:"bytes,1,req,name=package_id,json=packageId" json:"package_id"`
	UpdateTime uint64 `protobuf:"varint,2,req,name=update_time,json=updateTime" json:"update_time"`
	Name       string `protobuf:"bytes,3,opt,name=name" json:"name"`
	TotalCount int32  `protobuf:"varint,4,req,name=total_count,json=totalCount" json:"total_count"`
	CoverUrl   string `protobuf:"bytes,5,req,name=cover_url,json=coverUrl" json:"cover_url"`
	OwnerId    uint32 `protobuf:"varint,6,req,name=owner_id,json=ownerId" json:"owner_id"`
	Type       string `protobuf:"bytes,7,opt,name=type" json:"type"`
}

func (m *EmojiPackage) Reset()                    { *m = EmojiPackage{} }
func (m *EmojiPackage) String() string            { return proto.CompactTextString(m) }
func (*EmojiPackage) ProtoMessage()               {}
func (*EmojiPackage) Descriptor() ([]byte, []int) { return fileDescriptorEmoji_, []int{7} }

func (m *EmojiPackage) GetPackageId() string {
	if m != nil {
		return m.PackageId
	}
	return ""
}

func (m *EmojiPackage) GetUpdateTime() uint64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *EmojiPackage) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *EmojiPackage) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *EmojiPackage) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *EmojiPackage) GetOwnerId() uint32 {
	if m != nil {
		return m.OwnerId
	}
	return 0
}

func (m *EmojiPackage) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

//
// 通过指定表情包id获取表情列表
type GetEmojiListByPkgReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	PackageId  string      `protobuf:"bytes,2,req,name=package_id,json=packageId" json:"package_id"`
	StartIndex int32       `protobuf:"varint,3,opt,name=start_index,json=startIndex" json:"start_index"`
	Count      int32       `protobuf:"varint,4,opt,name=count" json:"count"`
	UpdateTime uint64      `protobuf:"varint,5,opt,name=update_time,json=updateTime" json:"update_time"`
}

func (m *GetEmojiListByPkgReq) Reset()                    { *m = GetEmojiListByPkgReq{} }
func (m *GetEmojiListByPkgReq) String() string            { return proto.CompactTextString(m) }
func (*GetEmojiListByPkgReq) ProtoMessage()               {}
func (*GetEmojiListByPkgReq) Descriptor() ([]byte, []int) { return fileDescriptorEmoji_, []int{8} }

func (m *GetEmojiListByPkgReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetEmojiListByPkgReq) GetPackageId() string {
	if m != nil {
		return m.PackageId
	}
	return ""
}

func (m *GetEmojiListByPkgReq) GetStartIndex() int32 {
	if m != nil {
		return m.StartIndex
	}
	return 0
}

func (m *GetEmojiListByPkgReq) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetEmojiListByPkgReq) GetUpdateTime() uint64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetEmojiListByPkgResp struct {
	BaseResp   *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	UpdateTime uint64       `protobuf:"varint,2,req,name=update_time,json=updateTime" json:"update_time"`
	Items      []*EmojiItem `protobuf:"bytes,3,rep,name=items" json:"items,omitempty"`
	PackageId  string       `protobuf:"bytes,4,req,name=package_id,json=packageId" json:"package_id"`
	Name       string       `protobuf:"bytes,5,opt,name=name" json:"name"`
	TotalCount int32        `protobuf:"varint,6,req,name=total_count,json=totalCount" json:"total_count"`
}

func (m *GetEmojiListByPkgResp) Reset()                    { *m = GetEmojiListByPkgResp{} }
func (m *GetEmojiListByPkgResp) String() string            { return proto.CompactTextString(m) }
func (*GetEmojiListByPkgResp) ProtoMessage()               {}
func (*GetEmojiListByPkgResp) Descriptor() ([]byte, []int) { return fileDescriptorEmoji_, []int{9} }

func (m *GetEmojiListByPkgResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetEmojiListByPkgResp) GetUpdateTime() uint64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *GetEmojiListByPkgResp) GetItems() []*EmojiItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *GetEmojiListByPkgResp) GetPackageId() string {
	if m != nil {
		return m.PackageId
	}
	return ""
}

func (m *GetEmojiListByPkgResp) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetEmojiListByPkgResp) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func init() {
	proto.RegisterType((*EmojiItem)(nil), "ga.EmojiItem")
	proto.RegisterType((*SaveEmojiReq)(nil), "ga.SaveEmojiReq")
	proto.RegisterType((*SaveEmojiResp)(nil), "ga.SaveEmojiResp")
	proto.RegisterType((*DeleteEmojiReq)(nil), "ga.DeleteEmojiReq")
	proto.RegisterType((*DeleteEmojiResp)(nil), "ga.DeleteEmojiResp")
	proto.RegisterType((*GetEmojiPkgListReq)(nil), "ga.GetEmojiPkgListReq")
	proto.RegisterType((*GetEmojiPkgListResp)(nil), "ga.GetEmojiPkgListResp")
	proto.RegisterType((*EmojiPackage)(nil), "ga.EmojiPackage")
	proto.RegisterType((*GetEmojiListByPkgReq)(nil), "ga.GetEmojiListByPkgReq")
	proto.RegisterType((*GetEmojiListByPkgResp)(nil), "ga.GetEmojiListByPkgResp")
	proto.RegisterEnum("ga.SaveEmojiReq_SaveEmojiMode", SaveEmojiReq_SaveEmojiMode_name, SaveEmojiReq_SaveEmojiMode_value)
}
func (m *EmojiItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EmojiItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(len(m.EmojiId)))
	i += copy(dAtA[i:], m.EmojiId)
	dAtA[i] = 0x12
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(len(m.Thumbnail)))
	i += copy(dAtA[i:], m.Thumbnail)
	dAtA[i] = 0x22
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(len(m.Desc)))
	i += copy(dAtA[i:], m.Desc)
	dAtA[i] = 0x30
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.Height))
	dAtA[i] = 0x38
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.Width))
	return i, nil
}

func (m *SaveEmojiReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SaveEmojiReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintEmoji_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.Mode))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	return i, nil
}

func (m *SaveEmojiResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SaveEmojiResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintEmoji_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.Item == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintEmoji_(dAtA, i, uint64(m.Item.Size()))
		n3, err := m.Item.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *DeleteEmojiReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteEmojiReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintEmoji_(dAtA, i, uint64(m.BaseReq.Size()))
		n4, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(len(m.PackageId)))
	i += copy(dAtA[i:], m.PackageId)
	if len(m.EmojiIds) > 0 {
		for _, s := range m.EmojiIds {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *DeleteEmojiResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteEmojiResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintEmoji_(dAtA, i, uint64(m.BaseResp.Size()))
		n5, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *GetEmojiPkgListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetEmojiPkgListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintEmoji_(dAtA, i, uint64(m.BaseReq.Size()))
		n6, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.StartIndex))
	dAtA[i] = 0x18
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x20
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.LoadFirstItem))
	dAtA[i] = 0x28
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.FirstItemLimit))
	return i, nil
}

func (m *GetEmojiPkgListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetEmojiPkgListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintEmoji_(dAtA, i, uint64(m.BaseResp.Size()))
		n7, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if len(m.PkgList) > 0 {
		for _, msg := range m.PkgList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintEmoji_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.FirstItem) > 0 {
		for _, msg := range m.FirstItem {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintEmoji_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *EmojiPackage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EmojiPackage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(len(m.PackageId)))
	i += copy(dAtA[i:], m.PackageId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.UpdateTime))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x20
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.TotalCount))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(len(m.CoverUrl)))
	i += copy(dAtA[i:], m.CoverUrl)
	dAtA[i] = 0x30
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.OwnerId))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(len(m.Type)))
	i += copy(dAtA[i:], m.Type)
	return i, nil
}

func (m *GetEmojiListByPkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetEmojiListByPkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintEmoji_(dAtA, i, uint64(m.BaseReq.Size()))
		n8, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(len(m.PackageId)))
	i += copy(dAtA[i:], m.PackageId)
	dAtA[i] = 0x18
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.StartIndex))
	dAtA[i] = 0x20
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x28
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.UpdateTime))
	return i, nil
}

func (m *GetEmojiListByPkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetEmojiListByPkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintEmoji_(dAtA, i, uint64(m.BaseResp.Size()))
		n9, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.UpdateTime))
	if len(m.Items) > 0 {
		for _, msg := range m.Items {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintEmoji_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x22
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(len(m.PackageId)))
	i += copy(dAtA[i:], m.PackageId)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x30
	i++
	i = encodeVarintEmoji_(dAtA, i, uint64(m.TotalCount))
	return i, nil
}

func encodeFixed64Emoji_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Emoji_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintEmoji_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *EmojiItem) Size() (n int) {
	var l int
	_ = l
	l = len(m.EmojiId)
	n += 1 + l + sovEmoji_(uint64(l))
	l = len(m.Url)
	n += 1 + l + sovEmoji_(uint64(l))
	l = len(m.Thumbnail)
	n += 1 + l + sovEmoji_(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovEmoji_(uint64(l))
	l = len(m.Desc)
	n += 1 + l + sovEmoji_(uint64(l))
	n += 1 + sovEmoji_(uint64(m.Height))
	n += 1 + sovEmoji_(uint64(m.Width))
	return n
}

func (m *SaveEmojiReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovEmoji_(uint64(l))
	}
	n += 1 + sovEmoji_(uint64(m.Mode))
	l = len(m.Key)
	n += 1 + l + sovEmoji_(uint64(l))
	return n
}

func (m *SaveEmojiResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovEmoji_(uint64(l))
	}
	if m.Item != nil {
		l = m.Item.Size()
		n += 1 + l + sovEmoji_(uint64(l))
	}
	return n
}

func (m *DeleteEmojiReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovEmoji_(uint64(l))
	}
	l = len(m.PackageId)
	n += 1 + l + sovEmoji_(uint64(l))
	if len(m.EmojiIds) > 0 {
		for _, s := range m.EmojiIds {
			l = len(s)
			n += 1 + l + sovEmoji_(uint64(l))
		}
	}
	return n
}

func (m *DeleteEmojiResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovEmoji_(uint64(l))
	}
	return n
}

func (m *GetEmojiPkgListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovEmoji_(uint64(l))
	}
	n += 1 + sovEmoji_(uint64(m.StartIndex))
	n += 1 + sovEmoji_(uint64(m.Count))
	n += 1 + sovEmoji_(uint64(m.LoadFirstItem))
	n += 1 + sovEmoji_(uint64(m.FirstItemLimit))
	return n
}

func (m *GetEmojiPkgListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovEmoji_(uint64(l))
	}
	if len(m.PkgList) > 0 {
		for _, e := range m.PkgList {
			l = e.Size()
			n += 1 + l + sovEmoji_(uint64(l))
		}
	}
	if len(m.FirstItem) > 0 {
		for _, e := range m.FirstItem {
			l = e.Size()
			n += 1 + l + sovEmoji_(uint64(l))
		}
	}
	return n
}

func (m *EmojiPackage) Size() (n int) {
	var l int
	_ = l
	l = len(m.PackageId)
	n += 1 + l + sovEmoji_(uint64(l))
	n += 1 + sovEmoji_(uint64(m.UpdateTime))
	l = len(m.Name)
	n += 1 + l + sovEmoji_(uint64(l))
	n += 1 + sovEmoji_(uint64(m.TotalCount))
	l = len(m.CoverUrl)
	n += 1 + l + sovEmoji_(uint64(l))
	n += 1 + sovEmoji_(uint64(m.OwnerId))
	l = len(m.Type)
	n += 1 + l + sovEmoji_(uint64(l))
	return n
}

func (m *GetEmojiListByPkgReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovEmoji_(uint64(l))
	}
	l = len(m.PackageId)
	n += 1 + l + sovEmoji_(uint64(l))
	n += 1 + sovEmoji_(uint64(m.StartIndex))
	n += 1 + sovEmoji_(uint64(m.Count))
	n += 1 + sovEmoji_(uint64(m.UpdateTime))
	return n
}

func (m *GetEmojiListByPkgResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovEmoji_(uint64(l))
	}
	n += 1 + sovEmoji_(uint64(m.UpdateTime))
	if len(m.Items) > 0 {
		for _, e := range m.Items {
			l = e.Size()
			n += 1 + l + sovEmoji_(uint64(l))
		}
	}
	l = len(m.PackageId)
	n += 1 + l + sovEmoji_(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovEmoji_(uint64(l))
	n += 1 + sovEmoji_(uint64(m.TotalCount))
	return n
}

func sovEmoji_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozEmoji_(x uint64) (n int) {
	return sovEmoji_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *EmojiItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEmoji_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: EmojiItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: EmojiItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EmojiId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EmojiId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Thumbnail", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Thumbnail = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Height", wireType)
			}
			m.Height = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Height |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Width", wireType)
			}
			m.Width = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Width |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipEmoji_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEmoji_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("emoji_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("thumbnail")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("height")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("width")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SaveEmojiReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEmoji_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SaveEmojiReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SaveEmojiReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Mode", wireType)
			}
			m.Mode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Mode |= (SaveEmojiReq_SaveEmojiMode(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipEmoji_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEmoji_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("mode")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("key")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SaveEmojiResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEmoji_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SaveEmojiResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SaveEmojiResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Item", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Item == nil {
				m.Item = &EmojiItem{}
			}
			if err := m.Item.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipEmoji_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEmoji_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("item")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteEmojiReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEmoji_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteEmojiReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteEmojiReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PackageId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PackageId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EmojiIds", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EmojiIds = append(m.EmojiIds, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEmoji_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEmoji_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("package_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteEmojiResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEmoji_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteEmojiResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteEmojiResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipEmoji_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEmoji_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetEmojiPkgListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEmoji_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetEmojiPkgListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetEmojiPkgListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartIndex", wireType)
			}
			m.StartIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartIndex |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoadFirstItem", wireType)
			}
			m.LoadFirstItem = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LoadFirstItem |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FirstItemLimit", wireType)
			}
			m.FirstItemLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FirstItemLimit |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipEmoji_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEmoji_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetEmojiPkgListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEmoji_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetEmojiPkgListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetEmojiPkgListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PkgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PkgList = append(m.PkgList, &EmojiPackage{})
			if err := m.PkgList[len(m.PkgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FirstItem", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FirstItem = append(m.FirstItem, &EmojiItem{})
			if err := m.FirstItem[len(m.FirstItem)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEmoji_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEmoji_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *EmojiPackage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEmoji_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: EmojiPackage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: EmojiPackage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PackageId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PackageId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalCount", wireType)
			}
			m.TotalCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalCount |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CoverUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CoverUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OwnerId", wireType)
			}
			m.OwnerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OwnerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEmoji_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEmoji_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("package_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("update_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("total_count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("cover_url")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("owner_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetEmojiListByPkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEmoji_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetEmojiListByPkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetEmojiListByPkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PackageId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PackageId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartIndex", wireType)
			}
			m.StartIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartIndex |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipEmoji_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEmoji_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("package_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetEmojiListByPkgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEmoji_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetEmojiListByPkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetEmojiListByPkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Items = append(m.Items, &EmojiItem{})
			if err := m.Items[len(m.Items)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PackageId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PackageId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEmoji_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalCount", wireType)
			}
			m.TotalCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalCount |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipEmoji_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEmoji_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("update_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("package_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("total_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipEmoji_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowEmoji_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowEmoji_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthEmoji_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowEmoji_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipEmoji_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthEmoji_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowEmoji_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("emoji_.proto", fileDescriptorEmoji_) }

var fileDescriptorEmoji_ = []byte{
	// 793 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x55, 0xcf, 0x4f, 0xdb, 0x48,
	0x14, 0xc6, 0x3f, 0x42, 0xe2, 0xe7, 0x04, 0xb2, 0xc3, 0xb2, 0x6b, 0xb1, 0xab, 0x10, 0x8c, 0x58,
	0x65, 0xb5, 0x28, 0x48, 0x91, 0x56, 0xea, 0xa1, 0x17, 0x52, 0x28, 0x72, 0x15, 0x7e, 0xc8, 0x50,
	0x0e, 0x95, 0x2a, 0x6b, 0x12, 0x0f, 0x8e, 0x1b, 0x3b, 0x36, 0xf6, 0x04, 0x9a, 0xfe, 0x15, 0xbd,
	0xf7, 0x1f, 0xe2, 0x52, 0xa9, 0xa7, 0xde, 0x5a, 0x55, 0xf4, 0x5f, 0xe8, 0xa5, 0xb7, 0x6a, 0xc6,
	0x4e, 0x62, 0x42, 0x24, 0x1a, 0xa9, 0xb7, 0xc9, 0xf7, 0xbd, 0x99, 0xbc, 0xef, 0xbd, 0xef, 0x3d,
	0x43, 0x91, 0xf8, 0xc1, 0x2b, 0xd7, 0xaa, 0x87, 0x51, 0x40, 0x03, 0x24, 0x3a, 0x78, 0xad, 0xe4,
	0x60, 0xab, 0x8d, 0x63, 0x92, 0x40, 0xfa, 0x47, 0x01, 0x94, 0x7d, 0x16, 0x63, 0x50, 0xe2, 0xa3,
	0x75, 0x28, 0x24, 0x17, 0x5c, 0x5b, 0x13, 0xaa, 0x62, 0x4d, 0x69, 0xca, 0x37, 0x9f, 0xd7, 0x17,
	0xcc, 0x3c, 0x47, 0x0d, 0x1b, 0xfd, 0x01, 0xd2, 0x20, 0xf2, 0x34, 0x31, 0xc3, 0x31, 0x00, 0xe9,
	0xa0, 0xd0, 0xee, 0xc0, 0x6f, 0xf7, 0xb1, 0xeb, 0x69, 0x52, 0x86, 0x9d, 0xc0, 0x48, 0x03, 0xb9,
	0x8f, 0x7d, 0xa2, 0xc9, 0x55, 0x61, 0x4c, 0x73, 0x84, 0x31, 0x36, 0x89, 0x3b, 0x5a, 0x2e, 0xcb,
	0x30, 0x04, 0xfd, 0x0d, 0x8b, 0x5d, 0xe2, 0x3a, 0x5d, 0xaa, 0x2d, 0x56, 0xc5, 0x5a, 0x29, 0xe5,
	0x52, 0x0c, 0xad, 0x41, 0xee, 0xda, 0xb5, 0x69, 0x57, 0xcb, 0x67, 0xc8, 0x04, 0x62, 0xc2, 0x8a,
	0xa7, 0xf8, 0x8a, 0x70, 0x71, 0x26, 0xb9, 0x44, 0xff, 0x40, 0x81, 0xe9, 0xb6, 0x22, 0x72, 0xc9,
	0xb5, 0xa9, 0x0d, 0xb5, 0xee, 0xe0, 0x7a, 0x13, 0xc7, 0xc4, 0x24, 0x97, 0x66, 0xbe, 0x9d, 0x1c,
	0xd0, 0x23, 0x90, 0xfd, 0xc0, 0x26, 0x5c, 0xe3, 0x52, 0xa3, 0xc2, 0x62, 0xb2, 0xef, 0x4c, 0x7e,
	0x1c, 0x06, 0x36, 0x19, 0x25, 0xcb, 0x6e, 0xb0, 0xe2, 0xf4, 0xc8, 0xf0, 0x8e, 0x7c, 0x06, 0xe8,
	0x2d, 0x28, 0xdd, 0xb9, 0x84, 0x54, 0xc8, 0x1b, 0x47, 0xe7, 0xbb, 0x2d, 0x63, 0xaf, 0xbc, 0x80,
	0xfe, 0x84, 0x95, 0xd3, 0xdd, 0xf3, 0x7d, 0x6b, 0xff, 0xf0, 0xf8, 0x99, 0x61, 0x9d, 0x1d, 0x27,
	0x87, 0xb2, 0x80, 0x56, 0xe1, 0x37, 0x4e, 0x18, 0x87, 0x07, 0x13, 0x58, 0xd4, 0x5f, 0x66, 0x5e,
	0x33, 0x49, 0x1c, 0xa2, 0x7f, 0x41, 0x49, 0x85, 0xc5, 0x61, 0xaa, 0xac, 0x38, 0x51, 0x16, 0x87,
	0x66, 0xa1, 0x9d, 0x9e, 0xd0, 0x06, 0xc8, 0x2e, 0x25, 0x3e, 0xd7, 0xa6, 0x36, 0x4a, 0x2c, 0x6a,
	0xdc, 0x7c, 0x93, 0x53, 0xfa, 0x1b, 0x58, 0xda, 0x23, 0x1e, 0xa1, 0xf3, 0x17, 0x6e, 0x13, 0x20,
	0xc4, 0x9d, 0x1e, 0x76, 0x08, 0xb3, 0x4f, 0xd6, 0x22, 0x4a, 0x8a, 0x1b, 0x36, 0xfa, 0x0b, 0x94,
	0x91, 0xc3, 0x62, 0x4d, 0xaa, 0x4a, 0x35, 0xc5, 0x2c, 0xa4, 0xe6, 0x8a, 0xf5, 0xc7, 0xb0, 0x7c,
	0xe7, 0xbf, 0xe7, 0x12, 0xa7, 0x7f, 0x12, 0x00, 0x1d, 0x10, 0xca, 0xef, 0x9e, 0xf4, 0x9c, 0x96,
	0x1b, 0xd3, 0x79, 0xd2, 0xdf, 0x02, 0x35, 0xa6, 0x38, 0xa2, 0x96, 0xdb, 0xb7, 0xc9, 0x6b, 0x4d,
	0xac, 0x0a, 0xb5, 0x5c, 0x9a, 0x3f, 0x70, 0xc2, 0x60, 0x38, 0xf3, 0x5c, 0x27, 0x18, 0xf4, 0xa9,
	0x26, 0x65, 0x02, 0x12, 0x08, 0x6d, 0xc3, 0xb2, 0x17, 0x60, 0xdb, 0xba, 0x70, 0xa3, 0x98, 0x5a,
	0xbc, 0xd2, 0x72, 0x26, 0xaa, 0xc4, 0xc8, 0xa7, 0x8c, 0xe3, 0xc3, 0x56, 0x87, 0xf2, 0x24, 0xd0,
	0xf2, 0x5c, 0xdf, 0xa5, 0x7c, 0x02, 0x46, 0xe1, 0x4b, 0x17, 0xa3, 0xd0, 0x16, 0xe3, 0xf4, 0x77,
	0x02, 0xac, 0xdc, 0xd3, 0x37, 0x5f, 0xff, 0xff, 0x83, 0x42, 0xd8, 0x73, 0x2c, 0xcf, 0x8d, 0xa9,
	0x26, 0x56, 0xa5, 0x9a, 0xda, 0x28, 0x8f, 0x3d, 0x70, 0x92, 0xf4, 0xc8, 0xcc, 0x87, 0xc9, 0xdb,
	0x68, 0x1b, 0x20, 0x23, 0x44, 0xe2, 0xe1, 0x53, 0x96, 0x51, 0xc6, 0x29, 0xea, 0xdf, 0x05, 0x28,
	0x66, 0xdf, 0x99, 0xb2, 0x83, 0x30, 0xdb, 0x0e, 0x5b, 0xa0, 0x0e, 0x42, 0x1b, 0x53, 0x62, 0x51,
	0xd7, 0x4f, 0x66, 0x4e, 0x1e, 0x15, 0x3d, 0x21, 0xce, 0xdc, 0x64, 0x41, 0xf0, 0xd5, 0x21, 0xdd,
	0x5b, 0x1d, 0x5b, 0xa0, 0xd2, 0x80, 0x62, 0xcf, 0x4a, 0x9a, 0x22, 0x57, 0xc5, 0x49, 0xd7, 0x38,
	0xf1, 0x84, 0x77, 0x66, 0x03, 0x94, 0x4e, 0x70, 0x45, 0x22, 0x8b, 0x6d, 0xaf, 0x5c, 0x26, 0x97,
	0x02, 0x87, 0x9f, 0x47, 0x1e, 0xdb, 0x7d, 0xc1, 0x75, 0x9f, 0x44, 0x2c, 0xdb, 0xec, 0xb2, 0xc9,
	0x73, 0xd4, 0xb0, 0x59, 0x12, 0x74, 0x18, 0x12, 0x2d, 0x9f, 0x4d, 0x82, 0x21, 0xfa, 0x7b, 0x01,
	0x7e, 0x1f, 0x75, 0x86, 0x95, 0xae, 0x39, 0x3c, 0xe9, 0x39, 0xbf, 0x7c, 0x74, 0xa6, 0x0c, 0x2a,
	0x3d, 0x64, 0x50, 0xf9, 0xbe, 0x41, 0xa7, 0xca, 0xcd, 0xdc, 0x36, 0xa3, 0xdc, 0xfa, 0x37, 0x01,
	0x56, 0x67, 0xe8, 0x99, 0xcf, 0x6b, 0x3f, 0xd9, 0xda, 0x4d, 0xc8, 0x31, 0x7f, 0xc5, 0xb3, 0x0d,
	0x96, 0x70, 0x53, 0xf5, 0x91, 0x67, 0xd7, 0x67, 0x64, 0x92, 0xdc, 0x43, 0x26, 0x59, 0x9c, 0x6d,
	0x92, 0xe6, 0xd1, 0xcd, 0x6d, 0x45, 0xf8, 0x70, 0x5b, 0x11, 0xbe, 0xdc, 0x56, 0x84, 0xb7, 0x5f,
	0x2b, 0x0b, 0xa0, 0x75, 0x02, 0xbf, 0x3e, 0x74, 0x87, 0xc1, 0x80, 0xa5, 0xc5, 0x96, 0xbc, 0x97,
	0x7c, 0x37, 0x5f, 0xe8, 0x4e, 0xe0, 0xe1, 0xbe, 0x53, 0xff, 0xbf, 0x41, 0x69, 0xbd, 0x13, 0xf8,
	0x3b, 0x1c, 0xee, 0x04, 0xde, 0x0e, 0x0e, 0xc3, 0x1d, 0xbe, 0xd1, 0x7e, 0x04, 0x00, 0x00, 0xff,
	0xff, 0xcc, 0xd1, 0x0f, 0x53, 0x7d, 0x07, 0x00, 0x00,
}
