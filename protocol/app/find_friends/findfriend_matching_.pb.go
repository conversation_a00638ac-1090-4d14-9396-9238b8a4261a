// Code generated by protoc-gen-gogo.
// source: findfriend_matching_.proto
// DO NOT EDIT!

/*
	Package find_friends is a generated protocol buffer package.

	It is generated from these files:
		findfriend_matching_.proto

	It has these top-level messages:
		CheckUserFinishFindFriendExamReq
		CheckUserFinishFindFriendExamResp
		StFindFriendExamEntrance
		GetFindFriendExamEntranceReq
		GetFindFriendExamEntranceResp
		UploadExamInstallRecommendGameReq
		UploadExamInstallRecommendGameResp
		FindfriendExamQuestion
		GetFindFriendExamQuestionReq
		GetFindFriendExamQuestionResp
		AddUserFindFriendExamReq
		AddUserFindFriendExamResp
		StUserExamResult
		GetUserFindFriendExamResultReq
		GetUserFindFriendExamResultResp
		FindFriendMatchInfo
		MatchFriendReq
		MatchFriendResp
*/
package find_friends

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type FindFriendMatchInfo_FindFriendClassify int32

const (
	FindFriendMatchInfo_entertainment FindFriendMatchInfo_FindFriendClassify = 0
	FindFriendMatchInfo_game          FindFriendMatchInfo_FindFriendClassify = 1
)

var FindFriendMatchInfo_FindFriendClassify_name = map[int32]string{
	0: "entertainment",
	1: "game",
}
var FindFriendMatchInfo_FindFriendClassify_value = map[string]int32{
	"entertainment": 0,
	"game":          1,
}

func (x FindFriendMatchInfo_FindFriendClassify) Enum() *FindFriendMatchInfo_FindFriendClassify {
	p := new(FindFriendMatchInfo_FindFriendClassify)
	*p = x
	return p
}
func (x FindFriendMatchInfo_FindFriendClassify) String() string {
	return proto.EnumName(FindFriendMatchInfo_FindFriendClassify_name, int32(x))
}
func (x *FindFriendMatchInfo_FindFriendClassify) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FindFriendMatchInfo_FindFriendClassify_value, data, "FindFriendMatchInfo_FindFriendClassify")
	if err != nil {
		return err
	}
	*x = FindFriendMatchInfo_FindFriendClassify(value)
	return nil
}
func (FindFriendMatchInfo_FindFriendClassify) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{15, 0}
}

// 检查用户是否完成了测试
type CheckUserFinishFindFriendExamReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *CheckUserFinishFindFriendExamReq) Reset()         { *m = CheckUserFinishFindFriendExamReq{} }
func (m *CheckUserFinishFindFriendExamReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserFinishFindFriendExamReq) ProtoMessage()    {}
func (*CheckUserFinishFindFriendExamReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{0}
}

func (m *CheckUserFinishFindFriendExamReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type CheckUserFinishFindFriendExamResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	IsFinish bool         `protobuf:"varint,2,req,name=is_finish,json=isFinish" json:"is_finish"`
	IsInit   bool         `protobuf:"varint,3,req,name=is_init,json=isInit" json:"is_init"`
}

func (m *CheckUserFinishFindFriendExamResp) Reset()         { *m = CheckUserFinishFindFriendExamResp{} }
func (m *CheckUserFinishFindFriendExamResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserFinishFindFriendExamResp) ProtoMessage()    {}
func (*CheckUserFinishFindFriendExamResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{1}
}

func (m *CheckUserFinishFindFriendExamResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckUserFinishFindFriendExamResp) GetIsFinish() bool {
	if m != nil {
		return m.IsFinish
	}
	return false
}

func (m *CheckUserFinishFindFriendExamResp) GetIsInit() bool {
	if m != nil {
		return m.IsInit
	}
	return false
}

// 获取入口标签
type StFindFriendExamEntrance struct {
	ExamId  uint32 `protobuf:"varint,1,req,name=exam_id,json=examId" json:"exam_id"`
	Name    string `protobuf:"bytes,2,opt,name=name" json:"name"`
	IconUrl string `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl" json:"icon_url"`
}

func (m *StFindFriendExamEntrance) Reset()         { *m = StFindFriendExamEntrance{} }
func (m *StFindFriendExamEntrance) String() string { return proto.CompactTextString(m) }
func (*StFindFriendExamEntrance) ProtoMessage()    {}
func (*StFindFriendExamEntrance) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{2}
}

func (m *StFindFriendExamEntrance) GetExamId() uint32 {
	if m != nil {
		return m.ExamId
	}
	return 0
}

func (m *StFindFriendExamEntrance) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StFindFriendExamEntrance) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

type GetFindFriendExamEntranceReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetFindFriendExamEntranceReq) Reset()         { *m = GetFindFriendExamEntranceReq{} }
func (m *GetFindFriendExamEntranceReq) String() string { return proto.CompactTextString(m) }
func (*GetFindFriendExamEntranceReq) ProtoMessage()    {}
func (*GetFindFriendExamEntranceReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{3}
}

func (m *GetFindFriendExamEntranceReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetFindFriendExamEntranceResp struct {
	BaseResp     *ga.BaseResp                `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	EntranceList []*StFindFriendExamEntrance `protobuf:"bytes,2,rep,name=entrance_list,json=entranceList" json:"entrance_list,omitempty"`
}

func (m *GetFindFriendExamEntranceResp) Reset()         { *m = GetFindFriendExamEntranceResp{} }
func (m *GetFindFriendExamEntranceResp) String() string { return proto.CompactTextString(m) }
func (*GetFindFriendExamEntranceResp) ProtoMessage()    {}
func (*GetFindFriendExamEntranceResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{4}
}

func (m *GetFindFriendExamEntranceResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFindFriendExamEntranceResp) GetEntranceList() []*StFindFriendExamEntrance {
	if m != nil {
		return m.EntranceList
	}
	return nil
}

// 上传测试用户推荐游戏(已安装的)
type UploadExamInstallRecommendGameReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GameIdList []uint32    `protobuf:"varint,2,rep,name=game_id_list,json=gameIdList" json:"game_id_list,omitempty"`
}

func (m *UploadExamInstallRecommendGameReq) Reset()         { *m = UploadExamInstallRecommendGameReq{} }
func (m *UploadExamInstallRecommendGameReq) String() string { return proto.CompactTextString(m) }
func (*UploadExamInstallRecommendGameReq) ProtoMessage()    {}
func (*UploadExamInstallRecommendGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{5}
}

func (m *UploadExamInstallRecommendGameReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UploadExamInstallRecommendGameReq) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

type UploadExamInstallRecommendGameResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *UploadExamInstallRecommendGameResp) Reset()         { *m = UploadExamInstallRecommendGameResp{} }
func (m *UploadExamInstallRecommendGameResp) String() string { return proto.CompactTextString(m) }
func (*UploadExamInstallRecommendGameResp) ProtoMessage()    {}
func (*UploadExamInstallRecommendGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{6}
}

func (m *UploadExamInstallRecommendGameResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取测试题目
type FindfriendExamQuestion struct {
	QuestionType uint32   `protobuf:"varint,1,req,name=question_type,json=questionType" json:"question_type"`
	Question     string   `protobuf:"bytes,2,req,name=question" json:"question"`
	Options      []string `protobuf:"bytes,3,rep,name=options" json:"options,omitempty"`
}

func (m *FindfriendExamQuestion) Reset()         { *m = FindfriendExamQuestion{} }
func (m *FindfriendExamQuestion) String() string { return proto.CompactTextString(m) }
func (*FindfriendExamQuestion) ProtoMessage()    {}
func (*FindfriendExamQuestion) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{7}
}

func (m *FindfriendExamQuestion) GetQuestionType() uint32 {
	if m != nil {
		return m.QuestionType
	}
	return 0
}

func (m *FindfriendExamQuestion) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *FindfriendExamQuestion) GetOptions() []string {
	if m != nil {
		return m.Options
	}
	return nil
}

type GetFindFriendExamQuestionReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ExamId  uint32      `protobuf:"varint,2,req,name=exam_id,json=examId" json:"exam_id"`
}

func (m *GetFindFriendExamQuestionReq) Reset()         { *m = GetFindFriendExamQuestionReq{} }
func (m *GetFindFriendExamQuestionReq) String() string { return proto.CompactTextString(m) }
func (*GetFindFriendExamQuestionReq) ProtoMessage()    {}
func (*GetFindFriendExamQuestionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{8}
}

func (m *GetFindFriendExamQuestionReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFindFriendExamQuestionReq) GetExamId() uint32 {
	if m != nil {
		return m.ExamId
	}
	return 0
}

type GetFindFriendExamQuestionResp struct {
	BaseResp     *ga.BaseResp              `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	QuestionList []*FindfriendExamQuestion `protobuf:"bytes,2,rep,name=question_list,json=questionList" json:"question_list,omitempty"`
}

func (m *GetFindFriendExamQuestionResp) Reset()         { *m = GetFindFriendExamQuestionResp{} }
func (m *GetFindFriendExamQuestionResp) String() string { return proto.CompactTextString(m) }
func (*GetFindFriendExamQuestionResp) ProtoMessage()    {}
func (*GetFindFriendExamQuestionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{9}
}

func (m *GetFindFriendExamQuestionResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFindFriendExamQuestionResp) GetQuestionList() []*FindfriendExamQuestion {
	if m != nil {
		return m.QuestionList
	}
	return nil
}

// 用户测试
type AddUserFindFriendExamReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ExamId       uint32      `protobuf:"varint,2,req,name=exam_id,json=examId" json:"exam_id"`
	AnswerIdList []uint32    `protobuf:"varint,3,rep,name=answer_id_list,json=answerIdList" json:"answer_id_list,omitempty"`
	IsFinish     bool        `protobuf:"varint,4,req,name=is_finish,json=isFinish" json:"is_finish"`
}

func (m *AddUserFindFriendExamReq) Reset()         { *m = AddUserFindFriendExamReq{} }
func (m *AddUserFindFriendExamReq) String() string { return proto.CompactTextString(m) }
func (*AddUserFindFriendExamReq) ProtoMessage()    {}
func (*AddUserFindFriendExamReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{10}
}

func (m *AddUserFindFriendExamReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AddUserFindFriendExamReq) GetExamId() uint32 {
	if m != nil {
		return m.ExamId
	}
	return 0
}

func (m *AddUserFindFriendExamReq) GetAnswerIdList() []uint32 {
	if m != nil {
		return m.AnswerIdList
	}
	return nil
}

func (m *AddUserFindFriendExamReq) GetIsFinish() bool {
	if m != nil {
		return m.IsFinish
	}
	return false
}

type AddUserFindFriendExamResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *AddUserFindFriendExamResp) Reset()         { *m = AddUserFindFriendExamResp{} }
func (m *AddUserFindFriendExamResp) String() string { return proto.CompactTextString(m) }
func (*AddUserFindFriendExamResp) ProtoMessage()    {}
func (*AddUserFindFriendExamResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{11}
}

func (m *AddUserFindFriendExamResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取用户测试评价结果
type StUserExamResult struct {
	Uid                  uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account              string `protobuf:"bytes,2,req,name=account" json:"account"`
	Nickname             string `protobuf:"bytes,3,req,name=nickname" json:"nickname"`
	ResultUrl            string `protobuf:"bytes,4,req,name=result_url,json=resultUrl" json:"result_url"`
	ResultDesc           string `protobuf:"bytes,5,opt,name=result_desc,json=resultDesc" json:"result_desc"`
	ResultExamName       string `protobuf:"bytes,6,opt,name=result_exam_name,json=resultExamName" json:"result_exam_name"`
	ResultTagName        string `protobuf:"bytes,7,opt,name=result_tag_name,json=resultTagName" json:"result_tag_name"`
	RelayCircleDefautMsg string `protobuf:"bytes,8,opt,name=relay_circle_defaut_msg,json=relayCircleDefautMsg" json:"relay_circle_defaut_msg"`
}

func (m *StUserExamResult) Reset()         { *m = StUserExamResult{} }
func (m *StUserExamResult) String() string { return proto.CompactTextString(m) }
func (*StUserExamResult) ProtoMessage()    {}
func (*StUserExamResult) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{12}
}

func (m *StUserExamResult) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StUserExamResult) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *StUserExamResult) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *StUserExamResult) GetResultUrl() string {
	if m != nil {
		return m.ResultUrl
	}
	return ""
}

func (m *StUserExamResult) GetResultDesc() string {
	if m != nil {
		return m.ResultDesc
	}
	return ""
}

func (m *StUserExamResult) GetResultExamName() string {
	if m != nil {
		return m.ResultExamName
	}
	return ""
}

func (m *StUserExamResult) GetResultTagName() string {
	if m != nil {
		return m.ResultTagName
	}
	return ""
}

func (m *StUserExamResult) GetRelayCircleDefautMsg() string {
	if m != nil {
		return m.RelayCircleDefautMsg
	}
	return ""
}

type GetUserFindFriendExamResultReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetUserFindFriendExamResultReq) Reset()         { *m = GetUserFindFriendExamResultReq{} }
func (m *GetUserFindFriendExamResultReq) String() string { return proto.CompactTextString(m) }
func (*GetUserFindFriendExamResultReq) ProtoMessage()    {}
func (*GetUserFindFriendExamResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{13}
}

func (m *GetUserFindFriendExamResultReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserFindFriendExamResultResp struct {
	BaseResp   *ga.BaseResp      `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ExamResult *StUserExamResult `protobuf:"bytes,2,req,name=exam_result,json=examResult" json:"exam_result,omitempty"`
}

func (m *GetUserFindFriendExamResultResp) Reset()         { *m = GetUserFindFriendExamResultResp{} }
func (m *GetUserFindFriendExamResultResp) String() string { return proto.CompactTextString(m) }
func (*GetUserFindFriendExamResultResp) ProtoMessage()    {}
func (*GetUserFindFriendExamResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{14}
}

func (m *GetUserFindFriendExamResultResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserFindFriendExamResultResp) GetExamResult() *StUserExamResult {
	if m != nil {
		return m.ExamResult
	}
	return nil
}

type FindFriendMatchInfo struct {
	Uid               uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	ExamName          string   `protobuf:"bytes,2,req,name=exam_name,json=examName" json:"exam_name"`
	TagName           string   `protobuf:"bytes,3,req,name=tag_name,json=tagName" json:"tag_name"`
	Account           string   `protobuf:"bytes,4,req,name=account" json:"account"`
	Nickname          string   `protobuf:"bytes,5,req,name=nickname" json:"nickname"`
	Sex               uint32   `protobuf:"varint,6,req,name=sex" json:"sex"`
	QuestionTypeNames []string `protobuf:"bytes,7,rep,name=question_type_names,json=questionTypeNames" json:"question_type_names,omitempty"`
	ScoreList         []uint32 `protobuf:"varint,8,rep,name=score_list,json=scoreList" json:"score_list,omitempty"`
	Desc              string   `protobuf:"bytes,9,req,name=desc" json:"desc"`
	Classify          uint32   `protobuf:"varint,10,req,name=classify" json:"classify"`
}

func (m *FindFriendMatchInfo) Reset()         { *m = FindFriendMatchInfo{} }
func (m *FindFriendMatchInfo) String() string { return proto.CompactTextString(m) }
func (*FindFriendMatchInfo) ProtoMessage()    {}
func (*FindFriendMatchInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{15}
}

func (m *FindFriendMatchInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FindFriendMatchInfo) GetExamName() string {
	if m != nil {
		return m.ExamName
	}
	return ""
}

func (m *FindFriendMatchInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *FindFriendMatchInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *FindFriendMatchInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *FindFriendMatchInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *FindFriendMatchInfo) GetQuestionTypeNames() []string {
	if m != nil {
		return m.QuestionTypeNames
	}
	return nil
}

func (m *FindFriendMatchInfo) GetScoreList() []uint32 {
	if m != nil {
		return m.ScoreList
	}
	return nil
}

func (m *FindFriendMatchInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *FindFriendMatchInfo) GetClassify() uint32 {
	if m != nil {
		return m.Classify
	}
	return 0
}

type MatchFriendReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uid     uint32      `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *MatchFriendReq) Reset()         { *m = MatchFriendReq{} }
func (m *MatchFriendReq) String() string { return proto.CompactTextString(m) }
func (*MatchFriendReq) ProtoMessage()    {}
func (*MatchFriendReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{16}
}

func (m *MatchFriendReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MatchFriendReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type MatchFriendResp struct {
	BaseResp        *ga.BaseResp         `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	UserInfo        *FindFriendMatchInfo `protobuf:"bytes,2,req,name=user_info,json=userInfo" json:"user_info,omitempty"`
	MatchedUserInfo *FindFriendMatchInfo `protobuf:"bytes,3,req,name=matched_user_info,json=matchedUserInfo" json:"matched_user_info,omitempty"`
	MatchPercent    uint32               `protobuf:"varint,4,req,name=match_percent,json=matchPercent" json:"match_percent"`
}

func (m *MatchFriendResp) Reset()         { *m = MatchFriendResp{} }
func (m *MatchFriendResp) String() string { return proto.CompactTextString(m) }
func (*MatchFriendResp) ProtoMessage()    {}
func (*MatchFriendResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindfriendMatching_, []int{17}
}

func (m *MatchFriendResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MatchFriendResp) GetUserInfo() *FindFriendMatchInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *MatchFriendResp) GetMatchedUserInfo() *FindFriendMatchInfo {
	if m != nil {
		return m.MatchedUserInfo
	}
	return nil
}

func (m *MatchFriendResp) GetMatchPercent() uint32 {
	if m != nil {
		return m.MatchPercent
	}
	return 0
}

func init() {
	proto.RegisterType((*CheckUserFinishFindFriendExamReq)(nil), "ga.CheckUserFinishFindFriendExamReq")
	proto.RegisterType((*CheckUserFinishFindFriendExamResp)(nil), "ga.CheckUserFinishFindFriendExamResp")
	proto.RegisterType((*StFindFriendExamEntrance)(nil), "ga.StFindFriendExamEntrance")
	proto.RegisterType((*GetFindFriendExamEntranceReq)(nil), "ga.GetFindFriendExamEntranceReq")
	proto.RegisterType((*GetFindFriendExamEntranceResp)(nil), "ga.GetFindFriendExamEntranceResp")
	proto.RegisterType((*UploadExamInstallRecommendGameReq)(nil), "ga.UploadExamInstallRecommendGameReq")
	proto.RegisterType((*UploadExamInstallRecommendGameResp)(nil), "ga.UploadExamInstallRecommendGameResp")
	proto.RegisterType((*FindfriendExamQuestion)(nil), "ga.FindfriendExamQuestion")
	proto.RegisterType((*GetFindFriendExamQuestionReq)(nil), "ga.GetFindFriendExamQuestionReq")
	proto.RegisterType((*GetFindFriendExamQuestionResp)(nil), "ga.GetFindFriendExamQuestionResp")
	proto.RegisterType((*AddUserFindFriendExamReq)(nil), "ga.AddUserFindFriendExamReq")
	proto.RegisterType((*AddUserFindFriendExamResp)(nil), "ga.AddUserFindFriendExamResp")
	proto.RegisterType((*StUserExamResult)(nil), "ga.StUserExamResult")
	proto.RegisterType((*GetUserFindFriendExamResultReq)(nil), "ga.GetUserFindFriendExamResultReq")
	proto.RegisterType((*GetUserFindFriendExamResultResp)(nil), "ga.GetUserFindFriendExamResultResp")
	proto.RegisterType((*FindFriendMatchInfo)(nil), "ga.FindFriendMatchInfo")
	proto.RegisterType((*MatchFriendReq)(nil), "ga.MatchFriendReq")
	proto.RegisterType((*MatchFriendResp)(nil), "ga.MatchFriendResp")
	proto.RegisterEnum("ga.FindFriendMatchInfo_FindFriendClassify", FindFriendMatchInfo_FindFriendClassify_name, FindFriendMatchInfo_FindFriendClassify_value)
}
func (m *CheckUserFinishFindFriendExamReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserFinishFindFriendExamReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *CheckUserFinishFindFriendExamResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserFinishFindFriendExamResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	if m.IsFinish {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	if m.IsInit {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *StFindFriendExamEntrance) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StFindFriendExamEntrance) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.ExamId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.IconUrl)))
	i += copy(dAtA[i:], m.IconUrl)
	return i, nil
}

func (m *GetFindFriendExamEntranceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFindFriendExamEntranceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.BaseReq.Size()))
		n3, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *GetFindFriendExamEntranceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFindFriendExamEntranceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.BaseResp.Size()))
		n4, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if len(m.EntranceList) > 0 {
		for _, msg := range m.EntranceList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintFindfriendMatching_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UploadExamInstallRecommendGameReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UploadExamInstallRecommendGameReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if len(m.GameIdList) > 0 {
		for _, num := range m.GameIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintFindfriendMatching_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *UploadExamInstallRecommendGameResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UploadExamInstallRecommendGameResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *FindfriendExamQuestion) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindfriendExamQuestion) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.QuestionType))
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.Question)))
	i += copy(dAtA[i:], m.Question)
	if len(m.Options) > 0 {
		for _, s := range m.Options {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *GetFindFriendExamQuestionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFindFriendExamQuestionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.BaseReq.Size()))
		n7, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.ExamId))
	return i, nil
}

func (m *GetFindFriendExamQuestionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFindFriendExamQuestionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.BaseResp.Size()))
		n8, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	if len(m.QuestionList) > 0 {
		for _, msg := range m.QuestionList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintFindfriendMatching_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddUserFindFriendExamReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserFindFriendExamReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.ExamId))
	if len(m.AnswerIdList) > 0 {
		for _, num := range m.AnswerIdList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintFindfriendMatching_(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x20
	i++
	if m.IsFinish {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *AddUserFindFriendExamResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserFindFriendExamResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.BaseResp.Size()))
		n10, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	return i, nil
}

func (m *StUserExamResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StUserExamResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x22
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.ResultUrl)))
	i += copy(dAtA[i:], m.ResultUrl)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.ResultDesc)))
	i += copy(dAtA[i:], m.ResultDesc)
	dAtA[i] = 0x32
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.ResultExamName)))
	i += copy(dAtA[i:], m.ResultExamName)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.ResultTagName)))
	i += copy(dAtA[i:], m.ResultTagName)
	dAtA[i] = 0x42
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.RelayCircleDefautMsg)))
	i += copy(dAtA[i:], m.RelayCircleDefautMsg)
	return i, nil
}

func (m *GetUserFindFriendExamResultReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserFindFriendExamResultReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.BaseReq.Size()))
		n11, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	return i, nil
}

func (m *GetUserFindFriendExamResultResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserFindFriendExamResultResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.BaseResp.Size()))
		n12, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	if m.ExamResult == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("exam_result")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.ExamResult.Size()))
		n13, err := m.ExamResult.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	return i, nil
}

func (m *FindFriendMatchInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendMatchInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.ExamName)))
	i += copy(dAtA[i:], m.ExamName)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.TagName)))
	i += copy(dAtA[i:], m.TagName)
	dAtA[i] = 0x22
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x30
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.Sex))
	if len(m.QuestionTypeNames) > 0 {
		for _, s := range m.QuestionTypeNames {
			dAtA[i] = 0x3a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.ScoreList) > 0 {
		for _, num := range m.ScoreList {
			dAtA[i] = 0x40
			i++
			i = encodeVarintFindfriendMatching_(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x4a
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(len(m.Desc)))
	i += copy(dAtA[i:], m.Desc)
	dAtA[i] = 0x50
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.Classify))
	return i, nil
}

func (m *MatchFriendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatchFriendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.BaseReq.Size()))
		n14, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *MatchFriendResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatchFriendResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.BaseResp.Size()))
		n15, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	if m.UserInfo == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.UserInfo.Size()))
		n16, err := m.UserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	if m.MatchedUserInfo == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("matched_user_info")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.MatchedUserInfo.Size()))
		n17, err := m.MatchedUserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintFindfriendMatching_(dAtA, i, uint64(m.MatchPercent))
	return i, nil
}

func encodeFixed64FindfriendMatching_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32FindfriendMatching_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintFindfriendMatching_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *CheckUserFinishFindFriendExamReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	return n
}

func (m *CheckUserFinishFindFriendExamResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	n += 2
	n += 2
	return n
}

func (m *StFindFriendExamEntrance) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFindfriendMatching_(uint64(m.ExamId))
	l = len(m.Name)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	l = len(m.IconUrl)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	return n
}

func (m *GetFindFriendExamEntranceReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	return n
}

func (m *GetFindFriendExamEntranceResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	if len(m.EntranceList) > 0 {
		for _, e := range m.EntranceList {
			l = e.Size()
			n += 1 + l + sovFindfriendMatching_(uint64(l))
		}
	}
	return n
}

func (m *UploadExamInstallRecommendGameReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	if len(m.GameIdList) > 0 {
		for _, e := range m.GameIdList {
			n += 1 + sovFindfriendMatching_(uint64(e))
		}
	}
	return n
}

func (m *UploadExamInstallRecommendGameResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	return n
}

func (m *FindfriendExamQuestion) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFindfriendMatching_(uint64(m.QuestionType))
	l = len(m.Question)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	if len(m.Options) > 0 {
		for _, s := range m.Options {
			l = len(s)
			n += 1 + l + sovFindfriendMatching_(uint64(l))
		}
	}
	return n
}

func (m *GetFindFriendExamQuestionReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	n += 1 + sovFindfriendMatching_(uint64(m.ExamId))
	return n
}

func (m *GetFindFriendExamQuestionResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	if len(m.QuestionList) > 0 {
		for _, e := range m.QuestionList {
			l = e.Size()
			n += 1 + l + sovFindfriendMatching_(uint64(l))
		}
	}
	return n
}

func (m *AddUserFindFriendExamReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	n += 1 + sovFindfriendMatching_(uint64(m.ExamId))
	if len(m.AnswerIdList) > 0 {
		for _, e := range m.AnswerIdList {
			n += 1 + sovFindfriendMatching_(uint64(e))
		}
	}
	n += 2
	return n
}

func (m *AddUserFindFriendExamResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	return n
}

func (m *StUserExamResult) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFindfriendMatching_(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	l = len(m.ResultUrl)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	l = len(m.ResultDesc)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	l = len(m.ResultExamName)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	l = len(m.ResultTagName)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	l = len(m.RelayCircleDefautMsg)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	return n
}

func (m *GetUserFindFriendExamResultReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	return n
}

func (m *GetUserFindFriendExamResultResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	if m.ExamResult != nil {
		l = m.ExamResult.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	return n
}

func (m *FindFriendMatchInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFindfriendMatching_(uint64(m.Uid))
	l = len(m.ExamName)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	l = len(m.TagName)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	n += 1 + sovFindfriendMatching_(uint64(m.Sex))
	if len(m.QuestionTypeNames) > 0 {
		for _, s := range m.QuestionTypeNames {
			l = len(s)
			n += 1 + l + sovFindfriendMatching_(uint64(l))
		}
	}
	if len(m.ScoreList) > 0 {
		for _, e := range m.ScoreList {
			n += 1 + sovFindfriendMatching_(uint64(e))
		}
	}
	l = len(m.Desc)
	n += 1 + l + sovFindfriendMatching_(uint64(l))
	n += 1 + sovFindfriendMatching_(uint64(m.Classify))
	return n
}

func (m *MatchFriendReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	n += 1 + sovFindfriendMatching_(uint64(m.Uid))
	return n
}

func (m *MatchFriendResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	if m.UserInfo != nil {
		l = m.UserInfo.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	if m.MatchedUserInfo != nil {
		l = m.MatchedUserInfo.Size()
		n += 1 + l + sovFindfriendMatching_(uint64(l))
	}
	n += 1 + sovFindfriendMatching_(uint64(m.MatchPercent))
	return n
}

func sovFindfriendMatching_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozFindfriendMatching_(x uint64) (n int) {
	return sovFindfriendMatching_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *CheckUserFinishFindFriendExamReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserFinishFindFriendExamReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserFinishFindFriendExamReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserFinishFindFriendExamResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserFinishFindFriendExamResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserFinishFindFriendExamResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsFinish", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsFinish = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsInit", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsInit = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_finish")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_init")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StFindFriendExamEntrance) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StFindFriendExamEntrance: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StFindFriendExamEntrance: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamId", wireType)
			}
			m.ExamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("exam_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFindFriendExamEntranceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFindFriendExamEntranceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFindFriendExamEntranceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFindFriendExamEntranceResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFindFriendExamEntranceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFindFriendExamEntranceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntranceList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntranceList = append(m.EntranceList, &StFindFriendExamEntrance{})
			if err := m.EntranceList[len(m.EntranceList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UploadExamInstallRecommendGameReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UploadExamInstallRecommendGameReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UploadExamInstallRecommendGameReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFindfriendMatching_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameIdList = append(m.GameIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFindfriendMatching_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFindfriendMatching_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFindfriendMatching_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameIdList = append(m.GameIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UploadExamInstallRecommendGameResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UploadExamInstallRecommendGameResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UploadExamInstallRecommendGameResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindfriendExamQuestion) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindfriendExamQuestion: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindfriendExamQuestion: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionType", wireType)
			}
			m.QuestionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuestionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Question", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Question = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Options", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Options = append(m.Options, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("question_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("question")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFindFriendExamQuestionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFindFriendExamQuestionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFindFriendExamQuestionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamId", wireType)
			}
			m.ExamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("exam_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFindFriendExamQuestionResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFindFriendExamQuestionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFindFriendExamQuestionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.QuestionList = append(m.QuestionList, &FindfriendExamQuestion{})
			if err := m.QuestionList[len(m.QuestionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserFindFriendExamReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserFindFriendExamReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserFindFriendExamReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamId", wireType)
			}
			m.ExamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFindfriendMatching_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.AnswerIdList = append(m.AnswerIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFindfriendMatching_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFindfriendMatching_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFindfriendMatching_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.AnswerIdList = append(m.AnswerIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field AnswerIdList", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsFinish", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsFinish = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("exam_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_finish")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserFindFriendExamResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserFindFriendExamResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserFindFriendExamResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StUserExamResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StUserExamResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StUserExamResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultExamName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultExamName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultTagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultTagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RelayCircleDefautMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RelayCircleDefautMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nickname")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("result_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserFindFriendExamResultReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserFindFriendExamResultReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserFindFriendExamResultReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserFindFriendExamResultResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserFindFriendExamResultResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserFindFriendExamResultResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamResult", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ExamResult == nil {
				m.ExamResult = &StUserExamResult{}
			}
			if err := m.ExamResult.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("exam_result")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendMatchInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendMatchInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendMatchInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExamName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExamName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionTypeNames", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.QuestionTypeNames = append(m.QuestionTypeNames, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 8:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFindfriendMatching_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ScoreList = append(m.ScoreList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFindfriendMatching_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFindfriendMatching_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFindfriendMatching_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ScoreList = append(m.ScoreList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScoreList", wireType)
			}
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Classify", wireType)
			}
			m.Classify = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Classify |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("exam_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("tag_name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nickname")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("sex")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("desc")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("classify")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatchFriendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MatchFriendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MatchFriendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatchFriendResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MatchFriendResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MatchFriendResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserInfo == nil {
				m.UserInfo = &FindFriendMatchInfo{}
			}
			if err := m.UserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchedUserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MatchedUserInfo == nil {
				m.MatchedUserInfo = &FindFriendMatchInfo{}
			}
			if err := m.MatchedUserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchPercent", wireType)
			}
			m.MatchPercent = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchPercent |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipFindfriendMatching_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindfriendMatching_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("user_info")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("matched_user_info")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("match_percent")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipFindfriendMatching_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowFindfriendMatching_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFindfriendMatching_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthFindfriendMatching_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowFindfriendMatching_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipFindfriendMatching_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthFindfriendMatching_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowFindfriendMatching_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("findfriend_matching_.proto", fileDescriptorFindfriendMatching_) }

var fileDescriptorFindfriendMatching_ = []byte{
	// 1013 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x56, 0x4d, 0x6f, 0xdb, 0x36,
	0x18, 0x8e, 0x2c, 0x37, 0x96, 0x5f, 0xdb, 0xf9, 0x60, 0x8b, 0x54, 0x0b, 0x1a, 0xc7, 0xd1, 0x3e,
	0xe0, 0x00, 0x83, 0x83, 0x05, 0xeb, 0x69, 0x87, 0xa1, 0x49, 0xeb, 0xcc, 0xc3, 0xba, 0x75, 0x6a,
	0x7d, 0xd9, 0x45, 0x60, 0x24, 0x5a, 0x21, 0x2a, 0x51, 0x8a, 0x48, 0x63, 0xf1, 0x69, 0x87, 0x9d,
	0x86, 0x61, 0x40, 0x7f, 0xc7, 0x7e, 0x49, 0x8f, 0x03, 0x76, 0x1f, 0x86, 0xec, 0x8f, 0x0c, 0xa4,
	0x28, 0x5b, 0xce, 0x9c, 0x04, 0xda, 0x8d, 0x7a, 0xf8, 0x7e, 0xf1, 0x7d, 0x1f, 0x3e, 0x14, 0xec,
	0x4e, 0x28, 0x0b, 0x26, 0x19, 0x25, 0x2c, 0xf0, 0x62, 0x2c, 0xfc, 0x0b, 0xca, 0x42, 0x6f, 0x90,
	0x66, 0x89, 0x48, 0x50, 0x2d, 0xc4, 0xbb, 0x9d, 0x10, 0x7b, 0xe7, 0x98, 0x93, 0x1c, 0x72, 0xbe,
	0x86, 0xde, 0xe9, 0x05, 0xf1, 0xdf, 0x8e, 0x39, 0xc9, 0x86, 0x94, 0x51, 0x7e, 0x31, 0xa4, 0x2c,
	0x18, 0x2a, 0xff, 0x17, 0x57, 0x38, 0x76, 0xc9, 0x25, 0xfa, 0x04, 0x2c, 0xe9, 0xe1, 0x65, 0xe4,
	0xd2, 0x36, 0x7a, 0xb5, 0x7e, 0xeb, 0xb8, 0x35, 0x08, 0xf1, 0xe0, 0x04, 0x73, 0xe2, 0x92, 0x4b,
	0xb7, 0x71, 0x9e, 0x2f, 0x9c, 0x77, 0x06, 0x1c, 0xdc, 0x13, 0x8c, 0xa7, 0xe8, 0x10, 0x9a, 0x3a,
	0x1a, 0x4f, 0x75, 0xb8, 0xf6, 0x22, 0x1c, 0x4f, 0x5d, 0xeb, 0x5c, 0xaf, 0xd0, 0x01, 0x34, 0x29,
	0xf7, 0x26, 0x2a, 0x92, 0x5d, 0xeb, 0xd5, 0xfa, 0xd6, 0x49, 0xfd, 0xfd, 0x5f, 0xfb, 0x6b, 0xae,
	0x45, 0x79, 0x1e, 0x1f, 0xed, 0x41, 0x83, 0x72, 0x8f, 0x32, 0x2a, 0x6c, 0xb3, 0x64, 0xb0, 0x4e,
	0xf9, 0x88, 0x51, 0xe1, 0x08, 0xb0, 0x5f, 0x8b, 0xe5, 0x22, 0x5e, 0x30, 0x91, 0x61, 0xe6, 0x13,
	0xe9, 0x4a, 0xae, 0x70, 0xec, 0xd1, 0x40, 0x95, 0xd1, 0x29, 0x5c, 0x25, 0x38, 0x0a, 0x90, 0x0d,
	0x75, 0x86, 0x63, 0x62, 0xd7, 0x7a, 0x46, 0xbf, 0xa9, 0xf7, 0x14, 0x82, 0xf6, 0xc1, 0xa2, 0x7e,
	0xc2, 0xbc, 0x69, 0x16, 0xd9, 0x66, 0x69, 0xb7, 0x21, 0xd1, 0x71, 0x16, 0x39, 0x43, 0x78, 0x72,
	0x46, 0x6e, 0x49, 0x5b, 0xa5, 0xa1, 0xbf, 0x19, 0xb0, 0x77, 0x47, 0xa0, 0x6a, 0xcd, 0x7c, 0x06,
	0x1d, 0xa2, 0x5d, 0xbd, 0x88, 0x72, 0x61, 0xd7, 0x7a, 0x66, 0xbf, 0x75, 0xfc, 0x44, 0x9a, 0xdf,
	0xd6, 0x23, 0xb7, 0x5d, 0xb8, 0x7c, 0x43, 0xb9, 0x70, 0x62, 0x38, 0x18, 0xa7, 0x51, 0x82, 0x95,
	0xcd, 0x88, 0x71, 0x81, 0xa3, 0xc8, 0x25, 0x7e, 0x12, 0xc7, 0x84, 0x05, 0x67, 0x38, 0xae, 0x72,
	0x38, 0xd4, 0x83, 0x76, 0x88, 0x63, 0xe2, 0xd1, 0x60, 0x51, 0x4e, 0xc7, 0x05, 0x89, 0x8d, 0x02,
	0x95, 0xee, 0x3b, 0x70, 0xee, 0x4b, 0x57, 0xa9, 0x05, 0xce, 0x4f, 0xb0, 0x33, 0x9c, 0xdf, 0x0e,
	0x19, 0xf4, 0xfb, 0x29, 0xe1, 0x82, 0x26, 0x0c, 0x1d, 0x42, 0xe7, 0x52, 0xaf, 0x3d, 0x31, 0x4b,
	0xc9, 0x12, 0x23, 0xda, 0xc5, 0xd6, 0x9b, 0x59, 0x4a, 0x50, 0x0f, 0xac, 0xe2, 0x5b, 0x71, 0xb2,
	0x98, 0xfe, 0x1c, 0x45, 0x36, 0x34, 0x92, 0x54, 0xae, 0xb8, 0x6d, 0xf6, 0xcc, 0x7e, 0xd3, 0x2d,
	0x3e, 0x1d, 0xb2, 0x82, 0x18, 0x45, 0x0d, 0x55, 0x7a, 0x57, 0xa2, 0x6e, 0xed, 0xbf, 0xd4, 0x75,
	0x7e, 0x5d, 0xc5, 0x9b, 0x45, 0x9e, 0x6a, 0xbc, 0xf9, 0xb2, 0xd4, 0x9a, 0x12, 0x6f, 0x76, 0xa5,
	0xf9, 0xea, 0x6e, 0x2e, 0x1a, 0xa6, 0xc6, 0xf8, 0xbb, 0x01, 0xf6, 0xb3, 0x20, 0xd0, 0xa2, 0xf0,
	0xff, 0xb4, 0xe5, 0x9e, 0x13, 0xa3, 0x8f, 0x60, 0x03, 0x33, 0xfe, 0x23, 0xc9, 0xe6, 0x74, 0x32,
	0x15, 0x9d, 0xda, 0x39, 0x9a, 0x13, 0x6a, 0x59, 0x4f, 0xea, 0xab, 0xf4, 0xc4, 0x19, 0xc2, 0x07,
	0xb7, 0xd4, 0x5a, 0x8d, 0x6a, 0x7f, 0xd6, 0x60, 0xeb, 0xb5, 0x90, 0x71, 0xb4, 0xf7, 0x34, 0x12,
	0x68, 0x07, 0xcc, 0xe9, 0x0d, 0xb5, 0x91, 0x00, 0xea, 0x42, 0x03, 0xfb, 0x7e, 0x32, 0x65, 0x62,
	0x89, 0x51, 0x05, 0x28, 0x29, 0xc7, 0xa8, 0xff, 0x56, 0xc9, 0x91, 0x59, 0xa6, 0x5c, 0x81, 0xa2,
	0x0f, 0x01, 0x32, 0x95, 0x43, 0x89, 0x52, 0xbd, 0x64, 0xd3, 0xcc, 0xf1, 0x71, 0x16, 0xa1, 0x8f,
	0xa1, 0xa5, 0x8d, 0x02, 0xc2, 0x7d, 0xfb, 0x41, 0x49, 0xba, 0xb4, 0xf7, 0x73, 0xc2, 0x7d, 0x34,
	0x80, 0x2d, 0x6d, 0xa6, 0x3a, 0xae, 0xb2, 0xae, 0x97, 0x6c, 0x37, 0xf2, 0x5d, 0x79, 0xae, 0x6f,
	0x65, 0xee, 0x4f, 0x61, 0x53, 0xdb, 0x0b, 0x1c, 0xe6, 0xe6, 0x8d, 0x92, 0x79, 0x27, 0xdf, 0x7c,
	0x83, 0x43, 0x65, 0xfd, 0x05, 0x3c, 0xce, 0x48, 0x84, 0x67, 0x9e, 0x4f, 0x33, 0x3f, 0x22, 0x5e,
	0x40, 0x26, 0x78, 0x2a, 0xbc, 0x98, 0x87, 0xb6, 0x55, 0xf2, 0x7a, 0xa4, 0x8c, 0x4e, 0x95, 0xcd,
	0x73, 0x65, 0xf2, 0x92, 0x87, 0xce, 0x57, 0xd0, 0x3d, 0x23, 0x62, 0xe5, 0x74, 0xa6, 0x91, 0xa8,
	0x22, 0xad, 0x3f, 0x1b, 0xb0, 0x7f, 0x67, 0xa8, 0x6a, 0x97, 0xe4, 0x29, 0xb4, 0x54, 0xb3, 0xf2,
	0xb3, 0xaa, 0x29, 0xb6, 0x8e, 0x1f, 0xe5, 0xd2, 0xba, 0x4c, 0x02, 0x17, 0xc8, 0x7c, 0xed, 0xfc,
	0x62, 0xc2, 0xc3, 0x45, 0xfa, 0x97, 0xf2, 0xb9, 0x1e, 0xb1, 0x49, 0x72, 0x2b, 0x51, 0x0e, 0xa0,
	0xb9, 0x98, 0xc9, 0x92, 0xf8, 0x90, 0x62, 0x1a, 0xfb, 0x60, 0xcd, 0xc7, 0x50, 0xe6, 0x4a, 0x43,
	0xe8, 0x01, 0x94, 0xc8, 0x56, 0xbf, 0x8f, 0x6c, 0x0f, 0x56, 0x92, 0x6d, 0x07, 0x4c, 0x4e, 0xae,
	0xec, 0xf5, 0x72, 0x75, 0x9c, 0x5c, 0xa1, 0x01, 0x3c, 0x5c, 0x12, 0x51, 0x55, 0x04, 0xb7, 0x1b,
	0x4a, 0x03, 0xb7, 0xcb, 0x22, 0x2a, 0x0b, 0xe1, 0x68, 0x0f, 0x80, 0xfb, 0x49, 0xa6, 0x9f, 0x23,
	0x4b, 0x5d, 0xd8, 0xa6, 0x42, 0xd4, 0x6d, 0xb5, 0xa1, 0xae, 0x78, 0xda, 0x2c, 0x15, 0xa1, 0x10,
	0x59, 0xa2, 0x1f, 0x61, 0xce, 0xe9, 0x64, 0x66, 0x43, 0xa9, 0x8a, 0x39, 0xea, 0x7c, 0x06, 0x68,
	0xd1, 0xd7, 0x53, 0x8d, 0xa2, 0x6d, 0xf5, 0x04, 0x92, 0x4c, 0x60, 0xca, 0x62, 0xc2, 0xc4, 0xd6,
	0x1a, 0xb2, 0xa0, 0x2e, 0x5f, 0x9c, 0x2d, 0xc3, 0x79, 0x05, 0x1b, 0x6a, 0x00, 0xb9, 0x4f, 0x15,
	0x6d, 0xd2, 0xd3, 0xaa, 0xdd, 0x98, 0x96, 0x73, 0x6d, 0xc0, 0xe6, 0x52, 0xc8, 0x6a, 0x9c, 0xfa,
	0x1c, 0x9a, 0x53, 0x2e, 0x15, 0x8d, 0x4d, 0x12, 0xcd, 0xa8, 0xc7, 0x85, 0xe8, 0xde, 0x20, 0x8c,
	0x6b, 0x49, 0x4b, 0x45, 0x9d, 0x53, 0xd8, 0x56, 0xbf, 0x7d, 0x24, 0xf0, 0x16, 0xde, 0xe6, 0xdd,
	0xde, 0x9b, 0xda, 0x63, 0x5c, 0x04, 0x39, 0x84, 0x8e, 0x82, 0xbc, 0x94, 0x64, 0x3e, 0xd1, 0x4c,
	0x99, 0x3f, 0x87, 0x6a, 0xeb, 0x55, 0xbe, 0x73, 0x32, 0x7e, 0x7f, 0xdd, 0x35, 0xfe, 0xb8, 0xee,
	0x1a, 0x7f, 0x5f, 0x77, 0x8d, 0x77, 0xff, 0x74, 0xd7, 0xc0, 0xf6, 0x93, 0x78, 0x30, 0xa3, 0xb3,
	0x64, 0x2a, 0xf3, 0xc5, 0x49, 0x40, 0xa2, 0xfc, 0x67, 0xf3, 0x87, 0xc3, 0x30, 0x89, 0x30, 0x0b,
	0x07, 0x4f, 0x8f, 0x85, 0x18, 0xf8, 0x49, 0x7c, 0xa4, 0x60, 0x3f, 0x89, 0x8e, 0x70, 0x9a, 0x1e,
	0xc9, 0x1f, 0x57, 0x2f, 0x7f, 0x4d, 0xf8, 0xbf, 0x01, 0x00, 0x00, 0xff, 0xff, 0xad, 0xc4, 0x83,
	0x24, 0xc7, 0x0a, 0x00, 0x00,
}
