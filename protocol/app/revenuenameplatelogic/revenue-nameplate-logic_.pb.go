// Code generated by protoc-gen-go. DO NOT EDIT.
// source: revenue-nameplate-logic_.proto

package revenuenameplatelogic // import "golang.52tt.com/protocol/app/revenuenameplatelogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetUserNameplateInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserNameplateInfoReq) Reset()         { *m = GetUserNameplateInfoReq{} }
func (m *GetUserNameplateInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserNameplateInfoReq) ProtoMessage()    {}
func (*GetUserNameplateInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_logic__d293854ed8e6a06c, []int{0}
}
func (m *GetUserNameplateInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserNameplateInfoReq.Unmarshal(m, b)
}
func (m *GetUserNameplateInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserNameplateInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserNameplateInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserNameplateInfoReq.Merge(dst, src)
}
func (m *GetUserNameplateInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserNameplateInfoReq.Size(m)
}
func (m *GetUserNameplateInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserNameplateInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserNameplateInfoReq proto.InternalMessageInfo

func (m *GetUserNameplateInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserNameplateInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserNameplateInfoResp struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Nameplates           []*app.NameplateDetailInfo `protobuf:"bytes,2,rep,name=nameplates,proto3" json:"nameplates,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetUserNameplateInfoResp) Reset()         { *m = GetUserNameplateInfoResp{} }
func (m *GetUserNameplateInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserNameplateInfoResp) ProtoMessage()    {}
func (*GetUserNameplateInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_logic__d293854ed8e6a06c, []int{1}
}
func (m *GetUserNameplateInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserNameplateInfoResp.Unmarshal(m, b)
}
func (m *GetUserNameplateInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserNameplateInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserNameplateInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserNameplateInfoResp.Merge(dst, src)
}
func (m *GetUserNameplateInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserNameplateInfoResp.Size(m)
}
func (m *GetUserNameplateInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserNameplateInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserNameplateInfoResp proto.InternalMessageInfo

func (m *GetUserNameplateInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserNameplateInfoResp) GetNameplates() []*app.NameplateDetailInfo {
	if m != nil {
		return m.Nameplates
	}
	return nil
}

type SetUserNameplateInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	IdList               []uint32     `protobuf:"varint,2,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetUserNameplateInfoReq) Reset()         { *m = SetUserNameplateInfoReq{} }
func (m *SetUserNameplateInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetUserNameplateInfoReq) ProtoMessage()    {}
func (*SetUserNameplateInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_logic__d293854ed8e6a06c, []int{2}
}
func (m *SetUserNameplateInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserNameplateInfoReq.Unmarshal(m, b)
}
func (m *SetUserNameplateInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserNameplateInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetUserNameplateInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserNameplateInfoReq.Merge(dst, src)
}
func (m *SetUserNameplateInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetUserNameplateInfoReq.Size(m)
}
func (m *SetUserNameplateInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserNameplateInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserNameplateInfoReq proto.InternalMessageInfo

func (m *SetUserNameplateInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUserNameplateInfoReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type SetUserNameplateInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUserNameplateInfoResp) Reset()         { *m = SetUserNameplateInfoResp{} }
func (m *SetUserNameplateInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetUserNameplateInfoResp) ProtoMessage()    {}
func (*SetUserNameplateInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_logic__d293854ed8e6a06c, []int{3}
}
func (m *SetUserNameplateInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserNameplateInfoResp.Unmarshal(m, b)
}
func (m *SetUserNameplateInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserNameplateInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetUserNameplateInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserNameplateInfoResp.Merge(dst, src)
}
func (m *SetUserNameplateInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetUserNameplateInfoResp.Size(m)
}
func (m *SetUserNameplateInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserNameplateInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserNameplateInfoResp proto.InternalMessageInfo

func (m *SetUserNameplateInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetUserAllNameplateListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserAllNameplateListReq) Reset()         { *m = GetUserAllNameplateListReq{} }
func (m *GetUserAllNameplateListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAllNameplateListReq) ProtoMessage()    {}
func (*GetUserAllNameplateListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_logic__d293854ed8e6a06c, []int{4}
}
func (m *GetUserAllNameplateListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAllNameplateListReq.Unmarshal(m, b)
}
func (m *GetUserAllNameplateListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAllNameplateListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAllNameplateListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAllNameplateListReq.Merge(dst, src)
}
func (m *GetUserAllNameplateListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAllNameplateListReq.Size(m)
}
func (m *GetUserAllNameplateListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAllNameplateListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAllNameplateListReq proto.InternalMessageInfo

func (m *GetUserAllNameplateListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserAllNameplateListResp struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Nameplates           []*app.NameplateDetailInfo `protobuf:"bytes,2,rep,name=nameplates,proto3" json:"nameplates,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetUserAllNameplateListResp) Reset()         { *m = GetUserAllNameplateListResp{} }
func (m *GetUserAllNameplateListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAllNameplateListResp) ProtoMessage()    {}
func (*GetUserAllNameplateListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_logic__d293854ed8e6a06c, []int{5}
}
func (m *GetUserAllNameplateListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAllNameplateListResp.Unmarshal(m, b)
}
func (m *GetUserAllNameplateListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAllNameplateListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAllNameplateListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAllNameplateListResp.Merge(dst, src)
}
func (m *GetUserAllNameplateListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAllNameplateListResp.Size(m)
}
func (m *GetUserAllNameplateListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAllNameplateListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAllNameplateListResp proto.InternalMessageInfo

func (m *GetUserAllNameplateListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserAllNameplateListResp) GetNameplates() []*app.NameplateDetailInfo {
	if m != nil {
		return m.Nameplates
	}
	return nil
}

func init() {
	proto.RegisterType((*GetUserNameplateInfoReq)(nil), "ga.revenuenameplatelogic.GetUserNameplateInfoReq")
	proto.RegisterType((*GetUserNameplateInfoResp)(nil), "ga.revenuenameplatelogic.GetUserNameplateInfoResp")
	proto.RegisterType((*SetUserNameplateInfoReq)(nil), "ga.revenuenameplatelogic.SetUserNameplateInfoReq")
	proto.RegisterType((*SetUserNameplateInfoResp)(nil), "ga.revenuenameplatelogic.SetUserNameplateInfoResp")
	proto.RegisterType((*GetUserAllNameplateListReq)(nil), "ga.revenuenameplatelogic.GetUserAllNameplateListReq")
	proto.RegisterType((*GetUserAllNameplateListResp)(nil), "ga.revenuenameplatelogic.GetUserAllNameplateListResp")
}

func init() {
	proto.RegisterFile("revenue-nameplate-logic_.proto", fileDescriptor_revenue_nameplate_logic__d293854ed8e6a06c)
}

var fileDescriptor_revenue_nameplate_logic__d293854ed8e6a06c = []byte{
	// 315 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x92, 0x4f, 0x4b, 0xf3, 0x40,
	0x10, 0x87, 0x49, 0x0b, 0x6d, 0xdf, 0xe9, 0x5b, 0x90, 0x5c, 0xb2, 0x54, 0x90, 0x92, 0x83, 0xd4,
	0x43, 0xb7, 0x10, 0x11, 0xcf, 0x96, 0x8a, 0x08, 0xd2, 0xc3, 0x16, 0x2f, 0xbd, 0x84, 0x4d, 0x32,
	0x2e, 0x0b, 0x9b, 0xec, 0x36, 0xbb, 0x15, 0x7a, 0x11, 0xfc, 0xe6, 0x92, 0x34, 0x46, 0x0f, 0xf5,
	0x20, 0x05, 0x6f, 0x3f, 0x86, 0x87, 0x67, 0xfe, 0x30, 0x70, 0x51, 0xe2, 0x2b, 0x16, 0x3b, 0x9c,
	0x15, 0x3c, 0x47, 0xa3, 0xb8, 0xc3, 0x99, 0xd2, 0x42, 0xa6, 0x31, 0x35, 0xa5, 0x76, 0xda, 0x27,
	0x82, 0xd3, 0x06, 0x69, 0x89, 0x1a, 0x18, 0x8f, 0x04, 0x8f, 0x13, 0x6e, 0xf1, 0x00, 0x86, 0x6b,
	0x08, 0x1e, 0xd0, 0x3d, 0x5b, 0x2c, 0x57, 0x9f, 0xdc, 0x63, 0xf1, 0xa2, 0x19, 0x6e, 0xfd, 0x4b,
	0x18, 0x54, 0x60, 0x5c, 0xe2, 0x96, 0x78, 0x13, 0x6f, 0x3a, 0x8c, 0x86, 0x54, 0x70, 0xba, 0xe0,
	0x16, 0x19, 0x6e, 0x59, 0x3f, 0x39, 0x04, 0xff, 0x0c, 0xba, 0x3b, 0x99, 0x91, 0xce, 0xc4, 0x9b,
	0x8e, 0x58, 0x15, 0xc3, 0x37, 0x20, 0xc7, 0xa5, 0xd6, 0xf8, 0x57, 0xf0, 0xaf, 0xb1, 0x5a, 0xd3,
	0x68, 0xff, 0x7f, 0x69, 0xad, 0x61, 0x83, 0xa4, 0x49, 0xfe, 0x2d, 0x40, 0x3b, 0xbc, 0x25, 0x9d,
	0x49, 0x77, 0x3a, 0x8c, 0x82, 0x8a, 0x6d, 0xad, 0x4b, 0x74, 0x5c, 0xaa, 0xda, 0xfd, 0x0d, 0x0d,
	0x37, 0x10, 0xac, 0x4f, 0x5c, 0x2a, 0x80, 0xbe, 0xcc, 0x62, 0x25, 0xad, 0xab, 0x1b, 0x8f, 0x58,
	0x4f, 0x66, 0x4f, 0xd2, 0xba, 0xf0, 0x1e, 0xc8, 0xfa, 0xf4, 0xdd, 0xc2, 0x25, 0x8c, 0x9b, 0x13,
	0xdd, 0x29, 0xd5, 0x9a, 0xaa, 0x0e, 0xbf, 0x98, 0x32, 0x7c, 0xf7, 0xe0, 0xfc, 0x47, 0xcd, 0xdf,
	0x1c, 0x7b, 0xb1, 0x02, 0x92, 0xea, 0x9c, 0xee, 0xe5, 0x5e, 0xef, 0x2a, 0x3e, 0xd7, 0x19, 0xaa,
	0xc3, 0x77, 0x6d, 0x22, 0xa1, 0x15, 0x2f, 0x04, 0xbd, 0x89, 0x9c, 0xa3, 0xa9, 0xce, 0xe7, 0x75,
	0x39, 0xd5, 0x6a, 0xce, 0x8d, 0x99, 0x1f, 0x7d, 0xd0, 0xa4, 0x57, 0x33, 0xd7, 0x1f, 0x01, 0x00,
	0x00, 0xff, 0xff, 0xf7, 0x55, 0x85, 0xcc, 0xe3, 0x02, 0x00, 0x00,
}
