// Code generated by protoc-gen-go. DO NOT EDIT.
// source: exchange_app_logic/exchange_app_logic.proto

package exchange_app_logic // import "golang.52tt.com/protocol/app/exchange-app-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 获取用户当前积分余额，并判断是否弹窗
type GetUserScoreRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ScoreType            uint32       `protobuf:"varint,2,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserScoreRequest) Reset()         { *m = GetUserScoreRequest{} }
func (m *GetUserScoreRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserScoreRequest) ProtoMessage()    {}
func (*GetUserScoreRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_app_logic_77c8d4ba8c94a4cb, []int{0}
}
func (m *GetUserScoreRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserScoreRequest.Unmarshal(m, b)
}
func (m *GetUserScoreRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserScoreRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserScoreRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserScoreRequest.Merge(dst, src)
}
func (m *GetUserScoreRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserScoreRequest.Size(m)
}
func (m *GetUserScoreRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserScoreRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserScoreRequest proto.InternalMessageInfo

func (m *GetUserScoreRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserScoreRequest) GetScoreType() uint32 {
	if m != nil {
		return m.ScoreType
	}
	return 0
}

type GetUserScoreResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Score_1              uint32        `protobuf:"varint,2,opt,name=score_1,json=score1,proto3" json:"score_1,omitempty"`
	Score_2              uint32        `protobuf:"varint,3,opt,name=score_2,json=score2,proto3" json:"score_2,omitempty"`
	Show                 bool          `protobuf:"varint,4,opt,name=show,proto3" json:"show,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserScoreResponse) Reset()         { *m = GetUserScoreResponse{} }
func (m *GetUserScoreResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserScoreResponse) ProtoMessage()    {}
func (*GetUserScoreResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_app_logic_77c8d4ba8c94a4cb, []int{1}
}
func (m *GetUserScoreResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserScoreResponse.Unmarshal(m, b)
}
func (m *GetUserScoreResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserScoreResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserScoreResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserScoreResponse.Merge(dst, src)
}
func (m *GetUserScoreResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserScoreResponse.Size(m)
}
func (m *GetUserScoreResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserScoreResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserScoreResponse proto.InternalMessageInfo

func (m *GetUserScoreResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserScoreResponse) GetScore_1() uint32 {
	if m != nil {
		return m.Score_1
	}
	return 0
}

func (m *GetUserScoreResponse) GetScore_2() uint32 {
	if m != nil {
		return m.Score_2
	}
	return 0
}

func (m *GetUserScoreResponse) GetShow() bool {
	if m != nil {
		return m.Show
	}
	return false
}

// 兑换积分为T豆
type BeginTransactionRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ScoreType            uint32       `protobuf:"varint,2,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
	Score_1              uint32       `protobuf:"varint,3,opt,name=score_1,json=score1,proto3" json:"score_1,omitempty"`
	Score_2              uint32       `protobuf:"varint,4,opt,name=score_2,json=score2,proto3" json:"score_2,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BeginTransactionRequest) Reset()         { *m = BeginTransactionRequest{} }
func (m *BeginTransactionRequest) String() string { return proto.CompactTextString(m) }
func (*BeginTransactionRequest) ProtoMessage()    {}
func (*BeginTransactionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_app_logic_77c8d4ba8c94a4cb, []int{2}
}
func (m *BeginTransactionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BeginTransactionRequest.Unmarshal(m, b)
}
func (m *BeginTransactionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BeginTransactionRequest.Marshal(b, m, deterministic)
}
func (dst *BeginTransactionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BeginTransactionRequest.Merge(dst, src)
}
func (m *BeginTransactionRequest) XXX_Size() int {
	return xxx_messageInfo_BeginTransactionRequest.Size(m)
}
func (m *BeginTransactionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BeginTransactionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BeginTransactionRequest proto.InternalMessageInfo

func (m *BeginTransactionRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BeginTransactionRequest) GetScoreType() uint32 {
	if m != nil {
		return m.ScoreType
	}
	return 0
}

func (m *BeginTransactionRequest) GetScore_1() uint32 {
	if m != nil {
		return m.Score_1
	}
	return 0
}

func (m *BeginTransactionRequest) GetScore_2() uint32 {
	if m != nil {
		return m.Score_2
	}
	return 0
}

type BeginTransactionResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BeginTransactionResponse) Reset()         { *m = BeginTransactionResponse{} }
func (m *BeginTransactionResponse) String() string { return proto.CompactTextString(m) }
func (*BeginTransactionResponse) ProtoMessage()    {}
func (*BeginTransactionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_exchange_app_logic_77c8d4ba8c94a4cb, []int{3}
}
func (m *BeginTransactionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BeginTransactionResponse.Unmarshal(m, b)
}
func (m *BeginTransactionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BeginTransactionResponse.Marshal(b, m, deterministic)
}
func (dst *BeginTransactionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BeginTransactionResponse.Merge(dst, src)
}
func (m *BeginTransactionResponse) XXX_Size() int {
	return xxx_messageInfo_BeginTransactionResponse.Size(m)
}
func (m *BeginTransactionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BeginTransactionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BeginTransactionResponse proto.InternalMessageInfo

func (m *BeginTransactionResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*GetUserScoreRequest)(nil), "ga.exchange_app_logic.GetUserScoreRequest")
	proto.RegisterType((*GetUserScoreResponse)(nil), "ga.exchange_app_logic.GetUserScoreResponse")
	proto.RegisterType((*BeginTransactionRequest)(nil), "ga.exchange_app_logic.BeginTransactionRequest")
	proto.RegisterType((*BeginTransactionResponse)(nil), "ga.exchange_app_logic.BeginTransactionResponse")
}

func init() {
	proto.RegisterFile("exchange_app_logic/exchange_app_logic.proto", fileDescriptor_exchange_app_logic_77c8d4ba8c94a4cb)
}

var fileDescriptor_exchange_app_logic_77c8d4ba8c94a4cb = []byte{
	// 316 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x52, 0x4d, 0x4b, 0xc3, 0x40,
	0x14, 0x24, 0xb6, 0xf4, 0x63, 0x6b, 0x2f, 0xab, 0xd2, 0x20, 0x08, 0x25, 0x07, 0xa9, 0x48, 0x37,
	0x34, 0xe2, 0x1f, 0x28, 0x88, 0x07, 0x6f, 0xb1, 0x5e, 0x44, 0x08, 0x9b, 0xf5, 0xb1, 0x0d, 0xa4,
	0x79, 0xaf, 0xd9, 0x2d, 0x9a, 0x5f, 0xe0, 0xdd, 0x5f, 0x2c, 0xf9, 0xc0, 0x56, 0x82, 0x07, 0xc1,
	0xdb, 0xcb, 0xcc, 0x64, 0x66, 0x76, 0xf7, 0xb1, 0x6b, 0x78, 0x57, 0x6b, 0x99, 0x69, 0x88, 0x24,
	0x51, 0x94, 0xa2, 0x4e, 0x94, 0xdf, 0x86, 0x04, 0xe5, 0x68, 0x91, 0x9f, 0x69, 0x29, 0xda, 0xe4,
	0xf9, 0x58, 0xcb, 0x28, 0x96, 0x06, 0x6a, 0x95, 0xf7, 0xc2, 0x4e, 0xee, 0xc1, 0x3e, 0x19, 0xc8,
	0x1f, 0x15, 0xe6, 0x10, 0xc2, 0x76, 0x07, 0xc6, 0xf2, 0x4b, 0x36, 0x28, 0x45, 0x51, 0x0e, 0x5b,
	0xd7, 0x99, 0x3a, 0xb3, 0x51, 0x30, 0x12, 0x5a, 0x8a, 0xa5, 0x34, 0xa5, 0x24, 0xec, 0xc7, 0xf5,
	0xc0, 0x2f, 0x18, 0x33, 0xe5, 0x7f, 0x91, 0x2d, 0x08, 0xdc, 0xa3, 0xa9, 0x33, 0x1b, 0x87, 0xc3,
	0x0a, 0x59, 0x15, 0x04, 0xde, 0x87, 0xc3, 0x4e, 0x7f, 0xda, 0x1b, 0xc2, 0xcc, 0x00, 0xbf, 0x62,
	0xc3, 0xc6, 0xdf, 0x50, 0x13, 0x70, 0xbc, 0x0f, 0x30, 0x14, 0x0e, 0xe2, 0x66, 0xe2, 0x13, 0xd6,
	0xaf, 0x23, 0x16, 0x8d, 0x7f, 0xaf, 0xfa, 0x5c, 0xec, 0x89, 0xc0, 0xed, 0x1c, 0x10, 0x01, 0xe7,
	0xac, 0x6b, 0xd6, 0xf8, 0xe6, 0x76, 0xa7, 0xce, 0x6c, 0x10, 0x56, 0xb3, 0xf7, 0xe9, 0xb0, 0xc9,
	0x12, 0x74, 0x92, 0xad, 0x72, 0x99, 0x19, 0xa9, 0x6c, 0x82, 0xd9, 0xff, 0x1e, 0xf6, 0xb0, 0x68,
	0xe7, 0xb7, 0xa2, 0xdd, 0xc3, 0xa2, 0xde, 0x1d, 0x73, 0xdb, 0x9d, 0xfe, 0x7c, 0x43, 0xcb, 0x07,
	0xe6, 0x2a, 0xdc, 0x88, 0x22, 0x29, 0x70, 0x57, 0x4a, 0x36, 0xf8, 0x0a, 0x69, 0xfd, 0xbe, 0xcf,
	0xbe, 0xc6, 0x54, 0x66, 0x5a, 0xdc, 0x06, 0xd6, 0x0a, 0x85, 0x1b, 0xbf, 0x82, 0x15, 0xa6, 0xbe,
	0x24, 0xfa, 0x5e, 0x9e, 0xb9, 0x24, 0x9a, 0x57, 0xfb, 0x11, 0xf7, 0x2a, 0xc1, 0xcd, 0x57, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x9f, 0x35, 0x88, 0x24, 0x6c, 0x02, 0x00, 0x00,
}
