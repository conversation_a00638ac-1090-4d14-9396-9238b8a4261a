package app

import "fmt"

type Version uint32

func V(major, minor uint8, release uint16) Version {
	return Version(V32(major, minor, release))
}

func V32(major, minor uint8, release uint16) uint32 {
	return (uint32(major) << 24) + (uint32(minor) << 16) + uint32(release)
}

func (v Version) Major() uint8    { return uint8((v >> 24) & 0x000F) }
func (v Version) Minor() uint8    { return uint8((v >> 16) & 0x000F) }
func (v Version) Release() uint16 { return uint16(v & 0x00FF) }

func (v Version) String() string { return fmt.Sprintf("%d.%d.%d", v.Major(), v.Minor(), v.Release()) }
