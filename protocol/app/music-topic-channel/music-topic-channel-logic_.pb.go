// Code generated by protoc-gen-go. DO NOT EDIT.
// source: music-topic-channel-logic_.proto

package music_topic_channel // import "golang.52tt.com/protocol/app/music-topic-channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import im "golang.52tt.com/protocol/app/im"
import muse_interest_hub_logic "golang.52tt.com/protocol/app/muse-interest-hub-logic"
import topic_channel "golang.52tt.com/protocol/app/topic-channel"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 标签属性
type FilterAttrType int32

const (
	FilterAttrType_FILTER_ATTR_TYPE_UNEXPECTED FilterAttrType = 0
	FilterAttrType_FILTER_ATTR_TYPE_SAME_CITY  FilterAttrType = 1
)

var FilterAttrType_name = map[int32]string{
	0: "FILTER_ATTR_TYPE_UNEXPECTED",
	1: "FILTER_ATTR_TYPE_SAME_CITY",
}
var FilterAttrType_value = map[string]int32{
	"FILTER_ATTR_TYPE_UNEXPECTED": 0,
	"FILTER_ATTR_TYPE_SAME_CITY":  1,
}

func (x FilterAttrType) String() string {
	return proto.EnumName(FilterAttrType_name, int32(x))
}
func (FilterAttrType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{0}
}

type MusicHomePageDialogType int32

const (
	MusicHomePageDialogType_Ktv_Dialog MusicHomePageDialogType = 0
)

var MusicHomePageDialogType_name = map[int32]string{
	0: "Ktv_Dialog",
}
var MusicHomePageDialogType_value = map[string]int32{
	"Ktv_Dialog": 0,
}

func (x MusicHomePageDialogType) String() string {
	return proto.EnumName(MusicHomePageDialogType_name, int32(x))
}
func (MusicHomePageDialogType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{1}
}

type MusicChannelLabel int32

const (
	MusicChannelLabel_MusicChannelLabelNone    MusicChannelLabel = 0
	MusicChannelLabel_MusicChannelLabelQuality MusicChannelLabel = 1
	MusicChannelLabel_MusicChannelLabelHot     MusicChannelLabel = 2
)

var MusicChannelLabel_name = map[int32]string{
	0: "MusicChannelLabelNone",
	1: "MusicChannelLabelQuality",
	2: "MusicChannelLabelHot",
}
var MusicChannelLabel_value = map[string]int32{
	"MusicChannelLabelNone":    0,
	"MusicChannelLabelQuality": 1,
	"MusicChannelLabelHot":     2,
}

func (x MusicChannelLabel) String() string {
	return proto.EnumName(MusicChannelLabel_name, int32(x))
}
func (MusicChannelLabel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{2}
}

type MuseCategoryType int32

const (
	MuseCategoryType_MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_UNSPECIFIED MuseCategoryType = 0
	MuseCategoryType_MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_GANGUP_TYPE MuseCategoryType = 1
)

var MuseCategoryType_name = map[int32]string{
	0: "MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_UNSPECIFIED",
	1: "MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_GANGUP_TYPE",
}
var MuseCategoryType_value = map[string]int32{
	"MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_UNSPECIFIED": 0,
	"MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_GANGUP_TYPE": 1,
}

func (x MuseCategoryType) String() string {
	return proto.EnumName(MuseCategoryType_name, int32(x))
}
func (MuseCategoryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{3}
}

type MusePlayingOption int32

const (
	MusePlayingOption_MUSE_SOCIAL_COMMUNITY_PLAYING_OPTION_AT_MAIN_PAGE_UNSPECIFIED MusePlayingOption = 0
	MusePlayingOption_MUSE_SOCIAL_COMMUNITY_PLAYING_OPTION_FRIEND                   MusePlayingOption = 1
)

var MusePlayingOption_name = map[int32]string{
	0: "MUSE_SOCIAL_COMMUNITY_PLAYING_OPTION_AT_MAIN_PAGE_UNSPECIFIED",
	1: "MUSE_SOCIAL_COMMUNITY_PLAYING_OPTION_FRIEND",
}
var MusePlayingOption_value = map[string]int32{
	"MUSE_SOCIAL_COMMUNITY_PLAYING_OPTION_AT_MAIN_PAGE_UNSPECIFIED": 0,
	"MUSE_SOCIAL_COMMUNITY_PLAYING_OPTION_FRIEND":                   1,
}

func (x MusePlayingOption) String() string {
	return proto.EnumName(MusePlayingOption_name, int32(x))
}
func (MusePlayingOption) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{4}
}

// 主题房处罚类型
type TopicChannelPunishType int32

const (
	TopicChannelPunishType_UnknownPunishType   TopicChannelPunishType = 0
	TopicChannelPunishType_WarnType            TopicChannelPunishType = 1
	TopicChannelPunishType_KickOutChannelType  TopicChannelPunishType = 2
	TopicChannelPunishType_PublishChannelLimit TopicChannelPunishType = 3
)

var TopicChannelPunishType_name = map[int32]string{
	0: "UnknownPunishType",
	1: "WarnType",
	2: "KickOutChannelType",
	3: "PublishChannelLimit",
}
var TopicChannelPunishType_value = map[string]int32{
	"UnknownPunishType":   0,
	"WarnType":            1,
	"KickOutChannelType":  2,
	"PublishChannelLimit": 3,
}

func (x TopicChannelPunishType) String() string {
	return proto.EnumName(TopicChannelPunishType_name, int32(x))
}
func (TopicChannelPunishType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{5}
}

type GetMusicChannelFilterV2Resp_FilterItemType int32

const (
	GetMusicChannelFilterV2Resp_HOME_FILTER_ITEM  GetMusicChannelFilterV2Resp_FilterItemType = 0
	GetMusicChannelFilterV2Resp_PAGE_FILTER_ITERM GetMusicChannelFilterV2Resp_FilterItemType = 1
	GetMusicChannelFilterV2Resp_PAGE_POST         GetMusicChannelFilterV2Resp_FilterItemType = 2
)

var GetMusicChannelFilterV2Resp_FilterItemType_name = map[int32]string{
	0: "HOME_FILTER_ITEM",
	1: "PAGE_FILTER_ITERM",
	2: "PAGE_POST",
}
var GetMusicChannelFilterV2Resp_FilterItemType_value = map[string]int32{
	"HOME_FILTER_ITEM":  0,
	"PAGE_FILTER_ITERM": 1,
	"PAGE_POST":         2,
}

func (x GetMusicChannelFilterV2Resp_FilterItemType) String() string {
	return proto.EnumName(GetMusicChannelFilterV2Resp_FilterItemType_name, int32(x))
}
func (GetMusicChannelFilterV2Resp_FilterItemType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{1, 0}
}

type GetMusicHomePageViewV2Resp_MusicHomePageType int32

const (
	GetMusicHomePageViewV2Resp_MusicHomePage_Full   GetMusicHomePageViewV2Resp_MusicHomePageType = 0
	GetMusicHomePageViewV2Resp_MusicHomePage_Scroll GetMusicHomePageViewV2Resp_MusicHomePageType = 1
)

var GetMusicHomePageViewV2Resp_MusicHomePageType_name = map[int32]string{
	0: "MusicHomePage_Full",
	1: "MusicHomePage_Scroll",
}
var GetMusicHomePageViewV2Resp_MusicHomePageType_value = map[string]int32{
	"MusicHomePage_Full":   0,
	"MusicHomePage_Scroll": 1,
}

func (x GetMusicHomePageViewV2Resp_MusicHomePageType) String() string {
	return proto.EnumName(GetMusicHomePageViewV2Resp_MusicHomePageType_name, int32(x))
}
func (GetMusicHomePageViewV2Resp_MusicHomePageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{5, 0}
}

type GetMusicHomePageViewV2Resp_ActionType int32

const (
	GetMusicHomePageViewV2Resp_Sing_A_Round_Dialog      GetMusicHomePageViewV2Resp_ActionType = 0
	GetMusicHomePageViewV2Resp_Quick_Match              GetMusicHomePageViewV2Resp_ActionType = 1
	GetMusicHomePageViewV2Resp_Url                      GetMusicHomePageViewV2Resp_ActionType = 2
	GetMusicHomePageViewV2Resp_Sing_A_Round_Quick_Match GetMusicHomePageViewV2Resp_ActionType = 3
)

var GetMusicHomePageViewV2Resp_ActionType_name = map[int32]string{
	0: "Sing_A_Round_Dialog",
	1: "Quick_Match",
	2: "Url",
	3: "Sing_A_Round_Quick_Match",
}
var GetMusicHomePageViewV2Resp_ActionType_value = map[string]int32{
	"Sing_A_Round_Dialog":      0,
	"Quick_Match":              1,
	"Url":                      2,
	"Sing_A_Round_Quick_Match": 3,
}

func (x GetMusicHomePageViewV2Resp_ActionType) String() string {
	return proto.EnumName(GetMusicHomePageViewV2Resp_ActionType_name, int32(x))
}
func (GetMusicHomePageViewV2Resp_ActionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{5, 1}
}

type MuseGetTopicChannelInfoRequest_ChannelInfoType int32

const (
	MuseGetTopicChannelInfoRequest_CHANNEL_INFO_TYPE_NORMAL_UNSPECIFIED MuseGetTopicChannelInfoRequest_ChannelInfoType = 0
	MuseGetTopicChannelInfoRequest_CHANNEL_INFO_TYPE_PLAY_TYPE          MuseGetTopicChannelInfoRequest_ChannelInfoType = 1
)

var MuseGetTopicChannelInfoRequest_ChannelInfoType_name = map[int32]string{
	0: "CHANNEL_INFO_TYPE_NORMAL_UNSPECIFIED",
	1: "CHANNEL_INFO_TYPE_PLAY_TYPE",
}
var MuseGetTopicChannelInfoRequest_ChannelInfoType_value = map[string]int32{
	"CHANNEL_INFO_TYPE_NORMAL_UNSPECIFIED": 0,
	"CHANNEL_INFO_TYPE_PLAY_TYPE":          1,
}

func (x MuseGetTopicChannelInfoRequest_ChannelInfoType) String() string {
	return proto.EnumName(MuseGetTopicChannelInfoRequest_ChannelInfoType_name, int32(x))
}
func (MuseGetTopicChannelInfoRequest_ChannelInfoType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{35, 0}
}

type MuseGetTopicChannelInfoResponse_TabType int32

const (
	MuseGetTopicChannelInfoResponse_TAB_TYPE_NORMAL_UNSPECIFIED MuseGetTopicChannelInfoResponse_TabType = 0
	MuseGetTopicChannelInfoResponse_TAB_TYPE_GAME               MuseGetTopicChannelInfoResponse_TabType = 1
	MuseGetTopicChannelInfoResponse_TAB_TYPE_MINI_GAME          MuseGetTopicChannelInfoResponse_TabType = 2
)

var MuseGetTopicChannelInfoResponse_TabType_name = map[int32]string{
	0: "TAB_TYPE_NORMAL_UNSPECIFIED",
	1: "TAB_TYPE_GAME",
	2: "TAB_TYPE_MINI_GAME",
}
var MuseGetTopicChannelInfoResponse_TabType_value = map[string]int32{
	"TAB_TYPE_NORMAL_UNSPECIFIED": 0,
	"TAB_TYPE_GAME":               1,
	"TAB_TYPE_MINI_GAME":          2,
}

func (x MuseGetTopicChannelInfoResponse_TabType) String() string {
	return proto.EnumName(MuseGetTopicChannelInfoResponse_TabType_name, int32(x))
}
func (MuseGetTopicChannelInfoResponse_TabType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{36, 0}
}

// 获取筛选器
type GetMusicChannelFilterV2Req struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FilterType           string       `protobuf:"bytes,2,opt,name=filter_type,json=filterType,proto3" json:"filter_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMusicChannelFilterV2Req) Reset()         { *m = GetMusicChannelFilterV2Req{} }
func (m *GetMusicChannelFilterV2Req) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelFilterV2Req) ProtoMessage()    {}
func (*GetMusicChannelFilterV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{0}
}
func (m *GetMusicChannelFilterV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterV2Req.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterV2Req.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterV2Req.Merge(dst, src)
}
func (m *GetMusicChannelFilterV2Req) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterV2Req.Size(m)
}
func (m *GetMusicChannelFilterV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterV2Req proto.InternalMessageInfo

func (m *GetMusicChannelFilterV2Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMusicChannelFilterV2Req) GetFilterType() string {
	if m != nil {
		return m.FilterType
	}
	return ""
}

type GetMusicChannelFilterV2Resp struct {
	BaseResp             *app.BaseResp                             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	FilterItems          []*GetMusicChannelFilterV2Resp_FilterItem `protobuf:"bytes,2,rep,name=filter_items,json=filterItems,proto3" json:"filter_items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *GetMusicChannelFilterV2Resp) Reset()         { *m = GetMusicChannelFilterV2Resp{} }
func (m *GetMusicChannelFilterV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelFilterV2Resp) ProtoMessage()    {}
func (*GetMusicChannelFilterV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{1}
}
func (m *GetMusicChannelFilterV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterV2Resp.Merge(dst, src)
}
func (m *GetMusicChannelFilterV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp.Size(m)
}
func (m *GetMusicChannelFilterV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterV2Resp proto.InternalMessageInfo

func (m *GetMusicChannelFilterV2Resp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusicChannelFilterV2Resp) GetFilterItems() []*GetMusicChannelFilterV2Resp_FilterItem {
	if m != nil {
		return m.FilterItems
	}
	return nil
}

type GetMusicChannelFilterV2Resp_FilterItem struct {
	Title                string                                       `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	FilterItemType       string                                       `protobuf:"bytes,2,opt,name=filter_item_type,json=filterItemType,proto3" json:"filter_item_type,omitempty"`
	FilterId             string                                       `protobuf:"bytes,3,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	FilterSubItems       []*GetMusicChannelFilterV2Resp_FilterSubItem `protobuf:"bytes,4,rep,name=filter_sub_items,json=filterSubItems,proto3" json:"filter_sub_items,omitempty"`
	Tip                  string                                       `protobuf:"bytes,5,opt,name=tip,proto3" json:"tip,omitempty"`
	FilterAttrType       uint32                                       `protobuf:"varint,6,opt,name=filter_attr_type,json=filterAttrType,proto3" json:"filter_attr_type,omitempty"`
	CityTitle            *SameCityTitle                               `protobuf:"bytes,7,opt,name=city_title,json=cityTitle,proto3" json:"city_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) Reset() {
	*m = GetMusicChannelFilterV2Resp_FilterItem{}
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelFilterV2Resp_FilterItem) ProtoMessage()    {}
func (*GetMusicChannelFilterV2Resp_FilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{1, 0}
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterV2Resp_FilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.Merge(dst, src)
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.Size(m)
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem proto.InternalMessageInfo

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetFilterItemType() string {
	if m != nil {
		return m.FilterItemType
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetFilterSubItems() []*GetMusicChannelFilterV2Resp_FilterSubItem {
	if m != nil {
		return m.FilterSubItems
	}
	return nil
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetFilterAttrType() uint32 {
	if m != nil {
		return m.FilterAttrType
	}
	return 0
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetCityTitle() *SameCityTitle {
	if m != nil {
		return m.CityTitle
	}
	return nil
}

type GetMusicChannelFilterV2Resp_FilterSubItem struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	FilterSubId          string   `protobuf:"bytes,2,opt,name=filter_sub_id,json=filterSubId,proto3" json:"filter_sub_id,omitempty"`
	FilterSubItemType    string   `protobuf:"bytes,3,opt,name=filter_sub_item_type,json=filterSubItemType,proto3" json:"filter_sub_item_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicChannelFilterV2Resp_FilterSubItem) Reset() {
	*m = GetMusicChannelFilterV2Resp_FilterSubItem{}
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) String() string {
	return proto.CompactTextString(m)
}
func (*GetMusicChannelFilterV2Resp_FilterSubItem) ProtoMessage() {}
func (*GetMusicChannelFilterV2Resp_FilterSubItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{1, 1}
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.Merge(dst, src)
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.Size(m)
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem proto.InternalMessageInfo

func (m *GetMusicChannelFilterV2Resp_FilterSubItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterSubItem) GetFilterSubId() string {
	if m != nil {
		return m.FilterSubId
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterSubItem) GetFilterSubItemType() string {
	if m != nil {
		return m.FilterSubItemType
	}
	return ""
}

// 标签是同城则使用此结构体信息替换title
type SameCityTitle struct {
	CityName             string   `protobuf:"bytes,1,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	ProvinceName         string   `protobuf:"bytes,2,opt,name=province_name,json=provinceName,proto3" json:"province_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SameCityTitle) Reset()         { *m = SameCityTitle{} }
func (m *SameCityTitle) String() string { return proto.CompactTextString(m) }
func (*SameCityTitle) ProtoMessage()    {}
func (*SameCityTitle) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{2}
}
func (m *SameCityTitle) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SameCityTitle.Unmarshal(m, b)
}
func (m *SameCityTitle) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SameCityTitle.Marshal(b, m, deterministic)
}
func (dst *SameCityTitle) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SameCityTitle.Merge(dst, src)
}
func (m *SameCityTitle) XXX_Size() int {
	return xxx_messageInfo_SameCityTitle.Size(m)
}
func (m *SameCityTitle) XXX_DiscardUnknown() {
	xxx_messageInfo_SameCityTitle.DiscardUnknown(m)
}

var xxx_messageInfo_SameCityTitle proto.InternalMessageInfo

func (m *SameCityTitle) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *SameCityTitle) GetProvinceName() string {
	if m != nil {
		return m.ProvinceName
	}
	return ""
}

// 房间流请求
type ListHobbyChannelV2Req struct {
	BaseReq                *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Count                  uint32       `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Sex                    int32        `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	FilterId               string       `protobuf:"bytes,4,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	IsStartLoad            bool         `protobuf:"varint,5,opt,name=is_start_load,json=isStartLoad,proto3" json:"is_start_load,omitempty"`
	ExposeChannelIds       []uint32     `protobuf:"varint,6,rep,packed,name=expose_channel_ids,json=exposeChannelIds,proto3" json:"expose_channel_ids,omitempty"`
	FilterSubIds           []string     `protobuf:"bytes,7,rep,name=filter_sub_ids,json=filterSubIds,proto3" json:"filter_sub_ids,omitempty"`
	ChannelPackageId       string       `protobuf:"bytes,8,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	IsUserLocationAuthOpen bool         `protobuf:"varint,9,opt,name=is_user_location_auth_open,json=isUserLocationAuthOpen,proto3" json:"is_user_location_auth_open,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}     `json:"-"`
	XXX_unrecognized       []byte       `json:"-"`
	XXX_sizecache          int32        `json:"-"`
}

func (m *ListHobbyChannelV2Req) Reset()         { *m = ListHobbyChannelV2Req{} }
func (m *ListHobbyChannelV2Req) String() string { return proto.CompactTextString(m) }
func (*ListHobbyChannelV2Req) ProtoMessage()    {}
func (*ListHobbyChannelV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{3}
}
func (m *ListHobbyChannelV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListHobbyChannelV2Req.Unmarshal(m, b)
}
func (m *ListHobbyChannelV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListHobbyChannelV2Req.Marshal(b, m, deterministic)
}
func (dst *ListHobbyChannelV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListHobbyChannelV2Req.Merge(dst, src)
}
func (m *ListHobbyChannelV2Req) XXX_Size() int {
	return xxx_messageInfo_ListHobbyChannelV2Req.Size(m)
}
func (m *ListHobbyChannelV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_ListHobbyChannelV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_ListHobbyChannelV2Req proto.InternalMessageInfo

func (m *ListHobbyChannelV2Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListHobbyChannelV2Req) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ListHobbyChannelV2Req) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ListHobbyChannelV2Req) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *ListHobbyChannelV2Req) GetIsStartLoad() bool {
	if m != nil {
		return m.IsStartLoad
	}
	return false
}

func (m *ListHobbyChannelV2Req) GetExposeChannelIds() []uint32 {
	if m != nil {
		return m.ExposeChannelIds
	}
	return nil
}

func (m *ListHobbyChannelV2Req) GetFilterSubIds() []string {
	if m != nil {
		return m.FilterSubIds
	}
	return nil
}

func (m *ListHobbyChannelV2Req) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *ListHobbyChannelV2Req) GetIsUserLocationAuthOpen() bool {
	if m != nil {
		return m.IsUserLocationAuthOpen
	}
	return false
}

// 音乐首页控件
type GetMusicHomePageViewV2Req struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMusicHomePageViewV2Req) Reset()         { *m = GetMusicHomePageViewV2Req{} }
func (m *GetMusicHomePageViewV2Req) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageViewV2Req) ProtoMessage()    {}
func (*GetMusicHomePageViewV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{4}
}
func (m *GetMusicHomePageViewV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageViewV2Req.Unmarshal(m, b)
}
func (m *GetMusicHomePageViewV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageViewV2Req.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageViewV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageViewV2Req.Merge(dst, src)
}
func (m *GetMusicHomePageViewV2Req) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageViewV2Req.Size(m)
}
func (m *GetMusicHomePageViewV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageViewV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageViewV2Req proto.InternalMessageInfo

func (m *GetMusicHomePageViewV2Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetMusicHomePageViewV2Resp struct {
	BaseResp             *app.BaseResp                                     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Type                 GetMusicHomePageViewV2Resp_MusicHomePageType      `protobuf:"varint,2,opt,name=type,proto3,enum=ga.GetMusicHomePageViewV2Resp_MusicHomePageType" json:"type,omitempty"`
	FullTypeViews        []*GetMusicHomePageViewV2Resp_MusicHomePageV2View `protobuf:"bytes,3,rep,name=full_type_views,json=fullTypeViews,proto3" json:"full_type_views,omitempty"`
	ScrollTypeViews      []*GetMusicHomePageViewV2Resp_MusicHomePageV2View `protobuf:"bytes,4,rep,name=scroll_type_views,json=scrollTypeViews,proto3" json:"scroll_type_views,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-"`
	XXX_unrecognized     []byte                                            `json:"-"`
	XXX_sizecache        int32                                             `json:"-"`
}

func (m *GetMusicHomePageViewV2Resp) Reset()         { *m = GetMusicHomePageViewV2Resp{} }
func (m *GetMusicHomePageViewV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageViewV2Resp) ProtoMessage()    {}
func (*GetMusicHomePageViewV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{5}
}
func (m *GetMusicHomePageViewV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageViewV2Resp.Unmarshal(m, b)
}
func (m *GetMusicHomePageViewV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageViewV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageViewV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageViewV2Resp.Merge(dst, src)
}
func (m *GetMusicHomePageViewV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageViewV2Resp.Size(m)
}
func (m *GetMusicHomePageViewV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageViewV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageViewV2Resp proto.InternalMessageInfo

func (m *GetMusicHomePageViewV2Resp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusicHomePageViewV2Resp) GetType() GetMusicHomePageViewV2Resp_MusicHomePageType {
	if m != nil {
		return m.Type
	}
	return GetMusicHomePageViewV2Resp_MusicHomePage_Full
}

func (m *GetMusicHomePageViewV2Resp) GetFullTypeViews() []*GetMusicHomePageViewV2Resp_MusicHomePageV2View {
	if m != nil {
		return m.FullTypeViews
	}
	return nil
}

func (m *GetMusicHomePageViewV2Resp) GetScrollTypeViews() []*GetMusicHomePageViewV2Resp_MusicHomePageV2View {
	if m != nil {
		return m.ScrollTypeViews
	}
	return nil
}

type GetMusicHomePageViewV2Resp_MusicHomePageV2View struct {
	Title                string                                            `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle             string                                            `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Icon                 string                                            `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	ActionType           GetMusicHomePageViewV2Resp_ActionType             `protobuf:"varint,4,opt,name=actionType,proto3,enum=ga.GetMusicHomePageViewV2Resp_ActionType" json:"actionType,omitempty"`
	SubViews             []*GetMusicHomePageViewV2Resp_MusicHomePageV2View `protobuf:"bytes,5,rep,name=sub_views,json=subViews,proto3" json:"sub_views,omitempty"`
	QuickMatchId         string                                            `protobuf:"bytes,6,opt,name=quick_match_id,json=quickMatchId,proto3" json:"quick_match_id,omitempty"`
	Url                  string                                            `protobuf:"bytes,7,opt,name=url,proto3" json:"url,omitempty"`
	TagId                uint32                                            `protobuf:"varint,8,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	BgImg                string                                            `protobuf:"bytes,9,opt,name=bg_img,json=bgImg,proto3" json:"bg_img,omitempty"`
	ViewId               string                                            `protobuf:"bytes,10,opt,name=view_id,json=viewId,proto3" json:"view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-"`
	XXX_unrecognized     []byte                                            `json:"-"`
	XXX_sizecache        int32                                             `json:"-"`
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) Reset() {
	*m = GetMusicHomePageViewV2Resp_MusicHomePageV2View{}
}
func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) String() string {
	return proto.CompactTextString(m)
}
func (*GetMusicHomePageViewV2Resp_MusicHomePageV2View) ProtoMessage() {}
func (*GetMusicHomePageViewV2Resp_MusicHomePageV2View) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{5, 0}
}
func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageViewV2Resp_MusicHomePageV2View.Unmarshal(m, b)
}
func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageViewV2Resp_MusicHomePageV2View.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageViewV2Resp_MusicHomePageV2View) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageViewV2Resp_MusicHomePageV2View.Merge(dst, src)
}
func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageViewV2Resp_MusicHomePageV2View.Size(m)
}
func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageViewV2Resp_MusicHomePageV2View.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageViewV2Resp_MusicHomePageV2View proto.InternalMessageInfo

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetActionType() GetMusicHomePageViewV2Resp_ActionType {
	if m != nil {
		return m.ActionType
	}
	return GetMusicHomePageViewV2Resp_Sing_A_Round_Dialog
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetSubViews() []*GetMusicHomePageViewV2Resp_MusicHomePageV2View {
	if m != nil {
		return m.SubViews
	}
	return nil
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetQuickMatchId() string {
	if m != nil {
		return m.QuickMatchId
	}
	return ""
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetBgImg() string {
	if m != nil {
		return m.BgImg
	}
	return ""
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetViewId() string {
	if m != nil {
		return m.ViewId
	}
	return ""
}

// 获取对话框
type GetMusicHomePageDialogV2Req struct {
	BaseReq              *app.BaseReq            `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Type                 MusicHomePageDialogType `protobuf:"varint,2,opt,name=type,proto3,enum=ga.MusicHomePageDialogType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetMusicHomePageDialogV2Req) Reset()         { *m = GetMusicHomePageDialogV2Req{} }
func (m *GetMusicHomePageDialogV2Req) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageDialogV2Req) ProtoMessage()    {}
func (*GetMusicHomePageDialogV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{6}
}
func (m *GetMusicHomePageDialogV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageDialogV2Req.Unmarshal(m, b)
}
func (m *GetMusicHomePageDialogV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageDialogV2Req.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageDialogV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageDialogV2Req.Merge(dst, src)
}
func (m *GetMusicHomePageDialogV2Req) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageDialogV2Req.Size(m)
}
func (m *GetMusicHomePageDialogV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageDialogV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageDialogV2Req proto.InternalMessageInfo

func (m *GetMusicHomePageDialogV2Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMusicHomePageDialogV2Req) GetType() MusicHomePageDialogType {
	if m != nil {
		return m.Type
	}
	return MusicHomePageDialogType_Ktv_Dialog
}

type GetMusicHomePageDialogV2Resp struct {
	BaseResp             *app.BaseResp                              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Views                []*GetMusicHomePageDialogV2Resp_DialogView `protobuf:"bytes,2,rep,name=views,proto3" json:"views,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *GetMusicHomePageDialogV2Resp) Reset()         { *m = GetMusicHomePageDialogV2Resp{} }
func (m *GetMusicHomePageDialogV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageDialogV2Resp) ProtoMessage()    {}
func (*GetMusicHomePageDialogV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{7}
}
func (m *GetMusicHomePageDialogV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageDialogV2Resp.Unmarshal(m, b)
}
func (m *GetMusicHomePageDialogV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageDialogV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageDialogV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageDialogV2Resp.Merge(dst, src)
}
func (m *GetMusicHomePageDialogV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageDialogV2Resp.Size(m)
}
func (m *GetMusicHomePageDialogV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageDialogV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageDialogV2Resp proto.InternalMessageInfo

func (m *GetMusicHomePageDialogV2Resp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusicHomePageDialogV2Resp) GetViews() []*GetMusicHomePageDialogV2Resp_DialogView {
	if m != nil {
		return m.Views
	}
	return nil
}

type GetMusicHomePageDialogV2Resp_DialogView struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle             string   `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Image                string   `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	BackColor            string   `protobuf:"bytes,4,opt,name=back_color,json=backColor,proto3" json:"back_color,omitempty"`
	QuickMatchId         string   `protobuf:"bytes,5,opt,name=quick_match_id,json=quickMatchId,proto3" json:"quick_match_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicHomePageDialogV2Resp_DialogView) Reset() {
	*m = GetMusicHomePageDialogV2Resp_DialogView{}
}
func (m *GetMusicHomePageDialogV2Resp_DialogView) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageDialogV2Resp_DialogView) ProtoMessage()    {}
func (*GetMusicHomePageDialogV2Resp_DialogView) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{7, 0}
}
func (m *GetMusicHomePageDialogV2Resp_DialogView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageDialogV2Resp_DialogView.Unmarshal(m, b)
}
func (m *GetMusicHomePageDialogV2Resp_DialogView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageDialogV2Resp_DialogView.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageDialogV2Resp_DialogView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageDialogV2Resp_DialogView.Merge(dst, src)
}
func (m *GetMusicHomePageDialogV2Resp_DialogView) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageDialogV2Resp_DialogView.Size(m)
}
func (m *GetMusicHomePageDialogV2Resp_DialogView) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageDialogV2Resp_DialogView.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageDialogV2Resp_DialogView proto.InternalMessageInfo

func (m *GetMusicHomePageDialogV2Resp_DialogView) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicHomePageDialogV2Resp_DialogView) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *GetMusicHomePageDialogV2Resp_DialogView) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *GetMusicHomePageDialogV2Resp_DialogView) GetBackColor() string {
	if m != nil {
		return m.BackColor
	}
	return ""
}

func (m *GetMusicHomePageDialogV2Resp_DialogView) GetQuickMatchId() string {
	if m != nil {
		return m.QuickMatchId
	}
	return ""
}

// 快速匹配
type QuickMatchHobbyChannelV2Req struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	QuickMatchId         string       `protobuf:"bytes,2,opt,name=quick_match_id,json=quickMatchId,proto3" json:"quick_match_id,omitempty"`
	ChannelPackageId     string       `protobuf:"bytes,3,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *QuickMatchHobbyChannelV2Req) Reset()         { *m = QuickMatchHobbyChannelV2Req{} }
func (m *QuickMatchHobbyChannelV2Req) String() string { return proto.CompactTextString(m) }
func (*QuickMatchHobbyChannelV2Req) ProtoMessage()    {}
func (*QuickMatchHobbyChannelV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{8}
}
func (m *QuickMatchHobbyChannelV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickMatchHobbyChannelV2Req.Unmarshal(m, b)
}
func (m *QuickMatchHobbyChannelV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickMatchHobbyChannelV2Req.Marshal(b, m, deterministic)
}
func (dst *QuickMatchHobbyChannelV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickMatchHobbyChannelV2Req.Merge(dst, src)
}
func (m *QuickMatchHobbyChannelV2Req) XXX_Size() int {
	return xxx_messageInfo_QuickMatchHobbyChannelV2Req.Size(m)
}
func (m *QuickMatchHobbyChannelV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickMatchHobbyChannelV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_QuickMatchHobbyChannelV2Req proto.InternalMessageInfo

func (m *QuickMatchHobbyChannelV2Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *QuickMatchHobbyChannelV2Req) GetQuickMatchId() string {
	if m != nil {
		return m.QuickMatchId
	}
	return ""
}

func (m *QuickMatchHobbyChannelV2Req) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

type QuickMatchHobbyChannelV2Resp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Footprint            string        `protobuf:"bytes,3,opt,name=footprint,proto3" json:"footprint,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *QuickMatchHobbyChannelV2Resp) Reset()         { *m = QuickMatchHobbyChannelV2Resp{} }
func (m *QuickMatchHobbyChannelV2Resp) String() string { return proto.CompactTextString(m) }
func (*QuickMatchHobbyChannelV2Resp) ProtoMessage()    {}
func (*QuickMatchHobbyChannelV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{9}
}
func (m *QuickMatchHobbyChannelV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickMatchHobbyChannelV2Resp.Unmarshal(m, b)
}
func (m *QuickMatchHobbyChannelV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickMatchHobbyChannelV2Resp.Marshal(b, m, deterministic)
}
func (dst *QuickMatchHobbyChannelV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickMatchHobbyChannelV2Resp.Merge(dst, src)
}
func (m *QuickMatchHobbyChannelV2Resp) XXX_Size() int {
	return xxx_messageInfo_QuickMatchHobbyChannelV2Resp.Size(m)
}
func (m *QuickMatchHobbyChannelV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickMatchHobbyChannelV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_QuickMatchHobbyChannelV2Resp proto.InternalMessageInfo

func (m *QuickMatchHobbyChannelV2Resp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *QuickMatchHobbyChannelV2Resp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *QuickMatchHobbyChannelV2Resp) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

// 重逢互动 push
type ReunionInteractionPush struct {
	JoinUser             *ReunionUser `protobuf:"bytes,1,opt,name=join_user,json=joinUser,proto3" json:"join_user,omitempty"`
	ReunionUser          *ReunionUser `protobuf:"bytes,2,opt,name=reunion_user,json=reunionUser,proto3" json:"reunion_user,omitempty"`
	Text                 string       `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	MicText              string       `protobuf:"bytes,4,opt,name=mic_text,json=micText,proto3" json:"mic_text,omitempty"`
	MetaId               string       `protobuf:"bytes,5,opt,name=meta_id,json=metaId,proto3" json:"meta_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ReunionInteractionPush) Reset()         { *m = ReunionInteractionPush{} }
func (m *ReunionInteractionPush) String() string { return proto.CompactTextString(m) }
func (*ReunionInteractionPush) ProtoMessage()    {}
func (*ReunionInteractionPush) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{10}
}
func (m *ReunionInteractionPush) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReunionInteractionPush.Unmarshal(m, b)
}
func (m *ReunionInteractionPush) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReunionInteractionPush.Marshal(b, m, deterministic)
}
func (dst *ReunionInteractionPush) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReunionInteractionPush.Merge(dst, src)
}
func (m *ReunionInteractionPush) XXX_Size() int {
	return xxx_messageInfo_ReunionInteractionPush.Size(m)
}
func (m *ReunionInteractionPush) XXX_DiscardUnknown() {
	xxx_messageInfo_ReunionInteractionPush.DiscardUnknown(m)
}

var xxx_messageInfo_ReunionInteractionPush proto.InternalMessageInfo

func (m *ReunionInteractionPush) GetJoinUser() *ReunionUser {
	if m != nil {
		return m.JoinUser
	}
	return nil
}

func (m *ReunionInteractionPush) GetReunionUser() *ReunionUser {
	if m != nil {
		return m.ReunionUser
	}
	return nil
}

func (m *ReunionInteractionPush) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ReunionInteractionPush) GetMicText() string {
	if m != nil {
		return m.MicText
	}
	return ""
}

func (m *ReunionInteractionPush) GetMetaId() string {
	if m != nil {
		return m.MetaId
	}
	return ""
}

type ReunionUser struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Username             string   `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReunionUser) Reset()         { *m = ReunionUser{} }
func (m *ReunionUser) String() string { return proto.CompactTextString(m) }
func (*ReunionUser) ProtoMessage()    {}
func (*ReunionUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{11}
}
func (m *ReunionUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReunionUser.Unmarshal(m, b)
}
func (m *ReunionUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReunionUser.Marshal(b, m, deterministic)
}
func (dst *ReunionUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReunionUser.Merge(dst, src)
}
func (m *ReunionUser) XXX_Size() int {
	return xxx_messageInfo_ReunionUser.Size(m)
}
func (m *ReunionUser) XXX_DiscardUnknown() {
	xxx_messageInfo_ReunionUser.DiscardUnknown(m)
}

var xxx_messageInfo_ReunionUser proto.InternalMessageInfo

func (m *ReunionUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReunionUser) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *ReunionUser) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ReunionUser) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

// 发布房间
type PublishMusicChannelReq struct {
	BaseReq              *app.BaseReq                 `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32                       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions         []*topic_channel.BlockOption `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	ChannelName          string                       `protobuf:"bytes,5,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ShowGeoInfo          bool                         `protobuf:"varint,6,opt,name=show_geo_info,json=showGeoInfo,proto3" json:"show_geo_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *PublishMusicChannelReq) Reset()         { *m = PublishMusicChannelReq{} }
func (m *PublishMusicChannelReq) String() string { return proto.CompactTextString(m) }
func (*PublishMusicChannelReq) ProtoMessage()    {}
func (*PublishMusicChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{12}
}
func (m *PublishMusicChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishMusicChannelReq.Unmarshal(m, b)
}
func (m *PublishMusicChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishMusicChannelReq.Marshal(b, m, deterministic)
}
func (dst *PublishMusicChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishMusicChannelReq.Merge(dst, src)
}
func (m *PublishMusicChannelReq) XXX_Size() int {
	return xxx_messageInfo_PublishMusicChannelReq.Size(m)
}
func (m *PublishMusicChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishMusicChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_PublishMusicChannelReq proto.InternalMessageInfo

func (m *PublishMusicChannelReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PublishMusicChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PublishMusicChannelReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *PublishMusicChannelReq) GetBlockOptions() []*topic_channel.BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *PublishMusicChannelReq) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *PublishMusicChannelReq) GetShowGeoInfo() bool {
	if m != nil {
		return m.ShowGeoInfo
	}
	return false
}

type PublishMusicChannelResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChangeCoolDown       uint32        `protobuf:"varint,2,opt,name=change_cool_down,json=changeCoolDown,proto3" json:"change_cool_down,omitempty"`
	FreezeDuration       uint32        `protobuf:"varint,3,opt,name=freeze_duration,json=freezeDuration,proto3" json:"freeze_duration,omitempty"`
	AutoDismissDuration  uint32        `protobuf:"varint,4,opt,name=auto_dismiss_duration,json=autoDismissDuration,proto3" json:"auto_dismiss_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PublishMusicChannelResp) Reset()         { *m = PublishMusicChannelResp{} }
func (m *PublishMusicChannelResp) String() string { return proto.CompactTextString(m) }
func (*PublishMusicChannelResp) ProtoMessage()    {}
func (*PublishMusicChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{13}
}
func (m *PublishMusicChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishMusicChannelResp.Unmarshal(m, b)
}
func (m *PublishMusicChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishMusicChannelResp.Marshal(b, m, deterministic)
}
func (dst *PublishMusicChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishMusicChannelResp.Merge(dst, src)
}
func (m *PublishMusicChannelResp) XXX_Size() int {
	return xxx_messageInfo_PublishMusicChannelResp.Size(m)
}
func (m *PublishMusicChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishMusicChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_PublishMusicChannelResp proto.InternalMessageInfo

func (m *PublishMusicChannelResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PublishMusicChannelResp) GetChangeCoolDown() uint32 {
	if m != nil {
		return m.ChangeCoolDown
	}
	return 0
}

func (m *PublishMusicChannelResp) GetFreezeDuration() uint32 {
	if m != nil {
		return m.FreezeDuration
	}
	return 0
}

func (m *PublishMusicChannelResp) GetAutoDismissDuration() uint32 {
	if m != nil {
		return m.AutoDismissDuration
	}
	return 0
}

// 房间发布取消
type CancelMusicChannelPublishReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CancelMusicChannelPublishReq) Reset()         { *m = CancelMusicChannelPublishReq{} }
func (m *CancelMusicChannelPublishReq) String() string { return proto.CompactTextString(m) }
func (*CancelMusicChannelPublishReq) ProtoMessage()    {}
func (*CancelMusicChannelPublishReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{14}
}
func (m *CancelMusicChannelPublishReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelMusicChannelPublishReq.Unmarshal(m, b)
}
func (m *CancelMusicChannelPublishReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelMusicChannelPublishReq.Marshal(b, m, deterministic)
}
func (dst *CancelMusicChannelPublishReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelMusicChannelPublishReq.Merge(dst, src)
}
func (m *CancelMusicChannelPublishReq) XXX_Size() int {
	return xxx_messageInfo_CancelMusicChannelPublishReq.Size(m)
}
func (m *CancelMusicChannelPublishReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelMusicChannelPublishReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelMusicChannelPublishReq proto.InternalMessageInfo

func (m *CancelMusicChannelPublishReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CancelMusicChannelPublishReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CancelMusicChannelPublishResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CancelMusicChannelPublishResp) Reset()         { *m = CancelMusicChannelPublishResp{} }
func (m *CancelMusicChannelPublishResp) String() string { return proto.CompactTextString(m) }
func (*CancelMusicChannelPublishResp) ProtoMessage()    {}
func (*CancelMusicChannelPublishResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{15}
}
func (m *CancelMusicChannelPublishResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelMusicChannelPublishResp.Unmarshal(m, b)
}
func (m *CancelMusicChannelPublishResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelMusicChannelPublishResp.Marshal(b, m, deterministic)
}
func (dst *CancelMusicChannelPublishResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelMusicChannelPublishResp.Merge(dst, src)
}
func (m *CancelMusicChannelPublishResp) XXX_Size() int {
	return xxx_messageInfo_CancelMusicChannelPublishResp.Size(m)
}
func (m *CancelMusicChannelPublishResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelMusicChannelPublishResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelMusicChannelPublishResp proto.InternalMessageInfo

func (m *CancelMusicChannelPublishResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetMusicFilterItemByIdsReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FilterIds            []string     `protobuf:"bytes,2,rep,name=filter_ids,json=filterIds,proto3" json:"filter_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMusicFilterItemByIdsReq) Reset()         { *m = GetMusicFilterItemByIdsReq{} }
func (m *GetMusicFilterItemByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicFilterItemByIdsReq) ProtoMessage()    {}
func (*GetMusicFilterItemByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{16}
}
func (m *GetMusicFilterItemByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicFilterItemByIdsReq.Unmarshal(m, b)
}
func (m *GetMusicFilterItemByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicFilterItemByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicFilterItemByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicFilterItemByIdsReq.Merge(dst, src)
}
func (m *GetMusicFilterItemByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicFilterItemByIdsReq.Size(m)
}
func (m *GetMusicFilterItemByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicFilterItemByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicFilterItemByIdsReq proto.InternalMessageInfo

func (m *GetMusicFilterItemByIdsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMusicFilterItemByIdsReq) GetFilterIds() []string {
	if m != nil {
		return m.FilterIds
	}
	return nil
}

type GetMusicFilterItemByIdsResp struct {
	BaseResp             *app.BaseResp               `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	FilterMap            map[string]*MusicFilterItem `protobuf:"bytes,2,rep,name=filter_map,json=filterMap,proto3" json:"filter_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetMusicFilterItemByIdsResp) Reset()         { *m = GetMusicFilterItemByIdsResp{} }
func (m *GetMusicFilterItemByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicFilterItemByIdsResp) ProtoMessage()    {}
func (*GetMusicFilterItemByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{17}
}
func (m *GetMusicFilterItemByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicFilterItemByIdsResp.Unmarshal(m, b)
}
func (m *GetMusicFilterItemByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicFilterItemByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicFilterItemByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicFilterItemByIdsResp.Merge(dst, src)
}
func (m *GetMusicFilterItemByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicFilterItemByIdsResp.Size(m)
}
func (m *GetMusicFilterItemByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicFilterItemByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicFilterItemByIdsResp proto.InternalMessageInfo

func (m *GetMusicFilterItemByIdsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusicFilterItemByIdsResp) GetFilterMap() map[string]*MusicFilterItem {
	if m != nil {
		return m.FilterMap
	}
	return nil
}

type MusicFilterItem struct {
	FilterId             string             `protobuf:"bytes,1,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	Name                 string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string             `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Images               []string           `protobuf:"bytes,4,rep,name=images,proto3" json:"images,omitempty"`
	SubFilters           []*MusicFilterItem `protobuf:"bytes,5,rep,name=sub_filters,json=subFilters,proto3" json:"sub_filters,omitempty"`
	FilterAttrType       uint32             `protobuf:"varint,6,opt,name=filter_attr_type,json=filterAttrType,proto3" json:"filter_attr_type,omitempty"`
	CityTitle            *SameCityTitle     `protobuf:"bytes,7,opt,name=city_title,json=cityTitle,proto3" json:"city_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MusicFilterItem) Reset()         { *m = MusicFilterItem{} }
func (m *MusicFilterItem) String() string { return proto.CompactTextString(m) }
func (*MusicFilterItem) ProtoMessage()    {}
func (*MusicFilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{18}
}
func (m *MusicFilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicFilterItem.Unmarshal(m, b)
}
func (m *MusicFilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicFilterItem.Marshal(b, m, deterministic)
}
func (dst *MusicFilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicFilterItem.Merge(dst, src)
}
func (m *MusicFilterItem) XXX_Size() int {
	return xxx_messageInfo_MusicFilterItem.Size(m)
}
func (m *MusicFilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicFilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_MusicFilterItem proto.InternalMessageInfo

func (m *MusicFilterItem) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *MusicFilterItem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MusicFilterItem) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *MusicFilterItem) GetImages() []string {
	if m != nil {
		return m.Images
	}
	return nil
}

func (m *MusicFilterItem) GetSubFilters() []*MusicFilterItem {
	if m != nil {
		return m.SubFilters
	}
	return nil
}

func (m *MusicFilterItem) GetFilterAttrType() uint32 {
	if m != nil {
		return m.FilterAttrType
	}
	return 0
}

func (m *MusicFilterItem) GetCityTitle() *SameCityTitle {
	if m != nil {
		return m.CityTitle
	}
	return nil
}

type ListMusicChannelsReq struct {
	BaseReq                *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FilterId               string       `protobuf:"bytes,2,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	SubFilterIds           []string     `protobuf:"bytes,3,rep,name=sub_filter_ids,json=subFilterIds,proto3" json:"sub_filter_ids,omitempty"`
	Count                  uint32       `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	IsStartLoad            bool         `protobuf:"varint,5,opt,name=is_start_load,json=isStartLoad,proto3" json:"is_start_load,omitempty"`
	ExposeChannelIds       []uint32     `protobuf:"varint,6,rep,packed,name=expose_channel_ids,json=exposeChannelIds,proto3" json:"expose_channel_ids,omitempty"`
	ChannelPackageId       string       `protobuf:"bytes,7,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	IsUserLocationAuthOpen bool         `protobuf:"varint,8,opt,name=is_user_location_auth_open,json=isUserLocationAuthOpen,proto3" json:"is_user_location_auth_open,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}     `json:"-"`
	XXX_unrecognized       []byte       `json:"-"`
	XXX_sizecache          int32        `json:"-"`
}

func (m *ListMusicChannelsReq) Reset()         { *m = ListMusicChannelsReq{} }
func (m *ListMusicChannelsReq) String() string { return proto.CompactTextString(m) }
func (*ListMusicChannelsReq) ProtoMessage()    {}
func (*ListMusicChannelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{19}
}
func (m *ListMusicChannelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicChannelsReq.Unmarshal(m, b)
}
func (m *ListMusicChannelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicChannelsReq.Marshal(b, m, deterministic)
}
func (dst *ListMusicChannelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicChannelsReq.Merge(dst, src)
}
func (m *ListMusicChannelsReq) XXX_Size() int {
	return xxx_messageInfo_ListMusicChannelsReq.Size(m)
}
func (m *ListMusicChannelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicChannelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicChannelsReq proto.InternalMessageInfo

func (m *ListMusicChannelsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListMusicChannelsReq) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *ListMusicChannelsReq) GetSubFilterIds() []string {
	if m != nil {
		return m.SubFilterIds
	}
	return nil
}

func (m *ListMusicChannelsReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ListMusicChannelsReq) GetIsStartLoad() bool {
	if m != nil {
		return m.IsStartLoad
	}
	return false
}

func (m *ListMusicChannelsReq) GetExposeChannelIds() []uint32 {
	if m != nil {
		return m.ExposeChannelIds
	}
	return nil
}

func (m *ListMusicChannelsReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *ListMusicChannelsReq) GetIsUserLocationAuthOpen() bool {
	if m != nil {
		return m.IsUserLocationAuthOpen
	}
	return false
}

type ListMusicChannelsResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Channels             []*MusicChannel `protobuf:"bytes,2,rep,name=channels,proto3" json:"channels,omitempty"`
	IsBottomReach        bool            `protobuf:"varint,3,opt,name=is_bottom_reach,json=isBottomReach,proto3" json:"is_bottom_reach,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListMusicChannelsResp) Reset()         { *m = ListMusicChannelsResp{} }
func (m *ListMusicChannelsResp) String() string { return proto.CompactTextString(m) }
func (*ListMusicChannelsResp) ProtoMessage()    {}
func (*ListMusicChannelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{20}
}
func (m *ListMusicChannelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicChannelsResp.Unmarshal(m, b)
}
func (m *ListMusicChannelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicChannelsResp.Marshal(b, m, deterministic)
}
func (dst *ListMusicChannelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicChannelsResp.Merge(dst, src)
}
func (m *ListMusicChannelsResp) XXX_Size() int {
	return xxx_messageInfo_ListMusicChannelsResp.Size(m)
}
func (m *ListMusicChannelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicChannelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicChannelsResp proto.InternalMessageInfo

func (m *ListMusicChannelsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListMusicChannelsResp) GetChannels() []*MusicChannel {
	if m != nil {
		return m.Channels
	}
	return nil
}

func (m *ListMusicChannelsResp) GetIsBottomReach() bool {
	if m != nil {
		return m.IsBottomReach
	}
	return false
}

type MusicChannel struct {
	ChannelId          uint32                    `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName        string                    `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelMemberCount uint32                    `protobuf:"varint,3,opt,name=channel_member_count,json=channelMemberCount,proto3" json:"channel_member_count,omitempty"`
	TabIcon            string                    `protobuf:"bytes,4,opt,name=tab_icon,json=tabIcon,proto3" json:"tab_icon,omitempty"`
	TabDesc            string                    `protobuf:"bytes,5,opt,name=tab_desc,json=tabDesc,proto3" json:"tab_desc,omitempty"`
	OwnerAccount       string                    `protobuf:"bytes,6,opt,name=owner_account,json=ownerAccount,proto3" json:"owner_account,omitempty"`
	OwnerSex           int32                     `protobuf:"varint,7,opt,name=owner_sex,json=ownerSex,proto3" json:"owner_sex,omitempty"`
	Accounts           []string                  `protobuf:"bytes,8,rep,name=accounts,proto3" json:"accounts,omitempty"`
	Status             string                    `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
	Song               string                    `protobuf:"bytes,10,opt,name=song,proto3" json:"song,omitempty"`
	Review             *MusicChannelReview       `protobuf:"bytes,11,opt,name=review,proto3" json:"review,omitempty"`
	Label              MusicChannelLabel         `protobuf:"varint,12,opt,name=label,proto3,enum=ga.MusicChannelLabel" json:"label,omitempty"`
	Glory              *KtvGlory                 `protobuf:"bytes,13,opt,name=glory,proto3" json:"glory,omitempty"`
	PersonalCert       *MusicChannelPersonalCert `protobuf:"bytes,14,opt,name=personal_cert,json=personalCert,proto3" json:"personal_cert,omitempty"`
	// 非业务必须字段，埋点需要
	Footprint      string `protobuf:"bytes,15,opt,name=footprint,proto3" json:"footprint,omitempty"`
	TabId          uint32 `protobuf:"varint,16,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	RegionId       uint64 `protobuf:"varint,17,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	ChannelLevelId uint32 `protobuf:"varint,18,opt,name=channel_level_id,json=channelLevelId,proto3" json:"channel_level_id,omitempty"`
	// 非业务必须字段，埋点需要
	TabName              string                                            `protobuf:"bytes,19,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Pia                  *MusicPia                                         `protobuf:"bytes,20,opt,name=pia,proto3" json:"pia,omitempty"`
	Interesting          *MusicInteresting                                 `protobuf:"bytes,21,opt,name=interesting,proto3" json:"interesting,omitempty"`
	Logo                 string                                            `protobuf:"bytes,22,opt,name=logo,proto3" json:"logo,omitempty"`
	MusicSocial          *MusicSocial                                      `protobuf:"bytes,23,opt,name=music_social,json=musicSocial,proto3" json:"music_social,omitempty"`
	SameCity             *muse_interest_hub_logic.TopicChannelSameCityInfo `protobuf:"bytes,24,opt,name=same_city,json=sameCity,proto3" json:"same_city,omitempty"`
	ChannelType          uint32                                            `protobuf:"varint,25,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Topic                string                                            `protobuf:"bytes,26,opt,name=topic,proto3" json:"topic,omitempty"`
	TopicIcon            string                                            `protobuf:"bytes,27,opt,name=topic_icon,json=topicIcon,proto3" json:"topic_icon,omitempty"`
	TopicType            int32                                             `protobuf:"varint,28,opt,name=topic_type,json=topicType,proto3" json:"topic_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-"`
	XXX_unrecognized     []byte                                            `json:"-"`
	XXX_sizecache        int32                                             `json:"-"`
}

func (m *MusicChannel) Reset()         { *m = MusicChannel{} }
func (m *MusicChannel) String() string { return proto.CompactTextString(m) }
func (*MusicChannel) ProtoMessage()    {}
func (*MusicChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{21}
}
func (m *MusicChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicChannel.Unmarshal(m, b)
}
func (m *MusicChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicChannel.Marshal(b, m, deterministic)
}
func (dst *MusicChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicChannel.Merge(dst, src)
}
func (m *MusicChannel) XXX_Size() int {
	return xxx_messageInfo_MusicChannel.Size(m)
}
func (m *MusicChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicChannel.DiscardUnknown(m)
}

var xxx_messageInfo_MusicChannel proto.InternalMessageInfo

func (m *MusicChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MusicChannel) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *MusicChannel) GetChannelMemberCount() uint32 {
	if m != nil {
		return m.ChannelMemberCount
	}
	return 0
}

func (m *MusicChannel) GetTabIcon() string {
	if m != nil {
		return m.TabIcon
	}
	return ""
}

func (m *MusicChannel) GetTabDesc() string {
	if m != nil {
		return m.TabDesc
	}
	return ""
}

func (m *MusicChannel) GetOwnerAccount() string {
	if m != nil {
		return m.OwnerAccount
	}
	return ""
}

func (m *MusicChannel) GetOwnerSex() int32 {
	if m != nil {
		return m.OwnerSex
	}
	return 0
}

func (m *MusicChannel) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *MusicChannel) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *MusicChannel) GetSong() string {
	if m != nil {
		return m.Song
	}
	return ""
}

func (m *MusicChannel) GetReview() *MusicChannelReview {
	if m != nil {
		return m.Review
	}
	return nil
}

func (m *MusicChannel) GetLabel() MusicChannelLabel {
	if m != nil {
		return m.Label
	}
	return MusicChannelLabel_MusicChannelLabelNone
}

func (m *MusicChannel) GetGlory() *KtvGlory {
	if m != nil {
		return m.Glory
	}
	return nil
}

func (m *MusicChannel) GetPersonalCert() *MusicChannelPersonalCert {
	if m != nil {
		return m.PersonalCert
	}
	return nil
}

func (m *MusicChannel) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

func (m *MusicChannel) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MusicChannel) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *MusicChannel) GetChannelLevelId() uint32 {
	if m != nil {
		return m.ChannelLevelId
	}
	return 0
}

func (m *MusicChannel) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *MusicChannel) GetPia() *MusicPia {
	if m != nil {
		return m.Pia
	}
	return nil
}

func (m *MusicChannel) GetInteresting() *MusicInteresting {
	if m != nil {
		return m.Interesting
	}
	return nil
}

func (m *MusicChannel) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *MusicChannel) GetMusicSocial() *MusicSocial {
	if m != nil {
		return m.MusicSocial
	}
	return nil
}

func (m *MusicChannel) GetSameCity() *muse_interest_hub_logic.TopicChannelSameCityInfo {
	if m != nil {
		return m.SameCity
	}
	return nil
}

func (m *MusicChannel) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *MusicChannel) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *MusicChannel) GetTopicIcon() string {
	if m != nil {
		return m.TopicIcon
	}
	return ""
}

func (m *MusicChannel) GetTopicType() int32 {
	if m != nil {
		return m.TopicType
	}
	return 0
}

type MusicSocialRankHonorSignInfo struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	StyleColorList       []string `protobuf:"bytes,2,rep,name=style_color_list,json=styleColorList,proto3" json:"style_color_list,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicSocialRankHonorSignInfo) Reset()         { *m = MusicSocialRankHonorSignInfo{} }
func (m *MusicSocialRankHonorSignInfo) String() string { return proto.CompactTextString(m) }
func (*MusicSocialRankHonorSignInfo) ProtoMessage()    {}
func (*MusicSocialRankHonorSignInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{22}
}
func (m *MusicSocialRankHonorSignInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicSocialRankHonorSignInfo.Unmarshal(m, b)
}
func (m *MusicSocialRankHonorSignInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicSocialRankHonorSignInfo.Marshal(b, m, deterministic)
}
func (dst *MusicSocialRankHonorSignInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicSocialRankHonorSignInfo.Merge(dst, src)
}
func (m *MusicSocialRankHonorSignInfo) XXX_Size() int {
	return xxx_messageInfo_MusicSocialRankHonorSignInfo.Size(m)
}
func (m *MusicSocialRankHonorSignInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicSocialRankHonorSignInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MusicSocialRankHonorSignInfo proto.InternalMessageInfo

func (m *MusicSocialRankHonorSignInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *MusicSocialRankHonorSignInfo) GetStyleColorList() []string {
	if m != nil {
		return m.StyleColorList
	}
	return nil
}

func (m *MusicSocialRankHonorSignInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type MusicSocial struct {
	MemberLabelBg        string                        `protobuf:"bytes,2,opt,name=member_label_bg,json=memberLabelBg,proto3" json:"member_label_bg,omitempty"`
	MemberLabelText      string                        `protobuf:"bytes,3,opt,name=member_label_text,json=memberLabelText,proto3" json:"member_label_text,omitempty"`
	RankSignInfo         *MusicSocialRankHonorSignInfo `protobuf:"bytes,4,opt,name=rank_sign_info,json=rankSignInfo,proto3" json:"rank_sign_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *MusicSocial) Reset()         { *m = MusicSocial{} }
func (m *MusicSocial) String() string { return proto.CompactTextString(m) }
func (*MusicSocial) ProtoMessage()    {}
func (*MusicSocial) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{23}
}
func (m *MusicSocial) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicSocial.Unmarshal(m, b)
}
func (m *MusicSocial) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicSocial.Marshal(b, m, deterministic)
}
func (dst *MusicSocial) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicSocial.Merge(dst, src)
}
func (m *MusicSocial) XXX_Size() int {
	return xxx_messageInfo_MusicSocial.Size(m)
}
func (m *MusicSocial) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicSocial.DiscardUnknown(m)
}

var xxx_messageInfo_MusicSocial proto.InternalMessageInfo

func (m *MusicSocial) GetMemberLabelBg() string {
	if m != nil {
		return m.MemberLabelBg
	}
	return ""
}

func (m *MusicSocial) GetMemberLabelText() string {
	if m != nil {
		return m.MemberLabelText
	}
	return ""
}

func (m *MusicSocial) GetRankSignInfo() *MusicSocialRankHonorSignInfo {
	if m != nil {
		return m.RankSignInfo
	}
	return nil
}

type MusicPia struct {
	Label                []string `protobuf:"bytes,1,rep,name=label,proto3" json:"label,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicPia) Reset()         { *m = MusicPia{} }
func (m *MusicPia) String() string { return proto.CompactTextString(m) }
func (*MusicPia) ProtoMessage()    {}
func (*MusicPia) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{24}
}
func (m *MusicPia) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicPia.Unmarshal(m, b)
}
func (m *MusicPia) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicPia.Marshal(b, m, deterministic)
}
func (dst *MusicPia) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicPia.Merge(dst, src)
}
func (m *MusicPia) XXX_Size() int {
	return xxx_messageInfo_MusicPia.Size(m)
}
func (m *MusicPia) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicPia.DiscardUnknown(m)
}

var xxx_messageInfo_MusicPia proto.InternalMessageInfo

func (m *MusicPia) GetLabel() []string {
	if m != nil {
		return m.Label
	}
	return nil
}

func (m *MusicPia) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type MusicInteresting struct {
	Topic                string   `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicInteresting) Reset()         { *m = MusicInteresting{} }
func (m *MusicInteresting) String() string { return proto.CompactTextString(m) }
func (*MusicInteresting) ProtoMessage()    {}
func (*MusicInteresting) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{25}
}
func (m *MusicInteresting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicInteresting.Unmarshal(m, b)
}
func (m *MusicInteresting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicInteresting.Marshal(b, m, deterministic)
}
func (dst *MusicInteresting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicInteresting.Merge(dst, src)
}
func (m *MusicInteresting) XXX_Size() int {
	return xxx_messageInfo_MusicInteresting.Size(m)
}
func (m *MusicInteresting) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicInteresting.DiscardUnknown(m)
}

var xxx_messageInfo_MusicInteresting proto.InternalMessageInfo

func (m *MusicInteresting) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

// 重逢
type MusicChannelReview struct {
	ReviewAccount        string   `protobuf:"bytes,1,opt,name=review_account,json=reviewAccount,proto3" json:"review_account,omitempty"`
	ReviewDesc           string   `protobuf:"bytes,2,opt,name=review_desc,json=reviewDesc,proto3" json:"review_desc,omitempty"`
	ReviewSex            int32    `protobuf:"varint,3,opt,name=review_sex,json=reviewSex,proto3" json:"review_sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicChannelReview) Reset()         { *m = MusicChannelReview{} }
func (m *MusicChannelReview) String() string { return proto.CompactTextString(m) }
func (*MusicChannelReview) ProtoMessage()    {}
func (*MusicChannelReview) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{26}
}
func (m *MusicChannelReview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicChannelReview.Unmarshal(m, b)
}
func (m *MusicChannelReview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicChannelReview.Marshal(b, m, deterministic)
}
func (dst *MusicChannelReview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicChannelReview.Merge(dst, src)
}
func (m *MusicChannelReview) XXX_Size() int {
	return xxx_messageInfo_MusicChannelReview.Size(m)
}
func (m *MusicChannelReview) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicChannelReview.DiscardUnknown(m)
}

var xxx_messageInfo_MusicChannelReview proto.InternalMessageInfo

func (m *MusicChannelReview) GetReviewAccount() string {
	if m != nil {
		return m.ReviewAccount
	}
	return ""
}

func (m *MusicChannelReview) GetReviewDesc() string {
	if m != nil {
		return m.ReviewDesc
	}
	return ""
}

func (m *MusicChannelReview) GetReviewSex() int32 {
	if m != nil {
		return m.ReviewSex
	}
	return 0
}

type KtvGlory struct {
	GloryName            string   `protobuf:"bytes,1,opt,name=glory_name,json=gloryName,proto3" json:"glory_name,omitempty"`
	GloryImg             string   `protobuf:"bytes,2,opt,name=glory_img,json=gloryImg,proto3" json:"glory_img,omitempty"`
	GloryBgImg           string   `protobuf:"bytes,3,opt,name=glory_bg_img,json=gloryBgImg,proto3" json:"glory_bg_img,omitempty"`
	GloryRank            uint32   `protobuf:"varint,4,opt,name=glory_rank,json=gloryRank,proto3" json:"glory_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KtvGlory) Reset()         { *m = KtvGlory{} }
func (m *KtvGlory) String() string { return proto.CompactTextString(m) }
func (*KtvGlory) ProtoMessage()    {}
func (*KtvGlory) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{27}
}
func (m *KtvGlory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KtvGlory.Unmarshal(m, b)
}
func (m *KtvGlory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KtvGlory.Marshal(b, m, deterministic)
}
func (dst *KtvGlory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KtvGlory.Merge(dst, src)
}
func (m *KtvGlory) XXX_Size() int {
	return xxx_messageInfo_KtvGlory.Size(m)
}
func (m *KtvGlory) XXX_DiscardUnknown() {
	xxx_messageInfo_KtvGlory.DiscardUnknown(m)
}

var xxx_messageInfo_KtvGlory proto.InternalMessageInfo

func (m *KtvGlory) GetGloryName() string {
	if m != nil {
		return m.GloryName
	}
	return ""
}

func (m *KtvGlory) GetGloryImg() string {
	if m != nil {
		return m.GloryImg
	}
	return ""
}

func (m *KtvGlory) GetGloryBgImg() string {
	if m != nil {
		return m.GloryBgImg
	}
	return ""
}

func (m *KtvGlory) GetGloryRank() uint32 {
	if m != nil {
		return m.GloryRank
	}
	return 0
}

type MusicChannelPersonalCert struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Color                []string `protobuf:"bytes,3,rep,name=color,proto3" json:"color,omitempty"`
	TextShadowColor      string   `protobuf:"bytes,4,opt,name=text_shadow_color,json=textShadowColor,proto3" json:"text_shadow_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicChannelPersonalCert) Reset()         { *m = MusicChannelPersonalCert{} }
func (m *MusicChannelPersonalCert) String() string { return proto.CompactTextString(m) }
func (*MusicChannelPersonalCert) ProtoMessage()    {}
func (*MusicChannelPersonalCert) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{28}
}
func (m *MusicChannelPersonalCert) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicChannelPersonalCert.Unmarshal(m, b)
}
func (m *MusicChannelPersonalCert) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicChannelPersonalCert.Marshal(b, m, deterministic)
}
func (dst *MusicChannelPersonalCert) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicChannelPersonalCert.Merge(dst, src)
}
func (m *MusicChannelPersonalCert) XXX_Size() int {
	return xxx_messageInfo_MusicChannelPersonalCert.Size(m)
}
func (m *MusicChannelPersonalCert) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicChannelPersonalCert.DiscardUnknown(m)
}

var xxx_messageInfo_MusicChannelPersonalCert proto.InternalMessageInfo

func (m *MusicChannelPersonalCert) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *MusicChannelPersonalCert) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *MusicChannelPersonalCert) GetColor() []string {
	if m != nil {
		return m.Color
	}
	return nil
}

func (m *MusicChannelPersonalCert) GetTextShadowColor() string {
	if m != nil {
		return m.TextShadowColor
	}
	return ""
}

type GetTabPublishHotRcmdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetTabPublishHotRcmdReq) Reset()         { *m = GetTabPublishHotRcmdReq{} }
func (m *GetTabPublishHotRcmdReq) String() string { return proto.CompactTextString(m) }
func (*GetTabPublishHotRcmdReq) ProtoMessage()    {}
func (*GetTabPublishHotRcmdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{29}
}
func (m *GetTabPublishHotRcmdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabPublishHotRcmdReq.Unmarshal(m, b)
}
func (m *GetTabPublishHotRcmdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabPublishHotRcmdReq.Marshal(b, m, deterministic)
}
func (dst *GetTabPublishHotRcmdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabPublishHotRcmdReq.Merge(dst, src)
}
func (m *GetTabPublishHotRcmdReq) XXX_Size() int {
	return xxx_messageInfo_GetTabPublishHotRcmdReq.Size(m)
}
func (m *GetTabPublishHotRcmdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabPublishHotRcmdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabPublishHotRcmdReq proto.InternalMessageInfo

func (m *GetTabPublishHotRcmdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetTabPublishHotRcmdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetTabPublishHotRcmdResp struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Items                []*TabPublishHotRcmd `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetTabPublishHotRcmdResp) Reset()         { *m = GetTabPublishHotRcmdResp{} }
func (m *GetTabPublishHotRcmdResp) String() string { return proto.CompactTextString(m) }
func (*GetTabPublishHotRcmdResp) ProtoMessage()    {}
func (*GetTabPublishHotRcmdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{30}
}
func (m *GetTabPublishHotRcmdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabPublishHotRcmdResp.Unmarshal(m, b)
}
func (m *GetTabPublishHotRcmdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabPublishHotRcmdResp.Marshal(b, m, deterministic)
}
func (dst *GetTabPublishHotRcmdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabPublishHotRcmdResp.Merge(dst, src)
}
func (m *GetTabPublishHotRcmdResp) XXX_Size() int {
	return xxx_messageInfo_GetTabPublishHotRcmdResp.Size(m)
}
func (m *GetTabPublishHotRcmdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabPublishHotRcmdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabPublishHotRcmdResp proto.InternalMessageInfo

func (m *GetTabPublishHotRcmdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetTabPublishHotRcmdResp) GetItems() []*TabPublishHotRcmd {
	if m != nil {
		return m.Items
	}
	return nil
}

type TabPublishHotRcmd struct {
	Id                   string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Hint                 string        `protobuf:"bytes,3,opt,name=hint,proto3" json:"hint,omitempty"`
	Blocks               []*MusicBlock `protobuf:"bytes,4,rep,name=blocks,proto3" json:"blocks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *TabPublishHotRcmd) Reset()         { *m = TabPublishHotRcmd{} }
func (m *TabPublishHotRcmd) String() string { return proto.CompactTextString(m) }
func (*TabPublishHotRcmd) ProtoMessage()    {}
func (*TabPublishHotRcmd) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{31}
}
func (m *TabPublishHotRcmd) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabPublishHotRcmd.Unmarshal(m, b)
}
func (m *TabPublishHotRcmd) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabPublishHotRcmd.Marshal(b, m, deterministic)
}
func (dst *TabPublishHotRcmd) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabPublishHotRcmd.Merge(dst, src)
}
func (m *TabPublishHotRcmd) XXX_Size() int {
	return xxx_messageInfo_TabPublishHotRcmd.Size(m)
}
func (m *TabPublishHotRcmd) XXX_DiscardUnknown() {
	xxx_messageInfo_TabPublishHotRcmd.DiscardUnknown(m)
}

var xxx_messageInfo_TabPublishHotRcmd proto.InternalMessageInfo

func (m *TabPublishHotRcmd) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *TabPublishHotRcmd) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TabPublishHotRcmd) GetHint() string {
	if m != nil {
		return m.Hint
	}
	return ""
}

func (m *TabPublishHotRcmd) GetBlocks() []*MusicBlock {
	if m != nil {
		return m.Blocks
	}
	return nil
}

type MusicBlock struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElementId            uint32   `protobuf:"varint,2,opt,name=element_id,json=elementId,proto3" json:"element_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicBlock) Reset()         { *m = MusicBlock{} }
func (m *MusicBlock) String() string { return proto.CompactTextString(m) }
func (*MusicBlock) ProtoMessage()    {}
func (*MusicBlock) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{32}
}
func (m *MusicBlock) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicBlock.Unmarshal(m, b)
}
func (m *MusicBlock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicBlock.Marshal(b, m, deterministic)
}
func (dst *MusicBlock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicBlock.Merge(dst, src)
}
func (m *MusicBlock) XXX_Size() int {
	return xxx_messageInfo_MusicBlock.Size(m)
}
func (m *MusicBlock) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicBlock.DiscardUnknown(m)
}

var xxx_messageInfo_MusicBlock proto.InternalMessageInfo

func (m *MusicBlock) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *MusicBlock) GetElementId() uint32 {
	if m != nil {
		return m.ElementId
	}
	return 0
}

type GetResourceConfigByChannelIdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetResourceConfigByChannelIdReq) Reset()         { *m = GetResourceConfigByChannelIdReq{} }
func (m *GetResourceConfigByChannelIdReq) String() string { return proto.CompactTextString(m) }
func (*GetResourceConfigByChannelIdReq) ProtoMessage()    {}
func (*GetResourceConfigByChannelIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{33}
}
func (m *GetResourceConfigByChannelIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetResourceConfigByChannelIdReq.Unmarshal(m, b)
}
func (m *GetResourceConfigByChannelIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetResourceConfigByChannelIdReq.Marshal(b, m, deterministic)
}
func (dst *GetResourceConfigByChannelIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResourceConfigByChannelIdReq.Merge(dst, src)
}
func (m *GetResourceConfigByChannelIdReq) XXX_Size() int {
	return xxx_messageInfo_GetResourceConfigByChannelIdReq.Size(m)
}
func (m *GetResourceConfigByChannelIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResourceConfigByChannelIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetResourceConfigByChannelIdReq proto.InternalMessageInfo

func (m *GetResourceConfigByChannelIdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetResourceConfigByChannelIdReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetResourceConfigByChannelIdResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Text                 string        `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Icon                 string        `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	JumpUrl              string        `protobuf:"bytes,4,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetResourceConfigByChannelIdResp) Reset()         { *m = GetResourceConfigByChannelIdResp{} }
func (m *GetResourceConfigByChannelIdResp) String() string { return proto.CompactTextString(m) }
func (*GetResourceConfigByChannelIdResp) ProtoMessage()    {}
func (*GetResourceConfigByChannelIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{34}
}
func (m *GetResourceConfigByChannelIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetResourceConfigByChannelIdResp.Unmarshal(m, b)
}
func (m *GetResourceConfigByChannelIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetResourceConfigByChannelIdResp.Marshal(b, m, deterministic)
}
func (dst *GetResourceConfigByChannelIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResourceConfigByChannelIdResp.Merge(dst, src)
}
func (m *GetResourceConfigByChannelIdResp) XXX_Size() int {
	return xxx_messageInfo_GetResourceConfigByChannelIdResp.Size(m)
}
func (m *GetResourceConfigByChannelIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResourceConfigByChannelIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetResourceConfigByChannelIdResp proto.InternalMessageInfo

func (m *GetResourceConfigByChannelIdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetResourceConfigByChannelIdResp) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *GetResourceConfigByChannelIdResp) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GetResourceConfigByChannelIdResp) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

type MuseGetTopicChannelInfoRequest struct {
	BaseReq              *app.BaseReq                                   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                                         `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelInfoType      MuseGetTopicChannelInfoRequest_ChannelInfoType `protobuf:"varint,3,opt,name=channel_info_type,json=channelInfoType,proto3,enum=ga.MuseGetTopicChannelInfoRequest_ChannelInfoType" json:"channel_info_type,omitempty"`
	ChannelType          uint32                                         `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                       `json:"-"`
	XXX_unrecognized     []byte                                         `json:"-"`
	XXX_sizecache        int32                                          `json:"-"`
}

func (m *MuseGetTopicChannelInfoRequest) Reset()         { *m = MuseGetTopicChannelInfoRequest{} }
func (m *MuseGetTopicChannelInfoRequest) String() string { return proto.CompactTextString(m) }
func (*MuseGetTopicChannelInfoRequest) ProtoMessage()    {}
func (*MuseGetTopicChannelInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{35}
}
func (m *MuseGetTopicChannelInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseGetTopicChannelInfoRequest.Unmarshal(m, b)
}
func (m *MuseGetTopicChannelInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseGetTopicChannelInfoRequest.Marshal(b, m, deterministic)
}
func (dst *MuseGetTopicChannelInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseGetTopicChannelInfoRequest.Merge(dst, src)
}
func (m *MuseGetTopicChannelInfoRequest) XXX_Size() int {
	return xxx_messageInfo_MuseGetTopicChannelInfoRequest.Size(m)
}
func (m *MuseGetTopicChannelInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseGetTopicChannelInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MuseGetTopicChannelInfoRequest proto.InternalMessageInfo

func (m *MuseGetTopicChannelInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MuseGetTopicChannelInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseGetTopicChannelInfoRequest) GetChannelInfoType() MuseGetTopicChannelInfoRequest_ChannelInfoType {
	if m != nil {
		return m.ChannelInfoType
	}
	return MuseGetTopicChannelInfoRequest_CHANNEL_INFO_TYPE_NORMAL_UNSPECIFIED
}

func (m *MuseGetTopicChannelInfoRequest) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type MuseGetTopicChannelInfoResponse struct {
	BaseResp       *app.BaseResp                           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId      uint32                                  `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId          uint32                                  `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName        string                                  `protobuf:"bytes,4,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	IsInGround     bool                                    `protobuf:"varint,5,opt,name=is_in_ground,json=isInGround,proto3" json:"is_in_ground,omitempty"`
	IsPrivate      bool                                    `protobuf:"varint,6,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	HeadDesc       string                                  `protobuf:"bytes,7,opt,name=head_desc,json=headDesc,proto3" json:"head_desc,omitempty"`
	PlayingOption  []MusePlayingOption                     `protobuf:"varint,8,rep,packed,name=playing_option,json=playingOption,proto3,enum=ga.MusePlayingOption" json:"playing_option,omitempty"`
	SwitchPlayInfo *MuseSwitchPlayInfo                     `protobuf:"bytes,9,opt,name=switch_play_info,json=switchPlayInfo,proto3" json:"switch_play_info,omitempty"`
	TabType        MuseGetTopicChannelInfoResponse_TabType `protobuf:"varint,10,opt,name=tab_type,json=tabType,proto3,enum=ga.MuseGetTopicChannelInfoResponse_TabType" json:"tab_type,omitempty"`
	WelcomeText    string                                  `protobuf:"bytes,11,opt,name=welcome_text,json=welcomeText,proto3" json:"welcome_text,omitempty"`
	// 从房间内跳到指定外部第三方游戏相关配置(即下载游戏)
	ThirdPartyGame       *MuseThirdPartyGame `protobuf:"bytes,12,opt,name=third_party_game,json=thirdPartyGame,proto3" json:"third_party_game,omitempty"`
	ShiftRoomDuration    uint32              `protobuf:"varint,13,opt,name=shift_room_duration,json=shiftRoomDuration,proto3" json:"shift_room_duration,omitempty"`
	FreezeDuration       uint32              `protobuf:"varint,14,opt,name=freeze_duration,json=freezeDuration,proto3" json:"freeze_duration,omitempty"`
	TagId                uint32              `protobuf:"varint,15,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TeamDesc             string              `protobuf:"bytes,16,opt,name=team_desc,json=teamDesc,proto3" json:"team_desc,omitempty"`
	ShowTeamDesc         bool                `protobuf:"varint,17,opt,name=show_team_desc,json=showTeamDesc,proto3" json:"show_team_desc,omitempty"`
	ShowPublishButton    bool                `protobuf:"varint,18,opt,name=show_publish_button,json=showPublishButton,proto3" json:"show_publish_button,omitempty"`
	CategoryType         MuseCategoryType    `protobuf:"varint,19,opt,name=category_type,json=categoryType,proto3,enum=ga.MuseCategoryType" json:"category_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *MuseGetTopicChannelInfoResponse) Reset()         { *m = MuseGetTopicChannelInfoResponse{} }
func (m *MuseGetTopicChannelInfoResponse) String() string { return proto.CompactTextString(m) }
func (*MuseGetTopicChannelInfoResponse) ProtoMessage()    {}
func (*MuseGetTopicChannelInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{36}
}
func (m *MuseGetTopicChannelInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseGetTopicChannelInfoResponse.Unmarshal(m, b)
}
func (m *MuseGetTopicChannelInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseGetTopicChannelInfoResponse.Marshal(b, m, deterministic)
}
func (dst *MuseGetTopicChannelInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseGetTopicChannelInfoResponse.Merge(dst, src)
}
func (m *MuseGetTopicChannelInfoResponse) XXX_Size() int {
	return xxx_messageInfo_MuseGetTopicChannelInfoResponse.Size(m)
}
func (m *MuseGetTopicChannelInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseGetTopicChannelInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MuseGetTopicChannelInfoResponse proto.InternalMessageInfo

func (m *MuseGetTopicChannelInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MuseGetTopicChannelInfoResponse) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseGetTopicChannelInfoResponse) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MuseGetTopicChannelInfoResponse) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *MuseGetTopicChannelInfoResponse) GetIsInGround() bool {
	if m != nil {
		return m.IsInGround
	}
	return false
}

func (m *MuseGetTopicChannelInfoResponse) GetIsPrivate() bool {
	if m != nil {
		return m.IsPrivate
	}
	return false
}

func (m *MuseGetTopicChannelInfoResponse) GetHeadDesc() string {
	if m != nil {
		return m.HeadDesc
	}
	return ""
}

func (m *MuseGetTopicChannelInfoResponse) GetPlayingOption() []MusePlayingOption {
	if m != nil {
		return m.PlayingOption
	}
	return nil
}

func (m *MuseGetTopicChannelInfoResponse) GetSwitchPlayInfo() *MuseSwitchPlayInfo {
	if m != nil {
		return m.SwitchPlayInfo
	}
	return nil
}

func (m *MuseGetTopicChannelInfoResponse) GetTabType() MuseGetTopicChannelInfoResponse_TabType {
	if m != nil {
		return m.TabType
	}
	return MuseGetTopicChannelInfoResponse_TAB_TYPE_NORMAL_UNSPECIFIED
}

func (m *MuseGetTopicChannelInfoResponse) GetWelcomeText() string {
	if m != nil {
		return m.WelcomeText
	}
	return ""
}

func (m *MuseGetTopicChannelInfoResponse) GetThirdPartyGame() *MuseThirdPartyGame {
	if m != nil {
		return m.ThirdPartyGame
	}
	return nil
}

func (m *MuseGetTopicChannelInfoResponse) GetShiftRoomDuration() uint32 {
	if m != nil {
		return m.ShiftRoomDuration
	}
	return 0
}

func (m *MuseGetTopicChannelInfoResponse) GetFreezeDuration() uint32 {
	if m != nil {
		return m.FreezeDuration
	}
	return 0
}

func (m *MuseGetTopicChannelInfoResponse) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *MuseGetTopicChannelInfoResponse) GetTeamDesc() string {
	if m != nil {
		return m.TeamDesc
	}
	return ""
}

func (m *MuseGetTopicChannelInfoResponse) GetShowTeamDesc() bool {
	if m != nil {
		return m.ShowTeamDesc
	}
	return false
}

func (m *MuseGetTopicChannelInfoResponse) GetShowPublishButton() bool {
	if m != nil {
		return m.ShowPublishButton
	}
	return false
}

func (m *MuseGetTopicChannelInfoResponse) GetCategoryType() MuseCategoryType {
	if m != nil {
		return m.CategoryType
	}
	return MuseCategoryType_MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_UNSPECIFIED
}

type MuseSwitchPlayInfo struct {
	RoomModel            uint32   `protobuf:"varint,3,opt,name=room_model,json=roomModel,proto3" json:"room_model,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseSwitchPlayInfo) Reset()         { *m = MuseSwitchPlayInfo{} }
func (m *MuseSwitchPlayInfo) String() string { return proto.CompactTextString(m) }
func (*MuseSwitchPlayInfo) ProtoMessage()    {}
func (*MuseSwitchPlayInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{37}
}
func (m *MuseSwitchPlayInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSwitchPlayInfo.Unmarshal(m, b)
}
func (m *MuseSwitchPlayInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSwitchPlayInfo.Marshal(b, m, deterministic)
}
func (dst *MuseSwitchPlayInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSwitchPlayInfo.Merge(dst, src)
}
func (m *MuseSwitchPlayInfo) XXX_Size() int {
	return xxx_messageInfo_MuseSwitchPlayInfo.Size(m)
}
func (m *MuseSwitchPlayInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSwitchPlayInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSwitchPlayInfo proto.InternalMessageInfo

func (m *MuseSwitchPlayInfo) GetRoomModel() uint32 {
	if m != nil {
		return m.RoomModel
	}
	return 0
}

type MuseThirdPartyGame struct {
	LabelUrl             string                                 `protobuf:"bytes,1,opt,name=label_url,json=labelUrl,proto3" json:"label_url,omitempty"`
	PublicUrl            string                                 `protobuf:"bytes,2,opt,name=public_url,json=publicUrl,proto3" json:"public_url,omitempty"`
	GameBaseInfo         []*MuseThirdPartyGame_MuseGameBaseInfo `protobuf:"bytes,3,rep,name=game_base_info,json=gameBaseInfo,proto3" json:"game_base_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *MuseThirdPartyGame) Reset()         { *m = MuseThirdPartyGame{} }
func (m *MuseThirdPartyGame) String() string { return proto.CompactTextString(m) }
func (*MuseThirdPartyGame) ProtoMessage()    {}
func (*MuseThirdPartyGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{38}
}
func (m *MuseThirdPartyGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseThirdPartyGame.Unmarshal(m, b)
}
func (m *MuseThirdPartyGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseThirdPartyGame.Marshal(b, m, deterministic)
}
func (dst *MuseThirdPartyGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseThirdPartyGame.Merge(dst, src)
}
func (m *MuseThirdPartyGame) XXX_Size() int {
	return xxx_messageInfo_MuseThirdPartyGame.Size(m)
}
func (m *MuseThirdPartyGame) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseThirdPartyGame.DiscardUnknown(m)
}

var xxx_messageInfo_MuseThirdPartyGame proto.InternalMessageInfo

func (m *MuseThirdPartyGame) GetLabelUrl() string {
	if m != nil {
		return m.LabelUrl
	}
	return ""
}

func (m *MuseThirdPartyGame) GetPublicUrl() string {
	if m != nil {
		return m.PublicUrl
	}
	return ""
}

func (m *MuseThirdPartyGame) GetGameBaseInfo() []*MuseThirdPartyGame_MuseGameBaseInfo {
	if m != nil {
		return m.GameBaseInfo
	}
	return nil
}

type MuseThirdPartyGame_MuseGameBaseInfo struct {
	Platform             string   `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	JumpUrl              string   `protobuf:"bytes,2,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	DownloadUrl          string   `protobuf:"bytes,3,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	PackageName          string   `protobuf:"bytes,4,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseThirdPartyGame_MuseGameBaseInfo) Reset()         { *m = MuseThirdPartyGame_MuseGameBaseInfo{} }
func (m *MuseThirdPartyGame_MuseGameBaseInfo) String() string { return proto.CompactTextString(m) }
func (*MuseThirdPartyGame_MuseGameBaseInfo) ProtoMessage()    {}
func (*MuseThirdPartyGame_MuseGameBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{38, 0}
}
func (m *MuseThirdPartyGame_MuseGameBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseThirdPartyGame_MuseGameBaseInfo.Unmarshal(m, b)
}
func (m *MuseThirdPartyGame_MuseGameBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseThirdPartyGame_MuseGameBaseInfo.Marshal(b, m, deterministic)
}
func (dst *MuseThirdPartyGame_MuseGameBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseThirdPartyGame_MuseGameBaseInfo.Merge(dst, src)
}
func (m *MuseThirdPartyGame_MuseGameBaseInfo) XXX_Size() int {
	return xxx_messageInfo_MuseThirdPartyGame_MuseGameBaseInfo.Size(m)
}
func (m *MuseThirdPartyGame_MuseGameBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseThirdPartyGame_MuseGameBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MuseThirdPartyGame_MuseGameBaseInfo proto.InternalMessageInfo

func (m *MuseThirdPartyGame_MuseGameBaseInfo) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *MuseThirdPartyGame_MuseGameBaseInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *MuseThirdPartyGame_MuseGameBaseInfo) GetDownloadUrl() string {
	if m != nil {
		return m.DownloadUrl
	}
	return ""
}

func (m *MuseThirdPartyGame_MuseGameBaseInfo) GetPackageName() string {
	if m != nil {
		return m.PackageName
	}
	return ""
}

// 主题房处罚推送
type TopicChannelUserWarnNotifyMsg struct {
	PunishType           uint32                `protobuf:"varint,1,opt,name=punish_type,json=punishType,proto3" json:"punish_type,omitempty"`
	ChannelId            uint32                `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidList              []uint32              `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	InValue              []*im.RichTextElement `protobuf:"bytes,4,rep,name=in_value,json=inValue,proto3" json:"in_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *TopicChannelUserWarnNotifyMsg) Reset()         { *m = TopicChannelUserWarnNotifyMsg{} }
func (m *TopicChannelUserWarnNotifyMsg) String() string { return proto.CompactTextString(m) }
func (*TopicChannelUserWarnNotifyMsg) ProtoMessage()    {}
func (*TopicChannelUserWarnNotifyMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{39}
}
func (m *TopicChannelUserWarnNotifyMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicChannelUserWarnNotifyMsg.Unmarshal(m, b)
}
func (m *TopicChannelUserWarnNotifyMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicChannelUserWarnNotifyMsg.Marshal(b, m, deterministic)
}
func (dst *TopicChannelUserWarnNotifyMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicChannelUserWarnNotifyMsg.Merge(dst, src)
}
func (m *TopicChannelUserWarnNotifyMsg) XXX_Size() int {
	return xxx_messageInfo_TopicChannelUserWarnNotifyMsg.Size(m)
}
func (m *TopicChannelUserWarnNotifyMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicChannelUserWarnNotifyMsg.DiscardUnknown(m)
}

var xxx_messageInfo_TopicChannelUserWarnNotifyMsg proto.InternalMessageInfo

func (m *TopicChannelUserWarnNotifyMsg) GetPunishType() uint32 {
	if m != nil {
		return m.PunishType
	}
	return 0
}

func (m *TopicChannelUserWarnNotifyMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TopicChannelUserWarnNotifyMsg) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *TopicChannelUserWarnNotifyMsg) GetInValue() []*im.RichTextElement {
	if m != nil {
		return m.InValue
	}
	return nil
}

// 主题房处罚推送房间
type TopicChannelUserWarnNotifyInChannelMsg struct {
	ChannelId            uint32                `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	InValue              []*im.RichTextElement `protobuf:"bytes,4,rep,name=in_value,json=inValue,proto3" json:"in_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *TopicChannelUserWarnNotifyInChannelMsg) Reset() {
	*m = TopicChannelUserWarnNotifyInChannelMsg{}
}
func (m *TopicChannelUserWarnNotifyInChannelMsg) String() string { return proto.CompactTextString(m) }
func (*TopicChannelUserWarnNotifyInChannelMsg) ProtoMessage()    {}
func (*TopicChannelUserWarnNotifyInChannelMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{40}
}
func (m *TopicChannelUserWarnNotifyInChannelMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicChannelUserWarnNotifyInChannelMsg.Unmarshal(m, b)
}
func (m *TopicChannelUserWarnNotifyInChannelMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicChannelUserWarnNotifyInChannelMsg.Marshal(b, m, deterministic)
}
func (dst *TopicChannelUserWarnNotifyInChannelMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicChannelUserWarnNotifyInChannelMsg.Merge(dst, src)
}
func (m *TopicChannelUserWarnNotifyInChannelMsg) XXX_Size() int {
	return xxx_messageInfo_TopicChannelUserWarnNotifyInChannelMsg.Size(m)
}
func (m *TopicChannelUserWarnNotifyInChannelMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicChannelUserWarnNotifyInChannelMsg.DiscardUnknown(m)
}

var xxx_messageInfo_TopicChannelUserWarnNotifyInChannelMsg proto.InternalMessageInfo

func (m *TopicChannelUserWarnNotifyInChannelMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TopicChannelUserWarnNotifyInChannelMsg) GetInValue() []*im.RichTextElement {
	if m != nil {
		return m.InValue
	}
	return nil
}

type TopicChannelUserWarnLinkJumpURL struct {
	JumpUrlMap           map[string]string `protobuf:"bytes,1,rep,name=jump_url_map,json=jumpUrlMap,proto3" json:"jump_url_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // Deprecated: Do not use.
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *TopicChannelUserWarnLinkJumpURL) Reset()         { *m = TopicChannelUserWarnLinkJumpURL{} }
func (m *TopicChannelUserWarnLinkJumpURL) String() string { return proto.CompactTextString(m) }
func (*TopicChannelUserWarnLinkJumpURL) ProtoMessage()    {}
func (*TopicChannelUserWarnLinkJumpURL) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{41}
}
func (m *TopicChannelUserWarnLinkJumpURL) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicChannelUserWarnLinkJumpURL.Unmarshal(m, b)
}
func (m *TopicChannelUserWarnLinkJumpURL) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicChannelUserWarnLinkJumpURL.Marshal(b, m, deterministic)
}
func (dst *TopicChannelUserWarnLinkJumpURL) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicChannelUserWarnLinkJumpURL.Merge(dst, src)
}
func (m *TopicChannelUserWarnLinkJumpURL) XXX_Size() int {
	return xxx_messageInfo_TopicChannelUserWarnLinkJumpURL.Size(m)
}
func (m *TopicChannelUserWarnLinkJumpURL) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicChannelUserWarnLinkJumpURL.DiscardUnknown(m)
}

var xxx_messageInfo_TopicChannelUserWarnLinkJumpURL proto.InternalMessageInfo

// Deprecated: Do not use.
func (m *TopicChannelUserWarnLinkJumpURL) GetJumpUrlMap() map[string]string {
	if m != nil {
		return m.JumpUrlMap
	}
	return nil
}

type GetAssociateRevChannelsRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	NextToken            string       `protobuf:"bytes,2,opt,name=next_token,json=nextToken,proto3" json:"next_token,omitempty"`
	Limit                uint32       `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32       `protobuf:"varint,5,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAssociateRevChannelsRequest) Reset()         { *m = GetAssociateRevChannelsRequest{} }
func (m *GetAssociateRevChannelsRequest) String() string { return proto.CompactTextString(m) }
func (*GetAssociateRevChannelsRequest) ProtoMessage()    {}
func (*GetAssociateRevChannelsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{42}
}
func (m *GetAssociateRevChannelsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAssociateRevChannelsRequest.Unmarshal(m, b)
}
func (m *GetAssociateRevChannelsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAssociateRevChannelsRequest.Marshal(b, m, deterministic)
}
func (dst *GetAssociateRevChannelsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAssociateRevChannelsRequest.Merge(dst, src)
}
func (m *GetAssociateRevChannelsRequest) XXX_Size() int {
	return xxx_messageInfo_GetAssociateRevChannelsRequest.Size(m)
}
func (m *GetAssociateRevChannelsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAssociateRevChannelsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAssociateRevChannelsRequest proto.InternalMessageInfo

func (m *GetAssociateRevChannelsRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAssociateRevChannelsRequest) GetNextToken() string {
	if m != nil {
		return m.NextToken
	}
	return ""
}

func (m *GetAssociateRevChannelsRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetAssociateRevChannelsRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetAssociateRevChannelsRequest) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetAssociateRevChannelsResponse struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	NextToken            string            `protobuf:"bytes,2,opt,name=next_token,json=nextToken,proto3" json:"next_token,omitempty"`
	Title                string            `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Channels             []*MuseRevChannel `protobuf:"bytes,4,rep,name=channels,proto3" json:"channels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAssociateRevChannelsResponse) Reset()         { *m = GetAssociateRevChannelsResponse{} }
func (m *GetAssociateRevChannelsResponse) String() string { return proto.CompactTextString(m) }
func (*GetAssociateRevChannelsResponse) ProtoMessage()    {}
func (*GetAssociateRevChannelsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{43}
}
func (m *GetAssociateRevChannelsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAssociateRevChannelsResponse.Unmarshal(m, b)
}
func (m *GetAssociateRevChannelsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAssociateRevChannelsResponse.Marshal(b, m, deterministic)
}
func (dst *GetAssociateRevChannelsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAssociateRevChannelsResponse.Merge(dst, src)
}
func (m *GetAssociateRevChannelsResponse) XXX_Size() int {
	return xxx_messageInfo_GetAssociateRevChannelsResponse.Size(m)
}
func (m *GetAssociateRevChannelsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAssociateRevChannelsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAssociateRevChannelsResponse proto.InternalMessageInfo

func (m *GetAssociateRevChannelsResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAssociateRevChannelsResponse) GetNextToken() string {
	if m != nil {
		return m.NextToken
	}
	return ""
}

func (m *GetAssociateRevChannelsResponse) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetAssociateRevChannelsResponse) GetChannels() []*MuseRevChannel {
	if m != nil {
		return m.Channels
	}
	return nil
}

type MuseRevChannel struct {
	ChannelId            uint32             `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SimpleDesc           string             `protobuf:"bytes,2,opt,name=simple_desc,json=simpleDesc,proto3" json:"simple_desc,omitempty"`
	ChannelName          string             `protobuf:"bytes,3,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelOwnerAccount  string             `protobuf:"bytes,4,opt,name=channel_owner_account,json=channelOwnerAccount,proto3" json:"channel_owner_account,omitempty"`
	ChannelOwnerSex      int32              `protobuf:"varint,5,opt,name=channel_owner_sex,json=channelOwnerSex,proto3" json:"channel_owner_sex,omitempty"`
	ChannelOwnerName     string             `protobuf:"bytes,6,opt,name=channel_owner_name,json=channelOwnerName,proto3" json:"channel_owner_name,omitempty"`
	ChannelHot           *MuseRevChannel    `protobuf:"bytes,7,opt,name=channel_hot,json=channelHot,proto3" json:"channel_hot,omitempty"` // Deprecated: Do not use.
	ChannelIconMd5       string             `protobuf:"bytes,8,opt,name=channel_icon_md5,json=channelIconMd5,proto3" json:"channel_icon_md5,omitempty"`
	RevChannelHot        *MuseRevChannelHot `protobuf:"bytes,9,opt,name=rev_channel_hot,json=revChannelHot,proto3" json:"rev_channel_hot,omitempty"`
	ChannelType          uint32             `protobuf:"varint,10,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MuseRevChannel) Reset()         { *m = MuseRevChannel{} }
func (m *MuseRevChannel) String() string { return proto.CompactTextString(m) }
func (*MuseRevChannel) ProtoMessage()    {}
func (*MuseRevChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{44}
}
func (m *MuseRevChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseRevChannel.Unmarshal(m, b)
}
func (m *MuseRevChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseRevChannel.Marshal(b, m, deterministic)
}
func (dst *MuseRevChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseRevChannel.Merge(dst, src)
}
func (m *MuseRevChannel) XXX_Size() int {
	return xxx_messageInfo_MuseRevChannel.Size(m)
}
func (m *MuseRevChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseRevChannel.DiscardUnknown(m)
}

var xxx_messageInfo_MuseRevChannel proto.InternalMessageInfo

func (m *MuseRevChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseRevChannel) GetSimpleDesc() string {
	if m != nil {
		return m.SimpleDesc
	}
	return ""
}

func (m *MuseRevChannel) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *MuseRevChannel) GetChannelOwnerAccount() string {
	if m != nil {
		return m.ChannelOwnerAccount
	}
	return ""
}

func (m *MuseRevChannel) GetChannelOwnerSex() int32 {
	if m != nil {
		return m.ChannelOwnerSex
	}
	return 0
}

func (m *MuseRevChannel) GetChannelOwnerName() string {
	if m != nil {
		return m.ChannelOwnerName
	}
	return ""
}

// Deprecated: Do not use.
func (m *MuseRevChannel) GetChannelHot() *MuseRevChannel {
	if m != nil {
		return m.ChannelHot
	}
	return nil
}

func (m *MuseRevChannel) GetChannelIconMd5() string {
	if m != nil {
		return m.ChannelIconMd5
	}
	return ""
}

func (m *MuseRevChannel) GetRevChannelHot() *MuseRevChannelHot {
	if m != nil {
		return m.RevChannelHot
	}
	return nil
}

func (m *MuseRevChannel) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type MuseRevChannelHot struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	Count                int64    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseRevChannelHot) Reset()         { *m = MuseRevChannelHot{} }
func (m *MuseRevChannelHot) String() string { return proto.CompactTextString(m) }
func (*MuseRevChannelHot) ProtoMessage()    {}
func (*MuseRevChannelHot) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa, []int{45}
}
func (m *MuseRevChannelHot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseRevChannelHot.Unmarshal(m, b)
}
func (m *MuseRevChannelHot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseRevChannelHot.Marshal(b, m, deterministic)
}
func (dst *MuseRevChannelHot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseRevChannelHot.Merge(dst, src)
}
func (m *MuseRevChannelHot) XXX_Size() int {
	return xxx_messageInfo_MuseRevChannelHot.Size(m)
}
func (m *MuseRevChannelHot) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseRevChannelHot.DiscardUnknown(m)
}

var xxx_messageInfo_MuseRevChannelHot proto.InternalMessageInfo

func (m *MuseRevChannelHot) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *MuseRevChannelHot) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

func init() {
	proto.RegisterType((*GetMusicChannelFilterV2Req)(nil), "ga.GetMusicChannelFilterV2Req")
	proto.RegisterType((*GetMusicChannelFilterV2Resp)(nil), "ga.GetMusicChannelFilterV2Resp")
	proto.RegisterType((*GetMusicChannelFilterV2Resp_FilterItem)(nil), "ga.GetMusicChannelFilterV2Resp.FilterItem")
	proto.RegisterType((*GetMusicChannelFilterV2Resp_FilterSubItem)(nil), "ga.GetMusicChannelFilterV2Resp.FilterSubItem")
	proto.RegisterType((*SameCityTitle)(nil), "ga.SameCityTitle")
	proto.RegisterType((*ListHobbyChannelV2Req)(nil), "ga.ListHobbyChannelV2Req")
	proto.RegisterType((*GetMusicHomePageViewV2Req)(nil), "ga.GetMusicHomePageViewV2Req")
	proto.RegisterType((*GetMusicHomePageViewV2Resp)(nil), "ga.GetMusicHomePageViewV2Resp")
	proto.RegisterType((*GetMusicHomePageViewV2Resp_MusicHomePageV2View)(nil), "ga.GetMusicHomePageViewV2Resp.MusicHomePageV2View")
	proto.RegisterType((*GetMusicHomePageDialogV2Req)(nil), "ga.GetMusicHomePageDialogV2Req")
	proto.RegisterType((*GetMusicHomePageDialogV2Resp)(nil), "ga.GetMusicHomePageDialogV2Resp")
	proto.RegisterType((*GetMusicHomePageDialogV2Resp_DialogView)(nil), "ga.GetMusicHomePageDialogV2Resp.DialogView")
	proto.RegisterType((*QuickMatchHobbyChannelV2Req)(nil), "ga.QuickMatchHobbyChannelV2Req")
	proto.RegisterType((*QuickMatchHobbyChannelV2Resp)(nil), "ga.QuickMatchHobbyChannelV2Resp")
	proto.RegisterType((*ReunionInteractionPush)(nil), "ga.ReunionInteractionPush")
	proto.RegisterType((*ReunionUser)(nil), "ga.ReunionUser")
	proto.RegisterType((*PublishMusicChannelReq)(nil), "ga.PublishMusicChannelReq")
	proto.RegisterType((*PublishMusicChannelResp)(nil), "ga.PublishMusicChannelResp")
	proto.RegisterType((*CancelMusicChannelPublishReq)(nil), "ga.CancelMusicChannelPublishReq")
	proto.RegisterType((*CancelMusicChannelPublishResp)(nil), "ga.CancelMusicChannelPublishResp")
	proto.RegisterType((*GetMusicFilterItemByIdsReq)(nil), "ga.GetMusicFilterItemByIdsReq")
	proto.RegisterType((*GetMusicFilterItemByIdsResp)(nil), "ga.GetMusicFilterItemByIdsResp")
	proto.RegisterMapType((map[string]*MusicFilterItem)(nil), "ga.GetMusicFilterItemByIdsResp.FilterMapEntry")
	proto.RegisterType((*MusicFilterItem)(nil), "ga.MusicFilterItem")
	proto.RegisterType((*ListMusicChannelsReq)(nil), "ga.ListMusicChannelsReq")
	proto.RegisterType((*ListMusicChannelsResp)(nil), "ga.ListMusicChannelsResp")
	proto.RegisterType((*MusicChannel)(nil), "ga.MusicChannel")
	proto.RegisterType((*MusicSocialRankHonorSignInfo)(nil), "ga.MusicSocialRankHonorSignInfo")
	proto.RegisterType((*MusicSocial)(nil), "ga.MusicSocial")
	proto.RegisterType((*MusicPia)(nil), "ga.MusicPia")
	proto.RegisterType((*MusicInteresting)(nil), "ga.MusicInteresting")
	proto.RegisterType((*MusicChannelReview)(nil), "ga.MusicChannelReview")
	proto.RegisterType((*KtvGlory)(nil), "ga.KtvGlory")
	proto.RegisterType((*MusicChannelPersonalCert)(nil), "ga.MusicChannelPersonalCert")
	proto.RegisterType((*GetTabPublishHotRcmdReq)(nil), "ga.GetTabPublishHotRcmdReq")
	proto.RegisterType((*GetTabPublishHotRcmdResp)(nil), "ga.GetTabPublishHotRcmdResp")
	proto.RegisterType((*TabPublishHotRcmd)(nil), "ga.TabPublishHotRcmd")
	proto.RegisterType((*MusicBlock)(nil), "ga.MusicBlock")
	proto.RegisterType((*GetResourceConfigByChannelIdReq)(nil), "ga.GetResourceConfigByChannelIdReq")
	proto.RegisterType((*GetResourceConfigByChannelIdResp)(nil), "ga.GetResourceConfigByChannelIdResp")
	proto.RegisterType((*MuseGetTopicChannelInfoRequest)(nil), "ga.MuseGetTopicChannelInfoRequest")
	proto.RegisterType((*MuseGetTopicChannelInfoResponse)(nil), "ga.MuseGetTopicChannelInfoResponse")
	proto.RegisterType((*MuseSwitchPlayInfo)(nil), "ga.MuseSwitchPlayInfo")
	proto.RegisterType((*MuseThirdPartyGame)(nil), "ga.MuseThirdPartyGame")
	proto.RegisterType((*MuseThirdPartyGame_MuseGameBaseInfo)(nil), "ga.MuseThirdPartyGame.MuseGameBaseInfo")
	proto.RegisterType((*TopicChannelUserWarnNotifyMsg)(nil), "ga.TopicChannelUserWarnNotifyMsg")
	proto.RegisterType((*TopicChannelUserWarnNotifyInChannelMsg)(nil), "ga.TopicChannelUserWarnNotifyInChannelMsg")
	proto.RegisterType((*TopicChannelUserWarnLinkJumpURL)(nil), "ga.TopicChannelUserWarnLinkJumpURL")
	proto.RegisterMapType((map[string]string)(nil), "ga.TopicChannelUserWarnLinkJumpURL.JumpUrlMapEntry")
	proto.RegisterType((*GetAssociateRevChannelsRequest)(nil), "ga.GetAssociateRevChannelsRequest")
	proto.RegisterType((*GetAssociateRevChannelsResponse)(nil), "ga.GetAssociateRevChannelsResponse")
	proto.RegisterType((*MuseRevChannel)(nil), "ga.MuseRevChannel")
	proto.RegisterType((*MuseRevChannelHot)(nil), "ga.MuseRevChannelHot")
	proto.RegisterEnum("ga.FilterAttrType", FilterAttrType_name, FilterAttrType_value)
	proto.RegisterEnum("ga.MusicHomePageDialogType", MusicHomePageDialogType_name, MusicHomePageDialogType_value)
	proto.RegisterEnum("ga.MusicChannelLabel", MusicChannelLabel_name, MusicChannelLabel_value)
	proto.RegisterEnum("ga.MuseCategoryType", MuseCategoryType_name, MuseCategoryType_value)
	proto.RegisterEnum("ga.MusePlayingOption", MusePlayingOption_name, MusePlayingOption_value)
	proto.RegisterEnum("ga.TopicChannelPunishType", TopicChannelPunishType_name, TopicChannelPunishType_value)
	proto.RegisterEnum("ga.GetMusicChannelFilterV2Resp_FilterItemType", GetMusicChannelFilterV2Resp_FilterItemType_name, GetMusicChannelFilterV2Resp_FilterItemType_value)
	proto.RegisterEnum("ga.GetMusicHomePageViewV2Resp_MusicHomePageType", GetMusicHomePageViewV2Resp_MusicHomePageType_name, GetMusicHomePageViewV2Resp_MusicHomePageType_value)
	proto.RegisterEnum("ga.GetMusicHomePageViewV2Resp_ActionType", GetMusicHomePageViewV2Resp_ActionType_name, GetMusicHomePageViewV2Resp_ActionType_value)
	proto.RegisterEnum("ga.MuseGetTopicChannelInfoRequest_ChannelInfoType", MuseGetTopicChannelInfoRequest_ChannelInfoType_name, MuseGetTopicChannelInfoRequest_ChannelInfoType_value)
	proto.RegisterEnum("ga.MuseGetTopicChannelInfoResponse_TabType", MuseGetTopicChannelInfoResponse_TabType_name, MuseGetTopicChannelInfoResponse_TabType_value)
}

func init() {
	proto.RegisterFile("music-topic-channel-logic_.proto", fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa)
}

var fileDescriptor_music_topic_channel_logic__bc07dcbdc765cafa = []byte{
	// 3913 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x3a, 0x4d, 0x73, 0xdb, 0x48,
	0x76, 0x06, 0xf5, 0x45, 0x3e, 0x8a, 0x14, 0xd5, 0x96, 0x6c, 0x5a, 0xb2, 0xc7, 0x0a, 0x76, 0x32,
	0xab, 0x91, 0x67, 0xe4, 0x29, 0xcd, 0x4c, 0x3e, 0xb6, 0x32, 0x55, 0x4b, 0xc9, 0x92, 0xcc, 0x19,
	0x51, 0x92, 0x21, 0x6a, 0x76, 0xbd, 0x95, 0x2c, 0x0a, 0x04, 0x5b, 0x64, 0x8f, 0x00, 0x34, 0x8d,
	0x06, 0x24, 0x6b, 0x93, 0x43, 0x52, 0x95, 0xca, 0x1c, 0x93, 0x43, 0x0e, 0x49, 0xaa, 0xf2, 0x03,
	0x36, 0xa7, 0x6c, 0xe5, 0x98, 0x5b, 0x0e, 0xa9, 0x54, 0xe5, 0x98, 0xff, 0x90, 0xaa, 0xe4, 0x0f,
	0xe4, 0x9a, 0xea, 0xd7, 0x0d, 0x02, 0x20, 0x29, 0x7b, 0xe8, 0xf2, 0xde, 0xd0, 0xef, 0x3d, 0xbc,
	0x7e, 0xdd, 0xef, 0xfb, 0x01, 0xb0, 0xe1, 0xc7, 0x82, 0xb9, 0x9f, 0x46, 0x7c, 0xc0, 0xdc, 0x4f,
	0xdd, 0xbe, 0x13, 0x04, 0xd4, 0xfb, 0xd4, 0xe3, 0x3d, 0xe6, 0xda, 0xdb, 0x83, 0x90, 0x47, 0x9c,
	0x14, 0x7a, 0xce, 0x5a, 0xa5, 0xe7, 0xd8, 0x1d, 0x47, 0x50, 0x05, 0x5a, 0x5b, 0x41, 0x72, 0x5b,
	0x93, 0x6b, 0xc2, 0xb5, 0x22, 0xf3, 0xf5, 0xd3, 0x23, 0x3f, 0x16, 0xd4, 0x66, 0x41, 0x44, 0x43,
	0x2a, 0x22, 0xbb, 0x1f, 0x77, 0x6c, 0x64, 0xa9, 0xd0, 0x26, 0x85, 0xb5, 0x43, 0x1a, 0xb5, 0xe4,
	0xc6, 0x7b, 0x8a, 0xc5, 0x01, 0xf3, 0x22, 0x1a, 0x7e, 0xbb, 0x63, 0xd1, 0x57, 0xe4, 0x23, 0x28,
	0xca, 0xad, 0xec, 0x90, 0xbe, 0xaa, 0x1b, 0x1b, 0xc6, 0x66, 0x79, 0xa7, 0xbc, 0xdd, 0x73, 0xb6,
	0x77, 0x1d, 0x41, 0x2d, 0xfa, 0xca, 0x5a, 0xe8, 0xa8, 0x07, 0xf2, 0x18, 0xca, 0x17, 0xf8, 0x9a,
	0x1d, 0xdd, 0x0c, 0x68, 0xbd, 0xb0, 0x61, 0x6c, 0x96, 0x2c, 0x50, 0xa0, 0xf6, 0xcd, 0x80, 0x9a,
	0x7f, 0x3b, 0x07, 0xeb, 0xb7, 0xee, 0x23, 0x06, 0xe4, 0x63, 0x28, 0xe9, 0x8d, 0xc4, 0x40, 0xef,
	0xb4, 0x98, 0xee, 0x24, 0x06, 0x56, 0xb1, 0xa3, 0x9f, 0x48, 0x0b, 0x16, 0xf5, 0x5e, 0x2c, 0xa2,
	0xbe, 0xa8, 0x17, 0x36, 0x66, 0x36, 0xcb, 0x3b, 0x5b, 0x92, 0xfa, 0x0d, 0x3b, 0x6c, 0xab, 0x45,
	0x33, 0xa2, 0xbe, 0xa5, 0x65, 0x95, 0xcf, 0x62, 0xed, 0x9f, 0x0b, 0x00, 0x29, 0x8e, 0xac, 0xc0,
	0x5c, 0xc4, 0x22, 0x8f, 0xa2, 0x10, 0x25, 0x4b, 0x2d, 0xc8, 0x26, 0xd4, 0x32, 0x7b, 0x66, 0x0f,
	0x59, 0x4d, 0x79, 0xc9, 0x83, 0x92, 0x75, 0x28, 0x25, 0x94, 0xdd, 0xfa, 0x0c, 0x92, 0x14, 0x35,
	0x49, 0x97, 0xfc, 0x6c, 0xc8, 0x46, 0xc4, 0x1d, 0x2d, 0xfe, 0x2c, 0x8a, 0xff, 0xe9, 0x0f, 0x13,
	0xff, 0x2c, 0xee, 0xe0, 0x09, 0xf4, 0xae, 0x7a, 0x29, 0x48, 0x0d, 0x66, 0x22, 0x36, 0xa8, 0xcf,
	0xe1, 0x7e, 0xf2, 0x31, 0x23, 0xb1, 0x13, 0x45, 0x5a, 0x2d, 0xf3, 0x1b, 0xc6, 0x66, 0x25, 0x79,
	0xb7, 0x11, 0x45, 0xa8, 0x1a, 0xf2, 0x19, 0x80, 0xcb, 0xa2, 0x1b, 0x5b, 0x1d, 0x7b, 0x01, 0xef,
	0x7e, 0x59, 0x8a, 0x73, 0xe6, 0xf8, 0x74, 0x8f, 0x45, 0x37, 0x6d, 0x89, 0xb0, 0x4a, 0x6e, 0xf2,
	0xb8, 0xf6, 0x2b, 0xa8, 0xe4, 0xc4, 0xb9, 0xe5, 0xd2, 0x4c, 0xa8, 0x64, 0x4f, 0xdb, 0xd5, 0x37,
	0x56, 0x4e, 0x65, 0xef, 0x92, 0xa7, 0xb0, 0x32, 0x72, 0x23, 0x4a, 0x54, 0x75, 0x73, 0xcb, 0xb9,
	0x63, 0xa2, 0x21, 0x1d, 0x41, 0xf5, 0x20, 0x7f, 0xe3, 0x2b, 0x50, 0x7b, 0x7e, 0xd2, 0xda, 0xb7,
	0x0f, 0x9a, 0x47, 0xed, 0x7d, 0xcb, 0x6e, 0xb6, 0xf7, 0x5b, 0xb5, 0x3b, 0x64, 0x15, 0x96, 0x4f,
	0x1b, 0x87, 0x59, 0xa8, 0xd5, 0xaa, 0x19, 0xa4, 0x02, 0x25, 0x04, 0x9f, 0x9e, 0x9c, 0xb5, 0x6b,
	0x05, 0xf3, 0x05, 0x54, 0x72, 0xa7, 0x94, 0xea, 0xc3, 0xcb, 0x08, 0x1c, 0x3f, 0x39, 0x4d, 0x51,
	0x02, 0x8e, 0x1d, 0x9f, 0x92, 0x1f, 0x41, 0x65, 0x10, 0xf2, 0x2b, 0x16, 0xb8, 0x54, 0x11, 0xa8,
	0x03, 0x2d, 0x26, 0x40, 0x49, 0x64, 0xfe, 0x77, 0x01, 0x56, 0x8f, 0x98, 0x88, 0x9e, 0xf3, 0x4e,
	0xe7, 0x46, 0x6b, 0x72, 0x3a, 0x67, 0x5a, 0x81, 0x39, 0x97, 0xc7, 0x41, 0x84, 0xec, 0x2b, 0x96,
	0x5a, 0x48, 0x15, 0x0b, 0xfa, 0x1a, 0x2f, 0x66, 0xce, 0x92, 0x8f, 0x79, 0x53, 0x9b, 0x1d, 0x31,
	0x35, 0x13, 0x2a, 0x4c, 0xd8, 0x22, 0x72, 0xc2, 0xc8, 0xf6, 0xb8, 0xd3, 0x45, 0xdb, 0x28, 0x5a,
	0x65, 0x26, 0xce, 0x24, 0xec, 0x88, 0x3b, 0x5d, 0xf2, 0x09, 0x10, 0xfa, 0x7a, 0xc0, 0x05, 0x1d,
	0x46, 0x0f, 0xd6, 0x15, 0xf5, 0xf9, 0x8d, 0x99, 0xcd, 0x8a, 0x55, 0x53, 0x18, 0x7d, 0x80, 0x66,
	0x57, 0x90, 0x0f, 0xa1, 0x9a, 0x53, 0xa7, 0xa8, 0x2f, 0x6c, 0xcc, 0xc8, 0xe3, 0x67, 0xf4, 0x29,
	0x24, 0xcf, 0x84, 0xd9, 0xc0, 0x71, 0x2f, 0x9d, 0x1e, 0x95, 0xd2, 0x15, 0x51, 0xba, 0x9a, 0xc6,
	0x9c, 0x2a, 0x44, 0xb3, 0x4b, 0x7e, 0x02, 0x6b, 0x4c, 0xd8, 0xb1, 0xa0, 0xa1, 0xed, 0x71, 0xd7,
	0x89, 0x18, 0x0f, 0x6c, 0x27, 0x8e, 0xfa, 0x36, 0x1f, 0xd0, 0xa0, 0x5e, 0x42, 0x91, 0xef, 0x31,
	0x71, 0x2e, 0x68, 0x78, 0xa4, 0xf1, 0x8d, 0x38, 0xea, 0x9f, 0x0c, 0x68, 0x60, 0xee, 0xc1, 0x83,
	0xc4, 0x61, 0x9e, 0x73, 0x9f, 0x9e, 0x3a, 0x3d, 0xfa, 0x2d, 0xa3, 0xd7, 0x53, 0xdd, 0xb5, 0xf9,
	0x3f, 0xf3, 0x69, 0xfc, 0x1b, 0xe5, 0x32, 0x5d, 0x58, 0x7a, 0x06, 0xb3, 0xc3, 0xb0, 0x50, 0xdd,
	0xf9, 0x2c, 0xeb, 0xcf, 0xe3, 0x8c, 0xb7, 0x73, 0x70, 0x69, 0xc6, 0x16, 0xbe, 0x4d, 0x7e, 0x01,
	0x4b, 0x17, 0xb1, 0xe7, 0xa1, 0x13, 0xd8, 0x57, 0x8c, 0x5e, 0x8b, 0xfa, 0x0c, 0x06, 0x88, 0x9d,
	0x69, 0x18, 0x7e, 0xbb, 0x23, 0x31, 0x56, 0x45, 0xb2, 0x92, 0xcc, 0xe5, 0x4a, 0x90, 0x5f, 0xc2,
	0xb2, 0x70, 0x43, 0x9e, 0xe7, 0x3e, 0xfb, 0xce, 0xdc, 0x97, 0x14, 0xb3, 0x21, 0xff, 0xb5, 0xff,
	0x2b, 0xc0, 0xdd, 0x09, 0x84, 0xb7, 0x44, 0x87, 0x75, 0x28, 0x49, 0x3b, 0x52, 0x18, 0xe5, 0x48,
	0x45, 0x11, 0x77, 0x94, 0x1b, 0x12, 0x98, 0x65, 0x2e, 0x0f, 0x74, 0x18, 0xc0, 0x67, 0xd2, 0x04,
	0x70, 0x5c, 0x69, 0x01, 0x72, 0x47, 0xb4, 0xf7, 0xea, 0xce, 0xc7, 0x6f, 0x91, 0xbb, 0x31, 0x7c,
	0xc1, 0xca, 0xbc, 0x4c, 0x4e, 0xd4, 0xde, 0xea, 0x06, 0xe6, 0xde, 0xf9, 0x06, 0xa4, 0xbc, 0xea,
	0x6a, 0x3f, 0x84, 0xea, 0xab, 0x98, 0xb9, 0x97, 0xb6, 0xef, 0x44, 0x6e, 0x5f, 0x5a, 0xfc, 0xbc,
	0x0a, 0x0d, 0x08, 0x6d, 0x49, 0x60, 0xb3, 0x2b, 0x5d, 0x38, 0x0e, 0x3d, 0x0c, 0xb1, 0x25, 0x4b,
	0x3e, 0x92, 0x55, 0x98, 0x8f, 0x9c, 0x5e, 0xe2, 0x21, 0x15, 0x6b, 0x2e, 0x72, 0x7a, 0xcd, 0xae,
	0x04, 0x77, 0x7a, 0x36, 0xf3, 0x7b, 0xe8, 0x02, 0x25, 0x6b, 0xae, 0xd3, 0x6b, 0xfa, 0x3d, 0x72,
	0x1f, 0x16, 0xa4, 0xc8, 0x92, 0x1c, 0x10, 0x3e, 0x2f, 0x97, 0xcd, 0xae, 0xb9, 0x0f, 0xcb, 0x63,
	0x06, 0x45, 0xee, 0x01, 0xc9, 0x01, 0xed, 0x83, 0xd8, 0xf3, 0x6a, 0x77, 0x48, 0x1d, 0x56, 0xf2,
	0xf0, 0x33, 0xd4, 0x63, 0xcd, 0x30, 0xff, 0x04, 0x20, 0xbd, 0x30, 0x72, 0x1f, 0xee, 0x9e, 0xb1,
	0xa0, 0x67, 0x37, 0x6c, 0x8b, 0xc7, 0x41, 0xd7, 0x7e, 0xc6, 0x1c, 0x8f, 0xf7, 0x6a, 0x77, 0xc8,
	0x12, 0x94, 0x5f, 0xe0, 0x61, 0xf1, 0x5c, 0x35, 0x83, 0x2c, 0xc0, 0xcc, 0x79, 0xe8, 0xd5, 0x0a,
	0xe4, 0x21, 0xd4, 0x73, 0xaf, 0x64, 0xc9, 0x66, 0xcc, 0xab, 0xb4, 0x04, 0x48, 0xf6, 0x56, 0x3c,
	0xa7, 0x0b, 0x8f, 0x4f, 0x73, 0x8e, 0xb6, 0x2e, 0x69, 0x26, 0xf0, 0x4c, 0x7d, 0xca, 0xfc, 0x75,
	0x01, 0x1e, 0xde, 0xbe, 0xf1, 0x74, 0x5e, 0xde, 0x80, 0x39, 0x65, 0x35, 0xaa, 0xea, 0x78, 0x32,
	0xc9, 0x6a, 0xb2, 0xbc, 0xb7, 0xf5, 0x42, 0x9a, 0x8b, 0x7a, 0x73, 0xed, 0x1f, 0x0c, 0x80, 0x14,
	0xfa, 0x2e, 0xde, 0xb1, 0x02, 0x73, 0xcc, 0x77, 0x7a, 0x49, 0x96, 0x54, 0x0b, 0xf2, 0x08, 0xa0,
	0xe3, 0xb8, 0x97, 0xb6, 0xcb, 0x3d, 0x1e, 0xea, 0x7c, 0x50, 0x92, 0x90, 0x3d, 0x09, 0x98, 0x60,
	0xa2, 0x73, 0xe3, 0x26, 0x6a, 0xfe, 0xbd, 0x01, 0xeb, 0x2f, 0x86, 0x80, 0x77, 0xcf, 0x61, 0xe3,
	0xbb, 0x15, 0x26, 0x38, 0xc4, 0xe4, 0x64, 0x31, 0x33, 0x39, 0x59, 0x98, 0x7f, 0x65, 0xc0, 0xc3,
	0xdb, 0x65, 0x9b, 0x4e, 0x8f, 0x8f, 0x00, 0xd2, 0x9c, 0xa7, 0x13, 0x6d, 0xc9, 0x4d, 0x92, 0x1d,
	0x79, 0x08, 0xa5, 0x0b, 0xce, 0xa3, 0x41, 0xc8, 0x82, 0x48, 0xcb, 0x93, 0x02, 0xcc, 0x7f, 0x33,
	0xe0, 0x9e, 0x45, 0xe3, 0x80, 0xf1, 0xa0, 0x29, 0xeb, 0x6a, 0x15, 0x59, 0x4e, 0x63, 0xd1, 0x27,
	0x9f, 0x40, 0xe9, 0x3b, 0xce, 0x02, 0x4c, 0x69, 0x5a, 0x84, 0x25, 0x29, 0x82, 0x26, 0x97, 0x89,
	0xcc, 0x2a, 0x4a, 0x0a, 0xf9, 0x44, 0x76, 0x60, 0x31, 0x54, 0x08, 0xf5, 0x42, 0x61, 0xf2, 0x0b,
	0xe5, 0x30, 0x5d, 0xc8, 0xd0, 0x18, 0xd1, 0xd7, 0x89, 0x54, 0xf8, 0x4c, 0x1e, 0x40, 0xd1, 0x67,
	0xae, 0x8d, 0x70, 0xa5, 0xf8, 0x05, 0x9f, 0xb9, 0x6d, 0x89, 0xba, 0x0f, 0x0b, 0x3e, 0x8d, 0x9c,
	0x54, 0xdf, 0xf3, 0x72, 0xd9, 0xec, 0x9a, 0x0c, 0xca, 0x99, 0x3d, 0x30, 0x36, 0xb1, 0x2e, 0x8a,
	0x5c, 0xb1, 0xe4, 0x23, 0x59, 0x83, 0xa2, 0x14, 0x2a, 0x53, 0xe8, 0x0c, 0xd7, 0x12, 0x17, 0x30,
	0xf7, 0x12, 0x71, 0xba, 0xc8, 0x4d, 0xd6, 0x49, 0xa1, 0x32, 0x3b, 0x2c, 0x54, 0xcc, 0xbf, 0x28,
	0xc0, 0xbd, 0xd3, 0xb8, 0xe3, 0x31, 0xd1, 0xcf, 0xd6, 0xb7, 0xd3, 0xd8, 0xd3, 0x5b, 0xf4, 0x85,
	0x71, 0xb4, 0x93, 0x18, 0x0f, 0xc6, 0x51, 0x59, 0x5d, 0xee, 0x42, 0xa5, 0xe3, 0x71, 0xf7, 0xd2,
	0xe6, 0x03, 0xa9, 0xa1, 0x24, 0xdb, 0x3d, 0x92, 0x5b, 0xe4, 0xda, 0xa6, 0xed, 0x5d, 0x49, 0x76,
	0x82, 0x54, 0xd6, 0x62, 0x27, 0x5d, 0x08, 0xf2, 0x3b, 0xb0, 0x98, 0xec, 0x8c, 0xc7, 0x55, 0xb7,
	0x58, 0xd6, 0x30, 0xac, 0x0b, 0x4d, 0xa8, 0x88, 0x3e, 0xbf, 0xb6, 0x7b, 0x94, 0xdb, 0x2c, 0xb8,
	0xe0, 0x18, 0xfc, 0x8b, 0x56, 0x59, 0x02, 0x0f, 0x29, 0x6f, 0x06, 0x17, 0xdc, 0xfc, 0x4f, 0x03,
	0xee, 0x4f, 0xbc, 0x83, 0xe9, 0xec, 0x76, 0x13, 0xd0, 0x2f, 0x7a, 0xd4, 0x76, 0x39, 0xf7, 0xec,
	0x2e, 0xbf, 0x0e, 0xf4, 0x6d, 0x54, 0x15, 0x7c, 0x8f, 0x73, 0xef, 0x19, 0xbf, 0x0e, 0xc8, 0x8f,
	0x61, 0xe9, 0x22, 0xa4, 0xf4, 0x57, 0xd4, 0xee, 0xc6, 0x21, 0x56, 0x4e, 0xfa, 0x6e, 0xaa, 0x0a,
	0xfc, 0x4c, 0x43, 0xc9, 0x0e, 0xac, 0x3a, 0x71, 0xc4, 0xed, 0x2e, 0x13, 0x3e, 0x13, 0x22, 0x25,
	0x9f, 0x45, 0xf2, 0xbb, 0x12, 0xf9, 0x4c, 0xe1, 0x92, 0x77, 0x4c, 0x0a, 0x0f, 0xf7, 0x9c, 0xc0,
	0xa5, 0x5e, 0xf6, 0x2c, 0xfa, 0x78, 0xef, 0x4f, 0xad, 0xe6, 0xd7, 0xf0, 0xe8, 0x0d, 0xdb, 0x4c,
	0x75, 0x73, 0xa6, 0x9b, 0x16, 0x7a, 0x69, 0x03, 0xb1, 0x7b, 0xd3, 0xec, 0x8a, 0x29, 0x05, 0x1e,
	0xd6, 0xdc, 0x2a, 0x09, 0xc8, 0xc8, 0xa0, 0x8b, 0x6e, 0x61, 0xfe, 0xaf, 0x91, 0xe6, 0xb8, 0xb1,
	0x5d, 0xa6, 0x6d, 0x73, 0x93, 0x9d, 0x7c, 0x67, 0xa0, 0xd3, 0xcd, 0x76, 0x36, 0xdd, 0x4c, 0xe0,
	0xaf, 0xbb, 0xc4, 0x96, 0x33, 0xd8, 0x0f, 0xa2, 0xf0, 0x26, 0x91, 0xac, 0xe5, 0x0c, 0xd6, 0x5e,
	0x24, 0x7d, 0x53, 0x82, 0x94, 0x7e, 0x7a, 0x49, 0x6f, 0x74, 0xda, 0x91, 0x8f, 0xe4, 0x63, 0x98,
	0xbb, 0x72, 0xbc, 0x98, 0xea, 0x38, 0x74, 0x77, 0x98, 0x5a, 0x33, 0xbd, 0xb3, 0xa2, 0xf8, 0x49,
	0xe1, 0x0f, 0x0c, 0xf3, 0xcf, 0x0b, 0xb0, 0x34, 0x82, 0xce, 0xf7, 0x24, 0xc6, 0x48, 0x4f, 0x42,
	0x60, 0x36, 0x13, 0x4d, 0xf0, 0x79, 0x62, 0xa5, 0x77, 0x0f, 0xe6, 0x31, 0xa5, 0x29, 0x7f, 0x2d,
	0x59, 0x7a, 0x45, 0xbe, 0x80, 0xb2, 0x4c, 0x8a, 0x8a, 0x5f, 0x52, 0xb8, 0x4d, 0x94, 0x12, 0x44,
	0xdc, 0x51, 0x4b, 0xf1, 0xdb, 0xec, 0x84, 0xcd, 0xff, 0x2a, 0xc0, 0x8a, 0x6c, 0xf6, 0xb2, 0xf6,
	0x39, 0x95, 0x3d, 0xe5, 0xee, 0xab, 0x30, 0x72, 0x5f, 0x1f, 0x42, 0x35, 0x3d, 0x2f, 0x1a, 0xdc,
	0x8c, 0xea, 0xb8, 0x86, 0xa7, 0x93, 0x1d, 0xd7, 0xb0, 0x5d, 0x9c, 0xcd, 0xb6, 0x8b, 0xef, 0xbf,
	0xff, 0x9b, 0x9c, 0xac, 0x17, 0xde, 0xa9, 0xb3, 0x2b, 0xbe, 0xb1, 0xb3, 0xfb, 0x3b, 0x43, 0xb5,
	0xd0, 0x23, 0xb7, 0x3a, 0x9d, 0xff, 0x7c, 0x02, 0x45, 0x2d, 0x54, 0x52, 0xac, 0xd5, 0x86, 0x96,
	0x92, 0x04, 0xdf, 0x21, 0x05, 0xf9, 0x08, 0x96, 0x98, 0xb0, 0x3b, 0x3c, 0x8a, 0xb8, 0x6f, 0x87,
	0xd4, 0x71, 0xfb, 0x68, 0x91, 0x45, 0xab, 0xc2, 0xc4, 0x2e, 0x42, 0x2d, 0x09, 0x34, 0xbf, 0x2f,
	0xc2, 0x62, 0x96, 0xc5, 0x48, 0x04, 0x33, 0x46, 0x13, 0xd3, 0x68, 0xf6, 0x28, 0x8c, 0x67, 0x8f,
	0xcf, 0x60, 0x25, 0x21, 0xf1, 0xa9, 0xdf, 0xa1, 0xa1, 0xad, 0xd4, 0xa9, 0xa2, 0x75, 0x72, 0xe7,
	0x2d, 0x44, 0xed, 0xa1, 0x6e, 0x1f, 0x40, 0x11, 0xb3, 0x9d, 0xab, 0x83, 0x74, 0xc9, 0x5a, 0x90,
	0xf9, 0x4e, 0xba, 0x8e, 0x46, 0x75, 0xa9, 0x70, 0x75, 0xa6, 0x92, 0xa8, 0x67, 0x54, 0xb8, 0xe4,
	0x47, 0x50, 0xe1, 0xd7, 0x81, 0x74, 0x03, 0x57, 0x6d, 0xa0, 0x5b, 0x14, 0x04, 0x36, 0x14, 0x4c,
	0xda, 0xa3, 0x22, 0x92, 0x29, 0x7c, 0x01, 0x53, 0x78, 0x11, 0x01, 0x67, 0xf4, 0xb5, 0xcc, 0xfa,
	0xfa, 0x5d, 0x51, 0x2f, 0xa2, 0x25, 0x0e, 0xd7, 0xd2, 0x67, 0x45, 0xe4, 0x44, 0xb1, 0xd0, 0x2d,
	0x8b, 0x5e, 0x49, 0xff, 0x16, 0x3c, 0xe8, 0xe9, 0x86, 0x05, 0x9f, 0xc9, 0x36, 0xcc, 0x87, 0x54,
	0x16, 0xc3, 0xf5, 0x32, 0xaa, 0xf0, 0xde, 0x98, 0x62, 0x10, 0x6b, 0x69, 0x2a, 0xf2, 0x04, 0xe6,
	0x3c, 0xa7, 0x43, 0xbd, 0xfa, 0x22, 0x96, 0xfc, 0xab, 0xa3, 0xe4, 0x47, 0x12, 0x69, 0x29, 0x1a,
	0x62, 0xc2, 0x5c, 0xcf, 0xe3, 0xe1, 0x4d, 0xbd, 0x92, 0x9a, 0xc7, 0x37, 0xd1, 0xd5, 0xa1, 0x84,
	0x59, 0x0a, 0x45, 0x1a, 0x50, 0x19, 0xd0, 0x50, 0xf0, 0xc0, 0xf1, 0x6c, 0x97, 0x86, 0x51, 0xbd,
	0x8a, 0xb4, 0x0f, 0x47, 0x19, 0x9f, 0x6a, 0xa2, 0x3d, 0x1a, 0x46, 0xd6, 0xe2, 0x20, 0xb3, 0xca,
	0x57, 0x88, 0x4b, 0x23, 0x15, 0x62, 0xa6, 0x1e, 0xa9, 0x65, 0xeb, 0x91, 0x75, 0x28, 0x85, 0xb4,
	0x27, 0x5d, 0x81, 0x75, 0xeb, 0xcb, 0x1b, 0xc6, 0xe6, 0xac, 0x55, 0x54, 0x80, 0x66, 0x37, 0x49,
	0xed, 0xd2, 0x0e, 0x3c, 0x7a, 0xa5, 0xec, 0x89, 0xa4, 0xa9, 0x5d, 0x9e, 0x53, 0x82, 0x9b, 0xdd,
	0x44, 0xc9, 0x68, 0x50, 0x77, 0x87, 0x4a, 0x46, 0x63, 0xfa, 0x00, 0x66, 0x06, 0xcc, 0xa9, 0xaf,
	0xa4, 0x67, 0xc7, 0xf3, 0x9c, 0x32, 0xc7, 0x92, 0x08, 0xf2, 0x7b, 0x50, 0x4e, 0x46, 0xc1, 0x2c,
	0xe8, 0xd5, 0x57, 0x91, 0x6e, 0x65, 0x48, 0xd7, 0x4c, 0x71, 0x56, 0x96, 0x50, 0xaa, 0xd1, 0xe3,
	0x3d, 0x5e, 0xbf, 0xa7, 0xd4, 0x28, 0x9f, 0x65, 0xf5, 0x8a, 0x03, 0x6b, 0x5b, 0x70, 0x97, 0x39,
	0x5e, 0xfd, 0x7e, 0x5a, 0xbd, 0x22, 0xb3, 0x33, 0x04, 0x5b, 0x65, 0x3f, 0x5d, 0x90, 0x17, 0x50,
	0x12, 0x8e, 0x4f, 0x6d, 0x19, 0x42, 0xeb, 0x75, 0x7c, 0xe1, 0x0b, 0xf9, 0xc2, 0x6d, 0x43, 0xea,
	0xb6, 0xac, 0xd2, 0xb4, 0x36, 0x92, 0x28, 0x2c, 0xeb, 0x29, 0xab, 0x28, 0xf4, 0x2a, 0xeb, 0x62,
	0x18, 0xdb, 0x1f, 0xe0, 0x9d, 0x25, 0x2e, 0xa6, 0x47, 0x84, 0x73, 0x58, 0xee, 0xd5, 0xd7, 0x74,
	0x8f, 0x25, 0x17, 0xd2, 0x75, 0x55, 0x11, 0x88, 0x8e, 0xb4, 0xae, 0x74, 0x88, 0x10, 0x74, 0xa5,
	0x21, 0x1a, 0xb9, 0x3e, 0x44, 0x5f, 0x50, 0x68, 0x1c, 0x44, 0x0e, 0xe0, 0x61, 0xf6, 0x94, 0x4e,
	0x70, 0xf9, 0x9c, 0x07, 0x3c, 0x3c, 0x63, 0xbd, 0x40, 0x0a, 0x38, 0x4c, 0x6c, 0x46, 0x26, 0xb1,
	0x6d, 0x42, 0x4d, 0x44, 0x37, 0x1e, 0x55, 0x3d, 0x9a, 0xed, 0x31, 0x11, 0xe9, 0x1a, 0xa2, 0x8a,
	0x70, 0xec, 0xd4, 0x64, 0xe4, 0x9b, 0x54, 0xe5, 0x9b, 0xbf, 0x36, 0xa0, 0x9c, 0xd9, 0x52, 0xc6,
	0x2c, 0x1d, 0x30, 0xd0, 0xf2, 0xed, 0x4e, 0x4f, 0x87, 0x97, 0x8a, 0x02, 0xa3, 0x5b, 0xec, 0xf6,
	0xc8, 0x16, 0x2c, 0xe7, 0xe8, 0x32, 0x8c, 0x97, 0x32, 0x94, 0xd8, 0x2e, 0x1c, 0x40, 0x35, 0x74,
	0x82, 0x4b, 0x5b, 0xb0, 0x5e, 0xa0, 0x6a, 0xd9, 0x59, 0x54, 0xd2, 0xc6, 0xa8, 0x56, 0x47, 0xcf,
	0x6b, 0x2d, 0xca, 0xf7, 0x92, 0x95, 0xf9, 0x05, 0x14, 0x13, 0xc3, 0x93, 0xb7, 0xaf, 0xdc, 0xd7,
	0xc0, 0xa3, 0x6a, 0x3f, 0x9d, 0x50, 0x0c, 0x98, 0x9b, 0x50, 0x1b, 0x35, 0xc3, 0x54, 0x77, 0x46,
	0x46, 0x77, 0xe6, 0x9f, 0xea, 0xe1, 0x46, 0x2e, 0x60, 0x90, 0xdf, 0x85, 0xaa, 0x0a, 0x19, 0xc3,
	0x18, 0xa7, 0x5e, 0xaa, 0x28, 0x68, 0x12, 0xe4, 0x1e, 0x43, 0x59, 0x93, 0x61, 0x9c, 0xd4, 0x5f,
	0x2b, 0x14, 0x08, 0x43, 0xe5, 0x23, 0xd0, 0x2b, 0x3b, 0x1d, 0xb9, 0x96, 0x14, 0xe4, 0x8c, 0xbe,
	0x36, 0xbf, 0x37, 0xa0, 0x98, 0x84, 0x14, 0x49, 0x8b, 0x41, 0x25, 0x3b, 0x32, 0x2e, 0x21, 0x04,
	0x1d, 0x72, 0x1d, 0xd4, 0x02, 0xa7, 0x39, 0x3a, 0xc1, 0x23, 0xa0, 0xe9, 0xf7, 0xc8, 0x06, 0x2c,
	0x2a, 0xa4, 0x9e, 0xf6, 0x28, 0xa5, 0x28, 0x7e, 0xbb, 0x38, 0xf2, 0x19, 0x72, 0x97, 0xb7, 0xab,
	0x33, 0xbc, 0x62, 0x28, 0x35, 0x60, 0xfe, 0xa5, 0x01, 0xf5, 0xdb, 0x02, 0xd6, 0x44, 0x0b, 0x4c,
	0xec, 0xaa, 0x90, 0xe9, 0x1e, 0xb1, 0x80, 0xf0, 0x78, 0xa8, 0xab, 0x0b, 0xb5, 0x90, 0x56, 0x23,
	0xb1, 0xb6, 0xe8, 0x3b, 0x5d, 0x7e, 0x9d, 0x9b, 0x2a, 0x2c, 0x49, 0xc4, 0x19, 0xc2, 0xd1, 0x62,
	0xcd, 0x9f, 0xc3, 0xfd, 0x43, 0x1a, 0xb5, 0x9d, 0x8e, 0xae, 0xcd, 0x9f, 0xf3, 0xc8, 0x72, 0xfd,
	0xee, 0x34, 0x85, 0x50, 0x1a, 0x31, 0x0b, 0x99, 0x88, 0x69, 0x86, 0x50, 0x9f, 0xcc, 0x79, 0xba,
	0x62, 0xe0, 0x09, 0xcc, 0x65, 0x3f, 0x16, 0x61, 0x06, 0x19, 0x67, 0xaa, 0x68, 0x4c, 0x01, 0xcb,
	0x63, 0x38, 0x52, 0x85, 0xc2, 0xb0, 0xa2, 0x2d, 0xb0, 0x5b, 0x6b, 0xd9, 0x7e, 0x3a, 0x30, 0xc0,
	0x67, 0xf2, 0x11, 0xcc, 0x63, 0x3b, 0x99, 0xf4, 0x9e, 0xd5, 0xa1, 0x23, 0x61, 0xcb, 0x69, 0x69,
	0xac, 0x79, 0x00, 0x90, 0x42, 0x65, 0x84, 0x57, 0x8d, 0xeb, 0xb0, 0xa6, 0x58, 0xc0, 0x75, 0xb3,
	0x2b, 0x2d, 0x82, 0x7a, 0xd4, 0xa7, 0x41, 0x94, 0x69, 0x99, 0x34, 0xa4, 0xd9, 0x35, 0xfb, 0xf0,
	0xf8, 0x90, 0x46, 0x16, 0x15, 0x3c, 0x0e, 0x5d, 0xba, 0xc7, 0x83, 0x0b, 0xd6, 0xdb, 0xbd, 0x19,
	0x96, 0x71, 0xef, 0xb1, 0x39, 0xfb, 0x1b, 0x03, 0x36, 0xde, 0xbc, 0xd5, 0x74, 0x3a, 0x9a, 0x64,
	0x9a, 0x93, 0xba, 0x83, 0x07, 0x50, 0xfc, 0x2e, 0xf6, 0x07, 0x76, 0x1c, 0x7a, 0x49, 0xf5, 0x23,
	0xd7, 0xe7, 0xa1, 0x67, 0xfe, 0x47, 0x01, 0x3e, 0x68, 0xc5, 0x82, 0x4a, 0x93, 0xc9, 0x24, 0x0e,
	0x8c, 0x4f, 0xf4, 0x55, 0x4c, 0x45, 0xf4, 0xbe, 0x06, 0x0e, 0xbf, 0x84, 0xe5, 0x21, 0x3a, 0xb8,
	0xe0, 0xe9, 0x47, 0xab, 0xaa, 0x9a, 0x24, 0xbf, 0x59, 0x8a, 0xed, 0x0c, 0x08, 0x07, 0x95, 0x4b,
	0x6e, 0x1e, 0x30, 0x96, 0xd4, 0x66, 0xc7, 0x92, 0x9a, 0xf9, 0xc7, 0xb0, 0x34, 0xc2, 0x86, 0x6c,
	0xc2, 0x87, 0x7b, 0xcf, 0x1b, 0xc7, 0xc7, 0xfb, 0x47, 0x76, 0xf3, 0xf8, 0xe0, 0xc4, 0x6e, 0xbf,
	0x3c, 0xdd, 0xb7, 0x8f, 0x4f, 0xac, 0x56, 0xe3, 0xc8, 0x3e, 0x3f, 0x3e, 0x3b, 0xdd, 0xdf, 0x6b,
	0x1e, 0x34, 0xf7, 0x9f, 0xd5, 0xee, 0x90, 0xc7, 0xb0, 0x3e, 0x4e, 0x79, 0x7a, 0xd4, 0x78, 0x89,
	0x4f, 0x35, 0xc3, 0xfc, 0xc7, 0x05, 0x78, 0x7c, 0xeb, 0x21, 0xc4, 0x80, 0x07, 0x82, 0xbe, 0xc7,
	0x79, 0xdb, 0x2d, 0xf3, 0x9b, 0x6c, 0xa1, 0x33, 0x9b, 0x2f, 0x74, 0x36, 0x60, 0x91, 0x09, 0x9b,
	0x05, 0x76, 0x2f, 0xe4, 0x71, 0x90, 0xb4, 0x37, 0xc0, 0x44, 0x33, 0x38, 0x44, 0x88, 0xdc, 0x92,
	0x09, 0x7b, 0x10, 0xb2, 0x2b, 0x27, 0xa2, 0x7a, 0x24, 0x53, 0x62, 0xe2, 0x54, 0x01, 0x64, 0x60,
	0xee, 0x53, 0xa7, 0xab, 0x52, 0x80, 0xea, 0x62, 0x8a, 0x12, 0x80, 0x09, 0xe0, 0x8f, 0xa0, 0x3a,
	0xf0, 0x9c, 0x1b, 0x16, 0xf4, 0xf4, 0xe8, 0x08, 0xeb, 0xdd, 0xb4, 0xf4, 0xa4, 0xa7, 0x0a, 0xab,
	0x27, 0x46, 0x95, 0x41, 0x76, 0x49, 0x7e, 0x0a, 0x35, 0x71, 0xcd, 0x22, 0xb7, 0x6f, 0x4b, 0xb8,
	0x4a, 0xa3, 0xa5, 0x5c, 0xa5, 0x4b, 0xcf, 0x10, 0x2f, 0xb9, 0xe0, 0x8d, 0x56, 0x45, 0x6e, 0x4d,
	0x0e, 0xd4, 0xc1, 0x51, 0xf5, 0x80, 0x56, 0xf5, 0xe4, 0x8d, 0x56, 0xa5, 0x14, 0x22, 0x43, 0x1a,
	0x9a, 0x93, 0xbc, 0xa5, 0xc4, 0x8c, 0xae, 0xa9, 0xe7, 0x72, 0x9f, 0xaa, 0xac, 0x5f, 0x56, 0xed,
	0x87, 0x86, 0x61, 0xc6, 0xff, 0x29, 0xd4, 0xa2, 0x3e, 0x0b, 0xbb, 0xf6, 0xc0, 0x09, 0xa3, 0x1b,
	0xbb, 0x27, 0xef, 0x7a, 0x31, 0x2f, 0x6c, 0x5b, 0xe2, 0x4f, 0x25, 0xfa, 0xd0, 0xf1, 0xa9, 0x55,
	0x8d, 0x72, 0x6b, 0xb2, 0x0d, 0x77, 0x45, 0x9f, 0x5d, 0x44, 0x76, 0xc8, 0xb9, 0x9f, 0x8e, 0x8f,
	0x2a, 0xa8, 0xc9, 0x65, 0x44, 0x59, 0x9c, 0xfb, 0xc3, 0x81, 0xd3, 0x84, 0xc9, 0x54, 0x75, 0xe2,
	0x64, 0x2a, 0xfd, 0x3a, 0xb2, 0x94, 0xfd, 0x3a, 0xb2, 0x0e, 0xa5, 0x88, 0x3a, 0xbe, 0xd2, 0x5c,
	0x4d, 0x69, 0x4e, 0x02, 0x50, 0x73, 0xb2, 0x67, 0xee, 0xf3, 0x6b, 0x3b, 0xa5, 0x58, 0x46, 0xcd,
	0x2f, 0x4a, 0x68, 0x3b, 0xa1, 0x42, 0x91, 0xf9, 0xb5, 0x3d, 0x50, 0x41, 0xde, 0xee, 0xc4, 0x51,
	0xc4, 0x03, 0x2c, 0xb7, 0x8b, 0x52, 0x64, 0x7e, 0xad, 0xc3, 0xff, 0x2e, 0x22, 0xc8, 0x1f, 0x42,
	0xc5, 0x75, 0x22, 0xda, 0x93, 0x99, 0x18, 0x95, 0x72, 0x17, 0x95, 0x92, 0x14, 0xce, 0x74, 0x4f,
	0x23, 0xf1, 0xf6, 0x17, 0xdd, 0xcc, 0xca, 0x3c, 0x87, 0x05, 0xad, 0x16, 0xe9, 0x74, 0xed, 0xc6,
	0xee, 0x1b, 0xbc, 0x72, 0x19, 0x2a, 0x43, 0x82, 0xc3, 0x46, 0x6b, 0xbf, 0x66, 0x90, 0x7b, 0x40,
	0x86, 0xa0, 0x56, 0xf3, 0xb8, 0xa9, 0xe0, 0x05, 0xf3, 0x73, 0x2c, 0x80, 0x46, 0xec, 0x08, 0x0b,
	0x17, 0xa9, 0x04, 0x9f, 0x77, 0xa9, 0xa7, 0x7d, 0xa9, 0x24, 0x21, 0x2d, 0x09, 0x30, 0x7f, 0x53,
	0x50, 0x6f, 0xe5, 0x15, 0x2a, 0x2f, 0x54, 0x55, 0x86, 0x32, 0xa4, 0xea, 0xa1, 0x0d, 0x02, 0xce,
	0x43, 0x6c, 0x70, 0xf1, 0x96, 0x5c, 0xc4, 0xaa, 0xe0, 0x5c, 0x52, 0x10, 0x89, 0x6e, 0x41, 0x55,
	0x9a, 0x0c, 0xfe, 0x91, 0xa2, 0x2c, 0x5d, 0x7d, 0xaf, 0xfc, 0xf1, 0x64, 0xe3, 0x51, 0x26, 0xec,
	0xf8, 0x54, 0xc6, 0x08, 0x55, 0x37, 0xf6, 0x32, 0xab, 0xb5, 0xbf, 0x36, 0xb0, 0x04, 0xcc, 0x91,
	0xc8, 0xbe, 0x73, 0xe0, 0x39, 0xd1, 0x05, 0x0f, 0xfd, 0x44, 0xbc, 0x64, 0x9d, 0xcb, 0x06, 0x85,
	0x5c, 0x36, 0x90, 0xc6, 0xdf, 0xe5, 0xd7, 0x81, 0xc7, 0x9d, 0x2e, 0xa2, 0x55, 0x12, 0x29, 0x27,
	0x30, 0x4d, 0x92, 0xcc, 0x32, 0x32, 0x41, 0xa6, 0xac, 0x61, 0xf8, 0x3d, 0xff, 0x9f, 0x0c, 0x78,
	0x94, 0x75, 0xb8, 0x73, 0x41, 0xc3, 0x9f, 0x39, 0x61, 0x70, 0xcc, 0x23, 0x76, 0x71, 0xd3, 0x12,
	0x3d, 0x59, 0x4e, 0x0e, 0xe2, 0x40, 0x9a, 0x11, 0x9a, 0x86, 0xca, 0xd7, 0xa0, 0x40, 0xa8, 0xf7,
	0xb7, 0x04, 0xbf, 0x07, 0x50, 0x8c, 0x59, 0x57, 0x75, 0x03, 0x33, 0x38, 0x7c, 0x59, 0x88, 0x59,
	0x17, 0xdb, 0x80, 0x6d, 0x28, 0xb2, 0xc0, 0x56, 0x43, 0xb9, 0xd9, 0x74, 0xdc, 0x65, 0x31, 0xb7,
	0x2f, 0x9d, 0x77, 0x5f, 0xa5, 0x7d, 0x6b, 0x81, 0x05, 0xdf, 0x4a, 0x1a, 0xf3, 0x1a, 0x3e, 0xba,
	0x5d, 0xd6, 0x66, 0xa0, 0xc1, 0x52, 0xe8, 0xb7, 0xc8, 0x34, 0xed, 0xc6, 0xff, 0x6a, 0xc0, 0xe3,
	0x49, 0x3b, 0x1f, 0xb1, 0xe0, 0xf2, 0x6b, 0xa9, 0x0f, 0xeb, 0x88, 0xbc, 0x84, 0xc5, 0x44, 0x55,
	0x38, 0xd3, 0x34, 0x90, 0xef, 0xe7, 0x58, 0x8b, 0xbd, 0xf9, 0xd5, 0xed, 0xaf, 0x95, 0x4a, 0x93,
	0xd9, 0xe5, 0x6e, 0xa1, 0x6e, 0x58, 0xf0, 0xdd, 0x10, 0xb8, 0xf6, 0x15, 0x2c, 0x8d, 0x90, 0x4c,
	0x18, 0x6f, 0xae, 0x64, 0xc7, 0x9b, 0xa5, 0xec, 0x24, 0xf3, 0x5f, 0x0c, 0xf8, 0xe0, 0x90, 0x46,
	0x0d, 0x81, 0x9d, 0x6c, 0x44, 0x2d, 0x7a, 0x95, 0x19, 0xe6, 0x4d, 0x5b, 0x37, 0x04, 0xb2, 0x6c,
	0x8e, 0xf8, 0x25, 0x0d, 0x12, 0x77, 0x91, 0x90, 0xb6, 0x04, 0x60, 0x2f, 0xc4, 0x7c, 0x96, 0x4c,
	0x77, 0xd4, 0x62, 0x44, 0x19, 0xb3, 0xb7, 0x67, 0xc7, 0xb9, 0x6c, 0x6d, 0xfc, 0x1b, 0x03, 0x6b,
	0xbd, 0xc9, 0x52, 0xbf, 0x53, 0x8a, 0x7e, 0x8b, 0xe4, 0x6a, 0x2e, 0x3a, 0x93, 0xfd, 0x4e, 0xb9,
	0x9d, 0x99, 0xb2, 0x29, 0x3b, 0x21, 0x89, 0xe3, 0xa7, 0xe2, 0xa4, 0x73, 0x36, 0xf3, 0xdf, 0x67,
	0xa0, 0x9a, 0x47, 0xbe, 0x6d, 0x82, 0xf6, 0x18, 0xca, 0x82, 0xf9, 0x03, 0x8f, 0xe6, 0x9a, 0x35,
	0x05, 0xc2, 0x58, 0x3e, 0x3a, 0x62, 0x9b, 0x19, 0x1f, 0xb1, 0xed, 0xc0, 0x6a, 0x42, 0x92, 0x1f,
	0x81, 0x29, 0x7f, 0xbf, 0xab, 0x91, 0x27, 0xd9, 0x49, 0xd8, 0x56, 0x5a, 0xe1, 0xa5, 0x13, 0xb1,
	0x39, 0x6c, 0x05, 0x97, 0xb2, 0xf4, 0x67, 0xf4, 0x75, 0x76, 0x34, 0xaa, 0x68, 0x51, 0x90, 0xf9,
	0xdc, 0x68, 0x14, 0x89, 0x51, 0x9a, 0xdf, 0x87, 0x44, 0x38, 0xbb, 0xcf, 0x23, 0x3d, 0x67, 0x9e,
	0x70, 0x6d, 0xca, 0xca, 0x35, 0xe9, 0x73, 0x1e, 0x65, 0x27, 0x44, 0xb2, 0x12, 0xb6, 0xfd, 0xee,
	0x97, 0xfa, 0xcf, 0x9a, 0x64, 0x42, 0xd4, 0x74, 0x79, 0xd0, 0xea, 0x7e, 0x49, 0xbe, 0x82, 0xa5,
	0x90, 0x5e, 0xd9, 0xd9, 0x6d, 0x54, 0x01, 0xb2, 0x3a, 0xbe, 0x8d, 0xec, 0x70, 0x64, 0x83, 0x9c,
	0x2e, 0xc7, 0xaa, 0x4f, 0x18, 0xaf, 0x3e, 0xbf, 0xc2, 0x5f, 0x0e, 0xf2, 0x6c, 0x26, 0x76, 0x9c,
	0xb9, 0xbf, 0x99, 0x66, 0xf4, 0x78, 0x7a, 0x6b, 0xf8, 0x39, 0x62, 0x38, 0x7c, 0x7f, 0x0c, 0xeb,
	0xfa, 0x5f, 0xad, 0x46, 0xbb, 0x6d, 0xa9, 0x84, 0x77, 0x7e, 0xbc, 0xff, 0xf3, 0xd3, 0xfd, 0xbd,
	0x36, 0x26, 0xc7, 0x0f, 0x60, 0x6d, 0x8c, 0xe0, 0xac, 0xd1, 0xda, 0xb7, 0xf7, 0x9a, 0xed, 0x97,
	0x35, 0x63, 0xeb, 0x63, 0xb8, 0x7f, 0xcb, 0x7f, 0x00, 0xa4, 0x0a, 0xf0, 0x4d, 0x74, 0x35, 0xfc,
	0x83, 0x61, 0xeb, 0x42, 0xff, 0x2f, 0x91, 0x9d, 0x1f, 0x92, 0x07, 0xb0, 0x3a, 0x06, 0x3c, 0xe6,
	0x01, 0xad, 0xdd, 0x21, 0x0f, 0xf3, 0x5d, 0x36, 0xa2, 0x5e, 0xc4, 0x8e, 0xc7, 0xa2, 0x9b, 0x9a,
	0x31, 0xfc, 0xa1, 0x22, 0x8b, 0x7d, 0xce, 0xa3, 0x5a, 0x61, 0xeb, 0xcf, 0x54, 0x32, 0xcb, 0x56,
	0x07, 0xe4, 0x73, 0x78, 0xda, 0x3a, 0x3f, 0xdb, 0xb7, 0xcf, 0x4e, 0xf6, 0x9a, 0x8d, 0x23, 0x7b,
	0xef, 0xa4, 0xd5, 0x3a, 0x3f, 0x6e, 0xb6, 0x5f, 0xda, 0x7b, 0x8d, 0xf6, 0xfe, 0xe1, 0x89, 0xf5,
	0x32, 0x39, 0x7a, 0xb6, 0x30, 0xf8, 0x81, 0x2f, 0x1d, 0x36, 0x8e, 0x0f, 0xcf, 0x4f, 0x75, 0x09,
	0xbf, 0xf5, 0xbd, 0xa1, 0x74, 0x94, 0xab, 0x55, 0x49, 0x03, 0xbe, 0x9a, 0xcc, 0x4a, 0x56, 0xff,
	0xcd, 0xe3, 0x43, 0xfb, 0xe4, 0xb4, 0xdd, 0x3c, 0x39, 0xb6, 0x1b, 0x6d, 0xbb, 0xd5, 0x68, 0x1e,
	0xdb, 0xf8, 0xbb, 0x5c, 0x5e, 0x9a, 0xa7, 0xf0, 0xe4, 0x07, 0xb1, 0x38, 0xb0, 0x9a, 0xfb, 0xc7,
	0xcf, 0x6a, 0xc6, 0x56, 0x00, 0xf7, 0xb2, 0x11, 0xfe, 0x34, 0x4d, 0x8d, 0xab, 0xb0, 0x7c, 0x1e,
	0x5c, 0x06, 0xfc, 0x3a, 0x48, 0x81, 0xb5, 0x3b, 0x64, 0x11, 0x8a, 0x32, 0x05, 0xe0, 0x0a, 0x6b,
	0xa0, 0x6f, 0x98, 0x7b, 0x79, 0x12, 0x47, 0x7b, 0xa9, 0x05, 0xd6, 0x0a, 0xe4, 0x3e, 0xdc, 0xd5,
	0x65, 0x5a, 0x72, 0xf5, 0x32, 0x9a, 0xd6, 0x66, 0x76, 0x8f, 0xa0, 0xee, 0x72, 0x7f, 0xfb, 0x86,
	0xdd, 0xf0, 0x18, 0xa7, 0x8b, 0xb2, 0xfa, 0x51, 0x3f, 0xbc, 0xfe, 0xe2, 0xb3, 0x1e, 0xf7, 0x9c,
	0xa0, 0xb7, 0xfd, 0xe5, 0x4e, 0x14, 0x6d, 0xbb, 0xdc, 0x7f, 0x8a, 0x60, 0x97, 0x7b, 0x4f, 0x9d,
	0xc1, 0xe0, 0xe9, 0x84, 0x3f, 0x70, 0x3b, 0xf3, 0x48, 0xf1, 0xf9, 0xff, 0x07, 0x00, 0x00, 0xff,
	0xff, 0x7c, 0xd2, 0x85, 0x4f, 0x9f, 0x2b, 0x00, 0x00,
}
