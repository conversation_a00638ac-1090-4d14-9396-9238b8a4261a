// Code generated by protoc-gen-go. DO NOT EDIT.
// source: missiongo-logic_.proto

package missoingologic // import "golang.52tt.com/protocol/app/missoingologic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type OpenNotificationPermissionReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OpenNotificationPermissionReq) Reset()         { *m = OpenNotificationPermissionReq{} }
func (m *OpenNotificationPermissionReq) String() string { return proto.CompactTextString(m) }
func (*OpenNotificationPermissionReq) ProtoMessage()    {}
func (*OpenNotificationPermissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_missiongo_logic__9936fc59a5c7d17e, []int{0}
}
func (m *OpenNotificationPermissionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenNotificationPermissionReq.Unmarshal(m, b)
}
func (m *OpenNotificationPermissionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenNotificationPermissionReq.Marshal(b, m, deterministic)
}
func (dst *OpenNotificationPermissionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenNotificationPermissionReq.Merge(dst, src)
}
func (m *OpenNotificationPermissionReq) XXX_Size() int {
	return xxx_messageInfo_OpenNotificationPermissionReq.Size(m)
}
func (m *OpenNotificationPermissionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenNotificationPermissionReq.DiscardUnknown(m)
}

var xxx_messageInfo_OpenNotificationPermissionReq proto.InternalMessageInfo

func (m *OpenNotificationPermissionReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type OpenNotificationPermissionResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OpenNotificationPermissionResp) Reset()         { *m = OpenNotificationPermissionResp{} }
func (m *OpenNotificationPermissionResp) String() string { return proto.CompactTextString(m) }
func (*OpenNotificationPermissionResp) ProtoMessage()    {}
func (*OpenNotificationPermissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_missiongo_logic__9936fc59a5c7d17e, []int{1}
}
func (m *OpenNotificationPermissionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenNotificationPermissionResp.Unmarshal(m, b)
}
func (m *OpenNotificationPermissionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenNotificationPermissionResp.Marshal(b, m, deterministic)
}
func (dst *OpenNotificationPermissionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenNotificationPermissionResp.Merge(dst, src)
}
func (m *OpenNotificationPermissionResp) XXX_Size() int {
	return xxx_messageInfo_OpenNotificationPermissionResp.Size(m)
}
func (m *OpenNotificationPermissionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenNotificationPermissionResp.DiscardUnknown(m)
}

var xxx_messageInfo_OpenNotificationPermissionResp proto.InternalMessageInfo

func (m *OpenNotificationPermissionResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*OpenNotificationPermissionReq)(nil), "ga.missoingologic.OpenNotificationPermissionReq")
	proto.RegisterType((*OpenNotificationPermissionResp)(nil), "ga.missoingologic.OpenNotificationPermissionResp")
}

func init() {
	proto.RegisterFile("missiongo-logic_.proto", fileDescriptor_missiongo_logic__9936fc59a5c7d17e)
}

var fileDescriptor_missiongo_logic__9936fc59a5c7d17e = []byte{
	// 216 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x8f, 0x31, 0x4b, 0x04, 0x31,
	0x10, 0x46, 0xb9, 0x46, 0xcf, 0x9c, 0x16, 0x6e, 0x21, 0x87, 0xa0, 0xc8, 0x15, 0xa2, 0x88, 0x59,
	0x38, 0xf1, 0x0f, 0x5c, 0x73, 0x85, 0xa0, 0x92, 0xd2, 0x66, 0x99, 0xc4, 0x71, 0x18, 0x48, 0x32,
	0xd9, 0x4d, 0x2c, 0xee, 0xdf, 0xcb, 0xee, 0x06, 0xc4, 0xc6, 0xee, 0x23, 0xef, 0xf1, 0xc8, 0xa8,
	0x8b, 0xc0, 0x39, 0xb3, 0x44, 0x92, 0x47, 0x2f, 0xc4, 0xae, 0xd3, 0x69, 0x90, 0x22, 0xcd, 0x39,
	0x81, 0x1e, 0x91, 0x70, 0x24, 0x99, 0xc8, 0xe5, 0x19, 0x41, 0x67, 0x21, 0xe3, 0x6c, 0x6c, 0xf6,
	0xea, 0xea, 0x2d, 0x61, 0x7c, 0x95, 0xc2, 0x5f, 0xec, 0xa0, 0xb0, 0xc4, 0x77, 0x1c, 0x6a, 0xce,
	0x60, 0xdf, 0xdc, 0xaa, 0xe5, 0xa8, 0x77, 0x03, 0xf6, 0xeb, 0xc5, 0xcd, 0xe2, 0x6e, 0xb5, 0x5d,
	0x69, 0x02, 0xbd, 0x83, 0x8c, 0x06, 0x7b, 0x73, 0x6c, 0xe7, 0xb1, 0x79, 0x51, 0xd7, 0xff, 0x85,
	0x72, 0x6a, 0xee, 0xd5, 0x49, 0x2d, 0xe5, 0x54, 0x53, 0xa7, 0xbf, 0xa9, 0x9c, 0xcc, 0xd2, 0xd6,
	0xb5, 0xdb, 0xab, 0xb5, 0x93, 0xa0, 0x0f, 0x7c, 0x90, 0xef, 0x51, 0x09, 0xf2, 0x89, 0x7e, 0xfe,
	0xf1, 0xc7, 0x03, 0x89, 0x87, 0x48, 0xfa, 0x79, 0x5b, 0x8a, 0x76, 0x12, 0xda, 0xe9, 0xd9, 0x89,
	0x6f, 0x21, 0xa5, 0xf6, 0xef, 0xb5, 0xf6, 0x68, 0x82, 0x4f, 0x3f, 0x01, 0x00, 0x00, 0xff, 0xff,
	0x8f, 0xa3, 0x4d, 0xd2, 0x21, 0x01, 0x00, 0x00,
}
