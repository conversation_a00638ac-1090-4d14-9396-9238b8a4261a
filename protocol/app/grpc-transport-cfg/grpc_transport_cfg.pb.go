// Code generated by protoc-gen-go. DO NOT EDIT.
// source: grpc_transport_cfg/grpc_transport_cfg.proto

package grpc_transport_cfg // import "golang.52tt.com/protocol/app/grpc-transport-cfg"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RefreshTransportConfigRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PreviousCheckSum     string       `protobuf:"bytes,2,opt,name=previous_check_sum,json=previousCheckSum,proto3" json:"previous_check_sum,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RefreshTransportConfigRequest) Reset()         { *m = RefreshTransportConfigRequest{} }
func (m *RefreshTransportConfigRequest) String() string { return proto.CompactTextString(m) }
func (*RefreshTransportConfigRequest) ProtoMessage()    {}
func (*RefreshTransportConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_grpc_transport_cfg_61eccf5a97689480, []int{0}
}
func (m *RefreshTransportConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefreshTransportConfigRequest.Unmarshal(m, b)
}
func (m *RefreshTransportConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefreshTransportConfigRequest.Marshal(b, m, deterministic)
}
func (dst *RefreshTransportConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefreshTransportConfigRequest.Merge(dst, src)
}
func (m *RefreshTransportConfigRequest) XXX_Size() int {
	return xxx_messageInfo_RefreshTransportConfigRequest.Size(m)
}
func (m *RefreshTransportConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RefreshTransportConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RefreshTransportConfigRequest proto.InternalMessageInfo

func (m *RefreshTransportConfigRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *RefreshTransportConfigRequest) GetPreviousCheckSum() string {
	if m != nil {
		return m.PreviousCheckSum
	}
	return ""
}

type RefreshTransportConfigResponse struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TransportConfig      *TransportConfigV2 `protobuf:"bytes,2,opt,name=transport_config,json=transportConfig,proto3" json:"transport_config,omitempty"`
	NewCheckSum          string             `protobuf:"bytes,3,opt,name=new_check_sum,json=newCheckSum,proto3" json:"new_check_sum,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *RefreshTransportConfigResponse) Reset()         { *m = RefreshTransportConfigResponse{} }
func (m *RefreshTransportConfigResponse) String() string { return proto.CompactTextString(m) }
func (*RefreshTransportConfigResponse) ProtoMessage()    {}
func (*RefreshTransportConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_grpc_transport_cfg_61eccf5a97689480, []int{1}
}
func (m *RefreshTransportConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefreshTransportConfigResponse.Unmarshal(m, b)
}
func (m *RefreshTransportConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefreshTransportConfigResponse.Marshal(b, m, deterministic)
}
func (dst *RefreshTransportConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefreshTransportConfigResponse.Merge(dst, src)
}
func (m *RefreshTransportConfigResponse) XXX_Size() int {
	return xxx_messageInfo_RefreshTransportConfigResponse.Size(m)
}
func (m *RefreshTransportConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RefreshTransportConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RefreshTransportConfigResponse proto.InternalMessageInfo

func (m *RefreshTransportConfigResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *RefreshTransportConfigResponse) GetTransportConfig() *TransportConfigV2 {
	if m != nil {
		return m.TransportConfig
	}
	return nil
}

func (m *RefreshTransportConfigResponse) GetNewCheckSum() string {
	if m != nil {
		return m.NewCheckSum
	}
	return ""
}

func init() {
	proto.RegisterType((*RefreshTransportConfigRequest)(nil), "ga.grpc_transport_cfg.RefreshTransportConfigRequest")
	proto.RegisterType((*RefreshTransportConfigResponse)(nil), "ga.grpc_transport_cfg.RefreshTransportConfigResponse")
}

func init() {
	proto.RegisterFile("grpc_transport_cfg/grpc_transport_cfg.proto", fileDescriptor_grpc_transport_cfg_61eccf5a97689480)
}

var fileDescriptor_grpc_transport_cfg_61eccf5a97689480 = []byte{
	// 307 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x91, 0x51, 0x4b, 0x02, 0x41,
	0x14, 0x85, 0xd9, 0x82, 0xd2, 0xd9, 0x24, 0x19, 0x08, 0x44, 0x28, 0x44, 0x28, 0x8c, 0x72, 0x16,
	0x36, 0xfa, 0x03, 0xfa, 0xd8, 0xdb, 0x18, 0x3d, 0xf4, 0xb2, 0xcc, 0x4e, 0x77, 0x47, 0xc9, 0xdd,
	0x3b, 0xce, 0xcc, 0x2a, 0xfe, 0xbf, 0x7e, 0x58, 0x38, 0xeb, 0xa6, 0xa5, 0xbd, 0xdd, 0xb9, 0xe7,
	0x70, 0x3e, 0xce, 0x1d, 0xf2, 0xa0, 0x8c, 0x96, 0x89, 0x33, 0xa2, 0xb0, 0x1a, 0x8d, 0x4b, 0x64,
	0xa6, 0xa2, 0xc3, 0x15, 0xd3, 0x06, 0x1d, 0xd2, 0x2b, 0x25, 0xd8, 0xa1, 0xd8, 0x6d, 0x29, 0x91,
	0xa4, 0xc2, 0x42, 0xe5, 0xea, 0xde, 0x1e, 0x89, 0xdc, 0xbd, 0x96, 0x71, 0x65, 0xeb, 0x97, 0xe4,
	0x9a, 0x43, 0x66, 0xc0, 0x4e, 0x5f, 0x6b, 0x71, 0x8c, 0x45, 0x36, 0x53, 0x1c, 0x16, 0x25, 0x58,
	0x47, 0xef, 0x48, 0x63, 0x93, 0x9a, 0x18, 0x58, 0x74, 0x82, 0x5e, 0x30, 0x08, 0xe3, 0x90, 0x29,
	0xc1, 0x46, 0xc2, 0x02, 0x87, 0x05, 0x3f, 0x4f, 0xab, 0x81, 0x3e, 0x12, 0xaa, 0x0d, 0x2c, 0x67,
	0x58, 0xda, 0x44, 0x4e, 0x41, 0x7e, 0x26, 0xb6, 0xcc, 0x3b, 0x27, 0xbd, 0x60, 0xd0, 0xe4, 0xed,
	0x5a, 0x19, 0x6f, 0x84, 0x49, 0x99, 0xf7, 0xbf, 0x02, 0x72, 0xf3, 0x1f, 0xd7, 0x6a, 0x2c, 0x2c,
	0xd0, 0x7b, 0xd2, 0xdc, 0x82, 0xad, 0xde, 0x92, 0x2f, 0x76, 0x64, 0xab, 0x79, 0x23, 0xdd, 0x4e,
	0x74, 0x42, 0xda, 0x7b, 0x45, 0x7d, 0x8c, 0x27, 0x87, 0xf1, 0x80, 0x1d, 0x3d, 0x16, 0xfb, 0x03,
	0x7d, 0x8b, 0xf9, 0xa5, 0xfb, 0xbd, 0xa2, 0x7d, 0xd2, 0x2a, 0x60, 0xb5, 0xd7, 0xe5, 0xd4, 0x77,
	0x09, 0x0b, 0x58, 0xd5, 0x35, 0x46, 0x2f, 0xa4, 0x23, 0x31, 0x67, 0xeb, 0xd9, 0x1a, 0xcb, 0x0d,
	0x29, 0xc7, 0x0f, 0x98, 0x57, 0x97, 0x7d, 0x8f, 0x14, 0xce, 0x45, 0xa1, 0xd8, 0x73, 0xec, 0x1c,
	0x93, 0x98, 0x47, 0x7e, 0x2d, 0x71, 0x1e, 0x09, 0xad, 0xfd, 0xef, 0x0e, 0x7f, 0x90, 0x43, 0x99,
	0xa9, 0xf4, 0xcc, 0x1b, 0x9e, 0xbe, 0x03, 0x00, 0x00, 0xff, 0xff, 0x8c, 0x74, 0xf9, 0x17, 0x0d,
	0x02, 0x00, 0x00,
}
