// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt_rev_common_logic/tt_rev_common_logic.proto

package tt_rev_common_logic // import "golang.52tt.com/protocol/app/tt_rev_common_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RechargeActEntryInfo_LocationType int32

const (
	RechargeActEntryInfo_LOCATION_TYPE_UNSPECIFIED RechargeActEntryInfo_LocationType = 0
	RechargeActEntryInfo_LOCATION_TYPE_ROOM        RechargeActEntryInfo_LocationType = 1
	RechargeActEntryInfo_LOCATION_TYPE_ROOM_GIFT   RechargeActEntryInfo_LocationType = 2
	RechargeActEntryInfo_LOCATION_TYPE_IM_GIFT     RechargeActEntryInfo_LocationType = 3
	RechargeActEntryInfo_LOCATION_TYPE_GIFTSHELF   RechargeActEntryInfo_LocationType = 4
)

var RechargeActEntryInfo_LocationType_name = map[int32]string{
	0: "LOCATION_TYPE_UNSPECIFIED",
	1: "LOCATION_TYPE_ROOM",
	2: "LOCATION_TYPE_ROOM_GIFT",
	3: "LOCATION_TYPE_IM_GIFT",
	4: "LOCATION_TYPE_GIFTSHELF",
}
var RechargeActEntryInfo_LocationType_value = map[string]int32{
	"LOCATION_TYPE_UNSPECIFIED": 0,
	"LOCATION_TYPE_ROOM":        1,
	"LOCATION_TYPE_ROOM_GIFT":   2,
	"LOCATION_TYPE_IM_GIFT":     3,
	"LOCATION_TYPE_GIFTSHELF":   4,
}

func (x RechargeActEntryInfo_LocationType) String() string {
	return proto.EnumName(RechargeActEntryInfo_LocationType_name, int32(x))
}
func (RechargeActEntryInfo_LocationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{0, 0}
}

type RechargeActAwardInfo_ItemType int32

const (
	RechargeActAwardInfo_ITEM_TYPE_UNSPECIFIED             RechargeActAwardInfo_ItemType = 0
	RechargeActAwardInfo_ITEM_TYPE_TBEAN                   RechargeActAwardInfo_ItemType = 1
	RechargeActAwardInfo_ITEM_TYPE_RED_DIAMONDD            RechargeActAwardInfo_ItemType = 2
	RechargeActAwardInfo_ITEM_TYPE_GOLD_DIAMOND_WITH_BOUNS RechargeActAwardInfo_ItemType = 3
	RechargeActAwardInfo_ITEM_TYPE_VIRTUAL_DRESS_DAY       RechargeActAwardInfo_ItemType = 4
)

var RechargeActAwardInfo_ItemType_name = map[int32]string{
	0: "ITEM_TYPE_UNSPECIFIED",
	1: "ITEM_TYPE_TBEAN",
	2: "ITEM_TYPE_RED_DIAMONDD",
	3: "ITEM_TYPE_GOLD_DIAMOND_WITH_BOUNS",
	4: "ITEM_TYPE_VIRTUAL_DRESS_DAY",
}
var RechargeActAwardInfo_ItemType_value = map[string]int32{
	"ITEM_TYPE_UNSPECIFIED":             0,
	"ITEM_TYPE_TBEAN":                   1,
	"ITEM_TYPE_RED_DIAMONDD":            2,
	"ITEM_TYPE_GOLD_DIAMOND_WITH_BOUNS": 3,
	"ITEM_TYPE_VIRTUAL_DRESS_DAY":       4,
}

func (x RechargeActAwardInfo_ItemType) String() string {
	return proto.EnumName(RechargeActAwardInfo_ItemType_name, int32(x))
}
func (RechargeActAwardInfo_ItemType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{3, 0}
}

type RechargeBannerInfo_BannerType int32

const (
	RechargeBannerInfo_BANNER_TYPE_UNSPECIFIED    RechargeBannerInfo_BannerType = 0
	RechargeBannerInfo_BANNER_TYPE_WEEK_CARD      RechargeBannerInfo_BannerType = 1
	RechargeBannerInfo_BANNER_TYPE_FIRST_RECHARGE RechargeBannerInfo_BannerType = 2
)

var RechargeBannerInfo_BannerType_name = map[int32]string{
	0: "BANNER_TYPE_UNSPECIFIED",
	1: "BANNER_TYPE_WEEK_CARD",
	2: "BANNER_TYPE_FIRST_RECHARGE",
}
var RechargeBannerInfo_BannerType_value = map[string]int32{
	"BANNER_TYPE_UNSPECIFIED":    0,
	"BANNER_TYPE_WEEK_CARD":      1,
	"BANNER_TYPE_FIRST_RECHARGE": 2,
}

func (x RechargeBannerInfo_BannerType) String() string {
	return proto.EnumName(RechargeBannerInfo_BannerType_name, int32(x))
}
func (RechargeBannerInfo_BannerType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{9, 0}
}

// 充值(首充)活动入口信息
type RechargeActEntryInfo struct {
	LocationType         uint32   `protobuf:"varint,1,opt,name=location_type,json=locationType,proto3" json:"location_type,omitempty"`
	EntryImg             string   `protobuf:"bytes,2,opt,name=entry_img,json=entryImg,proto3" json:"entry_img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RechargeActEntryInfo) Reset()         { *m = RechargeActEntryInfo{} }
func (m *RechargeActEntryInfo) String() string { return proto.CompactTextString(m) }
func (*RechargeActEntryInfo) ProtoMessage()    {}
func (*RechargeActEntryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{0}
}
func (m *RechargeActEntryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RechargeActEntryInfo.Unmarshal(m, b)
}
func (m *RechargeActEntryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RechargeActEntryInfo.Marshal(b, m, deterministic)
}
func (dst *RechargeActEntryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RechargeActEntryInfo.Merge(dst, src)
}
func (m *RechargeActEntryInfo) XXX_Size() int {
	return xxx_messageInfo_RechargeActEntryInfo.Size(m)
}
func (m *RechargeActEntryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RechargeActEntryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RechargeActEntryInfo proto.InternalMessageInfo

func (m *RechargeActEntryInfo) GetLocationType() uint32 {
	if m != nil {
		return m.LocationType
	}
	return 0
}

func (m *RechargeActEntryInfo) GetEntryImg() string {
	if m != nil {
		return m.EntryImg
	}
	return ""
}

type GetNewRechargeActEntryInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetNewRechargeActEntryInfoRequest) Reset()         { *m = GetNewRechargeActEntryInfoRequest{} }
func (m *GetNewRechargeActEntryInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetNewRechargeActEntryInfoRequest) ProtoMessage()    {}
func (*GetNewRechargeActEntryInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{1}
}
func (m *GetNewRechargeActEntryInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewRechargeActEntryInfoRequest.Unmarshal(m, b)
}
func (m *GetNewRechargeActEntryInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewRechargeActEntryInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetNewRechargeActEntryInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewRechargeActEntryInfoRequest.Merge(dst, src)
}
func (m *GetNewRechargeActEntryInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetNewRechargeActEntryInfoRequest.Size(m)
}
func (m *GetNewRechargeActEntryInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewRechargeActEntryInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewRechargeActEntryInfoRequest proto.InternalMessageInfo

func (m *GetNewRechargeActEntryInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetNewRechargeActEntryInfoResponse struct {
	BaseResp  *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ShowEntry bool                    `protobuf:"varint,2,opt,name=show_entry,json=showEntry,proto3" json:"show_entry,omitempty"`
	EntryList []*RechargeActEntryInfo `protobuf:"bytes,3,rep,name=entry_list,json=entryList,proto3" json:"entry_list,omitempty"`
	// 入口文案配置：固定格式：文案+礼物图片+礼物名
	EntryDesc            string   `protobuf:"bytes,4,opt,name=entry_desc,json=entryDesc,proto3" json:"entry_desc,omitempty"`
	EntryImgUrl          string   `protobuf:"bytes,5,opt,name=entry_img_url,json=entryImgUrl,proto3" json:"entry_img_url,omitempty"`
	EntryPresentName     string   `protobuf:"bytes,6,opt,name=entry_present_name,json=entryPresentName,proto3" json:"entry_present_name,omitempty"`
	FinishTs             int64    `protobuf:"varint,7,opt,name=finish_ts,json=finishTs,proto3" json:"finish_ts,omitempty"`
	LocalCacheSec        uint32   `protobuf:"varint,8,opt,name=local_cache_sec,json=localCacheSec,proto3" json:"local_cache_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewRechargeActEntryInfoResponse) Reset()         { *m = GetNewRechargeActEntryInfoResponse{} }
func (m *GetNewRechargeActEntryInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetNewRechargeActEntryInfoResponse) ProtoMessage()    {}
func (*GetNewRechargeActEntryInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{2}
}
func (m *GetNewRechargeActEntryInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewRechargeActEntryInfoResponse.Unmarshal(m, b)
}
func (m *GetNewRechargeActEntryInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewRechargeActEntryInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetNewRechargeActEntryInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewRechargeActEntryInfoResponse.Merge(dst, src)
}
func (m *GetNewRechargeActEntryInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetNewRechargeActEntryInfoResponse.Size(m)
}
func (m *GetNewRechargeActEntryInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewRechargeActEntryInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewRechargeActEntryInfoResponse proto.InternalMessageInfo

func (m *GetNewRechargeActEntryInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNewRechargeActEntryInfoResponse) GetShowEntry() bool {
	if m != nil {
		return m.ShowEntry
	}
	return false
}

func (m *GetNewRechargeActEntryInfoResponse) GetEntryList() []*RechargeActEntryInfo {
	if m != nil {
		return m.EntryList
	}
	return nil
}

func (m *GetNewRechargeActEntryInfoResponse) GetEntryDesc() string {
	if m != nil {
		return m.EntryDesc
	}
	return ""
}

func (m *GetNewRechargeActEntryInfoResponse) GetEntryImgUrl() string {
	if m != nil {
		return m.EntryImgUrl
	}
	return ""
}

func (m *GetNewRechargeActEntryInfoResponse) GetEntryPresentName() string {
	if m != nil {
		return m.EntryPresentName
	}
	return ""
}

func (m *GetNewRechargeActEntryInfoResponse) GetFinishTs() int64 {
	if m != nil {
		return m.FinishTs
	}
	return 0
}

func (m *GetNewRechargeActEntryInfoResponse) GetLocalCacheSec() uint32 {
	if m != nil {
		return m.LocalCacheSec
	}
	return 0
}

// 首充奖励信息
type RechargeActAwardInfo struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"` // Deprecated: Do not use.
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	StaticImg            string   `protobuf:"bytes,3,opt,name=static_img,json=staticImg,proto3" json:"static_img,omitempty"`
	GifUrl               string   `protobuf:"bytes,4,opt,name=gif_url,json=gifUrl,proto3" json:"gif_url,omitempty"`
	Amount               uint32   `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"` // Deprecated: Do not use.
	UnitText             string   `protobuf:"bytes,6,opt,name=unit_text,json=unitText,proto3" json:"unit_text,omitempty"`
	AwardTypeText        string   `protobuf:"bytes,7,opt,name=award_type_text,json=awardTypeText,proto3" json:"award_type_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RechargeActAwardInfo) Reset()         { *m = RechargeActAwardInfo{} }
func (m *RechargeActAwardInfo) String() string { return proto.CompactTextString(m) }
func (*RechargeActAwardInfo) ProtoMessage()    {}
func (*RechargeActAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{3}
}
func (m *RechargeActAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RechargeActAwardInfo.Unmarshal(m, b)
}
func (m *RechargeActAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RechargeActAwardInfo.Marshal(b, m, deterministic)
}
func (dst *RechargeActAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RechargeActAwardInfo.Merge(dst, src)
}
func (m *RechargeActAwardInfo) XXX_Size() int {
	return xxx_messageInfo_RechargeActAwardInfo.Size(m)
}
func (m *RechargeActAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RechargeActAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RechargeActAwardInfo proto.InternalMessageInfo

// Deprecated: Do not use.
func (m *RechargeActAwardInfo) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *RechargeActAwardInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RechargeActAwardInfo) GetStaticImg() string {
	if m != nil {
		return m.StaticImg
	}
	return ""
}

func (m *RechargeActAwardInfo) GetGifUrl() string {
	if m != nil {
		return m.GifUrl
	}
	return ""
}

// Deprecated: Do not use.
func (m *RechargeActAwardInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *RechargeActAwardInfo) GetUnitText() string {
	if m != nil {
		return m.UnitText
	}
	return ""
}

func (m *RechargeActAwardInfo) GetAwardTypeText() string {
	if m != nil {
		return m.AwardTypeText
	}
	return ""
}

// 首充档位信息
type RechargeActLevelInfo struct {
	LevelId              uint32                  `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	Price                uint32                  `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`
	SetDescUrl           string                  `protobuf:"bytes,3,opt,name=set_desc_url,json=setDescUrl,proto3" json:"set_desc_url,omitempty"`
	BeanSaved            uint32                  `protobuf:"varint,4,opt,name=bean_saved,json=beanSaved,proto3" json:"bean_saved,omitempty"`
	AwardList            []*RechargeActAwardInfo `protobuf:"bytes,5,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	ProductId            string                  `protobuf:"bytes,6,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *RechargeActLevelInfo) Reset()         { *m = RechargeActLevelInfo{} }
func (m *RechargeActLevelInfo) String() string { return proto.CompactTextString(m) }
func (*RechargeActLevelInfo) ProtoMessage()    {}
func (*RechargeActLevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{4}
}
func (m *RechargeActLevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RechargeActLevelInfo.Unmarshal(m, b)
}
func (m *RechargeActLevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RechargeActLevelInfo.Marshal(b, m, deterministic)
}
func (dst *RechargeActLevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RechargeActLevelInfo.Merge(dst, src)
}
func (m *RechargeActLevelInfo) XXX_Size() int {
	return xxx_messageInfo_RechargeActLevelInfo.Size(m)
}
func (m *RechargeActLevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RechargeActLevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RechargeActLevelInfo proto.InternalMessageInfo

func (m *RechargeActLevelInfo) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *RechargeActLevelInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *RechargeActLevelInfo) GetSetDescUrl() string {
	if m != nil {
		return m.SetDescUrl
	}
	return ""
}

func (m *RechargeActLevelInfo) GetBeanSaved() uint32 {
	if m != nil {
		return m.BeanSaved
	}
	return 0
}

func (m *RechargeActLevelInfo) GetAwardList() []*RechargeActAwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

func (m *RechargeActLevelInfo) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

// 获取首充活动弹窗信息
type GetNewRechargeActPopupInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetNewRechargeActPopupInfoRequest) Reset()         { *m = GetNewRechargeActPopupInfoRequest{} }
func (m *GetNewRechargeActPopupInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetNewRechargeActPopupInfoRequest) ProtoMessage()    {}
func (*GetNewRechargeActPopupInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{5}
}
func (m *GetNewRechargeActPopupInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewRechargeActPopupInfoRequest.Unmarshal(m, b)
}
func (m *GetNewRechargeActPopupInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewRechargeActPopupInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetNewRechargeActPopupInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewRechargeActPopupInfoRequest.Merge(dst, src)
}
func (m *GetNewRechargeActPopupInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetNewRechargeActPopupInfoRequest.Size(m)
}
func (m *GetNewRechargeActPopupInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewRechargeActPopupInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewRechargeActPopupInfoRequest proto.InternalMessageInfo

func (m *GetNewRechargeActPopupInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetNewRechargeActPopupInfoResponse struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	InfoList             []*RechargeActLevelInfo `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	RuleUrl              string                  `protobuf:"bytes,3,opt,name=rule_url,json=ruleUrl,proto3" json:"rule_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetNewRechargeActPopupInfoResponse) Reset()         { *m = GetNewRechargeActPopupInfoResponse{} }
func (m *GetNewRechargeActPopupInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetNewRechargeActPopupInfoResponse) ProtoMessage()    {}
func (*GetNewRechargeActPopupInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{6}
}
func (m *GetNewRechargeActPopupInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewRechargeActPopupInfoResponse.Unmarshal(m, b)
}
func (m *GetNewRechargeActPopupInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewRechargeActPopupInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetNewRechargeActPopupInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewRechargeActPopupInfoResponse.Merge(dst, src)
}
func (m *GetNewRechargeActPopupInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetNewRechargeActPopupInfoResponse.Size(m)
}
func (m *GetNewRechargeActPopupInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewRechargeActPopupInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewRechargeActPopupInfoResponse proto.InternalMessageInfo

func (m *GetNewRechargeActPopupInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNewRechargeActPopupInfoResponse) GetInfoList() []*RechargeActLevelInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetNewRechargeActPopupInfoResponse) GetRuleUrl() string {
	if m != nil {
		return m.RuleUrl
	}
	return ""
}

type NewFirstRechargeFinNotify struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewFirstRechargeFinNotify) Reset()         { *m = NewFirstRechargeFinNotify{} }
func (m *NewFirstRechargeFinNotify) String() string { return proto.CompactTextString(m) }
func (*NewFirstRechargeFinNotify) ProtoMessage()    {}
func (*NewFirstRechargeFinNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{7}
}
func (m *NewFirstRechargeFinNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewFirstRechargeFinNotify.Unmarshal(m, b)
}
func (m *NewFirstRechargeFinNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewFirstRechargeFinNotify.Marshal(b, m, deterministic)
}
func (dst *NewFirstRechargeFinNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewFirstRechargeFinNotify.Merge(dst, src)
}
func (m *NewFirstRechargeFinNotify) XXX_Size() int {
	return xxx_messageInfo_NewFirstRechargeFinNotify.Size(m)
}
func (m *NewFirstRechargeFinNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_NewFirstRechargeFinNotify.DiscardUnknown(m)
}

var xxx_messageInfo_NewFirstRechargeFinNotify proto.InternalMessageInfo

func (m *NewFirstRechargeFinNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 获取充值页banner信息
type GetRechargeBannerInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRechargeBannerInfoRequest) Reset()         { *m = GetRechargeBannerInfoRequest{} }
func (m *GetRechargeBannerInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetRechargeBannerInfoRequest) ProtoMessage()    {}
func (*GetRechargeBannerInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{8}
}
func (m *GetRechargeBannerInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRechargeBannerInfoRequest.Unmarshal(m, b)
}
func (m *GetRechargeBannerInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRechargeBannerInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetRechargeBannerInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRechargeBannerInfoRequest.Merge(dst, src)
}
func (m *GetRechargeBannerInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetRechargeBannerInfoRequest.Size(m)
}
func (m *GetRechargeBannerInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRechargeBannerInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRechargeBannerInfoRequest proto.InternalMessageInfo

func (m *GetRechargeBannerInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type RechargeBannerInfo struct {
	BannerType           uint32   `protobuf:"varint,1,opt,name=banner_type,json=bannerType,proto3" json:"banner_type,omitempty"`
	BannerPic            string   `protobuf:"bytes,2,opt,name=banner_pic,json=bannerPic,proto3" json:"banner_pic,omitempty"`
	JumpUrl              string   `protobuf:"bytes,3,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RechargeBannerInfo) Reset()         { *m = RechargeBannerInfo{} }
func (m *RechargeBannerInfo) String() string { return proto.CompactTextString(m) }
func (*RechargeBannerInfo) ProtoMessage()    {}
func (*RechargeBannerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{9}
}
func (m *RechargeBannerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RechargeBannerInfo.Unmarshal(m, b)
}
func (m *RechargeBannerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RechargeBannerInfo.Marshal(b, m, deterministic)
}
func (dst *RechargeBannerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RechargeBannerInfo.Merge(dst, src)
}
func (m *RechargeBannerInfo) XXX_Size() int {
	return xxx_messageInfo_RechargeBannerInfo.Size(m)
}
func (m *RechargeBannerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RechargeBannerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RechargeBannerInfo proto.InternalMessageInfo

func (m *RechargeBannerInfo) GetBannerType() uint32 {
	if m != nil {
		return m.BannerType
	}
	return 0
}

func (m *RechargeBannerInfo) GetBannerPic() string {
	if m != nil {
		return m.BannerPic
	}
	return ""
}

func (m *RechargeBannerInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

type GetRechargeBannerInfoResponse struct {
	BaseResp             *app.BaseResp         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	BannerList           []*RechargeBannerInfo `protobuf:"bytes,2,rep,name=banner_list,json=bannerList,proto3" json:"banner_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetRechargeBannerInfoResponse) Reset()         { *m = GetRechargeBannerInfoResponse{} }
func (m *GetRechargeBannerInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetRechargeBannerInfoResponse) ProtoMessage()    {}
func (*GetRechargeBannerInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{10}
}
func (m *GetRechargeBannerInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRechargeBannerInfoResponse.Unmarshal(m, b)
}
func (m *GetRechargeBannerInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRechargeBannerInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetRechargeBannerInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRechargeBannerInfoResponse.Merge(dst, src)
}
func (m *GetRechargeBannerInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetRechargeBannerInfoResponse.Size(m)
}
func (m *GetRechargeBannerInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRechargeBannerInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRechargeBannerInfoResponse proto.InternalMessageInfo

func (m *GetRechargeBannerInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRechargeBannerInfoResponse) GetBannerList() []*RechargeBannerInfo {
	if m != nil {
		return m.BannerList
	}
	return nil
}

type CheckCanModifySexRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckCanModifySexRequest) Reset()         { *m = CheckCanModifySexRequest{} }
func (m *CheckCanModifySexRequest) String() string { return proto.CompactTextString(m) }
func (*CheckCanModifySexRequest) ProtoMessage()    {}
func (*CheckCanModifySexRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{11}
}
func (m *CheckCanModifySexRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCanModifySexRequest.Unmarshal(m, b)
}
func (m *CheckCanModifySexRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCanModifySexRequest.Marshal(b, m, deterministic)
}
func (dst *CheckCanModifySexRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCanModifySexRequest.Merge(dst, src)
}
func (m *CheckCanModifySexRequest) XXX_Size() int {
	return xxx_messageInfo_CheckCanModifySexRequest.Size(m)
}
func (m *CheckCanModifySexRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCanModifySexRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCanModifySexRequest proto.InternalMessageInfo

func (m *CheckCanModifySexRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type CheckCanModifySexResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	NeedToast            bool          `protobuf:"varint,2,opt,name=need_toast,json=needToast,proto3" json:"need_toast,omitempty"`
	Toast                string        `protobuf:"bytes,3,opt,name=toast,proto3" json:"toast,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CheckCanModifySexResponse) Reset()         { *m = CheckCanModifySexResponse{} }
func (m *CheckCanModifySexResponse) String() string { return proto.CompactTextString(m) }
func (*CheckCanModifySexResponse) ProtoMessage()    {}
func (*CheckCanModifySexResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_logic_8d2824c838682773, []int{12}
}
func (m *CheckCanModifySexResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCanModifySexResponse.Unmarshal(m, b)
}
func (m *CheckCanModifySexResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCanModifySexResponse.Marshal(b, m, deterministic)
}
func (dst *CheckCanModifySexResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCanModifySexResponse.Merge(dst, src)
}
func (m *CheckCanModifySexResponse) XXX_Size() int {
	return xxx_messageInfo_CheckCanModifySexResponse.Size(m)
}
func (m *CheckCanModifySexResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCanModifySexResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCanModifySexResponse proto.InternalMessageInfo

func (m *CheckCanModifySexResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckCanModifySexResponse) GetNeedToast() bool {
	if m != nil {
		return m.NeedToast
	}
	return false
}

func (m *CheckCanModifySexResponse) GetToast() string {
	if m != nil {
		return m.Toast
	}
	return ""
}

func init() {
	proto.RegisterType((*RechargeActEntryInfo)(nil), "ga.tt_rev_common_logic.RechargeActEntryInfo")
	proto.RegisterType((*GetNewRechargeActEntryInfoRequest)(nil), "ga.tt_rev_common_logic.GetNewRechargeActEntryInfoRequest")
	proto.RegisterType((*GetNewRechargeActEntryInfoResponse)(nil), "ga.tt_rev_common_logic.GetNewRechargeActEntryInfoResponse")
	proto.RegisterType((*RechargeActAwardInfo)(nil), "ga.tt_rev_common_logic.RechargeActAwardInfo")
	proto.RegisterType((*RechargeActLevelInfo)(nil), "ga.tt_rev_common_logic.RechargeActLevelInfo")
	proto.RegisterType((*GetNewRechargeActPopupInfoRequest)(nil), "ga.tt_rev_common_logic.GetNewRechargeActPopupInfoRequest")
	proto.RegisterType((*GetNewRechargeActPopupInfoResponse)(nil), "ga.tt_rev_common_logic.GetNewRechargeActPopupInfoResponse")
	proto.RegisterType((*NewFirstRechargeFinNotify)(nil), "ga.tt_rev_common_logic.NewFirstRechargeFinNotify")
	proto.RegisterType((*GetRechargeBannerInfoRequest)(nil), "ga.tt_rev_common_logic.GetRechargeBannerInfoRequest")
	proto.RegisterType((*RechargeBannerInfo)(nil), "ga.tt_rev_common_logic.RechargeBannerInfo")
	proto.RegisterType((*GetRechargeBannerInfoResponse)(nil), "ga.tt_rev_common_logic.GetRechargeBannerInfoResponse")
	proto.RegisterType((*CheckCanModifySexRequest)(nil), "ga.tt_rev_common_logic.CheckCanModifySexRequest")
	proto.RegisterType((*CheckCanModifySexResponse)(nil), "ga.tt_rev_common_logic.CheckCanModifySexResponse")
	proto.RegisterEnum("ga.tt_rev_common_logic.RechargeActEntryInfo_LocationType", RechargeActEntryInfo_LocationType_name, RechargeActEntryInfo_LocationType_value)
	proto.RegisterEnum("ga.tt_rev_common_logic.RechargeActAwardInfo_ItemType", RechargeActAwardInfo_ItemType_name, RechargeActAwardInfo_ItemType_value)
	proto.RegisterEnum("ga.tt_rev_common_logic.RechargeBannerInfo_BannerType", RechargeBannerInfo_BannerType_name, RechargeBannerInfo_BannerType_value)
}

func init() {
	proto.RegisterFile("tt_rev_common_logic/tt_rev_common_logic.proto", fileDescriptor_tt_rev_common_logic_8d2824c838682773)
}

var fileDescriptor_tt_rev_common_logic_8d2824c838682773 = []byte{
	// 1077 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x56, 0xdf, 0x4e, 0xe3, 0xc6,
	0x17, 0xfe, 0x39, 0x01, 0xe2, 0x1c, 0x88, 0xb0, 0xe6, 0xb7, 0x40, 0x02, 0xa5, 0xb0, 0xae, 0xba,
	0xa2, 0xd5, 0x6e, 0xa8, 0xa8, 0xfa, 0x00, 0xf9, 0xe3, 0x80, 0x45, 0x48, 0xd0, 0xc4, 0x74, 0xb5,
	0xbd, 0x19, 0x39, 0xce, 0xc4, 0xb8, 0xb5, 0x3d, 0xc6, 0x9e, 0x00, 0x51, 0xaf, 0xfb, 0x0c, 0x95,
	0x7a, 0xd1, 0xb7, 0xe8, 0xa3, 0xf4, 0x41, 0x2a, 0xb5, 0xf7, 0xd5, 0xcc, 0x98, 0x24, 0x88, 0xb4,
	0x5d, 0xb8, 0xf3, 0xf9, 0xce, 0xf1, 0xf1, 0x9c, 0xef, 0xfb, 0x66, 0xc6, 0xf0, 0x8e, 0x73, 0x92,
	0xd2, 0x5b, 0xe2, 0xb1, 0x28, 0x62, 0x31, 0x09, 0x99, 0x1f, 0x78, 0xc7, 0x4b, 0xb0, 0x7a, 0x92,
	0x32, 0xce, 0xd0, 0xb6, 0xef, 0xd6, 0x97, 0x64, 0x77, 0x2b, 0xbe, 0x4b, 0x86, 0x6e, 0x46, 0x55,
	0x99, 0xf9, 0x97, 0x06, 0xaf, 0x30, 0xf5, 0xae, 0xdd, 0xd4, 0xa7, 0x0d, 0x8f, 0x5b, 0x31, 0x4f,
	0xa7, 0x76, 0x3c, 0x66, 0xe8, 0x33, 0xa8, 0x84, 0xcc, 0x73, 0x79, 0xc0, 0x62, 0xc2, 0xa7, 0x09,
	0xad, 0x6a, 0x87, 0xda, 0x51, 0x05, 0x6f, 0x3c, 0x80, 0xce, 0x34, 0xa1, 0x68, 0x0f, 0xca, 0x54,
	0xbc, 0x41, 0x82, 0xc8, 0xaf, 0x16, 0x0e, 0xb5, 0xa3, 0x32, 0xd6, 0x25, 0x60, 0x47, 0xbe, 0xf9,
	0x8b, 0x06, 0x1b, 0xdd, 0xc5, 0xea, 0x7d, 0xa8, 0x75, 0xfb, 0xad, 0x86, 0x63, 0xf7, 0x7b, 0xc4,
	0xf9, 0x70, 0x69, 0x91, 0xab, 0xde, 0xe0, 0xd2, 0x6a, 0xd9, 0x1d, 0xdb, 0x6a, 0x1b, 0xff, 0x43,
	0xdb, 0x80, 0x1e, 0xa7, 0x71, 0xbf, 0x7f, 0x61, 0x68, 0x68, 0x0f, 0x76, 0x9e, 0xe2, 0xe4, 0xd4,
	0xee, 0x38, 0x46, 0x01, 0xd5, 0x60, 0xeb, 0x71, 0xd2, 0xce, 0x53, 0xc5, 0xa7, 0xef, 0x09, 0x7c,
	0x70, 0x66, 0x75, 0x3b, 0xc6, 0x8a, 0x79, 0x0e, 0xaf, 0x4f, 0x29, 0xef, 0xd1, 0xbb, 0x65, 0xc3,
	0x63, 0x7a, 0x33, 0xa1, 0x19, 0x47, 0x6f, 0x40, 0x17, 0x54, 0x91, 0x94, 0xde, 0xc8, 0xf1, 0xd7,
	0x4f, 0xd6, 0xeb, 0xbe, 0x5b, 0x6f, 0xba, 0x19, 0xc5, 0xf4, 0x06, 0x97, 0x86, 0xea, 0xc1, 0xfc,
	0xa3, 0x00, 0xe6, 0xbf, 0x75, 0xcb, 0x12, 0x16, 0x67, 0x14, 0x7d, 0x01, 0xe5, 0xbc, 0x5d, 0x96,
	0xe4, 0xfd, 0x36, 0xe6, 0xfd, 0xb2, 0x04, 0xeb, 0xc3, 0xfc, 0x09, 0xed, 0x03, 0x64, 0xd7, 0xec,
	0x8e, 0x48, 0x32, 0x25, 0xb3, 0x3a, 0x2e, 0x0b, 0x44, 0x76, 0x45, 0xe7, 0x00, 0x8a, 0xf7, 0x30,
	0xc8, 0x78, 0xb5, 0x78, 0x58, 0x3c, 0x5a, 0x3f, 0x79, 0x5b, 0x5f, 0xae, 0x78, 0x7d, 0xe9, 0x9a,
	0x94, 0x6e, 0xdd, 0x20, 0xe3, 0xe2, 0x5b, 0xaa, 0xd9, 0x88, 0x66, 0x5e, 0x75, 0x45, 0xaa, 0xa8,
	0xd2, 0x6d, 0x9a, 0x79, 0xc8, 0x84, 0xca, 0x4c, 0x63, 0x32, 0x49, 0xc3, 0xea, 0xaa, 0xac, 0x58,
	0x7f, 0xd0, 0xf9, 0x2a, 0x0d, 0xd1, 0x5b, 0x40, 0xaa, 0x26, 0x49, 0x69, 0x46, 0x63, 0x4e, 0x62,
	0x37, 0xa2, 0xd5, 0x35, 0x59, 0x68, 0xc8, 0xcc, 0xa5, 0x4a, 0xf4, 0xdc, 0x48, 0xba, 0x66, 0x1c,
	0xc4, 0x41, 0x76, 0x4d, 0x78, 0x56, 0x2d, 0x1d, 0x6a, 0x47, 0x45, 0xac, 0x2b, 0xc0, 0xc9, 0xd0,
	0x1b, 0xd8, 0x14, 0x16, 0x0b, 0x89, 0xe7, 0x7a, 0xd7, 0x94, 0x64, 0xd4, 0xab, 0xea, 0xd2, 0x79,
	0xd2, 0x8e, 0x61, 0x4b, 0xa0, 0x03, 0xea, 0x99, 0x3f, 0x15, 0x1f, 0x19, 0xb7, 0x71, 0xe7, 0xa6,
	0x23, 0x69, 0xdc, 0x03, 0x28, 0x07, 0x9c, 0x46, 0x0b, 0xa6, 0x6d, 0x16, 0xaa, 0x1a, 0xd6, 0x05,
	0x28, 0x6d, 0x88, 0x60, 0x45, 0x2e, 0x4f, 0xf9, 0x55, 0x3e, 0x4b, 0xbe, 0xb9, 0xcb, 0x03, 0x4f,
	0x3a, 0xb9, 0xa8, 0x38, 0x50, 0x88, 0x1d, 0xf9, 0x68, 0x07, 0x4a, 0x7e, 0x30, 0x96, 0xd3, 0x2b,
	0x7e, 0xd6, 0xfc, 0x60, 0x2c, 0x06, 0xdf, 0x85, 0x35, 0x37, 0x62, 0x93, 0x98, 0x4b, 0x56, 0xd4,
	0x97, 0x72, 0x44, 0x8c, 0x39, 0x89, 0x03, 0x4e, 0x38, 0xbd, 0xe7, 0x39, 0x17, 0xba, 0x00, 0x1c,
	0x7a, 0x2f, 0xac, 0xb5, 0xe9, 0x8a, 0x25, 0xcb, 0x65, 0xaa, 0x92, 0x92, 0x2c, 0xa9, 0x48, 0x58,
	0x2c, 0x54, 0xd4, 0x99, 0xbf, 0x6a, 0xa0, 0xdb, 0x0f, 0x2b, 0xaf, 0xc1, 0x96, 0xed, 0x58, 0x17,
	0xcb, 0x36, 0xcf, 0xff, 0x61, 0x73, 0x9e, 0x72, 0x9a, 0x56, 0xa3, 0x67, 0x68, 0x68, 0x17, 0xb6,
	0xe7, 0x20, 0xb6, 0xda, 0xa4, 0x6d, 0x37, 0x2e, 0xfa, 0xbd, 0x76, 0xdb, 0x28, 0xa0, 0xcf, 0xe1,
	0xf5, 0x3c, 0x77, 0xda, 0xef, 0xce, 0x92, 0xe4, 0xbd, 0xed, 0x9c, 0x91, 0x66, 0xff, 0xaa, 0x37,
	0x30, 0x8a, 0xe8, 0x00, 0xf6, 0xe6, 0x65, 0xdf, 0xda, 0xd8, 0xb9, 0x6a, 0x74, 0x49, 0x1b, 0x5b,
	0x83, 0x01, 0x69, 0x37, 0x3e, 0x18, 0x2b, 0xe6, 0x9f, 0x8f, 0x0f, 0x90, 0x2e, 0xbd, 0xa5, 0xa1,
	0xd4, 0xa1, 0x06, 0x7a, 0x28, 0x02, 0x12, 0x8c, 0xf2, 0xb3, 0xa3, 0x24, 0x63, 0x7b, 0x84, 0x5e,
	0xc1, 0x6a, 0x92, 0x06, 0x9e, 0x92, 0xa0, 0x82, 0x55, 0x80, 0x0e, 0x61, 0x23, 0xa3, 0x5c, 0xba,
	0x50, 0x32, 0xad, 0x54, 0x80, 0x8c, 0x72, 0xe1, 0x43, 0xc1, 0xf6, 0x3e, 0xc0, 0x90, 0xba, 0x31,
	0xc9, 0xdc, 0x5b, 0x3a, 0x92, 0x4a, 0x54, 0x70, 0x59, 0x20, 0x03, 0x01, 0x88, 0x5d, 0xa1, 0x38,
	0x95, 0xbb, 0x62, 0xf5, 0xa3, 0x77, 0xc5, 0xcc, 0x3b, 0xb8, 0x2c, 0xdf, 0x7f, 0xd8, 0x15, 0x49,
	0xca, 0x46, 0x13, 0x8f, 0x8b, 0x01, 0x94, 0x7c, 0xe5, 0x1c, 0xb1, 0x47, 0x4b, 0xcf, 0x8f, 0x4b,
	0x96, 0x4c, 0x92, 0x97, 0x9c, 0x1f, 0xbf, 0x69, 0x4b, 0xce, 0x8f, 0x85, 0x6e, 0xcf, 0x3f, 0x3f,
	0x6c, 0x28, 0x07, 0xf1, 0x98, 0x29, 0x26, 0x0a, 0x1f, 0xcd, 0xc4, 0x4c, 0x3d, 0xac, 0x8b, 0xd7,
	0x25, 0x11, 0x35, 0xd0, 0xd3, 0x49, 0x48, 0x17, 0x24, 0x29, 0x89, 0xf8, 0x2a, 0x0d, 0xcd, 0x77,
	0x50, 0xeb, 0xd1, 0xbb, 0x4e, 0x90, 0x66, 0xfc, 0xa1, 0x49, 0x27, 0x88, 0x7b, 0x8c, 0x07, 0xe3,
	0x29, 0x32, 0xa0, 0x38, 0x99, 0x49, 0x2f, 0x1e, 0xcd, 0x0e, 0x7c, 0x72, 0x4a, 0x67, 0x95, 0x4d,
	0x37, 0x8e, 0x69, 0xfa, 0x12, 0xba, 0x7e, 0xd7, 0x00, 0x3d, 0xed, 0x82, 0x0e, 0x60, 0x7d, 0x28,
	0xa3, 0xc5, 0xfb, 0x0a, 0x14, 0x94, 0xdf, 0x3f, 0x79, 0x44, 0x92, 0xc0, 0xcb, 0xb7, 0x7f, 0x59,
	0x21, 0x97, 0x81, 0x27, 0x06, 0xfd, 0x7e, 0x12, 0x25, 0x8b, 0x83, 0x8a, 0x58, 0x0c, 0x3a, 0x02,
	0x68, 0xce, 0xfb, 0xec, 0xc1, 0x4e, 0xb3, 0xd1, 0xeb, 0x59, 0x78, 0xd9, 0x46, 0xac, 0xc1, 0xd6,
	0x62, 0xf2, 0xbd, 0x65, 0x9d, 0x93, 0x56, 0x03, 0xb7, 0x0d, 0x0d, 0x7d, 0x0a, 0xbb, 0x8b, 0xa9,
	0x8e, 0x8d, 0x07, 0x0e, 0xc1, 0x56, 0xeb, 0xac, 0x81, 0x4f, 0x2d, 0xa3, 0x60, 0xfe, 0xac, 0xc1,
	0xfe, 0x3f, 0x10, 0xf4, 0x7c, 0x07, 0x9c, 0xcf, 0xd8, 0x58, 0xf0, 0xc0, 0x97, 0xff, 0xe5, 0x81,
	0x85, 0x6f, 0xe6, 0x5c, 0x09, 0x0f, 0x98, 0x4d, 0xa8, 0xb6, 0xae, 0xa9, 0xf7, 0x43, 0xcb, 0x8d,
	0x2f, 0xd8, 0x28, 0x18, 0x4f, 0x07, 0xf4, 0xfe, 0xb9, 0xaa, 0xfd, 0x08, 0xb5, 0x25, 0x3d, 0x5e,
	0x74, 0x35, 0xc6, 0x94, 0x8e, 0x08, 0x67, 0xae, 0x9c, 0x4b, 0x5e, 0x8d, 0x02, 0x71, 0x04, 0x20,
	0xce, 0x16, 0x95, 0x51, 0x12, 0xaa, 0xa0, 0xd9, 0x85, 0xaa, 0xc7, 0xa2, 0xfa, 0x34, 0x98, 0xb2,
	0x89, 0xe8, 0x1b, 0xb1, 0x11, 0x0d, 0xd5, 0x2f, 0xd0, 0x77, 0x5f, 0xf9, 0x2c, 0x74, 0x63, 0xbf,
	0xfe, 0xcd, 0x09, 0xe7, 0x75, 0x8f, 0x45, 0xc7, 0x12, 0xf6, 0x58, 0x78, 0xec, 0x26, 0xc9, 0xb2,
	0x3f, 0xac, 0xe1, 0x9a, 0xac, 0xf8, 0xfa, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0x31, 0x17, 0x33,
	0xfd, 0x93, 0x09, 0x00, 0x00,
}
