syntax = "proto3";

/***************猫猫餐厅概率玩法*****************/

package ga.cat_canteen_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/cat-canteen-logic";


enum NotifyPlaceType {
  NOTIFY_PLACE_TYPE_UNSPECIFIED = 0;
  NOTIFY_PLACE_TYPE_CHANNEL = 1; //房间 浮层提醒
  NOTIFY_PLACE_TYPE_GAME = 2;    //玩法首页浮层提醒
  NOTIFY_PLACE_TYPE_PUBLIC= 3;   //公屏提醒
}

message NotifyItem {
  bool is_need = 1;    // 提醒开关
  string text = 2;     // 文案
  string color = 3;    // 文案字体颜色，有值时有效
  uint32 duration = 4; // 显示时长，有值时有效
  NotifyPlaceType place = 5; //提醒位置
}

message GameNotifyInfo {
  enum NotifyType{
    NOTIFY_TYPE_UNSPECIFIED = 0;
    NOTIFY_TYPE_COMMON = 1;  //玩法通用提醒
  }

  NotifyType notify_type = 1;
  uint32 begin_time = 2;            // 提醒开始时间
  uint32 end_time = 3;              // 提醒结束时间
  repeated NotifyItem notify_list = 4; // 提醒信息列表
  bool has_red = 5;                 // 是否有红点提示
}

message GameAccessNotifyInfo{
  enum ChanceGameType{
    CHANCE_GAME_TYPE_UNSPECIFIED = 0;
    CHANCE_GAME_TYPE_CAT_CANTEEN = 1;  //猫猫餐厅玩法
  }

  ChanceGameType game_type = 1;
  bool have_access = 3; // true-有入口权限，false，无入口权限
  repeated GameNotifyInfo notify_list = 4;  //提醒信息列表
  uint64 notify_version = 5;    //玩法提醒信息版本
}

// 获取玩法权限、提醒信息
message GetChanceGameAccessNotifyInfoRequest{
  ga.BaseReq base_req = 1;
  repeated uint32 game_type = 2; // game_type see GameType
  uint32 channel_id = 3;        // 用户所在房间id
}
message GetChanceGameAccessNotifyInfoResponse{
  ga.BaseResp base_resp = 1;
  repeated GameAccessNotifyInfo access_list = 2;
}

message AwardEffect {
  uint32 effect_id = 1;
  string effect_notify_text = 2;    // 中奖提示语， 如 幸运碎片
  string effect_notify_format = 3;  // 中奖提示语格式化字符串, 使用 中奖提示语 替换 “xxx”， 如 xxx66连！稀有麦位框坐骑唾手可得！,没有xxx占位符时，effect_notify_text默认展示在最前面
  string effect_url = 4;            // 光效背景资源
}

message LevelGift {
  uint32 pack_id = 1;     // 包裹id
  string pack_name = 2;   // 包裹名字
  string pack_pic = 3;    // 礼物图标
  uint32 pack_price = 4 ; // 包裹价值（T豆）
  uint32 amount = 5;      // 发放/消耗数量
  AwardEffect effect = 6; // 中奖横幅
  LightEffect light_effect = 7; // 中奖动态光效
}

message LevelMount {
  string mount_id = 1;    // 坐骑id
  string mount_name = 2;  // 坐骑名
  string mount_pic = 3;   // 坐骑图片
  uint32 award_days = 4;  // 奖励发放天数
  uint32 amount = 5;
}

// 关卡升级的奖励
message UpgradeAward {
  LevelGift present = 1;    // 礼物
  LevelMount mount = 2;     // 坐骑
  PropInfo prop_info = 3;  // 奖励的凭证信息
}

message ResourcesCfg {
  string url = 1;          // 关卡动效
  string md5 = 2;
}

// 关卡配置
message LevelCfg {
  uint32 level_id = 1;            // 关卡等级
  string level_name = 2;          // 关卡名
  ResourcesCfg resource_cfg = 3;  // 关卡资源配置
  LevelGift entry_gift = 4;       // 关卡门槛礼物（启动金）
  UpgradeAward upgrade_award = 5; // 关卡升级奖励（通关奖励）
  uint32 cost_chance_per = 6;     // 每次消耗的抽奖机会数量（鱼干）
  repeated uint32 buy_chance_amount_option = 7;  // 购买抽奖（鱼干）的个数选项
  repeated uint32 cost_chance_amount_option = 8; // 使用鱼干（抽奖）个数按钮选项
  uint32 notify_continue_fail_cnt = 9;           // 连续失败次数，达到改次数需有提示，为0则无效
  //bool rate_of_progress_enable = 10;             // 关卡好评值（保底值）进度开关，为false时不展示进度条
  string unlock_notify = 10;                     // 解锁关卡浮层文案
  uint32 max_n = 11;                           // 最大保底值
  PropInfo entry_prop_info = 12;  // 门槛经营凭证，升级成功需要消耗的凭证
}

message UserLevelStatus {
  enum Status {
    STATUS_UNSPECIFIED = 0;         // 无效值
    STATUS_TO_BE_OPEN = 1;          // 待开业
    STATUS_IN_OPENING = 2;          // 营业中
    STATUS_TEMPORARILY_CLOSED = 3;  // 已歇业
    STATUS_CLOSED = 4;              // 已停业
  }

  Status status = 1;
  uint32 days = 2;    // status 为STATUS_TEMPORARILY_CLOSED有效
}

message PropInfo {
  uint32 prop_id = 1;    // 道具id 1:小鱼干 2-5:第2-5关经营凭证
  string prop_icon = 2;  // 道具图标
  string prop_name = 3;  // 道具名
  uint32 amount = 4;     // 发放/消耗数量
  uint32 duration_day = 5; // 发放天数
}

// 用户的关卡存档
message UserPlayFile {
  uint32 level_id = 1;                    // 关卡等级
  uint32 entry_gift_cnt = 2;              // 关卡门槛礼物余额个数
  float rate_of_progress = 3;             // 关卡猫粮罐子进度百分比，满进度时为1
  uint32 can_lv = 4;                      // 猫粮罐子等级
  UserLevelStatus user_level_status = 5;  // 用户的关卡经营状态
  uint32 prop_num = 6;                    // 经营凭证数量
  uint32 user_n = 7;                     // 用户的保底值
}

message LevelInfo {
  LevelCfg level_cfg = 1;         // 关卡配置
  UserPlayFile user_play_info = 2;// 用户玩法信息
}

// 获取游戏信息
message GetGameInfoRequest {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}

message GetGameInfoResponse {
  ga.BaseResp base_resp = 1;
  uint32 user_chance_cnt = 2;             // 用户抽奖机会余额
  repeated LevelInfo level_info_list = 3; // 关卡信息列表
  LevelGift buy_chance_award = 4;         // 购买抽奖(鱼干)时的奖励配置
  ResourcesCfg can_resources_cfg = 5;     // 罐子资源配置
  string buy_chance_expire_desc = 6;      // 小鱼干有效期说明
}

// 购买抽奖机会
message BuyChanceRequest {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  uint32 chance_amount = 3;      // 数量
}

message BuyChanceResponse {
  ga.BaseResp base_resp = 1;
  uint32 final_chance_amount  = 2;    // 最终数量
  uint64 balance = 3;         // T豆余额
}

enum ResultType {
  RESULT_TYPE_UNSPECIFIED = 0;         // 无效值
  RESULT_TYPE_UPGRADE_FAIL = 1;        // 升级失败
  RESULT_TYPE_UPGRADE_SUCCESS = 2;     // 升级成功
  RESULT_TYPE_CROSS_LEVEL_UPGRADE = 3; // 跨级升级成功
}

message LightEffect {
  string effect_url = 1;    // 光效动画资源, 不存在时不展示
  string effect_md5 = 2;    // 光效md5
  string effect_json = 3;   // 光效json
}

// 奖励
message LotteryDrawAward {
  ResultType result_type = 1;
  LevelGift present = 2;    // 礼物
  LevelMount mount = 3;     // 坐骑
  PropInfo prop_info = 4;  // 奖励的凭证信息
}

message LotteryDrawResult {
  uint32 level_id = 1;
  ResultType result_type = 2;            // 最优奖励的类型
  repeated LotteryDrawAward award_list = 3;  // 奖励列表
  uint32 next_level_id = 4;              // 可升级到的关卡id
  uint32 incr_progress_val = 5;          // 增加的猫粮数
}

// 抽奖（经营）
message LotteryDrawRequest {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  uint32 amount = 3; // 抽奖次数
  uint32 level_id = 4;      // 关卡
}

message LotteryDrawResponse {
  ga.BaseResp base_resp = 1;
  uint32 remain_chance = 2;                   // 剩余抽奖机会数量（鱼干）
  repeated UserPlayFile play_file_list = 3;   // 玩法存档信息列表
  LotteryDrawResult result = 4;               // 结果
}

message GetUserPlayFileRequest {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}

message GetUserPlayFileResponse {
  ga.BaseResp base_resp = 1;
  repeated UserPlayFile play_file_list = 2;   // 玩法存档信息列表
  uint32 user_chance_cnt = 3;             // 用户抽奖机会余额(小鱼干)
}

// 中奖记录
message GameWinningRecord {
  enum RecordType {
    RECORD_TYPE_UNSPECIFIED = 0;          // 无效值
    RECORD_TYPE_AWARD_POSITIVE_VAL = 1;   // 奖励猫粮
    RECORD_TYPE_AWARD_GIFT = 2;           // 奖励礼物（小费）
    RECORD_TYPE_AWARD_UPGRADE = 3;        // 升级奖励
    RECORD_TYPE_AWARD_CROSS_LEVEL = 4;    // 跨级奖励
  }

  string id = 1;
  string order_id = 2;              // 订单号
  RecordType record_type = 3;       // 记录类型
  uint32 incr_progress_val = 4;     // 增加的进度值(猫粮值)
  LevelGift award_info = 5;         // 奖励的礼物信息
  LevelMount award_mount = 6;       // 奖励的坐骑信息
  LevelGift entry_gift = 7;         // 关卡门槛礼物（启动金）
  uint32 create_time = 8;
  PropInfo award_prop = 9;          // 奖励的凭证信息
}

// 获取用户中奖纪录
message GetWinningRecordsRequest {
  enum QueryType {
    QUERY_TYPE_UNSPECIFIED = 0;  // 无效值
    QUERY_TYPE_ALL = 1;          // 全部记录
    QUERY_TYPE_BONUS = 2;        // 分红记录（获奖记录）
  }

  ga.BaseReq base_req = 1;
  uint32 level_id = 2;
  string offset_id = 3;
  uint32 limit = 4;
  QueryType query_type = 5;
}

message GetWinningRecordsResponse {
  ga.BaseResp base_resp = 1;
  repeated GameWinningRecord records = 2;  // 中奖列表
}

// 平台中奖记录  轮播用
message SimpleGameWinningRecord {
  uint32 uid = 1;
  string nickname = 2;    // 获奖用户名
  LevelGift gift = 3;     // 礼物
}

// 获取最新的中奖纪录
message GetRecentWinningRecordsRequest {
  ga.BaseReq base_req = 1;
  uint32 limit = 2;
}

message GetRecentWinningRecordsResponse {
  ga.BaseResp base_resp = 1;
  repeated SimpleGameWinningRecord records = 2;  // 平台中奖记录
}

// 道具时效
message CatPropTimeliness {
  uint32 num = 1;
  int64 fin_timestamp = 2; // 到期时间戳（秒级）
  string level_name = 3;   // 关卡名
  uint32 prop_id = 4;      // 道具id
  string prop_name = 5;    // 经营凭证名
  string prop_icon = 6;    // 经营凭证图标
}

message GetUserCatPropListRequest {
  ga.BaseReq base_req = 1;
  bool sort_asc = 2;    // 是否顺序排序

  enum PropType {
    PROP_TYPE_UNSPECIFIED = 0;
    PROP_TYPE_FISH = 1;  // 小鱼干
    PROP_TYPE_VOUCHER = 2;  // 凭证
  }
  uint32 prop_type = 3;  // 道具类型 see PropType
}

message GetUserCatPropListResponse {
  ga.BaseResp base_resp = 1;
  repeated CatPropTimeliness prop_list = 2;
  string desc = 3;    // 描述
}

message CatPropNotify {
  uint32 prop_id = 1;    // 道具id 1:小鱼干 2-5:第2-5关经营凭证
  string notify_content = 2; // 提醒内容
}

message GetUserExpireCatPropNotifyRequest {
  ga.BaseReq base_req = 1;
}

message GetUserExpireCatPropNotifyResponse {
  ga.BaseResp base_resp = 1;
  bool need_notify = 2;        // 是否展示红点及浮层提醒
  repeated CatPropNotify notify_list = 3;
}

message GetCatCanteenResourceRequest {
  ga.BaseReq base_req = 1;
}

message GetCatCanteenResourceResponse {
  ga.BaseResp base_resp = 1;
  ResourcesCfg can_resources_cfg = 2;     // 罐子资源配置
  repeated LevelCfg level_cfg = 3;         // 关卡配置
}