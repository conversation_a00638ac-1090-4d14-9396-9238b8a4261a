syntax = "proto3";

package ga.mijing_npc_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/mijing-npc";

// NPC加入游戏
message MiJingNPCJoinGameReq{
    ga.BaseReq base_req = 1;
    uint32 scenario_id = 2;
    uint32 channel_id = 3;
}
message MiJingNPCJoinGameResp{
    ga.BaseResp base_resp = 1;
}

// 剧本是否支持NPC
message IsMiJingNPCSupportedReq{
    ga.BaseReq base_req = 1;
    repeated uint32 scenario_ids = 2;
}
message IsMiJingNPCSupportedResp{
    ga.BaseResp base_resp = 1;
    map<uint32,MiJingNpc> npcs = 2; // 为空则不支持
}

message MiJingNpc{
    string npc_url = 1; // 头像
    string nickname_url = 2; // 昵称
    string npc_bg_url = 3; // 背景
    string welcome_img_url = 4;  // 左上角的图
    string btn_bg_url = 5; // 主题推荐弹窗“找搭子玩”按钮背景
    string hi_content = 6; // 入口弹窗的2s后变化的文案
    string comment_img_url = 7;//  评论弹窗的图片(改名)

    MiJingNpcRes ainpc_btn_small = 8;
    MiJingNpcRes ainpc_btn_big = 9;
    MiJingNpcRes ainpc_btn_room_lottie = 10;
    MiJingNpcRes ainpc_btn_matching_lottie = 11;
    MiJingNpcRes ainpc_role_lottie = 12;
    string nickname = 13;
    uint32 npc_uid = 14; // npc用户id
}

message MiJingNpcRes{
    string md5 = 1;
    string url = 2;
}

// 机器人异常退出消息
message MijingNpcExit{
    string toast_text = 1;
}

message MijingNpcChannelImMsg{
    uint32 uid = 1;
    string nickname = 2;
    string content = 3; // 文本內容或者图片url（根据消息类型区分）
}

// 获取快捷语
message GetEscapeTipMsgRequest{
    ga.BaseReq base_req = 1;
    string text = 2;
    string bot_account = 3;  // 机器人账号的 Account
}
message GetEscapeTipMsgResponse{
    ga.BaseResp base_resp = 1;
    repeated string tip_list = 2;
}

// 获取一条机器人消息
message GetEscapeChatBotMsgRequest{
    ga.BaseReq base_req = 1;
    string account = 2; // 机器人账号的account
}
message GetEscapeChatBotMsgResponse{
    ga.BaseResp base_resp = 1;
}

// 获取聊天机器人信息
message GetEscapeChatBotInfoRequest{
    ga.BaseReq base_req = 1;
    string account = 2;
}
message GetEscapeChatBotInfoResponse{
    ga.BaseResp base_resp = 1;
    EscapeChatBot bot = 2;
}

// AI 聊天机器人（客户端）
message EscapeChatBot {
    // 内部信息
    string id = 1; // 唯一标识，p.s Insert 传空，Update 回传
    uint32 uid = 2; // 机器人 UID
    string account = 3; // mijing-npc.EscapeChatBot.username 机器人用户名
    uint32 order = 4; // 序号

    // 基础信息
    string nickname = 101;  // 机器人名称
    string bot_id = 102;  // 机器人 ID（中台提供）
    string version = 103;  // 机器人版本 ID
    string timbre_id = 104;  // 音色 ID
    string funny_session_id = 105;  // 今日趣事对话 ID

    // 基础人设
    string character = 201; // 人设内容
    string nickname_abbr = 203; // 机器人简称
    string avatar_url = 204; // 头像
    string appearance_url = 205; // 形象
    string chat_greeting = 206; // 开场白
    string chat_greeting_url = 207; // 开场白音频
    uint32 chat_greeting_duration = 208; // 开场白音频时长，单位：毫秒

    // 背景图
    string select_bg_url = 401; // 机器人选择页背景
    string chat_bg_url = 402; // 聊天窗背景
}

// 获取聊天机器人列表
message GetEscapeChatBotListRequest {
    ga.BaseReq base_req = 1;
}

message GetEscapeChatBotListResponse {
    ga.BaseResp base_resp = 1;
    repeated EscapeChatBot escape_chat_bot_list = 2;
}

message PromptAutoReplyMsg {
    string audio_url = 1;
    uint32 audio_duration_ms = 2; // 时长 毫秒级
    string audio_content = 3; // 具体播放的音频文本，括号外的文案，才需要转化为音频 例：（尾巴摇了摇，走过来）主人，有什么吩咐？
}
