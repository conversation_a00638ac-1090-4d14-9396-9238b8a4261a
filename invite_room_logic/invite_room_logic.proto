syntax = "proto3";

package ga.invite_room_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/invite-room-logic";


// const unsigned int CMD_GetInviteEntranceInfo = 6001; // 获取开黑入口信息
message GetInviteEntranceInfoReq{
  enum Source {
    SOURCE_UNSPECIFIED = 0;
    SOURCE_ENTER_ROOM = 1; // 进房
    SOURCE_ROTATE_AVATAR = 2; // 轮播头像
  }

  ga.BaseReq base_req = 1;
  
  // 玩法ID
  uint32 tab_id = 2;
  // 请求来源 see enum Source
  uint32 source = 3;
}

message GetInviteEntranceInfoResp{
  message User {
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
  }

  ga.BaseResp base_resp = 1;
  
  // 是否开启入口
  bool enable = 2;
  repeated User list = 3;
}


// const unsigned int CMD_GetInviteList = 6002; // 获取邀请列表
message GetInviteListReq{
  ga.BaseReq base_req = 1;
  // 用户房间玩法
  uint32 tab_id = 2;
  // 筛选标签
  repeated string labels = 3;
  // 强推uid,邀请入口点击用户头像需带上该用户
  repeated uint32 force_uid = 4;
  // 上次请求未曝光id列表
  repeated uint32 no_browse_list = 5;
  // 1-请求下一页，2-刷新
  uint32 req_type = 6;
}

message GetInviteListResp{
  ga.BaseResp base_resp = 1;
  // 列表数据
  repeated InviteListItem items = 2;
  //返回true表示没有下一页了
  bool load_finish = 3;
}

message InviteListItem {
  // 用户偏好玩法名称，顶部展示
  string tab_name = 1;
  // 用户偏好玩法图片，顶部展示
  string tab_img = 2;
  // 卡片标题，顶部展示
  string title = 3;
  // 卡片用户信息，中间展示
  InviteRoomUserInfo invite_user_info = 4;
  // 普通标签，底部展示
  repeated string user_tags = 5;
}

message InviteRoomUserInfo {
  enum OnlineStatus {
    ONLINE_STATUS_UNSPECIFIED = 0; // 离线
    ONLINE_STATUS_PUBLISHING = 1; // 发布中
    ONLINE_STATUS_ONLINE = 2; // 在线
    ONLINE_STATUS_IN_ROOM = 3; // 在房
  }

  enum InviteStatus {
    INVITE_STATUS_UNSPECIFIED = 0; // 无邀请
    INVITE_STATUS_INVITED = 1; // 邀请过
    INVITE_STATUS_REJECTED = 2; // 已拒绝
  }

  uint32 uid = 1;
  // 用户ttid
  string account = 2;
  // 用户昵称
  string nickname = 3;
  // 性别0：女，1：男
  uint32 sex = 4;
  // 用户状态，0-离线，1-发布中，2-在线 see OnlineStatus
  uint32 status = 5;
  // 关注的人/我的玩伴， 展示在头像下面，空不展示
  string follow_text = 6;
  // X前一起玩过Y ，展示在昵称下面，空不展示
  string play_text = 7;
  // 邀请成功率文案，空不展示
  string invite_rate_text = 8;
  // 当天邀请状态，0-未邀请过，1-已邀请，2-被拒绝过，用户展示按钮文案和提示文案, see enum InviteStatus
  uint32 invite_status = 9;
  // 互关好友备注名
  string friend_remark = 10;
}

// const unsigned int CMD_GetBeInvitedRecord = 6003; // 获取受邀记录
message GetBeInvitedRecordReq{
  ga.BaseReq base_req = 1;
  // 上次请求最后一条记录id
  string last_record_id = 2 [deprecated = true]; // 已废弃
  // 上次请求最后一条记录的邀请时间
  int64 last_record_invite_time = 3;
}

message GetBeInvitedRecordResp{
  ga.BaseResp base_resp = 1;
  // 列表数据
  repeated InviteRecordItem items = 2;
  //返回true表示没有下一页了
  bool load_finish = 3;
}

message BeInviteRoomUserInfo {
  uint32 uid = 1;
  // 用户ttid
  string account = 2;
  // 用户昵称
  string nickname = 3;
  // 性别0：女，1：男
  uint32 sex = 4;
  // 关注的人/我的玩伴， 展示在头像下面，空不展示
  string follow_text = 5;
}

message InviteChannelInfo {
  // 房间id
  uint32 channel_id = 1;
  // 受邀用户进房token
  string cipher_text = 2;
  // 当前在房人数
  uint32 channel_user_count = 3;
  // 当前在麦人数
  uint32 on_mic_user_count = 4;
  // 房间名
  string channel_name = 5;
}

message InviteRecordItem {
  // 邀请记录id
  string id = 1;
  // 玩法名称
  string tab_name = 2;
  // 玩法图片
  string tab_img = 3;
  // 邀请正文
  string invite_text = 4;
  // 邀请用户信息
  BeInviteRoomUserInfo inviter = 5;
  // 房间信息
  InviteChannelInfo channel_info = 6;
  // 邀请标签
  repeated string user_tags = 7 [deprecated = true]; // 已废弃
  // 邀请有效期：X分钟/X秒
  string valid_time_text = 8;
  // 过期时间，单位:ms
  int64 expire_time = 9;
  // 邀请时间，单位:ms
  int64 invite_time = 10;
  // 顶部邀请标签
  repeated string top_invite_tags = 11;
  // 中部邀请标签
  repeated string middle_invite_tags = 12;
  // 推送弹窗背景图，取游戏卡配置
  string background_img = 13;
  // 背景图底色值
  uint32 background_color_num = 14;
}

// 根据玩法id，返回邀请列表筛选标签
message GetFilterLabelsRequest {
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2; // 玩法id
}

message GetFilterLabelsResponse {
  ga.BaseResp base_resp = 1;
  repeated string hot_labels = 2; // 热门标签
  repeated RegularLabel regular_labels = 3; // 常规标签

}

message RegularLabel {
  string block_title = 1; // 一级标签文案
  repeated string elem_titles = 2; // 二级标签文案
}

// const unsigned int CMD_UserSendInvite = 6005; // 用户发出邀请
message UserSendInviteReq {
  ga.BaseReq base_req = 1;
  // 被邀请用户uid
  uint32 be_invite_uid = 2;
  // 邀请人所在房间玩法id
  uint32 tab_id = 3;
}

message UserSendInviteResp {
  ga.BaseResp base_resp = 1;
  // 邀请记录
  InviteRecordItem invite_record = 2;
}

// const unsigned int CMD_UserRefuseInvite = 6006; // 用户拒绝邀请
message UserRefuseInviteReq {
  ga.BaseReq base_req = 1;
  // 邀请记录id
  string invite_record_id = 2;
  // 邀请人uid
  uint32 inviter_uid = 3;
  // 邀请时间戳，跟id共同组成唯一标识
  int64 invite_time = 4;
}

message UserRefuseInviteResp {
  ga.BaseResp base_resp = 1;
}

// const unsigned int CMD_OpenTodayDontDisturb = 6007; // 开启今日免打扰
message OpenTodayDontDisturbReq {
  ga.BaseReq base_req = 1;
}

message OpenTodayDontDisturbResp {
  ga.BaseResp base_resp = 1;
}

// const unsigned int CMD_InviteBlockUser = 6008; // 屏蔽该用户
message InviteBlockUserReq {
  ga.BaseReq base_req = 1;
  // 屏蔽用户uid
  uint32 block_uid = 2;
}

message InviteBlockUserResp {
  ga.BaseResp base_resp = 1;
}

enum UserBehaviorStatus {
  USER_BEHAVIOR_STATUS_UNSPECIFIED = 0;
  USER_BEHAVIOR_STATUS_IN_ROOM_BG_FULL_SCREEN = 1; // 在房全屏切到后台
  USER_BEHAVIOR_STATUS_IN_ROOM_BG_MINIMISE = 2; // 在房最小化切换到后台
  USER_BEHAVIOR_STATUS_IN_ROOM_FULL_SCREEN = 3; // 前台在房全屏
  USER_BEHAVIOR_STATUS_IN_ROOM_MINIMISE = 4; // 前台在房最小化
  USER_BEHAVIOR_STATUS_NOT_IN_ROOM_BACKGROUND = 5; // 切后台非在房
  USER_BEHAVIOR_STATUS_NOT_IN_ROOM_FRONT = 6; // 前台非在房
}

// const unsigned int CMD_UserBehaviorStatusReport = 6009; // 用户行为状态数据上报
message UserBehaviorStatusReportReq {
  ga.BaseReq base_req = 1;
  // 用户行为状态 see enum UserBehaviorStatus
  uint32 status = 2;
}

message UserBehaviorStatusReportResp {
  ga.BaseResp base_resp = 1;
}

enum InviteRecordStatus {
  INVITE_RECORD_STATUS_UNSPECIFIED = 0;
  INVITE_RECORD_STATUS_REFUSE = 1; // 拒绝
  INVITE_RECORD_STATUS_EXPIRE = 2; // 过期
}

// 邀请记录状态变更推送
message InviteRecordStatusChangeNotify {
  // 邀请记录id
  string invite_record_id = 1;
  // 用户uid
  uint32 uid = 2;
  // 用户ttid
  string account = 3;
  // 邀请状态1-拒绝 2-过期 see enum InviteRecordStatus
  uint32 status = 4;
  // 邀请时间
  int64 invite_time = 5;
}

// 发送邀请推送
message InviteRoomNotify {
  InviteRecordItem item = 1;  // 邀请记录
}

// 用户行为状态数据上报kafka数据上报
message UserBehaviorStatusReportEvent {
  // 用户uid
  uint32 uid = 1;
  // 用户行为状态 see enum UserBehaviorStatus
  uint32 status = 2;
}