// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";

option cc_enable_arenas = true;

import "google/protobuf/unittest.proto";
import "google/protobuf/unittest_no_arena.proto";

// We don't put this in a package within proto2 because we need to make sure
// that the generated code doesn't depend on being in the proto2 namespace.
// In map_test_util.h we do "using namespace unittest = protobuf_unittest".
package protobuf_unittest;

// Tests maps.
message TestMap {
  map<int32   , int32   > map_int32_int32       = 1;
  map<int64   , int64   > map_int64_int64       = 2;
  map<uint32  , uint32  > map_uint32_uint32     = 3;
  map<uint64  , uint64  > map_uint64_uint64     = 4;
  map<sint32  , sint32  > map_sint32_sint32     = 5;
  map<sint64  , sint64  > map_sint64_sint64     = 6;
  map<fixed32 , fixed32 > map_fixed32_fixed32   = 7;
  map<fixed64 , fixed64 > map_fixed64_fixed64   = 8;
  map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  map<int32   , float   > map_int32_float       = 11;
  map<int32   , double  > map_int32_double      = 12;
  map<bool    , bool    > map_bool_bool         = 13;
  map<string  , string  > map_string_string     = 14;
  map<int32   , bytes   > map_int32_bytes       = 15;
  map<int32   , MapEnum > map_int32_enum        = 16;
  map<int32   , ForeignMessage> map_int32_foreign_message = 17;
  map<string  , ForeignMessage> map_string_foreign_message = 18;
  map<int32   , TestAllTypes> map_int32_all_types = 19;
}

message TestMapSubmessage {
  TestMap test_map = 1;
}

message TestMessageMap {
  map<int32, TestAllTypes> map_int32_message = 1;
}

// Two map fields share the same entry default instance.
message TestSameTypeMap {
  map<int32, int32> map1 = 1;
  map<int32, int32> map2 = 2;
}


enum MapEnum {
  MAP_ENUM_FOO = 0;
  MAP_ENUM_BAR = 1;
  MAP_ENUM_BAZ = 2;
}

// Test embedded message with required fields
message TestRequiredMessageMap {
  map<int32, TestRequired> map_field = 1;
}

message TestArenaMap {
  map<int32   , int32   > map_int32_int32       = 1;
  map<int64   , int64   > map_int64_int64       = 2;
  map<uint32  , uint32  > map_uint32_uint32     = 3;
  map<uint64  , uint64  > map_uint64_uint64     = 4;
  map<sint32  , sint32  > map_sint32_sint32     = 5;
  map<sint64  , sint64  > map_sint64_sint64     = 6;
  map<fixed32 , fixed32 > map_fixed32_fixed32   = 7;
  map<fixed64 , fixed64 > map_fixed64_fixed64   = 8;
  map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 9;
  map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 10;
  map<int32   , float   > map_int32_float       = 11;
  map<int32   , double  > map_int32_double      = 12;
  map<bool    , bool    > map_bool_bool         = 13;
  map<string  , string  > map_string_string     = 14;
  map<int32   , bytes   > map_int32_bytes       = 15;
  map<int32   , MapEnum > map_int32_enum        = 16;
  map<int32   , ForeignMessage> map_int32_foreign_message = 17;
  map<int32, .protobuf_unittest_no_arena.ForeignMessage>
      map_int32_foreign_message_no_arena = 18;
}

// Previously, message containing enum called Type cannot be used as value of
// map field.
message MessageContainingEnumCalledType {
  enum Type {
    TYPE_FOO = 0;
  }
  map<string, MessageContainingEnumCalledType> type = 1;
}

// Previously, message cannot contain map field called "entry".
message MessageContainingMapCalledEntry {
  map<int32, int32> entry = 1;
}

message TestRecursiveMapMessage {
  map<string, TestRecursiveMapMessage> a = 1;
}
