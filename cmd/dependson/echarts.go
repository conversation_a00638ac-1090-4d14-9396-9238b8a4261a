package main

import (
	"bytes"
	"encoding/json"
	"text/template"

	"golang.52tt.com/cmd/dependson/echarts"
)

type Node struct {
	Name      string      `json:"name"`
	Children  interface{} `json:"children"`
	ItemStyle ItemStyle   `json:"itemStyle"`
}

type ItemStyle struct {
	Color string `json:"color"`
}

func getHtmlContent(data interface{}) []byte {
	t := template.
		Must(template.New("tree-basic.html").
			Funcs(template.FuncMap{
				"toJson": func(v interface{}) string {
					a, _ := json.Marshal(v)
					return string(a)
				},
			}).Parse(string(echarts.IndexTemplate())))

	params := struct {
		Data interface{}
	}{
		Data: data,
	}

	var indexBuf bytes.Buffer
	if err := t.Execute(&indexBuf, params); err != nil {
		panic(err)
	}
	return indexBuf.Bytes()
}
