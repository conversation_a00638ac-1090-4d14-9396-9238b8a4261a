<!--
    THIS EXAMPLE WAS DOWNLOADED FROM https://echarts.apache.org/examples/zh/editor.html?c=tree-basic
-->
<!DOCTYPE html>
<html style="height: 100%">

<head>
    <meta charset="utf-8">
</head>

<body style="height: 100%; margin: 0">
    <div id="container" style="height: 100%"></div>

    <script src="https://apps.bdimg.com/libs/jquery/2.1.4/jquery.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/echarts@5/dist/echarts.min.js"></script>
    <!-- Uncomment this line if you want to dataTool extension
        <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/echarts@5/dist/extension/dataTool.min.js"></script>
        -->
    <!-- Uncomment this line if you want to use gl extension
        <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/echarts-gl@2/dist/echarts-gl.min.js"></script>
        -->
    <!-- Uncomment this line if you want to echarts-stat extension
        <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/echarts-stat@latest/dist/ecStat.min.js"></script>
        -->
    <!-- Uncomment this line if you want to use map
        <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/echarts@5/map/js/china.js"></script>
        <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/echarts@5/map/js/world.js"></script>
        -->
    <!-- Uncomment these two lines if you want to use bmap extension
        <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=<Your Key Here>"></script>
        <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/echarts@5/dist/extension/bmap.min.js"></script>
        -->

    <script type="text/javascript">
        var dom = document.getElementById("container");
        var myChart = echarts.init(dom);

        var option;

        myChart.showLoading();
        // var dataStr = {"name":"master-apprentice-logic","children":[{"name":"abtest","children":null,"itemStyle":{"color":"#543cd2"}},{"name":"account( c++ )","children":null,"itemStyle":{"color":"#506879"}},{"name":"antispamlogic( c++ )","children":null,"itemStyle":{"color":"#8c9276"}},{"name":"banuser( c++ )","children":null,"itemStyle":{"color":"#44890a"}},{"name":"channelol( c++ )","children":null,"itemStyle":{"color":"#54a113"}},{"name":"master-apprentice","children":[{"name":"abtest","children":null,"itemStyle":{"color":"#543cd2"}},{"name":"account( c++ )","children":null,"itemStyle":{"color":"#506879"}},{"name":"antispamlogic( c++ )","children":null,"itemStyle":{"color":"#8c9276"}},{"name":"banuser( c++ )","children":null,"itemStyle":{"color":"#44890a"}},{"name":"newbiesvr","children":null,"itemStyle":{"color":"#126475"}},{"name":"usertag( c++ )","children":null,"itemStyle":{"color":"#f2b2fb"}}],"itemStyle":{"color":"#23af36"}},{"name":"newbiesvr","children":null,"itemStyle":{"color":"#126475"}},{"name":"push-v3","children":[{"name":"account( c++ )","children":null,"itemStyle":{"color":"#506879"}},{"name":"lbs-supervise-server","children":null,"itemStyle":{"color":"#a6f9f7"}},{"name":"presence( c++ )","children":null,"itemStyle":{"color":"#fad881"}},{"name":"push-v3-token","children":null,"itemStyle":{"color":"#2a28c4"}}],"itemStyle":{"color":"#c29836"}},{"name":"seqgen-v2","children":null,"itemStyle":{"color":"#0d8ca3"}},{"name":"ugc-friendship","children":[{"name":"seqgen-v2","children":null,"itemStyle":{"color":"#0d8ca3"}}],"itemStyle":{"color":"#b3138d"}}],"itemStyle":{"color":"#a38c18"}}
        // var data = JSON.parse(dataStr);
        var data = {"name":"master-apprentice-logic","children":[{"name":"abtest","children":null,"itemStyle":{"color":"#543cd2"}},{"name":"account( c++ )","children":null,"itemStyle":{"color":"#506879"}},{"name":"antispamlogic( c++ )","children":null,"itemStyle":{"color":"#8c9276"}},{"name":"banuser( c++ )","children":null,"itemStyle":{"color":"#44890a"}},{"name":"channelol( c++ )","children":null,"itemStyle":{"color":"#54a113"}},{"name":"master-apprentice","children":[{"name":"abtest","children":null,"itemStyle":{"color":"#543cd2"}},{"name":"account( c++ )","children":null,"itemStyle":{"color":"#506879"}},{"name":"antispamlogic( c++ )","children":null,"itemStyle":{"color":"#8c9276"}},{"name":"banuser( c++ )","children":null,"itemStyle":{"color":"#44890a"}},{"name":"newbiesvr","children":null,"itemStyle":{"color":"#126475"}},{"name":"usertag( c++ )","children":null,"itemStyle":{"color":"#f2b2fb"}}],"itemStyle":{"color":"#23af36"}},{"name":"newbiesvr","children":null,"itemStyle":{"color":"#126475"}},{"name":"push-v3","children":[{"name":"account( c++ )","children":null,"itemStyle":{"color":"#506879"}},{"name":"lbs-supervise-server","children":null,"itemStyle":{"color":"#a6f9f7"}},{"name":"presence( c++ )","children":null,"itemStyle":{"color":"#fad881"}},{"name":"push-v3-token","children":null,"itemStyle":{"color":"#2a28c4"}}],"itemStyle":{"color":"#c29836"}},{"name":"seqgen-v2","children":null,"itemStyle":{"color":"#0d8ca3"}},{"name":"ugc-friendship","children":[{"name":"seqgen-v2","children":null,"itemStyle":{"color":"#0d8ca3"}}],"itemStyle":{"color":"#b3138d"}}],"itemStyle":{"color":"#a38c18"}};

        $(function () {
            myChart.hideLoading();

            try {
                data.children.forEach(function (datum, index) {
                    index % 2 === 0 && (datum.collapsed = true);
                });
            }
            catch(err){
                console.log(err);
            }
            

            myChart.setOption(option = {
                tooltip: {
                    trigger: 'item',
                    triggerOn: 'mousemove'
                },
                series: [
                    {
                        type: 'tree',

                        data: [data],

                        top: '1%',
                        left: '7%',
                        bottom: '1%',
                        right: '20%',

                        symbolSize: 14,

                        label: {
                            position: 'left',
                            verticalAlign: 'middle',
                            align: 'right',
                            fontSize: 14
                        },

                        leaves: {
                            label: {
                                position: 'right',
                                verticalAlign: 'middle',
                                align: 'left'
                            }
                        },

                        emphasis: {
                            focus: 'descendant'
                        },

                        expandAndCollapse: true,
                        animationDuration: 550,
                        animationDurationUpdate: 750
                    }
                ]
            });
        });

        if (option && typeof option === 'object') {
            myChart.setOption(option);
        }

    </script>
</body>

</html>