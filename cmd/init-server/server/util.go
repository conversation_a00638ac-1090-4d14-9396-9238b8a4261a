package main

import (
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"path"
	"path/filepath"
	"strings"
	"text/template"
)

const (
	TPLFILE   int = 1
	JSONFILE  int = 2
	PROTOFILE int = 3
)

func checkFileIsExist(filename string) bool {
	var exist = true
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		exist = false
	}
	return exist
}

// 判断文件夹是否存在
func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

func getDstPath(conf Config, filePath, outPath string, rename bool) (dstPath string, fileType int) {
	fileName := filepath.Base(filePath)
	if strings.Contains(fileName, ".tpl") {
		fileType = TPLFILE
	} else if strings.Contains(fileName, ".json") {
		fileType = JSONFILE
	} else if strings.Contains(fileName, ".proto") {
		fileType = PROTOFILE
	} else {
		fmt.Printf("filename err: %s\n", filePath)
		return
	}
	fileNames := strings.Split(fileName, ".")
	if len(fileNames) != 2 {
		fmt.Printf("len is: \n", len(fileNames))
		return
	}

	if rename {
		if fileType == TPLFILE {
			dstPath = path.Join(outPath, conf.Servicename+"_"+fileNames[0]+".go")
		} else if fileType == JSONFILE {
			dstPath = path.Join(outPath, conf.Servicename+".json")
		} else if fileType == PROTOFILE {
			dstPath = path.Join(outPath, conf.Servicename+".proto")
		}
	} else {
		if fileType == TPLFILE {
			dstPath = path.Join(outPath, fileNames[0]+".go")
		} else if fileType == JSONFILE {
			dstPath = path.Join(outPath, fileNames[0]+".json")
		} else if fileType == PROTOFILE {
			dstPath = path.Join(outPath, fileNames[0]+".proto")
		}
	}
	return
}

func tempalte(conf Config, filePath string, outPath string, rename bool) (err error) {
	filename, fileType := getDstPath(conf, filePath, outPath, rename)
	if filename == "" {
		return errors.New("filename is not exist")
	}
	var f *os.File
	if checkFileIsExist(filename) { // 如果文件存在
		f, err = os.OpenFile(filename, os.O_WRONLY, 0666) // 打开文件
		if err != nil {
			fmt.Printf("err:%+v\n", err)
			return err
		}
	} else {
		exist, err := PathExists(outPath)
		if err != nil {
			return err
		}

		if exist {
			fmt.Printf("has dir:%s\n", outPath)
		} else {
			fmt.Printf("no dir:%s\n", outPath)
			// 创建文件夹
			err := os.Mkdir(outPath, os.ModePerm)
			if err != nil {
				return err
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}

		f, err = os.Create(filename) // 创建文件
		if err != nil {
			fmt.Printf("err:%+v\n", err)
			return err
		}

		fmt.Printf("gen file:%s\n", filename)
	}

	if fileType == TPLFILE || fileType == PROTOFILE || fileType == JSONFILE {
		tmpl, err := template.New(filepath.Base(filePath)).ParseFiles(filePath)
		if err != nil {
			return fmt.Errorf("Unable to process template %s, %s", filePath, err)
		}

		/*temp, err := ioutil.TempFile(filepath.Dir(dstPath), "")
		defer temp.Close()
		if err != nil {
			return err
		}*/

		fmt.Println("dst path:", f.Name())
		if err = tmpl.Execute(f, conf); err != nil {
			os.Remove(f.Name())
			return err
		}
	} else if fileType == JSONFILE {
		fileInfo, err := ioutil.ReadFile(filePath)
		if err != nil {
			return err
		}
		_, err = f.Write(fileInfo)
		if err != nil {
			return err
		}
	}
	return nil
}

func Capitalize(str string) string {
	var upperStr string
	vv := []rune(str) // 后文有介绍
	for i := 0; i < len(vv); i++ {
		if i == 0 {
			if vv[i] >= 97 && vv[i] <= 122 { // 后文有介绍
				vv[i] -= 32 // string的码表相差32位
				upperStr += string(vv[i])
			} else {
				fmt.Println("Not begins with lowercase letter,")
				return str
			}
		} else {
			upperStr += string(vv[i])
		}
	}
	return upperStr
}