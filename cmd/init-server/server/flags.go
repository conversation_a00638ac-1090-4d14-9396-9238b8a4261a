package main

import (
	"flag"
	"fmt"
	"os"
)

type ServerFlags struct {
	*flag.FlagSet

	projectPath string
	serviceName string
	className   string
	haveMongo   bool
	haveMysql   bool
	haveRedis   bool
	haveKafka   bool
	haveTicker  bool
}

func ParseServerFlags(args []string) *ServerFlags {
	fs := flag.NewFlagSet(args[0], flag.ExitOnError)

	var (
		projectPathFlag = fs.String("project", "../../../../", "projectPath")
		serviceNameFlag = fs.String("sn", "", "serviceName")
		haveMongoFlag   = fs.Bool("hm", false, "haveMongo")
		haveMysqlFlag   = fs.Bool("hsql", false, "haveMysql")
		haveRedisFlag   = fs.Bool("hr", false, "haveRedis")
		haveKafkaFlag   = fs.Bool("hk", false, "haveKafka")
		haveTickerFlag  = fs.Bool("ht", false, "haveTicker")
		helpFlag        = fs.Bool("h", false, "help")
	)

	if err := fs.Parse(args[1:]); err != nil {
		return nil
	}

	if *helpFlag {
		fmt.Fprintf(os.Stdout, "Usage of %s:\n", os.Args[0])
		fs.SetOutput(os.Stdout)
		fs.PrintDefaults()
		os.Exit(0)
	}

	if *serviceNameFlag == "" {
		fmt.Fprintf(os.Stdout, "Usage of %s:\n", os.Args[0])
		fs.SetOutput(os.Stdout)
		fs.PrintDefaults()
		os.Exit(0)
	}

	return &ServerFlags{
		projectPath: *projectPathFlag,
		serviceName: *serviceNameFlag,
		haveMongo:   *haveMongoFlag,
		haveMysql:   *haveMysqlFlag,
		haveRedis:   *haveRedisFlag,
		haveKafka:   *haveKafkaFlag,
		haveTicker:  *haveTickerFlag,
		FlagSet:     fs,
	}
}
