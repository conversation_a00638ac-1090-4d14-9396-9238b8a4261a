package mongo

import (
	"fmt"
	"github.com/globalsign/mgo"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"time"
)

type MongoDao struct {
	infoSession *mgo.Session
	dbName      string
}

const (
	GenIdCollection  = "gen_id"
)

func NewMongoDao(cfg *config.MongoConfig) (*MongoDao, error) {
	fmt.Println("uri:", cfg)
	url := cfg.URI()
	info, err := mgo.ParseURL(url)
	if err != nil {
		return nil, err
	}

	info.Timeout = 3 * time.Second
	info.ReadTimeout = 15 * time.Second
	sess, err := mgo.DialWithInfo(info)
	if err != nil {
		log.Errorf("Failed to mgo.DialWithTimeout url(%s) err(%v)", url, err)
		return nil, err
	}

	sess.SetPoolLimit(cfg.MaxPoolSize)
	sess.SetMode(mgo.PrimaryPreferred, true)
	sess.SetSafe(&mgo.Safe{})

	mongoDao := &MongoDao{
		infoSession: sess,
		dbName:      cfg.Database,
	}
	mongoDao.initTask()

	return mongoDao, nil
}

func (m *MongoDao) connect(collection string) (*mgo.Session, *mgo.Collection) {
	s := m.infoSession.Clone()
	c := s.DB("").C(collection)
	return s, c
}

func (m *MongoDao) initTask() {
}
