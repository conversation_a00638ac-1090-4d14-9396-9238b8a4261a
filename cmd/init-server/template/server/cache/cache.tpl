package cache

import (
	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"strconv"
	"time"
)


type AutoLock struct {
	cache   *Cache
	key     string
	locked  bool
	begin   time.Time
	timeout time.Duration
	t       time.Time

	id   string
}

func NewAutoLock(cache *Cache) *AutoLock {
	return &AutoLock{
		cache: cache,
	}
}

func (s *AutoLock) _lock(key string, timeout time.Duration) bool {
	s.t = time.Now()
	s.timeout = timeout
	s.key = key

	ok, err := s.cache.Lock(s.key, strconv.FormatInt(s.t.Unix(), 10), timeout)
	if err != nil {
		log.Errorf("Lock %s fail: %v", key, err)
		return false
	}
	return ok
}

func (s *AutoLock) Lock(key string, timeout time.Duration) bool {
	s.begin = time.Now()
	s.locked = s._lock(key, timeout)
	//log.Infof("[%s]Lock takes %v get %v", s.id, time.Since(s.begin), s.locked)
	return s.locked
}

func (s *AutoLock) LockWithRetry(key string, timeout time.Duration, retry int, retryInterval time.Duration) bool {
	s.begin = time.Now()
	for i := 0; i < retry; i++ {
		s.locked = s._lock(key, timeout)
		if !s.locked {
			time.Sleep(retryInterval + time.Millisecond*time.Duration(i))
		} else {
			//log.Infof("[%s]Lock takes %v get %v", s.id, time.Since(s.begin), s.locked)
			return true
		}
	}

	//log.Infof("[%s]Lock takes %v get %v", s.id, time.Since(s.begin), s.locked)
	return false
}

func (s *AutoLock) Unlock() {
	if s.locked {
		if time.Since(s.t)+50*time.Millisecond < s.timeout {
			s.cache.Unlock(s.key)
		}
	}

	if s.locked {
		//log.Infof("[%s]Lock during %v", s.id, time.Since(s.begin))
	}
}

type Cache struct {
	cmder redis.Cmdable
}

func NewCache(cfg *config.RedisConfig) (*Cache, error) {
	return &Cache{
		cmder: redis.Cmdable(redis.NewClient(&redis.Options{
			Network:            cfg.Protocol,
			Addr:               cfg.Addr(),
			PoolSize:           cfg.PoolSize,
			IdleCheckFrequency: cfg.IdleCheckFrequency(),
			DB:                 cfg.DB,
		})),
	}, nil
}

func (s *Cache) Lock(key, id string, duration time.Duration) (bool, error) {
	return s.cmder.SetNX(key, id, duration).Result()
}

func (s *Cache) LockTry(key, id string, duration time.Duration, retry int) bool {
	for i := 0; i < retry; i++ {
		if ok, _ := s.Lock(key, id, duration); ok {
			time.Sleep(time.Millisecond*time.Duration(i+1))
		} else {
			return true
		}
	}
	return false
}

func (s *Cache) Locking(key string) (ok bool, id string, err error) {
	return s._resultWithExist(s.cmder.Get(key).Result())
}

func (s *Cache) Unlock(key string) {
	delSuc, err := s.cmder.Del(key).Result()
	if err != nil {
		log.Debugf("Unlock err:%+v, key:%s", err.Error(), key)
	}
	if delSuc == 0 {
		log.Debugf("Unlock err, key:%s", key)
	}
	return
}

func (s *Cache) TimedAdd(key string, value uint32, timeout int64) (exist bool, err error) {
	var count int64
	count, err = s.cmder.ZAdd(key, redis.Z{
		Score:  float64(timeout),
		Member: strconv.FormatInt(int64(value), 10),
	}).Result()

	if 0 == count {
		exist = true
	}
	return
}

func (s *Cache) TimedGet(key string, limit int64) (values []uint32, err error) {
	var members []string
	members, err = s.cmder.ZRangeByScore(key, redis.ZRangeBy{
		Min:   strconv.FormatInt(time.Now().Unix(), 10),
		Max:   "+inf",
		Count: limit,
	}).Result()
	if nil != err {
		if err == redis.Nil {
			return nil, nil
		}
		return
	}
	values = make([]uint32, len(members))
	for i := 0; i < len(values); i++ {
		t, _ := strconv.Atoi(members[i])
		values[i] = uint32(t)
	}
	return
}

func (s *Cache) TimedRemove(key string, id uint32) (ok bool, err error) {
	ok, err = s._resultCount(s.cmder.ZRem(key, id).Result())
	return
}

func (s *Cache) TimedRemoveList(key string, ids ...uint32) (ok bool, err error) {
	idsStr := make([]string, len(ids))
	for i, id := range ids {
		idsStr[i] = strconv.Itoa(int(id))
	}
	ok, err = s._resultCount(s.cmder.ZRem(key, idsStr).Result())
	return
}

func (s *Cache) TimedGetByTimeout(key string, limit int64) (ids []uint32, err error) {
	var values []string
	values, err = s.cmder.ZRangeByScore(key, redis.ZRangeBy{
		Min:   "-inf",
		Max:   strconv.FormatInt(time.Now().Unix(), 10),
		Count: limit,
	}).Result()
	if nil != err {
		if err == redis.Nil {
			return nil, nil
		}
		return
	}
	ids = make([]uint32, len(values))
	for i := 0; i < len(values); i++ {
		t, _ := strconv.Atoi(values[i])
		ids[i] = uint32(t)
	}
	return
}

func (s *Cache) _resultWithExist(value string, err error) (bool, string, error) {
	if nil == err {
		return true, value, nil
	} else {
		if err == redis.Nil {
			return false, value, nil
		}
		return false, value, err
	}
}

func (s *Cache) _resultOK(ok string, err error) (bool, error) {
	if ok == "OK" {
		return true, err
	}
	return false, err
}

func (s *Cache) _resultCount(ok int64, err error) (bool, error) {
	if ok > 0 {
		return true, err
	}
	return false, err
}