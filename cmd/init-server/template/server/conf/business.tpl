package conf

import (
	"encoding/json"
	"math/rand"
	"sort"
	"time"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/{{.Servicename}}"
	"golang.52tt.com/pkg/foundation/utils"
)

type Business struct {
}

func newBusiness(configer config.Configer, sectionName string) *Business {
	b := &Business{}

	b.init()

	log.Infof("{{.Servicename}} business(%s)", utils.ToJson(b))
	return b
}

func (b *Business) init() {
}
