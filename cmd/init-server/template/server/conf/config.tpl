package conf

import (
	"errors"
	"fmt"
	"golang.52tt.com/pkg/config"
)

type ServiceConfigT struct {
	{{if .HaveMongo}}MongoConfig *config.MongoConfig{{end}}
	{{if .HaveRedis}}RedisConfig []*config.RedisConfig{{end}}
	{{if .HaveMysql}}MysqlConfig *config.MysqlConfig{{end}}
	{{if .HaveKafka}}KafkaConfig *config.KafkaConfig{{end}}
}

const (
	Debug      = "debug"
	Testing    = "testing"
	Staging    = "staging"
	Production = "production"
)

var Environment = Debug
var RedisCount = 0

func (sc *ServiceConfigT) Parse(configer config.Configer) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	Environment = configer.String("environment")

	{{if .HaveRedis}}
	RedisCount, _ = configer.Int("redis_count")
	if 0 == RedisCount {
		RedisCount = 1
		sc.RedisConfig = make([]*config.RedisConfig, RedisCount)

		sc.RedisConfig[0] = config.NewRedisConfigWithSection(configer, "redis")
		if nil == sc.RedisConfig[0] {
			return errors.New("错误的redis配置")
		}
	} else {
		sc.RedisConfig = make([]*config.RedisConfig, RedisCount)
		for i := 0; i < RedisCount; i++ {
			sc.RedisConfig[i] = config.NewRedisConfigWithSection(configer, fmt.Sprintf("redis%d", i))
			if nil == sc.RedisConfig[i] {
				return errors.New("错误的redis配置")
			}
		}
	}
	{{end}}

	{{if .HaveMongo}}
	sc.MongoConfig = config.NewMongoConfigWithSection(configer, "mongo")
	{{end}}

	{{if .HaveKafka}}
	sc.KafkaConfig = config.NewKafkaConfigWithSection(configer, "kafka")
	{{end}}

	return
}
