package conf

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"github.com/fsnotify/fsnotify"
	"golang.52tt.com/pkg/log"
	"io/ioutil"
	"path/filepath"
	"time"
)

const (
	defaultInterval = 100*time.Millisecond
)

func load(filename string) (string, string, error) {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return "", "", err
	}
	sha := sha256.Sum256(data)
	return string(data), hex.EncodeToString(sha[:]), nil
}

func Watch(ctx context.Context, filename string, scanInterval time.Duration, reload func(string)) error {

	log.Infof("watch >> handle file %s", filename)

	data, sha, err := load(filename)
	if err != nil {
		log.Errorf("watch >> load fail: %v", err)
		return err
	}

	reload(data)

	lastSHA := sha
	lastData := data

	dir, file := filepath.Split(filename)

	go func() {
		for {
			timerC := time.After(scanInterval)

			watcher, err := fsnotify.NewWatcher()
			if err != nil {
				log.Errorf("watch >> new watcher fail: %v", err)
				goto Retry
			}

			err = watcher.Add(dir)
			if err != nil {
				log.Errorf("watch >> watcher add fail: %v", err)
				continue
			}

			for {
				select {
				case <-ctx.Done():
					log.Infof("watch >> stop")
					return

				case <-timerC:
					timerC = nil

					log.Debugf("watch >> scan by timeout")

					data, sha, err = load(filename)
					if err != nil {
						log.Errorf("watch >> load fail: %v", err)
						break
					}

					if sha == lastSHA {
						break
					} else {
						lastSHA = sha
						lastData = data
					}

					log.Debugf("watch >> data change from %s to %s", lastData, data)

					reload(data)

				case ev, ok := <-watcher.Events:
					if !ok {
						log.Infof("watch >> watch events closed")
						return
					}

					if ev.Name == file && (ev.Op & fsnotify.Create == fsnotify.Create || ev.Op & fsnotify.Write == fsnotify.Write){
						timerC = time.After(defaultInterval)

						log.Debugf("watch >> pending fs notify create %d, write %d", ev.Op & fsnotify.Create, ev.Op & fsnotify.Write)
					}

				case e, ok := <-watcher.Errors:
					if !ok {
						log.Infof("watch >> watch errors closed")
						return
					}

					log.Errorf("watch >> pending err: %v", e)
				}

				if nil == timerC {
					timerC = time.After(scanInterval)
				}
			}

		Retry:
			if nil != watcher {
				watcher.Close()
			}
			<-time.After(scanInterval)
		}
	}()

	return nil
}
