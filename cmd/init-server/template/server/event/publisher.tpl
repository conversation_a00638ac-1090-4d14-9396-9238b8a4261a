package event

import (
	"github.com/Shopify/sarama"
	"github.com/gogo/protobuf/proto"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
)

type Publisher struct {
	pub   *KafkaPub
	topic string
}

func NewPublisher(cfg *config.KafkaConfig) *Publisher {
	sc := sarama.NewConfig()
	sc.ClientID = cfg.ClientID
	sc.Producer.RequiredAcks = sarama.WaitForLocal
	sc.Producer.Return.Successes = true
	sc.Producer.Return.Errors = true
	sc.ChannelBufferSize = 2048
	if cfg.SASLEnable {
		sc.Net.SASL.Enable = true
		sc.Net.SASL.User = cfg.SASLUser
		sc.Net.SASL.Password = cfg.SASLPassword
	}
	if pub, err := NewKafkaPub(cfg.BrokerList(), sc); nil != err {
		log.Errorf("NewKafkaPub fail: %v", err)
		return nil
	} else {
		return &Publisher{pub: pub, topic: cfg.Topics}
	}
}

func (s *Publisher) Close() {
	s.pub.Close()
}

func (s *Publisher) Publish(key string, value []byte) {
	s.pub.publish(s.topic, key, value)
}

func (s *Publisher) PublishPB(key string, pbm proto.Message) (err error) {
	var data []byte
	if data, err = proto.Marshal(pbm); err != nil {
		return
	}
	s.pub.publish(s.topic, key, data)
	return
}
