package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	conf "golang.52tt.com/services/bots/bug-robot/config"
	"io/ioutil"
	"net/http"
	"time"
)

type MergeRequest struct {
	SourceBranch string `json:"source_branch"`
	TargetBranch string `json:"target_branch"`
	Author       struct {
		Username string `json:"username"`
		Nickname string `json:"name"`
	} `json:"author"`
	MergeStatus  string `json:"merge_status"`
	State        string `json:"state"`
	HeadPipeline bool   `json:"merge_when_pipeline_succeeds"`
}

const (
	pikachuUrl      = "https://hw-dev-go-api.ttyuyin.com/feishu/"
	sendRichTextUrl = "api/v1/sendRichMsg"
)

var httpCli *http.Client

func init() {
	httpCli = &http.Client{
		Timeout: time.Second * time.Duration(5),
	}
}

type RichTextReq struct {
	MsgType string `json:"msg_type"`
	Content struct {
		Text string `json:"text"`
	} `json:"content"`
	Uuid   string `json:"uuid"`
	ChatId string `json:"chat_id"`
}
type AA struct {
	MemberID     string `json:"member_id"`
	MemberIDtype string `json:"member_id_type"`
	Name         string `json:"name"`
	TencyKey     string `json:"tenant_key"`
}
type Member struct {
}
type Text struct {
	//Members string `json:"members"`
	//Manage []interface{} `json:"managers"`
	MapList map[string]AA       `json:"members"`
	Group   map[string][]string `json:"group"`
}

const requestUrl = "https://gitlab.ttyuyin.com/api/v4/projects/81/merge_requests?state=merged&created_after=2023-03-01T00:00:00.000Z"

type FeiShuServer struct {
	LarkClient             *lark.Client
	FeishuConfig           *conf.FeiShuConfig
	BugScanMessageTemplate string
}

func main() {

	request, err := http.NewRequestWithContext(context.Background(), "GET", requestUrl, nil)

	request.Header.Set("PRIVATE-TOKEN", "7txx_ujHicsym7sxy5kq")
	request.Header.Set("Content-Type", "application/json;charset=utf-8")

	response, err := http.DefaultClient.Do(request)

	body, err := ioutil.ReadAll(response.Body)
	if err != nil {
		_ = response.Body.Close()

	}
	_ = response.Body.Close()
	mr := make([]*MergeRequest, 0)
	err = json.Unmarshal(body, &mr)
	fmt.Printf("%v", err)
	for index, v := range mr {
		fmt.Printf("%d  :-------TargetBranch:%s,  SourceBranch:%s,  state:%s,  MergeStatus:%s,\n  HeadPipeline:%t,  username:%s, nickname:%s\n\n", index+1, v.TargetBranch, v.SourceBranch, v.State, v.MergeStatus, v.HeadPipeline, v.Author.Username, v.Author.Nickname)
	}
	data, err := ioutil.ReadFile("D:\\Download\\quicksilver\\cmd\\merge-remind-robot/member-config.json")
	text := &Text{}
	err = json.Unmarshal(data, text)
	fmt.Printf("%s", text)
	//挑选所有已合并并且合并成功的人员
	//挑选未进行合并人员
	mapList := make(map[string]int)
	for _, v := range mr {
		mapList[v.Author.Nickname] = 1
	}

	NoMergeUsers := make(map[string]int, 0)
	for index, item := range text.MapList {
		if _, ok := mapList[index]; !ok {
			NoMergeUsers[item.Name] = 1
		}
	}
	var content string
	for index, v := range text.Group {
		content += "     " + index + ":  " + "\n"
		for _, v1 := range v {
			if _, ok := NoMergeUsers[v1]; ok {
				content += "          " + v1 + "\n"
			}
		}
	}
	req := &RichTextReq{
		//ReceiveId: "",
		MsgType: "text",
	}
	req.ChatId = "oc_fb52f013b5eb343481b8e2aa3ea302d5"

	req.Content.Text = "预期未完成:  \n" + content
	b := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(b)
	jsonEncoder.SetEscapeHTML(false)
	jsonEncoder.Encode(req)
	requests, err := http.NewRequestWithContext(context.Background(), http.MethodPost, pikachuUrl+sendRichTextUrl, b)
	request.Header.Set("Content-Type", "application/json")

	post, err := httpCli.Do(requests)
	if err != nil {
		fmt.Printf("httpCli.Do error %+v\n", err)
	}
	defer post.Body.Close()

	if post.StatusCode != 200 {
		fmt.Printf("http StatusCode %+v\n", post.StatusCode)
	}

}
