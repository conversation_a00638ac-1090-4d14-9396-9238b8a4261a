package report

import (
	"context"
	"fmt"
)

type MergeTips struct {
	Icon string

	Project string

	Title       string
	Description string

	ChangesCount string

	WebUrl string

	SourceBranch string
	TargetBranch string

	Author string

	Error error
}

type MergeTipsReporter struct {
	botUrl string
	botKey string
}

func NewMergeTipsReporter(botUrl, botKey string) *MergeTipsReporter {
	return &MergeTipsReporter{
		botUrl: botUrl,
		botKey: botKey,
	}
}

func composeMergeTipsText(tips *MergeTips) (title, content string) {
	const (
		mergeTipsTitleFormat   = "%s[%s]合并请求"
		mergeTipsContentFormat = `标题：%s
描述：%s
%s变更数：%s
地址：%s
源分支：%s
目标分支：%s
申请人：%s
`
		errMergeTipsTitleFormat   = "%s[%s]异常合并"
		errMergeTipsContentFormat = `源分支：%s
目标分支：%s
申请人：%s
错误：%s
`
	)

	if nil == tips.Error {
		title = fmt.Sprintf(mergeTipsTitleFormat, tips.Icon, tips.Project)
		content = fmt.Sprintf(mergeTipsContentFormat,
			tips.Title, tips.Description, tips.Icon, tips.ChangesCount, tips.WebUrl, tips.SourceBranch, tips.TargetBranch, tips.Author)
	} else {
		title = fmt.Sprintf(errMergeTipsTitleFormat, tips.Icon, tips.Project)
		content = fmt.Sprintf(errMergeTipsContentFormat,
			tips.SourceBranch, tips.TargetBranch, tips.Author, tips.Error.Error())
	}

	return
}

func (s *MergeTipsReporter) Send(tips *MergeTips) error {
	title, content := composeMergeTipsText(tips)
	return callPikachu(context.Background(), title, content)
/*	if err != nil {
		fmt.Printf("Send callPikachu error %+v\n", err)
	}
	return reporter.NewFeishuReporter(s.botUrl, s.botKey).SendTexts(title, content)*/
}
