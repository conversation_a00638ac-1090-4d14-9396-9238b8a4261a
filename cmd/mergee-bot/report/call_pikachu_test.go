package report

import (
	"context"
	"golang.52tt.com/cmd/mergee-bot/member"
	"testing"
)

func init() {
	member.Init("../member-config.json")
}

func Test_callPikachu(t *testing.T) {
	type args struct {
		ctx     context.Context
		title   string
		content string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "",
			args:    args{
				ctx:     context.Background(),
				title:   "test",
				content: "test",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := callPikachu(tt.args.ctx, tt.args.title, tt.args.content); (err != nil) != tt.wantErr {
				t.Errorf("callPikachu() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}