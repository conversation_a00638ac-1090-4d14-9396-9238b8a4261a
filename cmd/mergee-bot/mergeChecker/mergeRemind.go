package mergeChecker

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"github.com/robfig/cron"
	"golang.52tt.com/cmd/mergee-bot/member"
	"strings"

	"golang.52tt.com/pkg/log"
	"io/ioutil"
	"net/http"
	"time"
)

type Merge struct {
}

const (
	pikachuUrl          = "https://hw-dev-go-api.ttyuyin.com/feishu/"
	sendRichTextUrl     = "api/v1/sendRichMsg"
	sendTextToUserUrl   = "api/v1/sendTextToUser"
	versionBranchPrefix = "release/tt_rel_"
	CodeMergeChatId     = "oc_2ef60bf5726d73aff9118a3b32df4f39"
	BackgroundChatId    = "oc_f2dbff4614adbe1272998efafcc15d9a"
	TestChatId          = "oc_fb52f013b5eb343481b8e2aa3ea302d5"
	ZiChanLuRuChatId    = "oc_a22e6bef9514a55127dd5d429af59f2f"
)

var httpCli *http.Client

func init() {
	httpCli = &http.Client{
		Timeout: time.Second * time.Duration(5),
	}
}

const requestGoUrl = "https://gitlab.ttyuyin.com/api/v4/projects/81/merge_requests?state=merged&updated_after=%d-%d-%dT00:00:00.000Z&updated_before=%d-%d-%dT23:59:59.000Z&per_page=10"
const requestCppUrl = "https://gitlab.ttyuyin.com/api/v4/projects/134/merge_requests?state=merged&updated_after=%d-%d-%dT00:00:00.000Z&updated_before=%d-%d-%dT23:59:59.000Z&per_page=10"

func Init(fileName string) {
	s := &Merge{}
	mergeRemindFile, err := s.loadFromFile(fileName)
	if err != nil {
	}
	mergeMovitation := &MergeMovitation{}

	//mergeStatistics := &MergeStatistics{}
	//mergeStatistics.CaculateTotalIntegrationDaysThisWeek(res)
	//s.CheckTest(res)
	//mergeMovitation.MotivatingUsersWithSendingFeiShuMsg(res)
	c := cron.New()
	c.AddFunc("00 00 17 ? * MON-FRI", func() { s.DailyIntegration(mergeRemindFile) })
	c.AddFunc("00 50 23 ? * MON-FRI", func() { s.DailyIntegrationTest(mergeRemindFile) })
	c.AddFunc("00 30 18 ? * MON-FRI", func() { mergeMovitation.MotivatingUsersWithSendingFeiShuMsg(mergeRemindFile) })
	c.AddFunc("00 00 16 ? * FRI", func() { s.MasterBranchMergeReminder() })
	c.AddFunc("00 00 17 ? * FRI", func() { s.MasterBranchMergeReminder() })
	c.Start()
}
func (s *Merge) DailyIntegration(mergeRemindFile *AllInfo) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	log.InfoWithCtx(ctx, "CheckBeginning")
	now := time.Now()
	goUrl := getGoMergeUrl(now)
	cppUrl := getCppMergeUrl(now)
	everyIntegrationGoInfo, err := s.GetRequestInfo(ctx, goUrl, mergeRemindFile.GoToken)
	everyIntegrationCppInfo, err := s.GetRequestInfo(ctx, cppUrl, mergeRemindFile.CppToken)
	mr := append(everyIntegrationGoInfo, everyIntegrationCppInfo...)

	if err != nil {
		return
	}

	content := s.CheckNoMergeUser(ctx, mr, mergeRemindFile)
	s.Send(ctx, content, CodeMergeChatId)
	s.Send(ctx, content, BackgroundChatId)
}

//每日集成测试
func (s *Merge) DailyIntegrationTest(res *AllInfo) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	log.InfoWithCtx(ctx, "CheckBeginning")
	now := time.Now()
	goUrl := getGoMergeUrl(now)
	cppUrl := getCppMergeUrl(now)
	mrGo, err := s.GetRequestInfo(ctx, goUrl, res.GoToken)
	mrCpp, err := s.GetRequestInfo(ctx, cppUrl, res.CppToken)
	mr := append(mrGo, mrCpp...)

	if err != nil {
		return
	}

	content := s.CheckNoMergeUser(ctx, mr, res)
	s.Send(ctx, content, TestChatId)
}

func (s *Merge) GetRequestInfo(ctx context.Context, url string, token string) ([]*MergeRequest, error) {
	log.InfoWithCtx(ctx, "GetRequestInfo")
	mr := make([]*MergeRequest, 0)
	var offset uint32
	offset = 1
	for {
		tempUrl := url
		tempUrl = fmt.Sprintf(tempUrl+"&page=%d", offset)
		request, err := http.NewRequestWithContext(context.Background(), "GET", tempUrl, nil)
		request.Header.Set("PRIVATE-TOKEN", token)
		request.Header.Set("Content-Type", "application/json;charset=utf-8")
		response, err := http.DefaultClient.Do(request)
		if err != nil {
			log.ErrorWithCtx(ctx, "http.DefaultClient.Do fail err:%v", err)
			return mr, err
		}
		body, err := ioutil.ReadAll(response.Body)
		if err != nil {
			_ = response.Body.Close()
			log.ErrorWithCtx(ctx, "ReadAll fail err:%v", err)
			return mr, err
		}
		_ = response.Body.Close()
		temp := make([]*MergeRequest, 0)
		err = json.Unmarshal(body, &temp)
		log.InfoWithCtx(ctx, "GetRequestInfo out mr:%v,request:%v ,err:%v", mr, request, err)
		mr = append(mr, temp...)
		page := response.Header.Get("X-Next-Page")
		if page != "" {
			offset++
		} else {
			break
		}
	}
	return mr, nil
}

//每周五提醒master合并
func (s *Merge) MasterBranchMergeReminder() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	req := &SendMessageToUserReq{
		MsgType: "interactive",
	}
	req.ReceiveId = CodeMergeChatId

	managers := member.MemberCL.LoadConfig().Managers
	mems := member.MemberCL.LoadConfig().Members
	attentionStr := ""
	for _, manager := range managers {
		attentionStr += fmt.Sprintf("<at id=%s></at>", mems[manager].MemberId) + " "
	}

	msg := MergeRequestTitle
	msgs := MergeRequestRemind
	msgs = strings.ReplaceAll(msgs, "t_user", attentionStr)
	msg = strings.ReplaceAll(msg, "place_holder", msgs)
	req.Content = msg
	b := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(b)
	jsonEncoder.SetEscapeHTML(false)
	err := jsonEncoder.Encode(req)

	if err != nil {
		return err
	}
	request, err := http.NewRequestWithContext(ctx, http.MethodPost, pikachuUrl+sendTextToUserUrl, b)
	request.Header.Set("Content-Type", "application/json")

	post, err := httpCli.Do(request)
	if err != nil {
		fmt.Printf("httpCli.Do error %+v\n", err)
		return err
	}

	defer post.Body.Close()

	if post.StatusCode != 200 {
		fmt.Printf("http StatusCode %+v\n", post.StatusCode)
		return errors.New(http.StatusText(post.StatusCode))
	}
	return nil
}

func (s *Merge) CheckNoMergeUser(ctx context.Context, mr []*MergeRequest, mergeRemindFile *AllInfo) string {
	log.InfoWithCtx(ctx, "CheckNoMergeUser req mr:%v", mr)
	//代码合并人员
	mapNameIsExist := make(map[string]int)

	for _, oneMergeInfo := range mr {
		//判断是否合并版本分支
		if strings.Contains(oneMergeInfo.TargetBranch, versionBranchPrefix) {
			mapNameIsExist[oneMergeInfo.Author.Nickname] = 1
		}
	}
	NoMergeUsersNameToOpenID := make(map[string]string, 0)
	for name, userInfoItem := range mergeRemindFile.MapList {
		if _, ok := mapNameIsExist[name]; !ok {
			NoMergeUsersNameToOpenID[userInfoItem.Name] = userInfoItem.MemberId
		}
	}
	var group string
	for groupName, memberArray := range mergeRemindFile.Group {
		var content string
		for _, everyMemberName := range memberArray {
			if _, ok := NoMergeUsersNameToOpenID[everyMemberName]; ok {
				user := fmt.Sprintf("<at id=%s></at>", NoMergeUsersNameToOpenID[everyMemberName])
				content += user + " "
			}
		}
		msg := DailyGroupMsg
		msg = strings.ReplaceAll(msg, "t_user", content)
		msg = strings.ReplaceAll(msg, "t_group", groupName)

		group += msg + ","
	}
	msg := DailyReport
	if len(group) == 0 {
		msgs := DailyGroupMsg
		msgs = strings.ReplaceAll(msgs, ",place_holderdr", "")
		msgs = strings.ReplaceAll(msgs, "t_group", "恭喜大家全部完成代码集成")
		msg = strings.ReplaceAll(msg, "place_holder", msgs)
	} else {
		group = group[0 : len(group)-1]
		msg = strings.ReplaceAll(msg, "place_holder", group)

	}

	log.InfoWithCtx(ctx, "CheckNoMergeUser out content:%s", msg)
	return msg

}
func (s *Merge) Send(ctx context.Context, content, chatID string) error {
	log.InfoWithCtx(ctx, "Send req content:%s", content)
	req := &SendMessageToUserReq{
		MsgType:   "interactive",
		ReceiveId: chatID,
		Content:   content,
	}

	b := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(b)
	jsonEncoder.SetEscapeHTML(false)
	jsonEncoder.Encode(req)
	requests, err := http.NewRequestWithContext(context.Background(), http.MethodPost, pikachuUrl+sendTextToUserUrl, b)
	requests.Header.Set("Content-Type", "application/json")

	post, err := httpCli.Do(requests)
	if err != nil {
		fmt.Printf("Send httpCli.Do error %+v\n", err)
		return err
	}
	defer post.Body.Close()

	if post.StatusCode != 200 {
		fmt.Printf("Send http StatusCode %+v\n", post.StatusCode)

	}
	log.InfoWithCtx(ctx, "Send out post:%v", post)
	return nil
}

func (s *Merge) loadFromFile(filename string) (res *AllInfo, err error) {
	res = &AllInfo{}
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return res, err
	}
	err = json.Unmarshal(data, res)
	if err != nil {
		return res, err
	}

	return res, nil
}
