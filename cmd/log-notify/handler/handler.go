package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/cmd/log-notify/app_config"
	"golang.52tt.com/cmd/log-notify/report"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

type LogError struct {
	Alarm    string       `json:"Alarm"`    //Alarm
	QueryLog [][]LogPanic `json:"QueryLog"` //QueryLog
}

type LogPanic struct {
	Content *LogContent `json:"content"` //LogTag
}
type LogContent struct {
	LogParseFailure string  `json:"LogParseFailure"` //LogParseFailure
	Tag             *LogTag `json:"__TAG__"`         //LogTag
	Caller          string  `json:"caller"`          //caller
	PkgId           string  `json:"pkg_id"`          //pkg_id
	Source          string  `json:"source"`          //source
	Msg             string  `json:"msg"`             //msg
}

type LogTag struct {
	ClusterId     string `json:"cluster_id"`      //集群
	ContainerName string `json:"container_name "` //
	Namespace     string `json:"namespace"`       //quicksilver
	PodLabelApp   string `json:"pod_label_app"`   //服务名
	PodName       string `json:"pod_name"`        //pod名称
}

func HandleErrorLog(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path != "/" {
		http.Error(w, "404 not found.", http.StatusNotFound)
		return
	}

	switch r.Method {
	case "GET":
		http.ServeFile(w, r, "form.html")
	case "POST":
		body, _ := ioutil.ReadAll(r.Body)
		var result LogError
		err := json.Unmarshal(body, &result)
		if err != nil {
			fmt.Printf("json Unmarshal body:%s err:%v\n", string(body), err)
			return
		}

		logUrl := getLogTitle(result.Alarm)

		mapPodCount := make(map[string]uint32)
		for _, info := range result.QueryLog {
			for _, subInfo := range info {
				if subInfo.Content != nil {
					key := subInfo.Content.Tag.ClusterId + "#" + subInfo.Content.Tag.PodLabelApp
					mapPodCount[key] += 1
				}
			}
		}

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		for key, cnt := range mapPodCount {
			infos := strings.Split(key, "#")
			clusterId := infos[0]
			app := infos[1]
			content := fmt.Sprintf("集群：%s, Pod:%s 数量：%d", clusterId, app, cnt)
			members := app_config.GetProjectOwner(app)
			report.CallPikachu(ctx, result.Alarm, content, logUrl, app, members)
		}
	default:
		fmt.Fprintf(w, "Sorry, only GET and POST methods are supported.")
	}
}

func getLogTitle(alarm string) string {
	if strings.Contains(alarm, "panic") {
		return "查询日志：https://yw-rd-logs.ttyuyin.com/s/jdxk9j"
	} else if strings.Contains(alarm, "redis") {
		return "查询日志：https://yw-rd-logs.ttyuyin.com/s/gllyqg"
	} else if strings.Contains(alarm, "mysql") {
		return "查询日志：https://yw-rd-logs.ttyuyin.com/s/QrRyAQ"
	} else if strings.Contains(alarm, "mongo") {
		return "查询日志：https://yw-rd-logs.ttyuyin.com/s/NyKW2Q"
	} else if strings.Contains(alarm, "cp战加分错误") {
		return "查询日志：https://yw-rd-logs.ttyuyin.com/s/gXBrej"
	}
	return "查询日志："
}
