package main

import (
	"fmt"
	"golang.52tt.com/cmd/log-notify/app_config"
	"golang.52tt.com/cmd/log-notify/handler"
	"golang.52tt.com/cmd/log-notify/member"
	"log"
	"net/http"
)

func main() {
	http.HandleFunc("/", handler.HandleErrorLog)

	err := app_config.Init()
	if err != nil {
		fmt.Printf("load config file err:%v\n", err)
		return
	}

	err = member.Init("./member-config.json")
	if err != nil {
		fmt.Printf("load config file err:%v\n", err)
		return
	}

	fmt.Printf("Starting server for testing HTTP POST...\n")
	if err := http.ListenAndServe(":9000", nil); err != nil {
		log.Fatal(err)
	}
}
