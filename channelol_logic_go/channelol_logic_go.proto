syntax = "proto3";

package ga.channelol_logic_go;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channelol-logic-go";


message GetChannelOnlineMemberRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message GetChannelOnlineMemberResponse {
    ga.BaseResp base_resp = 1;
    uint32 channel_id = 2;
    repeated ga.UserProfile user_profile_list = 3;
    int64 interval_ts = 4;  //拉取间隔
}